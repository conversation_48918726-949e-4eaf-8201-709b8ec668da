import styled from "styled-components";
import { Button } from "antd";

export const ButtonMain = styled(Button)`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 5px 10px;
  border: 1px solid
    ${({ bordercolor }) =>
    bordercolor ? `${bordercolor} !important` : "#7a869a"};
  border-radius: 8px;
  height: ${(props) => props.height}px;
  margin: 2px;
  ${(props) => (props.right ? `margin-right: ${props.right}px;` : ``)}
  ${(props) => (props.left ? `margin-left: ${props.left}px;` : ``)}
  mix-blend-mode: normal;
  border-radius: 8px;
  min-width: ${(props) =>
    (props.minwidth + "" || "").indexOf("%") >= 0
      ? props.minwidth
      : `${props.minwidth}px`};
  ${(props) => props.fitcontent && `width: 100%;`}
  cursor: pointer;
  transition: all 0.1s;
  transform: translateY(0);
  &:hover {
    background: linear-gradient(
        0deg,
        rgba(255, 255, 255, 0.9),
        rgba(255, 255, 255, 0.9)
      ),
      #0762f7;
  }
  &:active {
    background: #ffffff;
  }
  &:disabled {
    background: linear-gradient(
        0deg,
        rgba(23, 43, 77, 0.25),
        rgba(23, 43, 77, 0.25)
      ),
      #ffffff;
  }
  & svg {
    height: ${(props) =>
    props.iconheight ? props.iconheight + "px" : "20px"} !important;
    width: ${(props) =>
    props.iconheight ? props.iconheight + "px" : "20px"} !important;
    & path {
      fill: #7a869a;
    }
  }
  & .button-content {
    flex: 1;
    margin: 0 5px;
    color: #172b4d;
    font-style: normal;
    font-weight: 500;
    font-size: var(--fs-1);
    line-height: 20px;
  }
  & .button-right-content{
    margin-left: 5px;
  }
  &.primary {
    background: ${(props) => props.backgroundcolor || "#0762f7"};
    border: 1px solid ${(props) => props.backgroundcolor || "#0762f7"};
    & .button-content {
      color: #fff;
    }
    &:hover {
      background: #0549b9;
      border: 1px solid ${(props) => props.backgroundcolor || "#0549B9"};
    }
    &:active {
      background: ${(props) => props.backgroundcolor || "#0762f7"};
    }
    & svg {
      & path {
        fill: #fff;
      }
    }
    &.button-text {
      background: transparent !important;
      border: ${({ bordercolor }) =>
    bordercolor ? `1px solid ${bordercolor}` : "none"} !important;
      box-shadow: none !important;
      & .button-content {
        color: ${(props) => props.color || "#0762f7"};
      }
      & svg {
        & path {
          fill: ${(props) => props.color || "#0762f7"};
        }
      }
    }
  }
  &.success {
    background: ${(props) => props.backgroundcolor || "#049254"};
    border: 1px solid ${(props) => props.backgroundcolor || "#049254"};

    &:hover {
      background: #026138;
    }
    &:active {
      background: ${(props) => props.backgroundcolor || "#049254"};
    }
    & .button-content {
      color: #fff;
    }
    & svg {
      & path {
        fill: #fff;
      }
    }
    &.button-text {
      background: transparent !important;
      border: none !important;
      box-shadow: none !important;
      & .button-content {
        color: ${(props) => props.color || "#049254"};
      }
      & svg {
        & path {
          fill: ${(props) => props.color || "#049254"};
        }
      }
    }
  }
  &.warning {
    background: ${(props) => props.backgroundcolor || "#FE8803"} !important;
    border: 1px solid ${(props) => props.backgroundcolor || "#FE8803"} !important;
    & .button-content {
      color: #fff;
    }
    &:hover {
      background: #d46b08 !important;
      border: 1px solid ${(props) => props.backgroundcolor || "#D46B08 "} !important;
    }
    &:active {
      background: ${(props) => props.backgroundcolor || "#FE8803"} !important;
    }
    & svg {
      & path {
        fill: #fff;
      }
    }
    &.button-text {
      background: transparent !important;
      border: none !important;
      box-shadow: none !important;
      & .button-content {
        color: ${(props) => props.color || "#FE8803"};
      }
      & svg {
        & path {
          fill: ${(props) => props.color || "#FE8803"};
        }
      }
    }
  }
  &.error {
    background: ${(props) => props.backgroundcolor || "#fc3b3a"} !important;
    border: 1px solid ${(props) => props.backgroundcolor || "#fc3b3a"} !important;
    & .button-content {
      color: #fff;
    }
    &:hover {
      background: #d32f2f !important;
      border: 1px solid ${(props) => props.backgroundcolor || "#d32f2f"} !important;
    }
    &:active {
      background: ${(props) => props.backgroundcolor || "#fc3b3a"} !important;
    }
    & svg {
      & path {
        fill: #fff;
      }
    }
    &.button-text {
      background: transparent !important;
      border: none !important;
      box-shadow: none !important;
      & .button-content {
        color: ${(props) => props.color || "#fc3b3a"};
      }
      & svg {
        & path {
          fill: ${(props) => props.color || "#fc3b3a"};
        }
      }
    }
  }
  &.info {
    background: ${(props) =>
    props.backgroundcolor ||
    "linear-gradient( 0deg, rgba(255,255,255,0.9), rgba(255,255,255,0.9) ), #0762f7;"} !important;
    border: 1px solid
      ${(props) =>
    props.backgroundcolor ||
    "linear-gradient( 0deg, rgba(255,255,255,0.9), rgba(255,255,255,0.9) ), #0762f7;"};
    & .button-content {
      color: #172b4d;
    }
    & svg {
      & path {
        fill: #7a869a;
      }
    }
    &.button-text {
      background: transparent !important;
      border: none !important;
      box-shadow: none !important;
      & .button-content {
        color: ${(props) =>
    props.color ||
    "linear-gradient( 0deg, rgba(255,255,255,0.9), rgba(255,255,255,0.9) ), #0762f7;"};
      }

      & svg {
        & path {
          fill: ${(props) =>
    props.color ||
    "linear-gradient( 0deg, rgba(255,255,255,0.9), rgba(255,255,255,0.9) ), #0762f7;"};
        }
      }
    }
  }
  &.default {
    background: ${(props) => props.backgroundcolor || "#FFF"} !important;
    border: 1px solid #64748b;
    ${(props) =>
    props.backgroundcolor ||
    "linear-gradient( 0deg, rgba(255,255,255,0.9), rgba(255,255,255,0.9) ), #0762f7;"};
    & .button-content {
      color: ${(props) => props.color || "#172b4d"};
    }
    & svg {
      & path {
        fill: ${(props) => props.color || "#7a869a"};
      }
    }
    &:hover {
      background: ${({ hovercolor }) => hovercolor || "#f1f5f9"} !important;
    }
    &.button-text {
      background: transparent !important;
      border: none !important;
      box-shadow: none !important;
      & .button-content {
        color: ${(props) => props.color || "#000"};
      }

      & svg {
        & path {
          fill: ${(props) =>
    props.color ||
    "linear-gradient( 0deg, rgba(255,255,255,0.9), rgba(255,255,255,0.9) ), #0762f7;"};
        }
      }
    }
  }
  & img {
    max-height: ${(props) =>
    props.iconheight ? props.iconheight + "px" : "10px"};
  }

  &[disabled] {
    background: linear-gradient(
        0deg,
        rgba(23, 43, 77, 0.25),
        rgba(23, 43, 77, 0.25)
      ),
      #ffffff;
    border: 1px solid ${(props) => props.backgroundcolor || "#7a869a"};
    &:hover {
      background: linear-gradient(
          0deg,
          rgba(23, 43, 77, 0.25),
          rgba(23, 43, 77, 0.25)
        ),
        #ffffff;
    }
  }

  &:active {
    transform: translateY(3px);
  }
`;
