import { client, dataPath } from "client/request";
import { NB_HOAN_THANH_TOAN } from "client/api";
import apiBase from "data-access/api-base";
import { combineUrlParams } from "utils";

export default {
  ...apiBase.init({ API: NB_HOAN_THANH_TOAN }),
  vanTinTenTK: ({ soTaiKhoan, nganHangId }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_HOAN_THANH_TOAN}/van-tin-ten-tai-khoan`, {
          soTaiKhoanThuHuong: soTai<PERSON>hoan,
          nganHangThuHuongId: nganHangId,
        })
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  hoanTienNganHang: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_HOAN_THANH_TOAN}/kiem-tra-giao-dich`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  xacNhanHoanThanhToan: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_HOAN_THANH_TOAN}/xac-nhan`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  kyXacNhanHoanThanhToan: ({ id }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_HOAN_THANH_TOAN}/ky-xac-nhan/${id}`)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  duyetThanhToan: ({ id }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_HOAN_THANH_TOAN}/duyet-thanh-toan/${id}`)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  doiHinhThucChuyen: ({ maFile }) => {
    return new Promise((resolve, reject) => {
      client
        .post(
          combineUrlParams(`${dataPath}${NB_HOAN_THANH_TOAN}/doi-hinh-thuc`, {
            maFile,
          })
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  huyKyHoanThanhToan: ({ id }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_HOAN_THANH_TOAN}/huy-ky/${id}`)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  getDanhSachHoanThanhToanTheoLo: apiBase.init({
    API: `${NB_HOAN_THANH_TOAN}/theo-lo`,
  }).search,
  xoaFileChuyenTien: ({ maFile }) => {
    return new Promise((resolve, reject) => {
      client
        .post(
          combineUrlParams(`${dataPath}${NB_HOAN_THANH_TOAN}/huy-xac-nhan`, {
            maFile,
          })
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  kiemTraGiaoDich: (id) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_HOAN_THANH_TOAN}/kiem-tra-giao-dich/${id}`)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  guiLaiGiaoDich: ({ maFile }) => {
    return new Promise((resolve, reject) => {
      client
        .post(
          combineUrlParams(`${dataPath}${NB_HOAN_THANH_TOAN}/thanh-toan`, {
            maFile,
          })
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
};
