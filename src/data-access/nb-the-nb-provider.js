import { NB_THE_NB, ANA } from "client/api";
import { client, dataPath } from "client/request";
import apiBase from "./api-base";

export default {
  init: { ...apiBase.init({ API: NB_THE_NB }) },
  initHuy: { ...apiBase.init({ API: `${NB_THE_NB}/huy` }) },

  huyTheAna: (id) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${ANA}/huy-the/${id}`)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch(reject);
    });
  },
};
