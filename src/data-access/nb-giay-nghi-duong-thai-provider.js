import { NB_GIAY_NGHI_DUONG_THAI } from "client/api";
import { client, dataPath } from "client/request";
import apiBase from "./api-base";
export default {
  ...apiBase.init({ API: NB_GIAY_NGHI_DUONG_THAI }),

  dayGiayNghiDuongThaiHangLoat: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(
          `${dataPath}${NB_GIAY_NGHI_DUONG_THAI}/day-giay-nghi-duong-thai`,
          payload
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
};
