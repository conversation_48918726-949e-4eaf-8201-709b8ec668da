import { combineUrlParams } from "utils";
import { client, dataPath } from "client/request";
import {
  BAO_CAO_DA_IN,
  BC_PK,
  BAO_CAO_KHO,
  BAO_CAO_TIEM_CHUNG,
  BAO_CAO_KSNK,
  BC_LAO,
  BC_KHO_DINH_DUONG,
  BC_QUAN_TRI,
  BC_DDLS,
  BC_QT,
  BC_SANG_LOC_DD,
  BC_PHA_CHE,
} from "client/api";
import currentProvider from "data-access/bao-cao-da-in-provider.js";

export default {
  searchAll: () => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${BAO_CAO_DA_IN}/tong-hop`, {
            active: true,
          })
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  search: ({ page = 0, active, sort, size = 2000, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${BAO_CAO_DA_IN}`, {
            page: page + "",
            active,
            sort,
            size,
            ...payload,
          })
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  post: (params) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${BAO_CAO_DA_IN}`, params)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  put: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .put(`${dataPath}${BAO_CAO_DA_IN}/${id}`, rest)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  delete: (id) => {
    return new Promise((resolve, reject) => {
      client
        .delete(`${dataPath}${BAO_CAO_DA_IN}/${id}`)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  getBaoCao: (payload, path) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(path, {
            ...payload,
          })
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  // Báo cáo dịch vụ
  getBc01: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/dv-01`),
  getBc02: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-02`),
  getBc02_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-02.1`),
  getBc03: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-03`),
  getBc04: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-04`),
  getBc05: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-05`),
  getBc06: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-06`),
  getBc07: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-07`),
  getBc08: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-08`),
  getBc09: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-09`),
  getBc10: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-10`),
  getBc10_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-10.1`),
  getBc10_2: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-10.2`),
  getBc10_3: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-10.3`),
  getBc11: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-11`),
  getBc11_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-11.1`),
  getBc11_2: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-11.2`),
  getBc12: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-12`),
  getBc14: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-14`),
  getBc14_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-14.1`),
  getBc14_2: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-14.2`),
  getBc15: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-15`),
  getBc16: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-16`),
  getBc17: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-17`),
  getBc18: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-18`),
  getBc19: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-19`),
  getBc20: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-20`),
  getBc21: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-21`),
  getBc22: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-22`),
  getBc23: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-23`),
  getBc24: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-24`),
  getBc25: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-25`),
  getBc26: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-26`),
  getBc27: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-27`),
  getBc28: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-28`),
  getBc29: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-29`),
  getBc29_2: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-29.2`),
  getBc30: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-30`),
  getBc31: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-31`),
  getBc32: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-32`),
  getBc33: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-33`),
  getBc34: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-34`),
  getBc35: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-35`),
  getBc36: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-36`),
  getBc37: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-37`),
  getBc38: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-38`),
  getBc39: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-39`),
  getBc40: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-40`),
  getBc41: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-41`),
  getBc42: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/bc-42`),

  // Báo cáo pha chế thuốc
  getPc01: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PHA_CHE}/pc-01`),
  getPc02: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PHA_CHE}/pc-02`),
  getPc04: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PHA_CHE}/pc-04`),

  // Báo cáo tài chính
  getTc01: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-01`),
  getTc01_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-01.1`),
  getTc01_2: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-01.2`),
  getTc01_3: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-01.3`),
  getTc01_4: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-01.4`),
  getTc01_5: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-01.5`),
  getTc01_6: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-01.6`),
  getTc01_7: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-01.7`),
  getTc01_8: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-01.8`),
  getTc01_9: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-01.9`),
  getTc01_10: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-01.10`),
  getTc01_11: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-01.11`),
  getTc01_12: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-01.12`),
  getTc01_13: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-01.13`),
  getTc01_14: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-01.14`),
  getTc01_15: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-01.15`),
  getTc02: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-02`),
  getTc02_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-02.1`),
  getTc02_2: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-02.2`),
  getTc03: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-03`),
  getTc03_2: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-03.2`),
  getTc04: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-04`),
  getTc05: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-05`),
  getTc06: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-06`),
  getTc06_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-063`),
  getTc06_4: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-06.4`),
  getTc06_5: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-06.5`),
  getTc08: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-08`),
  getTc07: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-07`),
  getTc09: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-09`),
  getTc10: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-10`),
  getTc11: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-11`),
  getTc12: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-12`),
  getTc12_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-12.1`),
  getTc13: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-13`),
  getTc13_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-13.1`),
  getTc13_2: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-13.2`),
  getTc13_3: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-13.3`),
  getTc14: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-14`),
  getTc14_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-14.1`),
  getTc15: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-15`),
  getTc15_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-15.1`),
  getTc15_2: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-15.2`),
  getTc15_3: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-15.3`),
  getTc15_4: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-15.4`),
  getTc15_5: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-15.5`),
  getTc16: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-16`),
  getTc16_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-16.1`),
  getTc17: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-17`),
  getTc17_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-17.1`),
  getTc17_2: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-17.2`),
  getTc17_3: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-17.3`),
  getTc18: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-18`),
  getTc18_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-18.1`),
  getTc18_2: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-18.2`),
  getTc19: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-19`),
  getTc20: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-20`),
  getTc20_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-20.1`),
  getTc21: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-21`),
  getTc21_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-21.1`),
  getTc21_2: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-21.2`),
  getTc21_3: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-21.3`),
  getTc21_4: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-21.4`),
  getTc22: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-22`),
  getTc22_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-22.1`),
  getTc22_2: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-22.2`),
  getTc22_3: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-22.3`),
  getTc23: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-23`),
  getTc24: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-24`),
  getTc25: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-25`),
  getTc26: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-26`),
  getTc27: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-27`),
  getTc28: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-28`),
  getTc28_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-28.1`),
  getTc29: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-29`),
  getTc29_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-29.1`),
  getTc29_2: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-29.2`),
  getTc31: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-31`),
  getTc33: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-33`),
  getTc34: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-34`),
  getTc36: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-36`),
  getTc37: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-37`),
  getTc38: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-38`),
  getTc39: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-39`),
  getTc40: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-40`),
  getTc41: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-41`),
  getTc41_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-41.1`),
  getTc42: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-42`),
  getTc42_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-42.1`),
  getTc42_2: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-42.2`),
  getTc42_3: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-42.3`),
  getTc42_4: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-42.4`),
  getTc42_5: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-42.5`),
  getTc42_6: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-42.6`),
  getTc46: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-46`),
  getTc48: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-48`),
  getTc49: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-49`),
  getTc50: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-50`),
  getTc51: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-51`),
  getTc51_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-51.1`),
  getTc53: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-53`),
  getTc54: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-54`),
  getTc54_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-54.1`),
  getTc55: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-55`),
  getTc55_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-55.1`),
  getTc56: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-56`),
  getTc57: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-57`),
  getTc57_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-57.1`),
  getTc58: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-58`),
  getTc58_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-58.1`),
  getTc59: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-59`),
  getTc59_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-59.1`),
  getTc59_2: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-59.2`),
  getTc59_3: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-59.3`),
  getTc60: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-60`),
  getTc61: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-61`),
  getTc62: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-62`),
  getTc63: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-63`),
  getTc63_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-63.1`),
  getTc64: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-64`),
  getTc64_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-64.1`),
  getTc64_2: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-64.2`),
  getTc66: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-66`),
  getTc67: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-67`),
  getTc67_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-67.1`),
  getTc67_2: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-67.2`),
  getTc68: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-68`),
  getTc69: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-69`),
  getTc69_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-69.1`),
  getTc69_2: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-69.2`),
  getTc70: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-70`),
  getTc71: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-71`),
  getTc72: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-72`),
  getTc73: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-73`),
  getTc73_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-73.1`),
  getTc74: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-74`),
  getTc75: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-75`),
  getTc75_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-75.1`),
  getTc76: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-76`),
  getTc79: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-79`),
  getTc80: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-80`),
  getTc81: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-81`),
  getTc81_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-81.1`),
  getTc80_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-80.1`),
  getTc80_2: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/tc-80.2`),
  // Báo cáo phòng khám
  getBcDsNbKhamChiTiet: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/pk-01`),
  getPk02: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/pk-02`),
  getPk03: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/pk-03`),
  getPk04: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/pk-04`),
  getPk06: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/pk-06`),
  getPk07: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/pk-07`),
  getPk08: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/pk-08`),
  getPk09: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/pk-09`),
  getPk10: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/pk-10`),
  getPk11: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/pk-11`),
  getPk12: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/pk-12`),
  getPk13: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/pk-13`),
  getPk14: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/pk-14`),
  getPk15: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/pk-15`),

  // Báo cáo kho
  getK01: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k01`),
  getK01_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k01.1`),
  getK01_2: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k01.2`),
  getK01_3: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k01.3`),
  getK02: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k02`),
  getK02_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k02.1`),
  getK02_2: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k02.2`),
  getK02_3: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k02.3`),
  getK02_4: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k02.4`),
  getK02_5: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k02.5`),
  getK02_6: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k02.6`),
  getK02_7: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k02.7`),
  getK02_8: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k02.8`),
  getK02_10: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k02.10`),
  getK03: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k03`),
  getK04: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k04`),
  getK04_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k04.1`),
  getK04_2: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k04.2`),
  getK04_3: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k04.3`),
  getK04_4: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k04.4`),
  getK05: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k05`),
  getK05_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k05.1`),
  getK07: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k07`),
  getK07_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k07.1`),
  getK08: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k08`),
  getK10: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k10`),
  getK11: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k11`),
  getK12: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k12`),
  getK13: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k13`),
  getK14: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k14`),
  getK14_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k14.1`),
  getK14_2: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k14.2`),
  getK15: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k15`),
  getKvt04: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/kvt04`),
  getK20: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k20`),
  getK20_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k20.1`),
  getK20_2: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k20.2`),
  getK20_3: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k20.3`),
  getK21: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k21`),
  getK22: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k22`),
  getK23: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k23`),
  getK24: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k24`),
  getK25: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k25`),
  getK26: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k26`),
  getK27: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k27`),
  getK28: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k28`),
  getK29: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k29`),
  getK30: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k30`),
  getK31: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k31`),
  getK32: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k32`),
  getK33: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k33`),
  getK34: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k34`),
  getK35: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k35`),
  getK36: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k36`),
  getK37: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k37`),
  getK38: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k38`),
  getK39: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k39`),
  getK40: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k40`),
  getK42: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k42`),
  getK43: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k43`),
  getK44: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k44`),
  getK45: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k45`),
  getK46: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k46`),
  getK47: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k47`),
  getK48: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k48`),
  getK49: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k49`),
  getK50: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k50`),
  getK51: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k51`),
  getK52: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k52`),
  getK53: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k53`),
  getK54: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k54`),
  getK55: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k55`),
  getK56: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k56`),
  getK57: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k57`),
  getK58: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k58`),
  getK58_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k58.1`),
  getK59: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k59`),
  getK60: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k60`),
  getK61: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k61`),
  getK62: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k62`),
  getK63: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k63`),
  getK64: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k64`),
  getK65: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k65`),
  getK66: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k66`),
  getK67: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k67`),
  getK68: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k68`),
  getK69: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k69`),
  getK70: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k70`),
  getK71: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k71`),
  getK72: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k72`),
  getK73: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k73`),
  getK74: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k74`),
  getK75: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k75`),
  getK76: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k76`),
  getK77: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k77`),
  getK78: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k78`),
  getK79: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k79`),
  getK80: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k80`),
  getK82: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k82`),
  getK84: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/k84`),
  // Báo cáo nhà thuốc
  getKnt01: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/nt01`),
  getKnt02: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/nt02`),
  getKnt03: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/nt03`),
  getKnt03_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/nt03.1`),
  getKnt04: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/nt04`),
  getKnt05: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/nt05`),
  getKnt06: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/nt06`),
  getKnt07: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/nt07`),
  getKnt08: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/nt08`),
  getKnt08_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/nt08.1`),
  getKnt10: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/nt10`),
  getKnt11: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/nt11`),
  getKnt12: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/nt12`),
  getKnt13: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/nt13`),
  getKnt14: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/nt14`),
  getKnt15: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/nt15`),
  getKnt15_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/nt15.1`),
  getKnt16: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/nt16`),
  getKnt17: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/nt17`),
  getKnt18: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/nt18`),
  getKnt19: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/nt19`),
  getKnt20: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/nt20`),
  getKnt21: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/nt21`),
  getKnt22: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/nt22`),
  getKnt23: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/nt23`),
  getKnt24: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/nt24`),
  getKnt25: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/nt25`),
  getKnt26: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/nt26`),

  // Báo cáo kho vật tư/ hóa chất
  getKvt02: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/kvt02`),
  getKvt03: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/kvt03`),
  getKvt05: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/kvt05`),
  getKvt06: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/kvt06`),
  getKvt07: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/kvt07`),

  // Báo cáo khám sức khỏe
  getKsk01: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/ksk-01`),
  getKsk01_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/ksk-01.1`),
  getKsk02: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/ksk-02`),
  getKsk04: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/ksk-04`),
  getKsk05: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/ksk-05`),
  getKsk12: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/ksk-12`),
  getKsk13: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/ksk-13`),
  getKsk14: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/ksk-14`),
  getKsk15: ({ id }) => {
    return new Promise((resolve, reject) => {
      client
        .get(`${dataPath}${BC_PK}/ksk-15/${id}`)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  getKsk16: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/ksk-16`),
  getKsk17: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/ksk-17`),
  getKsk18: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/ksk-18`),
  getKsk19: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/ksk-19`),
  getKsk20: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/ksk-20`),
  getKsk20_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/ksk-20.1`),
  getKsk21: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/ksk-21`),
  // Báo cáo gói liệu trình
  getG01: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/g01`),
  getG02: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/g02`),
  getG03: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/g03`),
  getG04: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/g04`),
  getG05: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/g05`),
  getG06: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/g06`),
  getG07: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/g07`),

  // Báo cáo kế hoạch tổng hợp
  getKhth01: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-01`),
  getKhth02: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-02`),
  getKhth03: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-03`),
  getKhth04: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-04`),
  getKhth05: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-05`),
  getKhth06: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-06`),
  getKhth07: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-07`),
  getKhth08: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-08`),
  getKhth08_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-08.1`),
  getKhth08_2: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-08.2`),
  getKhth09: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-09`),
  getKhth10: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-10`),
  getKhth11: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-11`),
  getKhth12: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-12`),
  getKhth12_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-12.1`),
  getKhth13: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-13`),
  getKhth14: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-14`),
  getKhth15: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-15`),
  getKhth16: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-16`),
  getKhth17: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-17`),
  getKhth18: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-18`),
  getKhth19: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-19`),
  getKhth20: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-20`),
  getKhth21: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-21`),
  getKhth22: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-22`),
  getKhth23: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-23`),
  getKhth24: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-24`),
  getKhth25: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-25`),
  getKhth26: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-26`),
  getKhth27: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-27`),
  getKhth29: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-29`),
  getKhth30: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-30`),
  getKhth31: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-31`),
  getKhth33: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-33`),
  getKhth34: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-34`),
  getKhth35: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-35`),
  getKhth37: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-37`),
  getKhth38: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-38`),
  getKhth40: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-40`),
  getKhth41: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-41`),
  getKhth42: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-42`),
  getKhth43: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-43`),
  getKhth44: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-44`),
  getKhth45: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-45`),
  getKhth46: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-46`),
  getKhth47: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-47`),
  getKhth48: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-48`),
  getKhth49: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/khth-49`),
  // Báo cáo phẫu thuật, thủ thuật
  getPttt01: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/pttt-01`),
  getPttt02: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/pttt-02`),
  getPttt03: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/pttt-03`),
  getPttt04: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/pttt-04`),
  getPttt04_1: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/pttt-04.1`),
  getPttt04_2: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/pttt-04.2`),
  getPttt04_3: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/pttt-04.3`),
  getPttt04_4: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/pttt-04.4`),
  getPttt04_5: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/pttt-04.5`),
  getPttt04_7: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/pttt-04.7`),
  getPttt04_8: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/pttt-04.8`),
  getPttt04_9: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/pttt-04.9`),
  getPttt05: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/pttt-05`),
  getPttt06: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/pttt-06`),
  getPttt07: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/pttt-07`),
  getPttt09: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/pttt-09`),
  // Báo cáo suất ăn
  getDd01: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/dd-01`),
  getDd03: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_PK}/dd-03`),

  // Báo cáo tiêm chủng
  getTiem01: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KHO}/tiem01`),
  getTiem02: (payload) =>
    currentProvider.getBaoCao(
      payload,
      `${dataPath}${BAO_CAO_TIEM_CHUNG}/tiem-02`
    ),
  getTiem03: (payload) =>
    currentProvider.getBaoCao(
      payload,
      `${dataPath}${BAO_CAO_TIEM_CHUNG}/tiem-03`
    ),
  getTiem04: (payload) =>
    currentProvider.getBaoCao(
      payload,
      `${dataPath}${BAO_CAO_TIEM_CHUNG}/tiem-04`
    ),
  getTiem05: (payload) =>
    currentProvider.getBaoCao(
      payload,
      `${dataPath}${BAO_CAO_TIEM_CHUNG}/tiem-05`
    ),

  // Báo cáo kiểm soát nhiễm khuẩn
  getKsnk_01: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KSNK}/ksnk-01`),
  getKsnk_02: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BAO_CAO_KSNK}/ksnk-02`),

  // Báo cáo lao
  getLao01: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_LAO}/lao-01`),

  // Báo cáo Kho dinh dưỡng
  getKdd01: (payload) =>
    currentProvider.getBaoCao(
      payload,
      `${dataPath}${BC_KHO_DINH_DUONG}/kdd-01`
    ),
  getKdd02: (payload) =>
    currentProvider.getBaoCao(
      payload,
      `${dataPath}${BC_KHO_DINH_DUONG}/kdd-02`
    ),
  getKdd03: (payload) =>
    currentProvider.getBaoCao(
      payload,
      `${dataPath}${BC_KHO_DINH_DUONG}/kdd-03`
    ),
  getKdd04: (payload) =>
    currentProvider.getBaoCao(
      payload,
      `${dataPath}${BC_KHO_DINH_DUONG}/kdd-04`
    ),
  getKdd05: (payload) =>
    currentProvider.getBaoCao(
      payload,
      `${dataPath}${BC_KHO_DINH_DUONG}/kdd-05`
    ),
  getKdd06: (payload) =>
    currentProvider.getBaoCao(
      payload,
      `${dataPath}${BC_KHO_DINH_DUONG}/kdd-06`
    ),
  getKdd07: (payload) =>
    currentProvider.getBaoCao(
      payload,
      `${dataPath}${BC_KHO_DINH_DUONG}/kdd-07`
    ),
  getKdd08: (payload) =>
    currentProvider.getBaoCao(
      payload,
      `${dataPath}${BC_KHO_DINH_DUONG}/kdd-08`
    ),
  getKdd09: (payload) =>
    currentProvider.getBaoCao(
      payload,
      `${dataPath}${BC_KHO_DINH_DUONG}/kdd-09`
    ),
  getKdd10: (payload) =>
    currentProvider.getBaoCao(
      payload,
      `${dataPath}${BC_KHO_DINH_DUONG}/kdd-10`
    ),
  getKdd11: (payload) =>
    currentProvider.getBaoCao(
      payload,
      `${dataPath}${BC_KHO_DINH_DUONG}/kdd-11`
    ),
  getKdd12: (payload) =>
    currentProvider.getBaoCao(
      payload,
      `${dataPath}${BC_KHO_DINH_DUONG}/kdd-12`
    ),
  getKdd13: (payload) =>
    currentProvider.getBaoCao(
      payload,
      `${dataPath}${BC_KHO_DINH_DUONG}/kdd-13`
    ),
  getKdd14: (payload) =>
    currentProvider.getBaoCao(
      payload,
      `${dataPath}${BC_KHO_DINH_DUONG}/kdd-14`
    ),

  // Báo cáo quản trị
  getQt01: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_QUAN_TRI}/qt-01`),
  getQt02: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_QUAN_TRI}/qt-02`),
  getQt03: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_QUAN_TRI}/qt-03`),
  // Báo cáo quyết toán
  getQtbh13: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_QT}/qt-13`),

  // Báo cáo duyệt dược lâm sàng
  getDdls05: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_DDLS}/05`),
  getDdls06: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_DDLS}/06`),
  getDdls07: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_DDLS}/07`),
  getDdls08: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_DDLS}/08`),
  getDdls09: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_DDLS}/09`),
  getDdls10: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_DDLS}/10`),

  // Báo cáo sang lọc dinh dưỡng
  getSldd01: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_SANG_LOC_DD}/sldd-01`),
  getSldd02: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_SANG_LOC_DD}/sldd-02`),
  getSldd03: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_SANG_LOC_DD}/sldd-03`),
  getSldd05: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_SANG_LOC_DD}/sldd-05`),
  getSldd06: (payload) =>
    currentProvider.getBaoCao(payload, `${dataPath}${BC_SANG_LOC_DD}/sldd-06`),
};
