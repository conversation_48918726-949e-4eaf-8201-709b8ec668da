import { NB_DV_KY_THUAT, NB_DV_KY_THUAT_NGUOI_BENH } from "client/api";
import apiBase from "./api-base";
import { combineUrlParams } from "utils";
import { client, dataPath } from "client/request";

export default {
  ...apiBase.init({ API: NB_DV_KY_THUAT_NGUOI_BENH }),
  tomTatKqCls: (payload) =>
    apiBase.init({ API: `${NB_DV_KY_THUAT}/tom-tat-kq-cls` }).search({
      nbDotDieuTriId: payload.nbDotDieuTriId,
      page: "",
      size: "",
    }),
  thongBaoCls: (payload) =>
    apiBase
      .init({
        API: combineUrlParams(`${NB_DV_KY_THUAT}/thong-bao-cls`),
      })
      .post(payload),
  thongBaoDv: (payload) =>
    apiBase
      .init({
        API: combineUrlParams(`${NB_DV_KY_THUAT}/thong-bao-dv`),
      })
      .post(payload),
  getPhieuHuongDan: (payload) => {
    // /api/his/v1/nb-dv-kham/phieu-ket-luan/38065
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${NB_DV_KY_THUAT}/phieu-huong-dan`, {
            ...payload,
          })
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  kiemTraDvktTrungNguoiThucHien: ({
    dichVuId,
    nguoiThucHienId,
    thoiGianThucHien,
    thoiGianCoKetQua,
  }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DV_KY_THUAT}/trung-nguoi-thuc-hien/${dichVuId}`,
            {
              nguoiThucHienId,
              thoiGianThucHien,
              thoiGianCoKetQua,
            }
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
};
