import { isEmpty, isNil, orderBy, get, cloneDeep } from "lodash";
import moment, { isMoment } from "moment";
import { t } from "i18next";

import { removeVietnameseTones } from "lib-utils";

import { FORMAT_DATE, DOI_TUONG } from "constants/index";
import { message } from "antd";
import { getAllQueryString } from "hooks/useQueryString/queryString";
import cacheUtils from "lib-utils/cache-utils";

export const combineUrlParams = (
  url = "",
  params = {},
  allowNullParams = []
) => {
  const keys = Object.keys(params);
  const paramUrl = keys
    .reduce((result, key) => {
      const value = params[key];
      const isAllowNull = allowNullParams.includes(key);

      const shouldInclude =
        value !== undefined &&
        (isAllowNull || (value !== null && value !== "null")) && // Cho phép null nếu trong allowNullParams
        value !== "" &&
        !(Array.isArray(value) && (value.length === 0 || value[0] === "")) &&
        !(
          typeof value === "object" &&
          value !== null &&
          Object.keys(value).length === 0
        );

      return shouldInclude
        ? [
            ...result,
            `${key}=${key !== "sort" ? encodeURIComponent(value) : value}`,
          ]
        : [...result];
    }, [])
    .join("&");
  return `${url}${paramUrl ? `?${paramUrl}` : ""}`;
};

export const combineSort = (params = {}) => {
  const keys = Object.keys(params);
  const paramSort = keys
    .reduce(
      (result, key) =>
        params[key] &&
        params[key] !== undefined &&
        params[key] !== null &&
        params[key] !== ""
          ? [...result, `${key},${params[key] === 1 ? "asc" : "desc"}`]
          : [...result],
      []
    )
    .join("&sort=");
  return paramSort;
};

export function checkData(value, Array, valueCheck) {
  var data =
    Array && Array.length
      ? Array.find((option) => {
          return option[`${valueCheck ? valueCheck : "id"}`] === value;
        })
      : {};
  if (data) return data;
  return {};
}
export const hexToUtf8 = (hex) => {
  if (hex) return decodeURIComponent("%" + hex.match(/.{1,2}/g).join("%"));
  return hex;
};
export const formatPhone = (item = "") => {
  var data = item?.replaceAll(" ", "") || "";
  return data && data.length
    ? `${data?.substr(0, 4)} ${data?.substr(4, 3)} ${data?.substr(7)}`
    : data;
};

export const formatPhoneHideCharacters = (item = "") => {
  if (!item) return "";
  let strPhone = `${item}`;
  return `${strPhone?.substr(0, 4)}***${strPhone?.substr(7)}`;
};

export const handleBlurInput = (e) => {
  if (e.currentTarget.value === "0") e.currentTarget.value = "1";
};
export const handleKeypressInput = (e) => {
  const characterCode = e.key;
  let characterValue = e.target.value;
  if (characterCode === "Backspace") return;

  const characterNumber = Number(characterCode);
  if (characterNumber >= 0 && characterNumber <= 9) {
    if (e.currentTarget.value && e.currentTarget.value.length) {
      return;
    } else if (/0+/g.test(characterValue)) {
      e.preventDefault();
    } else if (/^0/g.test(characterValue)) {
      return e.target.value.slice(1);
    }
  } else {
    e.preventDefault();
  }
};

export const firstLetterWordUpperCase = (str) => {
  return str
    ? typeof str === "string"
      ? str
          .trim()
          .split(" ")
          .map((st) => st[0]?.toUpperCase() + st.toLowerCase().slice(1))
          .join(" ")
      : ""
    : "";
};

export const normalizeFirstLetter = (str) => {
  return str.charAt(0).toLowerCase() + str.slice(1);
};

export const addPrefixNumberZero = (number, length) => {
  if (isNil(number)) return;

  let num = number.toString();
  while (num.length < length) num = "0" + num;
  return num;
};

export const formatDecimal = (val) => {
  if (!val || val === 0 || val === "undefined") return 0;
  const num = String(val);
  const indexOfDot = num.indexOf(".");
  if (indexOfDot > 0) {
    const formattedNum = num.slice(0, indexOfDot);
    const decimal = num.slice(indexOfDot + 1, num.length);

    return formattedNum.replace(/\B(?=(\d{3})+(?!\d))/g, ".") + "," + decimal;
  }

  return num.replace(/\B(?=(\d{3})+(?!\d))/g, ".");
};

export const formatDecimalNoComma = (val) => {
  if (!val || val === 0 || val === "undefined") return 0;
  const num = String(val);
  const indexOfDot = num.indexOf(".");
  if (indexOfDot > 0) {
    const formattedNum = num.slice(0, indexOfDot);
    return formattedNum.replace(/\B(?=(\d{3})+(?!\d))/g, ".");
  }

  return num.replace(/\B(?=(\d{3})+(?!\d))/g, ".");
};

export const formatNumberInput = (value) => {
  return value
    ?.toLowerCase()
    .replace(
      /[eẽẹèéũụùúẵặăắằâẫậầấđềêễểệếưừữựứôồộốỗơỡờợớáãạàa-zA-Z!@#$%^&*()\-=_+|\\{}\[\]:':"<>,.?/~`; ]/gi,
      ""
    )
    .replace(/^(0)([0-9])$/gi, "$2")
    .replace(/^0{2,}/gi, "");
};

// tìm kiếm 2 từ cách xa nhau
export const searchString = (searchStr = "", str = "") => {
  const strRegExp = ("(" + searchStr.toLowerCase().trim() + ")")
    .split(" ")
    .join(")?(");
  return RegExp(strRegExp || "").test(str.toLowerCase());
};

// sắp xếp chữ
export const sortString = (key, value) => (a, b) => {
  if ((a[key] + "").toLowerCase() < (b[key] + "").toLowerCase())
    return value === 1 ? -1 : 1;
  if ((a[key] + "").toLowerCase() > (b[key] + "").toLowerCase())
    return value === 1 ? 1 : -1;
  return 0;
};

// formatter and parser input number
export const formatterNumber = (val, defaultNumber = 0) => {
  if (!val) return defaultNumber;
  return `${val}`
    .replace(/\B(?=(\d{3})+(?!\d))/g, ".")
    .replace(/\.(?=\d{0,2}$)/g, ",");
};

export const parserNumber = (val, digits = 2) => {
  if (!val) return 0;
  return Number.parseFloat(
    val.replace(/\$\s?|(\.*)/g, "").replace(/(\,{1})/g, ".")
  ).toFixed(digits);
};

// format number (for ui): [100.256,25]
export const formatNumber = (val, n = 2) => {
  // sửa lại làm tròn n phần tử
  const rounded = Number.parseFloat(`${val}`).toFixed(n);
  return rounded
    .replace(".", ",")
    .replace(/\B(?=(\d{3})+(?!\d))/g, ".")
    .replace(new RegExp(`,[0]{${n}}$`), "");
};

//normalize number (for api)
export const normalizeNumber = (val) =>
  (parserNumber(`${val}`) != NaN &&
    `${val}`.replace(/\.(?=(\d{3}))/g, "").replace(/\,(?=(\d{2}))/g, ".")) ||
  0;

export const confirmOpenFile = (pdf, print = false) => {
  window.refConfirm.current.show(
    {
      title: t("common.thongBao"),
      content: print
        ? t("common.fileDaDuocTaiVeBanCoMuonThucHienIn")
        : t("common.fileDaDuocTaiVeThanhCong"),
      showBtnOk: true,
      okText: print ? t("common.in") : t("common.xemFile"),
      cancelText: t("common.dong"),
      typeModal: "success",
      showIconContent: false,
    },
    () => {
      const popup = window.open(`/pdf-viewer/index.html?pdf=${pdf}`);
      if (popup) {
        if (print)
          popup.onload = () => {
            popup.print();
          };
      } else {
        alert(t("common.khongMoDuocFileLenCuaSoMoi"));
      }
    }
  );
};

// open new tab
export const openInNewTab = (url) => {
  if (window.openInNewTab) window.openInNewTab(url);
  else {
    if (isIOS()) confirmOpenFile(url, false);
    else {
      const newWindow = window.open(url, "_blank", "noopener,noreferrer");
      if (newWindow) newWindow.opener = null;
    }
  }
};

export const decodeBase64 = (hex) => {
  if (hex) return Buffer.from(hex, "base64").toString("utf8");
  return hex;
};

export const concatString = (...rest) => {
  let str = "";
  rest.forEach((item, index) => {
    if (index === 0) str = str.concat(item);
    else str = str.concat(" ").concat(item.toLowerCase());
  });
  return str;
};

export const isIOS = () => {
  if (window.isIOS) return window.isIOS(); // chờ sẵn để có thể config bằng mã tùy chỉnh
  const toMatch = [/iPhone/i, /iPad/i, /iPod/i];

  const ua = navigator.userAgent || navigator.vendor || window.opera;

  // iPad cũ
  const isOldIPad = /iPad/.test(ua);

  // iPad mới (iPadOS giả dạng macOS)
  const isNewIPad =
    (ua.includes("Macintosh") || ua.includes("Safari")) &&
    navigator.maxTouchPoints > 1;

  return (
    isOldIPad ||
    isNewIPad ||
    toMatch.some((toMatchItem) => {
      return navigator.userAgent.match(toMatchItem);
    })
  );
};

export const detectMob = () => {
  if (window.detectMob) return window.detectMob(); // chờ sẵn để có thể config bằng mã tùy chỉnh
  const toMatch = [/Android/i, /webOS/i, /BlackBerry/i, /Windows Phone/i];
  return (
    isIOS() ||
    toMatch.some((toMatchItem) => {
      return navigator.userAgent.match(toMatchItem);
    })
  );
};

export const parseFloatNumber = (strVal) => {
  const str = strVal?.toString().replaceAll(".", "").replaceAll(",", ".") || "";

  return str ? parseFloat(str) : 0;
};

export const numberToString = (value) => {
  const defaultNumbers = " hai ba bốn năm sáu bảy tám chín";
  const chuHangDonVi = ("1 một" + defaultNumbers).split(" ");
  const chuHangChuc = ("lẻ mười" + defaultNumbers).split(" ");
  const chuHangTram = ("không một" + defaultNumbers).split(" ");
  const dvBlock = "1 nghìn triệu tỷ".split(" ");
  const convert_block_three = (number) => {
    if (number == "000") return "";
    var _a = number + ""; //Convert biến 'number' thành kiểu string

    //Kiểm tra độ dài của khối
    switch (_a.length) {
      case 0:
        return "";
      case 1:
        return chuHangDonVi[_a];
      case 2:
        return convert_block_two(_a);
      case 3:
        var chuc_dv = "";
        if (_a.slice(1, 3) != "00") {
          chuc_dv = convert_block_two(_a.slice(1, 3));
        }
        var tram = chuHangTram[_a[0]] + " trăm";
        return tram + " " + chuc_dv;
    }
  };
  const convert_block_two = (number) => {
    var dv = chuHangDonVi[number[1]];
    var chuc = chuHangChuc[number[0]];
    var append = "";

    // Nếu chữ số hàng đơn vị là 5
    if (number[0] > 0 && number[1] == 5) {
      dv = "lăm";
    }

    // Nếu số hàng chục lớn hơn 1
    if (number[0] > 1) {
      append = " mươi";
      if (number[1] == 1) {
        dv = " mốt";
      }
    }

    return chuc + "" + append + " " + dv;
  };

  const to_vietnamese = (number) => {
    var str = parseInt(number) + "";
    var i = 0;
    var arr = [];
    var index = str.length;
    var result = [];
    var rsString = "";

    if (index == 0 || str == "NaN") {
      return "";
    }

    // Chia chuỗi số thành một mảng từng khối có 3 chữ số
    while (index >= 0) {
      arr.push(str.substring(index, Math.max(index - 3, 0)));
      index -= 3;
    }

    // Lặp từng khối trong mảng trên và convert từng khối đấy ra chữ Việt Nam
    for (i = arr.length - 1; i >= 0; i--) {
      if (arr[i] != "" && arr[i] != "000") {
        result.push(convert_block_three(arr[i]));

        // Thêm đuôi của mỗi khối
        if (dvBlock[i]) {
          result.push(dvBlock[i]);
        }
      }
    }

    // Join mảng kết quả lại thành chuỗi string
    rsString = result.join(" ");

    // Trả về kết quả kèm xóa những ký tự thừa
    return rsString.replace(/[0-9]/g, "").replace(/ /g, " ").replace(/ $/, "");
  };
  return to_vietnamese(value);
};
export function docSoViet(value) {
  if (!value) return "";
  let n = value + "";
  // Tạo danh sách các chữ số
  const chuSo = [
    "không",
    "một",
    "hai",
    "ba",
    "bốn",
    "năm",
    "sáu",
    "bảy",
    "tám",
    "chín",
    "mười",
  ];

  // Tạo danh sách các bậc số
  const bacSo = ["", "nghìn", "triệu", "tỷ"];

  // Hàm để đọc số từ 1 đến 999
  function docSoHangTram(soHangTram) {
    if (soHangTram === 0) {
      return "";
    } else if (soHangTram <= 10) {
      return chuSo[soHangTram];
    } else if (soHangTram < 100) {
      const chuc = Math.floor(soHangTram / 10);
      const donVi = soHangTram % 10;
      const _chuSoDonVi = donVi === 5 ? "lăm" : chuSo[donVi];
      if (chuc === 1) {
        return "mười" + (donVi > 0 ? " " + _chuSoDonVi : "");
      }
      return chuSo[chuc] + " mươi" + (donVi > 0 ? " " + _chuSoDonVi : "");
    } else {
      const tram = Math.floor(soHangTram / 100);
      const phanDu = soHangTram % 100;
      if (phanDu === 0) {
        return chuSo[tram] + " trăm";
      } else {
        return (
          chuSo[tram] +
          " trăm" +
          (phanDu > 0 ? " " + docSoHangTram(phanDu) : "")
        );
      }
    }
  }

  // Hàm chính để đọc số
  function docSoChinh(so) {
    if (so === 0) {
      return "không";
    }
    let chuoi = "";
    let bac = 0;
    while (so > 0) {
      const soHangNghin = so % 1000;
      if (soHangNghin > 0) {
        const chuoiHangNghin = docSoHangTram(soHangNghin);
        chuoi = chuoiHangNghin + " " + bacSo[bac] + " " + chuoi;
      }
      so = Math.floor(so / 1000);
      bac++;
    }
    return chuoi.trim();
  }

  // Kiểm tra xem n có dấu phẩy thập phân không
  if (n.includes(".")) {
    const parts = n.split(".");
    const phanNguyen = parseInt(parts[0]);
    const phanThapPhan = parseInt(parts[1]);
    const chuoiNguyen = docSoChinh(phanNguyen);
    let chuoiThapPhan = "";
    if (phanThapPhan > 0) {
      chuoiThapPhan = "phẩy ";
      for (let i = 0; i < parts[1].length; i++) {
        chuoiThapPhan += chuSo[parseInt(parts[1][i])] + " ";
      }
    }
    return chuoiNguyen + " " + chuoiThapPhan.trim();
  } else {
    return docSoChinh(parseInt(n));
  }
}

export const containText = (textA, textB, options) => {
  if (!textA) return false;
  if (!textB) return true;
  textA = textA.toLowerCase().unsignText().trim();
  textB = textB.toLowerCase().unsignText().trim();
  return textA.indexOf(textB) != -1;
};

const MAX = 100000;
export const filterSortText = (optionA, optionB, searchText) => {
  const searchTxt = searchText?.toLowerCase().unsignText().trim();
  const searchTxtLength = searchTxt.length;
  const charA = optionA?.label?.toLowerCase();
  const charB = optionB?.label?.toLowerCase();

  const getPosition = (char) => {
    let pos = char.indexOf(searchText);

    if (pos == -1) {
      //nếu ko tìm thấy kí tự chính xác theo tiếng Việt => sort đẩy về sau
      pos = MAX + char.unsignText().trim().indexOf(searchTxt);
    } else if (
      (char.charAt(pos - 1) == " " || char.charAt(pos - 1) == "") &&
      (char.charAt(pos + searchTxtLength) == " " ||
        char.charAt(pos + searchTxtLength) == "")
    ) {
      //trường hợp từ tìm kiếm nằm độc lập => sort ưu tiên cho xếp trước
      pos = pos - MAX;
    }
    return pos;
  };

  const posA = getPosition(charA);
  const posB = getPosition(charB);

  if (posA < posB) return -1;
  return 1;
};

export function nonAccentVietnamese(str) {
  if (!str || typeof str !== "string") return str;
  str = str.toLowerCase();
  str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
  str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
  str = str.replace(/ì|í|ị|ỉ|ĩ/g, "i");
  str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
  str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
  str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
  str = str.replace(/đ/g, "d");
  // Some system encode vietnamese combining accent as individual utf-8 characters
  str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ""); // Huyền sắc hỏi ngã nặng
  str = str.replace(/\u02C6|\u0306|\u031B/g, ""); // Â, Ê, Ă, Ơ, Ư
  return str;
}

export const locPhieuLisPacs = (
  { dsPhieuHis, dsPhieuLis, dsPhieuPacs } = {},
  {
    allData, // if true lấy về data để có thể in theo loại in được thiết lập, còn lại false thì chỉ lấy file và in thường
    isLocPhieu,
    isCdha,
    isXetNghiem,
    isAllHisLisPacs = false,
  } = {}
) => {
  const _dsPhieuHis = (dsPhieuHis || []).map((item) => ({
      ...item,
      isPhieuHis: true,
    })),
    _dsPhieuLis = (dsPhieuLis || []).map((item) => ({
      ...item,
      isPhieuLis: true,
    })),
    _dsPhieuPacs = dsPhieuPacs || [];
  const sortPhieuLisPacs = (data) => {
    if (isArray(data, true)) {
      return data.reduce((acc, cur) => {
        // update cho trường hợp [null, undefined,...] falsy values
        if (cur) {
          if (allData) cur.file = { pdf: cur.duongDan };
          acc.push(allData ? cur : cur.duongDan);
        }
        return acc;
      }, []);
    } else return [];
  };
  const locPhieuPacs = (data) => {
    let result = [];
    //lấy all phiếu pacs có thoiGianCoKetQua == null
    let dsPhieuPacsNull = data.filter((x) => x.thoiGianCoKetQua == null);

    //chỉ hiển thị kq mới nhất mà pacs trả về
    let objPacs = {},
      dsPhieuPacsNotNull = data.filter((x) => x.thoiGianCoKetQua !== null);

    // TH1: chỉ hiển thị kq mới nhất mà Pacs trả về nếu trùng soKetNoi
    // TH2: thoiGianCoKetQua bằng nhau thì so sánh id nào lớn hơn thì cho vào
    for (const item of dsPhieuPacsNotNull) {
      const sameEl = objPacs[`${item.soKetNoi}`];
      if (sameEl) {
        if (
          new Date(sameEl.thoiGianCoKetQua).getTime() <
          new Date(item.thoiGianCoKetQua).getTime()
        ) {
          objPacs[`${item.soKetNoi}`] = item;
        } else if (
          new Date(sameEl.thoiGianCoKetQua).getTime() ===
            new Date(item.thoiGianCoKetQua).getTime() &&
          parseInt(sameEl.id) < parseInt(item.id)
        ) {
          objPacs[`${item.soKetNoi}`] = item;
        } else {
          continue;
        }
      } else {
        objPacs[`${item.soKetNoi}`] = item;
      }
    }
    dsPhieuPacsNotNull = Object.values(objPacs);

    result = orderBy(
      [...dsPhieuPacsNotNull, ...dsPhieuPacsNull],
      "soPhieuId",
      "asc"
    );
    result = sortPhieuLisPacs(result);
    return result;
  };
  let dsPhieuPacsArr = sortPhieuLisPacs(_dsPhieuPacs);
  let dsPhieuLisArr = sortPhieuLisPacs(_dsPhieuLis);
  const dsPhieuHisArr = allData
    ? _dsPhieuHis
    : _dsPhieuHis.map((x) => x?.file?.pdf);
  if (isLocPhieu) {
    if (isCdha) {
      let hisCoSoKetNoi = _dsPhieuHis.some((i) =>
          i.duLieu?.hasOwnProperty("soKetNoi")
        ),
        inPhieuPacs;
      //check soKetNoi của his có tồn tại bên pacs k
      _dsPhieuHis.forEach((item) => {
        if (_dsPhieuPacs.some((i) => i.soKetNoi === item.duLieu?.soKetNoi)) {
          inPhieuPacs = true;
          return;
        }
      });
      if (inPhieuPacs) {
        //có thì in của pacs
        dsPhieuPacsArr = locPhieuPacs(_dsPhieuPacs);
      } else if (hisCoSoKetNoi && !isAllHisLisPacs) {
        //nếu lấy all his lis pacs thì sẽ thêm ở dưới
        dsPhieuPacsArr = [...dsPhieuHisArr, ...locPhieuPacs(_dsPhieuPacs)];
      } else {
        dsPhieuPacsArr = locPhieuPacs(_dsPhieuPacs);
      }
    } else {
      dsPhieuPacsArr = locPhieuPacs(_dsPhieuPacs);
    }

    if (!isXetNghiem) {
      //lấy all phiếu Lis có thoiGianCoKetQua == null
      let dsPhieuLisNull = _dsPhieuLis.filter(
        (x) => x.thoiGianCoKetQua == null
      );

      // TH1: chỉ hiển thị kq mới nhất mà Lis trả về nếu trùng soPhieuId và maBaoCao
      // TH2: thoiGianCoKetQua bằng nhau thì so sánh id nào lớn hơn thì cho vào
      let obj = {},
        dsPhieuLisNotNull = _dsPhieuLis.filter(
          (x) => x.thoiGianCoKetQua !== null
        );

      //SAKURA-62223: bổ sung check trùng key đường dẫn
      for (const item of dsPhieuLisNotNull) {
        const sameEl =
          obj[`${item.soPhieuId}_${item.maBaoCao}_${item.duongDan?.[0] || ""}`];
        if (sameEl) {
          if (
            new Date(sameEl.thoiGianCoKetQua).getTime() <
            new Date(item.thoiGianCoKetQua).getTime()
          ) {
            obj[`${item.soPhieuId}_${item.maBaoCao}`] = item;
          } else if (
            new Date(sameEl.thoiGianCoKetQua).getTime() ===
              new Date(item.thoiGianCoKetQua).getTime() &&
            parseInt(sameEl.id) < parseInt(item.id)
          ) {
            obj[
              `${item.soPhieuId}_${item.maBaoCao}_${item.duongDan?.[0] || ""}`
            ] = item;
          } else {
            continue;
          }
        } else {
          obj[
            `${item.soPhieuId}_${item.maBaoCao}_${item.duongDan?.[0] || ""}`
          ] = item;
        }
      }

      dsPhieuLisNotNull = Object.values(obj);

      dsPhieuLisArr = orderBy(
        [...dsPhieuLisNull, ...dsPhieuLisNotNull],
        "soPhieuId",
        "asc"
      );

      dsPhieuLisArr = sortPhieuLisPacs(dsPhieuLisArr);
    }
  }
  let data = [...dsPhieuPacsArr, ...dsPhieuLisArr];
  // Nếu ko có phiếu pacs và lis thì lấy phiếu his
  // hoặc Nếu check lấy tất cả
  if (data.length == 0 || isAllHisLisPacs) {
    data = [...data, ...dsPhieuHisArr];
  }
  return [...data];
};

export const isArray = (value, minLength, maxLength = Infinity) => {
  let _minLength = typeof minLength === "number" ? minLength : 1;
  return (
    Array.isArray(value) &&
    (minLength ? value.length >= _minLength && value.length <= maxLength : true)
  );
};

export const decimalAddition = (array = []) => {
  let result = 0;
  array.forEach((item) => (result += item * 100));
  return result / 100;
};
export const decimalSubtraction = (a, b) => (a * 100 - b * 100) / 100;

export const isNumber = (value) =>
  (typeof value === "number" && !isNaN(value)) ||
  (typeof value === "string" && !isEmpty(value) && !isNaN(Number(value)));

export const roundToDigits = (number, digits = 2) => {
  if (isNumber(number)) {
    return Number(Math.round(Number(number + "e" + digits)) + "e-" + digits);
  }
  return number;
};

export const roundNumberPoint = (number, point = 2) => {
  return (
    Math.round(Number(number || 0) * Math.pow(10, point)) / Math.pow(10, point)
  );
};

export const floorNumberPoint = (number, point = 2) => {
  return (
    Math.floor(Number(number || 0) * Math.pow(10, point)) / Math.pow(10, point)
  );
};

export const checkValidBody = (params = {}, fillWidthUndefined) => {
  const dataFilter = Object.entries(params).reduce((total, [key, value]) => {
    if (
      (value === 0 || value) &&
      (Array.isArray(value)
        ? isArray(
            value.filter((item) => item),
            1
          )
        : true)
    ) {
      return {
        ...total,
        [key]: value,
      };
    }
    return { ...total, ...(fillWidthUndefined && { [key]: undefined }) };
  }, {});

  return dataFilter;
};

export const renderMessTheoMaLoi = (s, dataUpdate) => {
  const { maHoSo, thoiGianVaoVien, doiTuong, doiTuongCu, loaiDoiTuong } =
    dataUpdate || {};
  const renderText = ({ value, content }) => {
    return value ? content : "";
  };
  let content = s?.message;
  const maHs = maHoSo || "";
  const thoiGian = thoiGianVaoVien
    ? moment(thoiGianVaoVien).format(FORMAT_DATE)
    : "";

  if (s.code === 7922 || s.code === 7925) {
    content = t("tiepDon.nguoiBenhConHoSoChuaThanhToan", {
      maHs: `<b>${maHs}</b>`,
      thoiGian: `<b>${thoiGian}</b>`,
      loaiDoiTuong: renderText({
        value: loaiDoiTuong?.ten,
        content: `<span> ${t("tiepDon.loaiDoiTuong").toLowerCase()} <b>${
          loaiDoiTuong?.ten
        }</b></span>`,
      }),
    });
    if (doiTuong === DOI_TUONG.BAO_HIEM && doiTuongCu === DOI_TUONG.BAO_HIEM) {
      content = t("tiepDon.nguoiBenhConHoSoBaoHiemChuaThanhToan", {
        maHs: `<b>${maHs}</b>`,
        thoiGian: `<b>${thoiGian}</b>`,
        loaiDoiTuong: renderText({
          value: loaiDoiTuong?.ten,
          content: `<span> ${t("tiepDon.loaiDoiTuong").toLowerCase()} <b>${
            loaiDoiTuong?.ten
          }</b></span>`,
        }),
      });
    }
  }

  if (s.code === 7923) {
    if (dataUpdate.doiTuong === DOI_TUONG.KHONG_BAO_HIEM) {
      content = t("tiepDon.nguoiBenhTonTaiHoSo", {
        maHs: `<b>${maHs}</b>`,
        thoiGian: `<b>${thoiGian}</b>`,
        loaiDoiTuong: renderText({
          value: loaiDoiTuong?.ten,
          content: `<span> ${t("tiepDon.loaiDoiTuong").toLowerCase()} <b>${
            loaiDoiTuong?.ten
          }</b></span>`,
        }),
      });
    } else {
      content = t("tiepDon.daTonTaiHoSoBaoHiemTrongNgay", {
        maHs: `<b>${maHs}</b>`,
        thoiGian: `<b>${thoiGian}</b>`,
        loaiDoiTuong: renderText({
          value: loaiDoiTuong?.ten,
          content: `<span> ${t("tiepDon.loaiDoiTuong").toLowerCase()} <b>${
            loaiDoiTuong?.ten
          }</b></span>`,
        }),
      });
    }
  }

  if (s.code === 7924) {
    if (doiTuongCu === DOI_TUONG.BAO_HIEM) {
      content = t("tiepDon.nguoiBenhConHoSoChuaThanhToan", {
        maHs: `<b>${maHs}</b>`,
        thoiGian: `<b>${thoiGian}</b>`,
        loaiDoiTuong: renderText({
          value: loaiDoiTuong?.ten,
          content: `<span> ${t("tiepDon.loaiDoiTuong").toLowerCase()} <b>${
            loaiDoiTuong?.ten
          }</b></span>`,
        }),
      });
    }
  }

  if (s.code === 7935) {
    content = t("tiepDon.nguoiBenhTonTaiMaHoSoTrongNgay", {
      maHs: `<b>${maHs}</b>`,
      loaiDoiTuong: renderText({
        value: loaiDoiTuong?.ten,
        content: `<span> ${t("tiepDon.loaiDoiTuong").toLowerCase()} <b>${
          loaiDoiTuong?.ten
        }</b></span>`,
      }),
    });
  }
  let subContent = "";
  if (s.code === 7925 || s.code === 7922 || s.code === 7923) {
    subContent = `<b>${t("common.luuY")}:</b><br/>
    ${t("tiepDon.chon")} <b>${t("common.huy")}:</b> ${t(
      "tiepDon.tiepTucSuDungMaHoSoCu"
    )} <br/>
    ${t("tiepDon.chon")} <b>${t("common.dongY")}:</b> ${
      s.code === 7925 ? `${t("tiepDon.boQuaDieuKienThanhToan")},` : ``
    } ${
      (s.code === 7923 && doiTuong === DOI_TUONG.BAO_HIEM) ||
      (s.code === 7922 && doiTuongCu === DOI_TUONG.BAO_HIEM)
        ? t("tiepDon.taoMaHoSoKhongBaoHiemMoi")
        : t("tiepDon.taoMaKhamChoNgayHienTai")
    } `;
  }
  return { content, subContent };
};
export const stripAccents = (input) => {
  if (input === "") return "";
  const inputReplaceSpace = input.replaceAll(" ", "0020");
  const input4Char = inputReplaceSpace.match(/.{1,4}/g);
  return input4Char.map((item) => String.fromCharCode(`0x${item}`)).join("");
};

export const containsSpecialChars = (str) => {
  const specialChars = /[`!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/;
  return specialChars.test(str);
};
export const converStringCCCD = (data) => {
  let value = data;
  if (!containsSpecialChars(data) && data.length > 17) {
    value = stripAccents(data);
  }
  return value;
};

export const updateObject = (data, input, value) => {
  const keys = input.split(".");
  let obj = data;

  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!obj[key]) obj[key] = {};
    obj = obj[key];
  }
  if (obj && keys?.length) obj[keys[keys.length - 1]] = value;

  return data;
};

export const checkBatBuocPhanPhongGiuong = (isBatBuocPhanPhongGiuong) => {
  if (isBatBuocPhanPhongGiuong.isMessage) {
    message.error(t("quanLyNoiTru.nguoiBenhChuaDuocPhanPhongGiuong"));
    if (isBatBuocPhanPhongGiuong.isError) {
      return true;
    }
  }
  return false;
};

/**
 * Converts a string value to its corresponding primitive value.
 * @param {string} value - The string value to be converted.
 * @returns {undefined|null|string} - The converted primitive value.
 */
export const convertStringToPrimitive = (
  value,
  { convertFields = ["undefined", "null", "true", "false"] } = {}
) => {
  if (convertFields.includes(value)) {
    return eval(value);
  }
  return value;
};

/**
 * usage: transformQueryString(keyMap)
 * @param {Object} keyMap - The keyMap object.
 * @param {Object} keyMap.key - The key of the query string object.
 * @param keyMap.key.format - format value when queryString is not empty
 * @returns {Object} - The transformed query string object.
 */
export const transformQueryString = (keyMap, searchParams) => {
  const queries =
    searchParams ||
    getAllQueryString(window.location.search, {
      filterEmpty: false,
    });
  for (let key in queries) {
    queries[key] = convertStringToPrimitive(queries[key], {
      convertFields: ["undefined", "null"],
    });
  }
  const mergeFormat = (value, type, format) => {
    if (type === "dateOptions") {
      return format ? format(moment(+value), value) : moment(+value);
    } else {
      return format ? format(value) : value;
    }
  };

  const mutate = (type, key, format) => {
    if (type === "dateOptions" && queries[key] === "-") {
      delete queries[key];
    } else if (type === "boolean") {
      queries[key] = format
        ? format(queries[key])
        : queries[key]?.toLowerCase() === "true";
    } else {
      queries[key] = mergeFormat(queries[key], type, format);
    }
  };

  for (const [keyMapKey, options] of Object.entries(keyMap)) {
    const { format, type, defaultValue } = options;
    if (queries[keyMapKey]) mutate(type, keyMapKey, format);
    else if (!(keyMapKey in queries) && defaultValue !== undefined) {
      queries[keyMapKey] = defaultValue;
    }
  }

  return queries;
};

export const findFirstFocusableElement = (container) => {
  const isFocusable = (item) => {
    if (item.tabIndex < 0) {
      return false;
    }
    switch (item.tagName) {
      case "A":
        return !!item.href;
      case "INPUT":
        return item.type !== "hidden" && !item.disabled;
      case "SELECT":
      case "TEXTAREA":
      case "BUTTON":
        return !item.disabled;
      default:
        return false;
    }
  };

  return Array.from(container.getElementsByTagName("*")).find(isFocusable);
};

export const getUniqueData = (data, fieldPath) => {
  if (!Array.isArray(data)) return [];
  return Array.from(
    new Map(
      (data || []).map((item) => [get(item, fieldPath, ""), item])
    ).values()
  );
};

export function safeConvertToArray(input, acceptType = ["number", "string"]) {
  if (Array.isArray(input)) {
    return input;
  }
  if (acceptType.includes(typeof input)) {
    return [input];
  }
  return [];
}

export const isBoolean = (value) => typeof value === "boolean";

export const copyToClipboard = (text, msg) => {
  if (navigator.clipboard && window.isSecureContext) {
    // HTTPS hoặc localhost → dùng Clipboard API
    navigator.clipboard
      .writeText(text)
      .then(() => {
        message.success(msg || t("quanLyNoiTru.saoChepThanhCong"));
      })
      .catch((err) => {
        console.error(err);
        fallbackCopy(text, msg);
      });
  } else {
    // HTTP hoặc không hỗ trợ → fallback
    fallbackCopy(text, msg);
  }
};

const fallbackCopy = (text, msg) => {
  const textarea = document.createElement("textarea");
  textarea.value = text;
  textarea.style.position = "fixed";
  textarea.style.left = "-9999px";

  document.body.appendChild(textarea);
  textarea.focus();
  textarea.select();

  try {
    const successful = document.execCommand("copy");
    if (successful) {
      message.success(msg || t("quanLyNoiTru.saoChepThanhCong"));
    } else {
      message.error(t("quanLyNoiTru.saoChepThatBai"));
    }
  } catch (err) {
    console.error("Fallback copy failed:", err);
    message.error(t("quanLyNoiTru.saoChepThatBai"));
  }

  document.body.removeChild(textarea);
};

export const sleep = (ms) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

export const cleanObjectUpdate = (obj = {}, key, value) => {
  const newObj = cloneDeep(obj);
  newObj[key] = value;

  Object.keys(newObj).forEach((sortKey) => {
    if (!newObj[sortKey]) {
      delete newObj[sortKey];
    }
  });

  return newObj;
};

export const randomStringAndNumber = (length) => {
  const characters =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let result = "";
  for (let i = 0; i < length; i++) {
    const randomIdx = Math.floor(Math.random() * characters.length);
    result += characters[randomIdx];
  }
  return result;
};

export const getAndConvertAuthDataToString = ({ thoiGianDenHan = "" } = {}) => {
  let authStorage = localStorage.getItem("_AUTH2");
  if (authStorage) {
    authStorage = cacheUtils.encrypt(
      JSON.stringify(
        {
          data: authStorage,
          thoiGianDenHan: thoiGianDenHan ?? "",
        },
        null,
        2
      )
    );
    authStorage = randomStringAndNumber(20) + authStorage;
  }

  return authStorage;
};

export const isUndefined = (value) =>
  [
    "undefined",
    "NaN",
    undefined,
    "",
    null,
    "null",
    false,
    "false",
    "Invalid date",
    Infinity,
  ].includes(value);

export const isObject = (value, isNotEmpty) => {
  let isObj = !!value && typeof value === "object" && !isUndefined(value);
  return isObj && isNotEmpty ? !isEmpty(value) : isObj;
};

export const parseDataDsToArray = (obj) => {
  return Object.keys(cloneDeep(obj)).reduce((acc, key) => {
    if (key.startsWith("ds") && !Array.isArray(obj[key])) {
      acc[key] = [obj[key]]; // Chuyển sang array nếu là ds
    } else {
      acc[key] = obj[key]; // giữ nguyên params
    }
    return acc;
  }, {});
};

export const cleanEmptyData = (data) => {
  if (data === null || data === undefined) {
    return undefined;
  }

  if (Array.isArray(data)) {
    const filtered = data
      .map((item) => cleanEmptyData(item))
      .filter((item) => item !== undefined);
    return filtered.length ? filtered : undefined;
  }

  if (typeof data === "object") {
    const cleaned = Object.entries(data)
      .map(([key, value]) => [key, cleanEmptyData(value)])
      .filter(([_, value]) => value !== undefined)
      .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});
    return Object.keys(cleaned).length ? cleaned : undefined;
  }

  if (typeof data === "string") {
    return data.trim() === "" ? undefined : data;
  }

  return data;
};

export const fillEmptyData = (data, options = {}) => {
  const {
    undefinedToNull = true,
    undefinedToEmptyArray = false,
    undefinedToEmptyObject = false,
    emptyStringToNull = true,
  } = options;

  if (data === undefined) {
    if (undefinedToEmptyArray) {
      return [];
    } else if (undefinedToEmptyObject) {
      return {};
    } else if (undefinedToNull) {
      return null;
    } else {
      return undefined;
    }
  }

  if (data === null) {
    return null;
  }

  if (Array.isArray(data)) {
    return data.map((item) => fillEmptyData(item, options));
  }

  if (typeof data === "object" && !isMoment(data)) {
    const filled = Object.entries(data).reduce(
      (acc, [key, value]) => ({
        ...acc,
        [key]: fillEmptyData(value, options),
      }),
      {}
    );
    return filled;
  } else if (isMoment(data)) {
    return data;
  }

  if (typeof data === "string") {
    return emptyStringToNull && data.trim() === "" ? null : data;
  }

  return data;
};
export const resizeImage = (base64Str, maxWidth = 400, maxHeight = 200) => {
  return new Promise((resolve) => {
    const img = new Image();
    img.src = base64Str;
    img.onload = () => {
      const canvas = document.createElement("canvas");
      const MAX_WIDTH = maxWidth;
      const MAX_HEIGHT = maxHeight;
      let width = img.width;
      let height = img.height;

      if (width > height) {
        if (width > MAX_WIDTH) {
          height *= MAX_WIDTH / width;
          width = MAX_WIDTH;
        }
      } else {
        if (height > MAX_HEIGHT) {
          width *= MAX_HEIGHT / height;
          height = MAX_HEIGHT;
        }
      }

      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext("2d");
      ctx.drawImage(img, 0, 0, width, height);
      resolve(canvas.toDataURL());
    };
  });
};

export const isValidMaTheByht = (maThe) => {
  const value = maThe?.trim();
  if (!value) return false;

  if (/^[a-zA-Z]/.test(value)) {
    return value.length === 15;
  }

  if (/^[0-9]/.test(value)) {
    return value.length <= 15;
  }

  return false;
};

export const parseFloatNumberFromString = (value) => {
  return typeof value === "string" ? parseFloatNumber(value) : value ?? null;
};

export const getIntervalMs = (value, defaultValue, unit = "second") => {
  const parsed = parseInt(value, 10);
  const validNumber = isNumber(parsed) && parsed > 0 ? parsed : defaultValue;
  if (unit === "minute") {
    return validNumber * 60 * 1000;
  }
  return validNumber * 1000;
};
export const splitArrayIntoChunks = (arr, chunkSize = 12) => {
  let result = [];
  for (let i = 0; i < arr.length; i += chunkSize) {
    // Đưa mảng con vào đối tượng với khóa 'dsCot'
    result.push(arr.slice(i, i + chunkSize));
  }

  // Kiểm tra nếu mảng cuối cùng không đủ 12 phần tử
  let lastChunk = result[result.length - 1];
  if (lastChunk?.length < chunkSize) {
    // Chèn thêm các đối tượng rỗng cho đến khi đủ 12 phần tử
    while (lastChunk?.length < chunkSize) {
      lastChunk.push({}); // Thêm đối tượng rỗng
    }
  }

  return result;
};

export const fillNullPayload = (data) =>
  Object.fromEntries(
    Object.entries(data).map(([key, value]) => [key, value ?? null])
  );
export const renderNbDiaChi = ({ dataDiachi = {}, dataMacDinh }) => {
  /*
  SAKURA-67495 FE (OP) [Tiếp đón] Không tự động fill địa chỉ cũ NB đã inactive trong danh mục
  nam.mn 16/07/2025 - không sinh địa chỉ ở đây nữa, mà sẽ đẩy text diaChi vào dataTemp :{diaChi} để component AddressFull tự parse ra
  */
  let nbDiaChi = {
    soNha: dataDiachi?.soNha,
    quocGiaId: dataMacDinh?.quocTich?.id,
  };
  return nbDiaChi;
};

export const onCheckThongTinTheBh = ({
  thongTinCoBan,
  dataBaoHiem,
  listGioiTinh,
}) => {
  if (window.checkThongTinThe) {
    return window.checkThongTinThe({
      thongTinCoBan,
      dataBaoHiem,
      listGioiTinh,
    });
  }
  const { tuNgayTheBhyt, denNgayTheBhyt, gioiTinh, maNoiDangKy } =
    thongTinCoBan || {};

  const format = "DD/MM/YYYY";
  let tuNgay = moment(tuNgayTheBhyt).format(format);
  let denNgay = moment(denNgayTheBhyt).format(format);
  const {
    gtTheTu,
    gtTheDen,
    gioiTinh: congBhGioiTinh,
    maDKBD,
  } = dataBaoHiem || {};
  let diffTuNgay = moment(gtTheTu, format).diff(moment(tuNgay, format), "days");
  let diffDenNgay = moment(gtTheDen, format).diff(
    moment(denNgay, format),
    "days"
  );
  const nbGioiTinh = listGioiTinh.find((i) => i.id === gioiTinh)?.ten;
  let obj = {
    tuNgay: {
      nguoiBenh: tuNgay,
      congBh: gtTheTu,
      condition: gtTheTu && diffTuNgay !== 0,
      title: t("thuNgan.giaTriTheTu"),
    },
    denNgay: {
      nguoiBenh: denNgay,
      congBh: gtTheDen,
      condition: gtTheDen && diffDenNgay !== 0,
      title: t("thuNgan.giaTriTheDen"),
    },
    gioiTinh: {
      nguoiBenh: nbGioiTinh,
      congBh: congBhGioiTinh,
      condition: congBhGioiTinh && congBhGioiTinh !== nbGioiTinh,
      title: t("common.gioiTinh"),
    },
    maNoiDangKy: {
      nguoiBenh: maNoiDangKy,
      congBh: maDKBD,
      condition: maDKBD && maNoiDangKy !== maDKBD,
      title: t("tiepDon.noiDangKy"),
    },
  };
  let errors = [];
  Object.entries(obj).forEach(([key, value]) => {
    if (value.condition) {
      errors.push({
        key,
        title: value.title,
        nguoiBenh: value.nguoiBenh,
        congBh: value.congBh,
      });
    }
  });

  return errors;
};

export const kiemTraTenNb = (tenNbCu, tenNbMoi) => {
  const convertTenNb = (data) =>
    removeVietnameseTones(data)?.trim().toUpperCase();

  return convertTenNb(tenNbCu) !== convertTenNb(tenNbMoi);
};

export const chuyenDoiTenNb = (tenNb, dsThayThe) => {
  if (window.chuyenDoiTenNb)
    return window.chuyenDoiTenNb(tenNb, dsThayThe == []);
  const ds = [
    // { key: "Ơ\\", value: "Ơ̆" },
    // { key: "A\\", value: "Ă" },
    // { key: "C\\", value: "Č" },
    // { key: "U\\", value: "Ŭ" },
    // { key: "E\\", value: "Ĕ" },
    // { key: "O\\", value: "Ŏ" },
    // { key: "I\\", value: "Ĭ" },
    // { key: "]", value: "Č" },
    // { key: "B\\", value: "Ƀ" },
    // { key: "N\\", value: "Ñ" },
    ...(isArray(dsThayThe) ? dsThayThe : []),
  ];
  tenNb = tenNb.toUpperCase();
  ds.forEach((item) => {
    tenNb = tenNb.replaceAll(item.key, item.value);
  });
  return tenNb;
};

export const isInputting = () => {
  const el = document.activeElement;
  const tag = el?.tagName?.toLowerCase();

  return (
    ["input", "textarea", "select"].includes(tag) ||
    el?.getAttribute("contenteditable") === "true" ||
    el?.closest(".ant-select") !== null ||
    el?.classList?.contains("ant-select-selector")
  );
};

export const getMoTaChanDoan = (dsMoTa) => {
  return dsMoTa
    .filter((item) => item)
    .map((item2) =>
      item2
        .map((item) =>
          !!item.moTa
            ? `${item.tenChanDoan?.trim() ?? ""} (${item.moTa?.trim()})`
            : item.tenChanDoan?.trim()
        )
        .join("; ")
    )
    .filter((item) => item)
    .join("/ ");
};

export const getDsMoTa = (dataChanDoan, key, key2) => {
  if (!dataChanDoan || !dataChanDoan[key]) {
    return [];
  }
  return (dataChanDoan?.[key] || []).map((item) => {
    return {
      id: item.id,
      tenChanDoan: `${item.ma} - ${item.ten}`,
      moTa: dataChanDoan?.moTaChanDoan?.[key2 || key]?.find(
        (item2) => item2.id == item.id
      )?.moTa,
    };
  });
};

export const calculateAgeChildren = (birthDate, currentDate = new Date()) => {
  if (!birthDate) return "";

  const birth = new Date(birthDate);
  const diffMs = currentDate - birth;
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  // Trường hợp 1: <= 72 giờ
  if (diffHours <= 72) {
    return `${diffHours} giờ tuổi`;
  }

  // Trường hợp 2: <= 30 ngày
  if (diffDays <= 30) {
    return `${diffDays} ngày tuổi`;
  }

  // Trường hợp 3: <= 1 năm
  let years = currentDate.getFullYear() - birth.getFullYear();
  let months = currentDate.getMonth() - birth.getMonth();
  let days = currentDate.getDate() - birth.getDate();

  if (days < 0) {
    months--;
    const prevMonth = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      0
    );
    days += prevMonth.getDate();
  }
  if (months < 0) {
    years--;
    months += 12;
  }

  if (years < 1) {
    const parts = [];
    if (months > 0) parts.push(`${months} tháng`);
    if (days > 0) parts.push(`${days} ngày`);
    return parts.join(", ") + " tuổi";
  }

  // Trường hợp 4: > 1 năm
  return `${years} tuổi`;
};

export const parseListConfig = (data, isNumber = false) => {
  if (!data) return [];
  return data
    .split(",")
    .map((item) => item.trim())
    .filter((item) => item !== "" && item != null)
    .map((item) => (isNumber ? Number(item) : item));
};
