import { LOAI_THUOC } from "constants/index";
import { flatten, groupBy, orderBy } from "lodash";

const tenDonThuoc = (t) => ({
  [LOAI_THUOC.TU_TRUC]: t("khamBenh.donThuoc.donThuocTuTruc"),
  [LOAI_THUOC.RA_VIEN]: t("khamBenh.donThuoc.thuocKho"),
  [LOAI_THUOC.BHYT]: t("khamBenh.donThuoc.donThuocBHYT"),
  [LOAI_THUOC.THUOC_LINH_NOI_TRU]: t("khamBenh.donThuoc.thuocKho"),
});

export const evalString = (val) => {
  if (typeof val === "number") return val;

  if (!val || val === "") return 0;

  const str = String(val).trim();

  if (!str) return 0;

  try {
    if (str.includes("/")) {
      const parts = str.split("/");
      if (parts.length === 2) {
        const numerator = parseFloat(parts[0].replace(",", "."));
        const denominator = parseFloat(parts[1].replace(",", "."));

        if (!isNaN(numerator) && !isNaN(denominator) && denominator !== 0) {
          return numerator / denominator;
        }
      }
    }

    const normalizedStr = str.replace(",", ".");

    const result = parseFloat(normalizedStr);

    return isNaN(result) ? 0 : result;
  } catch (error) {
    console.warn("evalString: Failed to parse value:", val, error);
    return 0;
  }
};

//gộp thuốc khác lô cùng lần chỉ định
export const gopThuocKhacLo = (data) => {
  const _groupThuoc = {
    ...groupBy(
      data.filter((item) => item.chiDinhId != null),
      "chiDinhId"
    ),
    ...data
      .filter((item) => item.chiDinhId == null)
      .reduce((acc, item, idx) => {
        acc[`null_${idx}`] = [item]; // tạo key riêng cho từng item null
        return acc;
      }, {}),
  };

  return Object.values(_groupThuoc).map((item) => {
    const reduceObj = (key) =>
      item.reduce(
        (accumulator, currentValue) => {
          if (key == "soLuongSuDung") {
            //với key soLuongSuDung trong phiếu truyền dịch => giữ giá trị null để đảm bảo logic tính ban đầu
            if (accumulator == null && currentValue[key] == null) {
              return null;
            }
          }
          return accumulator + (currentValue[key] || 0);
        },
        key == "soLuongSuDung" ? null : 0
      );

    const dsThuocGop = orderBy(
      item,
      ["ngayHanSuDung", "thoiGianDuyet"],
      ["desc", "desc"]
    );

    return {
      ...dsThuocGop[0],
      ...[
        "soLuong",
        "soLuongYeuCau",
        "soLuongTra",
        "thanhTien",
        "soLuongHuy",
        "soLuongSoCap",
        "soLuongSuDung",
        "slDung",
      ].reduce(
        (accumulator, currentValue) => ({
          ...accumulator,
          [currentValue]: reduceObj(currentValue),
        }),
        {}
      ),
      //nếu trong thuốc gộp có field children thì mới thực hiện để tránh tạo ra line con rỗng trong bảng
      ...(dsThuocGop.some((x) => x.hasOwnProperty("children"))
        ? {
            children: flatten(dsThuocGop.map((x) => x.children || [])), // giữ lại các thuốc dùng kèm
          }
        : {}),
      ...(dsThuocGop.some((x) => x.hasOwnProperty("dsThuocDungKem"))
        ? {
            dsThuocDungKem: flatten(
              dsThuocGop.map((x) => x.dsThuocDungKem || [])
            ), // giữ lại các thuốc dùng kèm
          }
        : {}),
      dsThuocGopId: dsThuocGop.map((x) => x.id),
      dsThuocGop,
    };
  });
};

export const groupThuoc = (
  t,
  listDvThuoc = [],
  listLoaiThuoc = [],
  tachTheoBacSi = false
) => {
  const grouped = flatten(
    Object.values(groupBy(listDvThuoc, "loai"))
      .map((item) => {
        return groupBy(item, "soPhieu");
      })
      .map((item) => Object.values(item))
  ).map((item) => {
    const { loai, loaiDonThuoc, soPhieu, phieuNhapXuatId } = item[0];

    let _table = tachTheoBacSi
      ? Object.values(groupBy(item, "bacSiChiDinhId"))
      : [item];

    _table = _table.map((item) => gopThuocKhacLo(item));

    return {
      loai,
      loaiDonThuoc,
      phieuNhapXuatId,
      key: loai + "_" + (soPhieu || ""),
      tenDon:
        (tenDonThuoc(t)[loai] ||
          listLoaiThuoc.find((item2) => item2.id == loai)?.ten) +
        (soPhieu ? ` - ${soPhieu}` : ""),
      soPhieu,
      table: _table,
      listDvThuoc: item,
    };
  });
  return grouped;
};

export const tinhSoLuong = ({
  soNgay,
  soLuong1Lan = 1,
  soLan1Ngay,
  soLuongHuy = 0,
}) => {
  soLuongHuy = evalString(soLuongHuy);
  soLuong1Lan = evalString(soLuong1Lan);
  if (!soNgay) return 0;
  return soNgay * soLan1Ngay * soLuong1Lan + soLuongHuy;
};

export const tinhSoNgay = ({
  soLuong,
  soLuongHuy = 0,
  soLan1Ngay,
  soLuong1Lan,
}) => {
  soLuongHuy = evalString(soLuongHuy);
  soLuong1Lan = evalString(soLuong1Lan);
  if (!soLuong) return 0;
  return Math.floor((soLuong - soLuongHuy) / (soLuong1Lan * soLan1Ngay));
};

export const calculateSL1LanAndLan1Ngay = (array = []) => {
  const result = { soLuong1Lan: null, soLan1Ngay: null };
  const arrValue = array
    .filter((item) => item)
    .map((item) => evalString(item.toString()));

  const truthyValue = arrValue[0];
  const priorityValue =
    array.find((item) => typeof item === "string" && item.includes("/")) ||
    truthyValue;

  if (truthyValue && arrValue.every((x) => truthyValue === x)) {
    result.soLuong1Lan = priorityValue || truthyValue;
    result.soLan1Ngay = arrValue.length;
  }
  return result;
};

export const tinhSoLuongV2 = ({
  isTinhSLTongTheoSLBuoi = false,
  soNgay,
  soLuong1Lan,
  soLan1Ngay,
  soLuongHuy = 0,
  slSang = "",
  slChieu = "",
  slToi = "",
  slDem = "",
}) => {
  soLuongHuy = evalString(soLuongHuy) || 0;
  soLuong1Lan = evalString(soLuong1Lan) || 0;

  if (!soNgay) return 0;

  if (isTinhSLTongTheoSLBuoi) {
    slSang = evalString(slSang) || 0;
    slChieu = evalString(slChieu) || 0;
    slToi = evalString(slToi) || 0;
    slDem = evalString(slDem) || 0;

    if (slSang || slChieu || slToi || slDem)
      return soNgay * (slSang + slChieu + slToi + slDem) + soLuongHuy;
    if (soLuong1Lan && soLan1Ngay)
      return soNgay * soLan1Ngay * soLuong1Lan + soLuongHuy;
    return null;
  } else {
    if (soLuong1Lan && soLan1Ngay)
      return soNgay * soLan1Ngay * soLuong1Lan + soLuongHuy;
    return null;
  }
};

export const tinhSoNgayV2 = ({
  isTinhSLTongTheoSLBuoi = false,
  soLuong,
  soLuongHuy = 0,
  soLan1Ngay,
  soLuong1Lan,
  slSang = "",
  slChieu = "",
  slToi = "",
  slDem = "",
  maxSoNgay,
}) => {
  soLuongHuy = evalString(soLuongHuy) || 0;

  if (!soLuong) return 0;
  let soNgay = null;
  if (isTinhSLTongTheoSLBuoi) {
    slSang = evalString(slSang) || 0;
    slChieu = evalString(slChieu) || 0;
    slToi = evalString(slToi) || 0;
    slDem = evalString(slDem) || 0;

    if (slSang || slChieu || slToi || slDem)
      soNgay = Math.floor(
        (soLuong - soLuongHuy) / (slSang + slChieu + slToi + slDem)
      );
    if (soLuong1Lan && soLan1Ngay) {
      soNgay = Math.floor((soLuong - soLuongHuy) / (soLuong1Lan * soLan1Ngay));
    }
  } else {
    soLuong1Lan = evalString(soLuong1Lan);

    if (soLuong1Lan && soLan1Ngay) {
      soNgay = Math.floor((soLuong - soLuongHuy) / (soLuong1Lan * soLan1Ngay));
    }
  }
  if (maxSoNgay && soNgay > maxSoNgay) {
    return maxSoNgay;
  } else {
    return soNgay;
  }
};

export default {
  tinhSoLuong,
  tinhSoNgay,
  groupThuoc,
  tenDonThuoc,
  evalString,
  tinhSoLuongV2,
  tinhSoNgayV2,
};
