import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { useDispatch } from "react-redux";
import imgSearch from "assets/images/template/icSearch.png";
import { Main } from "./styled";
import {
  HOTKEY,
  LOAI_CHI_DINH,
  LOAI_DICH_VU,
  THIET_LAP_CHUNG,
} from "constants/index.js";
import {
  Popover,
  InputTimeout,
  Button,
  ModalTemplate,
  Select,
  Checkbox,
} from "components";
import { message, Row, Radio } from "antd";
import TableVatTu from "./TableVatTu";
import { useTranslation } from "react-i18next";
import {
  useConfirm,
  useStore,
  useGuid,
  useThietLap,
  useDelayedState,
} from "hooks";
import cacheUtils from "lib-utils/cache-utils";
import useNbInfoTitle from "pages/khamBenh/hooks/useNbInfoTitle";
import { SVG } from "assets";
import useKiemTra<PERSON>hung<PERSON>hi from "pages/chiDinhDichVu/components/useKiemTraChungChi";
import { isArray } from "utils/index";
import { uniqBy } from "lodash";

const DichVuVatTu = (props, ref) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const nbInfoTitle = useNbInfoTitle();
  const configData = useStore("chiDinhKhamBenh.configData", null);
  const refModal = useRef(null);
  const [refIsSubmit, onChangeStateSubmit] = useDelayedState(3000);
  const refTable = useRef();
  const refInput = useRef(null);
  const layerId = useGuid();
  const refSubmit = useRef(null);
  const refCallback = useRef(null);
  const [kiemTraChungChi] = useKiemTraChungChi();

  const [dataHIEN_THI_CHECKBOX_TU_TRUC] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_CHECKBOX_TU_TRUC
  );

  const { isShowSoLuongDinhMuc = false, isVatTuXN = false } = props;

  const {
    chiDinhVatTu: {
      tamTinhTien,
      chiDinhDichVu,
      getListDichVuVatTu,
      getListDichVuTonKho,
    },
    phimTat: { onRegisterHotkey, onAddLayer, onRemoveLayer },
  } = useDispatch();

  const nhanVienId = useStore("auth.auth.nhanVienId", null);

  const [state, _setState] = useState({
    show: false,
    listSelectedDv: [],
    thanhTien: 0,
    activeHotKey: true,
    theoLo: false,
  });

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    if (state.show) {
      if (!state.notShowModal) {
        dangKyHotKey();
      }
      return () => {
        !state.notShowModal && onRemoveLayer({ layerId: layerId });
      };
    }
  }, [state.show]);

  useEffect(() => {
    return () => {
      onRemoveLayer({ layerId: layerId });
    };
  }, []);

  useEffect(() => {
    async function fetchData() {
      let data = await cacheUtils.read(
        "",
        "DATA_THEO_SO_LUONG_TON_KHO_VAT_TU",
        null,
        false
      );
      const splitCacheCustomize = await cacheUtils.read(
        nhanVienId,
        "DATA_CUSTOMIZE_COLUMN_" + "split_VATTU_ChiDinhVatTu",
        [],
        false
      );
      if (data) {
        setState({
          theoSoLuongTonKho: data?.theoSoLuongTonKho,
          splitCacheCustomize,
        });
      } else {
        setState({ theoSoLuongTonKho: 15, splitCacheCustomize });
      }
    }
    fetchData();
  }, []);

  useEffect(() => {
    if (!state.show) {
      setTimeout(() => refModal.current && refModal.current.hide(), 50);
    } else {
      refModal.current && refModal.current.show({});

      if (!state.notShowModal) {
        //focus vào ô tìm kiếm
        setTimeout(() => {
          refInput.current && refInput.current.focus();
        }, 1000);
      }
    }
  }, [state.show]);

  const dangKyHotKey = () => {
    onAddLayer({ layerId: layerId });
    onRegisterHotkey({
      layerId: layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.F2, //F2
          onEvent: (e) => {
            onChangeInput("");
            refInput.current && refInput.current.focus();
          },
        },
        {
          keyCode: HOTKEY.F4, //F4
          onEvent: () => {
            refSubmit.current && refSubmit.current();
          },
        },
      ],
    });

    setState({ activeHotKey: true });
  };

  useImperativeHandle(ref, () => ({
    show: (option = {}, callback) => {
      setState({
        show: true,
        filterText: "",
        dataSource: option?.dataSource || {},
        dataKho: option?.dataKho || [],
        sttBo: option?.sttBo,
        vatTuBoId: option?.vatTuBoId,
        notShowModal: option.notShowModal || false,
        khoId: option?.khoId,
        listSelectedDv: [],
        isTuTruc: option.isTuTruc || false,
        showChiDinhTuDichVuId: option.showChiDinhTuDichVuId || false,
        chiDinhTuDichVuIdOptions: option.chiDinhTuDichVuIdOptions || {},
      });

      onChangeStateSubmit(false);
      refCallback.current = callback;

      if (!option.notShowModal) {
        dangKyHotKey();
      }
    },
    onSubmit,
    onFocusInside: () => {
      dangKyHotKey();
    },
    onFocusInput: () => {
      //focus vào ô tìm kiếm
      setTimeout(() => {
        refInput.current && refInput.current.focus();
      }, 1000);
    },
    onOutsideClick: () => {
      onRemoveLayer({ layerId: layerId });
      setState({ activeHotKey: false });
    },
  }));

  const { thanhTien } = state;

  const onTamTinhTien = (listSelected, onError = () => {}) => {
    const payload = listSelected.map((item) => ({
      nbDotDieuTriId: configData?.nbDotDieuTriId,
      sttBo: state.sttBo || item?.sttBo,
      nbDichVu: {
        dichVuId: item?.dichVuId,
        nguonKhacId: item.nguonKhacId,
        soLuong: item.soLuong,
        khoaChiDinhId: configData?.khoaChiDinhId,
        chiDinhTuDichVuId: state.showChiDinhTuDichVuId
          ? item.chiDinhTuDichVuId
          : configData?.chiDinhTuDichVuId,
        chiDinhTuLoaiDichVu: configData?.chiDinhTuLoaiDichVu,
        ...(isVatTuXN ? { khongTinhTien: true } : {}),
      },
      nbDvKho: {
        khoId: state.khoId,
      },
    }));
    tamTinhTien(payload).then((s) => {
      //"code": 8324, "message": "Hồ sơ đã quyết toán Bảo hiểm Y tế"
      //=> case này báo lỗi và reset lại ko cho thêm dịch vụ
      if (s?.code == 8324) {
        setState({
          thanhTien: 0,
          listSelectedDv: [],
        });
        onError();
        message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
        return;
      }

      const thanhTien = Array.isArray(s)
        ? (s || []).reduce(
            (accumulator, currentValue) =>
              accumulator + currentValue.nbDichVu.thanhTien,
            0
          )
        : 0;
      const messageWarning = Array.isArray(s)
        ? ((s || []).filter((x) => x.message) || []).reduce(
            (value, currentValue) => value.concat(currentValue.message + ", "),
            ""
          )
        : "";
      if (messageWarning?.length) message.warning(messageWarning);
      setState({
        thanhTien: thanhTien,
        listSelectedDv: listSelected,
      });
    });
  };

  const onChangeInput = (e) => {
    let objSearch = {
      timKiem: e,
      page: 0,
      size: 10,
      theoLo: state?.theoLo,
    };
    getListDichVuTonKho(objSearch);
    setState({ timKiem: e });
  };

  const onSubmit = async () => {
    if (refIsSubmit.current) return;

    const isDaCoChungChi = await kiemTraChungChi();
    if (!isDaCoChungChi) {
      return;
    }

    const { listSelectedDv } = state;
    if (!listSelectedDv.length) {
      if (!state.notShowModal) {
        message.error("Yêu cầu nhập chỉ định dịch vụ!");
      }
      return;
    }
    let checkSoLuong = listSelectedDv.some(
      (item) => !item.soLuong || item.soLuong === 0
    );
    if (checkSoLuong) {
      message.error(t("khamBenh.donThuoc.truongBatBuocDienSoLuong"));
      return;
    }

    if (state.showChiDinhTuDichVuId) {
      //Yêu cầu bắt buộc chọn chiDinhTuDichVuId
      if (listSelectedDv.some((x) => !x.chiDinhTuDichVuId)) {
        message.error(
          t("khamBenh.chiDinh.vuiLongChonChiDinhTuDichVuId", {
            chiDinhTuDichVuId: state.chiDinhTuDichVuIdOptions?.title || "",
          })
        );
        return;
      }
    }

    const payload = listSelectedDv.map((item) => {
      const _loaiChiDinh = item.dotXuat
        ? LOAI_CHI_DINH.DOT_XUAT
        : item.boSung
        ? LOAI_CHI_DINH.BO_SUNG
        : LOAI_CHI_DINH.THUONG;
      return {
        nbDotDieuTriId: configData?.nbDotDieuTriId,
        sttBo: state.sttBo || item?.sttBo,
        nbDichVu: {
          dichVuId: item?.dichVuId,
          soLuong: item.soLuong,
          chiDinhTuDichVuId: state.showChiDinhTuDichVuId
            ? item.chiDinhTuDichVuId
            : configData?.chiDinhTuDichVuId,
          chiDinhTuLoaiDichVu: configData?.chiDinhTuLoaiDichVu,
          khoaChiDinhId: configData?.khoaChiDinhId,
          loaiDichVu: item?.loaiDichVu,
          tuTra: item?.tuTra,
          khongTinhTien: item?.khongTinhTien,
          nguonKhacId: item?.nguonKhacId,
        },
        nbDvKho: {
          khoId: item.khoId,
          moi: item.moi,
          loaiChiDinh: _loaiChiDinh,
          ...(dataHIEN_THI_CHECKBOX_TU_TRUC?.eval()
            ? {
                tuTruc: state.isTuTruc,
                phongId: configData?.phongThucHienId,
                canLamSang: configData?.canLamSang,
              }
            : {}),
          ...(state.theoLo && {
            loNhapId: item.loNhapId,
          }),
        },
        kichCoVtId: item?.kichCoVtId,
        ...(isVatTuXN ? { vatTuXetNghiem: true } : {}),
      };
    });
    onChangeStateSubmit(true);
    chiDinhDichVu(payload)
      .then((s) => {
        onChangeStateSubmit(false);
        getListDichVuVatTu({
          nbDotDieuTriId: configData?.nbDotDieuTriId,
          chiDinhTuDichVuId: configData?.chiDinhTuDichVuId,
          chiDinhTuLoaiDichVu: configData?.chiDinhTuLoaiDichVu,
          dsTrangThaiHoan: [0, 10, 20],
        }).then((res) => {
          onCancel();
        });

        refCallback.current && refCallback.current();

        let messageResponse = (s || []).filter((item) => item.message);
        if (messageResponse.length) {
          let messageError = messageResponse.filter((x) => x.code !== 0);
          if (messageError.length) {
            message.error(messageError.map((x) => x.message).join(","));
          } else {
            let content = messageResponse[messageResponse.length - 1].message;
            content &&
              showConfirm(
                {
                  title: t("common.canhBao"),
                  content: content,
                  cancelText: t("common.dong"),
                  classNameOkText: "button-error",
                  showImg: true,
                  typeModal: "warning",
                },
                () => {}
              );
          }
        }
      })
      .catch(() => {
        onChangeStateSubmit(false);
      });
  };

  const onSelectKho = (khoId) => {
    getListDichVuTonKho({
      page: 0,
      size: 10,
      khoId,
      theoLo: state?.theoLo,
      tachVatTuTaiSuDung: true,
      //nếu ko chọn kho => truyền dsKhoId để giới hạn những kho theo tài khoản
      ...(!khoId
        ? { dsKhoId: (state?.dataKho || []).map((item) => item.id) }
        : { dsKhoId: undefined }),
    });
    setState({ khoId: khoId });
  };

  const onCancel = () => {
    setState({
      show: false,
      thanhTien: 0,
      listSelectedDv: [],
      sttBo: null,
      timKiem: null,
      theoLo: false,
    });
  };

  const onChangeRadio = (e) => {
    setState({ theoSoLuongTonKho: e.target.value });
    getListDichVuTonKho({
      theoSoLuongTonKho: e.target.value,
      theoLo: state?.theoLo,
    });
    cacheUtils.save(
      "",
      "DATA_THEO_SO_LUONG_TON_KHO_VAT_TU",
      { theoSoLuongTonKho: e.target.value },
      false
    );
  };
  refSubmit.current = onSubmit;

  const popovercontent = (
    <div style={{ display: "grid" }}>
      {t("common.caiDatTimKiemHangHoa")}:
      <Radio.Group
        defaultValue={state?.theoSoLuongTonKho}
        style={{ display: "grid" }}
        onChange={onChangeRadio}
      >
        <Radio value={15}>{t("common.hangHoaConTon")}</Radio>
        <Radio value={""}>{t("common.tatCaHangHoa")}</Radio>
      </Radio.Group>
    </div>
  );

  const ContentChiDinhVatTu = (
    <Main className="content-chi-dinh-vat-tu">
      <Row className="content">
        <Row className="content-title">
          <span className="text">
            {t("vatTu.themChiDinh")}{" "}
            {state.notShowModal ? t("common.vatTu") : ""}
          </span>
          <div>&nbsp;&nbsp;&nbsp;</div>
          <Select
            placeholder={t("vatTu.chonKho")}
            data={state?.dataKho}
            onChange={onSelectKho}
            value={state?.khoId}
          ></Select>
          <div>&nbsp;&nbsp;&nbsp;</div>
          <div className="input-box">
            <img src={imgSearch} alt="imgSearch" />
            <InputTimeout
              style={{ width: 450, height: 32, marginLeft: 0 }}
              className="input-header"
              placeholder={t("vatTu.nhapMaVaTenVatTu")}
              onChange={onChangeInput}
              value={state?.timKiem}
              ref={refInput}
            />
          </div>
          {isVatTuXN && (
            <div>
              <Checkbox
                checked={true}
                style={{ marginLeft: "10px", fontWeight: "400" }}
              >
                {t("quanLyNoiTru.nhapVTTheoXN")}
              </Checkbox>
            </div>
          )}
          <div>
            <Checkbox
              checked={state?.theoLo}
              style={{ marginLeft: "10px", fontWeight: "400" }}
              onChange={(e) => setState({ theoLo: !state?.theoLo })}
            >
              {t("quanLyNoiTru.chonLoXuat")}
            </Checkbox>
          </div>
          <div className="setting">
            <Popover
              trigger={"click"}
              content={popovercontent}
              placement="bottom"
            >
              <SVG.IcSetting />
            </Popover>
          </div>
        </Row>
        <TableVatTu
          thanhTien={thanhTien}
          onSelected={onTamTinhTien}
          loaiDichVu={LOAI_DICH_VU.VAT_TU}
          visible={state.show}
          ref={refTable}
          chiDinhTuLoaiDichVu={configData?.chiDinhTuLoaiDichVu}
          khoId={state?.khoId}
          dsKho={state?.dataKho}
          vatTuBoId={state.vatTuBoId}
          theoSoLuongTonKho={state?.theoSoLuongTonKho}
          layerId={layerId}
          activeHotKey={state.activeHotKey}
          splitCacheCustomize={state.splitCacheCustomize}
          isShowSoLuongDinhMuc={isShowSoLuongDinhMuc}
          dsDinhMucVtytChiTiet={configData?.dsDinhMucVtytChiTiet}
          khoaChiDinhId={configData?.khoaChiDinhId}
          phongThucHienId={configData?.phongThucHienId}
          isVatTuXN={isVatTuXN}
          notShowModal={state.notShowModal}
          theoLo={state?.theoLo}
          showChiDinhTuDichVuId={state.showChiDinhTuDichVuId}
          chiDinhTuDichVuIdOptions={state.chiDinhTuDichVuIdOptions}
        />
      </Row>
    </Main>
  );

  if (state.notShowModal) {
    if (state.show) {
      return ContentChiDinhVatTu;
    } else {
      return null;
    }
  }

  return (
    <ModalTemplate
      width={1366}
      ref={refModal}
      title={t("common.chiDinhVatTu")}
      onCancel={onCancel}
      visible={state.show}
      rightTitle={nbInfoTitle}
      actionLeft={<Button.QuayLai onClick={onCancel} />}
      actionRight={
        <Button type="primary" minWidth={100} onClick={onSubmit}>
          <span> {t("common.dongY")}</span>
        </Button>
      }
      maskClosable={false}
    >
      {ContentChiDinhVatTu}
    </ModalTemplate>
  );
};

export default forwardRef(DichVuVatTu);
