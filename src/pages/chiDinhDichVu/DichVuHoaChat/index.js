import { message, Row, Radio } from "antd";
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { useDispatch } from "react-redux";
import imgSearch from "assets/images/template/icSearch.png";
import { Main } from "./styled";
import { HOTKEY, LOAI_DICH_VU } from "constants/index.js";
import {
  Popover,
  InputTimeout,
  Button,
  ModalTemplate,
  Select,
} from "components";
import TableHoaChat from "./TableHoaChat";
import { useTranslation } from "react-i18next";
import { useStore } from "hooks";
import cacheUtils from "lib-utils/cache-utils";
import useNbInfoTitle from "pages/khamBenh/hooks/useNbInfoTitle";
import stringUtils from "mainam-react-native-string-utils";
import { SVG } from "assets";
import ThemChiDinhSpan from "../components/ThemChiDinhTxtSpan";
import useKiemTraChungChi from "pages/chiDinhDichVu/components/useKiemTraChungChi";
import { isArray } from "utils";
import { uniqBy } from "lodash";

const DichVuHoaChat = (props, ref) => {
  const { t } = useTranslation();
  const nbInfoTitle = useNbInfoTitle();
  const { dataNb, chiDinhTuLoaiDichVu, configData } = props;

  const refModal = useRef(null);
  const refIsSubmit = useRef(null);
  const refTable = useRef();
  const refInput = useRef(null);
  const refLayerHotKey = useRef(stringUtils.guid());
  const refSubmit = useRef(null);
  const refCallback = useRef(null);

  const [kiemTraChungChi] = useKiemTraChungChi();

  const {
    chiDinhHoaChat: {
      tamTinhTien,
      chiDinhDichVu,
      getListDichVuHoaChat,
      getListDichVuTonKho,
    },
    phimTat: { onRegisterHotkey, onAddLayer, onRemoveLayer },
  } = useDispatch();

  const dataSearch = useStore("chiDinhHoaChat.dataSearch");
  const [state, _setState] = useState({
    show: false,
    listSelectedDv: [],
    thanhTien: 0,
  });

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    if (state.show) {
      onAddLayer({ layerId: refLayerHotKey.current });
      onRegisterHotkey({
        layerId: refLayerHotKey.current,
        hotKeys: [
          {
            keyCode: HOTKEY.F2, //F2
            onEvent: (e) => {
              onChangeInput("");
              refInput.current && refInput.current.focus();
            },
          },
          {
            keyCode: HOTKEY.F4, //F4
            onEvent: () => {
              refSubmit.current && refSubmit.current();
            },
          },
        ],
      });
      async function fetchData() {
        let data = await cacheUtils.read(
          "",
          "DATA_THEO_SO_LUONG_TON_KHO_HOA_CHAT",
          null,
          false
        );
        if (data) {
          setState({ theoSoLuongTonKho: data?.theoSoLuongTonKho });
        } else {
          setState({ theoSoLuongTonKho: 15 });
        }
      }
      fetchData();
      return () => {
        onRemoveLayer({ layerId: refLayerHotKey.current });
      };
    }
  }, [state.show]);

  useImperativeHandle(ref, () => ({
    show: (option = {}, callback) => {
      setState({
        show: true,
        filterText: "",
        dataSource: option?.dataSource || {},
        dataKho: option?.dataKho || [],
      });
      onSelectLoaiHoaChat(option?.khoId);
      refIsSubmit.current = false;
      if (refTable.current) {
        refTable.current.onShow({ khoId: dataSearch?.khoId });
      }
      refCallback.current = callback;
    },
  }));

  useEffect(() => {
    if (!state.show) {
      setTimeout(() => refModal.current.hide(), 50);
    } else {
      refModal.current.show({});
      setTimeout(() => {
        refInput.current.focus();
      }, 1000);
    }
  }, [state.show]);

  const { thanhTien } = state;

  const onTamTinhTien = (listSelected) => {
    const payload = listSelected.map((item) => ({
      nbDotDieuTriId: dataNb?.nbDotDieuTriId || dataNb?.id,
      nbDichVu: {
        dichVuId: item?.dichVuId,
        soLuong: item.soLuong,
        dichVu: {
          id: item?.id,
          ma: item?.ma,
          ten: item?.ten,
        },
        khoaChiDinhId: configData?.khoaChiDinhId || dataNb?.khoaChiDinhId,
        chiDinhTuDichVuId: configData?.chiDinhTuDichVuId || dataNb?.id,
        chiDinhTuLoaiDichVu: chiDinhTuLoaiDichVu,
        nguonKhacId: item.nguonKhacId,
      },
      nbDvKho: {
        khoId: dataSearch.khoId,
      },
    }));
    tamTinhTien(payload).then((s) => {
      const thanhTien = (s || []).reduce(
        (accumulator, currentValue) =>
          accumulator + currentValue.nbDichVu.thanhTien,
        0
      );
      const messageWarning = ((s || []).filter((x) => x.message) || []).reduce(
        (value, currentValue) => value.concat(currentValue.message + ", "),
        ""
      );
      if (messageWarning?.length) message.warning(messageWarning);
      setState({
        thanhTien: thanhTien,
        listSelectedDv: listSelected,
      });
    });
  };

  const onChangeInput = (e) => {
    let objSearch = {
      timKiem: e,
      page: 0,
      size: 10,
    };
    getListDichVuTonKho(objSearch);
  };

  const onSubmit = async () => {
    if (refIsSubmit.current) return;

    const isDaCoChungChi = await kiemTraChungChi();
    if (!isDaCoChungChi) {
      return;
    }

    const { listSelectedDv } = state;
    if (!listSelectedDv.length) {
      message.error("Yêu cầu nhập chỉ định dịch vụ!");
      return;
    }
    let checkSoLuong = listSelectedDv.some(
      (item) => !item.soLuong || item.soLuong === 0
    );
    if (checkSoLuong) {
      message.error(t("khamBenh.donThuoc.truongBatBuocDienSoLuong"));
      return;
    }

    const payload = listSelectedDv.map((item) => {
      return {
        nbDotDieuTriId: dataNb?.nbDotDieuTriId || dataNb?.id,
        nbDichVu: {
          dichVuId: item?.dichVuId,
          soLuong: item.soLuong,
          chiDinhTuDichVuId: configData?.chiDinhTuDichVuId || dataNb?.id,
          chiDinhTuLoaiDichVu: chiDinhTuLoaiDichVu,
          khoaChiDinhId: configData?.khoaChiDinhId || dataNb?.khoaChiDinhId,
          loaiDichVu: item?.loaiDichVu,
          tuTra: item?.tuTra,
          khongTinhTien: item?.khongTinhTien,
          nguonKhacId: item?.nguonKhacId,
        },
        nbDvKho: {
          khoId: dataSearch?.khoId,
        },
      };
    });
    refIsSubmit.current = true;

    chiDinhDichVu(payload)
      .then((s) => {
        getListDichVuHoaChat({
          nbDotDieuTriId: dataNb?.nbDotDieuTriId || dataNb?.id,
          chiDinhTuDichVuId: configData?.chiDinhTuDichVuId || dataNb.id,
          chiDinhTuLoaiDichVu: chiDinhTuLoaiDichVu,
          dsTrangThaiHoan: [0, 10, 20],
        }).then((res) => {
          refCallback.current && refCallback.current();
          onCancel();
        });
        let messageError = (s || []).filter(
          (item) => item.message && item.code !== 0
        );
        messageError?.length &&
          message.error(messageError.map((x) => x.message).join(","));
      })
      .catch(() => {
        refIsSubmit.current = false;
      });
  };

  refSubmit.current = onSubmit;

  const onSelectLoaiHoaChat = (khoId) => (value) => {
    setState({
      khoId: khoId,
      listSelectedDv: [],
    });
  };

  const onSelectKho = (khoId) => {
    getListDichVuTonKho({
      page: 0,
      size: 10,
      khoId,
    });
  };

  const onCancel = () => {
    setState({
      show: false,
      thanhTien: 0,
      listSelectedDv: [],
    });
  };

  const onChangeRadio = (e) => {
    setState({ theoSoLuongTonKho: e.target.value });
    cacheUtils.save(
      "",
      "DATA_THEO_SO_LUONG_TON_KHO_HOA_CHAT",
      { theoSoLuongTonKho: e.target.value },
      false
    );
    getListDichVuTonKho({ theoSoLuongTonKho: e.target.value });
  };

  const popovercontent = (
    <div style={{ display: "grid" }}>
      {t("common.caiDatTimKiemHangHoa")}:
      <Radio.Group
        defaultValue={state?.theoSoLuongTonKho}
        style={{ display: "grid" }}
        onChange={onChangeRadio}
      >
        <Radio value={15}>{t("common.hangHoaConTon")}</Radio>
        <Radio value={""}>{t("common.tatCaHangHoa")}</Radio>
      </Radio.Group>
    </div>
  );

  return (
    <ModalTemplate
      width={1366}
      ref={refModal}
      title={t("common.chiDinhHoaChat")}
      onCancel={onCancel}
      rightTitle={nbInfoTitle}
      visible={state.show}
      maskClosable={false}
      actionRight={
        <>
          <Button type={"default"} onClick={onCancel} minWidth={100}>
            {t("common.huy")}
          </Button>
          <Button type="primary" onClick={onSubmit} minWidth={100}>
            {t("common.dongY")}
          </Button>
        </>
      }
    >
      <Main>
        <Row className="content">
          <Row className="content-title">
            <ThemChiDinhSpan />
            <Select
              placeholder={t("quanLyNoiTru.chonKho")}
              data={state?.dataKho}
              onChange={onSelectKho}
              value={dataSearch?.khoId}
            ></Select>
            <div>&nbsp;&nbsp;&nbsp;</div>
            <div className="input-box">
              <img src={imgSearch} alt="imgSearch" />
              <InputTimeout
                style={{ width: 450, height: 32, marginLeft: 0 }}
                className="input-header"
                onChange={onChangeInput}
                value={dataSearch?.timKiem}
                ref={refInput}
              />
            </div>
            <div className="setting">
              <Popover
                trigger={"click"}
                content={popovercontent}
                placement="bottom"
              >
                <SVG.IcSetting />
              </Popover>
            </div>
          </Row>
          <TableHoaChat
            thanhTien={thanhTien}
            onSelected={onTamTinhTien}
            loaiDichVu={LOAI_DICH_VU.HOA_CHAT}
            visible={state.show}
            ref={refTable}
            layerId={refLayerHotKey.current}
            theoSoLuongTonKho={state.theoSoLuongTonKho}
            khoaChiDinhId={configData?.khoaChiDinhId || dataNb?.khoaChiDinhId}
          />
        </Row>
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(DichVuHoaChat);
