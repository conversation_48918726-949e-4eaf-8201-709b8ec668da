import React, {
  useState,
  useEffect,
  useRef,
  forwardRef,
  useImperativeHandle,
  useMemo,
} from "react";
import { Input, message } from "antd";
import { <PERSON>lt<PERSON>, HeaderSearch, TableWrapper, Select } from "components";
import CircleCheck from "assets/images/khamBenh/circle-check.png";
import { GlobalStyle, WrapperInput, WrapperSelect, BoxWrapper } from "./styled";
import { isArray, openInNewTab } from "utils";
import { useSelector, useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { useEnum, useListAll, useQueryAll, useStore, useThietLap } from "hooks";
import {
  DS_TINH_CHAT_KHOA,
  ENUM,
  HOTKEY,
  THIET_LAP_CHUNG,
} from "constants/index";
import { cloneDeep } from "lodash";
import stringUtils from "mainam-react-native-string-utils";
import { query } from "redux-store/stores";

const { Setting } = TableWrapper;

const TableMau = (props, ref) => {
  const { t } = useTranslation();
  const { onSelected, thanhTien, visible, layerId, khoId, khoaLamViec } = props;
  const refInput = useRef(null);
  const refSelectRow = useRef(null);
  const refAddService = useRef(null);
  const refSettings = useRef(null);
  const refSettingsLeft = useRef(null);
  const { dsDvMau } = useSelector((state) => state.chiDinhMau);

  const chiTietNguoiBenhNoiTru = useStore(
    "danhSachNguoiBenhNoiTru.chiTietNguoiBenhNoiTru"
  );

  const { data: listKhoaTheoTaiKhoan } = useQueryAll(
    query.khoa.queryKhoaTheoTaiKhoan
  );

  const [
    dataHIEN_THI_TAT_CA_KHOA_SU_DUNG_MAU,
    isFetchedHIEN_THI_TAT_CA_KHOA_SU_DUNG_MAU,
  ] = useThietLap(THIET_LAP_CHUNG.HIEN_THI_TAT_CA_KHOA_SU_DUNG_MAU);

  const { data: listAllKhoa } = useQueryAll(
    query.khoa.queryAllKhoa({
      params: {
        dsTinhChatKhoa: [
          DS_TINH_CHAT_KHOA.NOI_TRU,
          DS_TINH_CHAT_KHOA.PHAU_THUAT,
        ],
      },
      enabled:
        isFetchedHIEN_THI_TAT_CA_KHOA_SU_DUNG_MAU &&
        dataHIEN_THI_TAT_CA_KHOA_SU_DUNG_MAU?.eval(),
    })
  );

  const listKhoa = useMemo(() => {
    if (!isFetchedHIEN_THI_TAT_CA_KHOA_SU_DUNG_MAU) return [];
    return dataHIEN_THI_TAT_CA_KHOA_SU_DUNG_MAU?.eval()
      ? listAllKhoa
      : listKhoaTheoTaiKhoan;
  }, [
    dataHIEN_THI_TAT_CA_KHOA_SU_DUNG_MAU,
    listAllKhoa,
    listKhoaTheoTaiKhoan,
    isFetchedHIEN_THI_TAT_CA_KHOA_SU_DUNG_MAU,
  ]);

  const [listAllNguonKhacChiTra] = useListAll("nguonKhac", {}, true);
  const [listNhomMau] = useEnum(ENUM.NHOM_MAU);
  const [listMucDo] = useEnum(ENUM.MUC_DO_CHE_PHAM_MAU);
  const [listXetNghiemCmv] = useEnum(ENUM.XET_NGHIEM_CMV);

  const [dataMucDoChePhamMau, isFinish] = useThietLap(
    THIET_LAP_CHUNG.MUC_DO_CHE_PHAM_MAU
  );
  const [dataHienThiNhomMauChuaThucHienXacDinh, isFinish2] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_NHOM_MAU_CHUA_THUC_HIEN_XAC_DINH
  );

  const listMucDoChePhamMau = useMemo(() => {
    if (isFinish && dataMucDoChePhamMau) {
      return listMucDo.filter((x) =>
        dataMucDoChePhamMau
          .split(",")
          .map((s) => Number(s.trim()))
          .includes(x.id)
      );
    }
    return listMucDo;
  }, [isFinish, dataMucDoChePhamMau, listMucDo]);

  const isPhatTheoNhomMauNB = useMemo(() => {
    return (
      isFinish2 &&
      dataHienThiNhomMauChuaThucHienXacDinh?.toLowerCase() === "true" &&
      chiTietNguoiBenhNoiTru?.nhomMau === 9 // Chưa xác định
    );
  }, [
    isFinish2,
    dataHienThiNhomMauChuaThucHienXacDinh,
    chiTietNguoiBenhNoiTru,
  ]);

  const {
    phimTat: { onRegisterHotkey },
  } = useDispatch();

  const [state, _setState] = useState({
    listServiceSelected: [],
    elementKey: 1,
    keySelected: [],
    dataSelected: [],
    loaiDonVatTu: 10,
    khoaSuDung: "",
  });
  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };

  const onShow = () => {
    //khi bật lên thì mặc đinh reset popup
    setState({
      selectedRowKeys: [],
      listServiceSelected: [],
      dataSelected: [],
      keySelected: [],
      keyWord: "",
      boChiDinhSelected: null,
      isCheckAll: false,
      key: null,
      isShow: true,
    });
    setTimeout(() => {
      refInput.current && refInput.current.focus();
    }, 500);
  };

  useImperativeHandle(ref, () => ({
    onShow,
  }));

  useEffect(() => {
    if (khoaLamViec && state.isShow) {
      setState({ khoaSuDung: khoaLamViec.id });
    } else if (isArray(listKhoa, true) && state.isShow) {
      const khoaSuDung = listKhoa.find(
        (i) => i.ma === chiTietNguoiBenhNoiTru?.maKhoaNb
      );
      if (khoaSuDung) setState({ khoaSuDung: khoaSuDung.id });
    }
  }, [listKhoa, state.isShow, chiTietNguoiBenhNoiTru, khoaLamViec]);

  useEffect(() => {
    if (visible) {
      onShow();
    }
  }, [visible]); // khi thay đổi về visible hoặc loại đơn thuốc thì load lại bộ chỉ định, reset lại thông tin filter

  useEffect(() => {
    if (visible) {
      onRegisterHotkey({
        layerId,
        hotKeys: [
          {
            keyCode: HOTKEY.ENTER, //key enter
            onEvent: () => {
              if (refAddService.current) {
                refAddService.current();
              }
            },
          },
          {
            keyCode: HOTKEY.DOWN, // down
            onEvent: () => {
              if (refSelectRow.current) refSelectRow.current(1);
            },
          },
          {
            keyCode: HOTKEY.UP, //up
            onEvent: () => {
              if (refSelectRow.current) refSelectRow.current(-1);
            },
          },
        ],
      });
    }
  }, [visible]);

  refSelectRow.current = (index) => {
    const indexNextItem =
      (dsDvMau?.findIndex((item) => item.key === state?.key) || 0) + index;
    if (-1 < indexNextItem && indexNextItem < dsDvMau.length) {
      setState({ key: dsDvMau[indexNextItem]?.key });
      document
        .getElementsByClassName(
          "table-row-odd " + dsDvMau[indexNextItem]?.key
        )[0]
        .scrollIntoView({ block: "end", behavior: "smooth" });
    }
  };
  const onSelectChangeLeft = async (selectedRowKeys, data) => {
    //reset hết các field số lượng và nhóm máu về null
    let selectedRowKeysService = data.map((item) => ({
      ...item,
      soLuong: null,
      nhomMau: isPhatTheoNhomMauNB ? 26 : chiTietNguoiBenhNoiTru.nhomMau,
      mucDo: listMucDoChePhamMau[0]?.id || null,
      khoaSuDung: state.khoaSuDung,
    }));

    //lấy lại nhóm máu và số lượng từ dataSelected
    selectedRowKeysService.forEach((item1) => {
      state.dataSelected.forEach((item2) => {
        if (
          item1.dichVuId &&
          item2.dichVuId &&
          item1.dichVuId == item2.dichVuId &&
          item1.key === item2.key
        ) {
          item1.soLuong = item2.soLuong;
          item1.nhomMau = item2.nhomMau;
        }
      });
    });

    setState({
      selectedRowKeys: selectedRowKeys,
      listServiceSelected: data,
      keySelected: selectedRowKeys,
      dataSelected: selectedRowKeysService,
    });

    onSelected(selectedRowKeysService);
  };
  const onSelectChangeRight = (selectedRowKeys, data, item) => {
    setState({
      listServiceSelected: [...data].filter((i) => i),
      keySelected: selectedRowKeys,
      dataSelected: [...data].filter((i) => i),
    });
    // onSelectedNoPayment(selectedRowKeysService);
    onSelected(data);
  };

  const onSelectLeft = (record, isSelected) => {
    record = cloneDeep(record);
    record.key = record.key + stringUtils.guid(); //tạo lại key mới, vì thay đổi cho phép chọn nhiều dịch vụ trùng nhau.
    let listServiceSelected = [];
    let selectedRowKeys = [record?.key];
    if (isSelected) {
      listServiceSelected = [...state.dataSelected, record];
      selectedRowKeys = [...state.selectedRowKeys, ...selectedRowKeys];
    } else {
      listServiceSelected = state.dataSelected.filter(
        (item) => record.key !== item.key
      );
      selectedRowKeys = state.selectedRowKeys.filter(
        (item) => !selectedRowKeys.includes(item)
      );
    }
    onSelectChangeLeft(selectedRowKeys, listServiceSelected);
  };

  const rowSelectionLeft = {
    columnTitle: (
      <HeaderSearch
        // title={<Checkbox onChange={onCheckAll} checked={state?.isCheckAll} />}
        title=""
        isTitleCenter={true}
      />
    ),
    columnWidth: 50,
    onSelect: onSelectLeft,
    selectedRowKeys: [],
    preserveSelectedRowKeys: true,
  };
  const rowSelectionRight = {
    columnTitle: <HeaderSearch title={t("common.chon")} />,
    columnWidth: 50,
    onChange: onSelectChangeRight,
    selectedRowKeys: state.keySelected,
    preserveSelectedRowKeys: true,
  };

  const onChangeInput = (type, data) => (e) => {
    let value = "";
    if (e?.target) {
      value = e.target.value;
    } else if (e?._d) value = e._d.format("MM/dd/yyyy");
    else value = e;
    if (type === "dotXuat") {
      value = e.target.checked;
    }
    let dataSelect = state.dataSelected.find((item) => item.key == data.key);
    if (type === "soLuong" && Number(value) <= 0 && value) {
      dataSelect.soLuong = null;
      message.error(t("khamBenh.donThuoc.nhapSoLuongLonHon0"));
    } else {
      dataSelect[type] = value;
    }

    onSelected(state.dataSelected);
  };
  const blockInvalidChar = (e) =>
    ["e", "E", "+", "-"].includes(e.key) && e.preventDefault();

  const columnsTableLeft = [
    {
      title: (
        <HeaderSearch isTitleCenter={true} title={t("common.tenDichVu")} />
      ),
      dataIndex: "ten",
      key: "ten",
      width: 200,
      show: true,
      i18Name: "common.tenDichVu",
    },
    {
      title: (
        <HeaderSearch isTitleCenter={true} title={t("kho.slTonKhaDung")} />
      ),
      dataIndex: "soLuongKhaDung",
      key: "soLuongKhaDung",
      width: 80,
      align: "right",
      show: true,
      i18Name: "kho.slTonKhaDung",
    },
    {
      title: <HeaderSearch isTitleCenter={true} title={t("kho.slTonThucTe")} />,
      dataIndex: "soLuong",
      key: "soLuong",
      width: 80,
      align: "right",
      show: true,
      i18Name: "kho.slTonThucTe",
    },
  ];

  const columnsTableRight = [
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={
            <div
              className="pointer"
              onClick={() => openInNewTab("/danh-muc/suat-an")}
            >
              {t("common.tenDichVu")}
            </div>
          }
        />
      ),
      dataIndex: "ten",
      key: "ten",
      width: 200,
      show: true,
      i18Name: "common.tenDichVu",
      render: (item, data) => {
        return <div clickcheckbox="true">{item}</div>;
      },
    },
    {
      title: <HeaderSearch title={t("common.soLuong")} isTitleCenter={true} />,
      dataIndex: "soLuong",
      key: "soLuong",
      width: 100,
      align: "right",
      show: true,
      i18Name: "common.soLuong",
      render: (item, data, index) => {
        return (
          <WrapperInput className="form-item">
            <Input
              style={{
                width: "100%",
                border: !data.soLuong ? "1px solid red" : "unset",
                marginRight: 5,
              }}
              type="number"
              defaultValue={item}
              onChange={onChangeInput("soLuong", data)}
              onKeyDown={blockInvalidChar}
              autoFocus
            ></Input>
            <Tooltip title={data.tenDonViTinh}>
              <span>{data.tenDonViTinh}</span>
            </Tooltip>
          </WrapperInput>
        );
      },
    },
    {
      title: (
        <HeaderSearch title={t("khoMau.nhomMauPhat")} isTitleCenter={true} />
      ),
      dataIndex: "nhomMau",
      key: "nhomMau",
      width: 100,
      align: "right",
      show: true,
      i18Name: "khoMau.nhomMauPhat",
      render: (item, data, index) => {
        return (
          <WrapperSelect>
            <Select
              data={listNhomMau}
              style={{
                width: "100%",
                border: !data.nhomMau ? "1px solid red" : "unset",
              }}
              onChange={onChangeInput("nhomMau", data)}
              value={item}
            />
          </WrapperSelect>
        );
      },
    },
    {
      title: (
        <HeaderSearch title={t("khoMau.nhomMauNb")} isTitleCenter={true} />
      ),
      dataIndex: "nhomMauNb",
      key: "nhomMauNb",
      width: 100,
      align: "right",
      show: true,
      i18Name: "khoMau.nhomMauNb",
      render: (item, data, index) => {
        return (
          <div style={{ marginTop: "7px", fontWeight: "bold" }}>
            {
              listNhomMau?.find(
                (item) => item.id == chiTietNguoiBenhNoiTru.nhomMau
              )?.ten
            }
          </div>
        );
      },
    },
    {
      title: <HeaderSearch title={t("khoMau.mucDo")} isTitleCenter={true} />,
      dataIndex: "mucDo",
      key: "mucDo",
      width: 150,
      align: "left",
      show: true,
      i18Name: "khoMau.mucDo",
      render: (item, data, index) => {
        return (
          <WrapperSelect>
            <Select
              data={listMucDoChePhamMau}
              style={{
                width: "100%",
              }}
              defaultValue={item}
              onChange={onChangeInput("mucDo", data)}
            />
          </WrapperSelect>
        );
      },
    },
    {
      title: (
        <HeaderSearch title={t("khoMau.khoaSuDung")} isTitleCenter={true} />
      ),
      dataIndex: "khoaSuDung",
      key: "khoaSuDung",
      width: 200,
      align: "left",
      show: true,
      i18Name: "khoMau.khoaSuDung",
      render: (item, data, index) => {
        return (
          <WrapperSelect>
            <Select
              data={listKhoa}
              style={{
                width: "100%",
              }}
              defaultValue={item}
              onChange={onChangeInput("khoaSuDung", data)}
            />
          </WrapperSelect>
        );
      },
    },
    {
      title: <HeaderSearch title={t("common.ghiChu")} isTitleCenter={true} />,
      dataIndex: "ghiChu",
      key: "ghiChu",
      width: 150,
      align: "left",
      show: true,
      i18Name: "common.ghiChu",
      render: (item, data, index) => {
        return (
          <WrapperInput className="form-item">
            <Input
              style={{
                width: "100%",
                border: "2px solid #dfe1e6",
              }}
              defaultValue={item}
              onChange={onChangeInput("ghiChu", data)}
            ></Input>
          </WrapperInput>
        );
      },
    },
    {
      title: (
        <HeaderSearch title={t("danhMuc.nguonKhac")} isTitleCenter={true} />
      ),
      dataIndex: "nguonKhacId",
      key: "nguonKhacId",
      width: 200,
      align: "left",
      show: true,
      i18Name: "danhMuc.nguonKhac",
      render: (item, data, index) => {
        return (
          <WrapperSelect>
            <Select
              data={listAllNguonKhacChiTra}
              style={{
                width: "100%",
              }}
              defaultValue={item}
              onChange={onChangeInput("nguonKhacId", data)}
            />
          </WrapperSelect>
        );
      },
    },
    {
      title: (
        <HeaderSearch title={t("khoMau.dieuCheBoSung")} isTitleCenter={true} />
      ),
      dataIndex: "dsXetNghiemCmv",
      key: "dsXetNghiemCmv",
      width: 200,
      align: "left",
      show: true,
      i18Name: "khoMau.dieuCheBoSung",
      render: (item, data, index) => {
        return (
          <WrapperSelect>
            <Select
              data={listXetNghiemCmv}
              style={{
                width: "100%",
              }}
              defaultValue={item}
              onChange={onChangeInput("dsXetNghiemCmv", data)}
              mode="multiple"
            />
          </WrapperSelect>
        );
      },
    },
    {
      title: <HeaderSearch title={t("common.cachDung")} isTitleCenter={true} />,
      dataIndex: "cachDung",
      key: "cachDung",
      width: 180,
      show: true,
      render: (item, data, index) => {
        return (
          <WrapperInput className="form-item">
            <Input
              style={{
                width: "100%",
                border: "2px solid #dfe1e6",
              }}
              defaultValue={item}
              onChange={onChangeInput("cachDung", data)}
            ></Input>
          </WrapperInput>
        );
      },
      i18Name: "common.cachDung",
    },
  ];

  const renderEmptyTextLeftTable = () => {
    return (
      <div style={{ marginTop: 130 }}>
        <div style={{ color: "#c3c3c3", fontSize: 14 }}>
          {t("common.khongCoDuLieuPhuHop")}
        </div>
      </div>
    );
  };

  const onEnterAddService = () => {
    let data = dsDvMau.find((x) => x.key === state?.key);
    let key = state?.key;
    let isSelected = true;
    let listServiceSelected = state?.dataSelected;
    let selectedRowKeys = state?.selectedRowKeys;
    if (state?.selectedRowKeys.includes(key)) {
      isSelected = false;
    }
    if (isSelected) {
      selectedRowKeys = [...selectedRowKeys, data.key];
      listServiceSelected = [data, ...listServiceSelected];
    } else {
      selectedRowKeys = selectedRowKeys.filter((x) => x !== data.key);
      listServiceSelected = listServiceSelected.filter(
        (item) => data.key !== item.key
      );
    }

    onSelectChangeLeft(selectedRowKeys, listServiceSelected);
  };
  refAddService.current = onEnterAddService;

  return (
    <BoxWrapper>
      <GlobalStyle />
      <div className="content-left">
        <div className="title">
          <div className="title__left">{t("common.dichVu")}</div>
          <Setting refTable={refSettingsLeft} />
        </div>

        <div className="content-left-header-table">
          <TableWrapper
            rowKey={(record) => {
              return record.key;
            }}
            columns={columnsTableLeft}
            dataSource={khoId ? dsDvMau : []}
            rowSelection={rowSelectionLeft}
            rowClassName={(record, index) =>
              state?.key === record.key
                ? "row-actived table-row-odd " + record.key
                : "table-row-odd " + record.key
            }
            onRow={() => {
              return {
                onClick: (row) => {
                  row.currentTarget.firstChild.firstElementChild.firstElementChild.firstElementChild.click();
                },
              };
            }}
            locale={{
              emptyText: renderEmptyTextLeftTable(),
            }}
            ref={refSettingsLeft}
            tableName="table_DVKT_ChiDinhMau"
          />
        </div>
      </div>
      <div className="content-right">
        <div className="title">
          <div className="title__left">
            <img src={CircleCheck} alt="" /> {t("common.daChon")}
          </div>
          <div className="title__right">
            {/* {t("khamBenh.donThuoc.tongTien")}: {(thanhTien || 0).formatPrice()}{" "}
            <span>đ</span> */}
            <span>{t("common.caiDatHienThiBang")}</span>
            <Setting refTable={refSettings} />
          </div>
        </div>
        <div className="content-right_table">
          <TableWrapper
            rowKey={(record) => {
              return record.key;
            }}
            rowSelection={rowSelectionRight}
            className="table-right"
            columns={columnsTableRight}
            dataSource={state.dataSelected}
            ref={refSettings}
            tableName="table_CHIDINHMAU_KeMau"
            rowClassName={(record, index) => {
              return index % 2 === 0
                ? `table-row-even ${
                    index == state.listServiceSelected?.length - 1
                      ? "add-border"
                      : ""
                  }`
                : `table-row-odd ${
                    index == state.listServiceSelected?.length - 1
                      ? "add-border"
                      : ""
                  }`;
            }}
            onRow={() => {
              return {
                onClick: (row) => {
                  if (
                    row?.target?.firstElementChild?.hasAttribute(
                      "clickcheckbox"
                    ) ||
                    row?.target?.hasAttribute("clickcheckbox")
                  ) {
                    row.currentTarget.firstChild.firstElementChild.firstElementChild.firstElementChild.click();
                  }
                },
              };
            }}
            locale={{
              emptyText: (
                <div style={{ height: 297 }}>
                  <div style={{ color: "#c3c3c3", lineHeight: "297px" }}>
                    {t("quanLyNoiTru.suatAn.khongCoDuLieuThuocDaChon")}
                  </div>
                </div>
              ),
            }}
          />
        </div>
      </div>
    </BoxWrapper>
  );
};

export default forwardRef(TableMau);
