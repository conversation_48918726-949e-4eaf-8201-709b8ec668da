import React, {
  forwardRef,
  useRef,
  useState,
  useImperativeHandle,
  useEffect,
} from "react";
import { useTranslation } from "react-i18next";
import { ModalTemplate, Button, Select, InputTimeout } from "components";
import { message, Row } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { Main } from "./styled";
import { HOTKEY, LOAI_DICH_VU, THIET_LAP_CHUNG } from "constants/index.js";
import imgSearch from "assets/images/template/icSearch.png";
import TableMau from "./TableMau";
import { useDelayedState, useLoading, useStore, useThietLap } from "hooks";
import ModalBoSungThongTinDichVu from "pages/khamBenh/ChiDinhDichVu/ModalBoSungThongTinDichVu";
import dmDvPhongProvider from "data-access/categories/dm-dv-phong-provider";
import useNbInfoTitle from "pages/khamBenh/hooks/useNbInfoTitle";
import stringUtils from "mainam-react-native-string-utils";
import dmLoaiDoiTuongLoaiHinhTtProvider from "data-access/categories/dm-loai-doi-tuong-loai-hinh-tt-provider";
import moment from "moment";
import useKiemTraChungChi from "pages/chiDinhDichVu/components/useKiemTraChungChi";
import { SVG } from "assets";
import { isArray } from "lodash";
import { toSafePromise } from "lib-utils";

const DichVuMau = (props, ref) => {
  const { t } = useTranslation();
  const nbInfoTitle = useNbInfoTitle();
  const { showLoading, hideLoading } = useLoading();
  const {
    dataNb,
    chiDinhTuLoaiDichVu,
    chiDinhTuDichVuId,
    refreshList = () => {},
    khoaLamViec,
  } = props;
  const refModal = useRef(null);
  const refOption = useRef({});
  const refTable = useRef();
  const refCallback = useRef(null);
  const refAutoFocus = useRef(null);
  const refModalBoSungThongTinDichVu = useRef(null);
  const refLayerHotKey = useRef(stringUtils.guid());
  const refSubmit = useRef(null);
  const [refIsSubmit, onChangeStateSubmit] = useDelayedState(3000);
  const [kiemTraChungChi] = useKiemTraChungChi();

  const {
    chiDinhMau: { chiDinhDichVu, searchDv },
    phimTat: { onRegisterHotkey, onAddLayer, onRemoveLayer },
  } = useDispatch();

  const thongTinBenhNhan = useStore("nbDotDieuTri.thongTinBenhNhan", {});
  const configData = useStore("chiDinhKhamBenh.configData", {});
  const listThietLapChonKho = useSelector(
    (state) => state.thietLapChonKho.listThietLapChonKho
  );

  const [dataHIEN_THI_DICH_VU_KHOA] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_DICH_VU_KHOA,
    ""
  );

  const [state, _setState] = useState({
    show: false,
    listSelectedDv: [],
    thanhTien: 0,
    inputSearch: "",
  });

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useImperativeHandle(
    ref,
    () => ({
      show: (option = {}, callback) => {
        setState({
          show: true,
          khoId: option?.khoId,
          khoaChiDinhId: option?.khoaChiDinhId,
          filterText: "",
          dataSource: option?.dataSource || {},
          dataKho: option?.dataKho || [],
          inputSearch: "",
        });

        searchDv({
          khoId: option?.khoId,
          dsLoaiDichVu: [LOAI_DICH_VU.CHE_PHAM_MAU],
          ...(!dataHIEN_THI_DICH_VU_KHOA?.eval() && {
            khoaChiDinhId: option?.khoaChiDinhId,
          }),
        });
        refOption.current = {
          khoaChiDinhId: option?.khoaChiDinhId,
        };

        refIsSubmit.current = false;
        if (refTable.current) {
          refTable.current.onShow();
        }
        refCallback.current = callback;
      },
    }),
    [state.show]
  );

  useEffect(() => {
    if (!state.show) {
      setTimeout(() => refModal.current.hide(), 50);
    } else {
      setTimeout(() => {
        refAutoFocus.current.focus();
      }, 1000);

      refModal.current.show({});
      onAddLayer({ layerId: refLayerHotKey.current });
      onRegisterHotkey({
        layerId: refLayerHotKey.current,
        hotKeys: [
          {
            keyCode: HOTKEY.F2, //F2
            onEvent: (e) => {
              onChangeInput("");
              refAutoFocus.current && refAutoFocus.current.focus();
            },
          },
          {
            keyCode: HOTKEY.F4, //F4
            onEvent: () => {
              refSubmit.current && refSubmit.current();
            },
          },
        ],
      });
      return () => {
        onRemoveLayer({ layerId: refLayerHotKey.current });
      };
    }
  }, [state.show]);

  const onSelectedDichVu = (listSelected) => {
    setState({
      listSelectedDv: listSelected,
    });
  };

  const onChangeInput = (e) => {
    let objSearch = {
      ten: e,
      dsLoaiDichVu: [LOAI_DICH_VU.CHE_PHAM_MAU],
      khoId: state.khoId,
    };

    searchDv(objSearch);
    setState({
      inputSearch: e,
    });
  };

  const onSubmit = async () => {
    showLoading();
    try {
      if (refIsSubmit.current) return;
      onChangeStateSubmit(true);

      const isDaCoChungChi = await kiemTraChungChi();
      if (!isDaCoChungChi) {
        return;
      }

      const { listSelectedDv } = state;
      if (!listSelectedDv.length) {
        message.error(t("khamBenh.chiDinh.yeuCauNhapChiDinhDichVu"));
        return;
      }
      let checkSoLuong = listSelectedDv.some(
        (item) => !item.soLuong || item.soLuong === 0
      );
      if (checkSoLuong) {
        message.error(t("khamBenh.donThuoc.truongBatBuocDienSoLuong"));
        return;
      }

      let checkNhomMau = listSelectedDv.some((item) => !item.nhomMau);
      if (checkNhomMau) {
        message.error(t("quanLyNoiTru.truongBatBuocDienNhomMau"));
        return;
      }
      const payload = listSelectedDv.map((item) => {
        return {
          nbDotDieuTriId: configData.thongTinNguoiBenh?.id,
          nhomMau: item.nhomMau,
          mucDo: item.mucDo,
          khoaThucHienId: item.khoaSuDung,
          nbDichVu: {
            dichVuId: item?.dichVuId,
            soLuong: item.soLuong,
            chiDinhTuDichVuId: chiDinhTuDichVuId,
            chiDinhTuLoaiDichVu: chiDinhTuLoaiDichVu,
            khoaChiDinhId: refOption.current.khoaChiDinhId,
            loaiDichVu: item?.loaiDichVu,
            thoiGianThucHien: dataNb?.thoiGianYLenh,
            nguonKhacId: item.nguonKhacId,
            ghiChu: item.ghiChu,
          },
          nbDvKho: {
            khoId: state.khoId,
          },
          dsXetNghiemCmv: item.dsXetNghiemCmv,
          cachDung: item.cachDung,
        };
      });

      showLoading();
      const [e, s] = await toSafePromise(chiDinhDichVu(payload));
      if (s) {
        refCallback.current && refCallback.current();

        onDichVuKemTheo(s.data);
        refreshList();
        onCancel();
        return;
      }
      if (e && isArray(e)) {
        e.forEach((x) => {
          if (x.code != 0) {
            message.error(x?.message || t("kiosk.coLoiXayRaVuiLongThuLai"));
          }
        });
      }
    } finally {
      onChangeStateSubmit(false);
      hideLoading();
    }
  };

  const onDichVuKemTheo = async (data) => {
    let newTable = [];
    data.forEach((x) => {
      x.dsDvKemTheo.forEach((x1) => {
        if (x1.code != 0) {
          message.error(
            `${x1?.nbDichVu?.dichVu?.ten || ""}: ${x1?.message || ""}`
          );

          newTable.push({ ...x1, ngayThucHien: x?.nbDichVu?.thoiGianThucHien });
        }
      });
    });

    if (newTable.length) {
      const dataSource = await Promise.all(
        newTable.map(async (x) => {
          const res = await dmDvPhongProvider.getDanhSachPhong({
            page: 0,
            size: 500,
            dichVuId: x?.nbDichVu?.dichVuId,
          });

          const listLoaiHinh = await dmLoaiDoiTuongLoaiHinhTtProvider.search({
            active: true,
            page: "",
            size: "",
            dsDichVuId: [x?.nbDichVu?.dichVuId],
            loaiDoiTuongId: thongTinBenhNhan.loaiDoiTuongId,
            khoaChiDinhId: refOption.current.khoaChiDinhId,
            ngayThucHien: x?.ngayThucHien
              ? moment(x?.ngayThucHien).format("YYYY-MM-DD")
              : moment().format("YYYY-MM-DD"),
          });

          return {
            ...x,
            nbDichVu: {
              ...x.nbDichVu,
              loaiDichVu: x.nbDichVu.dichVu.loaiDichVu,
            },
            dsLoaiHinhThanhToan: listLoaiHinh.data,
            dsPhongThucHien: (res?.data || []).map((x1) => ({
              phongId: x1.phongId,
              ten: x1.phong.ten,
              ma: x1.phong.ma,
              dichVuId: x1.dichVuId,
            })),
          };
        })
      );

      refModalBoSungThongTinDichVu.current &&
        refModalBoSungThongTinDichVu.current.show({
          dataSource,
          isPhauThuat: false,
        });
    }
  };

  refSubmit.current = onSubmit;

  const onCancel = () => {
    setState({
      show: false,
      thanhTien: 0,
      listSelectedDv: [],
    });
  };

  const onSelectKho = (khoId) => {
    setState({ khoId, inputSearch: "" });
    if (khoId) {
      searchDv({
        khoId,
        dsLoaiDichVu: [LOAI_DICH_VU.CHE_PHAM_MAU],
      });
    }
  };

  return (
    <ModalTemplate
      width={"95%"}
      ref={refModal}
      title={t("pttt.chiDinhMau")}
      onCancel={onCancel}
      visible={state.show}
      rightTitle={nbInfoTitle}
      maskClosable={false}
      actionLeft={<Button.QuayLai onClick={onCancel} />}
      actionRight={
        <Button
          type="primary"
          rightIcon={<SVG.IcSave />}
          onClick={onSubmit}
          minWidth={100}
        >
          {t("common.dongY")}
        </Button>
      }
    >
      <Main>
        <Row className="content">
          <Row className="content-title">
            <Select
              data={listThietLapChonKho}
              style={{ width: "200px" }}
              placeholder={t("khamBenh.donThuoc.vuiLongChonKho")}
              onChange={onSelectKho}
              value={state.khoId}
            />
            <div>&nbsp;&nbsp;&nbsp;</div>
            <div className="input-box">
              <img src={imgSearch} alt="imgSearch" />
              <InputTimeout
                style={{ width: "100%", height: 32, marginLeft: 0 }}
                className="input-header"
                placeholder={t("pttt.chonDichVu")}
                onChange={onChangeInput}
                value={state.inputSearch}
                ref={refAutoFocus}
              />
            </div>
          </Row>

          <Row className="content-table">
            <TableMau
              thanhTien={state.thanhTien}
              onSelected={onSelectedDichVu}
              khoId={state.khoId}
              loaiDichVu={LOAI_DICH_VU.CHE_PHAM_MAU}
              visible={state.show}
              ref={refTable}
              layerId={refLayerHotKey.current}
              khoaLamViec={khoaLamViec}
            />
          </Row>
        </Row>
        <ModalBoSungThongTinDichVu ref={refModalBoSungThongTinDichVu} />
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(DichVuMau);
