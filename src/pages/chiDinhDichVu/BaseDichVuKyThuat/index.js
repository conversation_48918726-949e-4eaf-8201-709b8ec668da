import React, { useEffect, useMemo, useRef, useState } from "react";
import { Select, Checkbox, HotKeyTrigger } from "components";
import { Main } from "./styled";
import { Input, message } from "antd";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import ModalChiDinhDichVu from "pages/khamBenh/ChiDinhDichVu/ModalChiDinhDichVu";
import { useConfirm, useStore } from "hooks";
import DichVuDaChiDinh from "pages/khamBenh/ChiDinhDichVu/DichVuDaChiDinh";
import { LOAI_DICH_VU } from "constants/index";
import moment from "moment";
import { checkRoleOr } from "lib-utils/role-utils";
import ThemChiDinhSpan from "../components/ThemChiDinhTxtSpan";
import cacheUtils from "lib-utils/cache-utils";
import { useSelector } from "react-redux";
import ModalChiDinhPhacDo from "pages/chiDinhDichVu/PhacDoDieuTri/ModalDanhSachPhacDo";
import { SVG } from "assets";

const BaseDichVuKyThuat = ({
  isHiddenTyLett,
  dsDoiTuongSuDung = [30],
  listLoaiChiDinhDV,
  isDisplayLoaiPttt,
  isDisplayIconHoan,
  isReadonly,
  disabledAll,
  isDisplayCapCuu,
  isDisplayDoiDv,
  roles,
  isKePhacDoDieuTri,
  layerId,
  checkChongChiDinh,
  ...props
}) => {
  const refModalChiDinhDichVu = useRef(null);
  const { t } = useTranslation();
  const refChiDinhPhacDo = useRef(null);

  const { showConfirm } = useConfirm();

  const {
    benhPham: { getListAllBenhPham },
    pttt: { onSearchDanhSachPhauThuatThuThuat },
    chiDinhKhamBenh: {
      getDsDichVuChiDinhXN,
      getDsDichVuChiDinhKham,
      getDsDichVuChiDinhCLS,
    },
  } = useDispatch();

  const { nhanVienId } = useSelector((state) => state.auth.auth);
  const configData = useStore("chiDinhKhamBenh.configData", null);

  const { denNgayTheBhyt, tuNgayTheBhyt } = configData?.thongTinNguoiBenh || {};

  const [state, _setState] = useState({
    loaiDichVu: "",
    splitCacheCustomize: {},
  });
  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };

  const onChiDinhDichVu = () => {
    const chongChiDinhObject = checkChongChiDinh?.();
    if (chongChiDinhObject?.chongChiDinh) {
      chongChiDinhObject?.showConfirm();

      return;
    }
    refModalChiDinhDichVu.current &&
      refModalChiDinhDichVu.current.show(
        {
          loaiDichVu: state.loaiDichVu,
          dsDoiTuongSuDung: configData?.isPhauThuat
            ? [20, 30]
            : dsDoiTuongSuDung,
          nbThongTinId: configData?.nbThongTinId,
          nbDotDieuTriId: configData?.nbDotDieuTriId,
          khoaChiDinhId: configData?.khoaChiDinhId,
          chiDinhTuDichVuId: configData?.chiDinhTuDichVuId,
          chiDinhTuLoaiDichVu: configData?.chiDinhTuLoaiDichVu,
          disableChiDinh: false,
          isHiddenTyLett: isHiddenTyLett,
          isPhauThuat: configData?.isPhauThuat,
          listLoaiChiDinhDV: listLoaiChiDinhDV,
          doiTuong: configData?.thongTinNguoiBenh?.doiTuong,
          doiTuongKcb: configData?.thongTinNguoiBenh?.doiTuongKcb,
          loaiDoiTuongId: configData?.thongTinNguoiBenh?.loaiDoiTuongId,
          thoiGianVaoVien: configData?.thongTinNguoiBenh.thoiGianVaoVien,
          ngaySinh: configData?.thongTinNguoiBenh.ngaySinh,
          gioiTinh: configData?.thongTinNguoiBenh?.gioiTinh,
          thoiGianThucHien: configData?.thoiGianThucHien,
          khoaChiDinhMHPTTTId: configData?.khoaChiDinhMHPTTTId,
          isKhoaChiDinhDvTuMHPTTT: configData?.isKhoaChiDinhDvTuMHPTTT,
          dsMaDvKhoaThucHien: configData?.dsMaDvKhoaThucHien,
        },
        () => {
          if (
            configData?.chiDinhTuLoaiDichVu ===
            LOAI_DICH_VU.PHAU_THUAT_THU_THUAT
          )
            onSearchDanhSachPhauThuatThuThuat({
              size: 500,
              dataSearch: {
                nbDotDieuTriId: configData?.nbDotDieuTriId,
                khoaThucHienId: configData?.khoaChiDinhId,
                dsPhongThucHienId: configData?.phongThucHienId,
                dsTrangThai: [],
              },
            });

          if (
            configData?.chiDinhTuLoaiDichVu === LOAI_DICH_VU.TO_DIEU_TRI &&
            denNgayTheBhyt &&
            tuNgayTheBhyt
          ) {
            const tuNgay = moment(tuNgayTheBhyt);
            const denNgay = moment(denNgayTheBhyt);
            if (!moment().isBetween(tuNgay, denNgay)) {
              message.error(
                `Giá trị thẻ hiện tại của NB đã hết hạn. Thời gian sử dụng từ ${tuNgay.format(
                  "DD/MM/YYYY"
                )} đến ${denNgay.format("DD/MM/YYYY")}`
              );
            }
          }
        }
      );
  };
  const onChangeInput = (key) => (e) => {
    let value = "";
    if (e.target) {
      value = e.target.value;
    } else {
      value = e;
    }
    setState({ [key]: value });
  };

  useEffect(() => {
    async function fetchData() {
      const widthCacheDichVu = await cacheUtils.read(
        nhanVienId,
        "DATA_CUSTOMIZE_COLUMN_" + "split_KHAMBENH_KeDichVu_DichVu",
        [],
        false
      );

      const widthCacheDichVuBoChiDinh = await cacheUtils.read(
        nhanVienId,
        "DATA_CUSTOMIZE_COLUMN_" + "split_KHAMBENH_KeDichVu_DichVuBoChiDinh",
        [],
        false
      );

      const splitCacheCustomize = Object.assign(
        {},
        { widthCacheDichVu, widthCacheDichVuBoChiDinh }
      );

      setState({
        splitCacheCustomize,
      });
    }
    fetchData();
  }, []);

  useEffect(() => {
    if (configData) {
      getListAllBenhPham({ active: true, page: "", size: "" });
    }
  }, [configData]);

  const onResizeSplit = async (key, keyCache, value) => {
    const splitCacheCustomize = Object.assign(state.splitCacheCustomize, {
      [key]: value,
    });
    setState({ splitCacheCustomize });
    await cacheUtils.save(nhanVienId, keyCache, value, false);
  };

  const onChiDinhPhacDo = () => {
    refChiDinhPhacDo.current &&
      refChiDinhPhacDo.current.show(
        {
          maBenhId: configData?.maBenhId,
          nbDotDieuTriId: configData?.nbDotDieuTriId,
          chiDinhTuLoaiDichVu: configData?.chiDinhTuLoaiDichVu,
          chiDinhTuDichVuId: configData?.chiDinhTuDichVuId,
          khoaChiDinhId: configData?.khoaChiDinhId,
          loaiDoiTuongId: configData?.thongTinNguoiBenh?.loaiDoiTuongId,
          dsLoaiDichVu: [
            LOAI_DICH_VU.XET_NGHIEM,
            LOAI_DICH_VU.KHAM,
            LOAI_DICH_VU.CDHA,
            LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
          ],
          doiTuongKcb: configData?.doiTuongKcb,
          doiTuong: configData?.thongTinNguoiBenh?.doiTuong,
          ngaySinh: configData?.thongTinNguoiBenh?.ngaySinh,
          thoiGianThucHien: "",
          thoiGianVaoVien: configData?.thongTinNguoiBenh?.thoiGianVaoVien,
        },
        () => {
          getDsDichVuChiDinhXN();
          getDsDichVuChiDinhCLS();
          getDsDichVuChiDinhKham();
        }
      );
  };

  return (
    <Main>
      {!isReadonly && checkRoleOr(roles) && (
        <div className="selection">
          <ThemChiDinhSpan />
          <div>
            <Select
              placeholder={t("khamBenh.donThuoc.vuiLongChonLoaiDonThuoc")}
              data={listLoaiChiDinhDV}
              onChange={onChangeInput("loaiDichVu")}
              value={state?.loaiDichVu}
              disabled={disabledAll}
            />
          </div>
          <div>&nbsp;&nbsp;&nbsp;</div>
          <HotKeyTrigger
            layerIds={[layerId]}
            hotKey="F2"
            triggerEvent={!disabledAll && onChiDinhDichVu}
          >
            <div>
              <div className="input-box">
                <SVG.IcSearch />
                <Input
                  placeholder={`${t("common.timKiem")} [F2]`}
                  onClick={onChiDinhDichVu}
                  disabled={disabledAll}
                />
              </div>
            </div>
          </HotKeyTrigger>
          {isKePhacDoDieuTri && (
            <Checkbox
              checked={false}
              onChange={onChiDinhPhacDo}
              style={{ marginLeft: "10px", fontWeight: "400" }}
            >
              {t("khamBenh.chiDinhTheoPhacDo")}
            </Checkbox>
          )}
        </div>
      )}
      <DichVuDaChiDinh
        isHiddenTyLett={isHiddenTyLett}
        isDisplayLoaiPttt={isDisplayLoaiPttt}
        isDisplayIconHoan={isDisplayIconHoan}
        isDisplayCapCuu={isDisplayCapCuu}
        isDisplayDoiDv={isDisplayDoiDv}
        isReadonly={isReadonly}
        disabledAll={disabledAll}
      />
      <ModalChiDinhDichVu
        ref={refModalChiDinhDichVu}
        onResizeSplit={onResizeSplit}
        splitCacheCustomize={state.splitCacheCustomize}
      />
      <ModalChiDinhPhacDo ref={refChiDinhPhacDo} />
    </Main>
  );
};

export default BaseDichVuKyThuat;
