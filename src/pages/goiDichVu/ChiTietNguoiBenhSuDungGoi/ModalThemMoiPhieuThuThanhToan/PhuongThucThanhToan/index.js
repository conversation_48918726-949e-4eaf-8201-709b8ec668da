import React, {
  forwardRef,
  useState,
  useMemo,
  useEffect,
  useRef,
  useImperativeHandle,
} from "react";
import { useTranslation } from "react-i18next";
import { useSelector, useDispatch } from "react-redux";
import { cloneDeep, sortBy } from "lodash";

import { useEnum, useListAll, useStore, useThietLap } from "hooks";

import {
  ENUM,
  LOAI_DOI_TAC,
  LOAI_PHIEU_THU,
  LOAI_PHUONG_THUC_TT,
  THIET_LAP_CHUNG,
} from "constants/index";
import PhuongThucThanhToanItem from "./components/PhuongThucThanhToanItem";
import { Main } from "./styled";

const PhuongThucThanhToan = forwardRef(
  (
    {
      dsPhuongThucTt,
      onChange,
      isDuyetChiPhi,
      isDuyetPhieuThu,
      tongTien,
      toaNhaId,
      isDonThuoc,
      thongTinPhieuThu,
      ...props
    },
    ref
  ) => {
    const { t } = useTranslation();
    const [listAllToaNha] = useListAll("toaNha", {}, true);
    const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan");

    const [MA_PTTT_DUYET_CHI_PHI] = useThietLap(
      THIET_LAP_CHUNG.MA_PTTT_DUYET_CHI_PHI
    );
    const [MA_DOI_TAC_QRCODE_NHA_THUOC] = useThietLap(
      THIET_LAP_CHUNG.MA_DOI_TAC_QRCODE_NHA_THUOC
    );
    const [dataPTTT_TIEN_MAT_HIEN_THI_GHI_CHU] = useThietLap(
      THIET_LAP_CHUNG.PTTT_TIEN_MAT_HIEN_THI_GHI_CHU
    );
    const [listTrangThaiThanhToan] = useEnum(ENUM.TRANG_THAI_THANH_TOAN);
    const [dataPHIEU_TAM_UNG_NHIEU_PTTT] = useThietLap(
      THIET_LAP_CHUNG.PHIEU_TAM_UNG_NHIEU_PTTT
    );
    const [dataMA_DOI_TAC_QRCODE_KIEM_TRA_HIEN_THI_THEO_LOAI_DICH_VU] =
      useThietLap(
        THIET_LAP_CHUNG.MA_DOI_TAC_QRCODE_KIEM_TRA_HIEN_THI_THEO_LOAI_DICH_VU
      );

    const [state, _setState] = useState({
      dsPhuongThucTt: {},
      batBuocNganHang: false,
      listAllDoiTacThanhToanQR: [],
      tenTaiKhoan: "",
      isPTTTInteracted: false,
    });
    const refTienMat = useRef(null);
    const setState = (data = {}) => {
      _setState((state) => {
        return { ...state, ...data };
      });
    };

    const { listAllPhuongThucThanhToan } = useSelector(
      (state) => state.phuongThucTT
    );
    const listphuongThucTTThanhToan = useStore(
      "phuongThucTT.listphuongThucTTThanhToan",
      []
    );
    const listAllDoiTacThanhToan = useStore(
      "doiTac.listAllDoiTacThanhToan",
      []
    );
    const {
      doiTac: { getListAllDoiTacThanhToan },
      doiTacThanhToan: { getListByDoiTacId },
    } = useDispatch();

    const listAllDoiTacThanhToanMemo = useMemo(() => {
      return (listAllDoiTacThanhToan || []).map((item) => ({
        id: item?.id,
        ten: item?.ten,
      }));
    }, [listAllDoiTacThanhToan]);

    const listPhuongThucThanhToan = useMemo(() => {
      if (!listAllPhuongThucThanhToan) return [];
      if (isDuyetChiPhi) {
        return sortBy(
          listAllPhuongThucThanhToan.filter((item) =>
            MA_PTTT_DUYET_CHI_PHI.split(",").includes(item.ma)
          ),
          ["uuTien"],
          "asc"
        );
      } else if (isDuyetPhieuThu) {
        const listAllPtt = [];
        let allPtt = cloneDeep(
          sortBy(listAllPhuongThucThanhToan || [], ["uuTien"], "asc")
        );
        for (let pttt of allPtt) {
          if (state.dsPhuongThucTt[pttt.id]?.tongTien > 0) {
            listAllPtt.push(pttt);
          }
        }
        allPtt = allPtt.filter((i) => !listAllPtt.some((j) => j.id === i.id));
        return [...listAllPtt, ...allPtt];
      } else {
        return sortBy(listAllPhuongThucThanhToan, ["uuTien"], "asc");
      }
    }, [
      listAllPhuongThucThanhToan,
      isDuyetChiPhi,
      MA_PTTT_DUYET_CHI_PHI,
      state.dsPhuongThucTt,
    ]);

    const isPhieuThuNhaThuoc =
      thongTinPhieuThu?.loaiPhieuThu === LOAI_PHIEU_THU.NHA_THUOC || isDonThuoc;

    useEffect(() => {
      const ptttQrCodeId = (listPhuongThucThanhToan || []).find(
        (item) => item.loaiPhuongThucTt === LOAI_PHUONG_THUC_TT.QR_CODE
      )?.id;
      if (
        !state.isPTTTInteracted &&
        isPhieuThuNhaThuoc &&
        ptttQrCodeId &&
        Object.keys(dsPhuongThucTt || {}).length
      ) {
        const doiTac = listAllDoiTacThanhToan.find(
          (el) => el.ma === MA_DOI_TAC_QRCODE_NHA_THUOC
        );
        if (doiTac && dsPhuongThucTt[ptttQrCodeId]) {
          dsPhuongThucTt[ptttQrCodeId]["nganHangId"] = doiTac.id;
          onGetListTaiKhoan(doiTac.id);
        }
      }

      setState({ dsPhuongThucTt: dsPhuongThucTt || {} });
    }, [
      dsPhuongThucTt,
      MA_DOI_TAC_QRCODE_NHA_THUOC,
      listAllDoiTacThanhToan,
      listAllDoiTacThanhToanMemo,
      state.isPTTTInteracted,
      isPhieuThuNhaThuoc,
    ]);

    useEffect(() => {
      getListAllDoiTacThanhToan({ page: "", size: "", active: true });
    }, []);

    useEffect(() => {
      if (listAllDoiTacThanhToanMemo.length === 1) {
        //pttt qrcode id
        const ptttQrCodeId = (listPhuongThucThanhToan || []).find(
          (item) => item.loaiPhuongThucTt === LOAI_PHUONG_THUC_TT.QR_CODE
        )?.id;

        if (ptttQrCodeId && state.dsPhuongThucTt?.[ptttQrCodeId]) {
          //mặc định ngân hàng nếu chỉ có 1 đối tác
          onChangeValue(
            ptttQrCodeId,
            "nganHangId"
          )(listAllDoiTacThanhToanMemo[0].id);

          onGetListTaiKhoan(listAllDoiTacThanhToanMemo[0].id);
        }
      }
    }, [listAllDoiTacThanhToanMemo]);

    useImperativeHandle(ref, () => ({
      handleFocus: () => {
        refTienMat.current?.focus();
      },
      onGetListTaiKhoan,
      resetData: () => {
        setState({ isPTTTInteracted: false });
      },
    }));

    let ptttTienMatId = useMemo(() => {
      return (listPhuongThucThanhToan || []).find((item) => item.tienMat)?.id;
    }, [listPhuongThucThanhToan]);

    const onGetListTaiKhoan = async (nganHangId) => {
      const resTK = await getListByDoiTacId({ dsDoiTacId: nganHangId });

      if (resTK.length) {
        //Check thông tin thanh toán nếu có tòa nhà = tòa nhà quầy thu ngân chọn → lấy line này (>1 line thỏa mãn thì lấy line đầu tiên)
        const _selectTK = resTK.find((x) => x.toaNhaId == toaNhaId);
        if (_selectTK) {
          setState({ tenTaiKhoan: _selectTK.taiKhoan });
        } else {
          setState({ tenTaiKhoan: resTK[0]?.taiKhoan });
        }
      } else {
        setState({ tenTaiKhoan: "" });
      }
    };

    const onChangeValue = (id, type) => async (value) => {
      const dsPhuongThucTt = state.dsPhuongThucTt || {};
      if (!dsPhuongThucTt[id]) {
        dsPhuongThucTt[id] = { phuongThucTtId: id };
      }

      dsPhuongThucTt[id][type] = value;
      if (
        type === "tongTien" &&
        isPhieuThuNhaThuoc &&
        MA_DOI_TAC_QRCODE_NHA_THUOC
      ) {
        const doiTac = listAllDoiTacThanhToan.find(
          (el) => el.ma === MA_DOI_TAC_QRCODE_NHA_THUOC
        );
        if (doiTac) {
          dsPhuongThucTt[id]["nganHangId"] = doiTac.id;
          onGetListTaiKhoan(doiTac.id);
        }
      }
      if (type === "tongTien" && dataPHIEU_TAM_UNG_NHIEU_PTTT?.eval()) {
        const selectedHtttQrHoaDon = (listphuongThucTTThanhToan || []).find(
          (x) => x.id == id
        );

        const listNganHang =
          selectedHtttQrHoaDon?.loaiDoiTac === LOAI_DOI_TAC.THANH_TOAN
            ? selectedHtttQrHoaDon.dsNhaCungCap
            : listAllDoiTacThanhToanMemo;
        if (listNganHang?.length === 1) {
          dsPhuongThucTt[id]["nganHangId"] = listNganHang[0].id;
        }
        if (!value) {
          dsPhuongThucTt[id]["nganHangId"] = null;
        }
      }
      if (type == "nganHangId" && !!value) {
        onGetListTaiKhoan(value);
      }

      if (
        !isDuyetChiPhi &&
        !isDuyetPhieuThu &&
        id !== ptttTienMatId &&
        tongTien
      ) {
        // Tiền mặt = Số tiền phiếu thu - Tiền tại các phương thức còn lại
        const tongTienPttt = Object.entries(dsPhuongThucTt).reduce(
          (acc, [key, value]) => {
            if (+key !== ptttTienMatId) {
              acc = acc + Number(value?.tongTien || 0);
            }
            return acc;
          },
          0
        );
        const tongTienMat = tongTien - tongTienPttt;
        if (!dsPhuongThucTt[ptttTienMatId]) dsPhuongThucTt[ptttTienMatId] = {};
        dsPhuongThucTt[ptttTienMatId]["tongTien"] =
          tongTienMat > 0 ? tongTienMat : 0;
      }
      setState({
        dsPhuongThucTt: { ...dsPhuongThucTt },
        isPTTTInteracted: true,
      });
      onChange && onChange(dsPhuongThucTt);
    };

    return (
      <Main>
        {(listPhuongThucThanhToan || []).map((item, index) => {
          return (
            <PhuongThucThanhToanItem
              key={item.id}
              item={item}
              index={index}
              state={state}
              listphuongThucTTThanhToan={listphuongThucTTThanhToan}
              listAllDoiTacThanhToanMemo={listAllDoiTacThanhToanMemo}
              listAllToaNha={listAllToaNha}
              toaNhaId={toaNhaId}
              listTrangThaiThanhToan={listTrangThaiThanhToan}
              onChangeValue={onChangeValue}
              onGetListTaiKhoan={onGetListTaiKhoan}
              dataPTTT_TIEN_MAT_HIEN_THI_GHI_CHU={
                dataPTTT_TIEN_MAT_HIEN_THI_GHI_CHU
              }
              dataMA_DOI_TAC_QRCODE_KIEM_TRA_HIEN_THI_THEO_LOAI_DICH_VU={
                dataMA_DOI_TAC_QRCODE_KIEM_TRA_HIEN_THI_THEO_LOAI_DICH_VU
              }
              isDuyetPhieuThu={isDuyetPhieuThu}
              thongTinCoBan={thongTinCoBan}
              isDonThuoc={isDonThuoc}
            />
          );
        })}
      </Main>
    );
  }
);
export default React.memo(PhuongThucThanhToan);
