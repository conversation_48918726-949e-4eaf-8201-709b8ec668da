import React, { useEffect, useMemo } from "react";
import { Row, Col } from "antd";
import { useTranslation } from "react-i18next";

import { InputTimeout, Select } from "components";
import {
  LOAI_DICH_VU,
  LOAI_DOI_TAC,
  LOAI_PHIEU_THU,
  LOAI_PHUONG_THUC_TT,
} from "constants/index";
import { isArray } from "utils/index";

const PhuongThucThanhToanItem = ({
  item,
  index,
  state,
  listphuongThucTTThanhToan,
  listAllDoiTacThanhToanMemo,
  listAllToaNha,
  toaNhaId,
  listTrangThaiThanhToan,
  onChangeValue,
  onGetListTaiKhoan,
  dataPTTT_TIEN_MAT_HIEN_THI_GHI_CHU,
  dataMA_DOI_TAC_QRCODE_KIEM_TRA_HIEN_THI_THEO_LOAI_DICH_VU,
  isDuyetPhieuThu,
  thongTin<PERSON>o<PERSON>an,
  isDonThuoc,
}) => {
  const { t } = useTranslation();

  const selectedHtttQrHoaDon = (listphuongThucTTThanhToan || []).find(
    (x) => x.id === item.id
  );

  const listNganHang =
    selectedHtttQrHoaDon?.loaiDoiTac === LOAI_DOI_TAC.THANH_TOAN
      ? selectedHtttQrHoaDon.dsNhaCungCap
      : listAllDoiTacThanhToanMemo;

  const listNganHangMemo = useMemo(() => {
    let listMaDoiTac = dataMA_DOI_TAC_QRCODE_KIEM_TRA_HIEN_THI_THEO_LOAI_DICH_VU
      ?.split(",")
      .map((x) => x?.trim())
      .filter(Boolean);
    if (
      item.loaiPhuongThucTt === LOAI_PHUONG_THUC_TT.QR_CODE &&
      isArray(listMaDoiTac, true)
    ) {
      const isNguoiBenhNoiTru = [2, 3, 4, 6, 9].includes(
        thongTinCoBan?.doiTuongKcb
      );
      let dsDoiTac = listNganHang.filter((x) => listMaDoiTac.includes(x.ma));

      if (isDonThuoc) {
        dsDoiTac = dsDoiTac.filter((x) =>
          (x.dsLoaiDichVu || []).includes(LOAI_DICH_VU.NHA_THUOC)
        );
      } else if (isNguoiBenhNoiTru) {
        dsDoiTac = dsDoiTac.filter((x) =>
          (x.dsLoaiDichVu || []).includes(LOAI_DICH_VU.NOI_TRU)
        );
      } else {
        dsDoiTac = dsDoiTac.filter((x) =>
          (x.dsLoaiDichVu || []).includes(LOAI_DICH_VU.KHAM)
        );
      }

      return dsDoiTac;
    }
    return listNganHang;
  }, [
    dataMA_DOI_TAC_QRCODE_KIEM_TRA_HIEN_THI_THEO_LOAI_DICH_VU,
    listNganHang,
    item,
    thongTinCoBan,
  ]);

  const showTrangThaiTaiNganHang =
    isDuyetPhieuThu &&
    item.loaiPhuongThucTt === LOAI_PHUONG_THUC_TT.QR_CODE &&
    state.dsPhuongThucTt[item.id]?.trangThaiThanhToan;

  const disabled = !state.dsPhuongThucTt[item.id]?.tongTien;

  const batBuocNganHang =
    !state.dsPhuongThucTt[item.id]?.nganHangId &&
    state.dsPhuongThucTt[item.id]?.tongTien > 0 &&
    item.loaiPhuongThucTt === LOAI_PHUONG_THUC_TT.QR_CODE;

  const hasExactlyOneBank = isArray(listNganHangMemo, 1, 1);
  const hasAtLeastOneBank = isArray(listNganHangMemo, 1);

  useEffect(() => {
    if (item.loaiDoiTac !== LOAI_DOI_TAC.THANH_TOAN) return;

    const current = state.dsPhuongThucTt[item.id];
    if (!current) return;

    const nganHangHienTai = current.nganHangId;

    if (!nganHangHienTai && hasExactlyOneBank) {
      // chỉ có đúng 1 ngân hàng thì auto chọn
      onChangeValue(item.id, "nganHangId")(listNganHangMemo[0].id);
    } else if (
      nganHangHienTai &&
      (!hasAtLeastOneBank ||
        !listNganHangMemo.some((x) => x.id === nganHangHienTai))
    ) {
      // reset nếu ngân hàng hiện tại ko còn hợp lệ
      // 2 case thiết lập đè từ bên ngoài MA_DOI_TAC_QRCODE_NHA_THUOC, MA_DOI_TAC_QRCODE_MAC_DINH
      onChangeValue(item.id, "nganHangId")(null);
    }
  }, [item.id, listNganHangMemo, state.dsPhuongThucTt?.[item.id]]);

  return (
    <Row className="row-box" key={index} align={"top"} gutter={[10, 10]}>
      <Col span={12}>
        <div className="row-label">{item.ten}</div>
        <InputTimeout
          type="number"
          formatPrice={true}
          min={0}
          value={state.dsPhuongThucTt[item.id]?.tongTien}
          placeholder={t("common.nhapSoTien")}
          onChange={onChangeValue(item.id, "tongTien")}
        />
      </Col>

      {item.loaiDoiTac === LOAI_DOI_TAC.THANH_TOAN && (
        <Col span={12}>
          <div className="row-label">
            {t("thuNgan.tenNganHang")}
            {batBuocNganHang && <b style={{ color: "red" }}> *</b>}
          </div>
          <div className="row-group">
            <Select
              onChange={onChangeValue(item.id, "nganHangId")}
              value={state.dsPhuongThucTt[item.id]?.nganHangId}
              placeholder={t("thuNgan.chonNganHang")}
              data={listNganHangMemo}
              showAction={["focus"]}
              disabled={disabled}
            />
            {batBuocNganHang && (
              <div className="error" style={{ height: "15px" }}>
                {t("thuNgan.vuiLongChonTenNganHang")}
              </div>
            )}
          </div>
        </Col>
      )}

      {!item.tienMat && (
        <Col
          span={
            showTrangThaiTaiNganHang ||
            item.loaiPhuongThucTt === LOAI_PHUONG_THUC_TT.QR_CODE
              ? 12
              : 24
          }
        >
          <div className="row-label">
            {t("goiDichVu.maChuanChi/maGiaoDich")}
          </div>
          <InputTimeout
            value={state.dsPhuongThucTt[item.id]?.maChuanChi}
            placeholder={t("goiDichVu.nhapMaChuanChi")}
            onChange={onChangeValue(item.id, "maChuanChi")}
            disabled={disabled}
            isTextArea
            autoSize
          />
        </Col>
      )}

      {item.tienMat && dataPTTT_TIEN_MAT_HIEN_THI_GHI_CHU?.eval() && (
        <Col span={24}>
          <div className="row-label">{t("common.ghiChu")}</div>
          <InputTimeout
            value={state.dsPhuongThucTt[item.id]?.maChuanChi}
            placeholder={t("common.nhapGhiChu")}
            onChange={onChangeValue(item.id, "maChuanChi")}
            isTextArea
            autoSize
          />
        </Col>
      )}

      {item.loaiPhuongThucTt === LOAI_PHUONG_THUC_TT.QR_CODE && (
        <>
          <Col span={12}>
            <div className="row-label">{t("danhMuc.toaNha")}</div>
            <InputTimeout
              value={listAllToaNha.find((x) => x.id === toaNhaId)?.ten || ""}
              disabled={true}
              isTextArea
              autoSize
            />
          </Col>

          <Col span={12}>
            <div className="row-label">{t("common.taiKhoan")}</div>
            <InputTimeout
              value={
                state.dsPhuongThucTt[item.id]?.nganHangId
                  ? state.tenTaiKhoan || ""
                  : ""
              }
              disabled={true}
              isTextArea
              autoSize
            />
          </Col>
        </>
      )}

      {showTrangThaiTaiNganHang && (
        <Col span={12}>
          <div className="row-label">{t("thuNgan.trangThaiTaiNganHang")}</div>
          <Select
            disabled={true}
            value={state.dsPhuongThucTt[item.id]?.trangThaiThanhToan}
            placeholder={t("thuNgan.trangThaiTaiNganHang")}
            className={"select-disabled"}
            data={listTrangThaiThanhToan}
          />
        </Col>
      )}
    </Row>
  );
};

export default React.memo(PhuongThucThanhToanItem);
