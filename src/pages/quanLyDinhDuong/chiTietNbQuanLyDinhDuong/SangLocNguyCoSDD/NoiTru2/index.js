import React, {
  useEffect,
  useRef,
  forwardRef,
  useImperativeHandle,
  useState,
  useMemo,
} from "react";
import {
  TableWrapper,
  Pagination,
  Select,
  InputTimeout,
  Tooltip,
  DatePicker,
  Checkbox,
  Dropdown,
  ModalSignPrint,
} from "components";
import { useTranslation } from "react-i18next";
import { SVG } from "assets";
import {
  ENUM,
  LOAI_BIEU_MAU,
  LOAI_SANG_LOC_NGUY_CO_SDD,
} from "constants/index";
import {
  useConfirm,
  useEnum,
  useStore,
  useLoading,
  useQueryAll,
  useLazyKVMap,
} from "hooks";
import moment from "moment";
import { useDispatch, useSelector } from "react-redux";
import { Menu, Empty } from "antd";
import { getPhieuVaSoPhieu, getThongTinMh } from "../../utils";
import { checkIsPhieuKySo } from "utils/phieu-utils";
import { isArray } from "utils";
import { CHAN_DOAN_SDD } from "../../components/PhieuSLNBNoiTru/config";
import { useHistory } from "react-router-dom";
import { query } from "redux-store/stores";
import { Main } from "./styled";
import { GIAM_CAN_2 } from "../../components/PhieuSLNBNoiTru2/config";
const { Column, Setting } = TableWrapper;
const KEY_OBJ = { key: "NoiTru2" };

const NoiTru2 = ({ nbDotDieuTriId }, ref) => {
  const { t } = useTranslation();
  const history = useHistory();
  const { showLoading, hideLoading } = useLoading();
  const { showConfirm } = useConfirm();
  const refSettings = useRef(null);
  const refModalSignPrint = useRef(null);

  const [state, _setState] = useState({
    isSearching: false,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const [getGiamCan2] = useLazyKVMap(GIAM_CAN_2);
  const { data: listAllNhanVien } = useQueryAll(
    query.nhanVien.queryAllNhanVien
  );

  const listNbSangLocSuyDd = useStore(
    "sangLocSuyDd.listNbSangLocSuyDdNoiTru2",
    []
  );
  const thongTinBenhNhanTongHop = useStore(
    "nbDotDieuTri.thongTinBenhNhanTongHop"
  );

  const {
    totalElementsNoiTru2: totalElements,
    pageNoiTru2: page,
    sizeNoiTru2: size,
  } = useSelector((state) => state.sangLocSuyDd);

  const {
    sangLocSuyDd: {
      onSizeChange: onSizeChangeNoiTru,
      onSearch: onSearchNoiTru,
      onChangeInputSearch: onChangeInputSearchNoiTru,
      deleteSangLoc,
      inPhieuSangLocDd,
    },
    phieuIn: { getListPhieu, showFileEditor },
    nbDotDieuTri: { getByTongHopId },
  } = useDispatch();

  const onSizeChange = (payload = {}) =>
    onSizeChangeNoiTru({ ...KEY_OBJ, ...payload });
  const onSearch = (payload = {}) => onSearchNoiTru({ ...KEY_OBJ, ...payload });

  const onChangeInputSearch = (payload = {}) =>
    onChangeInputSearchNoiTru({ ...KEY_OBJ, ...payload });

  useEffect(() => {
    onChangeInputSearch({
      dsLoaiSangLocSuyDd: LOAI_SANG_LOC_NGUY_CO_SDD.NOI_TRU_2,
      nbDotDieuTriId,
      isGetPhieuMoi: true,
    });
  }, [nbDotDieuTriId]);

  const onGetListPhieu = () => {
    const manHinhParams = getThongTinMh();

    getListPhieu({
      maManHinh: manHinhParams.maManHinh,
      maViTri: manHinhParams.maViTri,
      nbDotDieuTriId: nbDotDieuTriId,
    }).then((res) => {
      setState({
        listPhieu: res || [],
        manHinhParams,
      });
    });
  };

  useEffect(() => {
    if (nbDotDieuTriId) {
      onGetListPhieu();

      getByTongHopId(nbDotDieuTriId);
    }
  }, [nbDotDieuTriId]);

  const listData = useMemo(() => {
    let result = [];
    if (isArray(listNbSangLocSuyDd, true) && thongTinBenhNhanTongHop) {
      result = listNbSangLocSuyDd.map((item) => ({
        ...item,
        cdChinh: thongTinBenhNhanTongHop?.cdChinh,
      }));
    }
    return result;
  }, [listNbSangLocSuyDd, thongTinBenhNhanTongHop]);

  const refreshData = () => {
    onChangeInputSearch({});
  };

  const onThemMoi = () => {
    history.push({
      pathname: `/quan-ly-dinh-duong/phieu-sang-loc/${LOAI_SANG_LOC_NGUY_CO_SDD.NOI_TRU_2}/${nbDotDieuTriId}/them-moi`,
      search: `?ref=${window.location.pathname}`,
    });
  };

  useImperativeHandle(ref, () => ({
    refreshData,
    onThemMoi,
  }));

  const onChange = (key) => (e) => {
    let value = e;

    if (key === "thoiGianThucHien") {
      let tuThoiGianThucHien = e ? e.format("YYYY-MM-DD 00:00:00") : null;
      let denThoiGianThucHien = e ? e.format("YYYY-MM-DD 23:59:59") : null;

      onChangeInputSearch({
        tuThoiGianThucHien,
        denThoiGianThucHien,
      });
    } else {
      onChangeInputSearch({
        [key]: value,
      });
    }

    setState({ isSearching: true });
  };

  const onDelete = (record) => () => {
    showConfirm(
      {
        title: t("common.xoaDuLieu"),
        content:
          t("common.banCoChacMuonXoa") +
          t("quanLyDinhDuong.phieuDanhGiaDinhDuong") +
          "?",
        cancelText: t("common.quayLai"),
        okText: t("common.dongY"),
        classNameOkText: "button-error",
        showBtnOk: true,
        typeModal: "error",
      },
      () => {
        deleteSangLoc(record.id).then(() => {
          refreshData();
        });
      }
    );
  };

  const refreshPhieu = (id) => async () => {
    const manHinhParams = getThongTinMh();

    const res = await getListPhieu({
      nbDotDieuTriId: nbDotDieuTriId,
      maManHinh: manHinhParams.maManHinh,
      maViTri: manHinhParams.maViTri,
    });

    const _dsPhieu = getPhieuVaSoPhieu({
      listMaPhieu: ["P817"],
      soPhieu: id,
      listPhieu: res,
    });

    return _dsPhieu[0];
  };

  const onPrintPhieu = (record, item) => async () => {
    if (item.loaiBieuMau == LOAI_BIEU_MAU.BIEU_MAU_CHINH_SUA) {
      let mhParams = {};
      //kiểm tra phiếu ký số
      if (checkIsPhieuKySo(item)) {
        //nếu là ký số thì lấy các thông tin về lịch sử ký + tên chân ký + báo cáo id
        mhParams = {
          maManHinh: state.manHinhParams.maManHinh,
          maViTri: state.manHinhParams.maViTri,
          kySo: true,
          maPhieuKy: item.ma,
          nbDotDieuTriId: nbDotDieuTriId,
        };
      }

      showFileEditor({
        phieu: item,
        nbDotDieuTriId: nbDotDieuTriId,
        ma: item.ma,
        mhParams,
        id: record.id,
      });
    } else {
      showLoading();
      try {
        if (checkIsPhieuKySo(item)) {
          refModalSignPrint.current &&
            refModalSignPrint.current.showToSign(
              {
                phieuKy: item,
                payload: {
                  maManHinh: state.manHinhParams.maManHinh,
                  maViTri: state.manHinhParams.maViTri,
                  nbDotDieuTriId: nbDotDieuTriId,
                },
              },
              refreshPhieu(record.id)
            );
        } else {
          await inPhieuSangLocDd(record.id);
        }
      } catch (err) {
        console.log("err", err);
      } finally {
        hideLoading();
      }
    }
  };

  const contentPrint = (record) => {
    let phieuNoiTru = getPhieuVaSoPhieu({
      listMaPhieu: ["P817"],
      soPhieu: record?.id,
      listPhieu: state.listPhieu,
    });

    const items =
      phieuNoiTru.length > 0
        ? phieuNoiTru
        : [
            {
              ten: t(
                "quanLyDinhDuong.nbNoiTru.phieuSangLocNguyCoDinhDuongNguoiBenhNoiTru"
              ),
              id: -1,
            },
          ];

    return (
      <Menu
        items={items.map((item, index) => ({
          key: index,
          label: (
            <a href={() => false} onClick={onPrintPhieu(record, item)}>
              {item.ten || item.tenBaoCao}
            </a>
          ),
        }))}
      />
    );
  };

  const columns = [
    Column({
      title: t("common.stt"),
      dataIndex: "index",
      key: "index",
      width: 50,
      ignore: true,
      align: "center",
    }),
    Column({
      title: t("quanLyDinhDuong.ngayThucHien"),
      dataIndex: "thoiGianThucHien",
      key: "thoiGianThucHien",
      i18Name: "quanLyDinhDuong.ngayThucHien",
      width: 160,
      selectSearch: true,
      renderSearch: (
        <DatePicker
          format="DD/MM/YYYY"
          placeholder={t("common.timKiem")}
          onChange={onChange("thoiGianThucHien")}
        />
      ),
      render: (item) =>
        item ? moment(item).format("DD/MM/YYYY HH:mm:ss") : "",
    }),
    Column({
      title: t("khamBenh.chiDinh.nguoiThucHien"),
      dataIndex: "tenNguoiThucHien",
      key: "tenNguoiThucHien",
      i18Name: "khamBenh.chiDinh.nguoiThucHien",
      width: 160,
      selectSearch: true,
      renderSearch: (
        <Select
          placeholder={t("common.timKiem")}
          data={listAllNhanVien}
          onChange={onChange("dsNguoiThucHienId")}
        />
      ),
    }),
    Column({
      title: t("quanLyDinhDuong.nbNoiTru.soPhieuSangLoc"),
      dataIndex: "soPhieu",
      key: "soPhieu",
      i18Name: "quanLyDinhDuong.nbNoiTru.soPhieuSangLoc",
      width: 120,
      search: true,
      align: "center",
      renderSearch: (
        <InputTimeout
          placeholder={t("common.timKiem")}
          onChange={onChange("soPhieu")}
        />
      ),
    }),
    Column({
      title: t("quanLyDinhDuong.tonTaiPhieuDanhGiaSGA"),
      dataIndex: "tonTaiSga",
      key: "tonTaiSga",
      i18Name: "quanLyDinhDuong.tonTaiPhieuDanhGiaSGA",
      width: 120,
      align: "center",
      render: (item) => <Checkbox checked={item} />,
    }),
    Column({
      title: t("quanLyDinhDuong.sangLoc.lanSangLoc"),
      dataIndex: "stt",
      key: "stt",
      i18Name: "quanLyDinhDuong.sangLoc.lanSangLoc",
      width: 100,
      align: "center",
    }),
    Column({
      title: t("khamBenh.chanDoan.chieuCao"),
      dataIndex: "chieuCao",
      key: "chieuCao",
      i18Name: "khamBenh.chanDoan.chieuCao",
      width: 80,
      align: "center",
    }),
    Column({
      title: t("common.canNang"),
      dataIndex: "canNang",
      key: "canNang",
      i18Name: "common.canNang",
      width: 80,
      align: "center",
    }),
    Column({
      title: t("sinhHieu.bmi"),
      dataIndex: "bmi",
      key: "bmi",
      i18Name: "sinhHieu.bmi",
      width: 80,
      align: "center",
    }),
    Column({
      title: t("common.chanDoan"),
      dataIndex: "cdChinh",
      key: "cdChinh",
      i18Name: "common.chanDoan",
      width: 160,
    }),
    Column({
      title: t("quanLyDinhDuong.sangLoc.sutCanTrongVong3Thang"),
      dataIndex: "giamCan2",
      key: "giamCan2",
      i18Name: "quanLyDinhDuong.sangLoc.sutCanTrongVong3Thang",
      width: 160,
      align: "center",
      render: (item) => (getGiamCan2(item) ? t(getGiamCan2(item).i18n) : ""),
    }),
    Column({
      title: t("quanLyDinhDuong.nbNoiTru.soKgSut"),
      dataIndex: "giamCan3",
      key: "giamCan3",
      i18Name: "quanLyDinhDuong.nbNoiTru.soKgSut",
      width: 80,
      align: "center",
    }),
    Column({
      title: t("quanLyDinhDuong.nbNoiTru.tyLePhanTramMatCan"),
      dataIndex: "tiLeGiamCan",
      key: "tiLeGiamCan",
      i18Name: "quanLyDinhDuong.nbNoiTru.tyLePhanTramMatCan",
      width: 100,
      align: "center",
    }),
    Column({
      title: t("quanLyDinhDuong.sangLoc.tongDiemSangLocCuoiCung"),
      dataIndex: "tongDiem",
      key: "tongDiem",
      i18Name: "quanLyDinhDuong.sangLoc.tongDiemSangLocCuoiCung",
      width: 180,
      align: "center",
      search: true,
      renderSearch: (
        <InputTimeout
          placeholder={t("common.timKiem")}
          onChange={onChange("tongDiem")}
        />
      ),
    }),
    Column({
      title: t("quanLyDinhDuong.sangLoc.ketLuanCuoiCung"),
      dataIndex: "ketLuan2",
      key: "ketLuan2",
      i18Name: "quanLyDinhDuong.sangLoc.ketLuanCuoiCung",
      width: 120,
      align: "center",
      selectSearch: true,
      renderSearch: (
        <Select
          placeholder={t("common.timKiem")}
          data={CHAN_DOAN_SDD}
          onChange={onChange("dsKetLuan2")}
        />
      ),
      render: (item) => CHAN_DOAN_SDD.find((x) => x.id === item)?.ten || "",
    }),

    Column({
      title: (
        <>
          {t("common.thaoTac")}
          <Setting refTable={refSettings} />
        </>
      ),
      width: 120,
      align: "center",
      ignore: true,
      fixed: "right",
      render: (item, data) => {
        return (
          <>
            <Tooltip title={t("common.xemChiTiet")}>
              <SVG.IcEye
                className="ic-action"
                onClick={() => {
                  const { id, nbDotDieuTriId, loai } = data || {};
                  history.push({
                    pathname: `/quan-ly-dinh-duong/phieu-sang-loc/${loai}/${nbDotDieuTriId}/${id}`,
                    search: `?ref=${window.location.pathname}`,
                  });
                }}
              />
            </Tooltip>
            {!data?.tonTaiSga && (
              <Tooltip title={t("common.xoa")}>
                <SVG.IcDelete className="ic-action" onClick={onDelete(data)} />
              </Tooltip>
            )}
            <Tooltip title={t("common.inGiayTo")}>
              <Dropdown
                placement="bottomRight"
                overlay={contentPrint(item)}
                trigger={["click"]}
              >
                <SVG.IcPrint className="ic-action" onClick={onGetListPhieu} />
              </Dropdown>
            </Tooltip>
          </>
        );
      },
    }),
  ];

  const onChangePage = (page) => {
    onSearch({ page: page - 1 });
  };

  const handleSizeChange = (size) => {
    onSizeChange({ size });
  };

  return (
    <Main>
      {listData.length > 0 || state.isSearching ? (
        <>
          <TableWrapper
            bordered
            columns={columns}
            dataSource={listData}
            rowKey={(record) => `${record.id}`}
            tableName="table_QLDD_SangLocNoiTru"
            ref={refSettings}
          />

          {!!totalElements && (
            <Pagination
              onChange={onChangePage}
              current={page + 1}
              pageSize={size}
              listData={listData}
              total={totalElements}
              onShowSizeChange={handleSizeChange}
            />
          )}
        </>
      ) : (
        <Empty
          image={<img src={require("assets/images/kho/empty.png")} alt="..." />}
          description={t("quanLyDinhDuong.sangLoc.chuaTaoPhieuSangLoc")}
        />
      )}

      <ModalSignPrint ref={refModalSignPrint} />
    </Main>
  );
};

export default forwardRef(NoiTru2);
