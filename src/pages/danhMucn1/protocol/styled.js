import styled from "styled-components";

export const Main = styled.div`
  height: 100%;
  overflow: auto;
  & .page-body {
    padding: 0 !important;
  }
  & .ant-table-header {
    & .ant-table-cell {
      & .custome-header {
        height: 100% !important;
        min-height: auto !important;
      }
    }
  }

  .table-tab {
    max-width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .ant-tabs-content-holder {
      width: 100%;
      height: 100%;
      overflow: hidden;
      flex: 1;

      .ant-tabs-content {
        display: flex;

        .ant-tabs-tabpane {
          width: 100%;
        }
      }
    }
  }

  .multi-level-tab {
    height: 100%;
    background-color: #fff;
  }

  .tab-dm-protocol {
    background: #fff !important;
    .ant-tabs-nav {
      background: #fff;
      box-shadow: none;
      .ant-tabs-nav-list {
        height: 50px !important;
        margin-left: 0 !important;
        padding-top: 0 !important;
        .ant-tabs-tab-active {
          border: 1px solid #0762f7 !important;
          .ant-tabs-tab-btn {
            color: #0762f7 !important;
          }
        }
      }
    }
    .ant-tabs-content-holder {
      border-top: 1px solid #e7e9ed;
      .ant-tabs-content {
        display: block !important;
        border: none !important;
        .ant-tabs-tabpane > div {
          border: none;
          box-shadow: none;
          border-radius: 0;
          .header-create {
            padding-left: 0;
            min-height: 40px;
            .create-title {
              font-weight: 500;
            }
          }
          .create-body {
            border-top: 0;
            padding-top: 0;
            max-height: calc(100vh - 300px);
          }
          .edit-wrapper {
            .action-header {
              .title {
                font-size: 20px;
              }
            }
            .children {
              .hidden-arrow {
                &::-webkit-outer-spin-button,
                &::-webkit-inner-spin-button {
                  -webkit-appearance: none;
                  margin: 0;
                }
              }
            }
          }
        }
      }
    }
  }
`;
