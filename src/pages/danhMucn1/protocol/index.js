import React, { useEffect, useState, useRef } from "react";
import { Col, Tabs } from "antd";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import BaseDm2 from "pages/danhMuc/BaseDm2";
import { useGuid, useLoading, useStore } from "hooks";
import { combineSort } from "utils";
import MultiLevelTab from "components/MultiLevelTab";
import { checkRole } from "lib-utils/role-utils";
import {
  ADD_LAYOUT_COLLAPSE,
  TABLE_LAYOUT_COLLAPSE,
  ADD_LAYOUT,
  TABLE_LAYOUT,
  ROLES,
  LOAI_DICH_VU,
} from "constants/index";
import {
  ProtocolTenTruong,
  FormProtocolTenTruong,
  Protocol,
  FormProtocol,
  ThietLapProtocol,
} from "./components";
import { Main } from "./styled";

const { TabPane } = Tabs;
const defaultParams = { active: "", saveCache: false, isForceCall: true };

const DmProtocol = () => {
  const { t } = useTranslation();
  const { showLoading, hideLoading } = useLoading();
  const layerId1 = useGuid();
  const layerId2 = useGuid();
  const { onAddLayer } = useDispatch().phimTat;

  const [editStatus, setEditStatus] = useState(false);
  const [activeTab, setActiveTab] = useState("0");
  const [editProtocolTenTruongId, setEditProtocolTenTruongId] = useState(null);
  const [editProtocolId, setEditProtocolId] = useState(null);

  const [collapseStatus, setCollapseStatus] = useState(false);

  const refFormProtocolTenTruong = useRef(null);
  const refFormProtocol = useRef(null);
  const refTab = useRef(null);

  const [state, _setState] = useState({
    showFullTable: false,
    activeKeyTab: "1",
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const {
    protocolTenTruong: {
      getListAllProtocolTenTruong,
      createOrEditProtocolTenTruong,
      searchProtocolTenTruong,
      updateData: updateDataProtocolTenTruong,
    },
    protocol: {
      getListAllProtocol,
      createOrEditProtocol,
      searchProtocol,
      updateData: updateDataProtocol,
    },
    dichVuKyThuat: { getAll: getAllDichVuPTTT },
  } = useDispatch();

  // ProtocolTenTruong
  const listProtocolTenTruong = useStore(
    "protocolTenTruong.listProtocolTenTruong",
    []
  );
  const listAllProtocolTenTruong = useStore(
    "protocolTenTruong.listAllProtocolTenTruong",
    []
  );
  const pageProtocolTenTruong = useStore(
    "protocolTenTruong.pageProtocolTenTruong",
    0
  );
  const sizeProtocolTenTruong = useStore(
    "protocolTenTruong.sizeProtocolTenTruong",
    10
  );
  const totalProtocolTenTruong = useStore(
    "protocolTenTruong.totalProtocolTenTruong",
    0
  );
  const dataSortProtocolTenTruong = useStore(
    "protocolTenTruong.dataSortProtocolTenTruong",
    {}
  );
  const dataSearchProtocolTenTruong = useStore(
    "protocolTenTruong.dataSearchProtocolTenTruong",
    {}
  );

  // Protocol
  const listProtocol = useStore("protocol.listProtocol", []);
  const pageProtocol = useStore("protocol.pageProtocol", 0);
  const sizeProtocol = useStore("protocol.sizeProtocol", 10);
  const totalProtocol = useStore("protocol.totalProtocol", 0);
  const dataSortProtocol = useStore("protocol.dataSortProtocol", {});
  const dataSearchProtocol = useStore("protocol.dataSearchProtocol", {});

  const listAllDVPTTT = useStore("dichVuKyThuat.listAllDVPTTT", []);

  useEffect(() => {
    if (activeTab === "0") {
      getListAllProtocolTenTruong({
        ...defaultParams,
        page: "",
        size: "",
      });
    } else if (activeTab === "1") {
      updateDataProtocol({
        dataSearchProtocol: {},
      });
    }
  }, [activeTab]);

  useEffect(() => {
    if (!listAllDVPTTT?.length) {
      getAllDichVuPTTT({
        "dichVu.loaiDichVu": LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
      });
    }
  }, [listAllDVPTTT]);

  const callback = (key) => {
    setActiveTab(`${parseInt(key, 10)}`);

    if (key === "0") onAddLayer({ layerId: layerId1 });
    else if (key === "1") onAddLayer({ layerId: layerId2 });
  };

  const handleChangeshowTable = () => {
    setState({
      changeShowFullTbale: true,
      showFullTable: !state.showFullTable,
    });
    setTimeout(() => {
      setState({
        changeShowFullTbale: false,
      });
    }, 1000);
  };

  const handleCollapsePane = () => {
    setCollapseStatus(!collapseStatus);
    setState({
      changeCollapseStatus: true,
      collapseStatus: !state.collapseStatus,
    });
    setTimeout(() => {
      setState({
        changeCollapseStatus: false,
      });
    }, 500);
  };

  const onEditProtocolTenTruong = (info) => {
    updateDataProtocolTenTruong({
      dataEditProtocolTenTruongDefault: info,
    });
    setEditProtocolTenTruongId(info.id);

    refFormProtocolTenTruong.current.setfields({
      info,
      editProtocolTenTruongId: info.id,
    });
  };

  const onEditProtocol = (info) => {
    const normalizedInfo = {
      ...info,
      dsDichVuId: Array.isArray(info.dsDichVuId) ? info.dsDichVuId : [],
    };

    updateDataProtocol({
      dataEditProtocolDefault: normalizedInfo,
    });
    setEditProtocolId(info.id);

    if (refFormProtocol.current) {
      refFormProtocol.current.setfields({
        info: normalizedInfo,
        editProtocolId: info.id,
      });
    }
  };

  const onResetProtocolTenTruongForm = () => {
    setEditProtocolTenTruongId(null);
    setEditStatus(false);
    if (refFormProtocolTenTruong.current) {
      refFormProtocolTenTruong.current.resetFields();
    }
  };

  const onResetProtocolForm = () => {
    setEditProtocolId(null);
    setEditStatus(false);
    if (refFormProtocol.current) {
      refFormProtocol.current.resetFields();
    }
  };

  const addNewProtocolTenTruong = (values) => {
    showLoading();

    createOrEditProtocolTenTruong({ ...values, id: editProtocolTenTruongId })
      .then(() => {
        getListAllProtocolTenTruong({
          pageProtocolTenTruong,
          sizeProtocolTenTruong,
          sort: combineSort(dataSortProtocolTenTruong),
          ...dataSearchProtocolTenTruong,
          ...defaultParams,
        }).then(() => {
          const params = {
            pageProtocolTenTruong: editProtocolTenTruongId
              ? pageProtocolTenTruong
              : 0,
            sizeProtocolTenTruong,
          };
          const sort = editProtocolTenTruongId
            ? { ...dataSortProtocolTenTruong }
            : { createdAt: 2 };
          searchProtocolTenTruong({
            ...params,
            ...dataSearchProtocolTenTruong,
            sort: combineSort(sort),
          });
          if (!editProtocolTenTruongId) {
            refFormProtocolTenTruong.current.resetFields();
          }
        });
      })
      .catch((err) => console.error(err))
      .finally(() => {
        hideLoading();
      });
  };

  const onSizeChangeProtocolTenTruong = (size) => {
    const params = {
      pageProtocolTenTruong,
      sizeProtocolTenTruong: size,
    };
    updateDataProtocolTenTruong(params);
    searchProtocolTenTruong({
      ...params,
      ...dataSearchProtocolTenTruong,
      sort: combineSort(dataSortProtocolTenTruong),
    });
  };

  const onChangePageProtocolTenTruong = (values) => {
    const params = { pageProtocolTenTruong: values - 1, sizeProtocolTenTruong };
    updateDataProtocolTenTruong(params);
    searchProtocolTenTruong({
      ...params,
      ...dataSearchProtocolTenTruong,
      sort: combineSort(dataSortProtocolTenTruong),
    });
  };

  const addNewProtocol = (values) => {
    showLoading();

    createOrEditProtocol({ ...values, id: editProtocolId })
      .then(() => {
        getListAllProtocol({
          pageProtocol,
          sizeProtocol,
          ...dataSearchProtocol,
          sort: combineSort(dataSortProtocol),
        }).then(() => {
          const params = {
            pageProtocol: editProtocolId ? pageProtocol : 0,
            sizeProtocol,
          };
          const sort = editProtocolId
            ? { ...dataSortProtocol }
            : { createdAt: 2 };
          searchProtocol({
            ...params,
            ...dataSearchProtocol,
            sort: combineSort(sort),
          });
          if (!editProtocolId) {
            refFormProtocol.current.resetFields();
          }
        });
      })
      .catch((err) => console.error(err))
      .finally(() => {
        hideLoading();
      });
  };

  const onSizeChangeProtocol = (size) => {
    const params = {
      pageProtocol,
      sizeProtocol: size,
    };
    updateDataProtocol(params);
    searchProtocol({
      ...params,
      ...dataSearchProtocol,
      sort: combineSort(dataSortProtocol),
    });
  };

  const onChangePageProtocol = (values) => {
    const params = { pageProtocol: values - 1, sizeProtocol };
    updateDataProtocol(params);
    searchProtocol({
      ...params,
      ...dataSearchProtocol,
      sort: combineSort(dataSortProtocol),
    });
  };

  const listPanel = [
    {
      key: 1,
      title: t("common.thongTinChiTiet"),
      render: () => {
        return (
          <FormProtocol
            layerId={layerId2}
            ref={refFormProtocol}
            editStatus={editStatus}
            handleSubmit={addNewProtocol}
            listAllDVPTTT={listAllDVPTTT}
          />
        );
      },
    },
    {
      key: 2,
      title: t("danhMuc.thietLapProtocol"),
      render: () => {
        return (
          <ThietLapProtocol
            protocolId={editProtocolId}
            roleSave={[ROLES["DANH_MUC"].PROTOCOL_THEM]}
            roleEdit={[ROLES["DANH_MUC"].PROTOCOL_SUA]}
            editStatus={
              editStatus
                ? !checkRole([ROLES["DANH_MUC"].PROTOCOL_SUA])
                : !checkRole([ROLES["DANH_MUC"].PROTOCOL_THEM])
            }
          />
        );
      },
    },
  ];

  return (
    <Main>
      <BaseDm2
        breadcrumb={[
          { title: t("danhMuc.title"), link: "/danh-muc" },
          {
            title: t("danhMuc.danhMucKhaiBaoMauProtocolPttt"),
            link: "/danh-muc/protocol",
          },
        ]}
        title={t("danhMuc.danhMucKhaiBaoMauProtocolPttt")}
      >
        <Col
          {...(!state.showFullTable
            ? state.collapseStatus
              ? TABLE_LAYOUT_COLLAPSE
              : TABLE_LAYOUT
            : null)}
          className={`pr-3 ${
            state.changeCollapseStatus ? "transition-ease" : ""
          }`}
        >
          <Tabs
            activeKey={activeTab}
            defaultActiveKey={activeTab}
            onChange={callback}
            className="table-tab"
          >
            <TabPane tab={t("danhMuc.danhMucMaHoaProtocolChung")} key="0">
              <ProtocolTenTruong
                layerId={layerId1}
                showFullTable={state.showFullTable}
                collapseStatus={state.collapseStatus}
                handleChangeshowTable={handleChangeshowTable}
                handleCollapsePane={handleCollapsePane}
                listProtocolTenTruong={listProtocolTenTruong}
                listAllProtocolTenTruong={listAllProtocolTenTruong}
                page={pageProtocolTenTruong}
                size={sizeProtocolTenTruong}
                total={totalProtocolTenTruong}
                sortData={dataSortProtocolTenTruong}
                searchProtocolTenTruong={searchProtocolTenTruong}
                onEditProtocolTenTruong={onEditProtocolTenTruong}
                setEditStatus={setEditStatus}
                onSizeChange={onSizeChangeProtocolTenTruong}
                onPageChange={onChangePageProtocolTenTruong}
                onReset={onResetProtocolTenTruongForm}
                updateData={updateDataProtocolTenTruong}
              />
            </TabPane>
            <TabPane tab={t("danhMuc.khaiBaoProtocolPTTT")} key="1">
              <Protocol
                layerId={layerId2}
                showFullTable={state.showFullTable}
                collapseStatus={state.collapseStatus}
                handleChangeshowTable={handleChangeshowTable}
                handleCollapsePane={handleCollapsePane}
                listProtocol={listProtocol}
                listAllProtocolTenTruong={listAllProtocolTenTruong}
                listAllDVPTTT={listAllDVPTTT}
                page={pageProtocol}
                size={sizeProtocol}
                total={totalProtocol}
                dataSearch={dataSearchProtocol}
                sortData={dataSortProtocol}
                searchProtocol={searchProtocol}
                onEditProtocol={onEditProtocol}
                setEditStatus={setEditStatus}
                onSizeChange={onSizeChangeProtocol}
                onPageChange={onChangePageProtocol}
                onReset={onResetProtocolForm}
                updateData={updateDataProtocol}
              />
            </TabPane>
          </Tabs>
        </Col>
        {!state.showFullTable && (
          <Col
            {...(state.collapseStatus ? ADD_LAYOUT_COLLAPSE : ADD_LAYOUT)}
            className={`mt-3  ${
              state.changeCollapseStatus ? "transition-ease" : ""
            }`}
            style={
              state.isSelected
                ? { border: "2px solid #c1d8fd", borderRadius: 20 }
                : {}
            }
          >
            {activeTab === "0" && (
              <>
                <FormProtocolTenTruong
                  layerId={layerId1}
                  ref={refFormProtocolTenTruong}
                  editStatus={editStatus}
                  handleSubmit={addNewProtocolTenTruong}
                  listAllProtocolTenTruong={listAllProtocolTenTruong}
                />
              </>
            )}
            {activeTab === "1" && (
              <MultiLevelTab
                className="tab-dm-protocol"
                ref={refTab}
                listPanel={listPanel}
                isBoxTabs={true}
                activeKey={state.activeKeyTab}
                onChange={(activeKeyTab) => setState({ activeKeyTab })}
              ></MultiLevelTab>
            )}
          </Col>
        )}
      </BaseDm2>
    </Main>
  );
};

export default DmProtocol;
