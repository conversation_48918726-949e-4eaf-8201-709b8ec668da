import React, {
  forwardRef,
  useEffect,
  useImperative<PERSON><PERSON>le,
  useRef,
  useState,
} from "react";
import { Input, Form } from "antd";
import { Checkbox, CreatedWrapper, Select } from "components";
import { checkRole } from "lib-utils/role-utils";
import { HOTKEY, ROLES } from "constants/index";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { selectMaTen } from "redux-store/selectors";

const FormProtocolTenTruong = (
  { handleSubmit, onCancel, editStatus, layerId, listAllProtocolTenTruong },
  ref
) => {
  const { t } = useTranslation();
  const refClickBtnAdd = useRef();
  const refClickBtnSave = useRef();
  const { onRegisterHotkey } = useDispatch().phimTat;
  const [form] = Form.useForm();
  const formRef = React.useRef();
  const [dataEdit, setdataEdit] = useState(null);
  const handleAddNew = () => {
    form
      .validateFields()
      .then((values) => {
        handleSubmit(values);
      })
      .catch((error) => {});
  };
  refClickBtnSave.current = handleAddNew;
  refClickBtnAdd.current = () => {
    form.resetFields();
    setdataEdit(null);
    if (refAutoFocus.current) {
      refAutoFocus.current.focus();
    }
  };

  useEffect(() => {
    onRegisterHotkey({
      layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.F1,
          onEvent: () => {
            refClickBtnAdd.current && refClickBtnAdd.current();
          },
        },
        {
          keyCode: HOTKEY.F4,
          onEvent: (e) => {
            refClickBtnSave.current && refClickBtnSave.current(e);
          },
        },
      ],
    });
  }, []);

  useImperativeHandle(
    ref,
    () => ({
      setfields: (data) => {
        if (data?.editProtocolTenTruongId) {
          form.setFieldsValue(data?.info);
          setdataEdit(data?.info?.id);
        } else {
          form.resetFields();
          setdataEdit(null);
        }
      },
      resetFields: (data) => {
        form.resetFields();
        setdataEdit(null);
      },
    }),
    []
  );
  const refAutoFocus = useRef(null);
  useEffect(() => {
    if (refAutoFocus.current) {
      refAutoFocus.current.focus();
    }
  }, [dataEdit]);

  return (
    <>
      <CreatedWrapper
        title={t("common.thongTinChiTiet")}
        onCancel={onCancel}
        cancelText={t("common.huy")}
        onOk={handleAddNew}
        okText={t("common.luu") + " [F4]"}
        roleSave={[ROLES["DANH_MUC"].PROTOCOL_THEM]}
        roleEdit={[ROLES["DANH_MUC"].PROTOCOL_SUA]}
        editStatus={editStatus}
      >
        <fieldset
          disabled={
            !checkRole([ROLES["DANH_MUC"].PROTOCOL_THEM]) && !editStatus
          }
        >
          <Form
            ref={formRef}
            form={form}
            layout="vertical"
            style={{ width: "100%" }}
            className="form-custom--one-line"
          >
            <Form.Item
              label={t("danhMuc.maTruong")}
              name="ma"
              rules={[
                {
                  required: true,
                  message: t("danhMuc.vuiLongNhapMaTruong") + "!",
                },
                {
                  whitespace: true,
                  message: t("danhMuc.vuiLongNhapMaTruong") + "!",
                },
              ]}
            >
              <Input
                ref={refAutoFocus}
                className="input-option"
                placeholder={t("danhMuc.vuiLongNhapMaTruong")}
              />
            </Form.Item>
            <Form.Item
              label={t("danhMuc.tenNoiDungTinhChat")}
              name="ten"
              rules={[
                {
                  required: true,
                  message: t("danhMuc.vuiLongNhapTenNoiDungTinhChat") + "!",
                },
                {
                  max: 255,
                  message: t(
                    "danhMuc.vuiLongNhapTenNoiDungTinhChatKhongQuaNumKyTu",
                    { num: 255 }
                  ),
                },
                {
                  whitespace: true,
                  message: t("danhMuc.vuiLongNhapTenNoiDungTinhChat") + "!",
                },
              ]}
            >
              <Input
                className="input-option"
                placeholder={t("danhMuc.vuiLongNhapTenNoiDungTinhChat")}
              />
            </Form.Item>
            <Form.Item label={t("danhMuc.capCha")} name="tenTruongChaId">
              <Select
                className="input-option"
                placeholder={t("danhMuc.vuiLongChonCapCha")}
                data={listAllProtocolTenTruong}
                getLabel={selectMaTen}
              />
            </Form.Item>
            {dataEdit && (
              <Form.Item name="active" valuePropName="checked">
                <Checkbox>{t("danhMuc.coHieuLuc")}</Checkbox>
              </Form.Item>
            )}
          </Form>
        </fieldset>
      </CreatedWrapper>
    </>
  );
};

export default forwardRef(FormProtocolTenTruong);
