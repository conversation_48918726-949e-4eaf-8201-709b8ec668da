import React, { useEffect, useRef, useState } from "react";
import { Input } from "antd";
import {
  Checkbox,
  Pagination,
  HeaderSearch,
  TableWrapper,
  Select,
  Button,
  EllipsisText,
} from "components";
import { ROLES, PAGE_DEFAULT, HIEU_LUC, HOTKEY } from "constants/index";
import { combineSort } from "utils";
import { checkRole } from "lib-utils/role-utils";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { SVG } from "assets";

let timer = null;

const Protocol = ({
  searchProtocol,
  page,
  size,
  total,
  listProtocol,
  onPageChange,
  onSizeChange,
  onReset,
  updateData,
  onEditProtocol,
  dataSearch,
  sortData,
  setEditStatus,
  handleChangeshowTable,
  showFullTable,
  collapseStatus,
  handleCollapsePane,
  layerId,
  listAllDVPTTT,
}) => {
  const { t } = useTranslation();
  const [dataEditDefault, setDataEditDefault] = useState(null);

  const refSelectRow = useRef();
  const {
    phimTat: { onRemoveLayer, onRegisterHotkey },
    protocol: { onExport, onImport },
  } = useDispatch();

  useEffect(() => {
    onRegisterHotkey({
      layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.UP,
          onEvent: (e) => {
            refSelectRow.current && refSelectRow.current(-1);
          },
        },
        {
          keyCode: HOTKEY.DOWN,
          onEvent: (e) => {
            refSelectRow.current && refSelectRow.current(1);
          },
        },
      ],
    });
    return () => {
      onRemoveLayer({ layerId });
    };
  }, []);

  refSelectRow.current = (index) => {
    const indexNextItem =
      (data?.findIndex((item) => item.id === dataEditDefault?.id) || 0) + index;
    if (-1 < indexNextItem && indexNextItem < data.length) {
      setEditStatus(true);
      setDataEditDefault(data[indexNextItem]);
      onEditProtocol(data[indexNextItem]);
      document
        .getElementsByClassName("row-id-" + data[indexNextItem]?.id)[0]
        .scrollIntoView({ block: "end", behavior: "smooth" });
    }
  };

  const onClickSort = (key, value) => {
    const sort = { ...sortData, [key]: value };
    updateData({ dataSortProtocol: sort });
    const res = combineSort(sort);
    searchProtocol({
      pageProtocol: PAGE_DEFAULT,
      sizeProtocol: size,
      sort: res,
      ...dataSearch,
    });
  };
  useEffect(() => {
    searchProtocol({
      pageProtocol: 0,
      sizeProtocol: 10,
      sort: combineSort(sortData),
    });
  }, []);

  const onSearchInput = (value, name) => {
    clearTimeout(timer);
    timer = setTimeout(() => {
      updateData({
        datasearchProtocol: { ...dataSearch, [name]: value },
      });
      searchProtocol({
        ...dataSearch,
        pageProtocol: 0,
        sizeProtocol: size,
        [name]: value,
        sort: combineSort(sortData),
      });
    }, 500);
  };

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: 40,
      dataIndex: "index",
      key: "index",
      align: "center",
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.maMauProtocol")}
          sort_key="ma"
          onClickSort={onClickSort}
          dataSort={sortData["ma"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.timMaMauProtocol")}
              onChange={(e) => {
                onSearchInput(e.target.value, "ma");
              }}
            />
          }
        />
      ),
      width: 100,
      dataIndex: "ma",
      key: "ma",
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.tenMauProtocol")}
          sort_key="ten"
          onClickSort={onClickSort}
          dataSort={sortData.ten || 0}
          search={
            <Input
              placeholder={t("danhMuc.timTenMauProtocol")}
              onChange={(e) => {
                onSearchInput(e.target.value, "ten");
              }}
            />
          }
        />
      ),
      width: 160,
      dataIndex: "ten",
      key: "ten",
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.dichVuApDung")}
          searchSelect={
            <Select
              placeholder={t("danhMuc.timDichVuApDung")}
              data={listAllDVPTTT}
              value={dataSearch?.dsDichVuId}
              onChange={(e) => {
                onSearchInput(e, "dsDichVuId");
              }}
            />
          }
        />
      ),
      width: 200,
      dataIndex: "dsDichVu",
      key: "dsDichVu",
      render: (dsDichVu) => {
        if (!Array.isArray(dsDichVu)) return null;

        const content = dsDichVu.map((dv) => dv.ten).join(", ");

        return (
          <EllipsisText.Tooltip
            tooltipPlacement="right"
            limitLine={3}
            content={content}
          />
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.coHieuLuc")}
          sort_key="active"
          onClickSort={onClickSort}
          dataSort={sortData.active || 0}
          searchSelect={
            <Select
              defaultValue=""
              data={HIEU_LUC}
              placeholder={t("danhMuc.chonHieuLuc")}
              onChange={(value) => {
                onSearchInput(value, "active");
              }}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 90,
      dataIndex: "active",
      key: "active",
      align: "center",
      render: (item) => {
        return <Checkbox checked={item} onClick={() => {}} />;
      },
    },
  ];

  const onRow = (record, index) => ({
    onClick: (event) => {
      setEditStatus(true);
      setDataEditDefault(record.action);
      onEditProtocol(record.action);
    },
  });
  const setRowClassName = (record) => {
    let idDiff = dataEditDefault?.id;
    return record.id === idDiff
      ? "row-actived row-id-" + record.id
      : "row-id-" + record.id;
  };
  const data = listProtocol.map((item, index) => {
    return {
      ...item,
      action: item,
      index: page * size + index + 1,
    };
  });

  return (
    <div>
      <TableWrapper
        titleUtilities={t("danhMuc.khaiBaoProtocolPTTT")}
        classNameRow={"custom-header"}
        styleMain={{ marginTop: 0 }}
        styleContainerButtonHeader={{
          display: "flex",
          width: "100%",
          justifyContent: "flex-end",
          alignItems: "center",
          paddingRight: 35,
        }}
        onExport={onExport}
        onImport={onImport}
        buttonHeader={
          checkRole([ROLES["DANH_MUC"].PROTOCOL_THEM])
            ? [
                {
                  content: (
                    <Button
                      type="success"
                      onClick={onReset}
                      rightIcon={<SVG.IcAdd />}
                    >
                      {t("common.themMoiF1")}
                    </Button>
                  ),
                },
                {
                  className: `btn-change-full-table ${
                    showFullTable ? "small" : "large"
                  }`,
                  title: showFullTable ? (
                    <SVG.IcShowThuNho />
                  ) : (
                    <SVG.IcShowFull />
                  ),
                  onClick: handleChangeshowTable,
                },

                {
                  className: "btn-collapse",
                  title: collapseStatus ? <SVG.IcExtend /> : <SVG.IcCollapse />,
                  onClick: handleCollapsePane,
                },
              ]
            : [
                {
                  className: `btn-change-full-table ${
                    showFullTable ? "small" : "large"
                  }`,
                  title: showFullTable ? (
                    <SVG.IcShowThuNho />
                  ) : (
                    <SVG.IcShowFull />
                  ),
                  onClick: handleChangeshowTable,
                },

                {
                  className: "btn-collapse",
                  title: collapseStatus ? <SVG.IcExtend /> : <SVG.IcCollapse />,
                  onClick: handleCollapsePane,
                },
              ]
        }
        columns={columns}
        dataSource={data}
        onRow={onRow}
        rowClassName={setRowClassName}
      />
      {!!total && (
        <Pagination
          onChange={onPageChange}
          current={page + 1}
          pageSize={size}
          total={total}
          listData={data}
          onShowSizeChange={onSizeChange}
        />
      )}
    </div>
  );
};
export default Protocol;
