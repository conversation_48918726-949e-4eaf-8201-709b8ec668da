import React, { useState, useEffect, useMemo } from "react";
import EditWrapper from "components/MultiLevelTab/EditWrapper";
import {
  TableWrapper,
  Pagination,
  HeaderSearch,
  Select,
  Checkbox,
} from "components";
import { useDispatch } from "react-redux";
import { ENUM, HIEU_LUC, YES_NO } from "constants/index";
import { message } from "antd";
import { checkRole } from "lib-utils/role-utils";
import { useEnum, useStore, useIsMounted, useLoading } from "hooks";
import { useTranslation } from "react-i18next";
import { Main } from "../styled";

const ThietLapProtocol = (props) => {
  const { protocolId, roleSave, roleEdit, editStatus } = props;
  const _editStatus = useMemo(() => editStatus, [editStatus]);
  const { t } = useTranslation();
  const { showLoading, hideLoading } = useLoading();
  const isMounted = useIsMounted();

  const [state, _setState] = useState({
    active: false,
    data: [],
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const {
    protocolChiTiet: {
      getData,
      onSearch,
      onSizeChange,
      onSortChange,
      onChangeInputSearch,
      createOrEdit,
    },
    protocolTenTruong: { getListAllProtocolTenTruong },
  } = useDispatch();

  const { listData, size, page, totalElements, dataSortColumn } = useStore(
    "protocolChiTiet",
    {},
    { field: "listData,size,page,totalElements,dataSortColumn" }
  );
  const listAllProtocolTenTruong = useStore(
    "protocolTenTruong.listAllProtocolTenTruong",
    []
  );

  const [listLoaiHienThi] = useEnum(ENUM.LOAI_HIEN_THI);

  useEffect(() => {
    if (protocolId) {
      getData({ protocolId });
    }
    getListAllProtocolTenTruong({
      page: "",
      size: "",
      active: true,
    });
    setState({
      isNew: false,
      pressButtonAdded: false,
      currentIndex: -1,
      currentItem: null,
    });
  }, [protocolId]);

  useEffect(() => {
    if (isMounted()) setState({ data: [...(listData || [])] });
  }, [listData, page, size, isMounted]);

  useEffect(() => {}, []);

  const onClickSort = (key, value) => {
    onSortChange({
      [key]: value,
    });
  };

  const onSearchInput = (key) => (e) => {
    let value = "";
    if (e?.target) {
      if (e.target.hasOwnProperty("checked")) value = e.target.checked;
      else value = e.target.value;
    } else if (e?._d) value = e.format("YYYY-MM-DD");
    else value = e;
    onChangeInputSearch({
      [key]: value,
      protocolId,
    });
  };

  const onChange = (key) => (e) => {
    let value = "";
    if (e?.target) {
      if (e.target.hasOwnProperty("checked")) value = e.target.checked;
      else value = e.target.value;
    } else if (e?._d) value = e._d;
    else value = e;
    if (state.currentItem) {
      setState({ currentItem: { ...state.currentItem, [key]: value } });
    }
  };

  const onChangeCheckbox = (key, item, index) => (e) => {
    e.stopPropagation();
    if (state.pressButtonAdded && state.currentIndex !== index) return;
    const value = e.target.checked;
    const _currentItem =
      state.currentIndex === index && state.currentItem
        ? state.currentItem
        : item;
    const data = {
      currentItem: {
        ..._currentItem,
        [key]: value,
      },
      ...(state.currentIndex !== index && {
        currentIndex: index,
      }),
      ...(!state.pressedRow &&
        state.currentIndex !== index && {
          pressedRow: true,
        }),
    };
    setState(data);
  };

  const onChangePage = (page) => {
    onSearch({ page: page - 1, protocolId });
  };

  const onChangeSize = (size) => {
    onSizeChange({ size: size, protocolId });
  };

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: 50,
      dataIndex: "index",
      key: "index",
    },
    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("danhMuc.tenTruong")}
              <span style={{ color: "red" }}>*</span>
            </>
          }
          sort_key="protocolTenTruongId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["protocolTenTruongId"] || 0}
          searchSelect={
            <Select
              data={listAllProtocolTenTruong}
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("protocolTenTruongId")}
            />
          }
        />
      ),
      width: 180,
      dataIndex: "protocolTenTruong",
      key: "protocolTenTruong",
      render: (item, _, index) => {
        if (index === state.currentIndex) {
          return (
            <Select
              placeholder={t("danhMuc.chonTenTruong")}
              data={listAllProtocolTenTruong}
              onChange={onChange("protocolTenTruongId")}
              value={state.currentItem?.protocolTenTruongId}
            ></Select>
          );
        } else return item?.ten || "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("danhMuc.dangHienThi")}
              <span style={{ color: "red" }}>*</span>
            </>
          }
          sort_key="loaiHienThi"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["loaiHienThi"] || 0}
          searchSelect={
            <Select
              data={listLoaiHienThi}
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("loaiHienThi")}
            />
          }
        />
      ),
      width: 180,
      dataIndex: "loaiHienThi",
      key: "loaiHienThi",
      render: (item, _, index) => {
        if (index === state.currentIndex) {
          return (
            <Select
              placeholder={t("danhMuc.chonDangHienThi")}
              data={listLoaiHienThi}
              onChange={onChange("loaiHienThi")}
              value={state.currentItem?.loaiHienThi}
            ></Select>
          );
        } else return listLoaiHienThi?.find((i) => i.id === item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.choPhepBoSungThemTextGhiChu")}
          sort_key="ghiChu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.ghiChu || 0}
          searchSelect={
            <Select
              data={YES_NO}
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("ghiChu")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "ghiChu",
      key: "ghiChu",
      align: "center",
      render: (item, list, index) => {
        return (
          <Checkbox
            checked={
              index == state.currentIndex ? state.currentItem?.ghiChu : item
            }
            onChange={onChangeCheckbox("ghiChu", list, index)}
          />
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.choPhepThucHienThatBai")}
          sort_key="thucHienThatBai"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.thucHienThatBai || 0}
          searchSelect={
            <Select
              data={YES_NO}
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("thucHienThatBai")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "thucHienThatBai",
      key: "thucHienThatBai",
      align: "center",
      render: (item, list, index) => {
        return (
          <Checkbox
            checked={
              index == state.currentIndex
                ? state.currentItem?.thucHienThatBai
                : item
            }
            onChange={onChangeCheckbox("thucHienThatBai", list, index)}
          />
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.macDinhChon")}
          sort_key="macDinh"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.macDinh || 0}
          searchSelect={
            <Select
              data={YES_NO}
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("macDinh")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "macDinh",
      key: "macDinh",
      align: "center",
      render: (item, list, index) => {
        return (
          <Checkbox
            checked={
              index == state.currentIndex ? state.currentItem?.macDinh : item
            }
            onChange={onChangeCheckbox("macDinh", list, index)}
          />
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.layLenBaoCao")}
          sort_key="baoCao"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.baoCao || 0}
          searchSelect={
            <Select
              data={YES_NO}
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("baoCao")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "baoCao",
      key: "baoCao",
      align: "center",
      render: (item, list, index) => {
        return (
          <Checkbox
            checked={
              index == state.currentIndex ? state.currentItem?.baoCao : item
            }
            onChange={onChangeCheckbox("baoCao", list, index)}
          />
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.batBuocKhaiBao")}
          sort_key="batBuoc"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.batBuoc || 0}
          searchSelect={
            <Select
              data={YES_NO}
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("batBuoc")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "batBuoc",
      key: "batBuoc",
      align: "center",
      render: (item, list, index) => {
        return (
          <Checkbox
            checked={
              index == state.currentIndex ? state.currentItem?.batBuoc : item
            }
            onChange={onChangeCheckbox("batBuoc", list, index)}
          />
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.coHieuLuc")}
          sort_key="active"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.active || 0}
          searchSelect={
            <Select
              data={HIEU_LUC}
              placeholder={t("danhMuc.chonHieuLuc")}
              onChange={onSearchInput("active")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "active",
      key: "active",
      align: "center",
      render: (item, list, index) => {
        return (
          <Checkbox
            checked={
              index == state.currentIndex ? state.currentItem?.active : item
            }
            onChange={onChangeCheckbox("active", list, index)}
          />
        );
      },
    },
  ];

  const onRow = (record = {}, index) => {
    if (!state.pressButtonAdded) {
      return {
        onClick: (event) => {
          if (state?.currentIndex !== index) {
            setState({
              currentItem: JSON.parse(JSON.stringify(record)),
              currentIndex: index,
              pressedRow: true,
            });
          }
        },
      };
    }
  };

  const onAddNewRow = () => {
    if (state.isNew) return;
    let item = { protocolId, active: true };
    setState({
      isNew: true,
      currentItem: item,
      currentIndex: 0,
      data: [item, ...state.data],
      pressButtonAdded: true,
    });
  };

  const onCancel = () => {
    setState({
      isNew: false,
      pressButtonAdded: false,
      currentIndex: -1,
      currentItem: null,
      data: (state.data || []).filter((item) => item.id),
    });
  };

  const onSave = async () => {
    try {
      showLoading();

      const {
        id,
        protocolId,
        protocolTenTruongId,
        loaiHienThi,
        ghiChu,
        thucHienThatBai,
        macDinh,
        baoCao,
        batBuoc,
        active = true,
      } = state.currentItem || {};

      if (!protocolTenTruongId) {
        message.error(
          t("danhMuc.vuiLongChonTitle", {
            title: t("danhMuc.tenTruong").toLowerCase(),
          })
        );
        return;
      }

      if (!loaiHienThi) {
        message.error(
          t("danhMuc.vuiLongChonTitle", {
            title: t("danhMuc.dangHienThi").toLowerCase(),
          })
        );
        return;
      }

      await createOrEdit({
        id,
        active,
        protocolId,
        protocolTenTruongId,
        loaiHienThi,
        ghiChu,
        thucHienThatBai,
        macDinh,
        baoCao,
        batBuoc,
      });
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }

    setState({
      currentIndex: -1,
      currentItem: null,
      pressButtonAdded: false,
      isNew: false,
    });
  };

  return (
    <Main>
      <EditWrapper
        title={t("danhMuc.thietLapProtocol")}
        showAdded={true}
        roleSave={roleSave}
        roleEdit={roleEdit}
        onAddNewRow={onAddNewRow}
        onCancel={onCancel}
        onSave={onSave}
        editStatus={state?.pressButtonAdded ? false : _editStatus}
        isShowSaveButton={state.currentItem}
        isShowCancelButton={state.currentItem}
        forceShowButtonSave={
          (state?.pressedRow && checkRole(props.roleEdit) && true) ||
          (state.pressButtonAdded && checkRole(props.roleEdit) && true) ||
          false
        }
        forceShowButtonCancel={
          (state?.pressedRow && checkRole(props.roleEdit) && true) ||
          (state.pressButtonAdded && checkRole(props.roleEdit) && true) ||
          false
        }
        isEditAndPressRow={protocolId && checkRole(roleEdit)}
      >
        <fieldset disabled={state?.pressButtonAdded ? false : _editStatus}>
          <TableWrapper
            scroll={{ y: 500, x: 700 }}
            columns={columns}
            rowKey={(row) => row.id || "uid"}
            dataSource={protocolId ? state.data : []}
            onRow={onRow}
            rowClassName={(record) => (!record.active ? "row-gray" : "")}
          ></TableWrapper>
          {protocolId && totalElements ? (
            <Pagination
              onChange={onChangePage}
              current={page + 1}
              pageSize={size}
              total={totalElements}
              onShowSizeChange={onChangeSize}
              listData={protocolId ? state.data : []}
              style={{ flex: 1, justifyContent: "flex-end" }}
            />
          ) : null}
        </fieldset>
      </EditWrapper>
    </Main>
  );
};

export default ThietLapProtocol;
