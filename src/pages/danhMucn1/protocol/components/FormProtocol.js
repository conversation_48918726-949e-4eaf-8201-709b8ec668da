import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
  useMemo,
} from "react";
import { Input, Form, Select as AntdSelect } from "antd";
import { Checkbox, CreatedWrapper } from "components";
import { checkRole } from "lib-utils/role-utils";
import { HOTKEY, ROLES } from "constants/index";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { containText } from "utils/index";
import { selectMaTen } from "redux-store/selectors";

const FormProtocol = (
  { handleSubmit, onCancel, editStatus, layerId, listAllDVPTTT },
  ref
) => {
  const { t } = useTranslation();
  const refClickBtnAdd = useRef();
  const refClickBtnSave = useRef();
  const formRef = useRef();

  const {
    phimTat: { onRegisterHotkey },
  } = useDispatch();

  const [form] = Form.useForm();
  const [dataEdit, setdataEdit] = useState(null);

  const handleAddNew = () => {
    form
      .validateFields()
      .then((values) => {
        handleSubmit(values);
      })
      .catch((error) => {});
  };
  refClickBtnSave.current = handleAddNew;
  refClickBtnAdd.current = () => {
    form.resetFields();
    setdataEdit(null);
    if (refAutoFocus.current) {
      refAutoFocus.current.focus();
    }
  };

  useEffect(() => {
    onRegisterHotkey({
      layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.F1,
          onEvent: () => {
            refClickBtnAdd.current && refClickBtnAdd.current();
          },
        },
        {
          keyCode: HOTKEY.F4,
          onEvent: (e) => {
            refClickBtnSave.current && refClickBtnSave.current(e);
          },
        },
      ],
    });
  }, []);

  useImperativeHandle(
    ref,
    () => ({
      setfields: (data) => {
        if (data?.editProtocolId) {
          form.setFieldsValue(data?.info);
          setdataEdit(data?.info?.id);
        } else {
          form.resetFields();
          setdataEdit(null);
        }
      },
      resetFields: (data) => {
        form.resetFields();
        setdataEdit(null);
      },
    }),
    []
  );

  const refAutoFocus = useRef(null);
  useEffect(() => {
    if (refAutoFocus.current) {
      refAutoFocus.current.focus();
    }
  }, [dataEdit]);

  return (
    <>
      <CreatedWrapper
        title={t("common.thongTinChiTiet")}
        onCancel={onCancel}
        cancelText={t("common.huy")}
        onOk={handleAddNew}
        okText={t("common.luuF4")}
        roleSave={[ROLES["DANH_MUC"].PROTOCOL_THEM]}
        roleEdit={[ROLES["DANH_MUC"].PROTOCOL_SUA]}
        editStatus={editStatus}
      >
        <fieldset
          disabled={
            !checkRole([ROLES["DANH_MUC"].PROTOCOL_THEM]) && !editStatus
          }
        >
          <Form
            ref={formRef}
            form={form}
            layout="vertical"
            style={{ width: "100%" }}
            className="form-custom--one-line"
          >
            <Form.Item
              label={t("danhMuc.maMauProtocol")}
              name="ma"
              rules={[
                {
                  required: true,
                  message: `${t("danhMuc.vuiLongNhapMaMauProtocol")}!`,
                },
                {
                  whitespace: true,
                  message: t("danhMuc.vuiLongNhapMaMauProtocol") + "!",
                },
              ]}
            >
              <Input
                ref={refAutoFocus}
                className="input-option"
                placeholder={t("danhMuc.vuiLongNhapMaMauProtocol")}
              />
            </Form.Item>
            <Form.Item
              label={t("danhMuc.tenPhieu")}
              name="ten"
              rules={[
                {
                  required: true,
                  message: `${t("danhMuc.vuiLongNhapTenMauProtocol")}!`,
                },
                {
                  whitespace: true,
                  message: t("danhMuc.vuiLongNhapTenMauProtocol") + "!",
                },
              ]}
            >
              <Input
                className="input-option"
                placeholder={t("danhMuc.vuiLongNhapTenMauProtocol")}
              />
            </Form.Item>
            <Form.Item label={t("danhMuc.dichVuApDung")} name="dsDichVuId">
              <CustomSelect
                className="input-option"
                placeholder={t("danhMuc.chonDichVuApDung")}
                data={listAllDVPTTT}
                getLabel={selectMaTen}
                mode="multiple"
                allowClear
              />
            </Form.Item>
            {dataEdit && (
              <Form.Item name="active" valuePropName="checked">
                <Checkbox>{t("danhMuc.coHieuLuc")}</Checkbox>
              </Form.Item>
            )}
          </Form>
        </fieldset>
      </CreatedWrapper>
    </>
  );
};

const CustomSelect = (props) => {
  const {
    value,
    onChange,
    data = [],
    placeholder,
    className,
    mode,
    getLabel = (item) => item?.label ?? item?.name,
    ...restProps
  } = props;

  const filterOption = (input, option) => {
    return containText(option?.props?.children, input);
  };

  const listOptions = useMemo(() => {
    let options = data.map((item, index) => (
      <AntdSelect.Option key={index} value={item?.id ?? item?.value}>
        {getLabel(item)}
      </AntdSelect.Option>
    ));
    return options;
  }, [data]);

  return (
    <AntdSelect
      className={className}
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      mode={mode}
      allowClear
      showSearch
      filterOption={filterOption}
      {...restProps}
    >
      {listOptions}
    </AntdSelect>
  );
};

export default forwardRef(FormProtocol);
