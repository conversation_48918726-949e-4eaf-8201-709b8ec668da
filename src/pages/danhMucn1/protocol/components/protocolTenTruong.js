import React, { useEffect, useRef, useState } from "react";
import { Input } from "antd";
import {
  Checkbox,
  Pagination,
  HeaderSearch,
  TableWrapper,
  Select,
  Button,
} from "components";
import { PAGE_DEFAULT, HIEU_LUC, ROLES, HOTKEY } from "constants/index";
import { combineSort } from "utils";
import { checkRole } from "lib-utils/role-utils";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { SVG } from "assets";
import { selectMaTen } from "redux-store/selectors";

let timer = null;

const ProtocolTenTruong = ({
  searchProtocolTenTruong,
  page,
  size,
  total,
  listProtocolTenTruong,
  listAllProtocolTenTruong,
  onPageChange,
  onSizeChange,
  onReset,
  updateData,
  onEditProtocolTenTruong,
  dataSearch,
  sortData,
  setEditStatus,
  handleChangeshowTable,
  showFullTable,
  collapseStatus,
  handleCollapsePane,
  layerId,
}) => {
  const { t } = useTranslation();
  const [dataEditDefault, setDataEditDefault] = useState(null);
  const refSelectRow = useRef();
  const {
    phimTat: { onRemoveLayer, onRegisterHotkey },
    protocolTenTruong: { onExport, onImport },
  } = useDispatch();

  useEffect(() => {
    onRegisterHotkey({
      layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.UP,
          onEvent: (e) => {
            refSelectRow.current && refSelectRow.current(-1);
          },
        },
        {
          keyCode: HOTKEY.DOWN,
          onEvent: (e) => {
            refSelectRow.current && refSelectRow.current(1);
          },
        },
      ],
    });
    return () => {
      onRemoveLayer({ layerId });
    };
  }, []);

  refSelectRow.current = (index) => {
    const indexNextItem =
      (data?.findIndex((item) => item.id === dataEditDefault?.id) || 0) + index;
    if (-1 < indexNextItem && indexNextItem < data.length) {
      setEditStatus(true);
      setDataEditDefault(data[indexNextItem]);
      onEditProtocolTenTruong(data[indexNextItem]);
      document
        .getElementsByClassName("row-id-" + data[indexNextItem]?.id)[0]
        .scrollIntoView({ block: "end", behavior: "smooth" });
    }
  };

  const onClickSort = (key, value) => {
    const sort = { ...sortData, [key]: value };
    updateData({ dataSortProtocolTenTruong: sort });
    const res = combineSort(sort);
    searchProtocolTenTruong({
      pageProtocolTenTruong: PAGE_DEFAULT,
      sizeProtocolTenTruong: size,
      sort: res,
      ...dataSearch,
    });
  };
  useEffect(() => {
    searchProtocolTenTruong({
      pageProtocolTenTruong: 0,
      sizeProtocolTenTruong: 10,
      sort: combineSort(sortData),
    });
  }, []);

  const onSearchInput = (value, name) => {
    clearTimeout(timer);
    timer = setTimeout(() => {
      updateData({
        dataSearchProtocolTenTruong: { ...dataSearch, [name]: value },
      });
      searchProtocolTenTruong({
        ...dataSearch,
        pageProtocolTenTruong: 0,
        sizeProtocolTenTruong: size,
        [name]: value,
        sort: combineSort(sortData),
      });
    }, 500);
  };
  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: 50,
      dataIndex: "index",
      key: "index",
      align: "center",
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.maTruong")}
          sort_key="ma"
          onClickSort={onClickSort}
          dataSort={sortData.ma || 0}
          search={
            <Input
              placeholder={t("danhMuc.timMaTruong")}
              onChange={(e) => {
                onSearchInput(e.target.value, "ma");
              }}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "ma",
      key: "ma",
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.tenNoiDungTinhChat")}
          sort_key="ten"
          onClickSort={onClickSort}
          dataSort={sortData.ten || 0}
          search={
            <Input
              placeholder={t("danhMuc.timTenNoiDungTinhChat")}
              onChange={(e) => {
                onSearchInput(e.target.value, "ten");
              }}
            />
          }
        />
      ),
      width: 180,
      dataIndex: "ten",
      key: "ten",
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.capCha")}
          searchSelect={
            <Select
              placeholder={t("danhMuc.timCapCha")}
              data={listAllProtocolTenTruong}
              getLabel={selectMaTen}
              onChange={(e) => {
                onSearchInput(e, "tenTruongChaId");
              }}
            />
          }
        />
      ),
      width: 180,
      dataIndex: "tenTruongChaId",
      key: "tenTruongChaId",
      render: (item) => {
        const obj = (listAllProtocolTenTruong || []).find((x) => x.id === item);
        return obj ? `${obj.ma} - ${obj.ten}` : "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.coHieuLuc")}
          sort_key="active"
          onClickSort={onClickSort}
          dataSort={sortData.active || 0}
          searchSelect={
            <Select
              defaultValue=""
              data={HIEU_LUC}
              placeholder={t("danhMuc.chonHieuLuc")}
              onChange={(value) => {
                onSearchInput(value, "active");
              }}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 80,
      dataIndex: "active",
      key: "active",
      align: "center",
      render: (item) => {
        return <Checkbox checked={item} onClick={() => {}} />;
      },
    },
  ];

  const onRow = (record, index) => ({
    onClick: (event) => {
      setEditStatus(true);
      setDataEditDefault(record.action);
      onEditProtocolTenTruong(record.action);
    },
  });
  const setRowClassName = (record) => {
    let idDiff = dataEditDefault?.id;
    return record.id === idDiff
      ? "row-actived row-id-" + record.id
      : "row-id-" + record.id;
  };
  const data = listProtocolTenTruong.map((item, index) => {
    return {
      ...item,
      action: item,
      index: page * size + index + 1,
    };
  });

  return (
    <div>
      <TableWrapper
        titleUtilities={t("danhMuc.danhMucMaHoaProtocolChung")}
        classNameRow={"custom-header"}
        styleMain={{ marginTop: 0 }}
        styleContainerButtonHeader={{
          display: "flex",
          width: "100%",
          justifyContent: "flex-end",
          alignItems: "center",
          paddingRight: 35,
        }}
        onExport={onExport}
        onImport={onImport}
        buttonHeader={
          checkRole([ROLES["DANH_MUC"].PROTOCOL_THEM])
            ? [
                {
                  content: (
                    <Button
                      type="success"
                      onClick={onReset}
                      rightIcon={<SVG.IcAdd />}
                    >
                      {t("common.themMoiF1")}
                    </Button>
                  ),
                },
                {
                  className: `btn-change-full-table ${
                    showFullTable ? "small" : "large"
                  }`,
                  title: showFullTable ? (
                    <SVG.IcShowThuNho />
                  ) : (
                    <SVG.IcShowFull />
                  ),
                  onClick: handleChangeshowTable,
                },

                {
                  className: "btn-collapse",
                  title: collapseStatus ? <SVG.IcExtend /> : <SVG.IcCollapse />,
                  onClick: handleCollapsePane,
                },
              ]
            : [
                {
                  className: `btn-change-full-table ${
                    showFullTable ? "small" : "large"
                  }`,
                  title: showFullTable ? (
                    <SVG.IcShowThuNho />
                  ) : (
                    <SVG.IcShowFull />
                  ),
                  onClick: handleChangeshowTable,
                },

                {
                  className: "btn-collapse",
                  title: collapseStatus ? <SVG.IcExtend /> : <SVG.IcCollapse />,
                  onClick: handleCollapsePane,
                },
              ]
        }
        columns={columns}
        dataSource={data}
        onRow={onRow}
        rowClassName={setRowClassName}
      />
      {!!total && (
        <Pagination
          onChange={onPageChange}
          current={page + 1}
          pageSize={size}
          total={total}
          listData={data}
          onShowSizeChange={onSizeChange}
        />
      )}
    </div>
  );
};
export default ProtocolTenTruong;
