import React, { useEffect, useMemo } from "react";
import moment from "moment";
import { Col, Row } from "antd";
import { useDispatch } from "react-redux";
import { DateTimePicker, Select, SelectLoadMore } from "components";
import BaseBaoCao from "pages/baocao/BaseBaoCao";
import { useTranslation } from "react-i18next";
import { useListAll, useEnum, useStore, useCache } from "hooks";
import {
  ENUM,
  CACHE_KEY,
  LOAI_THOI_GIAN,
  TRANG_THAI_THANH_TOAN_PHIEU_THU,
} from "constants/index";
import dichVuProvider from "data-access/categories/dm-dich-vu-provider";
/**
 * PTTT04.9. Bảng thanh toán tiền Phẫu thuật - Thủ thuật
 *
 */

const params = { page: "", size: "", active: true };

const Index = () => {
  const { t } = useTranslation();
  const {
    baoCaoDaIn: { getPttt04_9 },
    phong: { getListAllPhong },
    nhomDichVuCap1: { getAllTongHopDichVuCap1 },
    nhomDichVuCap2: { getAllTongHopDichVuCap2 },
    nhomDichVuCap3: { getAllTongHopDichVuCap3 },
  } = useDispatch();

  const [listDoiTuong] = useEnum(ENUM.DOI_TUONG);
  const [listLoaiThoiGian] = useEnum(ENUM.LOAI_THOI_GIAN);
  const [listDoiTuongKcb] = useEnum(ENUM.DOI_TUONG_KCB);
  const [listTrangThaiHoan] = useEnum(ENUM.TRANG_THAI_HOAN);
  const [listLoaiPhuCapPtTt] = useEnum(ENUM.LOAI_PHU_CAP_PTTT);

  const [listAllToaNha] = useListAll("toaNha", {}, true);
  const [listAllKhoa] = useListAll("khoa", {}, true);
  const [listAllKhoaTheoTaiKhoan] = useListAll(
    "khoa",
    {},
    true,
    "KhoaTheoTaiKhoan"
  );

  const listAllPhong = useStore("phong.listAllPhong", []);
  const listAllNhomDichVuCap1 = useStore(
    "nhomDichVuCap1.listAllNhomDichVuCap1",
    []
  );
  const listAllNhomDichVuCap2 = useStore(
    "nhomDichVuCap2.listAllNhomDichVuCap2",
    []
  );
  const listAllNhomDichVuCap3 = useStore(
    "nhomDichVuCap3.listAllNhomDichVuCap3",
    []
  );

  const [khoaChiDinh, setKhoaChiDinh] = useCache(
    "",
    CACHE_KEY.KHOA_CHI_DINH_PTTT04_9,
    null,
    false
  );
  const [trangThaiHoan, setTrangThaiHoan] = useCache(
    "",
    CACHE_KEY.TRANG_THAI_HOAN_PTTT04_9,
    null,
    false
  );

  useEffect(() => {
    getAllTongHopDichVuCap1(params);
    getAllTongHopDichVuCap2(params);
    getAllTongHopDichVuCap3(params);
    getListAllPhong(params);
  }, []);

  const listLoaiThoiGianMemo = useMemo(() => {
    return listLoaiThoiGian.filter((loaiThoiGian) =>
      [
        LOAI_THOI_GIAN.THEO_THOI_GIAN_THUC_HIEN,
        LOAI_THOI_GIAN.THEO_THOI_GIAN_THANH_TOAN,
        LOAI_THOI_GIAN.THEO_THOI_GIAO_KET_THUC,
      ].some((item) => item === loaiThoiGian.id)
    );
  }, [listLoaiThoiGian]);

  const handleChange = (key, onChange, _state) => (value) => {
    if (key === "dsKhoaThucHienId") {
      getListAllPhong({
        ...params,
        dsKhoaId: value,
      });
      onChange("dsPhongThucHienId")();
    }

    if (key === "dsNhomDichVuCap1Id") {
      getAllTongHopDichVuCap2({
        ...params,
        dsNhomDichVuCap1Id: value,
      });
      onChange("dsNhomDichVuCap2Id")();
      onChange("dsNhomDichVuCap3Id")();
      onChange("dsDichVuId", true)();
    } else if (key === "dsNhomDichVuCap2Id") {
      getAllTongHopDichVuCap3({
        ...params,
        dsNhomDichVuCap2Id: value,
      });
      onChange("dsNhomDichVuCap3Id")();
      onChange("dsDichVuId", true)();
    } else if (key === "dsNhomDichVuCap3Id") {
      onChange("dsDichVuId", true)();
    }

    if (key === "dsTrangThaiHoan") {
      setTrangThaiHoan(value, false);
    }

    if (key === "dsKhoaChiDinhId") {
      setKhoaChiDinh(value, false);
    }

    onChange(key)(value);
  };

  const renderFilter = ({ onChange, _state }) => {
    return (
      <Row>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.theoThoiGian")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonLoaiThoiGian")}
              onChange={onChange("loaiThoiGian")}
              value={_state.loaiThoiGian}
              data={listLoaiThoiGianMemo}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("baoCao.tuNgay")} <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              showTime={{ defaultValue: moment().startOf("day") }}
              value={_state.tuNgay}
              onChange={onChange("tuNgay")}
              placeholder={t("baoCao.chonNgay")}
              format="DD/MM/YYYY HH:mm:ss"
              className="input-filter"
            />
            {!_state.isValidData && !_state.tuNgay && (
              <div className="error">{t("baoCao.chonTuNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("baoCao.denNgay")} <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              showTime={{ defaultValue: moment().endOf("day") }}
              value={_state.denNgay}
              onChange={onChange("denNgay")}
              placeholder={t("baoCao.chonNgay")}
              format="DD/MM/YYYY HH:mm:ss"
              className="input-filter"
            />
            {!_state.isValidData && !_state.denNgay && (
              <div className="error">{t("baoCao.chonDenNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.khoaThucHien")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonKhoaThucHien")}
              onChange={handleChange("dsKhoaThucHienId", onChange)}
              value={_state.dsKhoaThucHienId}
              data={listAllKhoa}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.phongThucHien")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonPhongThucHien")}
              onChange={onChange("dsPhongThucHienId")}
              value={_state.dsPhongThucHienId}
              data={listAllPhong}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.khoaChiDinh")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonKhoaChiDinh")}
              onChange={handleChange("dsKhoaChiDinhId", onChange)}
              value={_state.dsKhoaChiDinhId}
              data={listAllKhoa}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.nhaThuNgan")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonNhaThuNgan")}
              onChange={onChange("dsNhaThuNganId")}
              value={_state.dsNhaThuNganId}
              data={listAllToaNha}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.loaiDoiTuongKCB")}
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonLoaiDoiTuongKCB")}
              onChange={onChange("dsDoiTuongKcb")}
              value={_state.dsDoiTuongKcb}
              data={listDoiTuongKcb}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.doiTuong")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonDoiTuong")}
              onChange={onChange("doiTuong")}
              value={_state.doiTuong}
              data={listDoiTuong}
              hasAllOption
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.nhomDichVuCap1")}</label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonNhomDichVuCap1")}
              onChange={handleChange("dsNhomDichVuCap1Id", onChange)}
              value={_state.dsNhomDichVuCap1Id}
              data={listAllNhomDichVuCap1}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.nhomDichVuCap2")}</label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonNhomDichVuCap2")}
              onChange={handleChange("dsNhomDichVuCap2Id", onChange)}
              value={_state.dsNhomDichVuCap2Id}
              data={listAllNhomDichVuCap2}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.nhomDichVuCap3")}</label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonNhomDichVuCap3")}
              onChange={handleChange("dsNhomDichVuCap3Id", onChange)}
              value={_state.dsNhomDichVuCap3Id}
              data={listAllNhomDichVuCap3}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.tenDichVu")}</label>
            <SelectLoadMore
              api={dichVuProvider.searchAll}
              mapData={(i) => ({
                value: i.id,
                label: i.ten,
              })}
              addParam={{
                dsNhomDichVuCap2Id: _state.dsNhomDichVuCap2Id,
                dsNhomDichVuCap3Id: _state.dsNhomDichVuCap3Id,
              }}
              onChange={onChange("dsDichVuId", true)}
              value={_state.dsDichVuId}
              keySearch={"ten"}
              placeholder={t("baoCao.chonDichVu")}
              className="input-filter"
              blurReset={true}
              mode="multiple"
              hasAll={true}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.nhomBaoCaoPhuCapPttt")}
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonNhomBaoCaoPhuCapPttt")}
              onChange={onChange("dsLoaiPhuCapPtTt")}
              value={_state.dsLoaiPhuCapPtTt}
              data={listLoaiPhuCapPtTt}
              hasAllOption
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("common.trangThaiHoan")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonTrangThaiHoan")}
              onChange={handleChange("dsTrangThaiHoan", onChange)}
              value={_state.dsTrangThaiHoan}
              data={listTrangThaiHoan}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.trangThaiThanhToan")}
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonTrangThaiThanhToan")}
              onChange={onChange("thanhToan")}
              value={_state.thanhToan}
              data={TRANG_THAI_THANH_TOAN_PHIEU_THU}
              hasAllOption
            />
          </div>
        </Col>
      </Row>
    );
  };

  const handleDataSearch = ({ _state }) => {
    const getFilteredList = (selected, all) =>
      selected?.length !== all?.length ? selected : null;

    return {
      tuThoiGian: moment(_state.tuNgay).format("DD-MM-YYYY HH:mm:ss"),
      denThoiGian: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
      dsKhoaThucHienId: getFilteredList(_state.dsKhoaThucHienId, listAllKhoa),
      dsPhongThucHienId: getFilteredList(
        _state.dsPhongThucHienId,
        listAllPhong
      ),
      dsKhoaChiDinhId: getFilteredList(_state.dsKhoaChiDinhId, listAllKhoa),
      dsNhaThuNganId: getFilteredList(_state.dsNhaThuNganId, listAllToaNha),
      dsDoiTuongKcb: _state.dsDoiTuongKcb,
      doiTuong: _state.doiTuong,
      dsNhomDichVuCap1Id: _state.dsNhomDichVuCap1Id,
      dsNhomDichVuCap2Id: _state.dsNhomDichVuCap2Id,
      dsNhomDichVuCap3Id: _state.dsNhomDichVuCap3Id,
      dsDichVuId: _state.dsDichVuId,
      dsLoaiPhuCapPtTt: _state.dsLoaiPhuCapPtTt,
      loaiThoiGian: _state.loaiThoiGian,
      thanhToan: _state?.thanhToan,
      dsTrangThaiHoan: _state.dsTrangThaiHoan,
    };
  };

  return (
    <BaseBaoCao
      title={t("baoCao.pttt04_9")}
      renderFilter={renderFilter}
      handleDataSearch={handleDataSearch}
      breadcrumb={[{ title: "PTTT04.9", link: "/bao-cao/pttt04_9" }]}
      getBc={getPttt04_9}
      initState={{
        loaiThoiGian: LOAI_THOI_GIAN.THEO_THOI_GIAN_THANH_TOAN,
        dsKhoaThucHienId: [""],
        dsPhongThucHienId: [""],
        dsKhoaChiDinhId: khoaChiDinh
          ? khoaChiDinh
          : listAllKhoaTheoTaiKhoan?.map((item) => item.id),
        dsDoiTuongKcb: [""],
        dsNhomDichVuCap1Id: [""],
        dsNhomDichVuCap2Id: [""],
        dsNhomDichVuCap3Id: [""],
        dsDichVuId: [""],
        dsLoaiPhuCapPtTt: [""],
        thanhToan: [""],
        dsTrangThaiHoan: trangThaiHoan ? trangThaiHoan : [""],
      }}
    />
  );
};

export default Index;
