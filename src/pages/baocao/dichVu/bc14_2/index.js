import React, { useMemo, useEffect } from "react";
import { Col, message, Row } from "antd";
import moment from "moment";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { useEnum, useListAll, useStore } from "hooks";
import { DateTimePicker, Select } from "components";
import BaseBaoCao from "pages/baocao/BaseBaoCao";
import { Main, GlobalStyle } from "./styled";
import { TRANG_THAI_THANH_TOAN_PHIEU_THU, ENUM, ROLES } from "constants/index";
import { selectMaTen } from "redux-store/selectors";
import { checkRole } from "lib-utils/role-utils";

const BC14_2 = () => {
  const { t } = useTranslation();
  const [listPhanLoaiPTTT] = useEnum(ENUM.PHAN_LOAI_PTTT);
  const [listAllKhoa] = useListAll("khoa", {}, true);
  const [listAllKhoaTheoTaiKhoan] = useListAll(
    "khoa",
    {},
    true,
    "KhoaTheoTaiKhoan"
  );
  const listAllNhomDichVuCap1 = useStore(
    "nhomDichVuCap1.listAllNhomDichVuCap1",
    []
  );
  const listPhong = useStore("phong.listPhong", []);

  const isHienThiTatCaKhoaChiDinh = checkRole([
    ROLES["BAO_CAO"].HIEN_THI_TAT_CA_KHOA_CHI_DINH,
  ]);
  const isHienThiTatCaKhoaThucHien = checkRole([
    ROLES["BAO_CAO"].HIEN_THI_TAT_CA_KHOA_THUC_HIEN,
  ]);

  const {
    baoCaoDaIn: { getBc14_2 },
    nhomDichVuCap1: { getAllTongHopDichVuCap1 },
    phong: { getListPhongTongHop },
  } = useDispatch();

  const listAllPhanLoaiPTTT = useMemo(() => {
    return listPhanLoaiPTTT
      .filter((item) => item.id !== 5)
      .map((item) => ({ ...item, id: item.id + "" }));
  }, [listPhanLoaiPTTT]);

  const dsKhoaChiDinh = useMemo(
    () =>
      isHienThiTatCaKhoaChiDinh ? listAllKhoa : listAllKhoaTheoTaiKhoan || [],
    [isHienThiTatCaKhoaChiDinh, listAllKhoa, listAllKhoaTheoTaiKhoan]
  );

  const dsKhoaThucHien = useMemo(
    () =>
      isHienThiTatCaKhoaThucHien ? listAllKhoa : listAllKhoaTheoTaiKhoan || [],
    [isHienThiTatCaKhoaThucHien, listAllKhoa, listAllKhoaTheoTaiKhoan]
  );

  useEffect(() => {
    getListPhongTongHop({ active: true, page: "", size: "" });
    getAllTongHopDichVuCap1({
      active: true,
      page: "",
      size: "",
      dsMaThietLap: ["MA_NHOM_DICH_VU_CAP1_PT", "MA_NHOM_DICH_VU_CAP1_TT"],
    });
  }, []);

  const renderFilter = ({ onChange, _state, onKeyDownDate }) => {
    return (
      <>
        <GlobalStyle />
        <Row>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-date">
              <label className="label-filter">
                {t("common.tuNgay")} <span className="icon-required">*</span>
              </label>
              <DateTimePicker
                className="input-filter"
                placeholder={t("common.chonNgay")}
                onChange={onChange("tuNgay")}
                value={_state.tuNgay}
                format="DD/MM/YYYY HH:mm:ss"
                onKeyDown={onKeyDownDate("tuNgay")}
                showTime={{ defaultValue: moment().startOf("day") }}
                disabledDate={(current) =>
                  current && _state.denNgay && current > _state.denNgay
                }
              />
              {!_state.isValidData && !_state.tuNgay && (
                <div className="error">{t("baoCao.chonTuNgay")}</div>
              )}
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-date">
              <label className="label-filter">
                {t("common.denNgay")} <span className="icon-required">*</span>
              </label>
              <DateTimePicker
                className="input-filter"
                placeholder={t("common.chonNgay")}
                onChange={onChange("denNgay")}
                value={_state.denNgay}
                format="DD/MM/YYYY HH:mm:ss"
                onKeyDown={onKeyDownDate("denNgay")}
                showTime={{ defaultValue: moment().endOf("day") }}
                disabledDate={(current) =>
                  current && _state.tuNgay && current < _state.tuNgay
                }
              />
              {!_state.isValidData && !_state.denNgay && (
                <div className="error">{t("baoCao.chonDenNgay")}</div>
              )}
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.khoaThucHien")}</label>
              <Select
                className="select input-filter"
                placeholder={t("baoCao.chonKhoa")}
                onChange={(e) => {
                  const value = onChange("dsKhoaThucHienId", true)(e);
                  getListPhongTongHop({
                    active: true,
                    page: "",
                    size: "",
                    dsKhoaId: value,
                    dsLoaiPhong: 30
                  });
                  onChange("dsPhongThucHienId", true)();
                }}
                value={_state.dsKhoaThucHienId}
                data={dsKhoaThucHien}
                getLabel={selectMaTen}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.khoaChiDinh")}</label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonKhoaChiDinh")}
                onChange={onChange("dsKhoaChiDinhId")}
                data={dsKhoaChiDinh}
                getLabel={selectMaTen}
                value={_state.dsKhoaChiDinhId}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">
                {t("baoCao.phanLoaiPttt")}
                <span className="icon-required">*</span>
              </label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonPhanLoaiPttt")}
                onChange={onChange("dsPhanLoaiPttt")}
                data={listAllPhanLoaiPTTT}
                value={_state.dsPhanLoaiPttt}
                mode="multiple"
              />
              {!_state.isValidData && !_state.dsPhanLoaiPttt?.length && (
                <div className="error">
                  {t("baoCao.vuiLongNhapPhanLoaiPttt")}
                </div>
              )}
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">
                {t("baoCao.trangThaiThanhToan")}
              </label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonTrangThaiThanhToan")}
                onChange={onChange("thanhToan")}
                data={TRANG_THAI_THANH_TOAN_PHIEU_THU}
                value={_state.thanhToan}
                hasAllOption
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.loaiBaoCao")}</label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonLoaiBaoCao")}
                onChange={onChange("dsNhomDichVuCap1Id")}
                value={_state.dsNhomDichVuCap1Id}
                data={listAllNhomDichVuCap1}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("danhMuc.khoaQuanLy")}</label>
              <Select
                className="input-filter"
                placeholder={t("khamSucKhoe.chonKhoaQuanLy")}
                onChange={onChange("dsKhoaQuanLyId")}
                value={_state.dsKhoaQuanLyId}
                data={listAllKhoaTheoTaiKhoan}
                getLabel={selectMaTen}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.phongKhamThucHien")}</label>
              <Select
                mode="multiple"
                onChange={onChange("dsPhongThucHienId")}
                value={_state.dsPhongThucHienId}
                className="input-filter"
                placeholder={t("baoCao.chonPhong")}
                data={listPhong}
              />
            </div>
          </Col>
        </Row>
      </>
    );
  };

  const handleDataSearch = ({ _state }) => {
    const filterList = (list, fullList) =>
      list?.length !== fullList?.length ? list : null;

    return {
      tuThoiGian: moment(_state.tuNgay).format("DD-MM-YYYY HH:mm:ss"),
      denThoiGian: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
      dsKhoaChiDinhId: filterList(_state.dsKhoaChiDinhId, listAllKhoa),
      dsKhoaThucHienId: filterList(_state.dsKhoaThucHienId, listAllKhoa),
      thanhToan: _state.thanhToan,
      dsPhanLoaiPttt:
        !_state.dsPhanLoaiPttt || _state.dsPhanLoaiPttt.includes("")
          ? listAllPhanLoaiPTTT.map((x) => x.id)
          : _state.dsPhanLoaiPttt,
      dsNhomDichVuCap1Id: filterList(
        _state.dsNhomDichVuCap1Id,
        listAllNhomDichVuCap1
      ),
      dsKhoaQuanLyId: _state.dsKhoaQuanLyId,
      dsPhongThucHienId: _state.dsPhongThucHienId
    };
  };

  const beforeOk =
    ({ _state, _beforeOk }) =>
      () => {
        if (!_state.tuNgay) {
          message.error(t("baoCao.chonTuNgay"));
          return false;
        }
        if (!_state.denNgay) {
          message.error(t("baoCao.chonDenNgay"));
          return false;
        }
        if (!_state.dsPhanLoaiPttt?.length) {
          message.error(t("baoCao.vuiLongNhapPhanLoaiPttt"));
          return false;
        }
        return _beforeOk();
      };

  return (
    <Main>
      <BaseBaoCao
        title={t("baoCao.bc14_2")}
        renderFilter={renderFilter}
        getBc={getBc14_2}
        handleDataSearch={handleDataSearch}
        beforeOk={beforeOk}
        initState={{
          dsKhoaChiDinhId: [""],
          dsKhoaThucHienId: [""],
          thanhToan: "",
          dsPhanLoaiPttt: [""],
          dsNhomDichVuCap1Id: [""],
          dsPhongThucHienId: [""]
        }}
        breadcrumb={[{ title: "BC14.2", link: "/bao-cao/bc14_2" }]}
      />
    </Main>
  );
};

export default BC14_2;
