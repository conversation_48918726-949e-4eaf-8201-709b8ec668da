import React, { useEffect, useMemo } from "react";
import { Col, message, Row } from "antd";
import moment from "moment";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import BaseBaoCao from "pages/baocao/BaseBaoCao";
import { Select, DateTimePicker, Checkbox, SelectLoadMore } from "components";
import { useCache, useStore, useEnum } from "hooks";
import { CACHE_KEY, DS_TINH_CHAT_KHOA, ENUM, LOAI_DICH_VU } from "constants/index";
import dmDvKyThuatProvider from "data-access/categories/dm-dv-ky-thuat-provider";

/**
 * BC21. Báo cáo thống kê suất ăn
 *
 */

const BC21 = () => {
  const { t } = useTranslation();
  const listAllKhoaNoiTru = useStore("khoa.listDataTongHop", []);
  const listAllLoaiBuaAn = useStore("loaiBuaAn.listAllLoaiBuaAn", []);
  const [cacheSapXepTheoPhong, setCacheSapXepTheoPhong] = useCache(
    "",
    CACHE_KEY.SAP_XEP_THEO_PHONG_BC21,
    false,
    false
  );

  const {
    baoCaoDaIn: { getBc21 },
    khoa: { getListKhoaTongHop },
    loaiBuaAn: { getListAllLoaiBuaAn },
  } = useDispatch();

  const [listLoaiThoiGian] = useEnum(ENUM.LOAI_THOI_GIAN);
  const listAllKhoaNoiTruCustom = useMemo(() => {
    return listAllKhoaNoiTru.map((item) => ({
      ...item,
      ten: `${item.ma} - ${item.ten}`,
    }));
  }, [listAllKhoaNoiTru]);

  useEffect(() => {
    let param = { active: true, page: "", size: "" };
    getListAllLoaiBuaAn(param);
    getListKhoaTongHop({
      page: "",
      size: "",
      active: true,
      dsTinhChatKhoa: DS_TINH_CHAT_KHOA.NOI_TRU,
    });
  }, []);

  const listLoaiThoiGianMemo = useMemo(() => {
    return listLoaiThoiGian.filter((loaiThoiGian) =>
      [30, 40, 70].some((item) => item === loaiThoiGian.id)
    );
  }, [listLoaiThoiGian]);

  const renderFilter = ({ onChange, _state }) => {
    return (
      <Row>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("baoCao.tuNgay")}
              <span className="icon-required"> *</span>
            </label>
            <DateTimePicker
              showTime={{ defaultValue: moment().startOf("day") }}
              value={_state.tuNgay}
              onChange={onChange("tuNgay")}
              placeholder={t("baoCao.chonNgay")}
              format="DD/MM/YYYY HH:mm:ss"
              className="input-filter"
            />
            {!_state.isValidData && !_state.tuNgay && (
              <div className="error">{t("baoCao.chonTuNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("baoCao.denNgay")}
              <span className="icon-required"> *</span>
            </label>
            <DateTimePicker
              showTime={{ defaultValue: moment().endOf("day") }}
              value={_state.denNgay}
              onChange={onChange("denNgay")}
              placeholder={t("baoCao.chonNgay")}
              format="DD/MM/YYYY HH:mm:ss"
              className="input-filter"
            />
            {!_state.isValidData && !_state.denNgay && (
              <div className="error">{t("baoCao.chonDenNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("common.khoa")}</label>
            <Select
              onChange={onChange("dsKhoaChiDinhId", true)}
              className="select input-filter"
              value={_state.dsKhoaChiDinhId}
              placeholder={t("baoCao.chonKhoa")}
              data={listAllKhoaNoiTruCustom}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("danhMuc.loaiBuaAn")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonLoaiBuaAn")}
              onChange={onChange("dsLoaiBuaAnId", true)}
              value={_state.dsLoaiBuaAnId}
              defaultValue={_state.dsLoaiBuaAnId}
              data={listAllLoaiBuaAn}
              mode="multiple"
              showArrow
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("quanLyNoiTru.suatAn.loaiSuatAn")}</label>
            <SelectLoadMore
              className="input-filter"
              placeholder={t("quanLyNoiTru.chonLoaiSuatAn")}
              onChange={onChange("dsDichVuId", true)}
              value={_state.dsDichVuId}
              api={dmDvKyThuatProvider.searchAll}
              mapData={(i) => ({
                value: i.id,
                label: i.ma,
              })}
              keySearch={"ma"}
              addParam={{
                loaiDichVu: LOAI_DICH_VU.SUAT_AN,
              }}
              blurReset={true}
              mode="multiple"
              hasAll={true}
              showArrow
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter pointer">
              {t("baoCao.loaiThoiGian")}
            </label>
            <Select
              onChange={onChange("loaiThoiGian")}
              value={_state.loaiThoiGian}
              className="input-filter"
              placeholder={t("baoCao.chonLoaiThoiGian")}
              data={listLoaiThoiGianMemo}
            />
          </div>
        </Col>

        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.sapXepTenPhong}
              onChange={(e) => {
                setCacheSapXepTheoPhong(e.target.checked, false);
                onChange("sapXepTenPhong")(e);
              }}
            >
              {t("baoCao.sapXepTheoPhong")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.hienThiGioAn}
              onChange={(e) => {
                setCacheSapXepTheoPhong(e.target.checked, false);
                onChange("hienThiGioAn")(e);
              }}
            >
              {t("baoCao.hienThiGioAn")}
            </Checkbox>
          </div>
        </Col>
      </Row>
    );
  };

  const handleDataSearch = ({ _state }) => ({
    tuThoiGian: moment(_state.tuNgay).format("DD-MM-YYYY HH:mm:ss"),
    denThoiGian: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
    dsKhoaChiDinhId: _state.dsKhoaChiDinhId,
    dsLoaiBuaAnId: _state.dsLoaiBuaAnId,
    dsDichVuId: _state.dsDichVuId,
    sapXepTenPhong: _state.sapXepTenPhong,
    hienThiGioAn: _state.hienThiGioAn,
    loaiThoiGian: _state.loaiThoiGian
  });

  const beforeOk =
    ({ _state, _beforeOk }) =>
      () => {
        if (!_state.tuNgay) {
          message.error(t("baoCao.chonTuNgay"));
          return false;
        }
        if (!_state.denNgay) {
          message.error(t("baoCao.chonDenNgay"));
          return false;
        }
        return _beforeOk();
      };

  return (
    <BaseBaoCao
      title={t("baoCao.bc21")}
      renderFilter={renderFilter}
      getBc={getBc21}
      initState={{
        dsLoaiBuaAnId: [""],
        dsKhoaChiDinhId: [""],
        dsDichVuId: [""],
        sapXepTenPhong: cacheSapXepTheoPhong,
        loaiThoiGian: 40,
        hienThiGioAn: true
      }}
      handleDataSearch={handleDataSearch}
      beforeOk={beforeOk}
      breadcrumb={[{ title: "BC21", link: "/bao-cao/bc21" }]}
    />
  );
};

export default BC21;
