import React, { useState, useEffect, useMemo } from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { Col, message, Row } from "antd";
import { Select, SelectLoadMore, Checkbox } from "components";
import moment from "moment";
import BaseBaoCao from "pages/baocao/BaseBaoCao";
import {
  useEnum,
  useStore,
  useListAll,
  useLazyKVMap,
  useCache,
  useThietLap,
  useQueryAll,
  useLazyMemo,
} from "hooks";
import dichVuProvider from "data-access/categories/dm-dich-vu-provider";
import {
  TRANG_THAI_THANH_TOAN_PHIEU_THU,
  THUC_HIEN_DICH_VU,
  SO_LUONG_DICH_VU,
  YES_NO,
  ENUM,
  LOAI_DOI_TAC,
  THIET_LAP_CHUNG,
  PHAN_LOAI_DOI_TUONG_KCB,
  TRANG_THAI_HOAN,
  LOAI_DICH_VU,
  LOAI_THOI_GIAN,
  LOAI_PHONG,
  CACHE_KEY,
  ROLES,
} from "constants/index";
import { selectMaTen } from "redux-store/selectors";
import { LIST_LOAI_PHIEU_THU_CUSTOM } from "../../utils";
import { concatString, isArray, openInNewTab } from "utils/index";
import { query } from "redux-store/stores";
import { Main } from "./styled";
import { ChonCoSoChiNhanh, FilterThoiGian } from "../../BaseBaoCao/components";
import { checkRole } from "lib-utils/role-utils";
import nbDotDieuTriProvider from "data-access/nb-dot-dieu-tri-provider";
/**
 * BC10. Báo cáo chi tiết dịch vụ
 *
 */

const LIST_TRANG_THAI_THUOC = [
  {
    id: true,
    i18n: "common.daPhat",
  },
  {
    id: false,
    i18n: "common.chuaPhat",
  },
];
const params = { page: "", size: "", active: true };

const Index = () => {
  const [state, _setState] = useState({
    addParamDv: {},
    dsKhoaThucHienId: null,
  });
  const setState = (data) => {
    _setState((pre) => ({ ...pre, ...data }));
  };
  const { t } = useTranslation();
  const {
    phong: { getListPhongTongHop },
    baoCaoDaIn: { getBc10 },
    dichVu: { getAllDichVu },
    loaiHinhThanhToan: { getListAllLoaiHinhThanhToan },
    doiTac: { getListTongHop: getListTongHopDoiTac },
  } = useDispatch();

  const [khoaChiDinh, setKhoaChiDinh, loadFinish] = useCache(
    "",
    CACHE_KEY.KHOA_CHI_DINH_BC10,
    null,
    false
  );
  const [trangThaiHoan, setTrangThaiHoan] = useCache(
    "",
    CACHE_KEY.DATA_TRANG_THAI_HOAN_BC10,
    [TRANG_THAI_HOAN.THUONG],
    false
  );

  const khoaChiDinhDefault = useLazyMemo(
    (prev) => {
      return prev ? prev : khoaChiDinh;
    },
    [khoaChiDinh]
  );

  const [listLoaiThoiGian] = useEnum(ENUM.LOAI_THOI_GIAN, []);
  const [listDoiTuong] = useEnum(ENUM.DOI_TUONG, []);
  const [listTrangThaiHoan] = useEnum(ENUM.TRANG_THAI_HOAN);
  const [listLoaiDichVu] = useEnum(ENUM.LOAI_DICH_VU);
  const [listAllNhomDichVuCap1] = useListAll("nhomDichVuCap1", {}, true);
  const [listAllNhomDichVuCap2] = useListAll("nhomDichVuCap2", {}, true);
  const [listAllNhomDichVuCap3] = useListAll("nhomDichVuCap3", {}, true);
  const [listAllLoaiDoiTuong] = useListAll("loaiDoiTuong", {}, true);
  const [listAllNhanVien] = useListAll("nhanVien", {}, true, "NhanVien");
  const [listAllBacSi] = useListAll(
    "nhanVien",
    {
      dsMaThietLapVanBang: [THIET_LAP_CHUNG.BAC_SI],
    },
    true,
    "BacSi"
  );

  const [dataMA_THU_NGAN, finishGetMaThuNgan] = useThietLap(
    THIET_LAP_CHUNG.MA_THU_NGAN
  );

  const { data: listThuNgan } = useQueryAll(
    query.nhanVien.queryAllNhanVien({
      params: {
        dsMaVaiTro: dataMA_THU_NGAN,
      },
      enabled: finishGetMaThuNgan,
    })
  );

  const [listAllKho] = useListAll(
    "kho",
    { dsLoaiDichVu: LOAI_DICH_VU.THUOC },
    true
  );

  const { data: listAllPhong } = useQueryAll(
    query.phong.queryAllPhong({
      params: {
        dsKhoaId: state.dsKhoaThucHienId,
      },
    })
  );
  const { data: listAllKhoaTheoTaiKhoan, finishGetListKhoaTheoTaiKhoan } =
    useQueryAll(query.khoa.queryKhoaTheoTaiKhoan);

  const [dsCoSoKcbId, setDsCoSoKcbId] = useState([]);
  const { data: listAllKhoa } = useQueryAll(
    query.khoa.queryAllKhoa({
      params: {
        dsCoSoKcbId,
      },
    })
  );
  const { data: listAllToaNha } = useQueryAll(query.toaNha.queryAllToaNha);

  const listAllKhoaTheoTaiKhoanId = useMemo(() => {
    return listAllKhoaTheoTaiKhoan.map((item) => item.id);
  }, [listAllKhoaTheoTaiKhoan]);

  const { data: listPhongTheoKhoa } = useQueryAll(
    query.phong.queryAllPhong({
      params: {
        dsLoaiPhong: [
          LOAI_PHONG.CAN_LAM_SANG,
          LOAI_PHONG.PHONG_KHAM,
          LOAI_PHONG.PHONG_KHAM_TIEM_CHUNG,
        ],
        dsKhoaId: khoaChiDinh ? khoaChiDinh : listAllKhoaTheoTaiKhoanId,
      },
      enabled:
        finishGetListKhoaTheoTaiKhoan &&
        loadFinish &&
        isArray(listAllKhoaTheoTaiKhoanId, 1),
    })
  );

  const [getPhanLoaiDoiTuongKcb] = useLazyKVMap(PHAN_LOAI_DOI_TUONG_KCB);
  const [getListLoaiPhieuThu] = useLazyKVMap(LIST_LOAI_PHIEU_THU_CUSTOM);
  const listDataTongHopDoiTac = useStore("doiTac.listDataTongHop", []);
  const listAllLoaiHinhThanhToan = useStore(
    "loaiHinhThanhToan.listAllLoaiHinhThanhToan",
    []
  );
  const auth = useStore("auth.auth", {});
  const isMacDinhKhoaChiDinh = checkRole([
    ROLES["BAO_CAO"].MAC_DINH_KHOA_CHI_DINH,
  ]);
  const isMacDinhKhoaThucHien = checkRole([
    ROLES["BAO_CAO"].MAC_DINH_KHOA_THUC_HIEN,
  ]);
  const isHienThiTatCaKhoaChiDinh = checkRole([
    ROLES["BAO_CAO"].HIEN_THI_TAT_CA_KHOA_CHI_DINH,
  ]);
  const isHienThiTatCaKhoaThucHien = checkRole([
    ROLES["BAO_CAO"].HIEN_THI_TAT_CA_KHOA_THUC_HIEN,
  ]);

  const listLoaiDoiTuongCustom = useMemo(() => {
    return (listAllLoaiDoiTuong || []).map((item) => ({
      id: item?.id,
      ten: `${item?.ma} - ${item?.ten}`,
    }));
  }, [listAllLoaiDoiTuong]);

  useEffect(() => {
    getListPhongTongHop(params);
    getAllDichVu(params);
    getListAllLoaiHinhThanhToan(params);
    getListTongHopDoiTac({
      ...params,
      dsLoaiDoiTac: LOAI_DOI_TAC.CONG_TY_KSK,
    });
  }, []);

  const listDataCongTy = useMemo(() => {
    return (listDataTongHopDoiTac || []).map((item) => ({
      id: item?.id,
      ten: `${item?.ma} - ${item?.ten}`,
    }));
  }, [listDataTongHopDoiTac]);

  const listLoaiThoiGianMemo = useMemo(() => {
    return listLoaiThoiGian.filter((loaiThoiGian) =>
      [
        LOAI_THOI_GIAN.THE0_THOI_GIAO_VAO_VIEN,
        LOAI_THOI_GIAN.THEO_THOI_GIAN_CHI_DINH,
        LOAI_THOI_GIAN.THEO_THOI_GIAN_THUC_HIEN,
        LOAI_THOI_GIAN.THEO_THOI_GIAN_THANH_TOAN,
        LOAI_THOI_GIAN.THEO_THOI_GIAO_RA_VIEN,
        LOAI_THOI_GIAN.THEO_THOI_GIAN_CO_KET_QUA,
        LOAI_THOI_GIAN.THEO_THOI_GIAN_TIEP_NHAN,
      ].some((item) => item === loaiThoiGian.id)
    );
  }, [listLoaiThoiGian]);

  const dsKhoaChiDinh = useMemo(
    () =>
      isHienThiTatCaKhoaChiDinh ? listAllKhoa : listAllKhoaTheoTaiKhoan || [],
    [isHienThiTatCaKhoaChiDinh, listAllKhoa, listAllKhoaTheoTaiKhoan]
  );

  const dsKhoaThucHien = useMemo(
    () =>
      isHienThiTatCaKhoaThucHien ? listAllKhoa : listAllKhoaTheoTaiKhoan || [],
    [isHienThiTatCaKhoaThucHien, listAllKhoa, listAllKhoaTheoTaiKhoan]
  );

  const handleChange = (key, onChange, _state) => (value) => {
    if (key === "dsNhomDichVuCap1Id") {
      setState({
        addParamDv: { ...state.addParamDv, dsNhomDichVuCap1Id: value },
      });
    }
    if (key === "dsKhoaThucHienId") {
      setState({ dsKhoaThucHienId: value });
      onChange("dsPhongThucHienId")();
    }
    if (key === "khoaChiDinhId") {
      onChange("dsPhongChiDinhId")([""]);
      setKhoaChiDinh(value);
    }
    if (key === "dsTrangThaiHoan") {
      setTrangThaiHoan(value, false);
    }
    onChange(key)(value);
  };

  const renderFilter = ({ onChange, _state, onKeyDownDate }) => {
    return (
      <Row>
        <Col span={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.theoThoiGian")}
              <span className="icon-required">*</span>
            </label>
            <Select
              className="input-filter"
              placeholder={t("common.chonThoiGian")}
              data={listLoaiThoiGianMemo}
              onChange={onChange("loaiThoiGian")}
              value={_state.loaiThoiGian}
            />
            {!_state.isValidData && !_state.loaiThoiGian && (
              <div className="error">
                {t("baoCao.vuiLongChonThoiGianThanhToan")}
              </div>
            )}
          </div>
        </Col>
        <FilterThoiGian
          t={t}
          onChange={onChange}
          _state={_state}
          onKeyDownDate={onKeyDownDate}
        />
        <ChonCoSoChiNhanh
          onChange={onChange}
          _state={_state}
          keyUpdate={["dsKhoaThucHienId", "dsPhongThucHienId"]}
          setDsCoSoKcbId={setDsCoSoKcbId}
        />
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.thuNgan")}</label>
            <Select
              onChange={onChange("dsThuNganId", true)}
              value={_state.dsThuNganId}
              className="input-filter"
              placeholder={t("baoCao.chonThuNgan")}
              data={listThuNgan}
              getLabel={(item) =>
                `${item.taiKhoan || ""} - ${item.ma} - ${item.ten}`
              }
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.loaiHinhThanhToan")}
            </label>
            <Select
              onChange={onChange("dsLoaiHinhThanhToanId", true)}
              value={_state.dsLoaiHinhThanhToanId}
              className="select input-filter"
              placeholder={t("baoCao.chonLoaiHinhThanhToan")}
              data={listAllLoaiHinhThanhToan}
              mode={"multiple"}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.doiTuongNguoiBenh")}
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonDoiTuongNguoiBenh")}
              data={listDoiTuong}
              hasAllOption
              onChange={onChange("doiTuong")}
              value={_state.doiTuong}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.doiTuongKcb")}</label>
            <Select
              onChange={onChange("dsDoiTuongKcb")}
              value={_state.dsDoiTuongKcb}
              className="input-filter"
              mode="multiple"
              placeholder={t("baoCao.chonDoiTuongKcb")}
              data={PHAN_LOAI_DOI_TUONG_KCB}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.loaiDoiTuong")}</label>
            <Select
              className="input-filter"
              placeholder={concatString(
                t("common.chon"),
                t("baoCao.loaiDoiTuong")
              )}
              data={listLoaiDoiTuongCustom}
              hasAllOption
              onChange={onChange("loaiDoiTuongId")}
              value={_state.loaiDoiTuongId}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.trangThaiThanhToan")}
            </label>
            <Select
              onChange={onChange("thanhToan")}
              value={_state.thanhToan}
              className="select input-filter"
              placeholder={t("baoCao.chonTrangThaiThanhToan")}
              data={TRANG_THAI_THANH_TOAN_PHIEU_THU}
              hasAllOption
            />
          </div>
        </Col>

        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.trangThaiDichVu")}
            </label>
            <Select
              onChange={onChange("thucHienDichVu")}
              value={_state.thucHienDichVu}
              className="select input-filter"
              placeholder={concatString(
                t("common.chon"),
                t("baoCao.trangThaiDichVu")
              )}
              hasAllOption
              data={THUC_HIEN_DICH_VU}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.soLuongDichVu")}</label>
            <Select
              onChange={onChange("dichVuHoan")}
              value={_state.dichVuHoan}
              className="select input-filter"
              placeholder={concatString(
                t("common.chon"),
                t("baoCao.soLuongDichVu")
              )}
              data={SO_LUONG_DICH_VU}
              hasAllOption={true}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.khoaChiDinh")}</label>
            <Select
              onChange={handleChange("khoaChiDinhId", onChange, _state)}
              className="input-filter"
              value={_state.khoaChiDinhId}
              placeholder={t("baoCao.chonKhoaChiDinh")}
              data={dsKhoaChiDinh}
              getLabel={selectMaTen}
              disabled={isMacDinhKhoaChiDinh}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label
              className="label-filter cursor-pointer"
              onClick={() => openInNewTab("/danh-muc/phong")}
            >
              {t("baoCao.phongChiDinh")}
            </label>
            <Select
              mode="multiple"
              onChange={onChange("dsPhongChiDinhId", true)}
              value={_state.dsPhongChiDinhId}
              defaultValue={_state.dsPhongChiDinhId}
              className="input-filter"
              placeholder={t("baoCao.chonPhong")}
              data={listPhongTheoKhoa}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.khoaThucHien")}</label>
            <Select
              onChange={handleChange("dsKhoaThucHienId", onChange, _state)}
              value={_state.dsKhoaThucHienId}
              className={`input-filter ${
                isMacDinhKhoaThucHien ? "default-khoa" : ""
              }`}
              placeholder={t("baoCao.chonKhoaThucHien")}
              data={dsKhoaThucHien}
              getLabel={selectMaTen}
              mode="multiple"
              disabled={isMacDinhKhoaThucHien}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.phongThucHien")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonPhongThucHien")}
              data={listAllPhong}
              onChange={onChange("dsPhongThucHienId", true)}
              value={_state.dsPhongThucHienId}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.nguoiChiDinh")}</label>
            <Select
              onChange={onChange("dsBacSiChiDinhId", true)}
              value={_state.dsBacSiChiDinhId}
              className="input-filter"
              placeholder={t("baoCao.chonNguoiChiDinh")}
              data={listAllNhanVien}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.bacSiThucHien")}</label>
            <Select
              onChange={onChange("dsBacSiThucHienId", true)}
              value={_state.dsBacSiThucHienId}
              className="input-filter"
              placeholder={concatString(
                t("common.chon"),
                t("baoCao.bacSiThucHien")
              )}
              data={listAllBacSi}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.theoYeuCau")}</label>
            <Select
              onChange={onChange("theoYeuCau")}
              value={_state.theoYeuCau}
              className="input-filter"
              placeholder={t("baoCao.chonYeuCau")}
              data={YES_NO}
              hasAllOption
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.nhomDichVuCap1")}</label>
            <Select
              onChange={handleChange("dsNhomDichVuCap1Id", onChange, _state)}
              value={_state.dsNhomDichVuCap1Id}
              className="input-filter"
              placeholder={t("danhMuc.chonNhomDichVuCap1")}
              data={listAllNhomDichVuCap1}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.nhomDichVuCap2")}</label>
            <Select
              onChange={onChange("dsNhomDichVuCap2Id", true)}
              value={_state.dsNhomDichVuCap2Id}
              className="input-filter"
              placeholder={t("danhMuc.chonNhomDichVuCap2")}
              data={listAllNhomDichVuCap2}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.nhomDichVuCap3")}</label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonNhomDichVuCap3")}
              onChange={onChange("dsNhomDichVuCap3Id", true)}
              value={_state.dsNhomDichVuCap3Id}
              data={listAllNhomDichVuCap3}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.tenDichVu")}</label>
            <SelectLoadMore
              api={dichVuProvider.searchAll}
              mapData={(i) => ({
                value: i.id,
                label: `${i.ma}-${i.ten}`,
              })}
              onChange={onChange("dsDichVuId", true)}
              value={_state.dsDichVuId}
              keySearch={"timKiem"}
              placeholder={t("baoCao.chonDichVu")}
              className="input-filter"
              addParam={state.addParamDv}
              blurReset={true}
              mode="multiple"
              hasAll={true}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.locDichVuBaoHiem")}
            </label>
            <Select
              onChange={onChange("dichVuBaoHiem")}
              value={_state.dichVuBaoHiem}
              className="input-filter"
              placeholder={concatString(
                t("common.chon"),
                t("baoCao.locDichVuBaoHiem")
              )}
              data={YES_NO}
              hasAllOption
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.NBCapCuu")}</label>
            <Select
              onChange={onChange("capCuu")}
              value={_state.capCuu}
              className="input-filter"
              placeholder={t("baoCao.NBCapCuu")}
              data={YES_NO}
              hasAllOption
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.congTyKsk")}</label>
            <Select
              data={listDataCongTy}
              onChange={onChange("dsDoiTacId", true)}
              value={_state.dsDoiTacId}
              placeholder={t("baoCao.chonCongTyKsk")}
              className="input-filter"
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.nhaThuNgan")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonNhaThuNgan")}
              onChange={onChange("dsNhaThuNganId")}
              value={_state.dsNhaThuNganId}
              data={listAllToaNha}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.loaiPhieuThu")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonLoaiPhieuThu")}
              onChange={onChange("dsLoaiPhieuThu")}
              value={_state.dsLoaiPhieuThu}
              data={LIST_LOAI_PHIEU_THU_CUSTOM}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("common.trangThaiHoan")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonTrangThaiHoan")}
              onChange={handleChange("dsTrangThaiHoan", onChange)}
              value={_state.dsTrangThaiHoan}
              data={listTrangThaiHoan}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.khoThuoc")}</label>
            <Select
              className="input-filter"
              placeholder={concatString(t("common.chon"), t("baoCao.khoThuoc"))}
              onChange={onChange("dsKhoId")}
              value={_state.dsKhoId}
              data={listAllKho}
              getLabel={(item) => `${item.ma} - ${item.ten}`}
              hasAllOption={true}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("kho.trangThaiThuoc")}</label>
            <Select
              className="input-filter"
              placeholder={t("kho.chonTrangThaiThuoc")}
              onChange={onChange("daPhat")}
              value={_state.daPhat}
              data={LIST_TRANG_THAI_THUOC}
              hasAllOption={true}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.chiDinhTuLoai")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonChiDinhTuLoai")}
              onChange={onChange("dsChiDinhTuLoaiDichVu")}
              value={_state.dsChiDinhTuLoaiDichVu}
              data={listLoaiDichVu}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("common.tenNb")}</label>
            <SelectLoadMore
              api={(params) => {
                const { maHoSo, ...rest } = params || {};

                let searchParams = rest;

                if (/^[0-9]{10}$/.test(maHoSo)) {
                  searchParams.maHoSo = maHoSo;
                } else if (/^[a-zA-Z]+[0-9]+$/.test(maHoSo)) {
                  searchParams.maNb = maHoSo;
                } else {
                  searchParams.tenNb = maHoSo;
                }
                return nbDotDieuTriProvider.searchNBDotDieuTriTongHop(
                  searchParams
                );
              }}
              mapData={(i) => ({
                value: i.id,
                label: `${i.tenNb} - ${i.maHoSo}`,
              })}
              onChange={onChange("dsNbDotDieuTriId", true)}
              value={_state.dsNbDotDieuTriId}
              keySearch={"maHoSo"}
              placeholder={t("phcn.timTenNbMaHs")}
              className="input-filter"
              mode="multiple"
              blurReset={true}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox checked={_state.moRong} onChange={onChange("moRong")}>
              {t("common.moRong")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.khoaThucHien}
              onChange={onChange("khoaThucHien")}
            >
              {t("baoCao.theoKhoaThucHien")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.chiSoKetQua}
              onChange={onChange("chiSoKetQua")}
            >
              {t("baoCao.chiSoKetQua")}
            </Checkbox>
          </div>
        </Col>
      </Row>
    );
  };

  const handleDataSearch = ({ _state }) => ({
    loaiThoiGian: _state.loaiThoiGian,
    tuThoiGian: moment(_state.tuNgay).format("DD-MM-YYYY HH:mm:ss"),
    denThoiGian: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
    dsCoSoKcbId: _state.dsCoSoKcbId,
    doiTuong: _state.doiTuong,
    khoaChiDinhId: _state.khoaChiDinhId,
    dsNhomDichVuCap1Id: _state.dsNhomDichVuCap1Id,
    dsNhomDichVuCap2Id: _state.dsNhomDichVuCap2Id,
    dsNhomDichVuCap3Id: _state.dsNhomDichVuCap3Id,
    thanhToan: _state.thanhToan,
    dsBacSiChiDinhId: _state.dsBacSiChiDinhId,
    dsDichVuId: _state.dsDichVuId,
    thucHienDichVu: _state.thucHienDichVu,
    dichVuHoan: _state.dichVuHoan,
    dsBacSiThucHienId: _state.dsBacSiThucHienId,
    dsDoiTuongKcb:
      _state.dsDoiTuongKcb && _state.dsDoiTuongKcb[0] !== ""
        ? _state.dsDoiTuongKcb.flatMap(
            (id) => getPhanLoaiDoiTuongKcb(id)?.referIds
          )
        : null,
    dsKhoaThucHienId: _state.dsKhoaThucHienId,
    dsPhongThucHienId: _state.dsPhongThucHienId,
    loaiDoiTuongId: _state.loaiDoiTuongId,
    dsLoaiHinhThanhToanId: _state.dsLoaiHinhThanhToanId,
    theoYeuCau: _state.theoYeuCau,
    moRong: _state.moRong,
    dichVuBaoHiem: _state.dichVuBaoHiem,
    capCuu: _state.capCuu,
    dsDoiTacId: _state.dsDoiTacId,
    khoaThucHien: _state.khoaThucHien,
    dsNhaThuNganId: _state.dsNhaThuNganId,
    dsLoaiPhieuThu: _state.dsLoaiPhieuThu
      ? _state.dsLoaiPhieuThu.flatMap(
          (id) => getListLoaiPhieuThu(id)?.ids || []
        )
      : null,
    dsTrangThaiHoan:
      _state.dsTrangThaiHoan?.length !== listTrangThaiHoan?.length
        ? _state.dsTrangThaiHoan
        : null,
    daPhat: _state.daPhat,
    dsKhoId: _state.dsKhoId,
    dsPhongChiDinhId: _state.dsPhongChiDinhId,
    dsThuNganId: _state.dsThuNganId,
    dsChiDinhTuLoaiDichVu: _state.dsChiDinhTuLoaiDichVu,
    chiSoKetQua: _state.chiSoKetQua,
    dsNbDotDieuTriId: _state.dsNbDotDieuTriId,
  });

  const beforeOk =
    ({ _state, _beforeOk }) =>
    () => {
      if (!isArray(_state.dsCoSoKcbId, 1)) {
        message.error(
          t("danhMuc.vuiLongChonTitle", {
            title: t("baoCao.coSoChiNhanh"),
          })
        );
        return false;
      }
      if (!_state.loaiThoiGian && _state.loaiThoiGian != "") {
        message.error(t("baoCao.vuiLongChonTheoThoiGian"));
        return false;
      }
      if (!_state.tuNgay) {
        message.error(t("baoCao.chonTuNgay"));
        return false;
      }
      if (!_state.denNgay) {
        message.error(t("baoCao.chonDenNgay"));
        return false;
      }
      return _beforeOk();
    };
  return (
    <Main>
      <BaseBaoCao
        title={t("baoCao.bc10")}
        renderFilter={renderFilter}
        getBc={getBc10}
        handleDataSearch={handleDataSearch}
        beforeOk={beforeOk}
        initState={{
          loaiThoiGian: 10,
          khoaChiDinhId: isMacDinhKhoaChiDinh
            ? auth?.khoaId
            : khoaChiDinhDefault,
          doiTuong: [""],
          dsBacSiChiDinhId: [""],
          thanhToan: [""],
          dsNhomDichVuCap1Id: [""],
          dsNhomDichVuCap2Id: [""],
          dsNhomDichVuCap3Id: [""],
          thucHienDichVu: [""],
          dichVuHoan: [""],
          dsBacSiThucHienId: [""],
          dsDoiTuongKcb: PHAN_LOAI_DOI_TUONG_KCB.map((i) => i.id),
          dsLoaiHinhThanhToanId: [""],
          loaiDoiTuongId: [""],
          dsKhoaThucHienId: isMacDinhKhoaThucHien ? [auth?.khoaId] : [""],
          dsPhongThucHienId: [""],
          theoYeuCau: [""],
          dsDichVuId: [""],
          capCuu: [""],
          dsNhaThuNganId: [""],
          khoaThucHien: false,
          dsLoaiPhieuThu: LIST_LOAI_PHIEU_THU_CUSTOM.map((i) => i.id),
          dsTrangThaiHoan: trangThaiHoan,
          dsKhoId: [""],
          daPhat: [""],
          dsPhongChiDinhId: [""],
          chiSoKetQua: false,
        }}
        breadcrumb={[{ title: "BC10", link: "/bao-cao/bc10" }]}
      />
    </Main>
  );
};

export default Index;
