import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { Col, message, Row } from "antd";
import moment from "moment";

import { useEnum, useQueryAll } from "hooks";
import { DateTimePicker, Select } from "components";
import BaseBaoCao from "pages/baocao/BaseBaoCao";
import { query } from "redux-store/stores";
import { selectMaTen } from "redux-store/selectors";
import { ENUM } from "constants/index";

const KHTH43 = () => {
  const { t } = useTranslation();
  const {
    baoCaoDaIn: { getKhth43 },
  } = useDispatch();
  const [listLoaiThoiGian] = useEnum(ENUM.LOAI_THOI_GIAN);
  const { data: listAllKhoa } = useQueryAll(query.khoa.queryAllKhoa);
  const { data: listAllPhong } = useQueryAll(query.phong.queryAllPhong);

  const listLoaiThoiGianMemo = useMemo(() => {
    return listLoaiThoiGian.filter((i) => [10, 30].includes(i.id));
  }, [listLoaiThoiGian]);

  const renderFilter = ({ onChange, _state }) => {
    return (
      <Row>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.tuNgay")}
              <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              className="input-filter"
              placeholder={t("common.chonNgay")}
              onChange={onChange("tuNgay")}
              value={_state.tuNgay}
              format="DD/MM/YYYY HH:mm:ss"
              showTime={{ defaultValue: moment().startOf("day") }}
            />
            {!_state.isValidData && !_state.tuNgay && (
              <div className="error">{t("baoCao.chonTuNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.denNgay")}
              <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              className="input-filter"
              placeholder={t("common.chonNgay")}
              onChange={onChange("denNgay")}
              value={_state.denNgay}
              format="DD/MM/YYYY HH:mm:ss"
              showTime={{ defaultValue: moment().endOf("day") }}
            />
            {!_state.isValidData && !_state.denNgay && (
              <div className="error">{t("baoCao.chonDenNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter pointer">
              {t("baoCao.loaiThoiGian")}
              <span className="icon-required"> *</span>
            </label>
            <Select
              onChange={onChange("loaiThoiGian")}
              value={_state.loaiThoiGian}
              className="input-filter"
              placeholder={t("baoCao.chonLoaiThoiGian")}
              data={listLoaiThoiGianMemo}
            />
            {!_state.isValidData && !_state.loaiThoiGian && (
              <div className="error">{t("baoCao.vuiLongChonLoaiThoiGian")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("common.khoa")}</label>
            <Select
              className="select input-filter"
              placeholder={t("common.chonKhoa")}
              onChange={onChange("dsKhoaNbId", true)}
              value={_state.dsKhoaNbId}
              data={listAllKhoa}
              getLabel={selectMaTen}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("dashboard.phongKham")}</label>
            <Select
              className="select input-filter"
              placeholder={t("common.chonPhongKham")}
              onChange={onChange("dsPhongThucHienId", true)}
              value={_state.dsPhongThucHienId}
              data={listAllPhong}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.khoaChiDinh")}</label>
            <Select
              className="select input-filter"
              placeholder={t("baoCao.chonKhoaChiDinh")}
              onChange={onChange("dsKhoaChiDinhId", true)}
              value={_state.dsKhoaChiDinhId}
              data={listAllKhoa}
              getLabel={selectMaTen}
              mode="multiple"
            />
          </div>
        </Col>
      </Row>
    );
  };

  const beforeOk =
    ({ _state, _beforeOk }) =>
    () => {
      if (!_state.loaiThoiGian) {
        message.error(t("baoCao.vuiLongChonLoaiThoiGian"));
        return false;
      }
      return _beforeOk();
    };

  const parseData = (data, list) => {
    return (data || []).length === (list || []).length ? null : data;
  };

  const handleDataSearch = ({ _state }) => ({
    tuThoiGian: moment(_state.tuNgay).format("DD-MM-YYYY HH:mm:ss"),
    denThoiGian: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
    loaiThoiGian: _state.loaiThoiGian,
    dsKhoaNbId: parseData(_state.dsKhoaNbId, listAllKhoa),
    dsPhongThucHienId: parseData(_state.dsPhongThucHienId, listAllPhong),
    dsKhoaChiDinhId: parseData(_state.dsKhoaChiDinhId, listAllKhoa),
  });

  return (
    <BaseBaoCao
      title={t("baoCao.khth43")}
      renderFilter={renderFilter}
      getBc={getKhth43}
      handleDataSearch={handleDataSearch}
      beforeOk={beforeOk}
      breadcrumb={[{ title: "KHTH43", link: "/bao-cao/khth-43" }]}
    />
  );
};

export default KHTH43;
