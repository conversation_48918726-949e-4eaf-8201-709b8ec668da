import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { Col, Row, InputNumber, message } from "antd";
import moment from "moment";
import { useEnum, useListAll, useQueryAll } from "hooks";
import { DateTimePicker, Select } from "components";
import BaseBaoCao from "pages/baocao/BaseBaoCao";
import { selectMaTen } from "redux-store/selectors";
import { ENUM, DS_TINH_CHAT_KHOA } from "constants/index";
import { query } from "redux-store/stores";

const LOAI_THOI_GIAN_FILTER = [
  10, //Theo thời gian vào viện
  280, //<PERSON> thời gian sinh
];

const KHTH31 = () => {
  const { t } = useTranslation();
  const {
    baoCaoDaIn: { getKhth31 },
  } = useDispatch();

  const [listAllKhoa] = useListAll("khoa", {}, true);
  const [listAllLoaiDoiTuong] = useListAll("loaiDoiTuong", {}, true);
  const [listAllNgoiThai] = useListAll("ngoiThai", {}, true);
  const [listDoiTuong] = useEnum(ENUM.DOI_TUONG);
  const [listLoaiThoiGian] = useEnum(ENUM.LOAI_THOI_GIAN);
  const { data: listAllKhoaNoiTru } = useQueryAll(
    query.khoa.queryAllKhoa({
      params: {
        dsTinhChatKhoa: [DS_TINH_CHAT_KHOA.NOI_TRU],
      },
    })
  );

  const listLoaiThoiGianMemo = useMemo(() => {
    return (listLoaiThoiGian || []).filter((item) =>
      LOAI_THOI_GIAN_FILTER.includes(item.id)
    );
  }, [listLoaiThoiGian]);

  const renderFilter = ({ onChange, _state }) => {
    return (
      <Row>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.tuNgay")}
              <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              className="input-filter"
              placeholder={t("common.chonNgay")}
              onChange={onChange("tuNgay")}
              value={_state.tuNgay}
              format="DD/MM/YYYY HH:mm:ss"
              showTime={{ defaultValue: moment().startOf("day") }}
            />
            {!_state.isValidData && !_state.tuNgay && (
              <div className="error">{t("baoCao.chonTuNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.denNgay")}
              <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              className="input-filter"
              placeholder={t("common.chonNgay")}
              onChange={onChange("denNgay")}
              value={_state.denNgay}
              format="DD/MM/YYYY HH:mm:ss"
              showTime={{ defaultValue: moment().endOf("day") }}
            />
            {!_state.isValidData && !_state.denNgay && (
              <div className="error">{t("baoCao.chonDenNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.theoThoiGian")}
              <span className="icon-required"> *</span>
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonLoaiThoiGian")}
              onChange={onChange("loaiThoiGian")}
              value={_state.loaiThoiGian}
              data={listLoaiThoiGianMemo}
            />
            {!_state.isValidData && !_state.loaiThoiGian && (
              <div className="error">{t("baoCao.vuiLongChonLoaiThoiGian")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("common.khoa")}</label>
            <Select
              className="input-filter"
              placeholder={t("common.chonKhoa")}
              onChange={onChange("dsKhoaId")}
              value={_state.dsKhoaId}
              data={listAllKhoa}
              getLabel={selectMaTen}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.loaiDoiTuong")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonLoaiDoiTuong")}
              data={listAllLoaiDoiTuong}
              onChange={onChange("dsLoaiDoiTuongId")}
              value={_state.dsLoaiDoiTuongId}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.doiTuong")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonDoiTuong")}
              data={listDoiTuong}
              onChange={onChange("doiTuong")}
              value={_state.doiTuong}
              hasAllOption={true}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-input">
            <label className="label-filter">{t("baoCao.tuCanNangCon")}</label>
            <InputNumber
              className="input-filter"
              placeholder={t("baoCao.nhapTuCanNangCon")}
              value={_state.tuCanNang}
              onChange={onChange("tuCanNang")}
              type="number"
              min={0}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-input">
            <label className="label-filter">{t("baoCao.denCanNangCon")}</label>
            <InputNumber
              className="input-filter"
              placeholder={t("baoCao.nhapDenCanNangCon")}
              value={_state.denCanNang}
              onChange={onChange("denCanNang")}
              type="number"
              min={0}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("danhMuc.ngoiThai")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonNgoiThai")}
              onChange={onChange("dsNgoiThaiId")}
              value={_state.dsNgoiThaiId}
              data={listAllNgoiThai}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("quanLyNoiTru.khoaDe")}</label>
            <Select
              className="input-filter"
              placeholder={t("common.chonKhoa")}
              onChange={onChange("dsKhoaSinhId")}
              value={_state.dsKhoaSinhId}
              data={listAllKhoaNoiTru}
              getLabel={selectMaTen}
              mode="multiple"
            />
          </div>
        </Col>
      </Row>
    );
  };

  const handleDataSearch = ({ _state }) => ({
    tuThoiGian: moment(_state.tuNgay).format("DD-MM-YYYY HH:mm:ss"),
    denThoiGian: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
    dsKhoaId: _state.dsKhoaId,
    dsLoaiDoiTuongId: _state.dsLoaiDoiTuongId,
    doiTuong: _state.doiTuong,
    tuCanNang: _state.tuCanNang,
    denCanNang: _state.denCanNang,
    dsNgoiThaiId: _state.dsNgoiThaiId,
    loaiThoiGian: _state.loaiThoiGian,
    dsKhoaSinhId: _state.dsKhoaSinhId
  });

  const beforeOk =
    ({ _state, _beforeOk }) =>
    () => {
      if (!_state.loaiThoiGian) {
        message.error(t("baoCao.vuiLongChonLoaiThoiGian"));
        return false;
      }
      return _beforeOk();
    };


  return (
    <BaseBaoCao
      title={t("baoCao.khth31")}
      renderFilter={renderFilter}
      getBc={getKhth31}
      handleDataSearch={handleDataSearch}
      breadcrumb={[{ title: "KHTH31", link: "/bao-cao/khth-31" }]}
      beforeOk={beforeOk}
    />
  );
};

export default KHTH31;
