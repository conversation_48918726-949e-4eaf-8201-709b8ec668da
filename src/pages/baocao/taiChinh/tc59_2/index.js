import React, { useEffect, useMemo } from "react";
import { Col, message, Row } from "antd";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { DateTimePicker, Select, SelectLoadMore } from "components";
import BaseBaoCao from "../../BaseBaoCao";
import { useListAll, useEnum, useLazyKVMap, useQueryAll } from "hooks";
import {
  ENUM,
  PHAN_LOAI_DOI_TUONG_KCB,
  THIET_LAP_CHUNG,
  TRANG_THAI_THANH_TOAN_PHIEU_THU,
  LOAI_QUAY,
  TAI_TRO_BHBL,
} from "constants/index";
import { query, select } from "redux-store/stores";
import { selectMaTen } from "redux-store/selectors";
import dichVuProvider from "data-access/categories/dm-dich-vu-provider";
import {
  DOI_TUONG_THANH_TOAN_DICH_VU,
  LOAI_PHIEU_THU_TRONG_VIEN,
} from "pages/baocao/utils";
import ChonNhomBaoCao from "pages/baocao/BaseBaoCao/components/ChonNhomBaoCao";
import { isNumber, openInNewTab } from "utils/index";

const Index = () => {
  const { t } = useTranslation();
  const {
    baoCaoDaIn: { getTc59_2 },
    khoa: { getListAllKhoa },
    thietLap: { getThietLap },
    nhanVien: { getListAllNhanVien },
    nhomDichVuCap2: { getAllTongHopDichVuCap2 },
    nhomDichVuCap3: { getAllTongHopDichVuCap3 },
  } = useDispatch();

  const [listAllToaNha] = useListAll("toaNha", {}, true);
  const [listAllLoaiHinhThanhToan] = useListAll("loaiHinhThanhToan", {}, true);
  const [listAllNhomDichVuCap1] = useListAll("nhomDichVuCap1", {}, true);
  const [listAllNhomDichVuCap2] = useListAll("nhomDichVuCap2", {}, true);
  const [listAllNhomDichVuCap3] = useListAll("nhomDichVuCap3", {}, true);
  const [listAllCaLamViec] = useListAll("caLamViec", {}, true);
  const [listAllQuayTiepDon] = useListAll(
    "quayTiepDon",
    {
      dsLoai: LOAI_QUAY.THU_NGAN,
    },
    true
  );
  const { data: listAllPhong } = useQueryAll(query.phong.queryAllPhong);

  const [listDoiTuong] = useEnum(ENUM.DOI_TUONG);
  const [listLoaiPhieuThu] = useEnum(ENUM.LOAI_PHIEU_THU);
  const [listLoaiThoiGian] = useEnum(ENUM.LOAI_THOI_GIAN);

  const [getPhanLoaiDoiTuongKcb] = useLazyKVMap(PHAN_LOAI_DOI_TUONG_KCB);

  useEffect(() => {
    const defaultParams = { page: "", size: "", active: true };
    getListAllKhoa(defaultParams);
    getThietLap({ ma: THIET_LAP_CHUNG.MA_THU_NGAN }).then((dsMaVaiTro) => {
      getListAllNhanVien({ ...defaultParams, dsMaVaiTro });
    });
  }, []);

  const listEnumLoaiPhieuThuTrongVien = useMemo(() => {
    return listLoaiPhieuThu.map((x) => x.id).filter((x) => ![5, 6].includes(x));
  }, [listLoaiPhieuThu]);

  const listLoaiPhieuThuEnumCustom = useMemo(() => {
    return [
      { id: 6, ten: t("baoCao.thuNhaThuoc") },
      {
        id: LOAI_PHIEU_THU_TRONG_VIEN,
        ten: t("baoCao.thuTrongVien"),
      },
      { id: 5, ten: t("baoCao.thuNgoai") },
    ];
  }, [listLoaiPhieuThu]);

  const listLoaiThoiGianMemo = useMemo(() => {
    return listLoaiThoiGian.filter((i) => [10, 40].includes(i.id));
  }, [listLoaiThoiGian]);

  const handleChange = (key, onChange) => (value) => {
    const defaultParams = { page: "", size: "", active: true };
    if (key === "dsNhomDichVuCap1Id") {
      getAllTongHopDichVuCap2({
        ...defaultParams,
        dsNhomDichVuCap1Id: value,
      });
      onChange("dsNhomDichVuCap2Id", true)([""]);
    } else if (key === "dsNhomDichVuCap2Id") {
      getAllTongHopDichVuCap3({
        ...defaultParams,
        dsNhomDichVuCap2Id: value,
      });
      onChange("dsNhomDichVuCap3Id", true)([""]);
    }
    onChange(key)(value);
  };

  const renderFilter = ({ onChange, _state, onKeyDownDate }) => {
    return (
      <Row>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter pointer">
              {t("baoCao.theoThoiGian")}
              <span className="icon-required"> *</span>
            </label>
            <Select
              onChange={onChange("loaiThoiGian")}
              value={_state.loaiThoiGian}
              className="input-filter"
              placeholder={t("baoCao.chonLoaiThoiGian")}
              data={listLoaiThoiGianMemo}
            />
            {!_state.isValidData && !_state.loaiThoiGian && (
              <div className="error">{t("baoCao.vuiLongChonLoaiThoiGian")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.tuThoiGian")} <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              showTime={{ defaultValue: moment().startOf("day") }}
              value={_state.tuNgay}
              onChange={onChange("tuNgay")}
              placeholder={t("common.chonThoiGian")}
              format="DD/MM/YYYY HH:mm:ss"
              className="input-filter"
              onKeyDown={onKeyDownDate("tuNgay")}
            />
            {!_state.isValidData && !_state.tuNgay && (
              <div className="error">{t("baoCao.chonTuNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.denThoiGian")} <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              showTime={{ defaultValue: moment().endOf("day") }}
              value={_state.denNgay}
              onChange={onChange("denNgay")}
              placeholder={t("common.chonThoiGian")}
              format="DD/MM/YYYY HH:mm:ss"
              className="input-filter"
              onKeyDown={onKeyDownDate("denNgay")}
            />
            {!_state.isValidData && !_state.denNgay && (
              <div className="error">{t("baoCao.chonDenNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("danhMuc.doiTuongKhamChuaBenh")}
            </label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonDoiTuongKhamChuaBenh")}
              onChange={onChange("dsDoiTuongKcb")}
              value={_state.dsDoiTuongKcb}
              mode="multiple"
              data={PHAN_LOAI_DOI_TUONG_KCB}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("common.doiTuong")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonDoiTuongNguoiBenh")}
              onChange={onChange("doiTuong")}
              value={_state.doiTuong}
              data={listDoiTuong}
              hasAllOption={true}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.khoaThucHien")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonKhoaThucHien")}
              onChange={handleChange("dsKhoaThucHienId", onChange)}
              value={_state.dsKhoaThucHienId}
              data={select.khoa.listAllKhoa}
              getLabel={selectMaTen}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.khoaChiDinh")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonKhoaChiDinh")}
              onChange={onChange("dsKhoaChiDinhId")}
              value={_state.dsKhoaChiDinhId}
              data={select.khoa.listAllKhoa}
              getLabel={selectMaTen}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.nhomDichVuCap1")}</label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonNhomDichVuCap1")}
              onChange={handleChange("dsNhomDichVuCap1Id", onChange)}
              value={_state.dsNhomDichVuCap1Id}
              data={listAllNhomDichVuCap1}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.nhomDichVuCap2")}</label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonNhomDichVuCap2")}
              onChange={handleChange("dsNhomDichVuCap2Id", onChange)}
              value={_state.dsNhomDichVuCap2Id}
              data={listAllNhomDichVuCap2}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.nhomDichVuCap3")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonDichVu")}
              onChange={handleChange("dsNhomDichVuCap3Id", onChange, _state)}
              value={_state.dsNhomDichVuCap3Id}
              data={listAllNhomDichVuCap3}
              mode="multiple"
              hasAllOption={true}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.tenDichVu")}</label>
            <SelectLoadMore
              api={dichVuProvider.searchAll}
              mapData={(i) => ({
                value: i.id,
                label: i.ten,
              })}
              onChange={onChange("dsDichVuId", true)}
              value={_state.dsDichVuId}
              keySearch={"ten"}
              placeholder={t("baoCao.chonDichVu")}
              className="input-filter"
              addParam={{ active: true }}
              blurReset={true}
              mode="multiple"
              hasAll={true}
            />
          </div>
        </Col>
        <ChonNhomBaoCao
          onChange={onChange}
          _state={_state}
          keyValue="dsMaNhomBaoCao"
          mode={"multiple"}
        />
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.nhaThuNgan")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonNhaThuNgan")}
              onChange={onChange("dsNhaThuNganId")}
              value={_state.dsNhaThuNganId}
              data={listAllToaNha}
              mode="multiple"
            />
          </div>
        </Col>
        <Col span={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.thuNgan")}</label>
            <Select
              mode="multiple"
              className="input-filter"
              placeholder={t("baoCao.chonThuNgan")}
              data={select.nhanVien.listAllNhanVien}
              getLabel={(item) =>
                `${item.taiKhoan ? item.taiKhoan + " - " : ""}${item.ma} - ${
                  item.ten
                }`
              }
              onChange={onChange("dsThuNganId")}
              value={_state.dsThuNganId}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("danhMuc.caLamViec")}</label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonLoaiQuay")}
              onChange={onChange("dsCaLamViecId")}
              value={_state.dsCaLamViecId}
              data={listAllCaLamViec}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label
              className="label-filter cursor-pointer"
              onClick={() => openInNewTab("/danh-muc/loai-hinh-thanh-toan")}
            >
              {t("baoCao.loaiHinhThanhToan")}
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonLoaiHinhThanhToan")}
              data={listAllLoaiHinhThanhToan}
              onChange={onChange("dsLoaiHinhThanhToanId", true)}
              value={_state.dsLoaiHinhThanhToanId}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.loaiPhieuThu")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonLoaiPhieuThu")}
              onChange={onChange("dsLoaiPhieuThu")}
              value={_state.dsLoaiPhieuThu}
              data={listLoaiPhieuThuEnumCustom}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.trangThaiThanhToan")}
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonTrangThaiThanhToan")}
              onChange={onChange("thanhToan")}
              value={_state.thanhToan}
              data={TRANG_THAI_THANH_TOAN_PHIEU_THU}
              hasAllOption={true}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.phongThucHien")}</label>
            <Select
              mode="multiple"
              onChange={onChange("dsPhongThucHienId")}
              value={_state.dsPhongThucHienId}
              className="input-filter"
              placeholder={t("baoCao.chonPhongThucHien")}
              data={listAllPhong}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label
              className="label-filter"
              onClick={() => openInNewTab("/danh-muc/quay-tiep-don")}
            >
              {t("baoCao.quayThu")}
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonQuayThu")}
              onChange={onChange("dsQuayId")}
              value={_state.dsQuayId}
              data={listAllQuayTiepDon}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.phongChiDinh")}</label>
            <Select
              mode="multiple"
              onChange={onChange("dsPhongChiDinhId", true)}
              value={_state.dsPhongChiDinhId}
              defaultValue={_state.dsPhongChiDinhId}
              className="input-filter"
              placeholder={t("baoCao.chonPhong")}
              data={listAllPhong}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.doiTuongThanhToanDichVu")}
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonDoiTuongThanhToanDichVu")}
              onChange={onChange("dsNhomLoaiDichVu")}
              value={_state.dsNhomLoaiDichVu}
              data={DOI_TUONG_THANH_TOAN_DICH_VU}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.taiTroBHBL")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonTaiTroBHBL")}
              onChange={onChange("hienThiTienTaiTro")}
              value={_state.hienThiTienTaiTro}
              data={TAI_TRO_BHBL}
              hasAllOption
            />
          </div>
        </Col>
      </Row>
    );
  };

  const convertDSLoaiPhieuThu = (data) => {
    if (!Array.isArray(data)) data = [data];

    return data
      .flatMap((item) => {
        if (item === LOAI_PHIEU_THU_TRONG_VIEN) {
          return listEnumLoaiPhieuThuTrongVien;
        }
        return item;
      })
      .filter((x) => isNumber(x));
  };

  const beforeOk =
    ({ _state, _beforeOk }) =>
    () => {
      if (!_state.loaiThoiGian) {
        message.error(t("baoCao.vuiLongChonTheoThoiGian"));
        return false;
      }
      return _beforeOk();
    };

  const handleDataSearch = ({ _state }) => {
    return {
      loaiThoiGian: _state.loaiThoiGian,
      tuThoiGian: moment(_state.tuNgay).format("DD-MM-YYYY HH:mm:ss"),
      denThoiGian: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
      dsDoiTuongKcb:
        _state.dsDoiTuongKcb && _state.dsDoiTuongKcb[0] != ""
          ? _state.dsDoiTuongKcb.flatMap(
              (item) => getPhanLoaiDoiTuongKcb(item)?.referIds || []
            )
          : null,
      doiTuong: _state.doiTuong,
      dsKhoaThucHienId: _state.dsKhoaThucHienId,
      dsKhoaChiDinhId: _state.dsKhoaChiDinhId,
      dsNhomDichVuCap1Id: _state.dsNhomDichVuCap1Id,
      dsNhomDichVuCap2Id: _state.dsNhomDichVuCap2Id,
      dsNhomDichVuCap3Id: _state.dsNhomDichVuCap3Id,
      dsDichVuId: _state.dsDichVuId,
      dsMaNhomBaoCao: _state.dsMaNhomBaoCao,
      dsNhaThuNganId: _state.dsNhaThuNganId,
      dsThuNganId: _state.dsThuNganId,
      dsCaLamViecId: _state.dsCaLamViecId,
      dsLoaiHinhThanhToanId: _state.dsLoaiHinhThanhToanId,
      dsLoaiPhieuThu: convertDSLoaiPhieuThu(_state.dsLoaiPhieuThu),
      thanhToan: _state.thanhToan,
      dsPhongThucHienId: _state.dsPhongThucHienId,
      dsQuayId: _state.dsQuayId,
      dsPhongChiDinhId: _state.dsPhongChiDinhId,
      dsNhomLoaiDichVu: _state.dsNhomLoaiDichVu,
      hienThiTienTaiTro: _state.hienThiTienTaiTro,
    };
  };

  return (
    <BaseBaoCao
      title={t("baoCao.tc59_2")}
      breadcrumb={[{ title: "TC59.2", link: "/bao-cao/tc59_2" }]}
      renderFilter={renderFilter}
      getBc={getTc59_2}
      handleDataSearch={handleDataSearch}
      beforeOk={beforeOk}
      initState={{
        dsDoiTuongKcb: [""],
        dsKhoaThucHienId: [""],
        dsKhoaChiDinhId: [""],
        dsNhaThuNganId: [""],
        dsNhomDichVuCap1Id: [""],
        dsNhomDichVuCap2Id: [""],
        dsNhomDichVuCap3Id: [""],
        dsThuNganId: [""],
        dsLoaiHinhThanhToanId: [""],
        dsLoaiPhieuThu: [LOAI_PHIEU_THU_TRONG_VIEN],
        dsCaLamViecId: [""],
        doiTuong: "",
        thanhToan: 50,
        loaiThoiGian: 40,
        dsPhongThucHienId: [""],
        dsQuayId: [""],
        dsNhomLoaiDichVu: [""],
        hienThiTienTaiTro: "",
      }}
    />
  );
};

export default Index;
