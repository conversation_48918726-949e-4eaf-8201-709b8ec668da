import React, { useEffect } from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { Col, message, Row } from "antd";

import {
  useLazyKVMap,
  useListAll,
  useQueryAll,
  useStore,
  useThietLap,
} from "hooks";

import { Select, DateTimePicker, Checkbox } from "components";
import BaseBaoCao from "pages/baocao/BaseBaoCao";
import { PHAN_LOAI_DOI_TUONG_KCB, THIET_LAP_CHUNG } from "constants/index";
import { query } from "redux-store/stores";
import { useMemo } from "react";

const TC81 = () => {
  const { t } = useTranslation();

  const listMauBaoCao = [
    {
      id: 1,
      ten: t("baoCao.mauChiTiet"),
    },
    {
      id: 2,
      ten: t("baoCao.mauTongHop"),
    },
    {
      id: 3,
      ten: t("baoCao.mauChiTietSoLuong"),
    },
  ];

  const {
    baoCaoDaIn: { getTc81 },
    nhomDichVuCap1: { getAllTongHopDichVuCap1 },
    nhomDichVuCap2: { getAllTongHopDichVuCap2 },
  } = useDispatch();

  const [dataMA_THU_NGAN, finishGetMaThuNgan] = useThietLap(
    THIET_LAP_CHUNG.MA_THU_NGAN
  );
  const [listAllQuayTiepDon] = useListAll("quayTiepDon", { dsLoai: 20 }, true);
  const [MA_NHOM_DV_C1_TE_BAO_GOC, isLoadFinish] = useThietLap(
    THIET_LAP_CHUNG.MA_NHOM_DV_C1_TE_BAO_GOC
  );
  const listAllNhomDichVuCap1 = useStore(
    "nhomDichVuCap1.listAllNhomDichVuCap1",
    []
  );
  const listAllNhomDichVuCap2 = useStore(
    "nhomDichVuCap2.listAllNhomDichVuCap2",
    []
  );
  const { data: listAllCaLamViec } = useQueryAll(
    query.caLamViec.queryAllCaLamViec
  );
  const { data: listAllToaNha } = useQueryAll(query.toaNha.queryAllToaNha);
  const { data: listThuNgan } = useQueryAll(
    query.nhanVien.queryAllNhanVien({
      params: {
        dsMaVaiTro: dataMA_THU_NGAN,
      },
      enabled: finishGetMaThuNgan,
    })
  );

  useEffect(() => {
    if (isLoadFinish) {
      getAllTongHopDichVuCap1({
        active: true,
        page: "",
        size: "",
        dsMaThietLap: ["MA_NHOM_DV_C1_TE_BAO_GOC"],
      });
    }
  }, [MA_NHOM_DV_C1_TE_BAO_GOC, isLoadFinish]);

  useEffect(() => {
    if (listAllNhomDichVuCap1?.length) {
      getAllTongHopDichVuCap2({
        page: "",
        size: "",
        active: true,
        dsNhomDichVuCap1Id: listAllNhomDichVuCap1.map((item) => item.id),
      });
    }
  }, [listAllNhomDichVuCap1]);

  const renderFilter = ({ onChange, _state }) => {
    return (
      <Row>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.tuNgay")}
              <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              className="input-filter"
              placeholder={t("common.chonNgay")}
              onChange={onChange("tuNgay")}
              value={_state.tuNgay}
              format="DD/MM/YYYY HH:mm:ss"
              showTime={{ defaultValue: moment().startOf("day") }}
            />
            {!_state.isValidData && !_state.tuNgay && (
              <div className="error">{t("baoCao.chonTuNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.denNgay")}
              <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              className="input-filter"
              placeholder={t("common.chonNgay")}
              value={_state.denNgay}
              onChange={onChange("denNgay")}
              format="DD/MM/YYYY HH:mm:ss"
              showTime={{ defaultValue: moment().endOf("day") }}
            />
            {!_state.isValidData && !_state.denNgay && (
              <div className="error">{t("baoCao.chonDenNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.thuNgan")}</label>
            <Select
              onChange={onChange("dsThuNganId")}
              value={_state.dsThuNganId}
              className="input-filter"
              placeholder={t("baoCao.chonThuNgan")}
              data={listThuNgan}
              getLabel={(item) =>
                `${item.taiKhoan || ""} - ${item.ma} - ${item.ten}`
              }
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("danhMuc.caLamViec")}</label>
            <Select
              className="input-filter"
              placeholder={t("thuNgan.chonCaLamViec")}
              onChange={onChange("dsCaLamViecId", true)}
              value={_state.dsCaLamViecId}
              defaultValue={_state.dsCaLamViecId}
              data={listAllCaLamViec}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.quayThu")}</label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonLoaiQuay")}
              onChange={onChange("dsQuayId", true)}
              value={_state.dsQuayId}
              data={listAllQuayTiepDon}
              mode="multiple"
              showArrow
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.nhaThuNgan")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonNhaThuNgan")}
              onChange={onChange("dsToaNhaId")}
              value={_state.dsToaNhaId}
              data={listAllToaNha}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.nhomDichVuCap2")}</label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonNhomDichVuCap2")}
              onChange={onChange("nhomDichVuCap2Id")}
              value={_state.nhomDichVuCap2Id}
              data={listAllNhomDichVuCap2}
              hasAllOption={true}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.mauBaoCao")} <span className="icon-required">*</span>
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonMauBaoCao")}
              data={listMauBaoCao}
              onChange={onChange("mauBaoCao")}
              value={_state.mauBaoCao}
            />
          </div>
        </Col>
      </Row>
    );
  };

  const beforeOk =
    ({ _state, _beforeOk }) =>
    () => {
      if (!_state.mauBaoCao) {
        message.error(t("baoCao.vuiLongChonMauBaoCao"));
        return false;
      }
      return _beforeOk();
    };

  const handleDataSearch = ({ _state }) => {
    return {
      tuThoiGian: moment(_state.tuNgay).format("DD-MM-YYYY HH:mm:ss"),
      denThoiGian: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
      dsThuNganId: _state.dsThuNganId,
      dsCaLamViecId: _state.dsCaLamViecId,
      dsQuayId: _state.dsQuayId,
      dsToaNhaId: _state.dsToaNhaId,
      mauBaoCao: _state.mauBaoCao,
      nhomDichVuCap2Id: _state.nhomDichVuCap2Id,
    };
  };

  return (
    <BaseBaoCao
      title={t("baoCao.tc81")}
      breadcrumb={[{ title: "tc81", link: "/bao-cao/tc81" }]}
      renderFilter={renderFilter}
      getBc={getTc81}
      initState={{
        dsQuayId: [""],
        dsThuNganId: [""],
        dsCaLamViecId: [""],
        dsToaNhaId: [""],
        inChiTiet: false,
      }}
      handleDataSearch={handleDataSearch}
      beforeOk={beforeOk}
    />
  );
};

export default TC81;
