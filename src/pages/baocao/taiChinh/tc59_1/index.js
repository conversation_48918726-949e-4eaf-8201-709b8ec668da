import React, { useEffect } from "react";
import { Col, Row } from "antd";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { Checkbox, DateTimePicker, Select } from "components";
import BaseBaoCao from "../../BaseBaoCao";
import { useListAll, useEnum, useLazyKVMap } from "hooks";
import {
  ENUM,
  PHAN_LOAI_DOI_TUONG_KCB,
  LOAI_QUAY,
  THIET_LAP_CHUNG,
  TAI_TRO_BHBL,
} from "constants/index";
import { select } from "redux-store/stores";
import { selectMaTen } from "redux-store/selectors";
import { DOI_TUONG_THANH_TOAN_DICH_VU } from "pages/baocao/utils";

const Index = () => {
  const { t } = useTranslation();
  const {
    baoCaoDaIn: { getTc59_1 },
    khoa: { getListAllKhoa },
    quayTiepDon: { getListAllQuayTiepDon },
    thietLap: { getThietLap },
    nhanVien: { getListAllNhanVien },
  } = useDispatch();

  const [listAllToaNha] = useListAll("toaNha", {}, true);
  const [listAllLoaiDoiTuong] = useListAll("loaiDoiTuong", {}, true);
  const [listAllLoaiHinhThanhToan] = useListAll("loaiHinhThanhToan", {}, true);
  const [listAllPhuongThucTT] = useListAll("phuongThucTT", {}, true);
  const [listAllDoiTacThanhToan] = useListAll(
    "doiTac",
    {},
    true,
    "DoiTacThanhToan"
  );
  const [listAllCaLamViec] = useListAll("caLamViec", {}, true);

  const [listDoiTuong] = useEnum(ENUM.DOI_TUONG);

  const [getPhanLoaiDoiTuongKcb] = useLazyKVMap(PHAN_LOAI_DOI_TUONG_KCB);

  useEffect(() => {
    const defaultParams = { page: "", size: "", active: true };

    getListAllKhoa(defaultParams);
    getListAllQuayTiepDon({ ...defaultParams, dsLoai: LOAI_QUAY.THU_NGAN });
    getThietLap({ ma: THIET_LAP_CHUNG.MA_THU_NGAN }).then((dsMaVaiTro) => {
      getListAllNhanVien({ ...defaultParams, dsMaVaiTro });
    });
  }, []);

  const handleChange = (key, onChange) => (value) => {
    onChange(key)(value);
  };

  const renderFilter = ({ onChange, _state, onKeyDownDate }) => {
    return (
      <Row>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.tuThoiGian")} <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              showTime={{ defaultValue: moment().startOf("day") }}
              value={_state.tuNgay}
              onChange={onChange("tuNgay")}
              placeholder={t("common.chonThoiGian")}
              format="DD/MM/YYYY HH:mm:ss"
              className="input-filter"
              onKeyDown={onKeyDownDate("tuNgay")}
            />
            {!_state.isValidData && !_state.tuNgay && (
              <div className="error">{t("baoCao.chonTuNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.denThoiGian")} <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              showTime={{ defaultValue: moment().endOf("day") }}
              value={_state.denNgay}
              onChange={onChange("denNgay")}
              placeholder={t("common.chonThoiGian")}
              format="DD/MM/YYYY HH:mm:ss"
              className="input-filter"
              onKeyDown={onKeyDownDate("denNgay")}
            />
            {!_state.isValidData && !_state.denNgay && (
              <div className="error">{t("baoCao.chonDenNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.khoaThucHien")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonKhoaThucHien")}
              onChange={handleChange("dsKhoaThucHienId", onChange)}
              value={_state.dsKhoaThucHienId}
              data={select.khoa.listAllKhoa}
              getLabel={selectMaTen}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.khoaChiDinh")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonKhoaChiDinh")}
              onChange={onChange("dsKhoaChiDinhId")}
              value={_state.dsKhoaChiDinhId}
              data={select.khoa.listAllKhoa}
              getLabel={selectMaTen}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.nhaThuNgan")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonNhaThuNgan")}
              onChange={onChange("dsNhaThuNganId")}
              value={_state.dsNhaThuNganId}
              data={listAllToaNha}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.quayThu")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonQuayThu")}
              onChange={onChange("dsQuayId")}
              value={_state.dsQuayId}
              data={select.quayTiepDon.listAllQuayTiepDon}
              mode="multiple"
            />
          </div>
        </Col>
        <Col span={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.thuNgan")}</label>
            <Select
              mode="multiple"
              className="input-filter"
              placeholder={t("baoCao.chonThuNgan")}
              data={select.nhanVien.listAllNhanVien}
              getLabel={(item) =>
                `${item.taiKhoan ? item.taiKhoan + " - " : ""}${item.ma} - ${
                  item.ten
                }`
              }
              onChange={onChange("dsThuNganId")}
              value={_state.dsThuNganId}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("common.doiTuong")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonDoiTuongNguoiBenh")}
              onChange={onChange("doiTuong")}
              value={_state.doiTuong}
              data={listDoiTuong}
              hasAllOption={true}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.loaiDoiTuong")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonLoaiDoiTuong")}
              onChange={onChange("dsLoaiDoiTuongId")}
              value={_state.dsLoaiDoiTuongId}
              data={listAllLoaiDoiTuong}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("danhMuc.doiTuongKhamChuaBenh")}
            </label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonDoiTuongKhamChuaBenh")}
              onChange={onChange("dsDoiTuongKcb")}
              value={_state.dsDoiTuongKcb}
              mode="multiple"
              data={PHAN_LOAI_DOI_TUONG_KCB}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label
              className="label-filter cursor-pointer"
              onClick={() => openInNewTab("/danh-muc/loai-hinh-thanh-toan")}
            >
              {t("baoCao.loaiHinhThanhToan")}
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonLoaiHinhThanhToan")}
              data={listAllLoaiHinhThanhToan}
              onChange={onChange("dsLoaiHinhThanhToanId", true)}
              value={_state.dsLoaiHinhThanhToanId}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("danhMuc.phuongThucThanhToan")}
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonPhuongThucThanhToan")}
              onChange={onChange("dsPhuongThucTtId")}
              value={_state.dsPhuongThucTtId}
              data={listAllPhuongThucTT}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("thuNgan.tenNganHang")}</label>
            <Select
              className="input-filter"
              placeholder={t("thuNgan.chonNganHang")}
              data={listAllDoiTacThanhToan}
              onChange={onChange("dsNganHangId")}
              value={_state.dsNganHangId}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("danhMuc.caLamViec")}</label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonLoaiQuay")}
              onChange={onChange("dsCaLamViecId", true)}
              value={_state.dsCaLamViecId}
              defaultValue={_state.dsCaLamViecId}
              data={listAllCaLamViec}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.doiTuongThanhToanDichVu")}
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonDoiTuongThanhToanDichVu")}
              onChange={onChange("dsNhomLoaiDichVu")}
              value={_state.dsNhomLoaiDichVu}
              data={DOI_TUONG_THANH_TOAN_DICH_VU}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.taiTroBHBL")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonTaiTroBHBL")}
              onChange={onChange("hienThiTienTaiTro")}
              value={_state.hienThiTienTaiTro}
              data={TAI_TRO_BHBL}
              hasAllOption
            />
          </div>
        </Col>
      </Row>
    );
  };

  const handleDataSearch = ({ _state }) => {
    return {
      tuThoiGian: moment(_state.tuNgay).format("DD-MM-YYYY HH:mm:ss"),
      denThoiGian: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
      dsKhoaThucHienId: _state.dsKhoaThucHienId,
      dsKhoaChiDinhId: _state.dsKhoaChiDinhId,
      dsNhaThuNganId: _state.dsNhaThuNganId,
      dsQuayId: _state.dsQuayId,
      dsThuNganId: _state.dsThuNganId,
      doiTuong: _state.doiTuong,
      dsLoaiDoiTuongId: _state.dsLoaiDoiTuongId,
      dsDoiTuongKcb:
        _state.dsDoiTuongKcb && _state.dsDoiTuongKcb[0] != ""
          ? _state.dsDoiTuongKcb.flatMap(
              (item) => getPhanLoaiDoiTuongKcb(item)?.referIds || []
            )
          : null,
      hienThiTienTaiTro: _state.hienThiTienTaiTro,
      dsLoaiHinhThanhToanId: _state.dsLoaiHinhThanhToanId,
      dsPhuongThucTtId: _state.dsPhuongThucTtId,
      dsNganHangId: _state.dsNganHangId,
      dsCaLamViecId: _state.dsCaLamViecId,
      dsNhomLoaiDichVu: _state.dsNhomLoaiDichVu,
    };
  };

  return (
    <BaseBaoCao
      title={t("baoCao.tc59_1")}
      breadcrumb={[{ title: "TC59.1", link: "/bao-cao/tc59_1" }]}
      renderFilter={renderFilter}
      getBc={getTc59_1}
      handleDataSearch={handleDataSearch}
      initState={{
        dsKhoaThucHienId: [""],
        dsKhoaChiDinhId: [""],
        dsNhaThuNganId: [""],
        dsQuayId: [""],
        dsThuNganId: [""],
        doiTuong: [""],
        dsLoaiDoiTuongId: [""],
        dsDoiTuongKcb: [""],
        dsLoaiHinhThanhToanId: [""],
      }}
    />
  );
};

export default Index;
