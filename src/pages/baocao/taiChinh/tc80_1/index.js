import React from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { Col, Row } from "antd";

import { useLazyKVMap, useListAll, useQueryAll, useThietLap } from "hooks";

import { Select, DateTimePicker, Checkbox } from "components";
import BaseBaoCao from "pages/baocao/BaseBaoCao";
import { PHAN_LOAI_DOI_TUONG_KCB, THIET_LAP_CHUNG } from "constants/index";
import { query } from "redux-store/stores";
import { selectMaTen } from "redux-store/selectors";

const TC80_1 = () => {
  const { t } = useTranslation();
  const {
    baoCaoDaIn: { getTc80_1 },
  } = useDispatch();

  const [dataMA_THU_NGAN, finishGetMaThuNgan] = useThietLap(
    THIET_LAP_CHUNG.MA_THU_NGAN
  );
  const [listAllQuayTiepDon] = useListAll("quayTiepDon", { dsLoai: 20 }, true);
  const { data: listAllCaLamViec } = useQueryAll(
    query.caLamViec.queryAllCaLamViec
  );
  const { data: listAllToaNha } = useQueryAll(query.toaNha.queryAllToaNha);
  const { data: listThuNgan } = useQueryAll(
    query.nhanVien.queryAllNhanVien({
      params: {
        dsMaVaiTro: dataMA_THU_NGAN,
      },
      enabled: finishGetMaThuNgan,
    })
  );
  const [getPhanLoaiDoiTuongKcb] = useLazyKVMap(PHAN_LOAI_DOI_TUONG_KCB);
  const [listAllKhoa] = useListAll("khoa", {}, true);
  const [listAllPhuongThucTT] = useListAll("phuongThucTT", {}, true);

  const renderFilter = ({ onChange, _state }) => {
    return (
      <Row>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.tuNgay")}
              <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              className="input-filter"
              placeholder={t("common.chonNgay")}
              onChange={onChange("tuNgay")}
              value={_state.tuNgay}
              format="DD/MM/YYYY HH:mm:ss"
              showTime={{ defaultValue: moment().startOf("day") }}
            />
            {!_state.isValidData && !_state.tuNgay && (
              <div className="error">{t("baoCao.chonTuNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.denNgay")}
              <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              className="input-filter"
              placeholder={t("common.chonNgay")}
              value={_state.denNgay}
              onChange={onChange("denNgay")}
              format="DD/MM/YYYY HH:mm:ss"
              showTime={{ defaultValue: moment().endOf("day") }}
            />
            {!_state.isValidData && !_state.denNgay && (
              <div className="error">{t("baoCao.chonDenNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.thuNgan")}</label>
            <Select
              onChange={onChange("dsThuNganId")}
              value={_state.dsThuNganId}
              className="input-filter"
              placeholder={t("baoCao.chonThuNgan")}
              data={listThuNgan}
              getLabel={(item) =>
                `${item.taiKhoan || ""} - ${item.ma} - ${item.ten}`
              }
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("danhMuc.caLamViec")}</label>
            <Select
              className="input-filter"
              placeholder={t("thuNgan.chonCaLamViec")}
              onChange={onChange("dsCaLamViecId", true)}
              value={_state.dsCaLamViecId}
              defaultValue={_state.dsCaLamViecId}
              data={listAllCaLamViec}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.quayThu")}</label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonLoaiQuay")}
              onChange={onChange("dsQuayId", true)}
              value={_state.dsQuayId}
              data={listAllQuayTiepDon}
              mode="multiple"
              showArrow
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.nhaThuNgan")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonNhaThuNgan")}
              onChange={onChange("dsToaNhaId")}
              value={_state.dsToaNhaId}
              data={listAllToaNha}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.doiTuongKcb")}</label>
            <Select
              onChange={onChange("dsDoiTuongKcb", true)}
              value={_state.dsDoiTuongKcb}
              className="input-filter"
              placeholder={t("baoCao.chonDoiTuongKcb")}
              data={PHAN_LOAI_DOI_TUONG_KCB}
              hasAllOption={true}
              mode="multiple"
            />
          </div>
        </Col>
        <Col span={6}>
          <div className="item-select">
            <label className="label-filter cursor-pointer">
              {t("baoCao.khoaNguoiBenh")}
            </label>
            <Select
              mode="multiple"
              className="input-filter"
              placeholder={t("baoCao.chonKhoaNguoiBenh")}
              data={listAllKhoa}
              getLabel={selectMaTen}
              onChange={onChange("dsKhoaNbId", true)}
              value={_state.dsKhoaNbId}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("danhMuc.phuongThucThanhToan")}
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonPhuongThucThanhToan")}
              onChange={onChange("dsPhuongThucTtId")}
              value={_state.dsPhuongThucTtId}
              data={listAllPhuongThucTT}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.mauTongHop}
              onChange={onChange("mauTongHop")}
            >
              {t("baoCao.inBaoCaoTongHop")}
            </Checkbox>
          </div>
        </Col>
      </Row>
    );
  };

  const handleDataSearch = ({ _state }) => {
    return {
      tuThoiGian: moment(_state.tuNgay).format("DD-MM-YYYY HH:mm:ss"),
      denThoiGian: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
      dsThuNganId: _state.dsThuNganId,
      dsCaLamViecId: _state.dsCaLamViecId,
      dsQuayId: _state.dsQuayId,
      dsToaNhaId: _state.dsToaNhaId,
      dsDoiTuongKcb: _state.dsDoiTuongKcb
        ? _state.dsDoiTuongKcb.flatMap(
            (item) => getPhanLoaiDoiTuongKcb(item)?.referIds || []
          )
        : null,
      dsPhuongThucTtId: _state.dsPhuongThucTtId,
      dsKhoaNbId: _state.dsKhoaNbId,
      mauTongHop: _state.mauTongHop,
    };
  };

  return (
    <BaseBaoCao
      title={t("baoCao.tc80_1")}
      breadcrumb={[{ title: "TC80.1", link: "/bao-cao/tc80_1" }]}
      renderFilter={renderFilter}
      getBc={getTc80_1}
      initState={{
        dsQuayId: [""],
        dsThuNganId: [""],
        dsCaLamViecId: [""],
        dsToaNhaId: [""],
        inChiTiet: false,
      }}
      handleDataSearch={handleDataSearch}
    />
  );
};

export default TC80_1;
