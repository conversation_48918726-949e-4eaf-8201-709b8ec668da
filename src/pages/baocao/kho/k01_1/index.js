import { Col, message, Row } from "antd";
import { Checkbox, Select, SelectLoadMore } from "components";
import moment from "moment";
import { useTranslation } from "react-i18next";
import React, { useEffect, useMemo, useState } from "react";
import { useDispatch } from "react-redux";
import BaseBaoCao from "pages/baocao/BaseBaoCao";
import { Main } from "./styled";
import {
  LOAI_KHO,
  HINH_THUC_TRONG_NGOAI_THAU,
  LOAI_NHAP_XUAT,
  LOAI_DICH_VU,
  ENUM,
  CACHE_KEY,
} from "constants/index";
import { useCache, useEnum, useListAll, useStore } from "hooks";
import phieuNhapXuatProvider from "data-access/kho/phieu-nhap-xuat-provider";
import { select } from "redux-store/stores";
import khoTonKhoProvider from "data-access/kho/kho-ton-kho-provider";
import { selectMaTen } from "redux-store/selectors";
import { openInNewTab } from "utils/index";
import { FilterThoiGian } from "pages/baocao/BaseBaoCao/components";

/**
 * Báo cáo kho: K01.1 Bảng kê chi tiết nhập theo nhà cung cấp
 *
 */

const Index = () => {
  const { t } = useTranslation();
  const listAllNhaCungCap = useStore("doiTac.listAllNhaCungCap", []);
  const listKhoUser = useStore("kho.listKhoUser", []);

  const [listAllNhaXuatXu] = useListAll("xuatXu", {}, true);
  const [listAllPhanLoaiThuoc] = useListAll("phanLoaiThuoc", {}, true);
  const [listAllNhomDichVuKhoCap1] = useListAll(
    "nhomDichVuKho",
    {
      dsLoaiDichVu: [
        LOAI_DICH_VU.THUOC,
        LOAI_DICH_VU.VAT_TU,
        LOAI_DICH_VU.HOA_CHAT,
      ],
    },
    true,
    "NhomDichVuKhoCap1"
  );
  const [listLoaiNhapXuat] = useEnum(ENUM.LOAI_NHAP_XUAT);
  const [listLoaiThoiGianBaoCaoNhapXuatKho] = useEnum(
    ENUM.LOAI_THOI_GIAN_BAO_CAO_NHAP_XUAT_KHO
  );
  const listHinhThucTongHop = useStore("hinhThucNhapXuat.listTongHop", []);
  const [cacheSapXepTheoNgayDuyet, setCacheSapXepTheoNgayDuyet] = useCache(
    "",
    CACHE_KEY.SAP_XEP_THEO_NGAY_DUYET_K01,
    false,
    false
  );

  const {
    kho: { getTheoTaiKhoan: getKhoTheoTaiKhoan },
    baoCaoDaIn: { getk01_1 },
    doiTac: { getListAllNhaCungCap },
    quyetDinhThau: { getListAllQuyetDinhThau },
    hinhThucNhapXuat: { getTongHop },
  } = useDispatch();

  const [state, _setState] = useState({
    addParam: { loaiNhapXuat: LOAI_NHAP_XUAT.NHAP_TU_NCC },
  });
  const setState = (data = {}) => {
    _setState((state) => ({ ...state, ...data }));
  };

  useEffect(() => {
    const defaultParams = {
      page: "",
      size: "",
      active: true,
    };
    getKhoTheoTaiKhoan({
      ...defaultParams,
      dsLoaiKho: LOAI_KHO.NHAP_TU_NCC,
    });
    getListAllNhaCungCap(defaultParams);
    getListAllQuyetDinhThau(defaultParams);
    getTongHop({ ...defaultParams });
  }, []);

  const listLoaiThoiGianBaoCaoMemo = useMemo(() => {
    return listLoaiThoiGianBaoCaoNhapXuatKho.filter(
      (i) => i.id === 10 || i.id === 30
    );
  }, [listLoaiThoiGianBaoCaoNhapXuatKho]);

  const dsLoaiNhapXuat = useMemo(() => {
    return (listLoaiNhapXuat || []).filter((i) =>
      [LOAI_NHAP_XUAT.NHAP_TU_NCC, LOAI_NHAP_XUAT.NHAP_KHAC].includes(i.id)
    );
  }, [listLoaiNhapXuat]);

  const customChange = (key, onChange) => (value) => {
    if (key === "dsKhoId") {
      let addParam = { loaiNhapXuat: LOAI_NHAP_XUAT.NHAP_TU_NCC };
      if (Array.isArray(value) && value.length > 0 && value[0] != "") {
        addParam = { ...addParam, dsKhoId: value };
      }
      setState({
        addParam,
      });

      onChange("soHoaDon")();
      onChange("dsQuyetDinhThauId")([]);
    }
    if (key === "dsNhaCungCapId") {
      setState({ addParam: { ...state?.addParam, dsNhaCungCapId: value } });

      onChange("soHoaDon")();
      onChange("dsQuyetDinhThauId")([]);
    }

    onChange(key)(value);
  };

  const renderFilter = ({ onChange, _state, onKeyDownDate }) => {
    return (
      <>
        <Row>
          <FilterThoiGian
            t={t}
            onChange={onChange}
            _state={_state}
            onKeyDownDate={onKeyDownDate}
          />
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">
                {t("baoCao.kho")}
                <span className="icon-required">*</span>
              </label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonKho")}
                data={listKhoUser}
                onChange={customChange("dsKhoId", onChange)}
                value={_state.dsKhoId}
                defaultValue={_state.dsKhoId}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("danhMuc.nhaCungCap")}</label>
              <Select
                mode="multiple"
                showArrow
                onChange={customChange("dsNhaCungCapId", onChange)}
                value={_state.dsNhaCungCapId}
                className="input-filter"
                placeholder={t("danhMuc.chonNhaCungCap")}
                data={listAllNhaCungCap}
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">
                {t("baoCao.hinhThucTrongNgoaiThau")}
              </label>
              <Select
                onChange={customChange("trongThau", onChange)}
                value={_state.trongThau}
                className="input-filter"
                placeholder={t("baoCao.chonHinhThucTrongNgoaiThau")}
                data={HINH_THUC_TRONG_NGOAI_THAU}
                hasAllOption={true}
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            {!(_state.trongThau === false) && (
              <div className="item-select">
                <label className="label-filter">
                  {t("baoCao.quyetDinhThau")}
                </label>
                <Select
                  onChange={onChange("dsQuyetDinhThauId", true)}
                  value={_state.dsQuyetDinhThauId}
                  className="input-filter"
                  placeholder={t("baoCao.chonQuyetDinhThau")}
                  mode="multiple"
                  data={select.quyetDinhThau.listAllQuyetDinhThau}
                  getLabel={(item) => item.quyetDinhThau}
                />
              </div>
            )}
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.soHoaDon")}</label>
              <SelectLoadMore
                api={phieuNhapXuatProvider.searchAllByPut}
                mapData={(i) => ({
                  value: `${i.soHoaDon}-${i.id}`,
                  label: i.soHoaDon,
                })}
                onChange={onChange("soHoaDon", true)}
                value={_state.soHoaDon}
                keySearch={"soHoaDon"}
                placeholder={t("baoCao.chonSoHoaDon")}
                className="select input-filter"
                blurReset={true}
                mode="multiple"
                hasAll={true}
                addParam={state?.addParam}
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">
                {t("baoCao.phanLoaiHangHoa")}
              </label>
              <Select
                onChange={onChange("phanLoaiDvKhoId", true)}
                value={_state.phanLoaiDvKhoId}
                className="input-filter"
                placeholder={t("baoCao.chonPhanLoaiHangHoa")}
                data={listAllPhanLoaiThuoc}
                hasAllOption={true}
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select checkbox-pl">
              <Checkbox
                checked={_state.hienThiTenTrungThau}
                onChange={onChange("hienThiTenTrungThau")}
              >
                {t("baoCao.hienThiTenHangHoaTheoThau")}
              </Checkbox>
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.tenHangHoa")}</label>
              <SelectLoadMore
                api={khoTonKhoProvider.searchAll}
                mapData={(i) => ({
                  value: `${i.dichVuId}-${i.khoId}-${i.ten}`,
                  label: `${i.ma} - ${i.ten}`,
                })}
                onChange={onChange("dsDichVuId", true)}
                keySearch={"ten"}
                value={_state.dsDichVuId}
                className="input-filter"
                placeholder={t("baoCao.chonHangHoa")}
                addParam={{
                  active: true,
                  dsKhoId: _state.dsKhoId?.[0] ? _state.dsKhoId : undefined,
                }}
                hasAll={true}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">
                {t("baoCao.nhomDvKhoCap1")}
              </label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonNhomDvKhoCap1")}
                onChange={onChange("dsNhomDvKhoCap1Id")}
                value={_state.dsNhomDvKhoCap1Id}
                data={listAllNhomDichVuKhoCap1}
                getLabel={selectMaTen}
                hasAllOption={true}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("danhMuc.xuatXu")}</label>
              <Select
                mode="multiple"
                className="input-filter"
                placeholder={t("danhMuc.xuatXu")}
                data={listAllNhaXuatXu}
                onChange={onChange("dsXuatXuId", true)}
                getLabel={selectMaTen}
                value={_state.dsXuatXuId}
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.loaiHoaDon")}</label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonLoaiHoaDon")}
                data={dsLoaiNhapXuat}
                onChange={onChange("dsLoaiNhapXuat")}
                value={_state.dsLoaiNhapXuat}
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label
                className="label-filter cursor-pointer"
                onClick={() => openInNewTab("/danh-muc/hinh-thuc-nhap-xuat")}
              >
                {t("kho.hinhThucNhap")}
              </label>
              <Select
                className="input-filter"
                placeholder={t("kho.chonHinhThucNhap")}
                data={listHinhThucTongHop}
                onChange={onChange("dsHinhThucNhapXuatId", true)}
                value={_state.dsHinhThucNhapXuatId}
                mode={"multiple"}
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.loaiThoiGian")}</label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonLoaiThoiGian")}
                data={listLoaiThoiGianBaoCaoMemo}
                onChange={onChange("loaiThoiGian")}
                value={_state.loaiThoiGian}
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select checkbox-pl">
              <Checkbox
                checked={_state.nhomTheoNhaCungCap}
                onChange={onChange("nhomTheoNhaCungCap")}
              >
                {t("baoCao.nhomTheoNhaCungCap")}
              </Checkbox>
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select checkbox-pl">
              <Checkbox
                checked={_state.hienThiSoDangKy}
                onChange={onChange("hienThiSoDangKy")}
              >
                {t("baoCao.hienThiSoDangKy")}
              </Checkbox>
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select checkbox-pl">
              <Checkbox
                checked={_state.hienThiTenNguoiDuyet}
                onChange={onChange("hienThiTenNguoiDuyet")}
              >
                {t("baoCao.hienThiTenNguoiDuyet")}
              </Checkbox>
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select checkbox-pl">
              <Checkbox
                checked={_state.sapXepTheoNgayDuyet}
                onChange={(e) => {
                  setCacheSapXepTheoNgayDuyet(e.target.checked, false);
                  onChange("sapXepTheoNgayDuyet")(e);
                }}
              >
                {t("baoCao.sapXepTheoNgayDuyet")}
              </Checkbox>
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select checkbox-pl">
              <Checkbox
                checked={_state.nhomTheoSoHoaDon}
                onChange={onChange("nhomTheoSoHoaDon")}
              >
                {t("baoCao.nhomTheoSoHoaDon")}
              </Checkbox>
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select checkbox-pl">
              <Checkbox
                checked={_state.sapXepTheoSoPhieu}
                onChange={onChange("sapXepTheoSoPhieu")}
              >
                {t("baoCao.sapXepTheoSoPhieu")}
              </Checkbox>
            </div>
          </Col>
        </Row>
      </>
    );
  };

  const handleDataSearch = ({ _state }) => ({
    loaiThoiGian: _state.loaiThoiGian,
    tuNgay: moment(_state.tuNgay).format("DD-MM-YYYY HH:mm:ss"),
    denNgay: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
    dsKhoId:
      _state.dsKhoId[0] == ""
        ? listKhoUser.map((item) => item.id)
        : _state.dsKhoId,
    dsNhaCungCapId: _state.dsNhaCungCapId,
    trongThau: _state.trongThau,
    dsQuyetDinhThauId:
      _state.trongThau === false ? null : _state.dsQuyetDinhThauId,
    soHoaDon: _state.soHoaDon?.map((item) => item?.split("-")[0]),
    phanLoaiDvKhoId: _state.phanLoaiDvKhoId,
    hienThiTenTrungThau: _state.hienThiTenTrungThau,
    dsDichVuId: _state.dsDichVuId?.map((item) => item?.split("-")[0]),
    dsNhomDvKhoCap1Id: _state.dsNhomDvKhoCap1Id,
    dsXuatXuId: _state.dsXuatXuId,
    nhomTheoNhaCungCap: _state.nhomTheoNhaCungCap,
    hienThiSoDangKy: _state.hienThiSoDangKy,
    hienThiTenNguoiDuyet: _state.hienThiTenNguoiDuyet,
    dsLoaiNhapXuat: _state.dsLoaiNhapXuat,
    dsHinhThucNhapXuatId: _state.dsHinhThucNhapXuatId,
    sapXepTheoNgayDuyet: _state.sapXepTheoNgayDuyet,
    nhomTheoSoHoaDon: _state.nhomTheoSoHoaDon,
    sapXepTheoSoPhieu: _state.sapXepTheoSoPhieu,
  });

  const beforeOk =
    ({ _state, _beforeOk }) =>
    () => {
      if (!_state.dsKhoId || _state.dsKhoId?.length === 0) {
        message.error(t("baoCao.vuiLongChonKho"));
        return false;
      }
      return _beforeOk();
    };

  return (
    <Main>
      <BaseBaoCao
        title={t("baoCao.k01_1")}
        renderFilter={renderFilter}
        beforeOk={beforeOk}
        getBc={getk01_1}
        initState={{
          dsKhoId: [""],
          dsDichVuId: [""],
          dsNhomDvKhoCap1Id: [""],
          dsXuatXuId: [""],
          nhomTheoNhaCungCap: true,
          dsLoaiNhapXuat: [LOAI_NHAP_XUAT.NHAP_TU_NCC],
          sapXepTheoNgayDuyet: cacheSapXepTheoNgayDuyet,
          sapXepTheoSoPhieu: false,
        }}
        handleDataSearch={handleDataSearch}
        breadcrumb={[{ title: "K01.1", link: "/bao-cao/k01_1" }]}
      />
    </Main>
  );
};

export default Index;
