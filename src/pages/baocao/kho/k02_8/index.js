import React, { useEffect, useMemo } from "react";
import { Col, message, Row } from "antd";
import { useDispatch } from "react-redux";
import moment from "moment";
import { t } from "i18next";

import { useListAll, useEnum, useStore } from "hooks";

import { Checkbox, DateTimePicker, Select, SelectLoadMore } from "components";
import BaseBaoCao from "pages/baocao/BaseBaoCao";
import { LOAI_DICH_VU, ENUM } from "constants/index";
import { isArray } from "utils/index";
import khoTonKhoProvider from "data-access/kho/kho-ton-kho-provider";

/**
 * K02.8. Báo cáo nhập xuất tồn kho toàn viện
 *
 */

const K02_8 = () => {
  const [listLoaiDichVu] = useEnum(ENUM.LOAI_DICH_VU);
  const [listAllKhoTheoTaiKhoan] = useListAll(
    "kho",
    {},
    true,
    "KhoTheoTaiKhoan"
  );
  const listAllNhaSanXuat = useStore("doiTac.listAllNhaSanXuat", []);
  const listNhomDichVuKhoCap1 = useStore(
    "nhomDichVuKho.listAllNhomDichVuKhoCap1",
    []
  );
  const listAllNhomDichVuKhoCap2 = useStore(
    "nhomDichVuKho.listAllNhomDichVuKhoCap2",
    []
  );
  const listAllNhomDichVuKho = useStore(
    "phanNhomDichVuKho.listAllNhomDichVuKho",
    []
  );
  const listAllPhanLoaiThuoc = useStore(
    "phanLoaiThuoc.listAllPhanLoaiThuoc",
    []
  );

  const {
    baoCaoDaIn: { getK02_8 },
    doiTac: { getListAllNhaSanXuat },
    nhomDichVuKho: { getListAllNhomDichVuKhoCap1, getListAllNhomDichVuKhoCap2 },
    phanNhomDichVuKho: { getListAllNhomDichVuKho },
    phanLoaiThuoc: { getListAllPhanLoaiThuoc },
  } = useDispatch();

  useEffect(() => {
    const params = { page: "", size: "", active: true };
    getListAllNhaSanXuat({
      ...params,
      dsLoaiDoiTac: [10],
    });
    getListAllNhomDichVuKhoCap1({
      ...params,
      dsLoaiDichVu: [LOAI_DICH_VU.THUOC],
    });
    getListAllNhomDichVuKhoCap2({
      ...params,
      loaiDichVu: LOAI_DICH_VU.THUOC,
    });
    getListAllNhomDichVuKho({
      ...params,
      loaiDichVu: LOAI_DICH_VU.THUOC,
    });
    getListAllPhanLoaiThuoc(params);
  }, []);

  const listLoaiDv = useMemo(() => {
    let result = [];
    if (isArray(listLoaiDichVu, true)) {
      result = listLoaiDichVu.filter((i) =>
        [
          LOAI_DICH_VU.THUOC,
          LOAI_DICH_VU.VAT_TU,
          LOAI_DICH_VU.VAC_XIN,
          LOAI_DICH_VU.HOA_CHAT,
          LOAI_DICH_VU.CHE_PHAM_MAU,
          LOAI_DICH_VU.CHE_PHAM_DINH_DUONG,
        ].includes(i.id)
      );
    }
    return result;
  }, [listLoaiDichVu]);

  const renderFilter = ({ _state, onChange }) => {
    return (
      <Row>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.tuNgay")}
              <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              className="input-filter"
              placeholder={t("common.chonNgay")}
              value={_state.tuNgay}
              onChange={onChange("tuNgay")}
              format="DD/MM/YYYY HH:mm:ss"
              showTime={{ defaultValue: moment().startOf("day") }}
              disabledDate={(current) =>
                current && _state.denNgay && current > _state.denNgay
              }
            />
            {!_state.isValidData && !_state.tuNgay && (
              <div className="error">{t("baoCao.chonTuNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.denNgay")}
              <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              className="input-filter"
              placeholder={t("common.chonNgay")}
              value={_state.denNgay}
              onChange={onChange("denNgay")}
              format="DD/MM/YYYY HH:mm:ss"
              showTime={{ defaultValue: moment().endOf("day") }}
              disabledDate={(current) =>
                current && _state.tuNgay && current < _state.tuNgay
              }
            />
            {!_state.isValidData && !_state.denNgay && (
              <div className="error">{t("baoCao.chonDenNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.loaiDichVuKho")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonLoaiDichVuKho")}
              value={_state.dsLoaiDichVu}
              data={listLoaiDv}
              onChange={onChange("dsLoaiDichVu", true)}
              mode={"multiple"}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.kho")} <span className="icon-required">*</span>
            </label>
            <Select
              mode="multiple"
              className="input-filter"
              placeholder={t("baoCao.chonKho")}
              data={listAllKhoTheoTaiKhoan}
              onChange={(e) => {
                onChange("dsKhoId", true)(e);
                onChange("dsDichVuId")([]);
              }}
              value={_state.dsKhoId}
            />
            {!_state.isValidData && !isArray(_state.dsKhoId, 1) && (
              <div className="error">{t("baoCao.vuiLongChonKho")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.hangSanXuat")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonHangSanXuat")}
              data={listAllNhaSanXuat || []}
              onChange={onChange("dsNhaSanXuatId")}
              value={_state.dsNhaSanXuatId}
              mode={"multiple"}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("danhMuc.nhomThuocCap1")}</label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonNhomThuocCap1")}
              onChange={onChange("dsNhomDvKhoCap1Id")}
              value={_state.dsNhomDvKhoCap1Id}
              data={listNhomDichVuKhoCap1}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("danhMuc.nhomThuocCap2")}</label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonNhomThuocCap2")}
              onChange={onChange("dsNhomDvKhoCap2Id")}
              value={_state.dsNhomDvKhoCap2Id}
              data={listAllNhomDichVuKhoCap2}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("danhMuc.phanNhomThuoc")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonPhanLoaiHangHoa")}
              onChange={onChange("dsPhanNhomDvKhoId", true)}
              value={_state.dsPhanNhomDvKhoId}
              data={listAllNhomDichVuKho}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.phanLoaiThuoc")}</label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonPhanLoaiThuoc")}
              onChange={onChange("dsPhanLoaiDvKhoId", true)}
              value={_state.dsPhanLoaiDvKhoId}
              data={listAllPhanLoaiThuoc}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.tenHangHoa")}</label>
            <SelectLoadMore
              api={khoTonKhoProvider.searchAll}
              mapData={(i, idx) => ({
                value: `${i.dichVuId}-${i.khoId}-${idx}`,
                label: `${i.ma}-${i.ten}`,
              })}
              onChange={onChange("dsDichVuId", true)}
              keySearch={"timKiem"}
              value={_state.dsDichVuId}
              className="input-filter"
              placeholder={t("baoCao.chonHangHoa")}
              addParam={{
                active: true,
                dsKhoId: isArray(_state.dsKhoId, true)
                  ? _state.dsKhoId
                  : listAllKhoTheoTaiKhoan.map((i) => i.id),
              }}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.hienThiDonGia}
              onChange={onChange("hienThiDonGia")}
            >
              {t("baoCao.hienThiThanhTien")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.thuocQuanLyDacBiet}
              onChange={onChange("thuocQuanLyDacBiet")}
            >
              {t("baoCao.thuocQuanLyDacBiet")}
            </Checkbox>
          </div>
        </Col>
      </Row>
    );
  };

  const handleDataSearch = ({ _state }) => {
    let dsDichVuId = isArray(_state.dsDichVuId, 1)
      ? _state?.dsDichVuId.map((id) => id.split("-")[0])
      : [];
    return {
      tuThoiGian: moment(_state.tuNgay).format("DD-MM-YYYY HH:mm:ss"),
      denThoiGian: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
      dsKhoId: _state.dsKhoId,
      dsNhaSanXuatId: _state.dsNhaSanXuatId,
      dsLoaiDichVu: _state.dsLoaiDichVu,
      dsNhomDvKhoCap1Id: _state.dsNhomDvKhoCap1Id,
      dsNhomDvKhoCap2Id: _state.dsNhomDvKhoCap2Id,
      dsPhanNhomDvKhoId: _state.dsPhanNhomDvKhoId,
      dsPhanLoaiDvKhoId: _state.dsPhanLoaiDvKhoId,
      dsDichVuId,
      hienThiDonGia: _state.hienThiDonGia,
      thuocQuanLyDacBiet: _state.thuocQuanLyDacBiet,
    };
  };

  const beforeOk =
    ({ _state, _beforeOk }) =>
    () => {
      if (!isArray(_state.dsKhoId, true)) {
        message.error(t("baoCao.vuiLongChonKho"));
        return false;
      }
      return _beforeOk();
    };

  return (
    <BaseBaoCao
      title={t("baoCao.k02_8")}
      getBc={getK02_8}
      beforeOk={beforeOk}
      handleDataSearch={handleDataSearch}
      renderFilter={renderFilter}
      breadcrumb={[{ title: "K02.8", link: "/bao-cao/k02_8" }]}
    />
  );
};

export default K02_8;
