import React, { useEffect } from "react";
import { useDispatch } from "react-redux";
import { Col, Row, message } from "antd";
import moment from "moment";
import { t } from "i18next";
import { useEnum, useStore } from "hooks";
import { ENUM, LOAI_DOI_TAC } from "constants/index";
import { Select, DateTimePicker } from "components";
import BaseBaoCao from "pages/baocao/BaseBaoCao";
import { selectMaTen } from "redux-store/selectors";

const GIOI_TINH = [
  { id: 1, ten: "Nam", i18n: "common.nam" },
  {
    id: 2,
    ten: "Nữ",
    i18n: "common.nu",
  },
];

const LOAI_PHIEU_THU_KSK = [
  { id: "other", ten: "NB tự thanh toán", i18n: "baoCao.nbTuThanhToan" },
  {
    id: "10",
    ten: "NB thanh toán theo hợp đồng",
    i18n: "baoCao.nbThanhToanTheoHopDong",
  },
];

const KSK20 = () => {
  const {
    baoCaoDaIn: { getKsk20 },
    doiTac: { getListTongHop },
    hopDongKSK: { getListAllHopDongKSK },
  } = useDispatch();

  const listAllHopDongKSK = useStore("hopDongKSK.listAllHopDongKSK", []);
  const listDataTongHop = useStore("doiTac.listDataTongHop", []);
  const [listLoaiPhieuThu] = useEnum(ENUM.LOAI_PHIEU_THU);

  useEffect(() => {
    getListAllHopDongKSK({
      page: "",
      size: "",
      active: true,
    });
    getListTongHop({
      page: "",
      size: "",
      active: true,
      dsLoaiDoiTac: LOAI_DOI_TAC.CONG_TY_KSK,
    });
  }, []);

  const handleChange = (key, onChange) => (value) => {
    if (key === "doiTacId") {
      getListAllHopDongKSK({
        page: "",
        size: "",
        active: true,
        doiTacId: value,
      });
      onChange("dsHopDongKskId")();
    }
    onChange(key)(value);
  };

  const renderFilter = ({ onChange, _state }) => {
    return (
      <Row>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.tuNgay")}
              <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              className="input-filter"
              placeholder={t("common.chonNgay")}
              onChange={onChange("tuNgay")}
              value={_state.tuNgay}
              format="DD/MM/YYYY HH:mm:ss"
              showTime={{ defaultValue: moment().startOf("day") }}
            />
            {!_state.isValidData && !_state.tuNgay && (
              <div className="error">{t("baoCao.chonTuNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.denNgay")}
              <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              className="input-filter"
              placeholder={t("common.chonNgay")}
              onChange={onChange("denNgay")}
              value={_state.denNgay}
              format="DD/MM/YYYY HH:mm:ss"
              showTime={{ defaultValue: moment().endOf("day") }}
            />
            {!_state.isValidData && !_state.denNgay && (
              <div className="error">{t("baoCao.chonDenNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.congTyKsk")}
              <span className="icon-required">*</span>
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonCongTyKsk")}
              onChange={handleChange("doiTacId", onChange)}
              value={_state.doiTacId}
              data={listDataTongHop}
              getLabel={selectMaTen}
            />
            {!_state.isValidData && !_state.doiTacId && (
              <div className="error">{t("baoCao.vuiLongChonCongTyKsk")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.hopDongKsk")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonHopDongKsk")}
              onChange={onChange("dsHopDongKskId")}
              value={_state.dsHopDongKskId}
              data={listAllHopDongKSK}
              getLabel={selectMaTen}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.loaiPhieuThuKsk")}
              <span className="icon-required">*</span>
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonLoaiPhieuThuKsk")}
              onChange={onChange("dsLoaiPhieuThu")}
              value={_state.dsLoaiPhieuThu}
              data={LOAI_PHIEU_THU_KSK}
              mode="multiple"
            />
            {!_state.isValidData && !_state.dsLoaiPhieuThu?.length && (
              <div className="error">
                {t("baoCao.vuiLongChonLoaiPhieuThuKsk")}
              </div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("common.gioiTinh")}</label>
            <Select
              className="input-filter"
              placeholder={t("common.chonGioiTinh")}
              onChange={onChange("gioiTinh")}
              value={_state.gioiTinh}
              data={GIOI_TINH}
              hasAllOption={true}
            />
          </div>
        </Col>
      </Row>
    );
  };

  const handleDataSearch = ({ _state }) => ({
    tuThoiGian: moment(_state.tuNgay).format("DD-MM-YYYY HH:mm:ss"),
    denThoiGian: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
    doiTacId: _state.doiTacId,
    dsHopDongKskId: _state.dsHopDongKskId,
    dsLoaiPhieuThu:
      _state.dsLoaiPhieuThu?.length === LOAI_PHIEU_THU_KSK?.length
        ? null
        : _state.dsLoaiPhieuThu == "other"
        ? listLoaiPhieuThu.map((x) => x.id).filter((x) => x !== 10)
        : _state.dsLoaiPhieuThu,
    gioiTinh: _state.gioiTinh,
  });

  const beforeOk =
    ({ _state, _beforeOk }) =>
    () => {
      if (!_state.tuNgay) {
        message.error(t("baoCao.chonTuNgay"));
        return false;
      }
      if (!_state.denNgay) {
        message.error(t("baoCao.chonDenNgay"));
        return false;
      }
      if (!_state.doiTacId) {
        message.error(t("baoCao.vuiLongChonCongTyKsk"));
        return false;
      }
      if (!_state.dsLoaiPhieuThu?.length) {
        message.error(t("baoCao.vuiLongChonLoaiPhieuThuKsk"));
        return false;
      }
      return _beforeOk();
    };

  return (
    <BaseBaoCao
      title={t("baoCao.ksk20")}
      renderFilter={renderFilter}
      getBc={getKsk20}
      handleDataSearch={handleDataSearch}
      beforeOk={beforeOk}
      breadcrumb={[{ title: "KSK20", link: "/bao-cao/ksk-20" }]}
    />
  );
};

export default KSK20;
