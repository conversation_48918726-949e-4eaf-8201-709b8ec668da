import React, {
  memo,
  useEffect,
  useState,
  useRef,
  forwardRef,
  useImperativeHandle,
} from "react";
import { message } from "antd";
import {
  Checkbox,
  HeaderSearch,
  Button,
  ModalTemplate,
  TableWrapper,
  Select,
  Tooltip,
} from "components";
import { Main } from "./styled";
import benhPhamProvider from "data-access/categories/dm-benh-pham-provider";
import { useSelector, useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { cloneDeep } from "lodash";
import {
  DOI_TUONG,
  HOTKEY,
  THIET_LAP_CHUNG,
  LOAI_DICH_VU,
} from "constants/index";
import { useConfirm, useGuid, useListAll, useStore, useThietLap } from "hooks";
import Icon from "@ant-design/icons";
import { SVG } from "assets";
import moment from "moment";

const ModalBoSungThongTinDichVu = (props, ref) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const refModal = useRef(null);
  const refIsSubmit = useRef(null);
  const layerId = useGuid();
  const refFuncSubmit = useRef(null);
  const refCallback = useRef(null);
  const { onDichVuKemTheo } = props;
  const [state, _setState] = useState({
    show: false,
    data: [],
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const thongTinNguoiBenh = useStore(
    "chiDinhKhamBenh.configData.thongTinNguoiBenh",
    null
  );

  const {
    chiDinhKhamBenh: { chiDinhDichVu },
    phimTat: { onAddLayer, onRemoveLayer, onRegisterHotkey },
    benhPham: { getListAllBenhPham },
    logNguoiDung: { guiLog },
  } = useDispatch();
  const [dataBAT_BUOC_LOAI_HINH_THANH_TOAN] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_LOAI_HINH_THANH_TOAN
  );

  const listAllBenhPham = useSelector(
    (state) => state.benhPham.listAllBenhPham
  );
  const [listAllPhanTangNguyCo] = useListAll("phanTangNguyCo", {}, true);
  const mayTinhId = useStore("application.mayTinhId", "");
  const [dataThucHienCanBang] = useThietLap(
    THIET_LAP_CHUNG.THUC_HIEN_CAN_BANG_PHONG
  );

  useImperativeHandle(ref, () => ({
    show: ({ dataSource, isPhauThuat, isShowDvTiepDon = false }, onOk) => {
      if (!dataSource.length) return;
      let listErrorCode = [];

      const data = dataSource.map((item, idx) => {
        const nbDichVu = item.nbDichVu;
        const keyNguoiThucHien =
          nbDichVu?.loaiDichVu === LOAI_DICH_VU.KHAM
            ? "bacSiKhamId"
            : "nguoiThucHienId";

        listErrorCode = [...listErrorCode, item.code];
        return {
          boChiDinhId: item.boChiDinhId || undefined,
          benhPhamId: item.benhPhamId,
          phanTangNguyCoId: item.phanTangNguyCoId,
          ...(nbDichVu?.loaiDichVu == LOAI_DICH_VU.XET_NGHIEM
            ? { phongLayMauId: item.phongLayMauId }
            : {}),
          nbChanDoan: { cdSoBo: item.nbChanDoan?.cdSoBo },
          nbDichVu: {
            nguonKhacId: nbDichVu.nguonKhacId,
            dichVu: nbDichVu?.dichVu,
            dichVuId: nbDichVu?.dichVuId,
            soLuong: nbDichVu?.soLuong || 1,
            chiDinhTuDichVuId: nbDichVu?.chiDinhTuDichVuId,
            chiDinhTuLoaiDichVu: nbDichVu?.chiDinhTuLoaiDichVu,
            khoaChiDinhId: nbDichVu?.khoaChiDinhId,
            loaiDichVu: nbDichVu?.loaiDichVu,
            nbGoiDvId: nbDichVu?.nbGoiDvId || undefined,
            nbGoiDvChiTietId: nbDichVu?.nbGoiDvChiTietId || undefined,
            loaiHinhThanhToanId: nbDichVu?.loaiHinhThanhToanId || undefined,
            tyLeTtDv: nbDichVu?.tyLeTtDv,
            mucDichId: null,
          },
          nbDotDieuTriId: item.nbDotDieuTriId,
          nbDvKyThuat: {
            phongThucHienId: item?.boChiDinhId
              ? item?.phongId
              : item.nbDvKyThuat?.phongThucHienId,
            tuVanVienId: item.nbDvKyThuat?.tuVanVienId,
            capCuu: item?.nbDvKyThuat?.capCuu,
          },
          key: idx,
          stt: idx + 1,
          code: item.code,
          dsPhongThucHien: item.dsPhongThucHien,
          yeuCauBenhPham: item.yeuCauBenhPham,
          dsMucDich: item.dsMucDich,
          dsLoaiHinhThanhToan: item.dsLoaiHinhThanhToan,
          [keyNguoiThucHien]: item?.nguoiThucHienId,
          ...([LOAI_DICH_VU.PHAU_THUAT_THU_THUAT, LOAI_DICH_VU.CDHA].includes(
            nbDichVu?.loaiDichVu
          )
            ? {
                ptTtNguoiThucHien: item?.ptTtNguoiThucHien,
                phanLoaiPtTt: item?.phanLoaiPtTt,
                dieuDuongId: item?.dieuDuongId,
              }
            : {}),
          ...(nbDichVu?.loaiDichVu == LOAI_DICH_VU.XET_NGHIEM
            ? {
                phuThucHien1Id: item?.phuThucHien1Id,
                phuThucHien2Id: item?.phuThucHien2Id,
                phuThucHien3Id: item?.phuThucHien3Id,
                thanhVienKhacId: item?.thanhVienKhacId,
                nguoiThucHien2Id: item?.nguoiThucHien2Id,
              }
            : {}),
        };
      });
      setState({
        data,
        isPhauThuat,
        isShowDvTiepDon,
        show: true,
        listErrorCode: [...new Set(listErrorCode)],
      });
      refCallback.current = onOk;
    },
  }));

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show({});
      onAddLayer({ layerId: layerId });
      onRegisterHotkey({
        layerId: layerId,
        hotKeys: [
          {
            keyCode: HOTKEY.ESC, //ESC
            onEvent: () => {
              onCancel();
            },
          },
          {
            keyCode: HOTKEY.F4, //F4
            onEvent: () => {
              refFuncSubmit.current && refFuncSubmit.current();
            },
          },
        ],
      });
    } else {
      refModal.current && refModal.current.hide();
    }
    return () => {
      onRemoveLayer({ layerId: layerId });
    };
  }, [state.show]);

  const onCancel = () => {
    setState({
      show: false,
    });
  };
  const onShowModalWarning = (data) => {
    let item = data.filter((x) => x.data).map((x1) => x1.data);
    let messageWarning = item[item.length - 1]?.filter((x) => x.message);
    let content = messageWarning?.length
      ? messageWarning[messageWarning?.length - 1]?.message
      : null;
    content &&
      showConfirm(
        {
          title: t("common.canhBao"),
          content: content,
          cancelText: t("common.huy"),
          classNameOkText: "button-error",
          showImg: true,
          typeModal: "warning",
        },
        () => {}
      );
  };
  const onChange = (type, idx) => async (e) => {
    let value = e;
    if (e?.target) {
      value = e.target.checked;
    } else {
      let item = value;
      if (typeof item === "string") {
        const response = await benhPhamProvider.post({
          ten: item,
        });
        if (response.code === 0) {
          value = response.data.id;
          getListAllBenhPham();
        } else {
          value = item;
        }
      }
    }
    setState({
      data: state.data.map((item, index) => {
        if (idx === index) {
          if (type === "benhPhamId") item.benhPhamId = value;
          if (type === "phongThucHienId")
            item.nbDvKyThuat.phongThucHienId = value;
          if (type === "tuTra") item.nbDichVu.tuTra = value;
          if (type === "tyLeTtDv") item.nbDichVu.tyLeTtDv = value;
          if (type === "mucDichId") {
            item.nbDichVu.mucDichId = value;
            item.nbDichVu.loaiHinhThanhToanId = null; //reset loai hinh thanh toan khi thay doi muc dich
          }
          if (type === "loaiHinhThanhToanId")
            item.nbDichVu.loaiHinhThanhToanId = value;
          if (type === "phanTangNguyCoId") item.phanTangNguyCoId = value;
        }
        return item;
      }),
    });
  };

  const onSubmit = (e) => {
    if (refIsSubmit.current) return; //Nếu đang submit thì bỏ qua
    e.preventDefault();
    let isError = false;

    (state.data || []).some((item) => {
      if (item.yeuCauBenhPham && !item.benhPhamId) {
        message.error(t("khamBenh.chiDinh.thieuThongTinBenhPham"));
        isError = true;
        return true;
      }
      if (
        (item.dsPhongThucHien || []).length > 1 &&
        !item.nbDvKyThuat?.phongThucHienId
      ) {
        message.error(t("khamBenh.chiDinh.thieuThongTinPhongThucHien"));
        isError = true;
        return true;
      }

      if (!item?.nbDichVu?.mucDichId && item?.dsMucDich?.length) {
        message.error(t("khamBenh.chiDinh.thieuThongTinThongTu35"));
        isError = true;
        return true;
      }

      if (
        !item?.nbDichVu?.loaiHinhThanhToanId &&
        dataBAT_BUOC_LOAI_HINH_THANH_TOAN === "3"
      ) {
        message.error(t("khamBenh.chiDinh.thieuThongTinLoaiHinhThanhToan"));
        isError = true;
        return true;
      }
    });

    if (isError) return;

    if (dataThucHienCanBang?.eval()) {
      const noiDungLog = state.data.map((x) => ({
        phongId: x.phongId,
        dsPhongThucHien: x.dsPhongThucHien,
        dichVuId: x?.nbDichVu?.dichVuId,
      }));

      const data = {
        thoiGianThucHien: moment().format("YYYY-MM-DD HH:mm:ss"),
        tenMay: `${mayTinhId || ""}|${window.tabId}`,
        url: window.location.href,
        noiDung: `nbDotDieuTriId : ${thongTinNguoiBenh.id} |${JSON.stringify(
          noiDungLog
        )}`,
      };
      guiLog(data);
    }
    const updatedData = cloneDeep(state.data).map((item) => {
      delete item["key"];
      delete item["stt"];
      delete item["code"];
      delete item["dsPhongThucHien"];
      delete item["yeuCauBenhPham"];
      delete item["dsMucDich"];
      if (item.benhPhamId?.length) {
        item.benhPhamId = item.benhPhamId[item.benhPhamId.length - 1];
      }
      return item;
    });
    refIsSubmit.current = true;

    chiDinhDichVu({
      dataTamTinhTien: updatedData,
      isShowDvTiepDon: state.isShowDvTiepDon,
    })
      .then((options) => {
        const { listDichVuPending } = options || {};
        if (listDichVuPending?.length) {
          const ids = listDichVuPending
            .map((item) => item.nbDichVu?.dichVuId)
            .filter((id) => id);
          const data = state.data.filter((item) =>
            ids.includes(item.nbDichVu?.dichVuId)
          );
          if (data.length) {
            setState({ data: data });
          } else {
            onCancel();
          }
        } else {
          onCancel();
        }

        refCallback.current && refCallback.current();
        onShowModalWarning(options.response);
        onDichVuKemTheo && onDichVuKemTheo(options.response);
      })
      .finally(() => {
        refIsSubmit.current = false;
      });
  };
  refFuncSubmit.current = onSubmit;

  const onDelete = (record) => () => {
    const data = (state.data || []).filter((x) => x.key != record.key);
    setState({ data });

    if (data.length == 0) {
      onCancel();
    }
  };

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: "50px",
      dataIndex: "stt",
      align: "center",
      key: "stt",
    },
    {
      title: <HeaderSearch title={t("common.tenDichVu")} />,
      width: "250px",
      dataIndex: "tenDichVu",
      align: "left",
      key: "tenDichVu",
      render: (item, record) => record?.nbDichVu?.dichVu?.ten,
    },
    {
      title: <HeaderSearch title={t("khamBenh.chiDinh.benhPham")} />,
      width: "200px",
      dataIndex: "benhPhamId",
      key: "benhPhamId",
      render: (item, record, idx) => {
        return (
          <Select
            className={
              record?.yeuCauBenhPham && !record?.benhPhamId ? "error" : ""
            }
            value={record?.benhPhamId}
            disabled={!record?.yeuCauBenhPham}
            allowClear
            data={listAllBenhPham}
            showSearch={true}
            placeholder={t("common.luaChon")}
            onChange={onChange("benhPhamId", idx)}
          ></Select>
        );
      },
      className: "custom-col",
    },
    {
      title: <HeaderSearch title={t("khamBenh.chiDinh.phongThucHien")} />,
      width: "150px",
      dataIndex: "phongThucHienId",
      key: "phongThucHienId",
      className: "custom-col",
      render: (item, record, idx) => {
        const dataPhong = record?.dsPhongThucHien?.map((item) => ({
          id: item?.phongId,
          ten: `${item?.ma} - ${item?.ten}`,
          dichVuId: item.dichVuId,
        }));
        let value = record?.nbDvKyThuat?.phongThucHienId;
        if (!value && record?.dsPhongThucHien?.length == 1) {
          value = record?.dsPhongThucHien[0].phongId;
        }
        return (
          <Select
            value={value}
            className={
              record?.dsPhongThucHien?.length > 1 &&
              !record?.nbDvKyThuat?.phongThucHienId
                ? "select-border"
                : ""
            }
            disabled={record?.dsPhongThucHien?.length < 2}
            allowClear
            data={dataPhong}
            placeholder={t("common.luaChon")}
            onChange={onChange("phongThucHienId", idx)}
          />
        );
      },
    },
    {
      title: <HeaderSearch title="TT35" />,
      width: "150px",
      dataIndex: "mucDichId",
      key: "mucDichId",
      render: (item, record, idx) => {
        return (
          <Select
            data={record?.dsMucDich}
            placeholder={t("common.luaChon")}
            onChange={onChange("mucDichId", idx)}
            className={
              record?.dsMucDich?.length && !record?.nbDichVu?.mucDichId
                ? "select-border"
                : ""
            }
            value={record?.nbDichVu?.mucDichId}
          />
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("khamBenh.chiDinh.loaiHinhThanhToan")}
          isTitleCenter={true}
        />
      ),
      dataIndex: "loaiHinhThanhToanId",
      key: "loaiHinhThanhToanId",
      width: 150,
      render: (item, list, idx) => {
        const selectedMucDich = list?.dsMucDich?.find(
          (item) => item.id === list?.nbDichVu?.mucDichId
        );

        const dataSource = (list.dsLoaiHinhThanhToan || [])
          .reduce((acc, item) => {
            // nếu tt35 được chọn có loại hình thanh toán thì lọc theo loại hình thanh toán đó
            if (
              !selectedMucDich?.loaiHinhThanhToanId ||
              item.id === selectedMucDich.loaiHinhThanhToanId
            ) {
              acc.push(item);
            }
            return acc;
          }, [])
          .map((item) => ({
            id: item.loaiHinhThanhToanId,
            ten: item.tenLoaiHinhThanhToan,
          }));
        return (
          <div className="soluong" onClick={(event) => event.stopPropagation()}>
            <Select
              value={list?.nbDichVu?.loaiHinhThanhToanId}
              data={dataSource}
              onChange={onChange("loaiHinhThanhToanId", idx)}
              style={{ margin: "0px" }}
              className={
                !list?.nbDichVu?.loaiHinhThanhToanId &&
                dataBAT_BUOC_LOAI_HINH_THANH_TOAN === "3"
                  ? "select-border"
                  : ""
              }
            />
          </div>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("khamBenh.chiDinh.phanTangNguyCo")}
          isTitleCenter={true}
        />
      ),
      dataIndex: "phanTangNguyCoId",
      key: "phanTangNguyCoId",
      width: 150,
      render: (item, list, idx) => {
        return (
          <div className="soluong" onClick={(event) => event.stopPropagation()}>
            <Select
              value={item}
              data={listAllPhanTangNguyCo}
              onChange={onChange("phanTangNguyCoId", idx)}
              style={{ margin: "0px" }}
            />
          </div>
        );
      },
    },
    {
      title: <HeaderSearch title={t("common.tuTuc")} />,
      width: "50px",
      dataIndex: "tuTra",
      key: "tuTra",
      align: "center",
      render: (item, record, idx) => (
        <div className="check">
          <Checkbox
            onChange={onChange("tuTra", idx)}
            disabled={record?.dsMucDich}
          />
        </div>
      ),
    },
    {
      title: <HeaderSearch title={t("common.tienIch")} />,
      width: "50px",
      align: "center",
      render: (list, item, index) => {
        return (
          <div className="ic-action">
            <Tooltip title={t("tiepDon.xoaDichVu")} placement="bottom">
              <Icon
                className="ic-action"
                component={SVG.IcDelete}
                onClick={onDelete(item)}
              ></Icon>
            </Tooltip>
          </div>
        );
      },
    },
  ];
  const setRowClassName = (record) => {
    if (
      record?.dsMucDich?.length &&
      thongTinNguoiBenh?.doiTuong === DOI_TUONG.BAO_HIEM
    )
      return "row-tt35";
  };
  return (
    <ModalTemplate
      ref={refModal}
      title={t("khamBenh.chiDinh.boSungThongTin")}
      onCancel={onCancel}
      width={1400}
      actionRight={
        <>
          <Button type={"default"} onClick={onCancel} minWidth={100}>
            {t("common.huy")}
          </Button>
          <Button type="primary" onClick={onSubmit} minWidth={100}>
            {t("common.dongY")}
          </Button>
        </>
      }
    >
      <Main data={state?.data}>
        <div className="info-content">
          <TableWrapper
            scroll={{ y: 500, x: 700 }}
            columns={columns}
            dataSource={state.data}
            rowClassName={setRowClassName}
          />
        </div>
      </Main>
    </ModalTemplate>
  );
};

export default memo(forwardRef(ModalBoSungThongTinDichVu));
