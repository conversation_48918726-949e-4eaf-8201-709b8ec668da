import React, { forwardRef, useImperativeHandle, useRef } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>erS<PERSON>ch, TableWrapper } from "components";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { useStore } from "hooks";
import { cloneDeep } from "lodash";
import { isArray } from "utils/index";
import classNames from "classnames";

const { Column } = TableWrapper;

const ThongTinDichVuThuoc = (props, ref) => {
  const { t } = useTranslation();
  const refSettings = useRef(null);
  const {
    onListData = () => {},
    selectedRowKeys = [],
    isCheckedAll,
    dsThuocTrung,
    onShowCanhBaoKeTrung,
    listAllDsThuoc,
    getColorTuongTacThuoc,
  } = props;

  const onClickSort = (key, value) => {
    onSortChange({ [key]: value, isThuocKho: true });
  };
  const {
    dsThuoc: { onSortChange },
  } = useDispatch();

  const listDsThuoc = useStore("dsThuoc.listDsThuoc", []);
  const dataSortColumn = useStore("dsThuoc.dataSortColumn", {});

  const onSettings = () => {
    refSettings && refSettings.current.settings();
  };

  useImperativeHandle(ref, () => ({
    onSettings,
  }));

  const columns = [
    Column({
      title: t("common.stt"),
      width: "50px",
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
    }),
    Column({
      title: t("khamBenh.donThuoc.kho"),
      sort_key: "tenKho",
      dataSort: dataSortColumn["tenKho"] || "",
      onClickSort: onClickSort,
      width: "150px",
      dataIndex: "tenKho",
      key: "tenKho",
      i18Name: "khamBenh.donThuoc.kho",
    }),
    Column({
      title: t("hsba.tenThuoc"),
      sort_key: "tenDichVu",
      dataSort: dataSortColumn["tenDichVu"] || "",
      onClickSort: onClickSort,
      width: "300px",
      dataIndex: "tenDichVu",
      key: "tenDichVu",
      i18Name: "hsba.tenThuoc",
    }),

    Column({
      title: t("common.slSoCap"),
      sort_key: "soLuongSoCap",
      dataSort: dataSortColumn["soLuongSoCap"] || "",
      onClickSort: onClickSort,
      width: "100px",
      dataIndex: "soLuongSoCap",
      key: "soLuongSoCap",
      align: "center",
      i18Name: "common.slSoCap",
      render: (item, list) => {
        const tenDvt = list?.tenDvtSoCap || "";
        return <span>{`${item || 0} ${tenDvt}`}</span>;
      },
    }),
    Column({
      title: t("common.soLuong"),
      sort_key: "soLuong",
      dataSort: dataSortColumn["soLuong"] || "",
      onClickSort: onClickSort,
      width: "100px",
      dataIndex: "soLuong",
      key: "soLuong",
      align: "center",
      i18Name: "common.soLuong",
      show: false,
      render: (item, list) => {
        const tenDvt = list?.tenDonViTinh || "";
        return <span>{`${item || 0} ${tenDvt}`}</span>;
      },
    }),
    Column({
      title: t("common.soNgay"),
      sort_key: "soNgay",
      dataSort: dataSortColumn["soNgay"] || "",
      onClickSort: onClickSort,
      width: "100px",
      dataIndex: "soNgay",
      key: "soNgay",
      align: "center",
      i18Name: "common.soNgay",
    }),
    Column({
      title: t("common.lan/ngay"),
      sort_key: "soNgay",
      dataSort: dataSortColumn["soLan1Ngay"] || "",
      onClickSort: onClickSort,
      width: "100px",
      dataIndex: "soLan1Ngay",
      key: "soLan1Ngay",
      align: "center",
      i18Name: "common.lan/ngay",
    }),
    Column({
      title: t("common.sl/lan"),
      sort_key: "soLuong1Lan",
      dataSort: dataSortColumn["soLuong1Lan"] || "",
      onClickSort: onClickSort,
      width: "100px",
      dataIndex: "soLuong1Lan",
      key: "soLuong1Lan",
      align: "center",
      i18Name: "common.sl/lan",
    }),
    Column({
      title: t("common.cachDung"),
      sort_key: "cachDung",
      dataSort: dataSortColumn["cachDung"] || "",
      onClickSort: onClickSort,
      width: "200px",
      dataIndex: "cachDung",
      key: "cachDung",
      i18Name: "common.cachDung",
    }),
    Column({
      title: t("khamBenh.donThuoc.thoiDiemDung"),
      sort_key: "thoiDiem",
      dataSort: dataSortColumn["thoiDiem"] || "",
      onClickSort: onClickSort,
      width: "120px",
      dataIndex: "thoiDiem",
      key: "thoiDiem",
      i18Name: "khamBenh.donThuoc.thoiDiemDung",
    }),
    Column({
      title: t("common.duongDung"),
      sort_key: "tenDuongDung",
      dataSort: dataSortColumn["tenDuongDung"] || "",
      onClickSort: onClickSort,
      width: "100px",
      dataIndex: "tenDuongDung",
      key: "tenDuongDung",
      i18Name: "common.duongDung",
    }),
    Column({
      title: t("common.slHuy"),
      sort_key: "soLuongHuy",
      dataSort: dataSortColumn["soLuongHuy"] || "",
      onClickSort: onClickSort,
      width: "100px",
      dataIndex: "soLuongHuy",
      key: "soLuongHuy",
      align: "center",
      i18Name: "common.slHuy",
    }),
    Column({
      title: t("common.lyDoHuy"),
      sort_key: "lyDoHuy",
      dataSort: dataSortColumn["lyDoHuy"] || "",
      onClickSort: onClickSort,
      width: "180px",
      dataIndex: "lyDoHuy",
      key: "lyDoHuy",
      i18Name: "common.lyDoHuy",
    }),
    Column({
      title: t("common.dvtSuDung"),
      sort_key: "tenDvtSuDung",
      dataSort: dataSortColumn["tenDvtSuDung"] || "",
      onClickSort: onClickSort,
      width: "120px",
      dataIndex: "tenDvtSuDung",
      key: "tenDvtSuDung",
      i18Name: "common.dvtSuDung",
    }),
    Column({
      title: t("common.khongTinhTien"),
      sort_key: "khongTinhTien",
      dataSort: dataSortColumn["khongTinhTien"] || "",
      onClickSort: onClickSort,
      width: "80px",
      dataIndex: "khongTinhTien",
      key: "khongTinhTien",
      align: "center",
      i18Name: "common.khongTinhTien",
      render: (item) => <Checkbox checked={item} />,
    }),
    Column({
      title: t("common.tuTra"),
      sort_key: "tuTra",
      dataSort: dataSortColumn["tuTra"] || "",
      onClickSort: onClickSort,
      width: "80px",
      dataIndex: "tuTra",
      key: "tuTra",
      align: "center",
      i18Name: "common.tuTra",
      render: (item) => <Checkbox checked={item} />,
    }),

    Column({
      title: t("common.lieuDung"),
      sort_key: "tenLieuDung",
      dataSort: dataSortColumn["tenLieuDung"] || "",
      onClickSort: onClickSort,
      width: "250px",
      dataIndex: "tenLieuDung",
      key: "tenLieuDung",
      i18Name: "common.lieuDung",
      render: (item) => (
        <Tooltip title={item}>
          <div className="item">{item}</div>
        </Tooltip>
      ),
    }),
    Column({
      title: t("hsba.bsChiDinh"),
      sort_key: "tenBacSiChiDinh",
      dataSort: dataSortColumn["tenBacSiChiDinh"] || "",
      onClickSort: onClickSort,
      width: "200px",
      dataIndex: "tenBacSiChiDinh",
      key: "tenBacSiChiDinh",
      i18Name: "hsba.bsChiDinh",
    }),
    Column({
      title: t("khamBenh.donThuoc.dotDung"),
      sort_key: "dotDung",
      dataSort: dataSortColumn["dotDung"] || "",
      onClickSort: onClickSort,
      width: "90px",
      dataIndex: "dotDung",
      key: "dotDung",
      align: "center",
      i18Name: "khamBenh.donThuoc.dotDung",
    }),
    Column({
      title: t("common.luuY"),
      sort_key: "ghiChu",
      dataSort: dataSortColumn["ghiChu"] || "",
      onClickSort: onClickSort,
      width: "200px",
      dataIndex: "ghiChu",
      key: "ghiChu",
      i18Name: "common.luuY",
    }),
  ];

  const onSelectChange = (record, isSelected) => {
    let updatedSelectedKeys = cloneDeep(selectedRowKeys);
    if (isSelected) {
      if (
        isArray(dsThuocTrung, true) &&
        dsThuocTrung.some((y) => y.id === record.id)
      ) {
        onShowCanhBaoKeTrung([record]);
        return;
      }
      updatedSelectedKeys = [...updatedSelectedKeys, record.id];
    } else {
      updatedSelectedKeys = updatedSelectedKeys.filter(
        (item) => record.id !== item
      );
    }
    onListData(updatedSelectedKeys);
  };

  const oncheckAll = (e) => {
    let listData = isArray(dsThuocTrung, true)
      ? listDsThuoc.filter((x) => !dsThuocTrung.some((y) => y.id === x.id))
      : listDsThuoc;
    let data = e.target?.checked ? listData.map((item) => item.id) : [];
    onListData(data);
  };

  const rowSelection = {
    columnTitle: (
      <HeaderSearch
        title={
          <Checkbox onChange={oncheckAll} checked={isCheckedAll}></Checkbox>
        }
      />
    ),
    columnWidth: 40,
    onSelect: onSelectChange,
    selectedRowKeys: selectedRowKeys,
  };

  return (
    <TableWrapper
      columns={columns}
      dataSource={listDsThuoc}
      rowKey={(record) => record.id}
      styleWrap={{ height: 300 }}
      rowSelection={rowSelection}
      scroll={{ x: 2100 }}
      tableName="table_KHAMBENH_SaoChepThuoc"
      ref={refSettings}
      rowClassName={(record) => {
        const colorTuongTacThuoc = getColorTuongTacThuoc(
          record.dichVuId,
          listAllDsThuoc
        );
        return classNames({
          "table-row-severity-red": colorTuongTacThuoc === "red",
          "table-row-severity-orange": colorTuongTacThuoc === "orange",
        });
      }}
    />
  );
};

export default forwardRef(ThongTinDichVuThuoc);
