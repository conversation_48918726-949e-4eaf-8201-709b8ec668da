import React, { forwardRef, useImperativeHandle, useRef } from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";

import { useStore } from "hooks";

import { Checkbox, Tooltip, HeaderSearch, TableWrapper } from "components";
import classNames from "classnames";

const { Column } = TableWrapper;

const ThongTinDichVuThuocKeNgoai = (props, ref) => {
  const { t } = useTranslation();
  const refSettings = useRef(null);
  const {
    onListData = () => {},
    selectedRowKeys = [],
    isCheckedAll,
    listAllDsThuoc,
    getColorTuongTacThuoc,
  } = props;

  const onClickSort = (key, value) => {
    onSortChange({ [key]: value });
  };
  const {
    dsThuoc: { onSortChange },
  } = useDispatch();

  const listDsThuocKeNgoai = useStore("dsThuoc.listDsThuocKeNgoai", []);
  const dataSortColumn = useStore("dsThuoc.dataSortColumn", {});

  const onSettings = () => {
    refSettings && refSettings.current.settings();
  };

  useImperativeHandle(ref, () => ({
    onSettings,
  }));

  const columns = [
    Column({
      title: t("common.stt"),
      width: "50px",
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
    }),
    Column({
      title: t("hsba.tenThuoc"),
      sort_key: "tenThuocChiDinhNgoai",
      dataSort: dataSortColumn["tenThuocChiDinhNgoai"] || "",
      onClickSort: onClickSort,
      width: "300px",
      dataIndex: "tenThuocChiDinhNgoai",
      key: "tenThuocChiDinhNgoai",
      i18Name: "hsba.tenThuoc",
    }),
    Column({
      title: t("common.slSoCap"),
      sort_key: "slSoCap",
      dataSort: dataSortColumn["slSoCap"] || "",
      onClickSort: onClickSort,
      width: "100px",
      dataIndex: "slSoCap",
      key: "slSoCap",
      align: "center",
      i18Name: "common.slSoCap",
      render: (item, list) => {
        return <span>{`${item || 0}`}</span>;
      },
    }),
    Column({
      title: t("common.soLuong"),
      sort_key: "soLuong",
      dataSort: dataSortColumn["soLuong"] || "",
      onClickSort: onClickSort,
      width: "100px",
      dataIndex: "soLuong",
      key: "soLuong",
      align: "center",
      i18Name: "common.soLuong",
      show: false,
      render: (item, list) => {
        const tenDvt = list?.tenDonViTinh || "";
        return <span>{`${item || 0} ${tenDvt}`}</span>;
      },
    }),
    Column({
      title: t("common.soNgay"),
      sort_key: "soNgay",
      dataSort: dataSortColumn["soNgay"] || "",
      onClickSort: onClickSort,
      width: "100px",
      dataIndex: "soNgay",
      key: "soNgay",
      align: "center",
      i18Name: "common.soNgay",
    }),
    Column({
      title: t("common.cachDung"),
      sort_key: "cachDung",
      dataSort: dataSortColumn["cachDung"] || "",
      onClickSort: onClickSort,
      width: "200px",
      dataIndex: "cachDung",
      key: "cachDung",
      i18Name: "common.cachDung",
    }),
    Column({
      title: t("common.duongDung"),
      sort_key: "tenDuongDung",
      dataSort: dataSortColumn["tenDuongDung"] || "",
      onClickSort: onClickSort,
      width: "100px",
      dataIndex: "tenDuongDung",
      key: "tenDuongDung",
      i18Name: "common.duongDung",
    }),

    Column({
      title: t("common.lieuDung"),
      sort_key: "tenLieuDung",
      dataSort: dataSortColumn["tenLieuDung"] || "",
      onClickSort: onClickSort,
      width: "250px",
      dataIndex: "tenLieuDung",
      key: "tenLieuDung",
      i18Name: "common.lieuDung",
      render: (item) => (
        <Tooltip title={item}>
          <div className="item">{item}</div>
        </Tooltip>
      ),
    }),
    Column({
      title: t("hsba.bsChiDinh"),
      sort_key: "tenBacSiChiDinh",
      dataSort: dataSortColumn["tenBacSiChiDinh"] || "",
      onClickSort: onClickSort,
      width: "200px",
      dataIndex: "tenBacSiChiDinh",
      key: "tenBacSiChiDinh",
      i18Name: "hsba.bsChiDinh",
    }),
    Column({
      title: t("khamBenh.donThuoc.dotDung"),
      sort_key: "dotDung",
      dataSort: dataSortColumn["dotDung"] || "",
      onClickSort: onClickSort,
      width: "90px",
      dataIndex: "dotDung",
      key: "dotDung",
      align: "center",
      i18Name: "khamBenh.donThuoc.dotDung",
    }),
    Column({
      title: t("common.luuY"),
      sort_key: "ghiChu",
      dataSort: dataSortColumn["ghiChu"] || "",
      onClickSort: onClickSort,
      width: "200px",
      dataIndex: "ghiChu",
      key: "ghiChu",
      i18Name: "common.luuY",
    }),
  ];

  const onSelectChange = (selectedRowKeys, data) => {
    let updatedSelectedKeys = selectedRowKeys;
    updatedSelectedKeys = [...new Set(updatedSelectedKeys)];
    onListData(updatedSelectedKeys);
  };

  const oncheckAll = (e) => {
    let data = e.target?.checked
      ? listDsThuocKeNgoai.map((item) => item.id)
      : [];
    onListData(data);
  };

  const rowSelection = {
    columnTitle: (
      <HeaderSearch
        title={
          <Checkbox onChange={oncheckAll} checked={isCheckedAll}></Checkbox>
        }
      />
    ),
    columnWidth: 40,
    onSelect: onSelectChange,
    selectedRowKeys: selectedRowKeys,
  };

  return (
    <TableWrapper
      columns={columns}
      dataSource={listDsThuocKeNgoai}
      rowKey={(record) => record.id}
      styleWrap={{ height: 300 }}
      rowSelection={rowSelection}
      scroll={{ x: 2100 }}
      tableName="table_KHAMBENH_SaoChepThuocKeNgoai"
      ref={refSettings}
      rowClassName={(record) => {
        const colorTuongTacThuoc = getColorTuongTacThuoc(
          record.dichVuId,
          listAllDsThuoc
        );
        return classNames({
          "table-row-severity-red": colorTuongTacThuoc === "red",
          "table-row-severity-orange": colorTuongTacThuoc === "orange",
        });
      }}
    />
  );
};

export default forwardRef(ThongTinDichVuThuocKeNgoai);
