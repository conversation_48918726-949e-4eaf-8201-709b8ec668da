import React, {
  useEffect,
  useState,
  useRef,
  useMemo,
  useImperativeHandle,
  forwardRef,
} from "react";
import { useDispatch } from "react-redux";
import { getState } from "redux-store/stores";
import { Space, message, Menu } from "antd";
import { Main, GlobalStyle } from "./styled";
import { FRAME_TITLE } from "../../configs";
import printProvider, { printJS } from "data-access/print-provider";
import { flatten, orderBy } from "lodash";
import ModalInChiDinhTheoDV from "./ModalInChiDinhTheoDV";
import ModalInXacNhanThucHienTheoDV from "./ModalInXacNhanThucHienTheoDV";
import ModalLapBADaiHan from "./ModalLapBADaiHan";
import ModalGanMaBADaiHan from "./ModalGanMaBADaiHan";
import nbDvKyThuatProvider from "data-access/nb-dv-ky-thuat-provider.js";
import nbChiSoSongProvider from "data-access/nb-chi-so-song-provider";
import {
  Tooltip,
  Popover,
  Button,
  Dropdown,
  Radio,
  ModalSignPrint,
  InputTimeout,
  TableWrapper,
} from "components";
import { useTranslation } from "react-i18next";
import {
  useStore,
  useThietLap,
  useConfirm,
  useLoading,
  useEnum,
  useCache,
  useRefFunc,
  useListAll,
  useDelayedState,
} from "hooks";
import ModalHoiChanNgoaiTru from "./ModalHoiChan/DanhSachHoiChan";
import {
  TRANG_THAI_DICH_VU,
  ROLES,
  LOAI_IN,
  LIST_PHIEU_CHON_TIEU_CHI,
  MA_BIEU_MAU_EDITOR,
  LOAI_DICH_VU,
  THIET_LAP_CHUNG,
  LIST_PHIEU_CHON_TIEU_CHI_IN_THEO_SO_PHIEU,
  DS_TINH_CHAT_KHOA,
  ENUM,
  LOAI_BIEU_MAU,
  CACHE_KEY,
  DOI_TUONG_KCB,
  TRANG_THAI_NB,
  HOTKEY,
  DOI_TUONG,
  TRANG_THAI_XAC_NHAN_BHYT,
  TRANG_THAI_THANH_TOAN_QR,
  TRANG_THAI_PHIEU_THU_THANH_TOAN,
  LOAI_PHUONG_THUC_TT,
  LOAI_MH_PHU,
  LOAI_DANG_KY,
  VI_TRI_PHIEU_IN,
  MAN_HINH_PHIEU_IN,
  GIOI_TINH,
} from "constants/index";
import { checkRole } from "lib-utils/role-utils";
import {
  openInNewTab,
  isArray,
  containText,
  isBoolean,
  combineUrlParams,
} from "utils/index";
import ModalXemKetQuaPDF from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ToDieuTri/modals/ModalXemKetQuaPDF";
import ModalChonTieuChi from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ModalChonTieuChi";
import { SVG } from "assets";
import moment from "moment";
import ModalDoiDVPhongTH from "./ModalDoiDVPhongTH";
import ModalLichSuTiemChung from "pages/hoSoBenhAn/ChiTietNguoiBenh/containers/ModalLichSuTiemChung";
import ModalInToDieuTriNhieuNgay from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ToDieuTri/modals/ModalInToDieuTriNhieuNgay";
import ModalDienBienChiSoXetNghiem from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalDienBienChiSoXetNghiem";
import { useParams } from "react-router-dom";
import { checkIsPhieuKySo } from "utils/phieu-utils";
import { TRANG_THAI_PHIEU_NUMBER } from "pages/quanLyBaoCaoAdr/config";
import ModalChonPhieuCongKhaiVtyt from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ToDieuTri/modals/ModalChonPhieuCongKhaiVtyt";
import ModalThemMoiLao from "pages/quanLyDieuTriLao/DanhSachNbDangKyThuocLao/components/Modal/ModalThemMoi";
import ModalDieuTriNgoaiTru from "./ModalDieuTriNgoaiTru";
import ModalSoDoPhongGiuong from "pages/quanLyNoiTru/ModalSoDoPhongGiuong";
import ModalDoSinhHieu from "pages/sinhHieu/ModalDoSinhHieu";
import ModalInPhieuCongKhai from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalInPhieuCongKhai";
import ModalChonPhieuCongKhaiThuoc from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ToDieuTri/modals/ModalChonPhieuCongKhaiThuoc";
import ModalInPhieuSaoBA from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalInPhieuSaoBA";
import { createUniqueText, toSafePromise } from "lib-utils";
import ModalChonPhieuTruyenDich from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ToDieuTri/modals/ModalChonPhieuTruyenDich";
import ModalInPhieuTomTatBenhAn from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalInPhieuTomTatBenhAn";
import nbXacNhanBaoHiemProvider from "data-access/nb-xac-nhan-bao-hiem-provider";
import nbDotDieuTriProvider from "data-access/nb-dot-dieu-tri-provider";
import ModalCanhBaoLapBADaiHan from "./ModalCanhBaoLapBADaiHan";
import nbPhieuThuProvider from "data-access/nb-phieu-thu-provider";
import ModalTaoQrCode from "pages/thuNgan/quanLyTamUng/chiTietQuanLyTamUng/Modal/ModalTaoQrCode";
import ModalThongBaoThanhToanQrCode from "pages/thuNgan/chiTietPhieuThu/ModalThongBaoThanhToanQrCode";
import nbDichVuProvider from "data-access/nb-dich-vu-provider";
import ModalChonLoaiPHCN from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalChonLoaiPHCN";
import ModalPhieuBanGiaoNguoiBenh from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalPhieuBanGiaoNguoiBenh";
import { refModalHoSoBenhAn } from "pages/khamBenh";
import ModalDoiBacSiKham from "./ModalDoiBacSiKham";
import { showError } from "utils/message-utils";
import ModalChonKhoaPhieuTruyenDich from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ToDieuTri/modals/ModalChonKhoaPhieuTruyenDich";
import isofhToolProvider from "data-access/isofh-tool-provider";
import ModalChonKetQuaXetNghiem from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ToDieuTri/modals/ModalChonKetQuaXetNghiem";
import ModalLapBenhAn from "pages/quanLyNoiTru/danhSachLapBenhAn/ChiTietBenhAn/ModalLapBenhAn";
import ModalSuaThoiGianDvKham from "./ModalSuaThoiGianDvKham";
import ModalInPhieuCamDoanChapNhanPTTT from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalInPhieuCamDoanChapNhanPTTT";
import ModalInGiayNghiHuongBHXH from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalInGiayNghiHuongBHXH";
import ModalLichSuPhongKham from "./ModalLichSuPhongKham";
import useListDieuTriKetHop from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/hooks/useListDieuTriKetHop";
import ModalSuaThoiGianKetThucKham from "./ModalSuaThoiGianKetThucKham";
import ModalInPhieuTienMe from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalInPhieuTienMe";
import { parseKiemTraCongKhiInBangKeBHYT } from "utils/thiet-lap-chung-utils";
import ModalCheckBaoHiem from "pages/tiepDon/components/ThongTinTiepDon/ModalCheckBaoHiem";

const sleep = (ms) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

const { ModalImport } = TableWrapper;

const StepWrapper = (
  {
    customHeaderRight,
    children,
    activeTab,
    handleShowModalChuyenKhoa,
    layerId,
  },
  ref
) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const { showLoading, hideLoading } = useLoading();
  const thongTinChiTiet = useStore("khamBenh.thongTinChiTiet", {});
  const nhanVienId = useStore("auth.auth.nhanVienId");
  const infoNb = useStore("khamBenh.infoNb", {});
  const configData = useStore("chiDinhKhamBenh.configData", null);
  const listNbGoiDv = useStore("nbGoiDv.listNbGoiDv", []);
  const thongTinBenhNhan = useStore("nbDotDieuTri.thongTinBenhNhan");
  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan");
  const listPhongKham = useStore("khamBenh.listPhongKham", []);
  const phieuChiDinhKhamBenh = useStore("phieuIn.phieuChiDinhKhamBenh", null);
  const phieuTongHopKhamBenh = useStore("phieuIn.phieuTongHopKhamBenh", null);
  const phieuKqCls = useStore("phieuIn.phieuKqCls", null);
  const listDataTongHop = useStore("khoa.listDataTongHop");
  const dsMaBADaiHan = useStore("nbMaBenhAn.dsMaBADaiHan", []);
  const dsTrangThaiPhcn = useStore("dieuTriPHCN.dsTrangThaiPhcn", []);

  const [openPopoverPhieuChiDinh, setOpenPopoverPhieuChiDinh] = useState(false);
  const [openPopoverPhieuChiDinh1, setOpenPopoverPhieuChiDinh1] =
    useState(false);
  const [openPopoverPhieuXacNhanThucHien, setOpenPopoverPhieuXacNhanThucHien] =
    useState(false);

  const refModalLichSuTiemChung = useRef(null);
  const refModalDienBienChiSoXetNghiem = useRef(null);
  const refDieuTriNgoaiTru = useRef(null);
  const { phongThucHienId } = useParams();
  const refModalSoDoPhongGiuong = useRef(null);
  const refModalPhieuBanGiaoNguoiBenh = useRef(null);
  const refModalInPhieuCamDoanChapNhanPTTT = useRef(null);
  const refModalInGiayNghiHuongBHXH = useRef(null);
  const refModalLichSuPhongKham = useRef(null);
  const refScrollDiv = useRef(null);
  const isKsk = useMemo(() => {
    return infoNb?.khamSucKhoe || infoNb?.loaiDoiTuongKsk;
  }, [infoNb]);
  const refModalSignPrint = useRef(null);
  const refModalInChiDinhTheoDV = useRef(null);
  const refModalInXacNhanThucHienTheoDV = useRef(null);
  const refModalXemKetQuaPDF = useRef(null);
  const refModalLapBADaiHan = useRef(null);
  const refModalGanMaBADaiHan = useRef(null);
  const refModalHoiChanNgoaiTru = useRef(null);
  const refModalChonTieuChi = useRef(null);
  const refContentPhieuChiDinh = useRef(null);
  const refModalInToDieuTri = useRef(null);
  const refModalChonPhieuCongKhaiVtyt = useRef(null);
  const refModalDangKyLao = useRef(null);
  const refModalDoSinhHieu = useRef(null);
  const refModalInPhieuCongKhai = useRef(null);
  const refModalChonPhieuCongKhaiThuoc = useRef(null);
  const refModalInPhieuSaoBA = useRef(null);
  const refModalChonPhieuTruyenDich = useRef(null);
  const refModalImportThongTinKskHopDong = useRef(null);
  const refModalInPhieuThoiGianInKhoaChiDinh = useRef(null);
  const refModalInPhieuTomTatBA = useRef(null);
  const refModalCanhBaoLapBADaiHan = useRef(null);
  const refModalTaoQrCode = useRef(null);
  const refModalThongBaoThanhToanQrCode = useRef(null);
  const refCurrentMsg = useRef(null);
  const refTimeout = useRef(null);
  const refTimeoutQr = useRef(null);
  const refModalChonLoaiPHCN = useRef(null);
  const refModalDoiBacSiKham = useRef(null);
  const refModalChonKhoaPhieuTruyenDich = useRef(null);
  const refModalChonKetQuaXetNghiem = useRef(null);
  const refModalLapBenhAn = useRef(null);
  const refModalSuaThoiGianDvKham = useRef(null);
  const refModalSuaThoiGianKetThucKham = useRef(null);
  const refModalInPhieuTienMe = useRef(null);
  const refModalCheckBaoHiem = useRef();

  const [refIsChange, onChangeStateUpdate] = useDelayedState(700);

  const [CANH_BAO_NGHI_HUONG_BHXH] = useThietLap(
    THIET_LAP_CHUNG.CANH_BAO_NGHI_HUONG_BHXH
  );
  const [LINK_KHAM_BENH_PM_GUT] = useThietLap(
    THIET_LAP_CHUNG.LINK_KHAM_BENH_PM_GUT
  );
  const [LINK_DO_SINH_HIEU_GUT] = useThietLap(
    THIET_LAP_CHUNG.LINK_DO_SINH_HIEU_GUT
  );
  const [LINK_KE_DON_THUOC_PM_GUT] = useThietLap(
    THIET_LAP_CHUNG.LINK_KE_DON_THUOC_PM_GUT
  );

  const [HIEN_THI_DV_TU_TIEP_DON_VAO_PHIEU_CHI_DINH_TONG_HOP_IN_O_KHAM_BENH] =
    useThietLap(
      THIET_LAP_CHUNG.HIEN_THI_DV_TU_TIEP_DON_VAO_PHIEU_CHI_DINH_TONG_HOP_IN_O_KHAM_BENH
    );

  const KHONG_TU_DONG_DONG_TAB =
    useThietLap(THIET_LAP_CHUNG.KHONG_TU_DONG_DONG_TAB)[0].toLowerCase() ===
    "true";
  const [dataBO_BAT_BUOC_CHON_PHAN_LOAI_KHI_DANG_KY_PHCN] = useThietLap(
    THIET_LAP_CHUNG.BO_BAT_BUOC_CHON_PHAN_LOAI_KHI_DANG_KY_PHCN
  );

  const [dataIN_QR_TAM_UNG_THANH_TOAN_TREN_PHIEU] = useThietLap(
    THIET_LAP_CHUNG.IN_QR_TAM_UNG_THANH_TOAN_TREN_PHIEU
  );
  const [dataKHAM_BENH_XAC_NHAN_BHYT_TU_DONG_IN_MA_PHIEU] = useThietLap(
    THIET_LAP_CHUNG.KHAM_BENH_XAC_NHAN_BHYT_TU_DONG_IN_MA_PHIEU
  );
  const [dataCHO_IN_BANG_KE_TIEN_ICH_NGUOI_BENH_NGOAI_TRU] = useThietLap(
    THIET_LAP_CHUNG.CHO_IN_BANG_KE_TIEN_ICH_NGUOI_BENH_NGOAI_TRU
  );
  const [dataKHONG_TU_DONG_DONG_TAB] = useThietLap(
    THIET_LAP_CHUNG.KHONG_TU_DONG_DONG_TAB,
    "false"
  );
  const [dataIN_PHIEU_CHI_DINH_DV_TU_TIEP_DON_VA_DV_TRONG_GOI_KSK_O_KHAM_BENH] =
    useThietLap(
      THIET_LAP_CHUNG.IN_PHIEU_CHI_DINH_DV_TU_TIEP_DON_VA_DV_TRONG_GOI_KSK_O_KHAM_BENH
    );

  const [dataIN_TAT_CA_PHIEU_CHI_DINH_1_LAN] = useThietLap(
    THIET_LAP_CHUNG.IN_TAT_CA_PHIEU_CHI_DINH_1_LAN,
    "false"
  );
  const [dataMAC_DINH_IN_DV_CHUA_THANH_TOAN_PHIEU_CHI_DINH_TONG_HOP] =
    useThietLap(
      THIET_LAP_CHUNG.MAC_DINH_IN_DV_CHUA_THANH_TOAN_PHIEU_CHI_DINH_TONG_HOP,
      "FALSE"
    );
  const [dataNHOM_GIAI_PHAU_BENH] = useThietLap(
    THIET_LAP_CHUNG.NHOM_GIAI_PHAU_BENH
  );
  const [dataKIEM_TRA_CONG_KHI_IN_BANG_KE_BHYT] = useThietLap(
    THIET_LAP_CHUNG.KIEM_TRA_CONG_KHI_IN_BANG_KE_BHYT
  );

  const [listTrangThaiDichVu] = useEnum(ENUM.TRANG_THAI_DICH_VU);
  const [listNgonNgu] = useEnum(ENUM.NGON_NGU, []);

  const [valuePhieuXacNhanThucHien, setValuePhieuXacNhanThucHien] = useCache(
    "",
    CACHE_KEY.KHAM_BENH_OPTION_IN_PHIEU_XAC_NHAN_THUC_HIEN,
    1
  );

  const [valuePhieuChiDinh, setValuePhieuChiDinh] = useCache(
    "",
    CACHE_KEY.KHAM_BENH_OPTION_IN_PHIEU_CHI_DINH,
    dataMAC_DINH_IN_DV_CHUA_THANH_TOAN_PHIEU_CHI_DINH_TONG_HOP?.eval() ? 4 : 1
  );

  useEffect(() => {
    setState({ valuePhieuChiDinhNew: valuePhieuChiDinh || 2 });
    // SAKURA-62553 > mặc định thì là giá trị đã chọn trước đó nếu chưa có thì mặc định là 2 (chỉ định chưa in)
    //
  }, [valuePhieuChiDinh, thongTinChiTiet.id]);

  const [listAllPhuongThucThanhToan] = useListAll("phuongThucTT", {}, true);
  const { loaiPhcnId, loaiYhctId } = useListDieuTriKetHop();

  const refDoiDVPhongTH = useRef(null);
  const {
    khamBenh: {
      huyKham,
      getTatCaGiayChiDinh,
      importThongTinKskHopDong,
      getNbDvKham,
      updateData: updateDataKhamBenh,
      getNbDotDieuTriTongHopTheoId,
    },
    phieuIn: {
      getListPhieu,
      getDataDanhSachPhieu,
      showFileEditor,
      getFilePhieuIn,
      getPhieuIn,
      updateData,
      getPhieuInTheoDsMa,
    },
    nbGoiDv: { getByNbThongTinId },
    giayNghiHuong: { dayGiayNghiBaoHiemById },
    quanLyNoiTru: { getTieuChi },
    khoa: { getKhoaTheoTaiKhoan, getListKhoaTongHop },
    nbDotDieuTri: { getThongTinCoBan, getByTongHopId, getById },
    nbMaBenhAn: { getDsMaBADaiHan },
    quanLyBaoCaoAdr: { taoMoiBaoCaoAdr, searchBaoCaoByMaHoSo },
    vitalSigns: { onCreate },
    quanLyDieuTriLao: { capNhatDangKy, getThuocLaoById },
    phimTat: { onRegisterHotkey },
    deNghiTamUng: { getDeNghiTamUngMoiNhat },
    thuNgan: { kiemTraTrangThaiThanhToanQr },
    dieuTriPHCN: { getTrangThaiPhcn, huyDangKyDotPHCN, dangKyDotPHCN },
    tiepDon: { giamDinhThe, onUpdate },
  } = useDispatch();

  const [state, _setState] = useState({
    visible: false,
    listDichVu: [],
    listSelectedDv: [],
    listGoiDv: [],
    isCheckAll: false,
    indeterminate: false,
    loadingChiDinh: false,
    isGoiDichVu: false,
    thanhTien: 0,
    listAllDichVu: [],
    nhatKyDieuTriVisible: false,
    nbPhieuAdr: null,
    qrThanhToan: null,
    valuePhieuChiDinhNew: 2,
  });

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    getKhoaTheoTaiKhoan({ page: "", size: "" });
    getListKhoaTongHop({
      page: "",
      size: "",
      active: true,
      dsTinhChatKhoa: DS_TINH_CHAT_KHOA.NOI_TRU,
    });
  }, []);

  useEffect(() => {
    onRegisterHotkey({
      layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.F2,
          onEvent: () => {
            onGanMaBADaiHan();
          },
          ctrlKey: true,
        },
        {
          keyCode: HOTKEY.F3,
          ctrlKey: true,
          onEvent: () => {
            onXacNhanBhyt();
          },
        },
        {
          keyCode: HOTKEY.F4,
          onEvent: () => {
            onDienBienChiSoXetNghiem();
          },
        },
      ],
    });
  }, []);

  const listKhoaTheoTaiKhoan = useStore("khoa.listKhoaTheoTaiKhoan", []);

  useEffect(() => {
    if (
      configData &&
      checkRole([ROLES.GOI_DICH_VU.DANH_SACH_NB_SU_DUNG_GOI_DICH_VU])
    ) {
      getByNbThongTinId({
        nbThongTinId: configData.nbThongTinId,
      });
    }
  }, [configData]);

  useEffect(() => {
    if (thongTinChiTiet?.nbDotDieuTriId && thongTinChiTiet?.id) {
      const params = {
        nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
        chiDinhTuDichVuId: thongTinChiTiet.id,
        maManHinh: MAN_HINH_PHIEU_IN.KHAM_BENH,
      };

      getListPhieu({
        ...params,
        dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
        maViTri: VI_TRI_PHIEU_IN.KHAM_BENH.IN_GIAY_TO,
      }).then((listPhieu) => {
        updateData({
          phieuChiDinhKhamBenh: (listPhieu || []).find((i) => i.ma === "P048"),
          phieuKqCls: (listPhieu || []).find((i) => i.ma === "P1137"),
        });
        setState({ listPhieu });
      });

      getListPhieu({
        ...params,
        maViTri: VI_TRI_PHIEU_IN.KHAM_BENH.IN_TAT_CA_GIAY_TO,
      }).then((listPhieu) => {
        updateData({
          phieuTongHopKhamBenh: (listPhieu || []).find((i) => i.ma === "P002"),
        });
      });

      clearFunc(true);
    }
  }, [thongTinChiTiet?.nbDotDieuTriId, thongTinChiTiet?.id]);

  useEffect(() => {
    //nam.mn thêm check quyền ở đây để không gọi khi không có quyền, hiện tại api nb-phieu-adr/tong-hop đang yêu cầu quyền 4000101
    if (
      infoNb?.maHoSo &&
      checkRole([ROLES.QUAN_LY_BAO_CAO_ADR.DANH_SACH_BAO_CAO_ADR])
    ) {
      searchBaoCaoByMaHoSo({ maHoSo: infoNb?.maHoSo }).then((res) => {
        if (res.length > 0) {
          setState({ nbPhieuAdr: res[0] });
        } else {
          setState({ nbPhieuAdr: null });
        }
      });
    }
  }, [infoNb?.maHoSo]);

  useEffect(() => {
    if (thongTinBenhNhan?.nbThongTinId) {
      getDsMaBADaiHan(thongTinBenhNhan?.nbThongTinId);
    }
  }, [thongTinBenhNhan]);

  useEffect(() => {
    return () => {
      clearFunc(true);
      updateData({
        phieuChiDinhKhamBenh: null,
        phieuTongHopKhamBenh: null,
        phieuKqCls: null,
      });
    };
  }, []);

  const handleBeforeUnLoad = useRefFunc(() => {
    if (dataIN_QR_TAM_UNG_THANH_TOAN_TREN_PHIEU?.eval()) {
      clearFunc(true);
    }
  });

  useEffect(() => {
    window.addEventListener("beforeunload", handleBeforeUnLoad);
    return () => {
      window.removeEventListener("removeEventListener", handleBeforeUnLoad);
    };
  }, []);

  useEffect(() => {
    refScrollDiv.current && refScrollDiv.current.scrollTo(0, 0);
  }, [activeTab]);

  const fetchTienIchKhac = async () => {
    if (!thongTinChiTiet?.nbDotDieuTriId) return;

    await getTrangThaiPhcn({
      nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
    });
  };

  useImperativeHandle(ref, () => ({
    onPrintDonThuoc,
    onInNhanhPhieuChiDinh,
    onInNhanhPhieuKqCls,
  }));
  // --------------------------------------------------------------------------------------------------------------------------------
  const onRenderFrameTitle = () => {
    if (activeTab === 0) {
      return thongTinChiTiet?.nbDichVu?.dichVu?.ten;
    }
    return t(FRAME_TITLE[activeTab]);
  };

  const trangThaiKham = useMemo(() => {
    if (
      !listTrangThaiDichVu?.length ||
      !thongTinChiTiet?.nbDvKyThuat?.trangThai
    )
      return null;
    const trangThai = listTrangThaiDichVu?.find(
      (o) => o.id === thongTinChiTiet?.nbDvKyThuat?.trangThai
    )?.ten;
    if (!trangThai) return null;
    return ` - ${t("common.trangThai")}: ${trangThai}`;
  }, [listTrangThaiDichVu, thongTinChiTiet?.nbDvKyThuat?.trangThai]);

  const isBADaiHan = useMemo(() => {
    if (thongTinBenhNhan?.maBenhAn) {
      const regexBADaiHan = /^([A-Za-z]{1}).*[0-9]{9}$/;

      return regexBADaiHan.test(thongTinBenhNhan?.maBenhAn);
    }
    return false;
  }, [thongTinBenhNhan?.maBenhAn]);

  const listMaBaDaiHan = useMemo(() => {
    return dsMaBADaiHan.filter(
      (i) => i.trangThai === TRANG_THAI_NB.DANG_DIEU_TRI
    );
  }, [dsMaBADaiHan]);

  const { kiemTra, dsManHinh } = useMemo(() => {
    if (dataKIEM_TRA_CONG_KHI_IN_BANG_KE_BHYT) {
      return parseKiemTraCongKhiInBangKeBHYT(
        dataKIEM_TRA_CONG_KHI_IN_BANG_KE_BHYT
      );
    }

    return {};
  }, [dataKIEM_TRA_CONG_KHI_IN_BANG_KE_BHYT]);

  // --------------------------------------------------------------------------------------------------------------------------------
  const onPrint = async () => {
    let res = await getTatCaGiayChiDinh({
      nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
      chiDinhTuDichVuId: thongTinChiTiet.id,
      loai: 1,
      chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
    });
    let flatList = flatten(res);
    const getDsPdf = (dsPhieu) => {
      return flatten(dsPhieu.map((item) => item.filePdf));
    };

    if (flatList.length == 0) {
      message.error(t("khamBenh.khongTonTaiPhieuChiDinh"));
      return;
    }
    let dsFilePdf = [];
    if (
      flatList.some((item) =>
        [LOAI_IN.IN_NHANH, LOAI_IN.IN_NHANH_PREVIEW].includes(item.loaiIn)
      )
    ) {
      let res2 = await printProvider.printPdf(flatList, {
        onlyInNhanh: true,
        merge: true,
      });
      if (res2.code == 0) {
        return;
      }

      if (res2.code == 1) {
        flatList = flatList.filter((item, index) => {
          return (
            res2.data.find(
              (item2) => item2.code != 0 && item2.index == index
            ) != null
          );
        });
      }
    }
    dsFilePdf = getDsPdf(flatList);
    if (dsFilePdf.length) {
      printProvider.getMergePdf(flatten(dsFilePdf)).then((s) => {
        if (
          (res || []).every((x1) =>
            (x1 || []).every((x2) => x2.loaiIn == LOAI_IN.MO_TAB)
          )
        ) {
          openInNewTab(s);
        } else {
          printJS({
            printable: s,
            type: "pdf",
          });
        }
      });
    }
  };
  // --------------------------------------------------------------------------------------------------------------------------------
  const onPrintPhieuChuaIn = async () => {
    let res = await getTatCaGiayChiDinh({
      nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
      chiDinhTuDichVuId: thongTinChiTiet.id,
      inPhieuChiDinh: false,
      loai: 1,
      chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
    });
    if (res?.length === 0) {
      message.error(t("khamBenh.khongConPhieuChiDinhChuaIn"));
      return null;
    }
    const dsFilePdf = res?.reduce((acc, item) => {
      if (Array.isArray(item)) {
        // xử lý phiếu , có những phiếu có mảng con bên trong
        let list = item.map((itemChild) => itemChild.file.pdf);
        acc = [...acc, [...list]];
        return acc;
      }
      acc = [...acc, ...(item.filePdf || [])];
      return acc;
    }, []);
    const s = await getDataDanhSachPhieu({
      dsFile: flatten(dsFilePdf),
      mode: 0,
    });
    if ((res || []).every((x1) => (x1 || []).every((x2) => x2.loaiIn == 20))) {
      openInNewTab(s);
    } else {
      printJS({
        printable: s,
        type: "pdf",
      });
    }
  };
  //
  // --------------------------------------------------------------------------------------------------------------------------------
  const onPrintTheoDichVu = async (callback, chuaThanhToan) => {
    showLoading();
    try {
      let res = null;
      if (!chuaThanhToan) {
        res = await nbDvKyThuatProvider.getDvPhieuChiDinh({
          nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
          chiDinhTuDichVuId: thongTinChiTiet.id,
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
        });
        refModalInChiDinhTheoDV.current.show(
          {
            data: res.data,
            chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
            nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
            chiDinhTuDichVuId: thongTinChiTiet.id,
          },
          callback
        );
      } else {
        res = await nbDvKyThuatProvider.getPhieuChiDinhTongHop({
          nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
          dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
          chiDinhTuDichVuId: thongTinChiTiet.id,
          thanhToan: false,
        });
        await printProvider.printPdf(res?.data);
      }
    } catch (error) {
      showError(error?.message);
    } finally {
      hideLoading();
    }
  };
  // --------------------------------------------------------------------------------------------------------------------------------
  const contentPhieuChiDinh = () => {
    return (
      <div
        ref={refContentPhieuChiDinh}
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <Radio.Group
          value={state.valuePhieuChiDinhNew}
          onChange={(e) => {
            setOpenPopoverPhieuChiDinh(false);
            setOpenPopoverPhieuChiDinh1(false);
            setState({
              valuePhieuChiDinhNew: e.target.value, //SAKURA-62553 > set lại vào vào state để tick lại vào option đã chọn
            });
            setValuePhieuChiDinh(e.target.value); //SAKURA-62553 > đồng thời lưu vào cache
          }}
        >
          <Space direction="vertical">
            <Radio value={1}>{t("khamBenh.tatCaChiDinh")}</Radio>
            <Radio value={2}>{t("khamBenh.chiDinhChuaIn")}</Radio>
            <Radio value={3}>{t("khamBenh.inChiDinhTheoDichVu")}</Radio>
            <Radio value={4}>{t("khamBenh.tatCaCacDichVuChuaThanhToan")}</Radio>
          </Space>
        </Radio.Group>
      </div>
    );
  };

  const contentNgonNgu = (item) => {
    let _dsNgonNgu = [
      { id: 10, ten: t("account.tiengViet"), baoCaoId: item.baoCaoId },
    ];
    item.dsNgonNgu.forEach((element) => {
      const _selected = listNgonNgu.find((x) => x.id == element.ngonNgu);

      _dsNgonNgu.push({
        id: element.ngonNgu,
        ten: _selected?.ten || "",
        baoCaoId: element.id,
      });
    });

    return (
      <div
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <Radio.Group
          value={state[`valueNgonNgu_${item.id}`] || 10}
          onChange={(e) => {
            setState({
              [`valueNgonNgu_${item.id}`]: e.target.value,
              [`baoCaoId_${item.id}`]: e.target.baoCaoId,
              [`openPopover_${item.id}`]: false,
            });
          }}
        >
          {/* Tiếng Việt là mặc định */}
          <Space direction="vertical">
            {_dsNgonNgu.map((item, index) => (
              <Radio key={index} value={item.id} baoCaoId={item.baoCaoId}>
                {item.ten}
              </Radio>
            ))}
          </Space>
        </Radio.Group>
      </div>
    );
  };

  // --------------------------------------------------------------------------------------------------------------------------------
  const onPrintPhieuChiDinh = (item) => async (e) => {
    // e.preventDefault();
    // e.stopPropagation();

    const _showModalPrint = (addParams = {}) => {
      refModalSignPrint.current &&
        refModalSignPrint.current.showToSign({
          phieuKy: item,
          payload: {
            nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
            maManHinh: MAN_HINH_PHIEU_IN.KHAM_BENH,
            maViTri: VI_TRI_PHIEU_IN.KHAM_BENH.IN_GIAY_TO,
            chiDinhTuDichVuId: thongTinChiTiet?.id,
            dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
            isGetListPhieu: true,
            maManHinh2: MAN_HINH_PHIEU_IN.KHAM_BENH,
            maViTri2: VI_TRI_PHIEU_IN.KHAM_BENH.PHIEU_CHI_DINH,
            loai: 1,
            paramsTheoMaPhieu: {
              P075: {
                dsChiDinhTuLoaiDichVu: getDsChiDinhTuLoaiDichVu(),
              },
            },
            ...addParams,
          },
        });
    };

    switch (state.valuePhieuChiDinhNew) {
      case 1: {
        // tất cả chỉ định
        // await onPrint();
        _showModalPrint();
        //SAKURA-62553 >> nếu in tất cả rồi lần đầu rồi, thì lần thứ 2 sẽ tự chuyển sang chỉ định chưa in
        if (dataIN_TAT_CA_PHIEU_CHI_DINH_1_LAN?.eval())
          setState({ valuePhieuChiDinhNew: 2 });
        break;
      }
      case 2: {
        // chỉ định chưa in
        // await onPrintPhieuChuaIn();
        _showModalPrint({
          inPhieuChiDinh: false,
          maViTri2: VI_TRI_PHIEU_IN.KHAM_BENH.PHIEU_CHI_DINH_CHUA_IN,
        });
        break;
      }
      case 3: {
        // in chỉ định theo dịch vụ
        await onPrintTheoDichVu((data) => {
          _showModalPrint(data);
        });
        break;
      }
      case 4: {
        await onPrintTheoDichVu(null, true);
        break;
      }
      default:
        // tất cả chỉ định
        // await onPrint();
        _showModalPrint();
        break;
    }
  };

  const onInNhanhPhieuChiDinh = async () => {
    if (phieuChiDinhKhamBenh) await onPrintPhieuChiDinh()();
  };

  const onInNhanhPhieuKqCls = async () => {
    if (phieuKqCls) await onPrintPhieu(phieuKqCls)();
  };

  const onPrintTatCaChiDinh = async () => {
    let res = null;
    try {
      res = await nbDvKyThuatProvider.getPhieuXacNhanThucHienDichVu({
        nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
        chiDinhTuDichVuId: thongTinChiTiet?.id,
        dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
      });
    } catch (e) {
      message.error(e?.message);
      return null;
    }
    const dsFilePdf = Array.isArray(res?.data)
      ? res?.data.map((itemChild) => itemChild.file.pdf)
      : res?.data.file.pdf
      ? [res?.data.file.pdf]
      : [];
    if (!dsFilePdf.length) {
      return null;
    }
    const s = await getDataDanhSachPhieu({
      dsFile: flatten(dsFilePdf),
      mode: 0,
    });
    printJS({
      printable: s,
      type: "pdf",
    });
  };

  const onPrintPhieuXacNhanChuaIn = async () => {
    let res = null;
    try {
      res = await nbDvKyThuatProvider.getPhieuXacNhanThucHienDichVu({
        nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
        chiDinhTuDichVuId: thongTinChiTiet?.id,
        dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
        inPhieuChiDinh: false,
      });
    } catch (e) {
      message.error(e?.message);
      return null;
    }
    if (res?.length === 0) {
      message.error(t("khamBenh.khongConPhieuChiDinhChuaIn"));
      return null;
    }
    const dsFilePdf = Array.isArray(res?.data)
      ? res?.data.map((itemChild) => itemChild.file.pdf)
      : res?.data.file.pdf
      ? [res?.data.file.pdf]
      : [];
    if (!dsFilePdf.length) {
      return null;
    }
    const s = await getDataDanhSachPhieu({
      dsFile: flatten(dsFilePdf),
      mode: 0,
    });
    printJS({
      printable: s,
      type: "pdf",
    });
  };

  const onPrintPhieuXacNhanTheoDV = async () => {
    let res = await nbDvKyThuatProvider.getDvPhieuChiDinh({
      nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
      chiDinhTuDichVuId: thongTinChiTiet?.id,
      dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
    });
    refModalInXacNhanThucHienTheoDV.current.show(res.data);
  };

  const onPrintPhieuXacNhanThucHien = async (e) => {
    showLoading();
    switch (valuePhieuXacNhanThucHien) {
      case 1: {
        // tất cả chỉ định
        await onPrintTatCaChiDinh();
        break;
      }
      case 2: {
        // chỉ định chưa in
        await onPrintPhieuXacNhanChuaIn();
        break;
      }
      case 3: {
        // in chỉ định theo dịch vụ
        await onPrintPhieuXacNhanTheoDV();
        break;
      }
      default:
        // tất cả chỉ định
        await onPrintTatCaChiDinh();
        break;
    }
    hideLoading();
  };

  const contentPhieuXacNhanThucHien = () => {
    return (
      <div
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <Radio.Group
          value={valuePhieuXacNhanThucHien || 2}
          onChange={(e) => {
            setOpenPopoverPhieuXacNhanThucHien(false);
            setValuePhieuXacNhanThucHien(e.target.value);
          }}
        >
          <Space direction="vertical">
            <Radio value={1}>{t("khamBenh.tatCaChiDinh")}</Radio>
            <Radio value={2}>{t("khamBenh.chiDinhChuaIn")}</Radio>
            <Radio value={3}>{t("khamBenh.inChiDinhTheoDichVu")}</Radio>
          </Space>
        </Radio.Group>
      </div>
    );
  };

  const getFileAndPrint = async (payload, item) => {
    try {
      showLoading();
      const { finalFile, dsPhieu } = await getFilePhieuIn(payload);
      if ((dsPhieu || []).every((x) => x?.loaiIn == LOAI_IN.MO_TAB)) {
        openInNewTab(finalFile);
      } else {
        if (["P002", "P1137"].includes(item?.ma)) {
          const res = await printProvider.printPdf(dsPhieu, {
            mergePdfFile: finalFile,
          });
          if (res.code == 0) {
            return;
          }
        }
        printJS({
          printable: finalFile,
          type: "pdf",
        });
      }
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onPrintPhieu =
    (item, addParams = {}) =>
    async () => {
      if (item.type == "editor") {
        let mhParams = {};
        //kiểm tra phiếu ký số
        if (checkIsPhieuKySo(item)) {
          //nếu là ký số thì lấy các thông tin về lịch sử ký + tên chân ký + báo cáo id
          mhParams = {
            nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
            chiDinhTuDichVuId: thongTinChiTiet.id,
            maManHinh: item.maManHinh || MAN_HINH_PHIEU_IN.KHAM_BENH,
            maViTri: item.maViTri || VI_TRI_PHIEU_IN.KHAM_BENH.IN_GIAY_TO,
            kySo: true,
            maPhieuKy: item.ma,
            baoCaoId: item.baoCaoId,
          };
        }

        if (
          dataCHO_IN_BANG_KE_TIEN_ICH_NGUOI_BENH_NGOAI_TRU?.eval() &&
          [
            DOI_TUONG_KCB.NGOAI_TRU,
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
          ].includes(infoNb?.doiTuongKcb) &&
          item.ma == "P178"
        ) {
          let data = {
            nbDvKhamId: thongTinChiTiet?.id,
            ma: item.ma,
            maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
              ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
              : "",
            loai: 60,
            loaiIn:
              item.dsBaoCao && item.dsBaoCao.length > 0
                ? item.dsBaoCao[0]?.loaiIn
                : null,
            ...mhParams,
            ...(dataKHONG_TU_DONG_DONG_TAB.eval() && mhParams?.kySo
              ? { notPrint: true }
              : {}), //setting kySo = true -> Vẫn bật tab nhưng không tự động in phiếu
          };

          if (dataKHONG_TU_DONG_DONG_TAB.eval()) {
            data.khongDongTab = true;
          }

          const url = combineUrlParams(
            `/print-file/bang-ke/${thongTinChiTiet?.nbDotDieuTriId}`,
            {
              ...data,
            }
          );
          window.open(url);
        } else if (LIST_PHIEU_CHON_TIEU_CHI.includes(item.ma)) {
          const dataSearch = {
            nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
            ...(item.ma === "P943" && {
              dataNHOM_GIAI_PHAU_BENH,
            }),
          };
          let dsTieuChi = await getTieuChi({
            maBaoCao: item.ma,
            dataSearch,
          });
          dsTieuChi = dsTieuChi.map((item) => {
            return {
              id: item.id,
              ten: (
                <span
                  title={`${item.maDichVu}-${item.tenDichVu}-${moment(
                    item.thoiGianThucHien
                  ).format("DD/MM/YYYY")}`}
                >
                  <b>
                    {item.maDichVu}-{item.tenDichVu}
                  </b>
                  -{moment(item.thoiGianThucHien).format("DD/MM/YYYY")}
                </span>
              ),
              uniqueText: createUniqueText(
                `${item.maDichVu}-${item.tenDichVu}-${moment(
                  item.thoiGianThucHien
                ).format("DD/MM/YYYY")}`
              ),
              value: item.id,
            };
          });
          refModalChonTieuChi &&
            refModalChonTieuChi.current.show(
              { data: dsTieuChi },
              (idTieuChi) => {
                showFileEditor({
                  phieu: item,
                  nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
                  nbDvXetNgiemId: idTieuChi,
                  ma: item.ma,
                  id: idTieuChi,
                  mhParams,
                });
              }
            );
        } else if (
          LIST_PHIEU_CHON_TIEU_CHI_IN_THEO_SO_PHIEU.includes(item.ma)
        ) {
          let dsTieuChi = item.dsSoPhieu || [];
          dsTieuChi = dsTieuChi.map((item) => {
            return {
              id: item.soPhieu,
              ten: (
                <span
                  title={`${item.ten1}-${item.ten2}-${moment(
                    item.thoiGianThucHien
                  ).format("DD/MM/YYYY")}`}
                >
                  <b>
                    {item.ten1}-{item.ten2}
                  </b>
                  -{moment(item.thoiGianThucHien).format("DD/MM/YYYY")}
                </span>
              ),
              uniqueText: createUniqueText(
                `${item.ten1}-${item.ten2}-${moment(
                  item.thoiGianThucHien
                ).format("DD/MM/YYYY")}`
              ),
              value: item.soPhieu,
            };
          });
          refModalChonTieuChi &&
            refModalChonTieuChi.current.show(
              { data: dsTieuChi },
              (idTieuChi) => {
                showFileEditor({
                  phieu: item,
                  nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
                  nbDvXetNgiemId: idTieuChi,
                  ma: item.ma,
                  id: idTieuChi,
                  mhParams,
                });
              }
            );
        } else {
          // in tờ điều trị ngoại trú
          if (["P975", "P184"]?.includes(item.ma)) {
            refModalInToDieuTri.current &&
              refModalInToDieuTri.current.show({
                nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
                khoaId: listPhongKham.find((x) => x.id == phongThucHienId)
                  ?.khoaId,
                maBaoCao: item.ma,
                chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
                mhParams: {
                  ...mhParams,
                  ngoaiTru: true,
                  loaiDichVu: LOAI_DICH_VU.KHAM,
                  baoCaoId: item.baoCaoId,
                },
              });
          } else if (["P086", "P606"].includes(item.ma)) {
            //Phiếu theo dõi truyền dịch
            refModalChonPhieuTruyenDich.current &&
              refModalChonPhieuTruyenDich.current.show(
                {
                  khoaChiDinhId: thongTinChiTiet.nbDichVu.khoaChiDinhId,
                  dsSoPhieu: item.dsSoPhieu || [],
                },
                (filterData) => {
                  showFileEditor({
                    phieu: item,
                    id: filterData?.id,
                    nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
                    ma: item.ma,
                    maBaoCao:
                      item.maBaoCao ||
                      (MA_BIEU_MAU_EDITOR[item.ma]
                        ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                        : ""),
                    khoaChiDinhId: filterData?.khoaChiDinhId,
                    tuThoiGian: filterData?.tuThoiGian,
                    denThoiGian: filterData?.denThoiGian,
                    mhParams,
                  });
                }
              );
          } else if (["P1073"].includes(item.ma)) {
            const _dsSoPhieu = (item.dsSoPhieu || []).filter(
              (x) => x.soPhieu != thongTinChiTiet?.nbDotDieuTriId
            );
            refModalChonKhoaPhieuTruyenDich.current &&
              refModalChonKhoaPhieuTruyenDich.current.show(
                {
                  khoaChiDinhId: thongTinChiTiet.nbDichVu.khoaChiDinhId,
                  dsSoPhieu: _dsSoPhieu,
                },
                (filterData) => {
                  showFileEditor({
                    phieu: item,
                    id: filterData?.id,
                    nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
                    ma: item.ma,
                    maBaoCao:
                      item.maBaoCao ||
                      (MA_BIEU_MAU_EDITOR[item.ma]
                        ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                        : ""),
                    khoaChiDinhId: filterData?.khoaChiDinhId,
                    tuThoiGian: filterData?.tuThoiGian,
                    denThoiGian: filterData?.denThoiGian,
                    mhParams,
                  });
                }
              );
          } else if (item.ma == "P197") {
            //Phiếu thực hiện và công khai vật tư y tế tiêu hao
            refModalChonPhieuCongKhaiVtyt.current &&
              refModalChonPhieuCongKhaiVtyt.current.show(
                {
                  khoaChiDinhId: configData?.khoaChiDinhId,
                  dsSoPhieu: item.dsSoPhieu || [],
                },
                (filterData) => {
                  showFileEditor({
                    phieu: item,
                    id: filterData?.id,
                    nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
                    ma: item.ma,
                    maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                      ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                      : "",
                    chiDinhTuDichVuId: thongTinChiTiet?.id,
                    chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
                    khoaChiDinhId: filterData?.khoaChiDinhId,
                    tuThoiGian: filterData?.tuThoiGian,
                    denThoiGian: filterData?.denThoiGian,
                    mhParams,
                  });
                }
              );
          } else if (item.ma == "P539") {
            refModalChonPhieuCongKhaiThuoc.current &&
              refModalChonPhieuCongKhaiThuoc.current.show(
                {
                  thoiGianYLenh: new Date(),
                  khoaChiDinhId: thongTinChiTiet.nbDichVu.khoaChiDinhId,
                  listDanhSachKhoa: listKhoaTheoTaiKhoan,
                },
                (filterData) => {
                  showFileEditor({
                    phieu: item,
                    id: thongTinChiTiet?.nbDotDieuTriId,
                    nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
                    ma: item.ma,
                    maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                      ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                      : "",
                    khoaChiDinhId: filterData?.khoaChiDinhId,
                    thoiGianYLenh: filterData?.thoiGianYLenh,
                    mhParams,
                  });
                }
              );
          } else if (["P370", "P438"].includes(item.ma)) {
            //Lọc bỏ phiếu có số phiếu = nbDotDieuTriId
            const _dsSoPhieu = (item.dsSoPhieu || []).filter(
              (x) => x.soPhieu != thongTinChiTiet.nbDotDieuTriId
            );
            //Sao bệnh án
            refModalInPhieuSaoBA.current &&
              refModalInPhieuSaoBA.current.show(
                {
                  khoaLamViec: { id: thongTinChiTiet.nbDichVu.khoaChiDinhId },
                  dsSoPhieu: _dsSoPhieu,
                  ma: item.ma,
                },
                (data) => {
                  const { thoiGianThucHien, khoaChiDinhId, id: idPhieu } = data;
                  showFileEditor({
                    phieu: item,
                    id: idPhieu,
                    nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
                    ma: item.ma,
                    maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                      ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                      : "",
                    chiDinhTuDichVuId: thongTinChiTiet.id,
                    khoaChiDinhId,
                    thoiGianThucHien,
                    mhParams,
                  });
                }
              );
          } else if (["P632", "P712"].includes(item.ma)) {
            //Lọc bỏ phiếu có số phiếu = nbDotDieuTriId
            const _dsSoPhieu = (item.dsSoPhieu || []).filter(
              (x) =>
                x.soPhieu != thongTinChiTiet?.nbDotDieuTriId &&
                x.soPhieu &&
                x.soPhieu !== "null"
            );
            refModalInPhieuTomTatBA.current &&
              refModalInPhieuTomTatBA.current.show(
                {
                  khoaChiDinhId: thongTinChiTiet.nbDichVu.khoaChiDinhId,
                  dsSoPhieu: _dsSoPhieu,
                  ten: item.ten,
                },
                (data) => {
                  const { thoiGianThucHien, khoaChiDinhId, id: idPhieu } = data;
                  showFileEditor({
                    phieu: item,
                    id: idPhieu,
                    nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
                    ma: item.ma,
                    maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                      ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                      : "",
                    chiDinhTuDichVuId: thongTinChiTiet?.nbDotDieuTriId,
                    khoaChiDinhId,
                    thoiGianThucHien,
                    mhParams,
                  });
                }
              );
          } else if (["P607"].includes(item.ma)) {
            const _dsSoPhieu = (item.dsSoPhieu || []).filter(
              (x) => x.soPhieu != thongTinChiTiet?.nbDotDieuTriId
            );
            refModalPhieuBanGiaoNguoiBenh.current &&
              refModalPhieuBanGiaoNguoiBenh.current.show(
                {
                  khoaChuyenDiId: listPhongKham.find(
                    (x) => x.id == phongThucHienId
                  )?.khoaId,
                  dsSoPhieu: _dsSoPhieu || [],
                },
                ({
                  khoaChuyenDiId,
                  idPhieu,
                  khoaChuyenDenId,
                  thoiGianBanGiao,
                }) => {
                  try {
                    showFileEditor({
                      phieu: item,
                      id: idPhieu,
                      nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
                      ma: item.ma,
                      maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                        ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                        : "",
                      chiDinhTuDichVuId: thongTinChiTiet?.nbDotDieuTriId,
                      thoiGianBanGiao,
                      khoaChuyenDenId,
                      khoaChuyenDiId,
                      mhParams,
                    });
                  } catch (error) {
                    console.log(error);
                  }
                }
              );
          } else if (item.ma === "P819") {
            //Phiếu khám tiền mê (không theo DV PTTT)
            const _dsSoPhieu = (item.dsSoPhieu || []).filter(
              (x) => x.soPhieu != thongTinChiTiet?.nbDotDieuTriId
            );

            refModalInPhieuTienMe.current &&
              refModalInPhieuTienMe.current.show(
                {
                  defaultValue: thongTinChiTiet.nbDichVu.khoaChiDinhId,
                  tenPhieu: item.ten,
                  dsSoPhieu: _dsSoPhieu,
                },
                (filterData = {}) => {
                  showFileEditor({
                    phieu: item,
                    id: filterData?.id,
                    nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
                    ma: item.ma,
                    maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                      ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                      : "",
                    khoaChiDinhId: filterData?.khoaChiDinhId,
                    thoiGianThucHien: filterData?.thoiGianThucHien,
                    mhParams,
                  });
                }
              );
          } else if (["P537", "P651", "P361", "P1134"].includes(item.ma)) {
            const _dsSoPhieu = (item.dsSoPhieu || []).filter(
              (x) => x.soPhieu != thongTinChiTiet?.nbDotDieuTriId
            );
            //Giấy cam đoan chấp nhận phẫu thuật, thủ thuật và gây mô hồi sức (Mẫu dùng cho bệnh viện phổi)
            refModalInPhieuCamDoanChapNhanPTTT.current &&
              refModalInPhieuCamDoanChapNhanPTTT.current.show(
                {
                  khoaChiDinhId: thongTinChiTiet.nbDichVu.khoaChiDinhId,
                  dsSoPhieu: _dsSoPhieu || [],
                  ten: item.ten,
                  ma: item.ma,
                },
                (filterData) => {
                  showFileEditor({
                    phieu: item,
                    id: filterData?.id,
                    nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
                    ma: item.ma,
                    maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                      ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                      : "",
                    chiDinhTuDichVuId: thongTinChiTiet?.id,
                    chiDinhTuLoaiDichVu:
                      item.ma === "P651"
                        ? LOAI_DICH_VU.KHAM
                        : LOAI_DICH_VU.TO_DIEU_TRI,
                    khoaChiDinhId: filterData?.khoaChiDinhId,
                    thoiGianThucHien: filterData?.thoiGianThucHien,
                    mhParams,
                  });
                }
              );
          } else if (["P1072"].includes(item.ma)) {
            const _dsSoPhieu = (item.dsSoPhieu || []).filter(
              (x) => x.soPhieu != thongTinChiTiet?.nbDotDieuTriId
            );

            refModalInPhieuCamDoanChapNhanPTTT.current &&
              refModalInPhieuCamDoanChapNhanPTTT.current.show(
                {
                  khoaChiDinhId: thongTinChiTiet.nbDichVu.khoaChiDinhId,
                  dsSoPhieu: _dsSoPhieu || [],
                  ten: item.ten,
                },
                (filterData) => {
                  showFileEditor({
                    phieu: item,
                    id: filterData?.id,
                    nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
                    ma: item.ma,
                    maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                      ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                      : "",
                    chiDinhTuDichVuId: thongTinChiTiet?.id,
                    chiDinhTuLoaiDichVu:
                      item.ma === "P651"
                        ? LOAI_DICH_VU.KHAM
                        : LOAI_DICH_VU.TO_DIEU_TRI,
                    khoaChiDinhId: filterData?.khoaChiDinhId,
                    thoiGianThucHien: filterData?.thoiGianThucHien,
                    mhParams,
                  });
                }
              );
          } else if (["P044"].includes(item.ma)) {
            refModalInGiayNghiHuongBHXH.current &&
              refModalInGiayNghiHuongBHXH.current.show(
                {
                  dsSoPhieu: item.dsSoPhieu || [],
                  ten: item.ten,
                },
                (filterData) => {
                  showFileEditor({
                    phieu: item,
                    id: filterData?.id,
                    nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
                    ma: item.ma,
                    maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                      ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                      : "",
                    chiDinhTuDichVuId: thongTinChiTiet?.id,
                    chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
                    thoiGianThucHien: filterData?.thoiGianThucHien,
                    mhParams,
                  });
                }
              );
          } else if (
            item.ma == "P032" &&
            kiemTra &&
            (!dsManHinh?.length || dsManHinh?.includes("003"))
          ) {
            let data = {
              hoTen: thongTinBenhNhan?.tenNb,
              maThe: thongTinBenhNhan?.nbTheBaoHiem?.maThe,
              ngaySinh: moment(thongTinBenhNhan?.ngaySinh).format("DD/MM/YYYY"),
            };
            const showFileEditorFunc = () =>
              showFileEditor({
                phieu: item,
                nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
                id: thongTinChiTiet?.nbDotDieuTriId,
                ma: item.ma,
                maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                  ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                  : "",
                khongDongTab: KHONG_TU_DONG_DONG_TAB ? true : undefined,
              });

            const showModalCheckBH = (_data) =>
              refModalCheckBaoHiem.current.show(
                {
                  show: true,
                  data: data,
                  hoTen: thongTinBenhNhan?.tenNb,
                  diaChi: thongTinBenhNhan?.nbDiaChi?.diaChi,
                },
                (res) => {
                  if (res.boQuaTheLoi) {
                    onUpdate({
                      id: thongTinChiTiet?.nbDotDieuTriId,
                      nbTheBaoHiem: { boQuaTheLoi: true },
                    });

                    showFileEditorFunc();
                  }
                }
              );
            giamDinhThe({ ...data, keepCode: true })
              .then((res) => {
                if (res?.code === 0) {
                  showFileEditorFunc();
                } else {
                  showModalCheckBH(res);
                }
              })
              .catch((e) => {
                showModalCheckBH(e);
              });
          } else {
            showFileEditor({
              phieu: item,
              nbDvKhamId: thongTinChiTiet?.id,
              nbThongTinId: infoNb?.nbThongTinId,
              nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
              goiDvId: listNbGoiDv[0]?.id,
              id: thongTinChiTiet?.nbDotDieuTriId,
              ma: item.ma,
              maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                : "",
              khoaChiDinhId: thongTinChiTiet.nbDichVu.khoaChiDinhId,
              mhParams: {
                ...mhParams,
                baoCaoId: item.baoCaoId,
              },
              chiDinhTuDichVuId: thongTinChiTiet?.id,
              chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
              khongDongTab:
                [
                  "P107",
                  "P032",
                  "P062",
                  "P541",
                  "P554",
                  "P678",
                  "P677",
                ].includes(item.ma) && KHONG_TU_DONG_DONG_TAB
                  ? true
                  : undefined,
              ngonNguParams: {
                ngonNgu: state[`valueNgonNgu_${item.id}`],
                baoCaoId: state[`baoCaoId_${item.id}`],
              },
            });
          }
        }
      } else {
        switch (item.ma) {
          case "P049":
            refModalSignPrint.current.show({
              nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
              chiDinhTuDichVuId: thongTinChiTiet?.id,
              maManHinh: MAN_HINH_PHIEU_IN.KHAM_BENH,
              maViTri: VI_TRI_PHIEU_IN.KHAM_BENH.IN_TAT_CA_GIAY_TO,
              isKyPhieu: true,
              kyTheoTungPhieu: true,
            });
            break;
          case "P051":
            refModalSignPrint.current &&
              refModalSignPrint.current.showToSign({
                phieuKy: item,
                payload: {
                  nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
                  chiDinhTuDichVuId: thongTinChiTiet?.id,
                  dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
                  maManHinh: MAN_HINH_PHIEU_IN.KHAM_BENH,
                  maViTri: VI_TRI_PHIEU_IN.KHAM_BENH.IN_GIAY_TO,
                  isGetListPhieu: true,
                  maManHinh2: MAN_HINH_PHIEU_IN.KHAM_BENH,
                  maViTri2: VI_TRI_PHIEU_IN.KHAM_BENH.IN_DON_THUOC,
                },
              });
            break;
          case "P092":
            //Phiếu công khai dịch vụ khám, chữa bệnh nội trú

            refModalInPhieuCongKhai.current &&
              refModalInPhieuCongKhai.current.show(
                {
                  khoaLamViec: { id: thongTinChiTiet.nbDichVu.khoaChiDinhId },
                  dsSoPhieu: item.dsSoPhieu || [],
                },
                async (data) => {
                  const {
                    tuThoiGian,
                    denThoiGian,
                    dsNhomDichVuCap1Id,
                    khoaChiDinhId,
                    dsLoaiDichVu,
                    dsThoiGian,
                    id: idPhieu,
                  } = data;
                  let mhParams = {};
                  //kiểm tra phiếu ký số

                  if (checkIsPhieuKySo(item)) {
                    //nếu là ký số thì lấy các thông tin về lịch sử ký + tên chân ký + báo cáo id
                    mhParams = {
                      maManHinh: MAN_HINH_PHIEU_IN.KHAM_BENH,
                      maViTri: VI_TRI_PHIEU_IN.KHAM_BENH.IN_GIAY_TO,
                      chiDinhTuDichVuId: thongTinChiTiet?.id,
                      dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
                      kySo: true,
                      maPhieuKy: item.ma,
                      nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
                    };
                  }

                  if (item.loaiBieuMau === LOAI_BIEU_MAU.BIEU_MAU_CHINH_SUA) {
                    showFileEditor({
                      phieu: item,
                      id: idPhieu,
                      nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
                      ma: item.ma,
                      maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                        ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                        : "",
                      chiDinhTuDichVuId: thongTinChiTiet.nbDotDieuTriId,
                      khoaChiDinhId,
                      tuThoiGian,
                      denThoiGian,
                      dsLoaiDichVu,
                      dsNhomDichVuCap1Id,
                      mhParams,
                    });
                  } else {
                    getFileAndPrint({
                      listPhieus: [item],
                      showError: true,
                      nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
                      tuThoiGian,
                      denThoiGian,
                      dsNhomDichVuCap1Id,
                      khoaChiDinhId,
                      dsLoaiDichVu,
                      dsThoiGian,
                    });
                  }
                }
              );
            break;
          case "P050":
            refModalChonKetQuaXetNghiem.current &&
              refModalChonKetQuaXetNghiem.current.show({
                nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
                baoCaoId: item.baoCaoId,
              });
            break;
          case "P895":
            onPrintPhieuThuNguoiBenh(item);
            break;

          default:
            if (checkIsPhieuKySo(item)) {
              refModalSignPrint.current &&
                refModalSignPrint.current.showToSign({
                  phieuKy: item,
                  payload: {
                    nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
                    maManHinh: item.maManHinh || MAN_HINH_PHIEU_IN.KHAM_BENH,
                    maViTri:
                      item.maViTri || VI_TRI_PHIEU_IN.KHAM_BENH.IN_GIAY_TO,
                    chiDinhTuDichVuId: thongTinChiTiet?.id,
                    dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
                    paramsTheoMaPhieu: {
                      P193: {
                        dsChiDinhTuLoaiDichVu: getDsChiDinhTuLoaiDichVu(),
                      },
                    },
                    ngonNguParams: {
                      ngonNgu: state[`valueNgonNgu_${item.id}`],
                      baoCaoId: state[`baoCaoId_${item.id}`],
                    },
                  },
                });
            } else {
              getFileAndPrint(
                {
                  listPhieus: [item],
                  nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
                  chiDinhTuDichVuId: thongTinChiTiet?.id,
                  dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
                  trangThai: 20,
                  maNb: infoNb?.maNb,
                  showError: true,
                  nbThongTinId: infoNb?.nbThongTinId,
                  khoaChiDinhId: item.khoaChiDinhId,
                  thoiGianThucHien: item.thoiGianThucHien,
                  nbDvKhamId: thongTinChiTiet?.id,
                  ...addParams,
                  ngonNguParams: {
                    ngonNgu: state[`valueNgonNgu_${item.id}`],
                    baoCaoId: state[`baoCaoId_${item.id}`],
                  },
                },
                item
              );
            }
        }
      }
    };

  const onPrintPhieuThuNguoiBenh = async (phieu) => {
    showLoading();
    const { finalFile, dsPhieu } = await getFilePhieuIn({
      listPhieus: (phieu.dsSoPhieu || []).map((item) => ({
        ma: item.maPhieu,
        ...item,
      })),
      maPhieuIn: "P033",
      maViTri: VI_TRI_PHIEU_IN.KHAM_BENH.IN_GIAY_TO,
    });

    if (isArray(dsPhieu, true)) {
      const inMoTab = dsPhieu.every((item) => {
        let valid = false;
        if (isArray(item.data, true)) {
          valid = item.data.every((el) => el?.loaiIn === LOAI_IN.MO_TAB);
        } else {
          valid = item?.loaiIn === LOAI_IN.MO_TAB;
        }
        return valid;
      });
      if (inMoTab) openInNewTab(finalFile);
      else printProvider.printPdf(dsPhieu);
    } else {
      printJS({
        printable: finalFile,
        type: "pdf",
      });
    }
    hideLoading();
  };

  const onPrintDonThuoc = async () => {
    // Cho phép in theo loại in
    try {
      showLoading();
      const phieuDonThuoc = await getPhieuIn({
        nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
        chiDinhTuDichVuId: thongTinChiTiet?.id,
        dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
        maManHinh: MAN_HINH_PHIEU_IN.KHAM_BENH,
        maViTri: VI_TRI_PHIEU_IN.KHAM_BENH.IN_DON_THUOC,
        noScrollEl: true,
      });
      if (!isArray(phieuDonThuoc, true)) {
        hideLoading();
        return;
      }
      //kiểm tra phiếu ký số
      if (checkIsPhieuKySo(phieuDonThuoc[0])) {
        refModalSignPrint.current.show({
          nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
          chiDinhTuDichVuId: thongTinChiTiet?.id,
          dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
          maManHinh: MAN_HINH_PHIEU_IN.KHAM_BENH,
          maViTri: VI_TRI_PHIEU_IN.KHAM_BENH.IN_DON_THUOC,
          isKyPhieu: true,
          kyTheoTungPhieu: true,
        });
      } else {
        const { finalFile, dsPhieu } = await getFilePhieuIn({
          // selectedIds: flatten(phieuDonThuoc.map((item) => item.key)),
          listPhieus: phieuDonThuoc,
          nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
          chiDinhTuDichVuId: thongTinChiTiet?.id,
          dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
          maManHinh: MAN_HINH_PHIEU_IN.KHAM_BENH,
          maViTri: VI_TRI_PHIEU_IN.KHAM_BENH.IN_DON_THUOC,
          // isPhieuTemp: true,
        });
        if (isArray(dsPhieu, true)) {
          const inMoTab = dsPhieu.every((item) => {
            let valid = false;
            if (isArray(item.data, true)) {
              valid = item.data.every((el) => el?.loaiIn === LOAI_IN.MO_TAB);
            } else {
              valid = item?.loaiIn === LOAI_IN.MO_TAB;
            }
            return valid;
          });
          if (inMoTab) openInNewTab(finalFile);
          else printProvider.printPdf(dsPhieu);
        } else {
          printJS({
            printable: finalFile,
            type: "pdf",
          });
        }
      }
    } finally {
      hideLoading();
    }
  };
  // --------------------------------------------------------------------------------------------------------------------------------

  const contentPhieuNhatKyDieuTri = () => {
    return (
      <div
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}
        className="nhat-ky"
      >
        {listNbGoiDv.map((x) => {
          return (
            <span
              onClick={() => window.open("/editor/bao-cao/EMR_BA156/" + x.id)}
            >
              {x.tenGoiDv}
            </span>
          );
        })}
      </div>
    );
  };

  const onPrintGiayNghiHuongBHXH = (item) => () => {
    let _tuoi = moment().diff(moment(infoNb?.ngaySinh), "years");
    if (
      CANH_BAO_NGHI_HUONG_BHXH &&
      CANH_BAO_NGHI_HUONG_BHXH.toLowerCase() == "true" &&
      _tuoi >= 7
    ) {
      showConfirm(
        {
          title: t("common.canhBao"),
          content: t("khamBenh.canhBaoNghiHuongBHXH"),
          cancelText: t("common.dong"),
          okText: t("common.dongY"),
          showBtnOk: true,
          typeModal: "warning",
        },
        () => {
          onPrintPhieu(item)();
        },
        () => {}
      );
    } else {
      onPrintPhieu({ ...item, type: "editor" })();
    }
  };
  const content = (
    <Menu
      items={[
        {
          key: "-1",
          label: (
            <div
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
              }}
            >
              <div className="input-box">
                <SVG.IcSearch />
                <InputTimeout
                  autoFocus
                  placeholder={t("common.timKiemTenPhieuIn")}
                  value={state.searchPhieuIn}
                  onChange={(e) => setState({ searchPhieuIn: e })}
                />
              </div>
            </div>
          ),
        },
        ...(state.listPhieu || [])
          .filter((phieu) => containText(phieu.ten, state?.searchPhieuIn))
          .filter((item) => isKsk || item.ma != "P053")
          .filter(
            (item) => item.ma !== "P1000" || infoNb?.gioiTinh === GIOI_TINH.NU
          )

          .map((item, index) => {
            if (item.ma == "P048") {
              return {
                key: index + "",
                label: (
                  <div style={{ display: "flex" }}>
                    <div
                      onClick={onPrintPhieuChiDinh(item)}
                      style={{ flex: 1 }}
                    >
                      {item.ten || item.tenBaoCao}
                    </div>

                    <Popover
                      getPopupContainer={(trigger) => trigger.parentNode}
                      overlayClassName={"step-wrapper-in-options right"}
                      placement="rightTop"
                      content={contentPhieuChiDinh()}
                      trigger="click"
                      open={openPopoverPhieuChiDinh1}
                      onOpenChange={setOpenPopoverPhieuChiDinh1}
                    >
                      <SVG.IcOption
                        onClick={(e) => {
                          e.stopPropagation();
                        }}
                      />
                    </Popover>
                  </div>
                ),
              };
            }
            if (item.ma == "P040" && listNbGoiDv?.length > 1) {
              return {
                key: index + "",
                label: (
                  <div style={{ display: "flex" }}>
                    <Popover
                      getPopupContainer={(trigger) => trigger.parentNode}
                      overlayClassName={
                        "step-wrapper-in-options-dieu-tri right"
                      }
                      placement="rightTop"
                      content={contentPhieuNhatKyDieuTri()}
                    >
                      <div style={{ flex: 1 }}>
                        {item.ten || item.tenBaoCao}
                      </div>
                    </Popover>
                  </div>
                ),
              };
            }
            if (item.ma === "P081") {
              return {
                key: index + "",
                label: (
                  <div style={{ display: "flex" }}>
                    <div
                      onClick={onPrintPhieuXacNhanThucHien}
                      style={{ flex: 1 }}
                    >
                      {item.ten || item.tenBaoCao}
                    </div>

                    <Popover
                      getPopupContainer={(trigger) => trigger.parentNode}
                      overlayClassName={"step-wrapper-in-options right"}
                      placement="rightTop"
                      content={contentPhieuXacNhanThucHien()}
                      trigger="click"
                      open={openPopoverPhieuXacNhanThucHien}
                      onOpenChange={setOpenPopoverPhieuXacNhanThucHien}
                    >
                      <SVG.IcOption
                        className="cursor-pointer"
                        style={{ marginLeft: 10 }}
                        onClick={(e) => {
                          e.stopPropagation();
                        }}
                      />
                    </Popover>
                  </div>
                ),
              };
            }
            if (item.ma === "P053") {
              return {
                key: index + "",
                label: item.ten || item.tenBaoCao,
                children: [
                  {
                    key: "giay_ksk_1",
                    label: (
                      <a onClick={onPrintPhieu(item, { formA4: true })}>
                        {"Form A4"}
                      </a>
                    ),
                  },
                  {
                    key: "giay_ksk_2",
                    label: (
                      <a onClick={onPrintPhieu(item, { formA4: false })}>
                        {"Form A3"}
                      </a>
                    ),
                  },
                ],
              };
            }

            return {
              key: index + "",
              label: (
                <div style={{ display: "flex" }}>
                  <div
                    style={{ flex: 1 }}
                    onClick={
                      item.ma == "P044"
                        ? onPrintGiayNghiHuongBHXH(item)
                        : onPrintPhieu(item)
                    }
                  >
                    {item.ten || item.tenBaoCao}
                  </div>

                  {item.dsNgonNgu?.length > 0 && (
                    <Popover
                      getPopupContainer={(trigger) => trigger.parentNode}
                      overlayClassName={"step-wrapper-in-options right"}
                      placement="rightTop"
                      content={contentNgonNgu(item)}
                      trigger="click"
                      open={state[`openPopover_${item.id}`] || false}
                      onOpenChange={(value) => {
                        setState({
                          [`openPopover_${item.id}`]: value,
                        });
                      }}
                    >
                      <SVG.IcLanguage
                        color={"var(--color-blue-primary)"}
                        onClick={(e) => {
                          e.stopPropagation();
                        }}
                      />
                    </Popover>
                  )}
                </div>
              ),
            };
          }),
      ]}
    />
  );

  const onHandleTienIchkhac = (e) => () => {
    switch (e) {
      case 1:
        doiTrangThaiFunc();
        break;
      case 2:
        refDoiDVPhongTH.current &&
          refDoiDVPhongTH.current.show({
            dichVuCu: thongTinChiTiet?.nbDichVu?.dichVu?.ten || "",
            tenPhongThucHien:
              thongTinChiTiet?.nbDvKyThuat?.phongThucHien?.ten || "",
            phongThucHienId: parseInt(phongThucHienId) || null,
            dichVuCuId: thongTinChiTiet?.nbDichVu?.dichVu?.id || "",
          });
        break;
      case 3:
        refModalHoSoBenhAn.current &&
          refModalHoSoBenhAn.current.show({
            nbThongTinId: infoNb.nbThongTinId,
            nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
          });
        break;
      default:
        break;
    }
  };

  const onHandleKetNoiVienGutAction = (index) => () => {
    let linkUrl = (
      [LINK_KHAM_BENH_PM_GUT, LINK_DO_SINH_HIEU_GUT, LINK_KE_DON_THUOC_PM_GUT][
        index
      ] || ""
    ).replace("{checkin_id}", thongTinBenhNhan?.nbNgoaiVien?.maHoSo);

    window.open(linkUrl);
  };

  const contentKetNoiVienGut = () => {
    const khamGut = {
      id: 1,
      ten: t("khamBenh.vienGut.khamGut"),
    };
    const doSinhHieuGut = {
      id: 2,
      ten: t("khamBenh.vienGut.doSinhHieuGut"),
    };
    const keDonThuocGut = {
      id: 3,
      ten: t("khamBenh.vienGut.keDonThuocGut"),
    };

    const options = [khamGut, doSinhHieuGut, keDonThuocGut];

    return (
      <Menu
        items={(options || []).map((item, index) => {
          return {
            key: index + 1,
            label: (
              <div onClick={onHandleKetNoiVienGutAction(index)}>{item.ten}</div>
            ),
          };
        })}
      />
    );
  };

  const onShowSoDoPhongGiuong = () => {
    if (configData) {
      refModalSoDoPhongGiuong.current &&
        refModalSoDoPhongGiuong.current.show({
          nbDotDieuTriId: null,
          khoaId: listDataTongHop[0]?.id,
        });
    }
  };

  const getDsChiDinhTuLoaiDichVu = () => {
    if (
      !HIEN_THI_DV_TU_TIEP_DON_VAO_PHIEU_CHI_DINH_TONG_HOP_IN_O_KHAM_BENH.eval()
    ) {
      return [LOAI_DICH_VU.KHAM];
    }

    const dsKham = getState()?.khamBenh?.thongTinKhamBN?.dsKham || [];
    //nếu dsKham rỗng thì chỉ có 1 bản ghi khám => truyền thêm tiếp đón + đặt khám
    if (!dsKham.length) {
      return [LOAI_DICH_VU.TIEP_DON, LOAI_DICH_VU.KHAM, LOAI_DICH_VU.DAT_KHAM];
    }

    const listDsKhamTonTaiThoiGianTiepNhan = dsKham.filter(
      (item) => item?.thoiGianTiepNhan
    );
    const dichVuKhamSomNhat = !isArray(listDsKhamTonTaiThoiGianTiepNhan, 1)
      ? null
      : listDsKhamTonTaiThoiGianTiepNhan.reduce((acc, cur) => {
          return new Date(cur.thoiGianTiepNhan) < new Date(acc.thoiGianTiepNhan)
            ? cur
            : acc;
        });

    let _dsChiDinhTuLoaiDichVu = [
      LOAI_DICH_VU.TIEP_DON,
      LOAI_DICH_VU.KHAM,
      LOAI_DICH_VU.DAT_KHAM,
    ];
    if (isKsk) {
      _dsChiDinhTuLoaiDichVu.push(LOAI_DICH_VU.GOI_KSK);
    }

    return dichVuKhamSomNhat && dichVuKhamSomNhat.id === thongTinChiTiet?.id
      ? _dsChiDinhTuLoaiDichVu
      : [LOAI_DICH_VU.KHAM];
  };

  const onClickButtonSinhHieu = async () => {
    try {
      showLoading();
      const res = await Promise.all([
        nbChiSoSongProvider.getChiSoSongByNbDotDieuTriId({
          nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
          dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.TIEP_DON,
        }),
        nbChiSoSongProvider.getChiSoSongNbRfid(infoNb?.maThe),
      ]);
      const dataSinhHieu = res?.[0]?.data || [];
      let dataCss = res?.[1]?.data || [];
      dataCss = dataCss.reduce((acc, cur) => {
        let thoiGian = moment(cur.thoiGianThucHien).valueOf();
        if (!cur.khoaChiDinhId) {
          cur.khoaChiDinhId =
            thongTinChiTiet?.nbDvKyThuat?.phongThucHien?.khoaId;
        }
        if (
          !dataSinhHieu.some(
            (i) => moment(i.thoiGianThucHien).valueOf() === thoiGian
          )
        ) {
          acc.push(cur);
        }
        return acc;
      }, []);
      if (isArray(dataCss, true)) {
        let params = {
          nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
          listData: dataCss,
          tiepDon: true,
        };
        await onCreate(params);
        await sleep(300);
      }
      hideLoading();
      refModalDoSinhHieu.current &&
        refModalDoSinhHieu.current.show({
          nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.TIEP_DON,
          khoaChiDinhId: thongTinChiTiet?.nbDvKyThuat?.phongThucHien?.khoaId,
        });
    } catch (err) {
      hideLoading();
      message.error(err?.message);
    }
  };

  const onCapNhatDangKy = () => {
    capNhatDangKy({ nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId }).then(
      async (s) => {
        const res = await getByTongHopId(thongTinChiTiet.nbDotDieuTriId);
        if (res?.data?.thuocLaoId) getThuocLaoById(res?.data?.thuocLaoId);
      }
    );
  };

  const onClickImportThongTinKskHopDong = () => {
    refModalImportThongTinKskHopDong.current &&
      refModalImportThongTinKskHopDong.current.show();
  };

  const onImportThongTinKskHopDong = (data) => {
    importThongTinKskHopDong({
      nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
      nbDvKhamId: thongTinChiTiet.id,
      ...data,
    }).then((s) => {
      getNbDvKham({
        dichVuId: thongTinChiTiet.id,
        isForce: true,
      });
    });
  };

  const ganMaBADaiHanOption = useMemo(() => {
    return [
      DOI_TUONG_KCB.NGOAI_TRU,
      DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
      DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
    ].includes(infoNb?.doiTuongKcb) &&
      thongTinBenhNhan?.maBenhAn === null &&
      isArray(listMaBaDaiHan, 1)
      ? {
          id: 7,
          ten: `${t("khamBenh.ganMaBADaiHan")} [CTRL + F2]`,
          onClick: async () => {
            refModalGanMaBADaiHan.current &&
              refModalGanMaBADaiHan.current.show({
                nbDotDieuTriId: infoNb.id,
                nbDotDieuTri: infoNb,
              });
          },
        }
      : null;
  }, [infoNb, thongTinBenhNhan?.maBenhAn, listMaBaDaiHan]);

  const xacNhanBhytOption = useMemo(() => {
    return infoNb.trangThaiXacNhanBaoHiem ===
      TRANG_THAI_XAC_NHAN_BHYT.CHUA_XAC_NHAN &&
      infoNb.doiTuong === DOI_TUONG.BAO_HIEM &&
      checkRole([ROLES["KHAM_BENH"].XAC_NHAN_BHYT])
      ? {
          id: "xacNhanBhyt",
          ten: t("thuNgan.xacNhanBhyt") + " [CTRL + F3]",
          onClick: (callFunc) => {
            onClickXacNhanBhyt(true, callFunc);
          },
        }
      : null;
  }, [infoNb]);

  const huyXacNhanBhytOption = useMemo(() => {
    return infoNb.trangThaiXacNhanBaoHiem ===
      TRANG_THAI_XAC_NHAN_BHYT.DA_XAC_NHAN &&
      infoNb.doiTuong === DOI_TUONG.BAO_HIEM &&
      checkRole([ROLES["KHAM_BENH"].HUY_XAC_NHAN_BHYT])
      ? {
          id: "huyXacNhanBhyt",
          ten: t("thuNgan.huyXacNhanBhyt"),
          onClick: () => onClickXacNhanBhyt(false),
        }
      : null;
  }, [infoNb]);

  const dienBienChiSoXetNghiemOption = useMemo(() => {
    return {
      id: 11,
      ten: t("quanLyNoiTru.xemBieuDoBienDongChiSoXetNghiem"),
      onClick: async () => {
        //nếu chưa có thông tin cơ bản thì call api get
        if (thongTinCoBan?.nbDotDieuTriId != thongTinBenhNhan?.id) {
          await getThongTinCoBan(thongTinBenhNhan?.id);
        }

        refModalDienBienChiSoXetNghiem.current &&
          refModalDienBienChiSoXetNghiem.current.show();
      },
    };
  }, [thongTinCoBan?.nbDotDieuTriId, thongTinBenhNhan?.id]);

  const onGanMaBADaiHan = useRefFunc(() => {
    return ganMaBADaiHanOption?.onClick();
  });

  const onXacNhanBhyt = useRefFunc(() => {
    if (refIsChange.current) return;
    onChangeStateUpdate(true);
    if (
      infoNb?.trangThaiXacNhanBaoHiem === TRANG_THAI_XAC_NHAN_BHYT.DA_XAC_NHAN
    ) {
      message.error(t("khamBenh.nguoiBenhDaXacNhanBhyt"));
      return;
    }
    return xacNhanBhytOption?.onClick(true);
  });

  const onDienBienChiSoXetNghiem = useRefFunc(() => {
    return dienBienChiSoXetNghiemOption?.onClick();
  });

  function onClickXacNhanBhyt(isXacNhan, callFunc) {
    const content = isXacNhan
      ? t("thuNgan.banCoChacChanMuonXacNhanBhyt")
      : t("thuNgan.banCoChacChanMuonHuyXacNhanBhyt");

    const onTuDongInBangKe = async () => {
      if (isXacNhan) {
        let listPhieuInKhamBenh =
          dataKHAM_BENH_XAC_NHAN_BHYT_TU_DONG_IN_MA_PHIEU
            ?.split(",")
            .map((i) => i.trim());

        if (isArray(listPhieuInKhamBenh, true)) {
          const res = await Promise.all([
            await getPhieuInTheoDsMa({
              nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
              maManHinh: MAN_HINH_PHIEU_IN.KHAM_BENH,
              maViTri: VI_TRI_PHIEU_IN.KHAM_BENH.IN_GIAY_TO,
              dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
              chiDinhTuDichVuId: thongTinChiTiet.id,
              dsMaPhieu: listPhieuInKhamBenh,
            }),
          ]);
          const _flattenRes = flatten(res);

          (_flattenRes || []).forEach((item) => {
            let _maBaoCao = MA_BIEU_MAU_EDITOR[item.ma]
              ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
              : "";

            let mhParams = {};
            //kiểm tra phiếu ký số
            if (checkIsPhieuKySo(item)) {
              //nếu là ký số thì lấy các thông tin về lịch sử ký + tên chân ký + báo cáo id
              mhParams = {
                nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
                chiDinhTuDichVuId: thongTinChiTiet.id,
                maManHinh: MAN_HINH_PHIEU_IN.KHAM_BENH,
                maViTri: VI_TRI_PHIEU_IN.KHAM_BENH.IN_GIAY_TO,
                kySo: true,
                maPhieuKy: item.ma,
              };
            }

            if (
              dataCHO_IN_BANG_KE_TIEN_ICH_NGUOI_BENH_NGOAI_TRU?.eval() &&
              [
                DOI_TUONG_KCB.NGOAI_TRU,
                DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
                DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
              ].includes(infoNb?.doiTuongKcb) &&
              item.ma == "P178"
            ) {
              let data = {
                nbDvKhamId: thongTinChiTiet?.id,
                ma: item.ma,
                maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                  ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                  : "",
                loai: 60,
                loaiIn:
                  item.dsBaoCao && item.dsBaoCao.length > 0
                    ? item.dsBaoCao[0]?.loaiIn
                    : null,
                ...mhParams,
                ...(dataKHONG_TU_DONG_DONG_TAB.eval() && mhParams?.kySo
                  ? { notPrint: true }
                  : {}), //setting kySo = true -> Vẫn bật tab nhưng không tự động in phiếu
              };

              if (dataKHONG_TU_DONG_DONG_TAB.eval()) {
                data.khongDongTab = true;
              }

              const url = combineUrlParams(
                `/print-file/bang-ke/${thongTinChiTiet?.nbDotDieuTriId}`,
                {
                  ...data,
                }
              );
              window.open(url);
            } else {
              showFileEditor({
                phieu: item,
                nbDvKhamId: thongTinChiTiet?.id,
                nbThongTinId: infoNb?.nbThongTinId,
                nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
                goiDvId: listNbGoiDv[0]?.id,
                id: thongTinChiTiet?.nbDotDieuTriId,
                ma: item.ma,
                maBaoCao: _maBaoCao,
                khoaChiDinhId: thongTinChiTiet.nbDichVu.khoaChiDinhId,
                mhParams,
                chiDinhTuDichVuId: thongTinChiTiet?.id,
                chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
                khongDongTab: KHONG_TU_DONG_DONG_TAB ? true : undefined,
              });
            }
          });
        }
      }
    };

    const onOk = async () => {
      showLoading();
      const [err] = await toSafePromise(
        nbXacNhanBaoHiemProvider[isXacNhan ? "xacNhanBhyt" : "huyXacNhanBhyt"]({
          id: infoNb.id,
          lyDo: !isXacNhan && "",
        })
      );
      hideLoading();
      if (err !== null) {
        message.error(err?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
        if (
          infoNb?.trangThaiNb < 100 ||
          (err?.code === 1003 && err?.message?.toLowerCase().includes("thuốc"))
        )
          return;

        onTuDongInBangKe();
        return;
      }

      nbDotDieuTriProvider.getByIdTongHop(infoNb.id).then((res) => {
        updateDataKhamBenh({
          infoNb: res.data,
        });
      });
      message.success(
        isXacNhan
          ? t("thuNgan.xacNhanBhytThanhCong")
          : t("thuNgan.huyXacNhanBhytThanhCong")
      );
      onTuDongInBangKe();
    };

    if (isBoolean(callFunc) && !!callFunc) {
      onOk();
      return;
    }

    showConfirm(
      {
        title: t("common.canhBao"),
        content,
        okText: t("common.xacNhan"),
        cancelText: t("common.quayLai"),
        showBtnOk: true,
        typeModal: "warning",
        classNameOkText: "button-warning",
      },
      onOk
    );
  }

  const onClickLapBenhAn = async () => {
    try {
      showLoading();
      const res = await getDsMaBADaiHan(thongTinBenhNhan?.nbThongTinId);
      let dsMaBADangDieuTri = [];
      if (isArray(res, true)) {
        dsMaBADangDieuTri = res.filter(
          (i) => i.trangThai === TRANG_THAI_NB.DANG_DIEU_TRI
        );
      }
      hideLoading();
      if (isArray(dsMaBADangDieuTri, true)) {
        refModalCanhBaoLapBADaiHan.current &&
          refModalCanhBaoLapBADaiHan.current.show(
            {
              data: dsMaBADangDieuTri,
              nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
              nbThongTinId: thongTinBenhNhan?.nbThongTinId,
            },
            () => {
              refModalLapBADaiHan.current &&
                refModalLapBADaiHan.current.show({
                  nbDotDieuTriId: infoNb.id,
                  nbDotDieuTri: infoNb,
                });
            }
          );
      } else {
        refModalLapBADaiHan.current &&
          refModalLapBADaiHan.current.show({
            nbDotDieuTriId: infoNb.id,
            nbDotDieuTri: infoNb,
          });
      }
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onClickLapBenhAnNoiTru = async () => {
    try {
      showLoading();
      await getThongTinCoBan(thongTinChiTiet?.nbDotDieuTriId);
      refModalLapBenhAn.current &&
        refModalLapBenhAn.current.show(false, () => {
          getNbDotDieuTriTongHopTheoId({
            nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
          });
          getById(thongTinChiTiet?.nbDotDieuTriId);
          getThongTinCoBan(thongTinChiTiet?.nbDotDieuTriId);
          getByTongHopId(thongTinChiTiet?.nbDotDieuTriId);
        });
    } finally {
      hideLoading();
    }
  };

  const onClickSuaThoiGianDvKham = () => {
    refModalSuaThoiGianDvKham.current &&
      refModalSuaThoiGianDvKham.current.show({
        dichVuId: thongTinChiTiet?.id,
        thoiGianTiepNhan: thongTinChiTiet?.nbDvKyThuat?.thoiGianTiepNhan,
        thoiGianThucHien:
          thongTinChiTiet?.nbDvKyThuat?.nbDichVu?.thoiGianThucHien,
      });
  };

  const onClickSuaThoiGianKetThucKham = () => {
    refModalSuaThoiGianKetThucKham.current &&
      refModalSuaThoiGianKetThucKham.current.show({
        dichVuId: thongTinChiTiet?.id,
        thoiGianKetLuan: thongTinChiTiet?.nbKetLuan?.thoiGianKetLuan,
      });
  };

  const onClickDieuTriNgoaiTru = async () => {
    try {
      showLoading();
      const res = await getDsMaBADaiHan(thongTinBenhNhan?.nbThongTinId);
      let dsMaBADangDieuTri = [];
      if (isArray(res, true)) {
        dsMaBADangDieuTri = res.filter(
          (i) => i.trangThai === TRANG_THAI_NB.DANG_DIEU_TRI
        );
      }
      hideLoading();
      if (isArray(dsMaBADangDieuTri, true)) {
        showConfirm(
          {
            title: t("common.canhBao"),
            content: t(
              "khamBenh.nbCoMaBADaiHanDangDieuTriBanCoMuonTiepTucDangKyDieuTriNgoaiTruKhong",
              {
                maBenhAn: dsMaBADangDieuTri.map((i) => i.maBenhAn).join(", "),
              }
            ),
            cancelText: t("common.quayLai"),
            okText: t("common.dongY"),
            showBtnOk: true,
            typeModal: "warning",
          },
          () => {
            refDieuTriNgoaiTru.current && refDieuTriNgoaiTru.current.show({});
          },
          () => {}
        );
      } else {
        refDieuTriNgoaiTru.current && refDieuTriNgoaiTru.current.show({});
      }
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onClickManHinhPhu = () => {
    window.open("/thu-ngan/man-hinh-phu", "_blank");
  };

  const putDataCenter = (data) => {
    if (dataIN_QR_TAM_UNG_THANH_TOAN_TREN_PHIEU?.eval()) {
      isofhToolProvider.putDataCenter(data);
    }
  };

  const clearFunc = (isLeave = false, isPhieuThu) => {
    refTimeout.current && clearInterval(refTimeout.current);
    refTimeoutQr.current && clearInterval(refTimeoutQr.current);
    if (dataIN_QR_TAM_UNG_THANH_TOAN_TREN_PHIEU?.eval()) {
      if (isLeave) {
        putDataCenter({ ma: "LOAI_MH_PHU", value: null });
        putDataCenter({ ma: "QR_DATA", value: null });
        putDataCenter({ ma: "TT_PHIEU_THU", value: {} });
        putDataCenter({ ma: "DS_DICH_VU", value: [] });
        putDataCenter({ ma: "QR_VALUE", value: null });
        putDataCenter({ ma: "TT_TAM_UNG", value: {} });
      } else {
        if (isPhieuThu) {
          putDataCenter({ ma: "QR_DATA", value: null });
          putDataCenter({ ma: "TT_PHIEU_THU", value: {} });
          putDataCenter({ ma: "DS_DICH_VU", value: [] });
          putDataCenter({ ma: "LOAI_MH_PHU", value: null });
        } else {
          putDataCenter({ ma: "QR_VALUE", value: null });
          putDataCenter({ ma: "TT_TAM_UNG", value: {} });
        }
      }
      putDataCenter({ ma: "TT_NB", value: {} });
    }
  };

  const kiemTraTrangThaiQrThanhToan = async (item, isPhieuThu) => {
    try {
      let params = {
        nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
        loai: isPhieuThu ? 20 : 10,
        tuBanGhiId: item?.id,
      };
      const res = await kiemTraTrangThaiThanhToanQr(params);
      const { data } = res || {};
      const message = data?.phanHoi?.message;
      if (data?.trangThai === TRANG_THAI_THANH_TOAN_QR.THANH_TOAN) {
        // Show popup thành công
        refTimeout.current && clearInterval(refTimeout.current);
        if (isPhieuThu) {
          putDataCenter({
            ma: "QR_DATA",
            value: { ...item, trangThaiThanhToan: data.trangThai },
          });
        } else {
          putDataCenter({
            ma: "TT_TAM_UNG",
            value: { ...item, trangThaiThanhToan: data.trangThai },
          });
        }
        refModalThongBaoThanhToanQrCode.current &&
          refModalThongBaoThanhToanQrCode.current.show({
            ...data,
            type: "success",
            title: t("thuNgan.thanhToanThanhCong"),
          });
      } else if (data?.trangThai !== TRANG_THAI_THANH_TOAN_QR.TAO_QR) {
        refTimeout.current && clearInterval(refTimeout.current);
      } else {
        const showWarningPopup = () => {
          refModalThongBaoThanhToanQrCode.current &&
            refModalThongBaoThanhToanQrCode.current.show({
              ...data,
              type: "warning",
              title: t("common.canhBao"),
            });
        };
        if (message === null) {
          // Gọi lại api
          refCurrentMsg.current = message;
        } else if (!!message) {
          if (
            refCurrentMsg.current === null &&
            refCurrentMsg.current !== message
          ) {
            // Show popup lỗi
            showWarningPopup();
          } else if (refCurrentMsg.current !== message) {
            // Show popup lỗi
            showWarningPopup();
          }
          refCurrentMsg.current = message;
        }
      }
    } catch (error) {
      console.error(error);
    }
  };

  const onClickPhieuInQr = () => {
    onClickXemQrCode(false);
    refTimeoutQr.current && clearTimeout(refTimeoutQr.current);
    refTimeoutQr.current = setTimeout(() => {
      clearFunc(true);
    }, 120000);
  };

  const onClickXemQrCode = async (isShowQr) => {
    try {
      if (isShowQr) showLoading();
      let dataQr, type, thongTinPhieuThu;

      const onViewQrCode = (item, isPhieuThu, thongTinPhieuThu) => {
        const qrData = item?.qrThanhToan;

        //đẩy dữ liệu sang mh phụ
        if (isPhieuThu) {
          putDataCenter({ ma: "QR_DATA", value: item });
          putDataCenter({ ma: "LOAI_MH_PHU", value: LOAI_MH_PHU.PHIEU_THU });
          putDataCenter({ ma: "TT_PHIEU_THU", value: thongTinPhieuThu });
          putDataCenter({ ma: "TT_NB", value: thongTinBenhNhan });
        } else {
          putDataCenter({ ma: "LOAI_MH_PHU", value: LOAI_MH_PHU.TAM_UNG });
          putDataCenter({ ma: "QR_VALUE", value: item });
          putDataCenter({ ma: "TT_NB", value: thongTinBenhNhan });
          putDataCenter({ ma: "TT_TAM_UNG", value: item });
        }

        //kiểm tra trạng thái thanh toán
        if (item.trangThaiThanhToan === TRANG_THAI_THANH_TOAN_QR.TAO_QR) {
          refTimeout.current && clearInterval(refTimeout.current);
          refTimeout.current = setInterval(() => {
            kiemTraTrangThaiQrThanhToan(item, isPhieuThu);
          }, 5000);
        }

        if (isShowQr) {
          refModalTaoQrCode.current &&
            refModalTaoQrCode.current.show({ qrData }, () => {
              clearFunc(false, isPhieuThu);
              if (refTimeout.current) {
                clearInterval(refTimeout.current);
              }
            });
        }
      };

      const getDeNghiTamUng = async () => {
        const res = await getDeNghiTamUngMoiNhat({
          nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
          dsTrangThai: [40, 50, 55, 15, 56],
        });
        let qrData = null;
        for (const item of res) {
          const pttt = (listAllPhuongThucThanhToan || []).find(
            (i) => i.id === item.phuongThucTtId
          );
          if (
            pttt?.loaiPhuongThucTt === LOAI_PHUONG_THUC_TT.QR_CODE &&
            item.trangThaiThanhToan === TRANG_THAI_THANH_TOAN_QR.TAO_QR
          ) {
            qrData = item;
            break;
          }
        }
        if (qrData) {
          return qrData;
        } else {
          throw new Error("No QR data found");
        }
      };

      const getPhieuThu = async () => {
        const res = await nbPhieuThuProvider.searchTongHop({
          page: "",
          size: "",
          active: true,
          nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
          sort: "thoiGianVaoVien,desc",
          thanhToan: TRANG_THAI_PHIEU_THU_THANH_TOAN.CHUA_THANH_TOAN_TAO_QR,
          dsTrangThaiThanhToan: TRANG_THAI_THANH_TOAN_QR.TAO_QR,
        });
        let dataPhieuThu = isArray(res?.data, true) ? res.data[0] : {};
        const { dsPhuongThucTt, id } = dataPhieuThu;
        let qrData = null;
        if (isArray(dsPhuongThucTt, true)) {
          qrData = dsPhuongThucTt.find(
            (i) => i.loaiPhuongThucTt === LOAI_PHUONG_THUC_TT.QR_CODE
          );
          const res = await nbDichVuProvider.searchAll({
            page: "",
            size: "",
            nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
            phieuThuId: id,
          });
          if (isArray(res?.data, true)) {
            putDataCenter({
              ma: "DS_DICH_VU",
              value: res.data.map((item) => ({ ...item, thaotac: null })),
            });
          }
        }
        if (qrData) {
          return { qrData, ttPhieuThu: dataPhieuThu };
        } else {
          throw new Error("No QR data found");
        }
      };

      switch (dataIN_QR_TAM_UNG_THANH_TOAN_TREN_PHIEU + "") {
        case "1":
          type = "tamUng";
          dataQr = await getDeNghiTamUng();
          break;
        case "2":
          const { ttPhieuThu, qrData } = await getPhieuThu();
          type = "phieuThu";
          dataQr = qrData;
          thongTinPhieuThu = ttPhieuThu;
          break;
        case "3":
          if (infoNb.doiTuong === DOI_TUONG.BAO_HIEM) {
            dataQr = await getDeNghiTamUng();
            type = "tamUng";
          } else {
            const { ttPhieuThu, qrData } = await getPhieuThu();
            dataQr = qrData;
            type = "phieuThu";
            thongTinPhieuThu = ttPhieuThu;
          }
          break;
        default:
          break;
      }
      if (dataQr) {
        if (type === "tamUng") {
          onViewQrCode(dataQr);
        } else {
          onViewQrCode(dataQr, true, thongTinPhieuThu);
        }
      }
    } catch (error) {
      console.error(error);
    } finally {
      if (isShowQr) hideLoading();
    }
  };

  const { listDataPhcn, listDataYhct } = useMemo(() => {
    let listDataPhcn = [];
    let listDataYhct = [];
    if (dsTrangThaiPhcn.length > 0) {
      listDataPhcn = dsTrangThaiPhcn.filter(
        (x) => x.loai === LOAI_DANG_KY.PHUC_HOI_CHUC_NANG
      );
      listDataYhct = dsTrangThaiPhcn.filter(
        (x) => x.loai === LOAI_DANG_KY.Y_HOC_CO_TRUYEN
      );
    }
    return {
      listDataPhcn,
      listDataYhct,
    };
  }, [dsTrangThaiPhcn]);

  const onDangKyPHCN = async () => {
    if (dataBO_BAT_BUOC_CHON_PHAN_LOAI_KHI_DANG_KY_PHCN?.eval()) {
      await dangKyDotPHCN({
        nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
        dsPhanLoaiPhcnId: null,
        loai: LOAI_DANG_KY.PHUC_HOI_CHUC_NANG,
        loaiPhcnId,
        moTa: thongTinChiTiet?.nbChanDoan?.moTa,
        dsCdYhctChinhId: thongTinChiTiet?.nbChanDoan?.dsCdChinhId,
        dsCdYhctKemTheoId: thongTinChiTiet?.nbChanDoan?.dsCdKemTheoId,
        chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
        chiDinhTuDichVuId: thongTinChiTiet?.id,
      });
      await getTrangThaiPhcn({
        nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
      });
    } else {
      refModalChonLoaiPHCN.current &&
        refModalChonLoaiPHCN.current.show({
          maHoSo: thongTinBenhNhan?.maHoSo,
          nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
          maBenhAn: thongTinBenhNhan?.maBenhAn,
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
          chiDinhTuDichVuId: thongTinChiTiet?.id,
        });
    }
  };

  const onDangKyYHCT = async () => {
    await dangKyDotPHCN({
      nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
      dsPhanLoaiPhcnId: null,
      loai: LOAI_DANG_KY.Y_HOC_CO_TRUYEN,
      loaiPhcnId: loaiYhctId,
      chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
      chiDinhTuDichVuId: thongTinChiTiet?.id,
    });
    await getTrangThaiPhcn({
      nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
    });
  };

  const { dieuKienPHCN, dieuKienYHCT } = useMemo(() => {
    const checkRole = (listData) => {
      let dangKy = true,
        huyDangKy = false;
      let idHuyDangKy = null;

      //đã có đợt phcn => ẩn nút đăng ký
      if (listData.length > 0) {
        //update 5/4/2023: NB chưa có bản ghi PHCN nào hoặc tồn tại các bản ghi PHCN và trạng thái tất cả bản ghi = Đã hủy (20) (Ngoài các TH trên thì ko hiển thị button Đăng ký)
        //bổ sung thêm: trạng thái != hoàn thành (40)
        if (listData.some((x) => x.trangThai != 20 && x.trangThai != 40)) {
          dangKy = false;
        }

        //update 5/4/2023: Truyền id bản ghi PHCN có stt nhỏ nhất , trạng thái # Đã hủy (20)
        //bổ sung thêm: trạng thái != hoàn thành (40)
        const _listDangKy = orderBy(
          listData.filter((x) => x.trangThai === 10) || [],
          "stt",
          "asc"
        );
        if (_listDangKy.length > 0) {
          huyDangKy = true;
          idHuyDangKy = _listDangKy[0].id;
        }
      }

      if (thongTinBenhNhan?.trangThai < 5 || thongTinBenhNhan?.trangThai > 50) {
        dangKy = false;
        huyDangKy = false;
        idHuyDangKy = null;
      }
      return {
        dangKy,
        huyDangKy,
        idHuyDangKy,
      };
    };

    const dieuKienPHCN = checkRole(listDataPhcn);
    const dieuKienYHCT = checkRole(listDataYhct);
    return {
      dieuKienPHCN,
      dieuKienYHCT,
    };
  }, [listDataPhcn, listDataYhct, thongTinBenhNhan?.trangThai]);

  const onHuyDangKy = (loai) => async () => {
    try {
      showLoading();
      await huyDangKyDotPHCN({
        nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
        id:
          loai === LOAI_DANG_KY.PHUC_HOI_CHUC_NANG
            ? dieuKienPHCN.idHuyDangKy
            : dieuKienYHCT.idHuyDangKy,
        loai,
      });
      await getTrangThaiPhcn({
        nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
      });
    } finally {
      hideLoading();
    }
  };

  const onClickDoiBacSiKham = () => {
    refModalDoiBacSiKham.current &&
      refModalDoiBacSiKham.current.show({
        nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
        thongTinChiTiet,
      });
  };

  const contentTienIch = () => {
    const huyKham =
      checkRole([ROLES["KHAM_BENH"].DOI_TRANG_THAI]) &&
      ![TRANG_THAI_DICH_VU.CHO_KHAM, TRANG_THAI_DICH_VU.DA_KET_LUAN].includes(
        thongTinChiTiet?.nbDvKyThuat?.trangThai
      )
        ? {
            id: 1,
            ten: t("khamBenh.huyKham"),
          }
        : null;
    const doiPhong =
      [
        TRANG_THAI_DICH_VU.CHO_KHAM,
        TRANG_THAI_DICH_VU.CHUAN_BI_KHAM,
        TRANG_THAI_DICH_VU.DANG_KHAM,
        TRANG_THAI_DICH_VU.DANG_THUC_HIEN_DICH_VU,
        TRANG_THAI_DICH_VU.CHO_KET_LUAN,
        TRANG_THAI_DICH_VU.DANG_KET_LUAN,
        TRANG_THAI_DICH_VU.DA_CHECKIN_KET_LUAN,
        TRANG_THAI_DICH_VU.CHUAN_BI_KET_LUAN,
        TRANG_THAI_DICH_VU.BO_QUA_KET_LUAN,
      ].includes(thongTinChiTiet?.nbDvKyThuat?.trangThai) &&
      checkRole([ROLES["KHAM_BENH"].DOI_PHONG_THUC_HIEN])
        ? {
            id: 2,
            ten: t("khamBenh.doiPhongThucHien"),
          }
        : null;

    const hsba = {
      id: 3,
      ten: t("quanLyNoiTru.xemHoSoBenhAn"),
    };

    const guiGiamDinhBHXH = {
      id: 4,
      ten: t("khamBenh.guiGiamDinhBHXH"),
      onClick: async () => {
        try {
          showLoading();
          await dayGiayNghiBaoHiemById(thongTinChiTiet.nbDotDieuTriId);
        } catch (error) {
        } finally {
          hideLoading();
        }
      },
    };

    const xemKetQuaPdf = {
      id: 5,
      ten: t("quanLyNoiTru.toDieuTri.inKetQua"),
      onClick: async () => {
        refModalXemKetQuaPDF.current &&
          refModalXemKetQuaPDF.current.show({
            nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
          });
      },
    };
    const lapBADaiHan = checkRole([ROLES["KHAM_BENH"].LAP_BA_DAI_HAN])
      ? {
          id: 6,
          ten: t("khamBenh.lapBADaiHan"),
          onClick: onClickLapBenhAn,
        }
      : null;

    const doiMaBADaiHan =
      isArray(listMaBaDaiHan, 2) &&
      [
        DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
        DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
      ].includes(infoNb?.doiTuongKcb) &&
      isBADaiHan
        ? {
            id: 8,
            ten: t("khamBenh.doiMaBADaiHan"),
            onClick: async () => {
              refModalGanMaBADaiHan.current &&
                refModalGanMaBADaiHan.current.show({
                  nbDotDieuTriId: infoNb.id,
                  nbDotDieuTri: infoNb,
                  title: t("khamBenh.doiMaBADaiHan"),
                  type: "doiMaBADaiHan",
                  maBenhAn: thongTinBenhNhan?.maBenhAn,
                });
            },
          }
        : null;
    const hoiChanNgoaiTru = {
      id: 9,
      ten: t("khamBenh.hoiChan"),
      onClick: async () => {
        refModalHoiChanNgoaiTru.current &&
          refModalHoiChanNgoaiTru.current.show({
            // nbDotDieuTriId: infoNb.id,
            // nbDotDieuTri: infoNb,
          });
      },
    };

    const xemLichSuTiemChung = {
      id: 10,
      ten: t("khamBenh.xemLichSuTiemChung"),
      onClick: async () => {
        refModalLichSuTiemChung.current &&
          refModalLichSuTiemChung.current.show({
            thongTinCoBan: {
              ...thongTinBenhNhan,
              maTiemChung: thongTinBenhNhan?.nbTiemChung?.maTiemChung,
              diaChi: thongTinBenhNhan?.nbDiaChi?.diaChi,
            },
          });
      },
    };

    const taoBaoCaoADR = checkRole([
      ROLES["QUAN_LY_BAO_CAO_ADR"].THEM_MOI_CHINH_SUA_BAO_CAO,
    ])
      ? {
          id: 12,
          ten: state.nbPhieuAdr
            ? t("quanLyBaoCaoAdr.xemBaoCaoADR")
            : t("quanLyBaoCaoAdr.taoBaoCaoADR"),
          onClick: async () => {
            if (state.nbPhieuAdr) {
              window.open(`/editor/bao-cao/EMR_BA275/${state.nbPhieuAdr.id}`);
            } else {
              try {
                showLoading();

                const { tienSuBanThan, diUngThuoc } =
                  thongTinChiTiet?.nbHoiBenh || {};
                const tienSu =
                  !tienSuBanThan && !diUngThuoc
                    ? "Chưa ghi nhận"
                    : `${tienSuBanThan || ""}, ${diUngThuoc || ""}`;

                const ngayHienTai = moment().format("YYYY-MM-DD");

                const res = await taoMoiBaoCaoAdr({
                  nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
                  chiDinhTuDichVuId: thongTinChiTiet.nbDotDieuTriId,
                  chiDinhTuLoaiDichVu: LOAI_DICH_VU.TIEP_DON,
                  nguoiBaoCaoId: nhanVienId,
                  ngayBaoCao: ngayHienTai,
                  ngayPhanUng: ngayHienTai,
                  trangThaiPhieu: [TRANG_THAI_PHIEU_NUMBER.TAO_MOI],
                  tienSu,
                });

                if (res?.id) {
                  setState({ nbPhieuAdr: res });

                  window.open(`/editor/bao-cao/EMR_BA275/${res.id}`);
                }
              } catch (error) {
              } finally {
                hideLoading();
              }
            }
          },
        }
      : null;

    const dieuTriNgoaiTru = checkRole([ROLES["KHAM_BENH"].DIEU_TRI_NGOAI_TRU])
      ? {
          id: 13,
          ten: t("khamBenh.dieuTriNgoaiTru"),
          onClick: onClickDieuTriNgoaiTru,
        }
      : null;
    const soDoPhongGiuong = checkRole([ROLES["KHAM_BENH"].DIEU_TRI_NGOAI_TRU])
      ? {
          id: 14,
          ten: t("khamBenh.soDoPhongGiuong"),
          onClick: async () => {
            onShowSoDoPhongGiuong();
          },
        }
      : null;

    const layDlSinhHieu = infoNb?.maThe
      ? {
          id: 15,
          ten: t("tiepDon.layDLSinhHieu"),
          onClick: onClickButtonSinhHieu,
        }
      : null;

    const chuyenKhoa = [
      TRANG_THAI_DICH_VU.CHO_KHAM,
      TRANG_THAI_DICH_VU.CHUAN_BI_KHAM,
      TRANG_THAI_DICH_VU.DANG_KHAM,
      TRANG_THAI_DICH_VU.DANG_THUC_HIEN_DICH_VU,
      TRANG_THAI_DICH_VU.CHO_KET_LUAN,
      TRANG_THAI_DICH_VU.DA_CHECKIN_KET_LUAN,
      TRANG_THAI_DICH_VU.CHUAN_BI_KET_LUAN,
      TRANG_THAI_DICH_VU.DANG_KET_LUAN,
    ].includes(thongTinChiTiet?.nbDvKyThuat?.trangThai)
      ? {
          id: 16,
          ten: t("pttt.chuyenKhoa"),
          onClick: () => {
            handleShowModalChuyenKhoa();
          },
        }
      : null;
    const capNhatDangKy = {
      id: 17,
      ten: t("quanLyDieuTriLao.capNhatSoDangKy"),
      onClick: onCapNhatDangKy,
    };

    const importThongTinKskHopDong = {
      id: 18,
      ten: t("khamBenh.importThongTinKskHopDong"),
      onClick: onClickImportThongTinKskHopDong,
    };

    const batMhPhu = checkRole([ROLES["KHAM_BENH"].BAT_MAN_HINH_PHU])
      ? {
          id: 19,
          ten: t("thuNgan.batMHPhu"),
          onClick: onClickManHinhPhu,
        }
      : null;

    const xemQrCode = checkRole([ROLES["KHAM_BENH"].XEM_QR_CODE])
      ? {
          id: 20,
          ten: t("thuNgan.xemQrCode"),
          onClick: () => onClickXemQrCode(true),
        }
      : null;

    const dangKyPhcn =
      checkRole([ROLES["KHAM_BENH"].HIEN_THI_DANG_KY_PHCN]) &&
      dieuKienPHCN.dangKy &&
      loaiPhcnId
        ? {
            id: 21,
            ten:
              listDataPhcn && listDataPhcn.length > 0
                ? t("quanLyNoiTru.themMoiDotPHCN")
                : t("quanLyNoiTru.dangKyPHCN"),
            onClick: onDangKyPHCN,
          }
        : null;

    const huyDangKyPhcn = dieuKienPHCN.huyDangKy
      ? {
          id: 22,
          ten: t("quanLyNoiTru.huyDangKyPHCN"),
          onClick: onHuyDangKy(LOAI_DANG_KY.PHUC_HOI_CHUC_NANG),
        }
      : null;

    const dangKyYhct =
      checkRole([ROLES["KHAM_BENH"].HIEN_THI_DANG_KY_YHCT]) &&
      dieuKienYHCT.dangKy &&
      loaiYhctId
        ? {
            id: 23,
            ten:
              listDataYhct && listDataYhct.length > 0
                ? t("quanLyNoiTru.themMoiDotYHCT")
                : t("quanLyNoiTru.dangKyYHCT"),
            onClick: onDangKyYHCT,
          }
        : null;

    const huyDangKyYhct = dieuKienYHCT.huyDangKy
      ? {
          id: 24,
          ten: t("quanLyNoiTru.huyDangKyYHCT"),
          onClick: onHuyDangKy(LOAI_DANG_KY.Y_HOC_CO_TRUYEN),
        }
      : null;

    const doiBacSiKham = checkRole([ROLES["KHAM_BENH"].DOI_BAC_SI_KHAM])
      ? {
          id: "doiBacSiKham",
          ten: t("khamBenh.doiBacSiKham"),
          onClick: onClickDoiBacSiKham,
        }
      : null;

    const lapBenhAnNoiTru =
      checkRole([ROLES["KHAM_BENH"].LAP_BENH_AN_NOI_TRU]) &&
      infoNb?.trangThaiNb === TRANG_THAI_NB.CHO_LAP_BENH_AN
        ? {
            id: "lapBenhAnNoiTru",
            ten: t("khamBenh.lapBenhAnNoiTru"),
            onClick: onClickLapBenhAnNoiTru,
          }
        : null;

    const suaThoiGianDvKham = checkRole([
      ROLES["KHAM_BENH"].SUA_THOI_GIAN_DV_KHAM,
    ])
      ? {
          id: "suaThoiGianDvKham",
          ten: t("khamBenh.suaThoiGianDvKham"),
          onClick: onClickSuaThoiGianDvKham,
        }
      : null;

    const suaThoiGianKetThucKham =
      checkRole([ROLES["KHAM_BENH"].SUA_THOI_GIAN_DV_KHAM]) &&
      thongTinChiTiet?.nbDvKyThuat?.trangThai === 150
        ? {
            id: "suaThoiGianKetThucKham",
            ten: t("khamBenh.suaThoiGianKetThucKham"),
            onClick: onClickSuaThoiGianKetThucKham,
          }
        : null;

    const xemLichSuPhongKham = checkRole([
      ROLES["KHAM_BENH"].XEM_LICH_SU_PHONG_KHAM,
    ])
      ? {
          id: 25,
          ten: t("khamBenh.xemLichSuPhongKham"),
          onClick: async () => {
            refModalLichSuPhongKham.current &&
              refModalLichSuPhongKham.current.show({
                thongTinBenhNhan,
                nbDichVuId: thongTinChiTiet?.nbDichVu?.id,
              });
          },
        }
      : null;

    const options = [
      huyKham,
      doiBacSiKham,
      doiPhong,
      hsba,
      guiGiamDinhBHXH,
      xemKetQuaPdf,
      lapBADaiHan,
      ganMaBADaiHanOption,
      doiMaBADaiHan,
      hoiChanNgoaiTru,
      xemLichSuTiemChung,
      dienBienChiSoXetNghiemOption,
      taoBaoCaoADR,
      dieuTriNgoaiTru,
      soDoPhongGiuong,
      layDlSinhHieu,
      chuyenKhoa,
      capNhatDangKy,
      importThongTinKskHopDong,
      xacNhanBhytOption,
      huyXacNhanBhytOption,
      batMhPhu,
      xemQrCode,
      dangKyPhcn,
      huyDangKyPhcn,
      dangKyYhct,
      huyDangKyYhct,
      lapBenhAnNoiTru,
      suaThoiGianDvKham,
      xemLichSuPhongKham,
      suaThoiGianKetThucKham,
    ].filter((item) => item);
    return (
      <Menu
        items={(options || []).map((item) => {
          return {
            key: item.id,
            label: (
              <div onClick={item.onClick || onHandleTienIchkhac(item?.id)}>
                {item.ten}
              </div>
            ),
          };
        })}
      />
    );
  };

  const onShowPrintOption = async () => {
    if (thongTinChiTiet.nbDotDieuTriId) {
      try {
        setState({
          loadingPhieuIn: true,
          searchPhieuIn: null,
        });
        const listPhieu = await getListPhieu({
          nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
          chiDinhTuDichVuId: thongTinChiTiet.id,
          dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
          maManHinh: MAN_HINH_PHIEU_IN.KHAM_BENH,
          maViTri: VI_TRI_PHIEU_IN.KHAM_BENH.IN_GIAY_TO,
        });
        setState({
          listPhieu: listPhieu,
          loadingPhieuIn: false,
        });
      } catch (error) {
        message.error(error?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
        setState({
          loadingPhieuIn: false,
        });
      } finally {
      }
    }
  };

  // ----------------------------------------------------RETURN----------------------------------------------------------------------------

  const doiTrangThaiFunc = () => {
    huyKham([thongTinChiTiet?.nbDvKyThuat?.id]).then((res) => {
      window.location.href = window.location.href;
    });
  };
  return (
    <Main className="step-wrapper">
      <GlobalStyle />
      <div className="section-header">
        <div className="create-title">
          <Tooltip title={onRenderFrameTitle() + trangThaiKham}>
            <div className="title">
              {onRenderFrameTitle()}
              &nbsp;
              <i>
                <small>{trangThaiKham}</small>
              </i>
            </div>
          </Tooltip>
          <div className="sub">{t("khamBenh.bamCtrlSDeLuu")}</div>
        </div>
        <div className="btn-action">
          <div className="btn-action__left"></div>
          <div className="btn-action__right">
            {phieuChiDinhKhamBenh &&
              checkRole([ROLES["KHAM_BENH"].IN_PHIEU_CHI_DINH]) && (
                <div className="btn-action__print">
                  <Button
                    style={{ paddingRight: 30 }}
                    className="custom-btn"
                    type="default"
                    rightIcon={<SVG.IcPrint />}
                    onClick={onPrintPhieuChiDinh(phieuChiDinhKhamBenh)}
                  >
                    <span>
                      {phieuChiDinhKhamBenh.ten ||
                        phieuChiDinhKhamBenh.tenBaoCao}
                    </span>
                  </Button>
                  <div
                    className="btn-action__printIcon"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                  >
                    <Popover
                      getPopupContainer={(trigger) => trigger.parentNode}
                      overlayClassName={"step-wrapper-in-options right"}
                      placement="rightTop"
                      content={contentPhieuChiDinh()}
                      trigger="click"
                      open={openPopoverPhieuChiDinh}
                      onOpenChange={setOpenPopoverPhieuChiDinh}
                    >
                      <SVG.IcOption
                        className="cursor-pointer"
                        onClick={(e) => {
                          e.stopPropagation();
                        }}
                      />
                    </Popover>
                  </div>
                </div>
              )}

            {thongTinBenhNhan?.nbNgoaiVien?.maHoSo !== null &&
              thongTinBenhNhan?.nbNgoaiVien?.loai === 40 && (
                <Dropdown overlay={contentKetNoiVienGut} trigger={["click"]}>
                  <Button className="custom-btn" type="default">
                    <span>{t("khamBenh.vienGut.ketNoiVienGut")}</span>
                  </Button>
                </Dropdown>
              )}

            {phieuTongHopKhamBenh &&
              checkRole([ROLES["KHAM_BENH"].IN_PHIEU_TONG_HOP_KHAM_BENH]) && (
                <Button
                  className="custom-btn"
                  type="default"
                  rightIcon={<SVG.IcPrint />}
                  onClick={onPrintPhieu(phieuTongHopKhamBenh)}
                >
                  <span>{t("khamBenh.phieuKcb")}</span>
                </Button>
              )}
            {phieuKqCls && checkRole([ROLES["KHAM_BENH"].IN_PHIEU_KQ_CLS]) && (
              <Button
                className="custom-btn"
                type="default"
                rightIcon={<SVG.IcPrint />}
                onClick={onPrintPhieu(phieuKqCls)}
              >
                <span>{t("khamBenh.phieuKqCls")}</span>
              </Button>
            )}
            <Dropdown overlay={contentTienIch} trigger={["click"]}>
              <Button
                className="custom-btn"
                type="default"
                onClick={fetchTienIchKhac}
              >
                <span>{t("khamBenh.tienIchKhac")}</span>
              </Button>
            </Dropdown>
            <Dropdown
              overlayClassName="khamBenh__button-print"
              overlay={content}
              trigger={["click"]}
              destroyPopupOnHide
            >
              <Button
                className="custom-btn"
                type="default"
                onClick={onShowPrintOption}
                rightIcon={<SVG.IcPrint />}
                iconHeight={20}
              >
                <span>{t("khamBenh.inGiayTo")}</span>
              </Button>
            </Dropdown>
            {customHeaderRight}
          </div>
        </div>
      </div>
      <div className={`element section-body`} ref={refScrollDiv}>
        {children}
      </div>

      <ModalSignPrint
        ref={refModalSignPrint}
        onClickPhieuInQr={onClickPhieuInQr}
      />
      <ModalInChiDinhTheoDV ref={refModalInChiDinhTheoDV} />
      <ModalDoiDVPhongTH ref={refDoiDVPhongTH} />
      <ModalInXacNhanThucHienTheoDV ref={refModalInXacNhanThucHienTheoDV} />
      <ModalXemKetQuaPDF ref={refModalXemKetQuaPDF} />
      <ModalLapBADaiHan ref={refModalLapBADaiHan} />
      <ModalGanMaBADaiHan ref={refModalGanMaBADaiHan} />
      <ModalHoiChanNgoaiTru ref={refModalHoiChanNgoaiTru} />
      <ModalChonTieuChi ref={refModalChonTieuChi} />
      <ModalLichSuTiemChung ref={refModalLichSuTiemChung} />
      <ModalInToDieuTriNhieuNgay ref={refModalInToDieuTri} />
      <ModalDienBienChiSoXetNghiem ref={refModalDienBienChiSoXetNghiem} />
      <ModalChonPhieuCongKhaiVtyt ref={refModalChonPhieuCongKhaiVtyt} />
      <ModalThemMoiLao ref={refModalDangKyLao} />
      <ModalDieuTriNgoaiTru ref={refDieuTriNgoaiTru} />
      <ModalSoDoPhongGiuong ref={refModalSoDoPhongGiuong} isShowInfo={false} />
      <ModalDoSinhHieu ref={refModalDoSinhHieu} />
      <ModalInPhieuCongKhai isKhamBenh={true} ref={refModalInPhieuCongKhai} />
      <ModalChonPhieuCongKhaiThuoc ref={refModalChonPhieuCongKhaiThuoc} />
      <ModalInPhieuSaoBA ref={refModalInPhieuSaoBA} />
      <ModalChonPhieuTruyenDich ref={refModalChonPhieuTruyenDich} />
      <ModalInPhieuTomTatBenhAn ref={refModalInPhieuThoiGianInKhoaChiDinh} />
      <ModalInPhieuTomTatBenhAn ref={refModalInPhieuTomTatBA} />
      <ModalImport
        ref={refModalImportThongTinKskHopDong}
        onImport={onImportThongTinKskHopDong}
      />
      <ModalCanhBaoLapBADaiHan ref={refModalCanhBaoLapBADaiHan} />
      <ModalTaoQrCode
        ref={refModalTaoQrCode}
        nbDotDieuTriId={thongTinChiTiet?.nbDotDieuTriId}
      />
      <ModalThongBaoThanhToanQrCode
        nbDotDieuTriId={thongTinChiTiet?.nbDotDieuTriId}
        ref={refModalThongBaoThanhToanQrCode}
      />
      <ModalChonLoaiPHCN ref={refModalChonLoaiPHCN} />
      <ModalPhieuBanGiaoNguoiBenh
        ref={refModalPhieuBanGiaoNguoiBenh}
      ></ModalPhieuBanGiaoNguoiBenh>
      <ModalDoiBacSiKham ref={refModalDoiBacSiKham} />
      <ModalChonKhoaPhieuTruyenDich ref={refModalChonKhoaPhieuTruyenDich} />
      <ModalChonKetQuaXetNghiem ref={refModalChonKetQuaXetNghiem} />
      <ModalLapBenhAn
        ref={refModalLapBenhAn}
        id={infoNb?.id}
        title={t("khamBenh.lapBenhAnNoiTru")}
      />
      <ModalSuaThoiGianDvKham ref={refModalSuaThoiGianDvKham} />
      <ModalInPhieuCamDoanChapNhanPTTT
        ref={refModalInPhieuCamDoanChapNhanPTTT}
      />
      <ModalInGiayNghiHuongBHXH ref={refModalInGiayNghiHuongBHXH} />
      <ModalLichSuPhongKham ref={refModalLichSuPhongKham} />
      <ModalSuaThoiGianKetThucKham ref={refModalSuaThoiGianKetThucKham} />
      <ModalInPhieuTienMe ref={refModalInPhieuTienMe} />
      <ModalCheckBaoHiem ref={refModalCheckBaoHiem} isShowButtonOk={true} />
    </Main>
  );
};
export default forwardRef(StepWrapper);
