import styled from "styled-components";
import { ModalTemplate } from "components";
import {
  SEVERITY_COLORS,
  SEVERITY_RANKING,
} from "pages/khamBenh/DonThuoc/ModalChiDinhThuoc/constants";
export const Main = styled.div`
  padding: 16px;
  max-height: calc(100vh - 150px);
  overflow-y: auto;
  @media (max-width: 1366px) {
    padding: 8px;
  }
  .info-service {
    padding: 16px;
    box-shadow: 0px 0px 15px rgba(9, 30, 66, 0.07);
    margin-top: 15px;
    @media (max-width: 1366px) {
      margin-top: 10px;
    }
    @media (max-width: 1366px) {
      padding: 8px;
    }

    border-radius: 8px;
    .info {
      display: flex;
      .title {
        min-width: 120px;
      }
      .detail {
        font-weight: bold;
        padding: 0 5px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 400px;
      }
      .detail2 {
        font-weight: bold;
        padding: 0 5px;
        /* white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis; */
        max-width: 400px;
        & span {
          font-weight: normal;
        }
      }
    }

    &-collapse {
      background-color: #fff !important;
      .ant-collapse-item {
        border-bottom: none !important;
        .ant-collapse-header {
          padding: 0;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          font-weight: 700;
          font-size: 16px;
          line-height: 25px;
          color: rgba(0, 0, 0, 0.85);

          .ant-collapse-arrow {
            position: initial !important;
            margin: 0 15px 0 0 !important;
            padding: 0 !important;
          }

          &-text > h3 {
            margin-bottom: 0;
          }
        }

        .ant-collapse-content {
          & .ant-collapse-content-box {
            padding: 10px;
          }
          .ant-row {
            align-items: center;
          }
          .error {
            @media (max-width: 1537px) {
              font-size: 10px !important;
            }
          }
        }
      }
    }
  }
  .table-row-severity-red {
    .ant-table-cell {
      background-color: ${SEVERITY_COLORS[SEVERITY_RANKING.SEVERE]} !important;
    }
  }
  .table-row-severity-orange {
    .ant-table-cell {
      background-color: ${SEVERITY_COLORS[
        SEVERITY_RANKING.MODERATE
      ]} !important;
    }
  }
  .table-row-severity-yellow {
    .ant-table-cell {
      background: ${SEVERITY_COLORS[SEVERITY_RANKING.MINOR]} !important;
    }
  }

  .tab-content {
    margin-top: 15px;
    min-height: 333px;
    @media (max-width: 1366px) {
      margin-top: 10px;
    }
    & .ant-table-tbody > tr.ant-table-row-selected > td {
      background: none;
    }
  }
  .thong-tin-sao-chep {
    display: flex;
    align-items: center;
    padding: 5px 0;
    .ant-select {
      min-width: 250px;
      padding-left: 10px;
    }
  }
  .item {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 250px;
  }
`;

export const ModalStyled = styled(ModalTemplate)`
  & .ant-modal-header {
    @media (max-width: 1366px) {
      padding: 8px 16px 10px 16px !important;
    }
  }
`;
