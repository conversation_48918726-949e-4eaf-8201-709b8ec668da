import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
  useMemo,
} from "react";
import { Collapse, Col, message, Row, TreeSelect } from "antd";
import { Main, ModalStyled } from "./styled";
import { useDispatch, useSelector } from "react-redux";
import {
  AlertMessage,
  Button,
  Card,
  Checkbox,
  Select,
  Tabs,
  Tooltip,
} from "components";
import { useTranslation } from "react-i18next";
import {
  useConfirm,
  useEnum,
  useListAll,
  useRefFunc,
  useStore,
  useThietLap,
} from "hooks";
import {
  LOAI_DON_THUOC,
  LOAI_DICH_VU,
  DOI_TUONG,
  ENUM,
  FORMAT_DATE,
  THIET_LAP_CHUNG,
  TRANG_THAI_DICH_VU,
  DOI_TUONG_KCB,
  CO_CHE_DUYET_PHAT,
} from "constants/index";
import moment from "moment";
import ThongTinDichVuKyThuat from "../LichSuKham/ThongTinDichVuKyThuat";
import ThongTinDichVuThuoc from "../LichSuKham/ThongTinDichVuThuoc";
import ThongTinDichVuThuocNhaThuoc from "../LichSuKham/ThongTinDichVuThuocNhaThuoc";
import ThongTinDichVuThuocKeNgoai from "../LichSuKham/ThongTinDichVuThuocKeNgoai";
import ThongTinDichVuVatTu from "../LichSuKham/ThongTinDichVuVatTu";
import ModalBoSungThongTinDichVu from "pages/khamBenh/ChiDinhDichVu/ModalBoSungThongTinDichVu";
import ModalThongTinThuoc from "pages/khamBenh/DonThuoc/ModalThongTinThuoc";
import { cloneDeep, groupBy, isEmpty, orderBy, uniq, uniqBy } from "lodash";
import { SVG } from "assets";
import { refActiveSaveBtn } from "../../ThongTin";
import useThuocDaKe from "../TableDonThuoc/useThuocDaKe";
import { isArray } from "utils";
import DOMPurify from "dompurify";
import useCanhBaoTuongTacMims from "pages/khamBenh/DonThuoc/ModalChiDinhThuoc/useCanhBaoTuongTacMims";
import { toSafePromise } from "lib-utils";

const { Panel } = Collapse;
const HOI_BENH = 1,
  KHAM_XET = 2,
  THONG_TIN_KHAM = 3,
  DON_THUOC = 4,
  SINH_HIEU = 6,
  CHAN_DOAN = 7;

const ModalSaoChepDichVu = (props, ref) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const refModalBoSungThongTinDichVu = useRef(null);
  const refModalThongTinThuoc = useRef(null);
  const refModal = useRef(null);
  const refThongTinDichVuThuoc = useRef(null);
  const refThongTinDichVuThuocKeNgoai = useRef(null);
  const refThongTinDichVuThuocNhaThuoc = useRef(null);
  const refThongTinDichVuVatTu = useRef(null);

  const [state, _setState] = useState({
    show: false,
    loaiThongTinSaoChep: [],
    loaiDichVu: [10],
    activeKey: 1,
    saoChepDvKham: {},
    saoChepInfoNb: {},
    saoChepDvKhamNgoai: {},
  });

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const {
    chiDinhKhamBenh: {
      chiDinhDichVu,
      updateData,
      getChiTietDichVuKham,
      getChiTietNguoiBenh,
      getNbKhamChuyenKhoaNgoai,
      luuLoiDan,
    },
    dsThuoc: {
      onChangeInputSearch,
      onSearchDsThuocNhaThuoc,
      updateData: updateDataDsThuoc,
      onSearchDsThuocKeNgoai,
    },
    dsVatTu: { onChangeInputSearch: onChangeInputSearchVatTu },
    dsDichVuKyThuat: { onSearch },
    chiDinhDichVuKho: {
      chiDinhDichVuKho,
      getListDichVuThuoc,
      chiDinhThuocNhaThuoc,
      getListThuocNhaThuoc,
      chiDinhDichVuThuocKeNgoai,
      getListDichVuThuocKeNgoai,
    },
    phongThucHien: { getListPhongTheoDichVu },
    khamBenh: {
      updateThongTinNbDvKham,
      updateData: updateDataKhamBenh,
      updateMultipleThongTinNbDvKham,
    },
    thietLapChonKho: { getListThietLapChonKhoTheoTaiKhoan },
    chiDinhVatTu: { chiDinhDichVu: chiDinhDichVuVatTu, getListDichVuVatTu },
  } = useDispatch();

  const [MAC_DINH_LOAI_THUOC_KHI_KE] = useThietLap(
    THIET_LAP_CHUNG.MAC_DINH_LOAI_THUOC_KHI_KE,
    ""
  );
  const [
    dataCHAN_KHONG_CHO_CHI_DINH_CUNG_THUOC_GIUA_CAC_DICH_VU_KHAM,
    loadFinish,
  ] = useThietLap(
    THIET_LAP_CHUNG.CHAN_KHONG_CHO_CHI_DINH_CUNG_THUOC_GIUA_CAC_DICH_VU_KHAM
  );
  const [listTrangThaiDichVu] = useEnum(ENUM.TRANG_THAI_DICH_VU);
  const [listHuongDieuTriKham] = useEnum(ENUM.HUONG_DIEU_TRI_KHAM);
  const [listKetQuaDieuTri] = useEnum(ENUM.KET_QUA_DIEU_TRI);
  const [listNhomMau] = useEnum(ENUM.NHOM_MAU);
  const [listTheBenhLao] = useEnum(ENUM.THE_BENH_LAO);

  const thongTinChiTiet = useStore("khamBenh.thongTinChiTiet", {});
  const nbChanDoan = useStore("khamBenh.thongTinChiTiet.nbChanDoan", null);
  const listDichVuKyThuat = useStore("dsDichVuKyThuat.listDichVuKyThuat", []);
  const listDsThuoc = useStore("dsThuoc.listDsThuoc", []);
  const listDsThuocNhaThuoc = useStore("dsThuoc.listDsThuocNhaThuoc", []);
  const listDsThuocKeNgoai = useStore("dsThuoc.listDsThuocKeNgoai", []);
  const listDsVatTu = useStore("dsVatTu.listDsVatTu", []);
  const infoNb = useStore("khamBenh.infoNb", {});
  const doiTuong = useStore("khamBenh.infoNb.doiTuong");
  const configData = useStore("chiDinhKhamBenh.configData", null);
  const chiTietKhamNgoai = useStore("khamBenh.chiTietKhamNgoai", {});
  const auth = useStore("auth.auth", {});
  const listThietLapChonKho = useSelector(
    (state) => state.thietLapChonKho.listThietLapChonKhoThuoc
  );
  const { nhanVienId, chucVuId } = auth;

  const [listAllMucDichSuDung] = useListAll("mucDichSuDung", {}, state.show);

  const listDsThuocDangKe = useMemo(() => {
    const dsThuocNhaThuocDangKe = isArray(state.dataThuocNhaThuoc, 1)
      ? state.dataThuocNhaThuoc
          .filter((item) => item.soLuongKhaDung > 0)
          .map((item) => ({
            nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
            dichVuId: item?.dichVuId,
            soLuong: item.soLuong,
            ghiChu: item?.ghiChu,
            lieuDungId: item.lieuDungId,
            duongDungId: item.duongDungId,
            chiDinhTuDichVuId: thongTinChiTiet.id,
            chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
            cachDung: item?.cachDung,
            soNgay: item?.soNgay,
            soLan1Ngay: item.soLan1Ngay,
            soLuong1Lan: item.soLuong1Lan,
            khoaChiDinhId: configData?.khoaChiDinhId,
            soLuongHuy: item?.soLuongHuy || 0,
            lyDoHuy: item?.lyDoHuy,
            soLuongKhaDung: item?.soLuongKhaDung,
            tenDichVu: item?.tenDichVu,
          }))
      : [];

    const dsThuocKhoDangKe = isArray(state.dataThuoc, 1)
      ? state.dataThuoc.map((item) => ({
          nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
          lieuDungId: item.lieuDungId,
          duongDungId: item.duongDungId,
          nbDichVu: {
            dichVuId: item?.dichVuId,
            soLuong: item.soLuong,
            chiDinhTuDichVuId: thongTinChiTiet.id,
            chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
            khoaChiDinhId: configData?.khoaChiDinhId,
            loaiDichVu: item?.loaiDichVu,
            dichVu: {
              id: item?.id,
              ma: item?.ma,
              ten: item?.ten,
              hamLuong: item?.hamLuong,
              tenHoatChat: item?.tenHoatChat,
            },
            tuTra: item?.tuTra,
            khongTinhTien: item?.khongTinhTien,
            ghiChu: item?.ghiChu,
          },
          nbDvKho: {
            khoId: state.khoId,
          },
          dsMucDich: item?.dsMucDich,
          cachDung: item?.cachDung,
          soNgay: item?.soNgay,
          soLan1Ngay: item.soLan1Ngay,
          soLuong1Lan: item.soLuong1Lan,
          thoiDiem: item?.thoiDiem,
        }))
      : [];

    const dsThuocKeNgoaiDangKe = isArray(state.dataThuocKeNgoai, 1)
      ? state.dataThuocKeNgoai.map((item) => ({
          nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
          dichVuId: item?.dichVuId,
          soLuong: item.soLuong,
          ghiChu: item?.ghiChu,
          lieuDungId: item.lieuDungId,
          duongDungId: item.duongDungId,
          thuocChiDinhNgoaiId: item?.thuocChiDinhNgoaiId,
          chiDinhTuDichVuId: thongTinChiTiet.id,
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
          cachDung: item?.cachDung,
          soNgay: item?.soNgay,
          soLan1Ngay: item.soLan1Ngay,
          soLuong1Lan: item.soLuong1Lan,
          soLuongKhaDung: item?.soLuongKhaDung,
          tenDichVu: item?.tenDichVu,
        }))
      : [];
    return [
      ...dsThuocNhaThuocDangKe,
      ...dsThuocKhoDangKe,
      ...dsThuocKeNgoaiDangKe,
    ];
  }, [
    state.dataThuocNhaThuoc,
    state.dataThuoc,
    state.dataThuocKeNgoai,
    thongTinChiTiet,
    configData,
  ]);

  const getDsThuocDangKe = useRefFunc(() => {
    return listDsThuocDangKe;
  }, [listDsThuocDangKe]);

  const {
    MimsPopup,
    action,
    dsTacNhanDiUngId,
    htmlContent,
    builtInteractionList,
    visible,
    error,
    loading,
    rawMimsData, // dữ liệu gốc từ mims
    prescriptionReasons,
    requiredInteractions,
    getColorTuongTacThuoc,
  } = useCanhBaoTuongTacMims({
    getDsThuocDangKe: getDsThuocDangKe,
  });

  const listAllDsThuoc = useMemo(() => {
    return [...(rawMimsData?.dsThuoc || []), ...listDsThuocDangKe];
  }, [rawMimsData, listDsThuocDangKe]);

  const { listDvThuoc, listDvThuocDaKe } = useThuocDaKe({
    visible: state.show,
    isExam: true,
    loaiDonThuoc: LOAI_DON_THUOC.THUOC_KHO,
  });

  useEffect(() => {
    updateDataDsThuoc({ dataSortColumn: { thoiGianChiDinh: 1 } });
  }, []);

  useImperativeHandle(ref, () => ({
    show: (data = {}) => {
      let additionState = {
        khoId: null,
        kho: null,
      };
      let nhaThuoc = false;

      if (MAC_DINH_LOAI_THUOC_KHI_KE.toLowerCase() == "true") {
        if (doiTuong == DOI_TUONG.BAO_HIEM) {
          additionState.loaiDichVu = [LOAI_DON_THUOC.THUOC_KHO];
        } else {
          nhaThuoc = true;
          additionState.loaiDichVu = [LOAI_DON_THUOC.NHA_THUOC];
        }
      }

      if (data?.id) {
        getChiTietDichVuKham(data?.id).then((res) => {
          setState({ saoChepDvKham: res });
        });
        getNbKhamChuyenKhoaNgoai(data?.id).then((res) => {
          setState({ saoChepDvKhamNgoai: res });
        });
      }

      if (data?.nbDotDieuTriId) {
        onChangeInputSearch({
          isThuocKho: true,
          nbDotDieuTriId: data?.nbDotDieuTriId,
          chiDinhTuDichVuId: data?.id,
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
          nhaThuoc: nhaThuoc,
          dsTrangThaiHoan: [0, 10, 20],
          page: "",
          size: "",
        });

        onSearchDsThuocNhaThuoc({
          nbDotDieuTriId: data?.nbDotDieuTriId,
          chiDinhTuDichVuId: data?.id,
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
          nhaThuoc: nhaThuoc,
          dsTrangThaiHoan: [0, 10, 20],
          page: "",
          size: "",
        });

        onSearchDsThuocKeNgoai({
          nbDotDieuTriId: data?.nbDotDieuTriId,
          chiDinhTuDichVuId: data?.id,
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
          dsTrangThaiHoan: [0, 10, 20],
          nhaThuoc: nhaThuoc,
          page: "",
          size: "",
        });

        onSearch({
          dataSearch: {
            nbDotDieuTriId: data?.nbDotDieuTriId,
            chiDinhTuDichVuId: data?.id,
            chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
            dsTrangThaiHoan: [0, 10, 20],
            page: "",
            size: "",
          },
        });

        onChangeInputSearchVatTu({
          nbDotDieuTriId: data?.nbDotDieuTriId,
          chiDinhTuDichVuId: data?.id,
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
          dsTrangThaiHoan: [0, 10, 20],
          page: "",
          size: "",
        });

        getChiTietNguoiBenh(data?.nbDotDieuTriId).then((res) => {
          setState({ saoChepInfoNb: res });
        });
      }

      setState({ show: true, data, ...additionState });
    },
    hide: () => {
      setState({ show: false });
    },
  }));

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
      action.onReset(); // reset mims data
    }
  }, [state.show]);

  useEffect(() => {
    if (
      state.show &&
      configData &&
      configData.nbDotDieuTriId &&
      window.location.pathname.indexOf("/kham-benh") !== -1
    ) {
      getListThietLapChonKhoTheoTaiKhoan({
        loaiDoiTuongId: configData.thongTinNguoiBenh?.loaiDoiTuongId,
        loaiDichVu: LOAI_DICH_VU.THUOC,
        khoaNbId: configData.thongTinNguoiBenh?.khoaNbId,
        khoaChiDinhId: configData.khoaChiDinhId,
        doiTuong: configData.thongTinNguoiBenh?.doiTuong,
        noiTru: [
          DOI_TUONG_KCB.NGOAI_TRU,
          DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
          DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
        ].includes(configData.thongTinNguoiBenh?.doiTuongKcb)
          ? false
          : true,
        capCuu: configData.thongTinNguoiBenh?.capCuu,
        phongId: configData.phongThucHienId,
        nhanVienId: nhanVienId,
        chucVuId: chucVuId,
        canLamSang: false,
      });
    }
  }, [configData, state.isVacxin, state.show]);

  const dataKho = useMemo(() => {
    if (state?.loaiDichVu?.includes(LOAI_DON_THUOC.THUOC_KHO))
      return uniqBy(listThietLapChonKho || [], "id");
    return [];
  }, [state.loaiDichVu, listThietLapChonKho]);

  useEffect(() => {
    if (state?.loaiDichVu?.includes(LOAI_DON_THUOC.THUOC_KHO)) {
      if (listThietLapChonKho.length === 1) {
        setState({
          khoId: listThietLapChonKho[0]?.id,
        });
      } else if (
        MAC_DINH_LOAI_THUOC_KHI_KE &&
        MAC_DINH_LOAI_THUOC_KHI_KE.toLowerCase() == "true" &&
        listThietLapChonKho.length > 1
      ) {
        const sortKho = orderBy(listThietLapChonKho, "uuTien", "asc");
        setState({ khoId: sortKho[0].id });
      } else {
        setState({ khoId: null });
      }
    } else {
      setState({ khoId: null });
    }
  }, [listThietLapChonKho, MAC_DINH_LOAI_THUOC_KHI_KE, state.loaiDichVu]);

  const onShowCanhBaoKeTrung = (dsThuocTrung) => {
    if (!isArray(dsThuocTrung, true)) return;

    const messThuoc = t("khamBenh.chiDinh.tenThuoc").replace(
      "{ten_thuoc}",
      dsThuocTrung[0]?.tenDichVu || ""
    );

    const messCanhBao = uniq(
      dsThuocTrung.map((x) =>
        t("khamBenh.chiDinh.canhBaoKeTrungThuoc")
          .replace("{ten_bac_si}", x.tenBacSiChiDinh)
          .replace("{so_luong}", x.soLuong)
          .replace("{dvt}", x.tenDonViTinh)
      )
    ).join(", ");

    const msg = `${messThuoc} ${messCanhBao}`;

    showConfirm({
      title: t("common.canhBao"),
      content: msg,
      cancelText: t("common.huy"),
      okText: t("common.xacNhan"),
      showImg: false,
      showBtnOk: false,
      typeModal: "warning",
    });
  };

  const filterByCoCheDuyetPhatChung = (data, listKho, khoId) => {
    return (data || []).filter((item) => {
      const kho = listKho?.find((k) => k.id === (khoId ?? item.khoId));
      return kho?.dsCoCheDuyetPhat?.includes(
        CO_CHE_DUYET_PHAT.DUYET_PHAT_CHUNG
      );
    });
  };

  useEffect(() => {
    if (!state.show || !loadFinish) return;

    if (!state.loaiDichVu?.includes(20)) {
      return setState({
        dataThuoc: [],
        selectedRowKeysThuoc: [],
        isCheckedAllThuoc: false,
      });
    }

    const isRuleEnabled =
      dataCHAN_KHONG_CHO_CHI_DINH_CUNG_THUOC_GIUA_CAC_DICH_VU_KHAM?.eval() &&
      configData?.thongTinNguoiBenh?.doiTuong === DOI_TUONG.BAO_HIEM &&
      state.khoId;
    let _listDSThuoc = cloneDeep(listDsThuoc);
    let dsThuocTrung = [];

    if (isRuleEnabled) {
      const thuocMoi = filterByCoCheDuyetPhatChung(
        _listDSThuoc,
        listThietLapChonKho,
        state.khoId
      );
      const thuocDaKe = filterByCoCheDuyetPhatChung(
        listDvThuoc,
        listThietLapChonKho
      );

      if (isArray(thuocMoi, true) && isArray(thuocDaKe, true)) {
        const idsDaKe = new Set(thuocDaKe.map((t) => t.id));
        dsThuocTrung = thuocMoi.filter((t) => idsDaKe.has(t.id));
        _listDSThuoc = _listDSThuoc.filter((t) => !idsDaKe.has(t.id));
      }

      if (dsThuocTrung.length) {
        setTimeout(() => onShowCanhBaoKeTrung(dsThuocTrung), 200);
      }
    }

    setState({
      dataThuoc: _listDSThuoc,
      selectedRowKeysThuoc: _listDSThuoc.map((t) => t.id),
      isCheckedAllThuoc: _listDSThuoc.length === listDsThuoc.length,
      dsThuocTrung,
    });
  }, [
    listDsThuoc,
    state.show,
    state.loaiDichVu,
    listDvThuoc,
    listThietLapChonKho,
    dataCHAN_KHONG_CHO_CHI_DINH_CUNG_THUOC_GIUA_CAC_DICH_VU_KHAM,
    loadFinish,
    configData,
    state.khoId,
  ]);

  useEffect(() => {
    if (!state.show) return null;
    if (state?.loaiDichVu.includes(10)) {
      setState({
        dataThuocNhaThuoc: listDsThuocNhaThuoc,
        selectedRowKeysThuocNhaThuoc: listDsThuocNhaThuoc.map(
          (item) => item?.id
        ),
        isCheckedAllThuocNhaThuoc: true,
      });
    } else {
      setState({
        dataThuocNhaThuoc: [],
        selectedRowKeysThuocNhaThuoc: [],
        isCheckedAllThuocNhaThuoc: false,
      });
    }
  }, [listDsThuocNhaThuoc, state.show, state?.loaiDichVu]);

  useEffect(() => {
    if (!state.show) return null;
    if (state?.loaiDichVu.includes(150)) {
      setState({
        dataThuocKeNgoai: listDsThuocKeNgoai,
        selectedRowKeysThuocKeNgoai: listDsThuocKeNgoai.map((item) => item?.id),
        isCheckedAllThuocKeNgoai: true,
      });
    } else {
      setState({
        dataThuocKeNgoai: [],
        selectedRowKeysThuocKeNgoai: [],
        isCheckedAllThuocKeNgoai: false,
      });
    }
  }, [listDsThuocKeNgoai, state.show, state?.loaiDichVu]);

  useEffect(() => {
    if (!state.show) return null;
    if (state?.loaiDichVu.includes(100)) {
      setState({
        dataVatTu: listDsVatTu,
        selectedRowKeysVatTu: listDsVatTu.map((item) => item?.id),
        isCheckedAllVatTu: true,
      });
    } else {
      setState({
        dataVatTu: [],
        selectedRowKeysVatTu: [],
        isCheckedAllVatTu: false,
      });
    }
  }, [listDsVatTu, state.show, state?.loaiDichVu]);

  useEffect(() => {
    if (!state.show) return null;
    if (state?.loaiDichVu.includes(30)) {
      setState({
        dataDvkt: listDichVuKyThuat,
        selectedRowKeysDvkt: listDichVuKyThuat.map((item) => item?.id),
        isCheckedAllDvkt: true,
      });
      updateData({
        listLoaiDichVu: listDichVuKyThuat.map((item) => item?.loaiDichVu),
      });
    } else {
      setState({
        dataDvkt: [],
        selectedRowKeysDvkt: [],
        isCheckedAllDvkt: false,
      });
    }
  }, [listDichVuKyThuat, state.show, state?.loaiDichVu]);

  const onListDataThuocNhaThuoc = (data = []) => {
    setState({
      dataThuocNhaThuoc: listDsThuocNhaThuoc.filter((x) => data.includes(x.id)),
      selectedRowKeysThuocNhaThuoc: data,
      isCheckedAllThuocNhaThuoc:
        listDsThuocNhaThuoc.length === data.length ? true : false,
    });
  };

  const onListDataThuocKeNgoai = (data = []) => {
    setState({
      dataThuocKeNgoai: listDsThuocKeNgoai.filter((x) => data.includes(x.id)),
      selectedRowKeysThuocKeNgoai: data,
      isCheckedAllThuocKeNgoai: listDsThuocKeNgoai.length === data.length,
    });
  };

  const onListDataVatTu = (data = []) => {
    setState({
      dataVatTu: listDsVatTu.filter((x) => data.includes(x.id)),
      selectedRowKeysVatTu: data,
      isCheckedAllVatTu: listDsVatTu.length === data.length,
    });
  };

  const onListDataDvkt = (data) => {
    let dataDvkt = listDichVuKyThuat.filter((x) => data.includes(x.id));
    setState({
      dataDvkt: dataDvkt,
      selectedRowKeysDvkt: data,
      isCheckedAllDvkt: listDichVuKyThuat.length === data.length ? true : false,
    });
    updateData({
      listLoaiDichVu: dataDvkt.map((item) => item?.loaiDichVu),
    });
  };

  const onListDataThuoc = (data = []) => {
    setState({
      dataThuoc: listDsThuoc.filter((x) => data.includes(x.id)),
      selectedRowKeysThuoc: data,
      isCheckedAllThuoc: listDsThuoc.length === data.length ? true : false,
    });
  };

  const onUpdateLoaiHinhThanhToan = (data) => {
    setState({ dataLHTT: data });
  };

  const tabItems = useMemo(() => {
    const items = [];

    if (state?.loaiDichVu.includes(10)) {
      items.push({
        key: "1",
        label: t("khamBenh.donThuoc.thuocNhaThuoc"),
        children: (
          <ThongTinDichVuThuocNhaThuoc
            ref={refThongTinDichVuThuocNhaThuoc}
            nbDotDieuTriId={state?.data?.nbDotDieuTriId}
            id={state?.data?.id}
            onListData={onListDataThuocNhaThuoc}
            selectedRowKeys={state?.selectedRowKeysThuocNhaThuoc}
            isCheckedAll={state?.isCheckedAllThuocNhaThuoc}
            listAllDsThuoc={listAllDsThuoc}
            getColorTuongTacThuoc={getColorTuongTacThuoc}
          />
        ),
        type: 10,
      });
    }

    if (state?.loaiDichVu.includes(20)) {
      items.push({
        key: "2",
        label: t("khamBenh.donThuoc.thuocKho"),
        children: (
          <ThongTinDichVuThuoc
            ref={refThongTinDichVuThuoc}
            nbDotDieuTriId={state?.data?.nbDotDieuTriId}
            id={state?.data?.id}
            onListData={onListDataThuoc}
            selectedRowKeys={state?.selectedRowKeysThuoc}
            isCheckedAll={state?.isCheckedAllThuoc}
            dsThuocTrung={state.dsThuocTrung}
            onShowCanhBaoKeTrung={onShowCanhBaoKeTrung}
            listAllDsThuoc={listAllDsThuoc}
            getColorTuongTacThuoc={getColorTuongTacThuoc}
          />
        ),
        type: 20,
      });
    }

    if (state?.loaiDichVu.includes(150)) {
      items.push({
        key: "3",
        label: t("khamBenh.donThuoc.thuocKeNgoai"),
        children: (
          <ThongTinDichVuThuocKeNgoai
            ref={refThongTinDichVuThuocKeNgoai}
            nbDotDieuTriId={state?.data?.nbDotDieuTriId}
            id={state?.data?.id}
            onListData={onListDataThuocKeNgoai}
            selectedRowKeys={state?.selectedRowKeysThuocKeNgoai}
            isCheckedAll={state?.isCheckedAllThuocKeNgoai}
            listAllDsThuoc={listAllDsThuoc}
            getColorTuongTacThuoc={getColorTuongTacThuoc}
          />
        ),
        type: 150,
      });
    }

    if (state?.loaiDichVu.includes(30)) {
      items.push({
        key: "4",
        label: t("khamBenh.dichVuKyThuat"),
        children: (
          <ThongTinDichVuKyThuat
            nbDotDieuTriId={state?.data?.nbDotDieuTriId}
            id={state?.data?.id}
            onListData={onListDataDvkt}
            selectedRowKeys={state?.selectedRowKeysDvkt}
            isCheckedAll={state?.isCheckedAllDvkt}
            configData={configData}
            onUpdateLoaiHinhThanhToan={onUpdateLoaiHinhThanhToan}
          />
        ),
        type: 30,
      });
    }

    if (state?.loaiDichVu.includes(100)) {
      items.push({
        key: "5",
        label: t("common.vatTu"),
        children: (
          <ThongTinDichVuVatTu
            ref={refThongTinDichVuVatTu}
            nbDotDieuTriId={state?.data?.nbDotDieuTriId}
            id={state?.data?.id}
            onListData={onListDataVatTu}
            selectedRowKeys={state?.selectedRowKeysVatTu}
            isCheckedAll={state?.isCheckedAllVatTu}
          />
        ),
        type: 100,
      });
    }

    return items;
  }, [state, configData, listAllDsThuoc, getColorTuongTacThuoc]);

  useEffect(() => {
    const validKeys = state.loaiDichVu
      .map((type) => {
        switch (type) {
          case 10:
            return "1";
          case 20:
            return "2";
          case 150:
            return "3";
          case 30:
            return "4";
          case 100:
            return "5";
          default:
            return null;
        }
      })
      .filter(Boolean);

    if (!validKeys.includes(String(state.activeKey)) && validKeys.length > 0) {
      setState({ activeKey: validKeys[0] });
    }
  }, [state.loaiDichVu]);

  const onCancel = () => {
    setState({ show: false, loaiDichVu: [10] });
  };

  const onKeThuoc = (payload) => {
    chiDinhDichVuKho(payload).then((s) => {
      getListDichVuThuoc({
        nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
        chiDinhTuDichVuId: thongTinChiTiet.id,
        chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
        dsTrangThaiHoan: [0, 10, 20, 30],
      });
      const newTable = (s || [])
        .map((item) => ({
          ...item,
          dsMucDich: listAllMucDichSuDung.filter(
            (x) => x?.dichVuId === item?.nbDichVu?.dichVuId
          ),
        }))
        .filter((item1) => {
          item1.message && message.error(item1.message);
          return [7624, 8501].includes(item1.code);
        });
      if (newTable.length > 0)
        refModalThongTinThuoc.current &&
          refModalThongTinThuoc.current.show(
            {
              newTable,
              nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
              chiDinhTuDichVuId: thongTinChiTiet.id,
              loaiDonThuoc: state?.loaiDichVu.includes(20) ? 20 : null,
            },
            (options) => {
              setState({ activeKey: options.activeKey, show: false });
            }
          );
    });
  };

  const onKeThuocNhaThuoc = (payload) => {
    let dsThuocSaoChep = payload.filter((x) => x.soLuongKhaDung > 0);
    let dsThuocWarning = payload.filter((x) => !x.soLuongKhaDung);

    if (!dsThuocSaoChep.length) {
      return;
    }

    if (dsThuocWarning.length > 0) {
      showConfirm(
        {
          title: t("khamBenh.thuocHetTon"),
          content: t("khamBenh.thuoc{{thuoc}}hetTonKhaDung", {
            thuoc: dsThuocWarning.map((x) => x.tenDichVu).join(", "),
          }),
          okText: t("common.xacNhan"),
          classNameOkText: "button-confirm",
          showImg: false,
          showBtnOk: true,
          showBtnCancel: false,
          typeModal: "warning",
        },
        () => {}
      );
    }

    chiDinhThuocNhaThuoc(dsThuocSaoChep).then((s) => {
      getListThuocNhaThuoc({
        nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
        chiDinhTuDichVuId: thongTinChiTiet.id,
        chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
        dsTrangThaiHoan: [0, 10, 20],
      });
      const newTable = (s || [])
        .map((item) => ({
          ...item,
          dsMucDich: listAllMucDichSuDung.filter(
            (x) => x?.dichVuId === item?.nbDichVu?.dichVuId
          ),
        }))
        .filter((item1) => {
          item1.message && message.error(item1.message);
          return [7624, 8501].includes(item1.code);
        });
      if (newTable.length > 0)
        refModalThongTinThuoc.current &&
          refModalThongTinThuoc.current.show(
            {
              newTable,
              nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
              chiDinhTuDichVuId: thongTinChiTiet.id,
              loaiDonThuoc: state?.loaiDichVu.includes(20) ? 20 : null,
              thuocNhaThuoc: true,
            },
            (options) => {
              setState({ activeKey: options.activeKey, show: false });
            }
          );
    });
  };

  const onKeThuocKeNgoai = (payload) => {
    chiDinhDichVuThuocKeNgoai(payload).then((s) => {
      getListDichVuThuocKeNgoai({
        nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
        chiDinhTuDichVuId: thongTinChiTiet.id,
        chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
        dsTrangThaiHoan: [0, 10, 20, 30],
      });
      const newTable = (s?.neededUpdateRecord || [])
        .map((item) => ({
          ...item,
          dsMucDich: listAllMucDichSuDung.filter(
            (x) => x?.dichVuId === item?.nbDichVu?.dichVuId
          ),
        }))
        .filter((item1) => {
          item1.message && message.error(item1.message);
          return [7624, 8501].includes(item1.code);
        });
      if (newTable.length > 0)
        refModalThongTinThuoc.current &&
          refModalThongTinThuoc.current.show(
            {
              newTable,
              nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
              chiDinhTuDichVuId: thongTinChiTiet.id,
              loaiDonThuoc: state?.loaiDichVu.includes(150) ? 150 : null,
            },
            (options) => {
              setState({ activeKey: options.activeKey, show: false });
            }
          );
    });
  };

  const onKeDvkt = (dataTamTinhTien) => {
    chiDinhDichVu({ dataTamTinhTien }).then((s) => {
      let dsDichVuCanBoSung = s?.dsDichVuCanBoSung;
      let listPhong = [];
      async function fetchData() {
        try {
          listPhong = await getListPhongTheoDichVu({
            page: "",
            size: "",
            dsDichVuId: s?.dsDichVuCanBoSung.map(
              (item) => item?.nbDichVu?.dichVuId
            ),
            khoaChiDinhId: configData?.khoaChiDinhId,
            doiTuongKcb: configData?.thongTinNguoiBenh?.doiTuongKcb,
          });
        } catch (error) {
          listPhong = [];
        }
        const phongByDichVuId = groupBy(listPhong, "dichVuId");
        (dsDichVuCanBoSung || []).forEach((dichVu) => {
          let dsMucDich = listAllMucDichSuDung.filter(
            (x) => x.dichVuId === dichVu?.nbDichVu?.dichVuId
          );
          dichVu.dsPhongThucHien = phongByDichVuId[dichVu?.nbDichVu?.dichVuId];
          dichVu.dsMucDich = dsMucDich;
        });
        onShowDichVuBoSung(dsDichVuCanBoSung);
      }
      fetchData();
    });
  };

  const onKeVatTu = (payload) => {
    chiDinhDichVuVatTu(payload).then((s) => {
      getListDichVuVatTu({
        nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
        chiDinhTuDichVuId: thongTinChiTiet?.id,
        chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
        dsTrangThaiHoan: [0, 10, 20],
      }).then((s) => {
        let messageResponse = (s || []).filter((item) => item.message);
        if (messageResponse.length) {
          let messageError = messageResponse.filter((x) => x.code !== 0);
          if (messageError.length) {
            message.error(messageError.map((x) => x.message).join(","));
          } else {
            let content = messageResponse[messageResponse.length - 1].message;
            content &&
              showConfirm(
                {
                  title: t("common.canhBao"),
                  content: content,
                  cancelText: t("common.dong"),
                  classNameOkText: "button-error",
                  showImg: true,
                  typeModal: "warning",
                },
                () => {}
              );
          }
        }
      });
    });
  };

  const onShowDichVuBoSung = (dsDichVuCanBoSung) => {
    refModalBoSungThongTinDichVu.current &&
      dsDichVuCanBoSung?.length &&
      refModalBoSungThongTinDichVu.current.show({
        dataSource: dsDichVuCanBoSung,
        isPhauThuat: false,
      });
  };

  const onSaoChepThongTinKham = (loai) => {
    const { nbHoiBenh, nbKhamXet, nbKetLuan, nbChiSoSong, nbChanDoan } =
      state.saoChepDvKham || {};
    const { lyDoDenKham } = state.saoChepInfoNb || {};
    const {
      tuanHoan,
      hoHap,
      mat,
      taiMuiHong,
      thanKinh,
      thanTietNieu,
      tieuHoa,
      coXuongKhop,
      dinhDuong,
      rangHamMat,
      khac,
    } = state.saoChepDvKhamNgoai || {};

    let dataUpdateNbDvKham = {};
    let dataUpdateKhamBenh = {};

    if (loai.includes(HOI_BENH)) {
      dataUpdateNbDvKham["nbHoiBenh"] = {
        quaTrinhBenhLy: nbHoiBenh.quaTrinhBenhLy,
        tienSuBanThan: nbHoiBenh.tienSuBanThan,
        tienSuGiaDinh: nbHoiBenh.tienSuGiaDinh,
        diUngThuoc: nbHoiBenh.diUngThuoc,
      };

      dataUpdateKhamBenh["infoNb"] = {
        ...infoNb,
        lyDoDenKham,
      };
    }
    if (loai.includes(KHAM_XET)) {
      dataUpdateNbDvKham["nbKhamXet"] = {
        toanThan: nbKhamXet.toanThan,
        ghiChu: nbKhamXet.ghiChu,
        giaiDoanBenh: nbKhamXet.giaiDoanBenh,
        dienBien: nbKhamXet.dienBien,
        cacBoPhan: nbKhamXet.cacBoPhan,
      };
    }
    if (loai.includes(THONG_TIN_KHAM)) {
      dataUpdateNbDvKham["nbThongTinKhamNgoai"] = {
        tuanHoan,
        hoHap,
        mat,
        taiMuiHong,
        thanKinh,
        thanTietNieu,
        tieuHoa,
        coXuongKhop,
        dinhDuong,
        rangHamMat,
        khac,
      };
      dataUpdateKhamBenh["chiTietKhamNgoai"] = {
        ...chiTietKhamNgoai,
        tuanHoan,
        hoHap,
        mat,
        taiMuiHong,
        thanKinh,
        thanTietNieu,
        tieuHoa,
        coXuongKhop,
        dinhDuong,
        rangHamMat,
        khac,
      };
    }
    if (loai.includes(SINH_HIEU)) {
      dataUpdateNbDvKham["nbChiSoSong"] = {
        chieuCao: nbChiSoSong.chieuCao,
        canNang: nbChiSoSong.canNang,
        huyetApTamThu: nbChiSoSong.huyetApTamThu,
        huyetApTamTruong: nbChiSoSong.huyetApTamTruong,
        spo2: nbChiSoSong.spo2,
        nhipTho: nbChiSoSong.nhipTho,
        nhietDo: nbChiSoSong.nhietDo,
        mach: nbChiSoSong.mach,
        bmi: nbChiSoSong.bmi,
        nhomMau: nbChiSoSong.nhomMau,
      };
    }
    const listDataChanDoan = [
      "dsCdChinhId",
      "dsCdKemTheoId",
      "cdSoBo",
      "moTa",
      "theBenhLao",
    ];
    if (
      loai.some((i) => listDataChanDoan.includes(i)) ||
      loai.includes(CHAN_DOAN)
    ) {
      for (const item of listDataChanDoan) {
        if (!dataUpdateNbDvKham["nbChanDoan"]) {
          dataUpdateNbDvKham["nbChanDoan"] = {};
        }
        dataUpdateNbDvKham["nbChanDoan"][item] = nbChanDoan[item];
      }
    }

    if (!isEmpty(dataUpdateNbDvKham)) {
      updateMultipleThongTinNbDvKham(dataUpdateNbDvKham);
    }
    if (!isEmpty(dataUpdateKhamBenh)) {
      updateDataKhamBenh(dataUpdateKhamBenh);
    }

    if (loai.includes(DON_THUOC)) {
      luuLoiDan({
        id: thongTinChiTiet.id,
        body: {
          ghiChu: nbKhamXet?.ghiChu,
          loiDan: nbKetLuan?.loiDan,
          soNgayChoDon: nbKetLuan?.soNgayChoDon,
        },
        notUpdateThongTinKham: true,
      });
    }

    if (refActiveSaveBtn.current) {
      refActiveSaveBtn.current();
    }
    message.success(t("khamBenh.saoChepThongTinLuotKhamThanhCong"));
  };

  const onSubmit = async () => {
    //kiểm tra bản ghi sao chép có chung cơ sở với bản ghi khám hiện tại hay không?
    if (state.data?.coSoKcbId !== auth?.coSoKcbId) {
      message.error(
        t("khamBenh.khongSaoChepDichVuKhiKhacCoSoKhamChuaBenh", {
          tenCoSoKcb: state.data?.tenCoSoKcb,
        })
      );
      return;
    }

    if (!nbChanDoan) {
      message.error(t("khamBenh.dichVuKhamChuaDienChanDoan"));
      return;
    }

    if (state?.loaiDichVu?.includes(LOAI_DON_THUOC.THUOC_KHO) && !state.khoId) {
      message.error(t("khamBenh.donThuoc.vuiLongChonKho"));
      return;
    }

    const _trangThai = thongTinChiTiet.nbDvKyThuat.trangThai;
    const _trangThaiTxt =
      listTrangThaiDichVu.find((x) => x.id === _trangThai)?.ten || "";
    //Nếu người bệnh đang ở trạng thái < Đang khám hoặc >= Đã kết luận thì không cho sao chép
    if (
      _trangThai < TRANG_THAI_DICH_VU.DANG_KHAM ||
      _trangThai >= TRANG_THAI_DICH_VU.DA_KET_LUAN
    ) {
      message.error(
        t("khamBenh.chiChoPhepSaoChepVoiDVKhamCoTrangThaiThoaMan", {
          trangThaiHienTai: _trangThaiTxt,
        })
      );
      return;
    }

    const continueFunc = () => {
      if (state.loaiThongTinSaoChep?.length > 0) {
        onSaoChepThongTinKham(state.loaiThongTinSaoChep);
      }

      let dataTamTinhTien = state?.dataDvkt.map((item) => {
        const _selectedLHTT = (state.dataLHTT || []).find(
          (x) => x.id === item.id
        );

        return {
          nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
          nbDichVu: {
            dichVuId: item.dichVuId,
            soLuong: item.soLuong,
            chiDinhTuDichVuId: thongTinChiTiet.id,
            chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
            khoaChiDinhId: configData?.khoaChiDinhId,
            loaiDichVu: item.loaiDichVu,
            thanhTien: item.thanhTien,
            nbGoiDvId: item.nbGoiDvId || undefined,
            nbGoiDvChiTietId: item.nbGoiDvChiTietId || undefined,
            loaiHinhThanhToanId: _selectedLHTT?.loaiHinhThanhToanId,
            tyLeTtDv: item?.tyLeTtDv,
            mucDichId: item?.mucDichId,
          },
          nbDvKyThuat: {
            phongThucHienId: item.phongThucHienId,
            tuVanVienId: item.tuVanVienId,
          },
        };
      });

      const payload = state?.dataThuoc.map((item) => {
        return {
          nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
          lieuDungId: item.lieuDungId,
          duongDungId: item.duongDungId,
          nbDichVu: {
            dichVuId: item?.dichVuId,
            soLuong: item.soLuong,
            chiDinhTuDichVuId: thongTinChiTiet.id,
            chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
            khoaChiDinhId: configData?.khoaChiDinhId,
            loaiDichVu: item?.loaiDichVu,
            dichVu: {
              id: item?.id,
              ma: item?.ma,
              ten: item?.ten,
              hamLuong: item?.hamLuong,
              tenHoatChat: item?.tenHoatChat,
            },
            tuTra: item?.tuTra,
            khongTinhTien: item?.khongTinhTien,
            ghiChu: item?.ghiChu,
          },
          nbDvKho: {
            khoId: state.khoId,
          },
          dsMucDich: item?.dsMucDich,
          cachDung: item?.cachDung,
          soNgay: item?.soNgay,
          soLan1Ngay: item.soLan1Ngay,
          soLuong1Lan: item.soLuong1Lan,
          thoiDiem: item?.thoiDiem,
        };
      });

      const payloadThuocNhaThuoc = state?.dataThuocNhaThuoc.map((item) => {
        return {
          nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
          dichVuId: item?.dichVuId,
          soLuong: item.soLuong,
          ghiChu: item?.ghiChu,
          lieuDungId: item.lieuDungId,
          duongDungId: item.duongDungId,
          chiDinhTuDichVuId: thongTinChiTiet.id,
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
          cachDung: item?.cachDung,
          soNgay: item?.soNgay,
          soLan1Ngay: item.soLan1Ngay,
          soLuong1Lan: item.soLuong1Lan,
          khoaChiDinhId: configData?.khoaChiDinhId,
          soLuongHuy: item?.soLuongHuy || 0,
          lyDoHuy: item?.lyDoHuy,
          soLuongKhaDung: item?.soLuongKhaDung,
          tenDichVu: item?.tenDichVu,
        };
      });

      const payloadThuocKeNgoai = state?.dataThuocKeNgoai.map((item) => {
        return {
          nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
          dichVuId: item?.dichVuId,
          soLuong: item.soLuong,
          ghiChu: item?.ghiChu,
          lieuDungId: item.lieuDungId,
          duongDungId: item.duongDungId,
          thuocChiDinhNgoaiId: item?.thuocChiDinhNgoaiId,
          chiDinhTuDichVuId: thongTinChiTiet.id,
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
          cachDung: item?.cachDung,
          soNgay: item?.soNgay,
          soLan1Ngay: item.soLan1Ngay,
          soLuong1Lan: item.soLuong1Lan,
          soLuongKhaDung: item?.soLuongKhaDung,
          tenDichVu: item?.tenDichVu,
        };
      });

      const payloadVatTu = state.dataVatTu.map((item) => {
        return {
          nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
          sttBo: item?.sttBo,
          nbDichVu: {
            dichVuId: item?.dichVuId,
            soLuong: item.soLuong,
            chiDinhTuDichVuId: thongTinChiTiet.id,
            chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
            khoaChiDinhId: configData?.khoaChiDinhId,
            loaiDichVu: item?.loaiDichVu,
            tuTra: item?.tuTra,
            khongTinhTien: item?.khongTinhTien,
            nguonKhacId: item?.nguonKhacId,
          },
          nbDvKho: {
            khoId: item.khoId,
            moi: item.moi,
            loaiChiDinh: item.item,
          },
          kichCoVtId: item?.kichCoVtId,
        };
      });

      dataTamTinhTien.length && onKeDvkt(dataTamTinhTien);
      payload.length && onKeThuoc(payload);
      payloadThuocNhaThuoc.length && onKeThuocNhaThuoc(payloadThuocNhaThuoc);
      payloadThuocKeNgoai.length && onKeThuocKeNgoai(payloadThuocKeNgoai);
      payloadVatTu.length && onKeVatTu(payloadVatTu);
      onCancel();
    };

    const [error] = await toSafePromise(
      action.kiemTraMims({
        chiDinhTuDichVuId: configData?.chiDinhTuDichVuId,
        chiDinhTuLoaiDichVu: configData?.chiDinhTuLoaiDichVu,
        nbDotDieuTriId: configData?.nbDotDieuTriId,
        dsTacNhanDiUngId: configData?.dsTacNhanDiUngId,
        dsDichVuId: listDsThuocDangKe.map((item) => item?.dichVuId),
        thoiGianYLenh:
          configData?.chiDinhTuLoaiDichVu === LOAI_DICH_VU.TO_DIEU_TRI
            ? configData?.thoiGianYLenh &&
              moment(configData.thoiGianYLenh).format("YYYY-MM-DD HH:mm:ss")
            : null,
      })
    );
    if (error) {
      continueFunc();
    } else {
      action.showMimsPopup();
      action.onContinue = continueFunc;
    }
  };

  const listLoaiDichVu = [
    {
      id: 10,
      ten: t("khamBenh.donThuoc.thuocNhaThuoc"),
    },
    {
      id: 20,
      ten: t("khamBenh.thuocKho"),
    },
    {
      id: 150,
      ten: t("khamBenh.donThuoc.thuocKeNgoai"),
    },
    {
      id: 30,
      ten: t("khamBenh.xetNghiemCdhaTdcn"),
    },
    {
      id: 100,
      ten: t("common.vatTu"),
    },
  ];

  const listThongTinSaoChep = [
    {
      value: THONG_TIN_KHAM,
      key: THONG_TIN_KHAM,
      title: t("khamBenh.khamSan.thongTinKham"),
    },
    {
      value: SINH_HIEU,
      key: SINH_HIEU,
      title: t("title.sinhHieu"),
    },
    {
      value: HOI_BENH,
      key: HOI_BENH,
      title: t("dieuTriDaiHan.hoiBenh"),
    },
    {
      value: KHAM_XET,
      key: KHAM_XET,
      title: t("dieuTriDaiHan.khamXet"),
    },
    {
      value: CHAN_DOAN,
      key: CHAN_DOAN,
      title: t("common.chanDoan"),
      children: [
        {
          value: "dsCdChinhId",
          key: "dsCdChinhId",
          title: t("khamBenh.chanDoan.chanDoanBenh"),
        },
        {
          value: "dsCdKemTheoId",
          key: "dsCdKemTheoId",
          title: t("khamBenh.chanDoan.chanDoanKemTheo"),
        },
        {
          value: "cdSoBo",
          key: "cdSoBo",
          title: t("khamBenh.chanDoan.chanDoanSoBo"),
        },
        {
          value: "moTa",
          key: "moTa",
          title: t("khamBenh.chanDoan.moTaChiTiet"),
        },
        {
          value: "theBenhLao",
          key: "theBenhLao",
          title: t("khamBenh.chanDoan.theBenh"),
        },
      ],
    },
    {
      value: DON_THUOC,
      key: DON_THUOC,
      title: t("khamBenh.frameTitle.donThuoc"),
    },
  ];

  const treeData = [
    {
      title: "Tất cả",
      value: "all",
      selectable: true, // vẫn tick được
      disableCheckbox: false,
      children: listThongTinSaoChep, // vẫn gắn con ở đây
      isLeaf: false, // giữ parent
    },
  ];

  const onChangeLoaiDichVu = (e) => {
    if (e?.includes(10) || e?.includes(20)) {
      let nhaThuoc = false;
      if (e.includes(10) && !e.includes(20)) {
        nhaThuoc = true;
      }
      if (e.includes(10) && e.includes(20)) {
        nhaThuoc = null;
      }
      onChangeInputSearch({
        nbDotDieuTriId: state?.data?.nbDotDieuTriId,
        chiDinhTuDichVuId: state?.data?.id,
        chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
        nhaThuoc: nhaThuoc,
        isThuocKho: true,
        dsTrangThaiHoan: [0, 10, 20],
      });
    }
    setState({ loaiDichVu: e });
  };

  const onSelectKho = (value, data) => {
    setState({ khoId: value, kho: data });
  };

  const allValues = useMemo(() => {
    const getAll = (nodes) =>
      nodes.reduce((acc, n) => {
        acc.push(n.value);
        if (n.children) acc.push(...getAll(n.children));
        return acc;
      }, []);
    return getAll(listThongTinSaoChep); // chỉ tính các node con thật
  }, []);

  const onChangeLoaiThongTinSaoChep = (newValue) => {
    if (newValue.includes("all")) {
      // Nếu chọn "Tất cả"
      setState({ loaiThongTinSaoChep: ["all", ...allValues] });
    } else {
      // Nếu bỏ "Tất cả" thì bỏ hết
      setState({ loaiThongTinSaoChep: newValue.filter((v) => v !== "all") });
    }
  };

  const refMapByType = {
    10: refThongTinDichVuThuocNhaThuoc,
    20: refThongTinDichVuThuoc,
    150: refThongTinDichVuThuocKeNgoai,
    100: refThongTinDichVuVatTu,
  };

  const currentTab = tabItems.find((i) => i.key === String(state.activeKey));

  const onClickSettingTable = () => {
    const ref = refMapByType[currentTab?.type];
    if (ref?.current?.onSettings) {
      ref.current.onSettings();
    }
  };

  const operations =
    currentTab?.type && refMapByType[currentTab.type] ? (
      <Tooltip title={t("common.caiDatHienThiBang")}>
        <SVG.IcSetting onClick={onClickSettingTable} />
      </Tooltip>
    ) : null;

  const renderThongTinLuotKhamSaoChep = () => {
    const { nbHoiBenh, nbKhamXet, nbKetLuan, nbChiSoSong, nbChanDoan } =
      state.saoChepDvKham || {};
    const { lyDoDenKham } = state.saoChepInfoNb || {};
    const {
      tuanHoan,
      hoHap,
      mat,
      taiMuiHong,
      thanKinh,
      thanTietNieu,
      tieuHoa,
      coXuongKhop,
      dinhDuong,
      rangHamMat,
      khac,
    } = state.saoChepDvKhamNgoai || {};

    return (
      <div className="info-service">
        <Collapse className="info-service-collapse" bordered={false}>
          <Panel header={<h3>{t("khamBenh.thongTinLuotKham")}</h3>} key="1">
            <Row>
              <Col className="info" span={12}>
                <div className="title">{t("khamBenh.ngayKetLuanKham")}:</div>
                <div className="detail">
                  {state?.data?.thoiGianKetLuan &&
                    moment(state?.data.thoiGianKetLuan).format(FORMAT_DATE)}
                </div>
              </Col>
              <Col className="info" span={12}>
                <div className="title">
                  {t("khamBenh.chanDoanBenhVaKhamBenh")}:
                </div>
                <Tooltip
                  title={[
                    ...(state?.data?.dsCdChinh || []),
                    ...(state?.data?.dsCdKemTheo || []),
                  ]
                    .map((item) => item.ma + " - " + item.ten)
                    .join(", ")}
                >
                  <div className="detail">
                    {[
                      ...(state?.data?.dsCdChinh || []),
                      ...(state?.data?.dsCdKemTheo || []),
                    ]
                      .map((item) => item.ma + " - " + item.ten)
                      .join(", ")}
                  </div>
                </Tooltip>
              </Col>
              <Col className="info" span={12}>
                <div className="title">{t("khamBenh.bsKetLuanKham")}:</div>
                <div className="detail">{state?.data?.tenBacSiKetLuan}</div>
              </Col>
              <Col className="info" span={6}>
                <div className="title">{t("khamBenh.huongDieuTri")}:</div>
                <div className="detail">
                  {
                    listHuongDieuTriKham.find(
                      (x) => x.id === state?.data?.huongDieuTri
                    )?.ten
                  }
                </div>
              </Col>
              <Col className="info" span={6}>
                <div className="title">{t("khamBenh.ketQua.ketQua")}:</div>
                <div className="detail">
                  {
                    listKetQuaDieuTri.find(
                      (x) => x.id === state?.data?.ketQuaDieuTri
                    )?.ten
                  }
                </div>
              </Col>
              <Col className="info" span={12}>
                <div className="title">{t("common.canNang")}:</div>
                <div className="detail">
                  {state?.data?.canNang ? `${state?.data?.canNang}kg` : ""}
                </div>
              </Col>
            </Row>
          </Panel>
          <Panel header={<h3>{t("title.sinhHieu")}</h3>} key="6">
            <Row>
              <Col className="info" span={6}>
                <div className="title">{t("khamBenh.chanDoan.mach")}:</div>
                <div className="detail2">
                  {nbChiSoSong?.mach}{" "}
                  <span>{t("khamBenh.chanDoan.lanPhut")}</span>
                </div>
              </Col>
              <Col className="info" span={6}>
                <div className="title">{t("khamBenh.chanDoan.nhietDo")}:</div>
                <div className="detail2">
                  {nbChiSoSong?.nhietDo}{" "}
                  <span>
                    <sup>0</sup>C
                  </span>
                </div>
              </Col>
              <Col className="info" span={6}>
                <div className="title">{t(t("khamBenh.chanDoan.spo2"))}:</div>
                <div className="detail2">
                  {nbChiSoSong?.spo2} <span>%</span>
                </div>
              </Col>
              <Col className="info" span={6}>
                <div className="title">{t("khamBenh.chanDoan.chieuCao")}:</div>
                <div className="detail2">
                  {nbChiSoSong?.chieuCao} <span>cm</span>
                </div>
              </Col>
              <Col className="info" span={6}>
                <div className="title">{t("khamBenh.chanDoan.huyetAp")}:</div>
                <div className="detail2">
                  {nbChiSoSong?.huyetApTamThu && nbChiSoSong?.huyetApTamTruong
                    ? `${nbChiSoSong?.huyetApTamThu}/${nbChiSoSong?.huyetApTamTruong}`
                    : ""}{" "}
                  <span> mmHg</span>
                </div>
              </Col>
              <Col className="info" span={6}>
                <div className="title">6. BMI:</div>
                <div className="detail2">{nbChiSoSong?.bmi}</div>
              </Col>
              <Col className="info" span={6}>
                <div className="title">{t("khamBenh.chanDoan.nhipTho")}:</div>
                <div className="detail2">
                  {nbChiSoSong?.chieuCao}{" "}
                  <span>{t("khamBenh.chanDoan.lanPhut")}</span>
                </div>
              </Col>
              <Col className="info" span={6}>
                <div className="title">{t("khamBenh.chanDoan.nhomMau")}:</div>
                <div className="detail2">
                  {listNhomMau.find((x) => x.id === nbChiSoSong?.nhomMau)?.ten}
                </div>
              </Col>
              <Col className="info" span={6}>
                <div className="title">{t("khamBenh.chanDoan.canNang")}:</div>
                <div className="detail2">
                  {nbChiSoSong?.canNang} <span>kg</span>
                </div>
              </Col>
              <Col className="info" span={6}>
                <div className="title">{t("khamBenh.chanDoan.plTheLuc")}:</div>
                <div className="detail2">{nbChiSoSong?.phanLoaiTheLuc}</div>
              </Col>
              <Col className="info" span={6}>
                <div className="title">{t("khamBenh.chanDoan.bopBong")}:</div>
                <Checkbox
                  checked={nbChiSoSong?.troTho === 10}
                  onChange={() => {}}
                />
              </Col>
            </Row>
          </Panel>
          <Panel header={<h3>{t("dieuTriDaiHan.hoiBenh")}</h3>} key="2">
            <Row>
              <Col className="info" span={12}>
                <div className="title">
                  1. {t("khamBenh.hanhChinh.lyDoDenKham")}:
                </div>
                <div className="detail2">{lyDoDenKham}</div>
              </Col>
              <Col className="info" span={12}>
                <div className="title">
                  2. {t("khamBenh.hoiBenh.quaTrinhBenhLy")}:
                </div>
                <div className="detail2">{nbHoiBenh?.quaTrinhBenhLy}</div>
              </Col>

              <Col className="info" span={24}>
                <div className="title">
                  3. {t("khamBenh.hoiBenh.tienSuBenh")}:
                </div>
              </Col>
              <Col className="info" span={24}>
                <div className="title">- {t("khamBenh.hoiBenh.banThan")}:</div>
                <div className="detail2">{nbHoiBenh?.tienSuBanThan}</div>
              </Col>
              <Col className="info" span={24}>
                <div className="title">- {t("khamBenh.hoiBenh.giaDinh")}:</div>
                <div className="detail2">{nbHoiBenh?.tienSuGiaDinh}</div>
              </Col>
              <Col className="info" span={24}>
                <div className="title">
                  - {t("khamBenh.hoiBenh.diUngThuoc")}:
                </div>
                <div className="detail2">{nbHoiBenh?.diUngThuoc}</div>
              </Col>
            </Row>
          </Panel>
          <Panel header={<h3>{t("dieuTriDaiHan.khamXet")}</h3>} key="3">
            <Row>
              <Col className="info" span={12}>
                <div className="title">
                  1. {t("khamBenh.khamXet.toanThan")}:
                </div>
                <div className="detail2">{nbKhamXet?.toanThan}</div>
              </Col>
              <Col className="info" span={12}>
                <div className="title">
                  2. {t("khamBenh.khamXet.cacBoPhan")}:
                </div>
                <div className="detail2">{nbKhamXet?.cacBoPhan}</div>
              </Col>
              <Col className="info" span={12}>
                <div className="title">3. {t("khamBenh.khamXet.luuY")}:</div>
                <div
                  className="detail2"
                  dangerouslySetInnerHTML={{
                    __html: DOMPurify.sanitize(nbKhamXet?.ghiChu),
                  }}
                />
              </Col>
              <Col className="info" span={12}>
                <div className="title">
                  4. {t("khamBenh.khamXet.dienBien")}:
                </div>
                <div className="detail2">{nbKhamXet?.dienBien}</div>
              </Col>
              <Col className="info" span={12}>
                <div className="title">5. {t("hoiChan.giaiDoanBenh")}:</div>
                <div
                  className="detail2"
                  dangerouslySetInnerHTML={{
                    __html: DOMPurify.sanitize(nbKhamXet?.giaiDoanBenh),
                  }}
                />
              </Col>
            </Row>
          </Panel>
          <Panel header={<h3>{t("common.chanDoan")}</h3>} key="7">
            <Row>
              <Col className="info" span={12}>
                <div className="title">
                  1. {t("khamBenh.chanDoan.chanDoanBenh")}:
                </div>
                <div className="detail2">
                  {nbChanDoan?.dsCdChinh
                    ?.map((item) => `${item?.ma} - ${item?.ten}`)
                    ?.join("; ")}
                </div>
              </Col>
              <Col className="info" span={12}>
                <div className="title">
                  2. {t("khamBenh.chanDoan.chanDoanKemTheo")}:
                </div>
                <div className="detail2">
                  {nbChanDoan?.dsCdKemTheo
                    ?.map((item) => `${item?.ma} - ${item?.ten}`)
                    ?.join("; ")}
                </div>
              </Col>
              <Col className="info" span={12}>
                <div className="title">
                  3. {t("khamBenh.chanDoan.chanDoanSoBo")}:
                </div>
                <div className="detail2">{nbChanDoan?.cdSoBo}</div>
              </Col>
              <Col className="info" span={12}>
                <div className="title">
                  4. {t("khamBenh.chanDoan.moTaChiTiet")}:
                </div>
                <div className="detail2">{nbChanDoan?.moTa}</div>
              </Col>
              <Col className="info" span={12}>
                <div className="title">
                  5. {t("khamBenh.chanDoan.theBenh")}:
                </div>
                <div className="detail2">
                  {
                    listTheBenhLao.find((x) => x.id === nbChanDoan?.theBenhLao)
                      ?.ten
                  }
                </div>
              </Col>
            </Row>
          </Panel>
          <Panel header={<h3>{t("khamBenh.khamSan.thongTinKham")}</h3>} key="4">
            <Row>
              <Col className="info" span={12}>
                <div className="title">
                  1. {t("khamBenh.khamSucKhoe.tuanHoan")}:
                </div>
                <div className="detail2">{tuanHoan}</div>
              </Col>
              <Col className="info" span={12}>
                <div className="title">
                  2. {t("khamBenh.thongTinThamKham.coXuongKhop")}:
                </div>
                <div className="detail2">{coXuongKhop}</div>
              </Col>
              <Col className="info" span={12}>
                <div className="title">
                  3. {t("khamBenh.khamOm.thanTietNieu")}:
                </div>
                <div className="detail2">{thanTietNieu}</div>
              </Col>
              <Col className="info" span={12}>
                <div className="title">
                  4. {t("khamBenh.khamSucKhoe.tieuHoa")}:
                </div>
                <div className="detail2">{tieuHoa}</div>
              </Col>
              <Col className="info" span={12}>
                <div className="title">
                  5. {t("khamBenh.khamSucKhoe.thanKinh")}:
                </div>
                <div className="detail2">{thanKinh}</div>
              </Col>
              <Col className="info" span={12}>
                <div className="title">
                  6. {t("khamBenh.khamSucKhoe.hoHap")}:
                </div>
                <div className="detail2">{hoHap}</div>
              </Col>
              <Col className="info" span={12}>
                <div className="title">
                  7. {t("khamBenh.khamOm.taiMuiHong")}:
                </div>
                <div className="detail2">{taiMuiHong}</div>
              </Col>
              <Col className="info" span={12}>
                <div className="title">
                  8. {t("khamBenh.khamOm.rangHamMat")}:
                </div>
                <div className="detail2">{rangHamMat}</div>
              </Col>
              <Col className="info" span={12}>
                <div className="title">9. {t("khamBenh.khamSucKhoe.mat")}:</div>
                <div className="detail2">{mat}</div>
              </Col>
              <Col className="info" span={12}>
                <div className="title">
                  10. {t("khamBenh.khamOm.dinhDuong")}:
                </div>
                <div className="detail2">{dinhDuong}</div>
              </Col>
              <Col className="info" span={12}>
                <div className="title">
                  11. {t("khamBenh.khamSucKhoe.khac")}:
                </div>
                <div className="detail2">{khac}</div>
              </Col>
            </Row>
          </Panel>
          <Panel header={<h3>{t("kho.thongTinDonThuoc")}</h3>} key="5">
            <Row>
              <Col className="info" span={12}>
                <div className="title">1. {t("common.ghiChu")}:</div>
                <div
                  className="detail2"
                  dangerouslySetInnerHTML={{
                    __html: DOMPurify.sanitize(nbKhamXet?.ghiChu),
                  }}
                ></div>
              </Col>
              <Col className="info" span={12}>
                <div className="title">2. {t("common.loiDan")}:</div>
                <div
                  className="detail2"
                  dangerouslySetInnerHTML={{
                    __html: DOMPurify.sanitize(nbKetLuan?.loiDan),
                  }}
                ></div>
              </Col>
              <Col className="info" span={12}>
                <div className="title">
                  3. {t("khamBenh.donThuoc.soNgayChoDon")}:
                </div>
                <div className="detail2">{nbKetLuan?.soNgayChoDon}</div>
              </Col>
            </Row>
          </Panel>
        </Collapse>
      </div>
    );
  };

  return (
    <ModalStyled
      width={1366}
      ref={refModal}
      title={t("khamBenh.thongTinSaoChep")}
      onCancel={onCancel}
      actionLeft={<Button.QuayLai onClick={onCancel} />}
      actionRight={
        <Button
          type="primary"
          minWidth={100}
          onClick={onSubmit}
          rightIcon={<SVG.IcSave />}
        >
          {t("khamBenh.saoChep")}
        </Button>
      }
    >
      <Main>
        <AlertMessage
          keyThietLap={THIET_LAP_CHUNG.TEN_CANH_BAO_POPUP_SAO_CHEP_DV}
        />

        <div className="thong-tin-sao-chep">
          <h1>{t("khamBenh.thongTinLuotKhamSaoChep")}</h1>
          <TreeSelect
            treeData={treeData}
            treeCheckable
            showCheckedStrategy={TreeSelect.SHOW_PARENT}
            placeholder={t("khamBenh.thongTinLuotKhamSaoChep")}
            value={state?.loaiThongTinSaoChep}
            onChange={onChangeLoaiThongTinSaoChep}
          />
        </div>
        {renderThongTinLuotKhamSaoChep()}

        <div className="thong-tin-sao-chep">
          <h1>{t("khamBenh.thongTinDichVuSaoChep")}</h1>
          <Select
            data={listLoaiDichVu}
            mode="multiple"
            showArrow
            value={state?.loaiDichVu}
            onChange={onChangeLoaiDichVu}
          />
          {state?.loaiDichVu?.includes(LOAI_DON_THUOC.THUOC_KHO) && (
            <Select
              data={dataKho}
              showArrow
              value={state?.khoId}
              onChange={onSelectKho}
              placeholder={t("khamBenh.donThuoc.vuiLongChonKho")}
            />
          )}
        </div>
        <Card noPadding={true} className="tab-content">
          <Tabs
            activeKey={String(state.activeKey)}
            onChange={(activeKey) => setState({ activeKey })}
            tabBarExtraContent={operations}
          >
            {tabItems.map((item) => (
              <Tabs.TabPane key={item.key} tab={item.label}>
                {item.children}
              </Tabs.TabPane>
            ))}
          </Tabs>
        </Card>
      </Main>
      <ModalBoSungThongTinDichVu ref={refModalBoSungThongTinDichVu} />
      <ModalThongTinThuoc
        ref={refModalThongTinThuoc}
        thongTinNguoiBenh={infoNb}
      ></ModalThongTinThuoc>
      <MimsPopup
        dsTacNhanDiUngId={dsTacNhanDiUngId}
        htmlContent={htmlContent}
        builtInteractionList={builtInteractionList}
        visible={visible}
        error={error}
        loading={loading}
        action={action}
        getDsThuocDangKe={getDsThuocDangKe}
        prescriptionReasons={prescriptionReasons}
        onPrescriptionReasonChange={action.setPrescriptionReasons}
        requiredInteractions={requiredInteractions}
      />
    </ModalStyled>
  );
};

export default forwardRef(ModalSaoChepDichVu);
