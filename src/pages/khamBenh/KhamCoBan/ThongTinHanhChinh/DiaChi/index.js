import React, { memo, forwardRef } from "react";
import { useStore } from "hooks";
import { useTranslation } from "react-i18next";
import { Col } from "antd";

const DiaChi = ({ fromSetting, index, ...props }, ref) => {
  const { t } = useTranslation();
  const { diaChi } =
    useStore("khamBenh.infoNb");
  return (
    <Col {...props} ref={ref}>
      <div className="info-profile">
        {index}. {t("khamBenh.hanhChinh.diaChi")}:
        <span>
          {diaChi}
        </span>
      </div>
    </Col>
  );
};

export default memo(forwardRef(DiaChi));
