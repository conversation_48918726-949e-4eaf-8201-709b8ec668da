import React, { useMemo } from "react";
import { CollapseWrapper } from "../styled";
import { CaretDownOutlined, CaretUpOutlined } from "@ant-design/icons";
import { Collapse, Row, Col } from "antd";
import { TextField, CheckField } from "components";
import { refElement } from "../../../ThongTin";
import { useTranslation } from "react-i18next";
import { useStore, useThietLap } from "hooks";
import { THIET_LAP_CHUNG, TRANG_THAI_DICH_VU } from "constants/index";
import { isNil } from "lodash";

const { Panel } = Collapse;

const TaiMuiHong = (props) => {
  const { handleSetData, danhMucMauKsk } = props;

  const { t } = useTranslation();
  const thongTinKSK = useStore("nbDichVuKhamKSK.thongTinKSK", {});
  const trangThaiKham = useStore(
    "khamBenh.thongTinChiTiet.nbDvKyThuat.trangThai",
    {}
  );
  const [dataMAC_DINH_TICK_BINH_THUONG_KHAM_LS_KSK] = useThietLap(
    THIET_LAP_CHUNG.MAC_DINH_TICK_BINH_THUONG_KHAM_LS_KSK,
    "FALSE"
  );
  const dataKsk = useMemo(() => {
    const data = danhMucMauKsk || thongTinKSK || {};
    const tickMacDinh = dataMAC_DINH_TICK_BINH_THUONG_KHAM_LS_KSK?.eval();
    const isKetThucKham = trangThaiKham !== TRANG_THAI_DICH_VU.DA_KET_LUAN;

    return {
      ...data,
      taiMuiHongBinhThuong:
        tickMacDinh && isNil(data.taiMuiHongBinhThuong) && isKetThucKham
          ? true
          : data.taiMuiHongBinhThuong,
    };
  }, [
    danhMucMauKsk,
    thongTinKSK,
    dataMAC_DINH_TICK_BINH_THUONG_KHAM_LS_KSK,
    trangThaiKham,
  ]);

  const {
    taiTraiNoiThuong,
    taiTraiNoiTham,
    taiPhaiNoiThuong,
    taiPhaiNoiTham,
    taiMuiHong,
    phanLoaiTaiMuiHong,
    phanLoaiNoiSoiTmh,
    noiSoiTmh,
    taiMuiHongBinhThuong,
    tai,
    mui,
    vom,
    hong,
    thanhQuan,
    chanDoanTmh,
  } = dataKsk;

  return (
    <div className="collapse-content">
      <CollapseWrapper
        bordered={false}
        defaultActiveKey={["tai-mui-hong"]}
        expandIcon={({ isActive }) =>
          isActive ? <CaretDownOutlined /> : <CaretUpOutlined />
        }
        className="site-collapse-custom-collapse"
        // onChange={onCollapsed}
      >
        <Panel
          key={"tai-mui-hong"}
          header={t("khamBenh.khamSucKhoe.taiMuiHong")}
        >
          <div style={{ display: "flex" }}>
            <div style={{ flex: "1" }}>
              - {t("khamBenh.khamSucKhoe.ketQuaKhamThinhLuc")}:
            </div>
            <CheckField
              onChange={(e) =>
                handleSetData(["nbKSK", "taiMuiHongBinhThuong"])(
                  e?.target?.checked
                )
              }
              defaultChecked={taiMuiHongBinhThuong}
            >
              {t("khamBenh.binhThuong")}
            </CheckField>
          </div>
          <Row>
            <Col className="sub-label" span={3}>
              {t("khamBenh.khamSucKhoe.taiTrai")}:
            </Col>
            <Col span={6}>
              <TextField
                label={t("khamBenh.khamSucKhoe.noiThuong")}
                html={taiTraiNoiThuong}
                onChange={handleSetData(["nbKSK", "taiTraiNoiThuong"])}
                maxLength={20}
                refsChild={refElement}
              />
            </Col>
            <Col className="sub-label" flex={"80px"}>
              {t("khamBenh.khamSucKhoe.mHoacHz")};
            </Col>
            <Col flex={"20px"}></Col>

            <Col span={6}>
              <TextField
                label={t("khamBenh.khamSucKhoe.noiTham")}
                html={taiTraiNoiTham}
                onChange={handleSetData(["nbKSK", "taiTraiNoiTham"])}
                maxLength={20}
                refsChild={refElement}
              />
            </Col>
            <Col className="sub-label" flex={"80px"}>
              {t("khamBenh.khamSucKhoe.mHoacHz")};
            </Col>
          </Row>
          <Row>
            <Col className="sub-label" span={3}>
              {t("khamBenh.khamSucKhoe.taiPhai")}:
            </Col>
            <Col span={6}>
              <TextField
                label={t("khamBenh.khamSucKhoe.noiThuong")}
                html={taiPhaiNoiThuong}
                onChange={handleSetData(["nbKSK", "taiPhaiNoiThuong"])}
                maxLength={20}
                refsChild={refElement}
              />
            </Col>
            <Col className="sub-label" flex={"80px"}>
              {t("khamBenh.khamSucKhoe.mHoacHz")};
            </Col>
            <Col flex={"20px"}></Col>

            <Col span={6}>
              <TextField
                label={t("khamBenh.khamSucKhoe.noiTham")}
                html={taiPhaiNoiTham}
                onChange={handleSetData(["nbKSK", "taiPhaiNoiTham"])}
                maxLength={20}
                refsChild={refElement}
              />
            </Col>
            <Col className="sub-label" flex={"80px"}>
              {t("khamBenh.khamSucKhoe.mHoacHz")};
            </Col>
          </Row>
          <TextField
            label={`- ${t("khamBenh.khamSucKhoe.cacBenhVeTaiMuiHong")}`}
            html={taiMuiHong}
            onChange={handleSetData(["nbKSK", "taiMuiHong"])}
            maxLength={1000}
            maxLengthErrorMessage={t("common.truongGioihanSoLuongKyTu", {
              tenTruong: t("khamBenh.khamSucKhoe.cacBenhVeTaiMuiHong"),
              soLuong: 1000,
            })}
            refsChild={refElement}
          />
          <TextField
            label={`- ${t("khamBenh.khamSucKhoe.phanLoai")}`}
            html={phanLoaiTaiMuiHong}
            onChange={handleSetData(["nbKSK", "phanLoaiTaiMuiHong"])}
            maxLength={2000}
            refsChild={refElement}
          />
          <div>
            <b>{t("khamBenh.khamSucKhoe.noiSoiTaMuiHong")}</b>
          </div>
          <TextField
            label={`- ${t("khamBenh.khamSucKhoe.tai")}`}
            html={tai}
            onChange={handleSetData(["nbKSK", "tai"])}
            maxLength={1000}
            maxLengthErrorMessage={t("common.truongGioihanSoLuongKyTu", {
              tenTruong: t("khamBenh.khamSucKhoe.tai"),
              soLuong: 1000,
            })}
            refsChild={refElement}
          />{" "}
          <TextField
            label={`- ${t("khamBenh.khamSucKhoe.mui")}`}
            html={mui}
            onChange={handleSetData(["nbKSK", "mui"])}
            maxLength={1000}
            maxLengthErrorMessage={t("common.truongGioihanSoLuongKyTu", {
              tenTruong: t("khamBenh.khamSucKhoe.mui"),
              soLuong: 1000,
            })}
            refsChild={refElement}
          />{" "}
          <TextField
            label={`- ${t("khamBenh.khamOm.vom")}`}
            html={vom}
            onChange={handleSetData(["nbKSK", "vom"])}
            maxLength={500}
            maxLengthErrorMessage={t("common.truongGioihanSoLuongKyTu", {
              tenTruong: t("khamBenh.khamOm.vom"),
              soLuong: 500,
            })}
            refsChild={refElement}
          />{" "}
          <TextField
            label={`- ${t("khamBenh.khamSucKhoe.hong")}`}
            html={hong}
            onChange={handleSetData(["nbKSK", "hong"])}
            maxLength={1000}
            maxLengthErrorMessage={t("common.truongGioihanSoLuongKyTu", {
              tenTruong: t("khamBenh.khamSucKhoe.hong"),
              soLuong: 1000,
            })}
            refsChild={refElement}
          />{" "}
          <TextField
            label={`- ${t("khamBenh.khamOm.thanhQuan")}`}
            html={thanhQuan}
            onChange={handleSetData(["nbKSK", "thanhQuan"])}
            maxLength={500}
            maxLengthErrorMessage={t("common.truongGioihanSoLuongKyTu", {
              tenTruong: t("khamBenh.khamOm.thanhQuan"),
              soLuong: 500,
            })}
            refsChild={refElement}
          />{" "}
          <TextField
            label={`- ${t("common.chanDoan")}`}
            html={chanDoanTmh}
            onChange={handleSetData(["nbKSK", "chanDoanTmh"])}
            maxLength={500}
            maxLengthErrorMessage={t("common.truongGioihanSoLuongKyTu", {
              tenTruong: t("common.chanDoan"),
              soLuong: 500,
            })}
            refsChild={refElement}
          />
          <TextField
            label={`- ${t("khamBenh.khamSucKhoe.phanLoai")}`}
            html={phanLoaiNoiSoiTmh}
            onChange={handleSetData(["nbKSK", "phanLoaiNoiSoiTmh"])}
            maxLength={2000}
            refsChild={refElement}
          />
          <TextField
            label={`- ${t("khamBenh.khamSucKhoe.ketLuan")}`}
            html={noiSoiTmh}
            onChange={handleSetData(["nbKSK", "noiSoiTmh"])}
            maxLength={1000}
            maxLengthErrorMessage={t("common.truongGioihanSoLuongKyTu", {
              tenTruong: t("khamBenh.khamSucKhoe.ketLuan"),
              soLuong: 1000,
            })}
            refsChild={refElement}
          />
        </Panel>
      </CollapseWrapper>
    </div>
  );
};

export default TaiMuiHong;
