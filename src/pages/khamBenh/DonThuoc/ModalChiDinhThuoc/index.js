import React, {
  forwardRef,
  useImperativeHandle,
  useState,
  useEffect,
  useRef,
  useMemo,
} from "react";
import { useDispatch, useSelector } from "react-redux";
import { GlobalStyle, Main } from "./styled";
import { useTranslation } from "react-i18next";
import {
  Button,
  Select,
  ModalTemplate,
  InputTimeout,
  Popover,
  Checkbox,
  TableWrapper,
  LazyLoad,
} from "components";
import TableDonThuoc from "../../components/TableDonThuoc";
import TableThuocKeNgoai from "../../components/TableThuocKeNgoai";
import {
  TRANG_THAI_DICH_VU,
  LOAI_DICH_VU,
  LIST_LOAI_DON_THUOC,
  LOAI_DON_THUOC,
  ENUM,
  THIET_LAP_CHUNG,
  DOI_TUONG,
  HOTKEY,
  CACHE_KEY,
  TRANG_THAI_HOAN,
  LOAI_CHI_DINH,
  CO_CHE_DUYET_PHAT,
  ROLES,
  LOAI_HOI_CHAN,
} from "constants/index";
import { message, Radio, Select as AntSelect } from "antd";
import ModalThongTinThuoc from "../ModalThongTinThuoc";
import cacheUtils from "lib-utils/cache-utils";
import {
  useCache,
  useConfirm,
  useEnum,
  useGuid,
  useStore,
  useThietLap,
} from "hooks";
import { debounce, flatten, orderBy, some, uniqBy } from "lodash";
import useNbInfoTitle from "pages/khamBenh/hooks/useNbInfoTitle";
import { SVG } from "assets";
import { evalString, groupThuoc } from "utils/chi-dinh-thuoc-utils";
import useTuongTacThuoc from "./useTuongTacThuoc";
import TableThuocDaKe from "pages/khamBenh/components/TableThuocDaKe";
import { isArray } from "utils/index";
import moment, { isMoment } from "moment";
import useKiemTraChungChi from "pages/chiDinhDichVu/components/useKiemTraChungChi";
import useManHinh from "pages/khamBenh/components/TableDonThuoc/useManHinh";
import { checkRole } from "lib-utils/role-utils";
import useCanhBaoTuongTacMims from "./useCanhBaoTuongTacMims";
import { toSafePromise } from "lib-utils";

const { Column } = TableWrapper;

const parseDate = (dateStr) => {
  if (!dateStr) return null;
  const formats = ["YYYY-MM-DD", "MM/DD/YYYY", "DD/MM/YYYY"];
  const fmt = formats.find((f) => moment(dateStr, f, true).isValid());
  return fmt ? moment(dateStr, fmt) : moment.invalid();
};

export const ModalChiDinhThuoc = forwardRef(
  (
    {
      modeThuoc,
      isShowSoLuongDinhMuc = false,
      isKhamBenh,
      listLoaiDonThuoc,
      isToDieuTriNoiTru,
      isXetNghiem,
      onKetThucKham,
      hideThuocKho,
    },
    ref
  ) => {
    const { showConfirm } = useConfirm();
    const { canhBaoThuoc } = useTuongTacThuoc();
    const { t } = useTranslation();
    const nbInfoTitle = useNbInfoTitle();
    const layerId = useGuid();
    const refIsSubmit = useRef(null);
    const refModalChiTietHoiChan = useRef(null);
    const refModalThongTinThuoc = useRef(null);
    const refModal = useRef(null);
    const refInput = useRef(null);
    const refCallback = useRef(null);
    const refSubmit = useRef(null);
    const refListSelectedDv = useRef([]);

    const {
      MimsPopup,
      action,
      dsTacNhanDiUngId,
      htmlContent,
      builtInteractionList,
      visible,
      error,
      loading,
      rawMimsData, // dữ liệu gốc từ mims
      prescriptionReasons,
      requiredInteractions,
      getColorTuongTacThuoc,
    } = useCanhBaoTuongTacMims({
      getDsThuocDangKe: () => {
        return refListSelectedDv.current;
      },
    });

    const { isNoiTruToDieuTri } = useManHinh({
      modeThuoc,
    });
    const [kiemTraChungChi] = useKiemTraChungChi();
    const isNoiTru =
      window.location.pathname.indexOf(
        "/quan-ly-noi-tru/chi-tiet-nguoi-benh-noi-tru"
      ) >= 0;
    const [state, _setState] = useState({
      show: false,
      listDichVu: [],
      listGoiDv: [],
      thanhTien: 0,
      msgWarned: [],
      splitCacheCustomize: {},
      tachDon: false,
      activeHotKey: true,
      khoId: null,
    });
    const setState = (data = {}) => {
      _setState((state) => {
        return { ...state, ...data };
      });
    };
    const [listLoaiThuoc] = useEnum(ENUM.LOAI_THUOC);
    const nbKetLuan = useStore("khamBenh.thongTinChiTiet.nbKetLuan");

    const { dataNb } = useSelector((state) => state.chiDinhDichVuKho);
    const { nhanVienId } = useSelector((state) => state.auth.auth);
    const configData = useStore("chiDinhKhamBenh.configData", {});
    const listAllLieuDung = useSelector(
      (state) => state.lieuDung.listAllLieuDung
    );
    const collapseDichVu = useStore("chiDinhDichVuKho.collapseDichVu", []);
    const thongTinNguoiBenh = useStore(
      "chiDinhKhamBenh.configData.thongTinNguoiBenh"
    );
    const thongTinChiTiet = useStore("khamBenh.thongTinChiTiet", {});
    const listAllNhomDichVuKho = useStore(
      "phanNhomDichVuKho.listAllNhomDichVuKho",
      []
    );
    const [theoSoLuongTonKho, setTheoSoLuongTonKho] = useCache(
      "",
      CACHE_KEY.DATA_THEO_SO_LUONG_TON_KHO_THUOC,
      15,
      false
    );

    const [dataHIEN_THI_HOI_CHAN_KHI_KE_THUOC_DAU_SAO] = useThietLap(
      THIET_LAP_CHUNG.HIEN_THI_HOI_CHAN_KHI_KE_THUOC_DAU_SAO
    );
    const isHienThiHoiChanKhiKeThuocDauSao =
      dataHIEN_THI_HOI_CHAN_KHI_KE_THUOC_DAU_SAO?.eval();

    const [BAT_BUOC_LIEU_DUNG_CACH_DUNG_THUOC_BHYT] = useThietLap(
      THIET_LAP_CHUNG.BAT_BUOC_LIEU_DUNG_CACH_DUNG_THUOC_BHYT
    );
    const [BAT_BUOC_NHAP_SL_SANG_CHIEU_TOI_PL_THUOC_INSULIN] = useThietLap(
      THIET_LAP_CHUNG.BAT_BUOC_NHAP_SL_SANG_CHIEU_TOI_PL_THUOC_INSULIN
    );
    const [dataPHAN_LOAI_THUOC_INSULIN] = useThietLap(
      THIET_LAP_CHUNG.PHAN_LOAI_THUOC_INSULIN
    );
    const [dataTACH_DON_THUOC] = useThietLap(THIET_LAP_CHUNG.TACH_DON_THUOC);
    const [dataHIEN_THI_THUOC_DA_KE] = useThietLap(
      THIET_LAP_CHUNG.HIEN_THI_THUOC_DA_KE
    );
    const [KE_THUOC_KHO_KHI_CHUA_CO_HUONG_DIEU_TRI] = useThietLap(
      THIET_LAP_CHUNG.KE_THUOC_KHO_KHI_CHUA_CO_HUONG_DIEU_TRI
    );
    const [KET_THUC_KHAM_KHI_CHO_DON_THUOC] = useThietLap(
      THIET_LAP_CHUNG.KET_THUC_KHAM_KHI_CHO_DON_THUOC
    );

    const [dataKET_NOI_MIMS] = useThietLap(THIET_LAP_CHUNG.KET_NOI_MIMS);

    const isValidateLieuDungCachDung =
      thongTinNguoiBenh?.doiTuong === DOI_TUONG.BAO_HIEM &&
      BAT_BUOC_LIEU_DUNG_CACH_DUNG_THUOC_BHYT?.eval() &&
      state.loaiDonThuoc === LOAI_DON_THUOC.THUOC_KHO;

    const [dataTAO_PHIEU_LINH_TRA_THUOC_RA_VIEN] = useThietLap(
      THIET_LAP_CHUNG.TAO_PHIEU_LINH_TRA_THUOC_RA_VIEN
    );
    const [dataHIEN_THI_O_KE_THUOC_RA_VIEN] = useThietLap(
      THIET_LAP_CHUNG.HIEN_THI_O_KE_THUOC_RA_VIEN
    );
    const isTaoPhieuLinhTraThuocRaVien =
      dataTAO_PHIEU_LINH_TRA_THUOC_RA_VIEN?.eval();

    const [dataHIEN_THI_CHECKBOX_TU_TRUC] = useThietLap(
      THIET_LAP_CHUNG.HIEN_THI_CHECKBOX_TU_TRUC
    );
    const isHienThiCheckboxTuTruc = dataHIEN_THI_CHECKBOX_TU_TRUC?.eval();
    const [dataAN_THUOC_KHO_KHI_CHI_DINH_DON_THUOC_RA_VIEN, loadFinish] =
      useThietLap(THIET_LAP_CHUNG.AN_THUOC_KHO_KHI_CHI_DINH_DON_THUOC_RA_VIEN);
    const anThuocKho =
      (modeThuoc === "DonThuocRaVien" &&
        dataAN_THUOC_KHO_KHI_CHI_DINH_DON_THUOC_RA_VIEN?.eval()) ||
      hideThuocKho;

    const {
      chiDinhDichVuKho: {
        searchDv,
        updateData,
        tamTinhTien,
        getListDichVuTonKho,
        getListDichVuThuoc,
        chiDinhDichVuThuocKeNgoai,
        getListDichVuThuocKeNgoai,
        chiDinhThuocNhaThuoc,
        getListThuocNhaThuoc,
        chiDinhDvThuocVacxin,
        getListDichVuVacxin,
        kiemTraTuongTacThuocKho,
        kiemTraTuongTacThuocNhaThuoc,
      },
      chiDinhKhamBenh: { updateConfigData, getDsDichVuTiepDon },
      chiDinhThuocDaKe: { chiDinhDichVuThuocDaKe, getListThuocDaKe },
      boChiDinh: { getBoChiDinh },
      phimTat: { onAddLayer, onRemoveLayer, onRegisterHotkey },
      phanNhomDichVuKho: { getListAllNhomDichVuKho },
    } = useDispatch();

    const [CANH_BAO_TRUNG_CHI_DINH_TRONG_NGAY] = useThietLap(
      THIET_LAP_CHUNG.CANH_BAO_TRUNG_CHI_DINH_TRONG_NGAY
    );

    useEffect(() => {
      async function fetchData() {
        const widthCacheThuocDaKe = await cacheUtils.read(
          nhanVienId,
          "DATA_CUSTOMIZE_COLUMN_" + "split_KHAMBENH_KeDonThuoc_DaKe",
          [],
          false
        );
        const widthCacheDonThuoc = await cacheUtils.read(
          nhanVienId,
          "DATA_CUSTOMIZE_COLUMN_" + "split_KHAMBENH_KeDonThuoc_DonThuoc",
          [],
          false
        );
        const widthCacheThuocKeNgoai = await cacheUtils.read(
          nhanVienId,
          "DATA_CUSTOMIZE_COLUMN_" + "split_KHAMBENH_KeDonThuoc_KeNgoai",
          [],
          false
        );
        const splitCacheCustomize = Object.assign(
          {},
          { widthCacheThuocDaKe, widthCacheDonThuoc, widthCacheThuocKeNgoai }
        );

        setState({ splitCacheCustomize });
      }
      fetchData();
    }, []);

    const dangKyHotKey = () => {
      onAddLayer({ layerId: layerId });
      onRegisterHotkey({
        layerId: layerId,
        hotKeys: [
          {
            keyCode: HOTKEY.F2, //F2
            onEvent: (e) => {
              setState({ keyword: "" });
              const scrollContainer = document.querySelector(
                ".modal-chi-dinh-thuoc .content-right_table .ant-table-body"
              );

              scrollContainer.scrollTo({
                left: 0,
                behavior: "instant",
              });
              refInput.current && refInput.current.focus();
            },
          },
          {
            keyCode: HOTKEY.F4, //F4
            onEvent: () => {
              refSubmit.current && refSubmit.current();
            },
          },
        ],
      });
    };

    useImperativeHandle(ref, () => ({
      show: (option = {}, onOk) => {
        setState({
          show: true,
          loaiDonThuoc: option.loaiDonThuoc,
          khoId: option.khoId,
          dataSource: option.dataSource || {},
          dataKho: option.dataKho || [],
          disableChiDinh: option.disableChiDinh,
          dungKemId: option.dungKemId,
          loaiChiDinh: option.loaiChiDinh,
          keyword: "",
          isExam: option.isExam,
          toDieuTriId: option.toDieuTriId,
          dataToDieuTri: option.dataToDieuTri || [],
          phanNhomDvKhoId: null,
          tenHoatChat: option.tenHoatChat,
          tuNgay: option.tuNgay,
          denNgay: option.denNgay,
          notShowModal: option.notShowModal || false,
          isTuTruc: option.isTuTruc || false,
          isThuocNoiVien: option.isThuocNoiVien || false,
          isDisabledThemDungKem: option.isDisabledThemDungKem || false,
        });
        refIsSubmit.current = false;
        onSelectLoaiDonThuoc(option.khoId)(option.loaiDonThuoc);
        if (isNoiTru && CANH_BAO_TRUNG_CHI_DINH_TRONG_NGAY?.eval()) {
          const params = {
            active: true,
            nbThongTinId: configData.nbThongTinId,
            dsLoaiDichVu: [LOAI_DICH_VU.THUOC],
            tuThoiGianThucHien: moment()
              .startOf("day")
              .format("YYYY-MM-DD HH:mm:ss"),
            denThoiGianThucHien: moment()
              .endOf("day")
              .format("YYYY-MM-DD HH:mm:ss"),
            page: 0,
            size: 200,
          };
          getDsDichVuTiepDon(params);
        }

        if (!option.notShowModal) {
          dangKyHotKey();
        }
        getListAllNhomDichVuKho({
          page: "",
          size: "",
          active: true,
          loaiDichVu: LOAI_DICH_VU.THUOC,
        });
        refCallback.current = onOk;
      },
      onSubmit,
      onFocusInside: () => {
        dangKyHotKey();

        setState({ activeHotKey: true });
      },
      onFocusInput: () => {
        //focus vào ô tìm kiếm
        setTimeout(() => {
          refInput.current && refInput.current.focus();
        }, 500);
      },
      onOutsideClick: () => {
        onRemoveLayer({ layerId: layerId });

        setState({ activeHotKey: false });
      },
    }));

    useEffect(() => {
      if (!state.show) {
        onRemoveLayer({ layerId: layerId });
        action.onReset(); // reset mims data
        refModal.current && refModal.current.hide({});
      } else {
        refModal.current && refModal.current.show({});

        if (!state.notShowModal) {
          //focus vào ô tìm kiếm
          setTimeout(() => {
            refInput.current && refInput.current.focus();
          }, 500);
        }
      }
    }, [state.show]);

    useEffect(() => {
      return () => {
        onRemoveLayer({ layerId: layerId });
      };
    }, []);

    useEffect(() => {
      if (!!dataNb?.cdSoBo || dataNb?.dsCdChinhId.length > 0) {
        searchDv({});
      } else {
        updateData({
          listDvKho: [],
        });
      }
    }, [state.disableChiDinh, dataNb]);

    const { thanhTien, loaiDonThuoc } = state;

    const onTamTinhTien = debounce(
      (listSelected, notCallApiTinhTien = false, onError = () => {}) => {
        if (notCallApiTinhTien) {
          refListSelectedDv.current = listSelected;
          return;
        }
        const payload = listSelected.map((item) => ({
          nbDotDieuTriId: configData.nbDotDieuTriId,
          nbDichVu: {
            dichVuId:
              state.loaiDonThuoc == LOAI_DON_THUOC.KE_NGOAI
                ? item?.id
                : item?.dichVuId,
            soLuong: item.soLuong || 0,
            khoaChiDinhId: configData.khoaChiDinhId,
            chiDinhTuDichVuId: configData.chiDinhTuDichVuId,
            chiDinhTuLoaiDichVu: configData.chiDinhTuLoaiDichVu,
            tuTra: item?.tuTra,
            khongTinhTien: item?.khongTinhTien,
            nguonKhacId: item?.nguonKhacId,
            loaiDichVu: item.loaiDichVu, //truyền thêm loại dịch vụ để tính tiền vắc xin / thuốc
          },
          nbDvKho: {
            khoId: state.khoId,
          },
          lyDoChiDinh: item?.lyDoChiDinh,
        }));

        tamTinhTien(payload).then((s) => {
          //"code": 8324, "message": "Hồ sơ đã quyết toán Bảo hiểm Y tế"
          //=> case này báo lỗi và reset lại ko cho thêm dịch vụ
          if (s?.code == 8324) {
            onError();
            message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            return;
          }

          let thanhTien = 0;
          let tongTien = 0;
          thanhTien = (s || []).reduce((accumulator, currentValue) => {
            const { tienNbCungChiTra, tienNbPhuThu, tienNbTuTra } =
              currentValue.nbDichVu || {};
            if (thongTinNguoiBenh?.doiTuong === DOI_TUONG.BAO_HIEM) {
              return (
                accumulator +
                (tienNbCungChiTra || 0) +
                (tienNbPhuThu || 0) +
                (tienNbTuTra || 0)
              );
            } else {
              return accumulator + currentValue.nbDichVu.thanhTien;
            }
          }, 0);

          //tự tính thành tiền với loại thuốc nhà thuốc
          if (state.loaiDonThuoc == LOAI_DON_THUOC.NHA_THUOC) {
            thanhTien = (listSelected || []).reduce(
              (accumulator, currentValue) => {
                const { giaKhongBaoHiem, soLuongSoCap } = currentValue || {};
                return (
                  accumulator + (giaKhongBaoHiem || 0) * (soLuongSoCap || 0)
                );
              },
              0
            );
          }
          if (
            state.loaiDonThuoc == LOAI_DON_THUOC.THUOC_KHO &&
            thongTinNguoiBenh?.doiTuong === DOI_TUONG.BAO_HIEM &&
            thongTinNguoiBenh.mucHuongTheBhyt > 0
          ) {
            tongTien = (s || []).reduce((accumulator, currentValue) => {
              const {
                tienBhThanhToan,
                tienNbCungChiTra,
                tienNbPhuThu,
                tienNbTuTra,
                tienNguonKhac,
              } = currentValue.nbDichVu || {};
              if (thongTinNguoiBenh?.doiTuong === DOI_TUONG.BAO_HIEM) {
                return (
                  accumulator +
                  (tienBhThanhToan || 0) +
                  (tienNbCungChiTra || 0) +
                  (tienNbPhuThu || 0) +
                  (tienNbTuTra || 0) +
                  (tienNguonKhac || 0)
                );
              } else {
                return accumulator + currentValue.nbDichVu.tongTien;
              }
            }, 0);
          }

          refListSelectedDv.current = listSelected;
          setState({
            show: true,
            thanhTien: thanhTien,
            tongTien: tongTien,
          });
          let msgWarning = (s || []).reduce(
            (a, c) =>
              c.message && !state.msgWarned.includes(c.message)
                ? [...a, c.message]
                : a,
            []
          );
          if (!msgWarning.length) return;
          setState({ msgWarned: [...state.msgWarned, ...msgWarning] });
          showConfirm({
            title: t("common.canhBao"),
            content: msgWarning.join(", "),
            cancelText: t("common.dong"),
            okText: t("common.xacNhan"),
            typeModal: "warning",
          });
        });
      },
      500
    );
    const onSetData = (listSelected) => {
      refListSelectedDv.current = listSelected;
    };
    const onSelectedNoPayment = (data) => {
      refListSelectedDv.current = data;
      setState({
        show: true,
        thanhTien: thanhTien,
      });
    };

    const onSelectedBoChiDinh = (itemSelected) => {
      let item = {};
      let obj = {
        loaiDichVu: LOAI_DICH_VU.THUOC,
        notCallBoChiDinh: true,
      };

      if (itemSelected.id !== state.boChiDinhSelected?.id) {
        //nếu item không giống thì sẽ thêm vào
        item = itemSelected;
      }
      if (!!item.id) {
        obj.boChiDinhId = item.id;
      }
      if (state.loaiDonThuoc == 150) {
        // kê thuốc ngoài
        delete obj.loaiDichVu;
      } else if (loaiDonThuoc == 20 || loaiDonThuoc == 30) {
        delete obj.loaiDichVu;
        delete obj.notCallBoChiDinh;
        getListDichVuTonKho({
          ...obj,
          khoId: state.khoId,
          page: "0",
          size: "10",
        });
      } else {
        searchDv(obj);
      }
    };

    const onSelectLoaiDonThuoc = (khoId) => (value) => {
      refListSelectedDv.current = [];
      setState({
        loaiDonThuoc: value,
        khoId: khoId,
      });
      if (value === 150) {
        // thuốc kê ngoài
        getBoChiDinh({
          thuocChiDinhNgoai: true,
          dsBacSiChiDinhId: nhanVienId,
          page: "",
          size: "",
        });
      } else {
        getBoChiDinh({
          dsLoaiDichVu: LOAI_DICH_VU.THUOC,
          dsBacSiChiDinhId: nhanVienId,
          page: "",
          size: "",
        });
      }
    };
    const onSelectKho = (value) => {
      const khoTuTruc = state?.dataKho?.find(
        (x) => x.id === value && x?.dsCoCheDuyetPhat?.includes(20)
      );
      if (
        !khoTuTruc && //khi chưa có hướng điều trị không cho kê thuốc kho
        loaiDonThuoc === LOAI_DON_THUOC.THUOC_KHO &&
        !thongTinChiTiet?.nbKetLuan?.huongDieuTri &&
        isKhamBenh &&
        !KE_THUOC_KHO_KHI_CHUA_CO_HUONG_DIEU_TRI?.eval()
      ) {
        message.error(t("khamBenh.chonHuongDieuTriTruocKhiChiDinhThuocKho"));
        return;
      }
      setState({ khoId: value });
    };
    const onSelectToDieuTri = (value) => {
      setState({
        toDieuTriId: value,
      });
    };

    const isKeThuocRaVienTheoTungNgay = useMemo(() => {
      return (
        modeThuoc == "DonThuocRaVien" &&
        loaiDonThuoc === LOAI_DON_THUOC.THUOC_KHO &&
        isTaoPhieuLinhTraThuocRaVien
      ); // đơn thuốc ra viện + TAO_PHIEU_LINH_TRA_THUOC_RA_VIEN = True
    }, [isTaoPhieuLinhTraThuocRaVien, modeThuoc, loaiDonThuoc]);

    const showTuNgayDenNgay = useMemo(() => {
      return (
        dataHIEN_THI_O_KE_THUOC_RA_VIEN?.eval() &&
        (modeThuoc == "DonThuocRaVien" || isKhamBenh) &&
        loaiDonThuoc === LOAI_DON_THUOC.THUOC_KHO
      );
    }, [dataHIEN_THI_O_KE_THUOC_RA_VIEN, modeThuoc, loaiDonThuoc]);

    const onShowGroup = (data) => {
      let grouped = groupThuoc(t, data, listLoaiThuoc);
      let activeKey = Object.keys(grouped || {}).map((key) => grouped[key].key);
      updateData({ collapseDichVu: [...collapseDichVu, ...activeKey] });
    };

    const columnsCanhBaoThuoc = [
      Column({
        title: t("common.stt"),
        width: 50,
        dataIndex: "index",
        key: "index",
        align: "center",
        render: (item, _, index) => index + 1,
      }),
      Column({
        title: t("danhMuc.ma"),
        dataIndex: "ma",
        key: "ma",
        width: 100,
        render: (item, record) => {
          return record?.nbDichVu?.dichVu?.ma;
        },
      }),
      Column({
        title: t("danhMuc.tenThuoc"),
        dataIndex: "ten",
        key: "ten",
        width: 220,
        render: (item, record) => {
          return record?.nbDichVu?.dichVu?.ten;
        },
      }),
      Column({
        title: t("danhMuc.noiDungCanhBaoSuDungThuoc"),
        dataIndex: "nbDichVu",
        key: "nbDichVu",
        width: 350,
        render: (item, record) => {
          return (
            <div>
              <label>
                {record?.dsCanhBao?.length &&
                  `- ${record?.dsCanhBao?.join(", ")}`}
              </label>
              <label>
                {item?.dichVu?.canhBao && `- ${item?.dichVu?.canhBao}`}{" "}
              </label>
            </div>
          );
        },
      }),
    ];
    const onShowCanhBaoThuoc = (data) => {
      if (data.length) {
        let _dataSource = data;
        if (modeThuoc == "DonThuocRaVien") {
          //nếu là đơn thuốc ra viện thì lọc theo dichVuId => tránh báo lỗi trùng
          _dataSource = data.filter(
            (item, index, self) =>
              index ===
              self.findIndex(
                (obj) => obj?.nbDichVu?.dichVuId === item?.nbDichVu?.dichVuId
              )
          );
        }

        showConfirm({
          title: t("common.canhBao"),
          content: (
            <div style={{ padding: "8px" }}>
              <TableWrapper
                dataSource={_dataSource}
                columns={columnsCanhBaoThuoc}
              />
            </div>
          ),
          cancelText: t("common.dong"),
          okText: t("common.xacNhan"),
          typeModal: "warning",
          isContentElement: true,
          width: 800,
        });
      }
    };

    const onSubmit = debounce(async ({ isKetThucKham } = {}) => {
      if (refIsSubmit.current) return;

      const isDaCoChungChi = await kiemTraChungChi();
      if (!isDaCoChungChi) return;

      const listSelectedDv = refListSelectedDv.current;

      if (
        !checkChoPhepKeThuocKho &&
        !checkRole([ROLES["KHAM_BENH"].CHI_DINH_THUOC_KHI_NB_DA_KET_LUAN])
      ) {
        message.error(t("khamBenh.donThuoc.huyKetLuanDeChiDinhThemThuoc"));
        return;
      }

      if (!listSelectedDv.length) {
        if (!state.notShowModal) {
          message.error(t("khamBenh.chiDinh.yeuCauNhapChiDinhDichVu"));
        }
        return;
      }
      let checkSoLuong = listSelectedDv.some(
        (item) => !item.soLuong || item.soLuong == 0
      );
      if (checkSoLuong) {
        message.error(t("khamBenh.donThuoc.truongBatBuocDienSoLuong"));
        return;
      }
      let checkSoGiotMl = listSelectedDv.some(
        (item) => item.donViTocDoTruyen == 10 && !item.soGiot
      );
      if (checkSoGiotMl) {
        message.error(t("khamBenh.donThuoc.truongBatBuocDienSoGiotMl"));
        return;
      }

      let listBatBuocNhapSl =
        BAT_BUOC_NHAP_SL_SANG_CHIEU_TOI_PL_THUOC_INSULIN?.split(",").map((i) =>
          i.trim()
        ) || null;

      let dataBatBuoc,
        isBatBuocNhapInsulin =
          state.loaiDonThuoc == LOAI_DON_THUOC.THUOC_KHO &&
          BAT_BUOC_NHAP_SL_SANG_CHIEU_TOI_PL_THUOC_INSULIN &&
          dataPHAN_LOAI_THUOC_INSULIN;

      if (isArray(listBatBuocNhapSl, 2)) {
        isBatBuocNhapInsulin =
          isBatBuocNhapInsulin &&
          listBatBuocNhapSl.every((i) => [1, 3].includes(+i)) &&
          (isKhamBenh || isNoiTruToDieuTri);
      } else if (isArray(listBatBuocNhapSl, 1)) {
        dataBatBuoc = listBatBuocNhapSl[0];
        isBatBuocNhapInsulin =
          isBatBuocNhapInsulin &&
          ((isKhamBenh && dataBatBuoc == 1) ||
            (isNoiTruToDieuTri && dataBatBuoc == 3));
      }

      if (isBatBuocNhapInsulin) {
        let listMaThuocInsuLin = dataPHAN_LOAI_THUOC_INSULIN
          .split(",")
          .map((i) => i.trim());
        let dataInvalid = listSelectedDv
          .filter((i) => listMaThuocInsuLin.includes(i.maPhanLoaiDvKho))
          .find(
            (item) =>
              !item.slSang && !item.slChieu && !item.slToi && !item.slDem
          );
        if (dataInvalid) {
          message.error(
            t("khamBenh.donThuoc.thuocThuocPhanLoaiInsulin", {
              tenThuoc: dataInvalid.ten,
            })
          );
          return;
        }
      }

      let checkLieuDungCachDung = listSelectedDv.some(
        (item) =>
          !item.lieuDungId && !item.cachDung && isValidateLieuDungCachDung
      );

      if (
        configData.chiDinhTuLoaiDichVu !== LOAI_DICH_VU.PHAU_THUAT_THU_THUAT &&
        checkLieuDungCachDung
      ) {
        message.error(
          t("khamBenh.donThuoc.vuiLongKhongDeTrongTruongLieuDungHoacCachDung")
        );
        return;
      }
      if (configData.isTuVanThuoc) {
        if (!configData.chiDinhTuDichVuId) {
          message.error(t("quanLyNoiTru.toDieuTri.vuiLongChonToTieuTri"));
          return;
        }
      }
      if (isKeThuocRaVienTheoTungNgay || showTuNgayDenNgay) {
        if (listSelectedDv.some((item) => !item.tuNgay || !item.denNgay)) {
          message.error(
            t("quanLyNoiTru.vuiLongNhapTuNgayDenNgayKhiKeThuocRaVien")
          );
          return;
        }
      }

      let payloadKeTuNgayDenNgay = [];
      let listIdsThuocDuocPhaChung = [];
      if (state.loaiDonThuoc == LOAI_DON_THUOC.THUOC_KHO) {
        listIdsThuocDuocPhaChung = flatten(
          listSelectedDv.map((x1) =>
            (x1.dsPhaChung || []).map((x2) => x2.dichVuId)
          )
        );
      }

      const filteredDv = listSelectedDv.filter(
        (item) =>
          !(
            state.loaiDonThuoc == LOAI_DON_THUOC.THUOC_KHO &&
            listIdsThuocDuocPhaChung.includes(item.dichVuId)
          )
      );

      const isShowModalChiTietHoiChan =
        isHienThiHoiChanKhiKeThuocDauSao &&
        filteredDv.some((item) => item?.thuocDauSao);

      let payload = filteredDv.map((item) => {
        let commonPayload = {
          _dichVuId:
            state.loaiDonThuoc == LOAI_DON_THUOC.KE_NGOAI
              ? item?.id
              : item?.dichVuId,
          nbDotDieuTriId: configData.nbDotDieuTriId,
          chiDinhTuDichVuId: configData.chiDinhTuDichVuId,
          chiDinhTuLoaiDichVu: configData.chiDinhTuLoaiDichVu,
          soNgay: isNaN(+item?.soNgay) ? null : item?.soNgay,
          soLan1Ngay: item.soLan1Ngay,
          soLuong1Lan: evalString(item.soLuong1Lan),
          lieuDungId: item.lieuDungId,
          duongDungId: item.duongDungId,
          nguonKhacId: item.nguonKhacId,
          lyDoChiDinh: item?.lyDoChiDinh,
          cachDung: item?.cachDung,
          ngayThucHienTu: state?.tuNgay,
          ngayThucHienDen: state?.denNgay,
          slSang: evalString(item.slSang),
          slChieu: evalString(item.slChieu),
          slToi: evalString(item.slToi),
          slDem: evalString(item.slDem),
          tocDoTruyen: item.tocDoTruyen,
          donViTocDoTruyen: item.donViTocDoTruyen,
          soGiot: item.soGiot,
          cachGio: item.cachGio,
          ...(showTuNgayDenNgay && {
            ngayThucHienTu:
              item.tuNgay && !isMoment(item.tuNgay)
                ? parseDate(item.tuNgay)
                : item.tuNgay,
            ngayThucHienDen:
              item.denNgay && !isMoment(item.denNgay)
                ? parseDate(item.denNgay)
                : item.denNgay,
          }),
        };
        const _loaiChiDinh = item.dotXuat
          ? LOAI_CHI_DINH.DOT_XUAT
          : item.boSung
          ? LOAI_CHI_DINH.BO_SUNG
          : LOAI_CHI_DINH.THUONG;
        switch (state.loaiDonThuoc) {
          case LOAI_DON_THUOC.DA_KE:
            return {
              ...commonPayload,
              nbDichVuId: item.id,
              thoiGianThucHien: configData.thoiGianThucHien,
              soLuong: item.soLuong,
              khoaChiDinhId: configData.khoaChiDinhId,
              bacSiChiDinhId: nhanVienId,
              chiDinhTuDichVuId: configData.chiDinhTuDichVuId,
              chiDinhTuLoaiDichVu: configData.chiDinhTuLoaiDichVu,
              dsDungKem: item.children?.map((x) => {
                return {
                  nbDotDieuTriId: configData.nbDotDieuTriId,
                  chiDinhTuDichVuId: configData.chiDinhTuDichVuId,
                  chiDinhTuLoaiDichVu: configData.chiDinhTuLoaiDichVu,
                  soNgay: x?.soNgay,
                  soLan1Ngay: x.soLan1Ngay,
                  soLuong1Lan: evalString(x.soLuong1Lan),
                  lieuDungId: x.lieuDungId,
                  duongDungId: x.duongDungId,
                  cachDung: x?.cachDung,
                  nbDichVuId: x.id,
                  thoiGianThucHien: configData.thoiGianThucHien,
                  thoiGianChiDinh: x.thoiGianChiDinh,
                  soLuong: x.soLuong,
                  khoaChiDinhId: configData.khoaChiDinhId,
                  bacSiChiDinhId: nhanVienId,
                  chiDinhTuDichVuId: configData.chiDinhTuDichVuId,
                  chiDinhTuLoaiDichVu: configData.chiDinhTuLoaiDichVu,
                };
              }),
            };
          case LOAI_DON_THUOC.KE_NGOAI:
            return {
              ...commonPayload,
              thuocChiDinhNgoaiId: item?.id,
              soLuong: item.soLuong,
              ghiChu: item?.ghiChu,
              dungKemId: state.dungKemId,
              soLuongHuy: evalString(item?.soLuongHuy),
              lyDoHuy: item?.lyDoHuy,
              dungChoCon: item.dungChoCon,
              ...(configData.isTuVanThuoc
                ? {
                    tuVanThuocChiTietId: configData.tuVanThuocChiTietId,
                    tuVanThuocId: configData.tuVanThuocId,
                  }
                : {}),
            };
          case LOAI_DON_THUOC.NHA_THUOC:
            return {
              ...commonPayload,
              dichVuId: item?.dichVuId,
              soLuong: item.soLuong,
              ghiChu: item?.ghiChu,
              dungKemId: state.dungKemId,
              soLuongHuy: evalString(item?.soLuongHuy || 0),
              lyDoHuy: item?.lyDoHuy,
              khoaChiDinhId: configData.khoaChiDinhId,
              tachDon: state.tachDon,
              loaiDonThuoc: item.loaiDonThuoc,
              dungChoCon: item.dungChoCon,
              ...(configData.isTuVanThuoc
                ? {
                    tuVanThuocChiTietId: configData.tuVanThuocChiTietId,
                    tuVanThuocId: configData.tuVanThuocId,
                  }
                : {}),
            };
          default: // thuốc kho
            let _loaiNhapXuat =
              modeThuoc == "DonThuocRaVien"
                ? 105
                : state.isThuocNoiVien
                ? 101
                : undefined;
            const _payloadItem = {
              ...commonPayload,
              nbDichVu: {
                dichVuId:
                  state.loaiDonThuoc == LOAI_DON_THUOC.KE_NGOAI
                    ? item?.id
                    : item?.dichVuId,
                soLuong: item.soLuong,
                chiDinhTuDichVuId: configData.chiDinhTuDichVuId,
                chiDinhTuLoaiDichVu: configData.chiDinhTuLoaiDichVu,
                khoaChiDinhId: configData.khoaChiDinhId,
                loaiDichVu: item?.loaiDichVu,
                tuTra: item?.tuTra,
                khongTinhTien: item?.khongTinhTien,
                ghiChu: item?.ghiChu,
                nguonKhacId: item.nguonKhacId,
              },
              lyDoChiDinh: item?.lyDoChiDinh,
              dungChoCon: item.dungChoCon,
              nbDvKho: {
                khoId: item.khoId,
                dungKemId: state.dungKemId,
                loaiNhapXuat: _loaiNhapXuat,
                soLuongHuy: evalString(item?.soLuongHuy),
                lyDoHuy: item?.lyDoHuy,
                loaiChiDinh: _loaiChiDinh,
                dsPhaChungId: item.dsPhaChungId,
                dsPhaChung: item.dsPhaChung?.map((x) => {
                  const _phaChungItem = listSelectedDv.find(
                    (x2) => x2.dichVuId == x.dichVuId
                  );

                  return {
                    nbDotDieuTriId: configData.nbDotDieuTriId,
                    lieuDungId: _phaChungItem.lieuDungId,
                    duongDungId: _phaChungItem.duongDungId,
                    nbDichVu: {
                      dichVuId: _phaChungItem?.dichVuId,
                      soLuong: _phaChungItem.soLuong,
                      chiDinhTuDichVuId: configData.chiDinhTuDichVuId,
                      chiDinhTuLoaiDichVu: configData.chiDinhTuLoaiDichVu,
                      khoaChiDinhId: configData.khoaChiDinhId,
                      loaiDichVu: _phaChungItem?.loaiDichVu,
                      tuTra: _phaChungItem?.tuTra,
                      khongTinhTien: _phaChungItem?.khongTinhTien,
                      ghiChu: _phaChungItem?.ghiChu,
                      nguonKhacId: _phaChungItem.nguonKhacId,
                    },
                    lyDoChiDinh: _phaChungItem?.lyDoChiDinh,
                    nbDvKho: {
                      khoId: state.khoId,
                      loaiNhapXuat: _loaiNhapXuat,
                      soLuongHuy: evalString(_phaChungItem?.soLuongHuy),
                      lyDoHuy: _phaChungItem?.lyDoHuy,
                      loaiChiDinh: _loaiChiDinh,
                    },
                    dsMucDich: _phaChungItem?.dsMucDich,
                    cachDung: _phaChungItem?.cachDung,
                    soNgay: _phaChungItem?.soNgay,
                    soLan1Ngay: _phaChungItem.soLan1Ngay,
                    soLuong1Lan: evalString(x.soLuong1Lan),
                    thoiDiem: _phaChungItem?.thoiDiem,
                    loaiDonThuoc: _phaChungItem.loaiDonThuoc,
                    slSang: evalString(_phaChungItem?.slSang),
                    slChieu: evalString(_phaChungItem?.slChieu),
                    slToi: evalString(_phaChungItem?.slToi),
                    slDem: evalString(_phaChungItem?.slDem),
                  };
                }),
                dsDungKem: item.children?.map((x) => {
                  return {
                    nbDotDieuTriId: configData.nbDotDieuTriId,
                    lieuDungId: x.lieuDungId,
                    duongDungId: x.duongDungId,
                    nbDichVu: {
                      dichVuId:
                        state.loaiDonThuoc == LOAI_DON_THUOC.KE_NGOAI
                          ? x?.id
                          : x?.dichVuId,
                      soLuong: x.soLuong,
                      chiDinhTuDichVuId: configData.chiDinhTuDichVuId,
                      chiDinhTuLoaiDichVu: configData.chiDinhTuLoaiDichVu,
                      khoaChiDinhId: configData.khoaChiDinhId,
                      loaiDichVu: x?.loaiDichVu,
                      tuTra: x?.tuTra,
                      khongTinhTien: x?.khongTinhTien,
                      ghiChu: x?.ghiChu,
                      nguonKhacId: x.nguonKhacId,
                    },
                    lyDoChiDinh: x?.lyDoChiDinh,
                    nbDvKho: {
                      khoId: state.khoId,
                      dungKemId: state.dungKemId,
                      loaiNhapXuat: _loaiNhapXuat,
                      soLuongHuy: evalString(x?.soLuongHuy),
                      lyDoHuy: x?.lyDoHuy,
                      loaiChiDinh: _loaiChiDinh,
                      ...(isHienThiCheckboxTuTruc
                        ? {
                            tuTruc: state.isTuTruc,
                            phongId: configData.phongThucHienId,
                            canLamSang: configData.canLamSang,
                          }
                        : {}),
                    },
                    dsMucDich: x?.dsMucDich,
                    cachDung: x?.cachDung,
                    soNgay: x?.soNgay,
                    soLan1Ngay: x.soLan1Ngay,
                    soLuong1Lan: evalString(x.soLuong1Lan),
                    thoiDiem: x?.thoiDiem,
                    tachDon: state.tachDon,
                    loaiDonThuoc: x.loaiDonThuoc,
                    slSang: evalString(x?.slSang),
                    slChieu: evalString(x?.slChieu),
                    slToi: evalString(x?.slToi),
                    slDem: evalString(x?.slDem),
                  };
                }),
                ...(configData.isTuVanThuoc
                  ? {
                      tuVanThuocChiTietId: configData.tuVanThuocChiTietId,
                      tuVanThuocId: configData.tuVanThuocId,
                    }
                  : {}),
                ...(isHienThiCheckboxTuTruc
                  ? {
                      tuTruc: state.isTuTruc,
                      phongId: configData.phongThucHienId,
                      canLamSang: configData.canLamSang,
                    }
                  : {}),
              },
              dsMucDich: item?.dsMucDich,
              thoiDiem: item?.thoiDiem,
              tachDon: state.tachDon,
              loaiDonThuoc: item.loaiDonThuoc,
              thoiGianBatDau:
                item.thoiGianBatDau && configData.thoiGianThucHien
                  ? moment(configData.thoiGianThucHien).format("YYYY-MM-DD ") +
                    item.thoiGianBatDau
                  : null,
            };
            if (isKeThuocRaVienTheoTungNgay) {
              let _tuNgay = moment(item.tuNgay);
              let _denNgay = moment(item.denNgay);
              //nếu ko có số ngày thì lấy đến ngày - từ ngày + 1
              const _soNgay = _payloadItem.soNgay
                ? _payloadItem.soNgay
                : _denNgay.diff(_tuNgay, "days") + 1;

              const _gioThucHien = moment().format("HH:mm:ss");

              let startingMoment = _tuNgay;
              while (startingMoment <= _denNgay) {
                const _ngayThucHien = startingMoment
                  .clone()
                  .format("YYYY-MM-DD");

                payloadKeTuNgayDenNgay.push({
                  ..._payloadItem,
                  nbDichVu: {
                    ..._payloadItem.nbDichVu,
                    thoiGianThucHien: `${_ngayThucHien} ${_gioThucHien}`,
                    soLuong: _payloadItem.nbDichVu.soLuong / _soNgay, //lấy số lượng theo ngày
                  },
                });
                startingMoment.add(1, "days");
              }
            }
            return _payloadItem;
        }
      });

      const continueFunc = async (prescriptionReasons = {}) => {
        if (
          isShowModalChiTietHoiChan &&
          refModalChiTietHoiChan.current &&
          isToDieuTriNoiTru
        ) {
          await new Promise((resolve) => {
            setTimeout(() => {
              refModalChiTietHoiChan.current.show(
                {
                  isAdd: true,
                  nbDotDieuTriId: configData.nbDotDieuTriId,
                  loaiHoiChan: 40, // thuoc
                },
                () => {
                  resolve();
                },
                () => {
                  resolve();
                }
              );
            }, 100);
          });
          refModalChiTietHoiChan.current.hide();
        }

        refIsSubmit.current = true;
        const isKsk =
          configData.thongTinNguoiBenh?.khamSucKhoe ||
          configData.thongTinNguoiBenh?.loaiDoiTuongKsk;

        const mapPrescriptionReasonsToPayload = (
          payload,
          prescriptionReasons
        ) => {
          if (
            !prescriptionReasons ||
            Object.keys(prescriptionReasons).length === 0
          ) {
            return payload;
          }

          const dichVuIdToInteractions = new Map();

          Object.entries(prescriptionReasons).forEach(([groupKey, reason]) => {
            if (groupKey.startsWith("pair_")) {
              const [_, id1, id2] = groupKey.split("_");

              [id1, id2].forEach((drugId) => {
                if (!dichVuIdToInteractions.has(drugId)) {
                  dichVuIdToInteractions.set(drugId, []);
                }

                const otherDrugId = drugId === id1 ? id2 : id1;
                dichVuIdToInteractions.get(drugId).push({
                  tuongTacThuocId: otherDrugId,
                  ghiChu: reason,
                });
              });
            } else if (groupKey.startsWith("single_")) {
              // Cho single drug
              const [_, id] = groupKey.split("_");
              if (!dichVuIdToInteractions.has(id)) {
                dichVuIdToInteractions.set(id, []);
              }
              dichVuIdToInteractions.get(id).push({
                tuongTacThuocId: id,
                ghiChu: reason,
              });
            } else if (
              groupKey.startsWith("duplicateTherapy_") ||
              groupKey.startsWith("duplicateIngredient_")
            ) {
              // trùng lặp liệu pháp thành phần
              const drugIds = groupKey.split("_").slice(1);

              const [id1, id2] = drugIds;
              const hasSelfInteraction = id1 === id2;

              if (hasSelfInteraction) {
                if (!dichVuIdToInteractions.has(id1)) {
                  dichVuIdToInteractions.set(id1, []);
                }

                dichVuIdToInteractions.get(id1).push({
                  tuongTacThuocId: id1,
                  ghiChu: reason,
                });
              } else {
                [id1, id2].forEach((id) => {
                  if (!dichVuIdToInteractions.has(id)) {
                    dichVuIdToInteractions.set(id, []);
                  }
                  dichVuIdToInteractions.get(id).push({
                    tuongTacThuocId: id === id1 ? id2 : id1,
                    ghiChu: reason,
                  });
                });
              }
            }
          });

          return payload.map((item) => {
            const dichVuId = item._dichVuId || item.nbDichVu?.dichVuId;

            if (dichVuId && dichVuIdToInteractions.has(dichVuId.toString())) {
              const interactions = dichVuIdToInteractions.get(
                dichVuId.toString()
              );

              //body tương tác thuốc
              const tuongTacThuoc = {
                chiDinhTuDichVuId: configData.chiDinhTuDichVuId,
                thoiGianThucHien: configData.thoiGianThucHien,
                chiDinhTuLoaiDichVu: item.chiDinhTuLoaiDichVu || 10,
                dsDichVuId: [dichVuId],
                dsTuongTacThuocId: interactions.map(
                  (interaction) => interaction.tuongTacThuocId
                ),
                dsThuoc: interactions.map((interaction) => ({
                  tuongTacThuocId: interaction.tuongTacThuocId,
                  dsDichVuId: [dichVuId],
                  ghiChu: interaction.ghiChu,
                })),
              };

              return {
                ...item,
                tuongTacNgoaiVien: true,
                tuongTacThuoc,
                _dichVuId: undefined, //clean up
              };
            }

            return item;
          });
        };

        const payloadWithReasons = mapPrescriptionReasonsToPayload(
          payload,
          prescriptionReasons
        );

        try {
          if (state.loaiDonThuoc === LOAI_DON_THUOC.DA_KE) {
            //thuốc đã kê ở tờ điều trị
            const s = await chiDinhDichVuThuocDaKe(payloadWithReasons);
            if (s?.code === 0) {
              getListThuocDaKe({
                nbDotDieuTriId: configData.nbDotDieuTriId,
                chiDinhTuDichVuId: isKsk ? null : configData.chiDinhTuDichVuId,
                chiDinhTuLoaiDichVu: isKsk
                  ? null
                  : configData.chiDinhTuLoaiDichVu,
              })
                .then((res) => {
                  onClosePopup();

                  refIsSubmit.current = false;
                  refListSelectedDv.current = [];
                  setState({
                    show: false,
                  });

                  refCallback.current &&
                    refCallback.current({ listDvThuoc: res.data });
                })
                .catch((err) => {
                  refIsSubmit.current = false;
                });

              if (isArray(s, true)) {
                const data = s.filter(
                  (x) => x.nbDichVu.dichVu.canhBao || x?.dsCanhBao?.length
                );
                onShowCanhBaoThuoc(data);
              }
            }
          } else if (state.loaiDonThuoc === LOAI_DON_THUOC.KE_NGOAI) {
            // thuốc kê ngoài
            const s = await chiDinhDichVuThuocKeNgoai(payloadWithReasons);
            if (s?.code === 0) {
              getListDichVuThuocKeNgoai({
                nbDotDieuTriId: configData.nbDotDieuTriId,
                chiDinhTuDichVuId: isKsk ? null : configData.chiDinhTuDichVuId,
                chiDinhTuLoaiDichVu: isKsk
                  ? null
                  : configData.chiDinhTuLoaiDichVu,
              })
                .then((res) => {
                  onClosePopup();

                  refIsSubmit.current = false;
                  refListSelectedDv.current = [];
                  setState({
                    show: false,
                  });

                  refCallback.current &&
                    refCallback.current({ listDvThuocKeNgoai: res.data });
                })
                .catch((err) => {
                  refIsSubmit.current = false;
                });

              updateData({
                collapseDichVu: [...collapseDichVu, "KEY_THUOC_KE_NGOAI"],
              });
            }
            let newTable = s?.neededUpdateRecord.filter((x) => {
              return x.code === 8501;
            });
            if (newTable.length > 0)
              refModalThongTinThuoc.current &&
                refModalThongTinThuoc.current.show(
                  {
                    newTable,
                    chiDinhTuDichVuId: configData.chiDinhTuDichVuId,
                    nbDotDieuTriId: configData.nbDotDieuTriId,
                    loaiDonThuoc,
                    dungKemId: state.dungKemId,
                    canhBaoThuoc,
                  },
                  () => {
                    if (isKetThucKham) {
                      onKetThucKham({ isKetThucKham: true });
                    }
                  }
                );
          } else if (state.loaiDonThuoc === LOAI_DON_THUOC.NHA_THUOC) {
            // Nếu có kết nối mims thì cho response [] to bypass
            const resTuongTacThuoc = dataKET_NOI_MIMS?.eval()
              ? []
              : await kiemTraTuongTacThuocNhaThuoc(payload);
            onShowMessageTuongTacThuoc(
              resTuongTacThuoc,
              (lyDoChiDinh = null) => {
                const newPayload = lyDoChiDinh
                  ? payload.map((item) => ({
                      ...item,
                      nbDichVu: {
                        ...item.nbDichVu,
                        lyDoChiDinh: lyDoChiDinh[item.nbDichVu?.dichVuId],
                      },
                    }))
                  : payload;

                const newPayloadWithReasons = mapPrescriptionReasonsToPayload(
                  newPayload,
                  prescriptionReasons
                );

                chiDinhThuocNhaThuoc(newPayloadWithReasons)
                  .then((s) => {
                    getListThuocNhaThuoc({
                      nbDotDieuTriId: configData.nbDotDieuTriId,
                      chiDinhTuDichVuId: isKsk
                        ? null
                        : configData.chiDinhTuDichVuId,
                      chiDinhTuLoaiDichVu: isKsk
                        ? null
                        : configData.chiDinhTuLoaiDichVu,
                      dsTrangThaiHoan: [
                        TRANG_THAI_HOAN.THUONG,
                        TRANG_THAI_HOAN.CHO_DUYET_HOAN,
                        TRANG_THAI_HOAN.CHO_DUYET_DOI,
                      ],
                    })
                      .then((res) => {
                        onClosePopup();

                        refIsSubmit.current = false;
                        updateData({
                          collapseDichVu: [
                            ...collapseDichVu,
                            "KEY_THUOC_NHA_THUOC",
                          ],
                        });
                        refListSelectedDv.current = [];
                        setState({
                          show: false,
                        });

                        const newTable = (s || [])
                          .map((item) => ({
                            ...item,
                            dsMucDich: payload.find(
                              (x) =>
                                x?.nbDichVu?.dichVuId ===
                                item?.nbDichVu?.dichVuId
                            )?.dsMucDich,
                            loaiDonThuoc: payload.find(
                              (x) => x?.dichVuId === item?.dichVuId
                            )?.loaiDonThuoc,
                          }))
                          .filter((item1) => {
                            item1.message && message.error(item1.message);
                            return [7624, 8501].includes(item1.code);
                          });

                        if (newTable.length > 0) {
                          refModalThongTinThuoc.current &&
                            refModalThongTinThuoc.current.show(
                              {
                                newTable,
                                nbDotDieuTriId: configData.nbDotDieuTriId,
                                chiDinhTuDichVuId: configData.chiDinhTuDichVuId,
                                loaiDonThuoc,
                                dungKemId: state.dungKemId,
                                thuocNhaThuoc: true,
                                tachDon: state.tachDon,
                                ngayThucHienTu: state?.tuNgay,
                                ngayThucHienDen: state?.denNgay,
                              },
                              (options) => {
                                setState({
                                  activeKey: options.activeKey,
                                  show: false,
                                });
                                refCallback.current &&
                                  refCallback.current({
                                    listDvThuoc: res.data,
                                  });
                                if (isKetThucKham) {
                                  onKetThucKham({ isKetThucKham: true });
                                }
                              }
                            );
                        } else {
                          refCallback.current &&
                            refCallback.current({ listDvThuoc: res.data });
                        }
                      })
                      .catch((err) => {
                        refIsSubmit.current = false;
                      });

                    const data = s.filter(
                      (x) => x.nbDichVu.dichVu.canhBao || x?.dsCanhBao?.length
                    );
                    onShowCanhBaoThuoc(data);
                  })
                  .catch((e) => {
                    refIsSubmit.current = false;
                  });
              },
              () => {
                refIsSubmit.current = false;
              }
            );
          } else {
            if (isKeThuocRaVienTheoTungNgay) {
              payload = payloadKeTuNgayDenNgay;
            }
            // Nếu có kết nối mims thì cho response [] to bypass
            const resTuongTacThuoc = dataKET_NOI_MIMS?.eval()
              ? []
              : await kiemTraTuongTacThuocKho(payload);

            onShowMessageTuongTacThuoc(
              resTuongTacThuoc,
              (lyDoChiDinh = null) => {
                const newPayload = lyDoChiDinh
                  ? payload.map((item) => ({
                      ...item,
                      lyDoChiDinh: lyDoChiDinh[item?.nbDichVu?.dichVuId],
                      nbDichVu: {
                        ...item.nbDichVu,
                      },
                    }))
                  : payload;

                const newPayloadWithReasons = mapPrescriptionReasonsToPayload(
                  newPayload,
                  prescriptionReasons
                );

                chiDinhDvThuocVacxin(newPayloadWithReasons)
                  .then(({ resThuoc, resVacxin }) => {
                    const getListDvParams = {
                      nbDotDieuTriId: configData.nbDotDieuTriId,
                      chiDinhTuDichVuId: isKsk
                        ? null
                        : configData.chiDinhTuDichVuId,
                      chiDinhTuLoaiDichVu: isKsk
                        ? null
                        : configData.chiDinhTuLoaiDichVu,
                      dsTrangThaiHoan: [
                        TRANG_THAI_HOAN.THUONG,
                        TRANG_THAI_HOAN.CHO_DUYET_HOAN,
                        TRANG_THAI_HOAN.CHO_DUYET_DOI,
                      ],
                    };
                    Promise.all([
                      resThuoc.length > 0
                        ? getListDichVuThuoc(getListDvParams)
                        : Promise.resolve({ code: 0, data: [] }),
                      resVacxin.length > 0
                        ? getListDichVuVacxin(getListDvParams)
                        : Promise.resolve({ code: 0, data: [] }),
                    ]).then((res) => {
                      const getListRes = [...res[0]?.data, ...res[1]?.data];
                      onClosePopup();
                      onShowGroup(getListRes);
                      refListSelectedDv.current = [];
                      setState({
                        show: false,
                      });

                      const resWarningTamUngThem = [...resThuoc, ...resVacxin]
                        .map((item) => item.message)
                        .filter(
                          (item) =>
                            !!item &&
                            item.includes(
                              "NB cần đi tạm ứng thêm để thực hiện dịch vụ"
                            )
                        );
                      if (
                        configData.chiDinhTuLoaiDichVu === LOAI_DICH_VU.CDHA &&
                        resWarningTamUngThem.length > 0
                      ) {
                        showConfirm({
                          title: t("common.canhBao"),
                          content: resWarningTamUngThem[0],
                          cancelText: t("common.dong"),
                          showBtnOk: false,
                          typeModal: "warning",
                        });
                      }

                      const newTable = ([...resThuoc, ...resVacxin] || [])
                        .map((item) => ({
                          ...item,
                          dsMucDich: payload.find(
                            (x) =>
                              x?.nbDichVu?.dichVuId === item?.nbDichVu?.dichVuId
                          )?.dsMucDich,
                        }))
                        .filter((item1) => {
                          if (item1.code !== 8504) {
                            item1.message && message.error(item1.message);
                          }
                          return [7624, 8501].includes(item1.code);
                        });
                      if (newTable.length > 0) {
                        refModalThongTinThuoc.current &&
                          refModalThongTinThuoc.current.show(
                            {
                              newTable,
                              nbDotDieuTriId: configData.nbDotDieuTriId,
                              chiDinhTuDichVuId: configData.chiDinhTuDichVuId,
                              loaiDonThuoc,
                              dungKemId: state.dungKemId,
                              tachDon: state.tachDon,
                              ngayThucHienTu: state?.tuNgay,
                              ngayThucHienDen: state?.denNgay,
                            },
                            (options) => {
                              setState({
                                activeKey: options.activeKey,
                                show: false,
                              });
                              refCallback.current &&
                                refCallback.current({ listDvThuoc: res.data });
                              const data = (options?.dataThuoc || []).filter(
                                (x) =>
                                  x.nbDichVu.dichVu.canhBao ||
                                  x?.dsCanhBao?.length
                              );
                              onShowCanhBaoThuoc(data);
                              if (isKetThucKham) {
                                onKetThucKham({ isKetThucKham: true });
                              }
                            }
                          );
                      } else {
                        refCallback.current &&
                          refCallback.current({ listDvThuoc: getListRes });
                      }
                    });
                    const data = resThuoc.filter(
                      (x) => x.nbDichVu.dichVu.canhBao || x?.dsCanhBao?.length
                    );
                    onShowCanhBaoThuoc(data);
                  })
                  .catch((e) => {
                    console.error("e", e);
                    refIsSubmit.current = false;
                  });
              },
              () => {
                refIsSubmit.current = false;
              }
            );
          }
        } catch (error) {
          console.log(error);
          refIsSubmit.current = false;
        } finally {
          //không xử lý bỏ chặn submit ở đây, vì phần xử lý đang chạy bất đồng bộ
          //=> có case chưa đóng popup mà bấm tiếp thì vẫn bị kê thêm 1 lần nữa
          // refIsSubmit.current = false;
        }
      };

      const [error] = await toSafePromise(
        action.kiemTraMims({
          chiDinhTuDichVuId: configData.chiDinhTuDichVuId,
          chiDinhTuLoaiDichVu: configData.chiDinhTuLoaiDichVu,
          nbDotDieuTriId: configData.nbDotDieuTriId,
          dsTacNhanDiUngId: configData.dsTacNhanDiUngId,
          dsDichVuId: filteredDv.map((item) =>
            state.loaiDonThuoc == LOAI_DON_THUOC.KE_NGOAI
              ? item?.id
              : item?.dichVuId
          ),
          thoiGianYLenh:
            configData.chiDinhTuLoaiDichVu === LOAI_DICH_VU.TO_DIEU_TRI
              ? configData.thoiGianYLenh &&
                moment(configData.thoiGianYLenh).format("YYYY-MM-DD HH:mm:ss")
              : null,
        })
      );

      if (error) {
        continueFunc();
      } else {
        action.showMimsPopup();
        action.onContinue = continueFunc;
      }
    }, 1000);

    const onShowMessageTuongTacThuoc = (
      data,
      callbackOkFunc = () => {},
      callbackCancelFunc = () => {}
    ) => {
      let tuongTacThuoc = data?.filter((x) => x.tuongTacThuoc);
      if (tuongTacThuoc?.length) {
        canhBaoThuoc(
          tuongTacThuoc,
          callbackOkFunc,
          callbackCancelFunc,
          data,
          false,
          thongTinNguoiBenh?.tuoi2
        );
      } else {
        callbackOkFunc();
      }
    };

    const onClosePopup = () => {
      refListSelectedDv.current = [];
      setState({
        show: false,
        thanhTien: 0,
        msgWarned: [],
        tachDon: false,
      });
    };

    // cho phép kê thuốc tủ trực khi bệnh nhân chưa ra viện
    const allowChiDinhThuocTuTruc =
      checkRole([
        ROLES["KHAM_BENH"].CHI_DINH_THUOC_TU_TRUC_KHI_NB_NOI_TRU_CHUA_RA_VIEN,
      ]) &&
      configData.thongTinNguoiBenh?.trangThaiNb < 100 &&
      TRANG_THAI_DICH_VU.DA_KET_LUAN === configData.trangThaiKham;

    const dsKho = useMemo(() => {
      if (
        TRANG_THAI_DICH_VU.DA_KET_LUAN === configData.trangThaiKham &&
        loaiDonThuoc === LOAI_DON_THUOC.THUOC_KHO
      ) {
        if (allowChiDinhThuocTuTruc) {
          return (state.dataKho || []).filter(
            (item) =>
              item.dsCoCheDuyetPhat?.includes(
                CO_CHE_DUYET_PHAT.DUYET_PHAT_NGAY_KHI_KE
              ) // chỉ cho chọn kho tủ trực để tránh lỗi khi đổi qua lại
          );
        }
        return [];
      }

      return state.dataKho;
    }, [
      state.dataKho,
      loaiDonThuoc,
      configData.trangThaiKham,
      allowChiDinhThuocTuTruc,
    ]);

    const checkChoPhepKeThuocKho = useMemo(() => {
      const isSelectedKhoTuTruc = dsKho
        ?.find((item) => item.id === state.khoId)
        ?.dsCoCheDuyetPhat?.includes(CO_CHE_DUYET_PHAT.DUYET_PHAT_NGAY_KHI_KE);

      if (allowChiDinhThuocTuTruc && isSelectedKhoTuTruc) return true;

      if (
        TRANG_THAI_DICH_VU.DA_KET_LUAN === configData.trangThaiKham &&
        loaiDonThuoc === LOAI_DON_THUOC.THUOC_KHO
      )
        return false;
      return true;
    }, [loaiDonThuoc, configData, dsKho, state.khoId, allowChiDinhThuocTuTruc]);

    const isShowSelectKho = useMemo(() => {
      return loaiDonThuoc === LOAI_DON_THUOC.THUOC_KHO;
    }, [loaiDonThuoc]);
    const isKeThuocDaKe = loaiDonThuoc === LOAI_DON_THUOC.DA_KE;

    const disabled =
      state.loaiDonThuoc === undefined ||
      state.loaiDonThuoc == LOAI_DON_THUOC.NHA_THUOC ||
      state.loaiDonThuoc == LOAI_DON_THUOC.KE_NGOAI ||
      state.loaiDonThuoc == LOAI_DON_THUOC.DA_KE;

    const onChangeRadio = (e) => {
      setTheoSoLuongTonKho(e.target.value);
    };

    const listLoaiDonThuocEnum = useMemo(() => {
      if (!loadFinish) return [];
      let result = [];
      if (
        configData.chiDinhTuLoaiDichVu === LOAI_DICH_VU.TO_DIEU_TRI &&
        state.notShowModal
      ) {
        result = [
          LOAI_DON_THUOC.THUOC_KHO,
          LOAI_DON_THUOC.NHA_THUOC,
          LOAI_DON_THUOC.KE_NGOAI,
        ];
      }

      if (
        configData.chiDinhTuLoaiDichVu === LOAI_DICH_VU.TO_DIEU_TRI &&
        !configData.isTuVanThuoc &&
        dataHIEN_THI_THUOC_DA_KE?.eval() &&
        modeThuoc !== "DonThuocRaVien"
      ) {
        result = [
          LOAI_DON_THUOC.NHA_THUOC,
          LOAI_DON_THUOC.THUOC_KHO,
          LOAI_DON_THUOC.KE_NGOAI,
          LOAI_DON_THUOC.DA_KE,
        ];
      } else {
        result = [
          LOAI_DON_THUOC.NHA_THUOC,
          LOAI_DON_THUOC.THUOC_KHO,
          LOAI_DON_THUOC.KE_NGOAI,
        ];
      }
      if (anThuocKho) {
        result = result.filter((item) => item !== LOAI_DON_THUOC.THUOC_KHO);
      }
      if (!checkRole([ROLES["KHAM_BENH"].HIEN_THI_THUOC_KE_NGOAI])) {
        result = result?.filter((x) => x !== LOAI_DON_THUOC.KE_NGOAI);
      }
      if (isXetNghiem) {
        result = result.filter((item) => item === LOAI_DON_THUOC.THUOC_KHO);
      }
      return result;
    }, [
      dataHIEN_THI_THUOC_DA_KE,
      configData,
      state.notShowModal,
      anThuocKho,
      loadFinish,
      modeThuoc,
      isXetNghiem,
    ]);

    const popovercontent = (
      <div style={{ display: "grid" }}>
        {t("common.caiDatTimKiemHangHoa")}:
        <Radio.Group
          defaultValue={theoSoLuongTonKho}
          style={{ display: "grid" }}
          onChange={onChangeRadio}
        >
          <Radio value={15}>{t("common.hangHoaConTon")}</Radio>
          <Radio value={-1}>{t("common.tatCaHangHoa")}</Radio>
        </Radio.Group>
      </div>
    );

    const onChangeNhomDvKho = (value) => {
      setState({ phanNhomDvKhoId: value });
    };

    const onChange = (value) => {
      setState({ keyword: value });
    };
    const onResizeSplit = async (key, keyCache, value) => {
      const splitCacheCustomize = Object.assign(state.splitCacheCustomize, {
        [key]: value,
      });
      setState({ splitCacheCustomize });
      await cacheUtils.save(nhanVienId, keyCache, value, false);
    };

    refSubmit.current = onSubmit;

    let tableProps = {
      loaiDonThuoc: state.loaiDonThuoc,
      visible: state.show,
      layerId: layerId,
      activeHotKey: state.activeHotKey,
      keyword: state.keyword,
      boChiDinhSelected: state.boChiDinhSelected,
      thanhTien: thanhTien,
      onResizeSplit: onResizeSplit,
      phanNhomDvKhoId: state.phanNhomDvKhoId,
      notShowModal: state.notShowModal,
      ngayThucHienTu: state?.tuNgay,
      ngayThucHienDen: state?.denNgay,
      isKeThuocRaVienTheoTungNgay,
      rawMimsData,
      getColorTuongTacThuoc,
      showTuNgayDenNgay,
    };

    const onChangeConfig = (data) => {
      updateConfigData({
        configData: {
          ...configData,
          ...data,
        },
      });
    };

    const ketThucKham = async () => {
      if (!nbKetLuan?.ketQuaDieuTri || !nbKetLuan.huongDieuTri) {
        message.error("Vui lòng nhập kết quả điều trị và hướng điều trị");
        return;
      }
      onSubmit({ isKetThucKham: true });
    };

    const ContentChiDinhThuoc = (
      <>
        <Main className="content-chi-dinh-thuoc">
          <GlobalStyle></GlobalStyle>
          <div className="filter-box">
            {state.notShowModal && (
              <>
                <span className="text">{`${t("vatTu.themChiDinh")} ${t(
                  "common.thuoc"
                )}`}</span>
                <div>&nbsp;&nbsp;&nbsp;</div>
              </>
            )}
            <Select
              className="filter-item"
              style={{ width: "160px" }}
              value={state.loaiDonThuoc}
              data={listLoaiDonThuocEnum
                .map((key) =>
                  LIST_LOAI_DON_THUOC.find((item) => item.id === key)
                )
                .filter((item) => !!item)}
              onChange={onSelectLoaiDonThuoc()}
              placeholder={t("khamBenh.donThuoc.vuiLongChonLoaiDonThuoc")}
            />
            {/* kê thuốc kho thì hiển thị chọn kho */}
            {isShowSelectKho && (
              <AntSelect
                className="filter-item"
                style={{ width: "210px", marginRight: "15px" }}
                onClick={() => {
                  if (
                    TRANG_THAI_DICH_VU.DA_KET_LUAN ===
                      configData.trangThaiKham &&
                    loaiDonThuoc === LOAI_DON_THUOC.THUOC_KHO
                  ) {
                    setState({
                      isShowTextError: true,
                    });
                  }
                }}
                placeholder={t("khamBenh.donThuoc.vuiLongChonKho")}
                onChange={onSelectKho}
                value={state.khoId}
                disabled={disabled}
              >
                {dsKho.map((item, index) => (
                  <AntSelect.Option key={index} value={item.id}>
                    {item.ten}
                  </AntSelect.Option>
                ))}
              </AntSelect>
            )}
            {/* kê thuốc đã kê thì hiển thị chọn tờ điều trị */}
            {isKeThuocDaKe && (
              <Select
                className="filter-item"
                data={state.dataToDieuTri}
                style={{ width: "300px", marginRight: "15px" }}
                placeholder={t("quanLyNoiTru.toDieuTri.vuiLongChonToTieuTri")}
                onChange={onSelectToDieuTri}
                value={state.toDieuTriId}
              />
            )}
            <Select
              className="filter-item"
              placeholder={t("khamBenh.donThuoc.phanNhomThuoc")}
              style={{ width: "210px" }}
              data={listAllNhomDichVuKho}
              value={state.phanNhomDvKhoId}
              onChange={onChangeNhomDvKho}
            />
            <InputTimeout
              className="filter-item"
              ref={refInput}
              placeholder={t("khamBenh.donThuoc.nhapTenThuoc")}
              value={state.keyword}
              onChange={onChange}
            />
            <div className="setting">
              <Popover
                trigger={"click"}
                content={popovercontent}
                placement="bottom"
              >
                <SVG.IcSetting className="icon" />
              </Popover>
            </div>
            {dataTACH_DON_THUOC?.eval() && (
              <Checkbox
                checked={state.tachDon}
                onChange={() => setState({ tachDon: !state.tachDon })}
              >
                {t("khamBenh.donThuoc.tachDon")}
              </Checkbox>
            )}
          </div>
          {state.loaiDonThuoc === LOAI_DON_THUOC.DA_KE ? (
            <TableThuocDaKe
              onSelected={onTamTinhTien}
              listAllLieuDung={listAllLieuDung || []}
              toDieuTriId={state.toDieuTriId}
              nbDotDieuTriId={configData.nbDotDieuTriId}
              onSetData={onSetData}
              splitCacheCustomize={
                state.splitCacheCustomize?.widthCacheThuocDaKe
              }
              dsKho={dsKho}
              isTuTruc={state.isTuTruc}
              {...tableProps}
            />
          ) : state.loaiDonThuoc === LOAI_DON_THUOC.KE_NGOAI ? (
            <TableThuocKeNgoai
              listToDieuTri={state.dataToDieuTri || []}
              onChangeConfig={onChangeConfig}
              onSelectedNoPayment={onSelectedNoPayment}
              onSelectedBoChiDinh={onSelectedBoChiDinh}
              splitCacheCustomize={
                state.splitCacheCustomize?.widthCacheThuocKeNgoai
              }
              {...tableProps}
            />
          ) : (
            <TableDonThuoc
              isShowing={state.show}
              onSelected={onTamTinhTien}
              onChangeConfig={onChangeConfig}
              listAllLieuDung={listAllLieuDung || []}
              listToDieuTri={state.dataToDieuTri || []}
              khoId={state.khoId}
              loaiChiDinh={state.loaiChiDinh}
              nbDotDieuTriId={configData.nbDotDieuTriId}
              onSetData={onSetData}
              modeThuoc={modeThuoc}
              isExam={state.isExam}
              dungKemId={state.dungKemId}
              theoSoLuongTonKho={
                theoSoLuongTonKho !== -1 ? theoSoLuongTonKho : null
              }
              splitCacheCustomize={
                state.splitCacheCustomize?.widthCacheDonThuoc
              }
              tenHoatChat={state.tenHoatChat}
              isShowSoLuongDinhMuc={isShowSoLuongDinhMuc}
              tongTien={state?.tongTien}
              dsKho={dsKho}
              isTuTruc={state.isTuTruc}
              isDisabledThemDungKem={state.isDisabledThemDungKem}
              {...tableProps}
            />
          )}
        </Main>
        <ModalThongTinThuoc
          ref={refModalThongTinThuoc}
          thongTinNguoiBenh={configData.thongTinNguoiBenh}
          modeThuoc={modeThuoc}
        />
        {isHienThiHoiChanKhiKeThuocDauSao && (
          <LazyLoad
            component={() =>
              import("pages/hoiChan/DanhSachHoiChan/ModalChiTietHoiChan/index")
            }
            showTimKiemNguoiBenh={false}
            ref={refModalChiTietHoiChan}
            macDinhLoaiHoiChan={40} // thuoc
          />
        )}
      </>
    );

    if (state.notShowModal) {
      return ContentChiDinhThuoc;
    }

    return (
      <ModalTemplate
        ref={refModal}
        width={"98%"}
        layerId={layerId}
        title={t("khamBenh.donThuoc.chiDinhThuoc")}
        rightTitle={nbInfoTitle}
        wrapClassName="modal-chi-dinh-thuoc"
        onCancel={() => {
          setState({
            show: false,
            tachDon: false,
          });
          return true;
        }}
        actionRight={
          <>
            <Button minWidth={100} type="default" onClick={onClosePopup}>
              {t("common.huy")}
            </Button>
            <Button minWidth={100} type="primary" onClick={onSubmit}>
              <span> {t("common.dongY")} [F4]</span>{" "}
            </Button>
            {KET_THUC_KHAM_KHI_CHO_DON_THUOC?.eval() && isKhamBenh && (
              <Button minWidth={100} type="primary" onClick={ketThucKham}>
                <span> {t("khamBenh.luuVaKetThucKham")} </span>{" "}
              </Button>
            )}
          </>
        }
        maskClosable={false}
      >
        {ContentChiDinhThuoc}
        <MimsPopup
          dsTacNhanDiUngId={dsTacNhanDiUngId}
          htmlContent={htmlContent}
          builtInteractionList={builtInteractionList}
          visible={visible}
          error={error}
          loading={loading}
          action={action}
          getDsThuocDangKe={() => refListSelectedDv.current}
          prescriptionReasons={prescriptionReasons}
          onPrescriptionReasonChange={action.setPrescriptionReasons}
          requiredInteractions={requiredInteractions}
        />
      </ModalTemplate>
    );
  }
);

export default ModalChiDinhThuoc;
