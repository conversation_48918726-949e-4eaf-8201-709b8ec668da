import React, { useMemo } from "react";
import { useCallback, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { query } from "redux-store/stores";
import { Modal, Spin, Alert, message, Row, Col, Tabs, Tag } from "antd";
import { get, isEmpty, orderBy, chain, isNil } from "lodash";
import moment from "moment";
import { useLazyKVMap, useQueryAll, useStore, useThietLap } from "hooks";
import { Button, VirtualizedSelect } from "components";
import mimsProvider from "data-access/mims-provider";
import MimsIframeViewer from "./MimsIframeViewer";
import BuiltInteractionList from "./BuiltInteractionList";
import VnInteractionList from "./VnInteractionList";
import PrescriptionReasonInput from "./PrescriptionReasonInput";
import BlockedDrugsList from "./BlockedDrugsList";
import { isArray, isNumber, safeConvertToArray } from "utils";
import { SVG } from "assets";
import { EyeOutlined, EyeInvisibleOutlined } from "@ant-design/icons";
import { THIET_LAP_CHUNG } from "constants/index";
import styled from "styled-components";
import { VN_SEVERITIES, VN_SEVERITY_RANKING } from "./vnConstants";
import { extractSeverityLevels } from "./constants";
import { lazyGetRecordByKey } from "redux-store/selectors";

const Main = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
  .ant-tabs {
    .ant-tabs-nav-wrap {
      padding: 0 16px;
    }
    .ant-tabs-nav {
      margin: 0 !important;
    }
  }
`;

const InteractionListContainer = styled.div`
  padding: 12px 16px;
  height: ${(props) =>
    props.$loading ? "100%" : "clamp(500px, calc(100vh - 300px), 1200px)"};
  overflow: auto;
`;

const buildInteractionList = (mimsData, mucDoCanhBaoMap) => {
  if (!mimsData || !mimsData.enXml) {
    return [];
  }

  const xmlData = mimsData.enXml;
  const interaction = xmlData.Interaction;
  const interactions = [];
  const healthIssues = [];
  const duplicateTherapies = [];
  const duplicateIngredients = [];
  const allergies = [];
  const lactations = [];
  const pregnancies = [];

  // Recursive function to extract interactions from nested structure
  const extractInteractions = (items, parentDrug = null) => {
    if (!items) return;

    const itemsArray = Array.isArray(items) ? items : [items];

    itemsArray.forEach((item) => {
      const currentDrug = {
        name: item.name || "",
        tenDichVu: item.tenDichVu,
        generic: null,
        dichVuId: item.dichVuId,
        maDichVu: item.maDichVu,
      };

      // If this item has a Route with ClassInteraction, it's an interaction

      if (isArray(item.Route, 1)) {
        item.Route.forEach((route) => {
          if (route.ClassInteraction) {
            const classInteraction = route.ClassInteraction;
            const severity = get(classInteraction, "Severity.ranking", 1);

            // mức độ nhỏ hơn thiết lập hiển thị thì skip
            // healthIssue có 2 mức độ 2 và 3 , 3 là cao nhất
            if (severity >= mucDoCanhBaoMap.D2D) {
              const drugA = {
                name: parentDrug?.name || currentDrug.name,
                tenDichVu: parentDrug?.tenDichVu || currentDrug.tenDichVu,
                generic: get(
                  classInteraction,
                  "PrescribingInteractionClass.PrescribingMolecule.name",
                  ""
                ),
                dichVuId: parentDrug?.dichVuId || currentDrug.dichVuId,
                maDichVu: parentDrug?.maDichVu || currentDrug.maDichVu,
                codeType: route.codeType,
              };

              // Get drug B (InteractionClass - affecting drug)
              const drugB = {
                name: currentDrug.name,
                tenDichVu: currentDrug.tenDichVu,
                generic: get(
                  classInteraction,
                  "InteractionClass.Molecule.name",
                  ""
                ),
                dichVuId: currentDrug.dichVuId,
                maDichVu: currentDrug.maDichVu,
                codeType: route.codeType,
              };

              // Get severity

              interactions.push({
                nameA: drugA.name,
                tenDichVuA: drugA.tenDichVu,
                genericA: drugA.generic,
                nameB: drugB.name,
                tenDichVuB: drugB.tenDichVu,
                genericB: drugB.generic,
                severity: severity,
                severityRanking: get(classInteraction, "Severity.ranking", 1),
                fullInteraction: classInteraction,
                drugADetails: drugA,
                drugBDetails: drugB,
                route: route.name,
                codeType: route.codeType,
              });
            }
          }

          if (isArray(route.HealthIssueCode, 1)) {
            for (const item of route.HealthIssueCode) {
              if (!item.ClassInteraction) continue;

              const healthIssueCode = item.ClassInteraction;
              const severityRanking = get(
                healthIssueCode,
                "Severity.ranking",
                1
              );
              if (severityRanking < mucDoCanhBaoMap.D2H) continue;

              const drugA = {
                name: parentDrug?.name || currentDrug.name,
                tenDichVu: parentDrug?.tenDichVu || currentDrug.tenDichVu,
                generic: get(
                  healthIssueCode,
                  "PrescribingInteractionClass.PrescribingMolecule.name",
                  ""
                ),
                dichVuId: parentDrug?.dichVuId || currentDrug.dichVuId,
                maDichVu: parentDrug?.maDichVu || currentDrug.maDichVu,
              };

              // Get drug B (InteractionClass - affecting drug)
              const drugB = {
                name: item.name,
                tenDichVu: item.tenDichVu,
                generic: get(
                  healthIssueCode,
                  "InteractionClass.Molecule.name",
                  ""
                ),
                dichVuId: item.dichVuId,
                maDichVu: item.maDichVu,
                codeType: item.codeType,
                code: item.code,
              };

              // Get severity
              const severity = get(healthIssueCode, "Severity.name", "");

              healthIssues.push({
                nameA: drugA.name,
                tenDichVuA: drugA.tenDichVu,
                genericA: drugA.generic,
                nameB: drugB.name,
                tenDichVuB: drugB.tenDichVu,
                genericB: drugB.generic,
                severity: severity,
                severityRanking: get(healthIssueCode, "Severity.ranking", 1),
                fullInteraction: healthIssueCode,
                drugADetails: drugA,
                drugBDetails: drugB,
                route: route.name,
                codeType: item.codeType,
                code: item.code,
              });
            }
          }

          if (route.Pregnancy) {
            const pregnancy = route.Pregnancy;
            if (isArray(pregnancy.InteractionClass, 1)) {
              const getCategory = lazyGetRecordByKey(
                pregnancy.Category || [],
                "Source"
              );
              pregnancy.InteractionClass.forEach(
                (interactionClass, interactionClassIndex) => {
                  // Filter pregnancy by D2P severity setting
                  const fdaCategory = getCategory("FDA");
                  const mimsCategory = getCategory("MIMS");

                  let fdaSeverityRanking = 1;
                  if (fdaCategory?.name) {
                    switch (fdaCategory.name.toUpperCase()) {
                      case "X":
                        fdaSeverityRanking = 5;
                        break;
                      case "D":
                        fdaSeverityRanking = 4;
                        break;
                      case "C":
                      case "+":
                        fdaSeverityRanking = 3;
                        break;
                      case "B":
                        fdaSeverityRanking = 2;
                        break;
                      case "A":
                        fdaSeverityRanking = 1;
                        break;
                    }
                  }

                  const severityRanking = Math.max(
                    fdaSeverityRanking,
                    mimsCategory ? 3 : 1
                  );

                  // Pregnancy higher is riskier
                  // D2P: 5=X, 4=D, 3=C, 2=B, 1=A
                  // Ví dụ thiết lập 4 thì chỉ hiển thị X, D
                  if (severityRanking < mucDoCanhBaoMap.D2P) return;

                  const groupKey = `${
                    currentDrug.dichVuId || currentDrug.name
                  }_${interactionClass?.name || "general"}`;

                  pregnancies.push({
                    drugName: currentDrug.tenDichVu || currentDrug.name,
                    tenDichVu: currentDrug.tenDichVu,
                    name: currentDrug.name,
                    dichVuId: currentDrug.dichVuId,
                    maDichVu: currentDrug.maDichVu,
                    Category: pregnancy.Category,
                    Molecule: interactionClass.Molecule,
                    groupKey: groupKey,
                    fdaCategory: fdaCategory,
                    mimsCategory: mimsCategory,
                    fdaSeverityRanking: fdaSeverityRanking,
                    severityRanking: severityRanking,
                    // Add full pregnancy data for detailed display
                    fullPregnancy: {
                      ...pregnancy,
                      currentSeverityIndex: interactionClassIndex,
                    },
                  });
                }
              );
            }
          }

          if (route.Lactation) {
            const lactation = route.Lactation;

            lactation.Severity = lactation.Severity
              ? safeConvertToArray(lactation.Severity, ["object"])
              : null;
            if (lactation.Severity && isArray(lactation.Severity, 1)) {
              lactation.Severity.forEach((severity, severityIndex) => {
                // Lactation lower is riskier
                if (severity.ranking > mucDoCanhBaoMap.D2L) return;

                const interactionClass =
                  lactation.InteractionClass &&
                  lactation.InteractionClass[severityIndex]
                    ? lactation.InteractionClass[severityIndex]
                    : null;

                const groupKey = `${currentDrug.dichVuId || currentDrug.name}_${
                  interactionClass?.name || "general"
                }`;

                lactations.push({
                  drugName: currentDrug.tenDichVu || currentDrug.name,
                  tenDichVu: currentDrug.tenDichVu,
                  name: currentDrug.name,
                  dichVuId: currentDrug.dichVuId,
                  maDichVu: currentDrug.maDichVu,
                  severity: severity.name,
                  severityRanking: severity.ranking || 5,
                  comment:
                    lactation.Comment && lactation.Comment[severityIndex]
                      ? lactation.Comment[severityIndex]
                      : "",
                  interactionClass: interactionClass
                    ? {
                        name: interactionClass.name,
                        description: interactionClass.description,
                        molecule: interactionClass.Molecule,
                      }
                    : null,
                  references: lactation.References || [],
                  route: route.name,
                  type: "lactation",
                  groupKey: groupKey,
                  // Add full lactation data for detailed display
                  fullLactation: {
                    ...lactation,
                    currentSeverityIndex: severityIndex,
                  },
                });
              });
            }
          }

          let listKeys = ["Product", "GGPI", "GenericItem"];

          for (const key of listKeys) {
            if (route[key]) {
              if (Array.isArray(route[key])) {
                extractInteractions(route[key], currentDrug);
              } else if (route[key] && typeof route[key] === "object") {
                extractInteractions([route[key]], currentDrug);
              }
            }
          }
        });
      }

      if (isArray(item.Allergy, 1) && mucDoCanhBaoMap.DA >= 1) {
        item.Allergy.forEach((allergyGroup, allergyGroupIndex) => {
          const allergyDetails = [];

          Object.keys(allergyGroup).forEach((allergyType) => {
            const allergyItems = allergyGroup[allergyType];
            if (!isArray(allergyItems, 1)) return;

            allergyItems.forEach((allergyItem) => {
              allergyDetails.push({
                allergyType: allergyType,
                allergyItemName: allergyItem.tenDichVu || allergyItem.name,
                allergyItemReference: allergyItem.reference,
                fullAllergyItem: allergyItem,
                SubstanceClass: allergyItem.SubstanceClass
                  ? safeConvertToArray(allergyItem.SubstanceClass, ["object"])
                  : null,
              });
            });
          });

          if (allergyDetails.length > 0) {
            allergies.push({
              drugName: currentDrug.tenDichVu || currentDrug.name,
              name: currentDrug.name,
              tenDichVu: currentDrug.tenDichVu,
              dichVuId: currentDrug.dichVuId,
              maDichVu: currentDrug.maDichVu,
              allergyDetails: allergyDetails,
              type: "allergy",
              groupIndex: allergyGroupIndex,
              fullAllergyData: {
                allergyGroup,
                currentDrug,
              },
            });
          }
        });
      }
    });
  };

  const extractVnXml = (DANH_SACH_TUONG_TAC) => {
    if (!isArray(DANH_SACH_TUONG_TAC, 1)) return [];
    return DANH_SACH_TUONG_TAC.flatMap((item) => item.CAP_TUONG_TAC);
  };

  // Extract duplicate therapy data from enXml
  const extractDuplicateTherapy = (duplicateTherapyData) => {
    if (!duplicateTherapyData || !duplicateTherapyData.Warning) return [];

    let listWarning = [];

    duplicateTherapyData.Warning.forEach((warning, index) => {
      const duplicate = warning.Duplicate;
      // duplicate therapy mức độ nhỏ hơn thiết lập hiển thị thì skip
      // do mims trả về mức độ cao nhất là 1
      if (warning.Level > mucDoCanhBaoMap.DT) return;

      const drugs = [
        ...(duplicate.GenericItem || []),
        ...(duplicate.Product || []),
        ...(duplicate.GGPI || []),
      ];

      let description = "";
      if (drugs.length >= 2) {
        const drug1 = drugs[0];
        const drug2 = drugs[1];
        const drug1Name = drug1.tenDichVu || drug1.name || "";
        const drug2Name = drug2.tenDichVu || drug2.name || "";
        description = `${drug1Name} and ${drug2Name} [ATC Code: ${duplicate.ATCCode}]`;
      } else if (drugs.length === 1) {
        const drug = drugs[0];
        const drugName = drug.tenDichVu || drug.name || "";
        description = `${drugName} and ${drugName} [ATC Code: ${duplicate.ATCCode}]`;
      } else {
        description = ` drugs [ATC Code: ${duplicate.ATCCode}]`;
      }

      listWarning.push({
        id: index,
        level: warning.Level,
        description: description,
        atcCode: duplicate.ATCCode,
        drugs: drugs.map((drug) => ({
          dichVuId: drug.dichVuId,
          maDichVu: drug.maDichVu,
          tenDichVu: drug.tenDichVu,
          name: drug.name,
          reference: drug.reference,
        })),
        routeOfAdministration: duplicate.RouteOfAdministration?.name || "",
        severityColor:
          warning.Level == "1"
            ? "#ff0000"
            : warning.Level == "2"
            ? "#FFF333"
            : "#90EE90",
      });
    });

    return listWarning;
  };

  const extractDuplicateIngredient = (duplicateIngredientData) => {
    if (!duplicateIngredientData || !duplicateIngredientData.Warning) return [];

    let listWarning = [];

    duplicateIngredientData.Warning.forEach((warning, index) => {
      // duplicate ingredient mức độ nhỏ hơn thiết lập hiển thị thì skip
      // do mims trả về mức độ cao nhất là 1
      if (warning.Level > mucDoCanhBaoMap.DI) return;

      const molecule = warning.Molecule;
      const drugs = [
        ...(molecule.Molecule?.GenericItem || []),
        ...(molecule.Molecule?.Product || []),
        ...(molecule.Molecule?.GGPI || []),
        ...(molecule.GenericItem || []),
        ...(molecule.Product || []),
        ...(molecule.GGPI || []),
      ];

      let description = "";
      const moleculeName = molecule.tenDichVu || molecule.name || "";

      if (drugs.length >= 2) {
        const drug1 = drugs[0];
        const drug2 = drugs[1];
        const drug1Name = drug1.tenDichVu || drug1.name || "";
        const drug2Name = drug2.tenDichVu || drug2.name || "";
        description = `${drug1Name} [${moleculeName}] vs ${drug2Name} [${moleculeName}]`;
      } else if (drugs.length === 1) {
        const drug = drugs[0];
        const drugName = drug.tenDichVu || drug.name || "";
        description = `${drugName} [${moleculeName}] vs ${drugName} [${moleculeName}]`;
      } else {
        description = ` [${moleculeName}] vs  [${moleculeName}]`;
      }

      listWarning.push({
        id: index,
        level: warning.Level,
        description: description,
        molecule: {
          dichVuId: molecule.dichVuId,
          maDichVu: molecule.maDichVu,
          tenDichVu: molecule.tenDichVu,
          name: molecule.name,
          reference: molecule.reference,
        },
        drugs: drugs.map((drug) => ({
          dichVuId: drug.dichVuId,
          maDichVu: drug.maDichVu,
          tenDichVu: drug.tenDichVu,
          name: drug.name,
          reference: drug.reference,
        })),
        routeOfAdministration: warning.RouteOfAdministration?.name || "",
        severityColor:
          warning.Level == "1"
            ? "#ff0000"
            : warning.Level == "2"
            ? "#FFF333"
            : "#90EE90",
      });
    });

    return listWarning;
  };

  if (interaction.Product) {
    extractInteractions(interaction.Product, null);
  }
  if (interaction.GGPI) {
    extractInteractions(interaction.GGPI, null);
  }
  if (interaction.GenericItem) {
    extractInteractions(interaction.GenericItem, null);
  }

  const vnInteractions = extractVnXml(
    mimsData?.vnXml?.Interaction?.DANH_SACH_TUONG_TAC
  );

  const enInteraction = mimsData?.enXml?.Interaction;
  if (enInteraction?.DuplicateTherapy) {
    duplicateTherapies.push(
      ...extractDuplicateTherapy(enInteraction.DuplicateTherapy)
    );
  }
  if (enInteraction?.DuplicateIngredient) {
    duplicateIngredients.push(
      ...extractDuplicateIngredient(enInteraction.DuplicateIngredient)
    );
  }

  return {
    interactions,
    healthIssues,
    vnInteractions,
    duplicateTherapies,
    duplicateIngredients,
    allergies,
    lactations,
    pregnancies,
  };
};

const MimsPopup = ({
  loading,
  action,
  visible,
  error,
  htmlContent,
  dsTacNhanDiUngId,
  builtInteractionList,
  prescriptionReasons,
  onPrescriptionReasonChange,
  requiredInteractions,
  getDsThuocDangKe,
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState("interactions");
  const [showMimsContent, setShowMimsContent] = useState(false);
  const dsThuocDangKe = getDsThuocDangKe();

  const [getThuocDangKe] = useLazyKVMap(dsThuocDangKe, "dichVuId");

  const { data: listAllTacNhanDiUng } = useQueryAll(
    query.tacNhanDiUng.queryAllTacNhanDiUng
  );

  // Check if all required prescription reasons are filled
  const allReasonsFilled = useMemo(() => {
    if (!requiredInteractions || requiredInteractions.length === 0) {
      return true;
    }

    return requiredInteractions.every(
      (key) => !isEmpty(get(prescriptionReasons, key, ""))
    );
  }, [requiredInteractions, prescriptionReasons]);

  const [dataCHONG_CHI_DINH_THUOC_BYT] = useThietLap(
    THIET_LAP_CHUNG.CHONG_CHI_DINH_THUOC_BYT
  );

  // Lọc các tương tác chống chỉ định
  const blockedInteractions = useMemo(() => {
    if (
      !dataCHONG_CHI_DINH_THUOC_BYT?.eval() ||
      parseInt(dataCHONG_CHI_DINH_THUOC_BYT) !== 1
    ) {
      return [];
    }
    return (builtInteractionList?.vnInteractions || []).filter(
      (vnInteraction) =>
        vnInteraction.MucDoNghiemTrong === VN_SEVERITY_RANKING.CHONG_CHI_DINH &&
        (getThuocDangKe(vnInteraction.hoatChat1DTO?.dichVuId) ||
          getThuocDangKe(vnInteraction.hoatChat2DTO?.dichVuId))
    );
  }, [
    builtInteractionList?.vnInteractions,
    dataCHONG_CHI_DINH_THUOC_BYT,
    dsThuocDangKe,
  ]);

  const handleSwitchToReasonsTab = () => {
    setActiveTab("reasons");
  };

  const handleSwitchToBlockedDrugsTab = () => {
    setActiveTab("blockedDrugs");
  };

  const handleToggleMimsContent = () => {
    setShowMimsContent(!showMimsContent);
  };

  return (
    <Modal
      centered={loading}
      title={
        loading ? null : (
          <div style={{ display: "flex", alignItems: "center", gap: 10 }}>
            <div>{t("khamBenh.canhBaoTuongTacThuoc")}</div>
            <div style={{ width: 300 }}>
              <VirtualizedSelect
                options={listAllTacNhanDiUng}
                value={dsTacNhanDiUngId}
                onChange={action.setDsTacNhanDiUngId}
                width={300}
                dropdownWidth={300}
                placeholder={t("khamBenh.chonTacNhanDiUng")}
                mode="multiple"
              />
            </div>
            <Button
              onClick={() => {
                action.fetchFunction?.({
                  dsTacNhanDiUngId: dsTacNhanDiUngId,
                });
              }}
            >
              {t("khamBenh.kiemTraTuongTacThuoc")}
            </Button>

            {htmlContent && (
              <Button
                type="text"
                icon={
                  showMimsContent ? <EyeInvisibleOutlined /> : <EyeOutlined />
                }
                onClick={handleToggleMimsContent}
                style={{ marginLeft: 8 }}
              >
                {showMimsContent ? "Ẩn nội dung MIMS" : "Hiện nội dung MIMS"}
              </Button>
            )}
          </div>
        )
      }
      open={visible}
      closable={loading ? false : true}
      onCancel={action.closePopup}
      width={loading ? "300px" : "clamp(500px, 90vw, 1600px)"}
      style={{ top: 20 }}
      footer={
        loading
          ? null
          : [
              <div
                style={{
                  display: "flex",
                  gap: 10,
                  justifyContent: "space-between",
                }}
              >
                <Button onClick={action.closePopup}>{t("common.dong")}</Button>
                <Button
                  onClick={() => {
                    if (blockedInteractions.length > 0) {
                      message.error(t("khamBenh.tonTaiThuocChongChiDinh"));
                      return;
                    }

                    if (!allReasonsFilled) {
                      message.error(
                        "Vui lòng nhập đầy đủ lý do chỉ định cho tất cả tương tác thuốc bắt buộc"
                      );
                      return;
                    }

                    action?.onContinue?.(prescriptionReasons);
                    action?.closePopup?.();
                  }}
                  type="primary"
                  disabled={!allReasonsFilled}
                  rightIcon={<SVG.IcArrowLeft rotate={180} />}
                >
                  {t("common.tiepTuc")}
                </Button>
              </div>,
            ]
      }
      destroyOnClose
      bodyStyle={{
        padding: !loading && "0 0 24px 0",
        position: "relative",
      }}
      transitionName=""
      maskTransitionName=""
    >
      <Main $loading={loading}>
        {error && (
          <Alert
            message="Lỗi"
            description={error}
            type="error"
            showIcon
            style={{ margin: "24px" }}
          />
        )}

        {loading ? (
          <div
            style={{
              textAlign: "center",
              display: "flex",
              flexDirection: "column",
              gap: 10,
            }}
          >
            <Spin size="large" />
            <div style={{ fontSize: 16, fontWeight: 500 }}>
              {t("khamBenh.dangKiemTraTuongTacThuoc")}
            </div>
          </div>
        ) : (
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            defaultActiveKey="interactions"
            size="small"
          >
            <Tabs.TabPane
              tab={
                <span>
                  Tương tác thuốc
                  {blockedInteractions.length > 0 && (
                    <Tag color="red" style={{ marginLeft: 8 }}>
                      {blockedInteractions.length} chống chỉ định
                    </Tag>
                  )}
                  {requiredInteractions && requiredInteractions.length > 0 && (
                    <Tag color="orange" style={{ marginLeft: 8 }}>
                      {requiredInteractions.length} cần lý do
                    </Tag>
                  )}
                </span>
              }
              key="interactions"
            >
              <InteractionListContainer>
                {blockedInteractions.length > 0 && (
                  <Alert
                    message={`Có thuốc tương tác chống chỉ định. Vui lòng chuyển sang tab "Thuốc chống chỉ định" để xem chi tiết.`}
                    type="error"
                    showIcon
                    style={{ marginBottom: 16 }}
                    action={
                      <Button
                        size="small"
                        type="link"
                        onClick={handleSwitchToBlockedDrugsTab}
                      >
                        Chuyển đến tab thuốc chống chỉ định
                      </Button>
                    }
                  />
                )}

                {requiredInteractions && requiredInteractions.length > 0 && (
                  <Alert
                    message={`Có ${requiredInteractions.length} tương tác thuốc cần nhập lý do chỉ định. Vui lòng chuyển sang tab "Lý do chỉ định" để nhập thông tin.`}
                    type="warning"
                    showIcon
                    style={{ marginBottom: 16 }}
                    action={
                      <Button
                        size="small"
                        type="link"
                        onClick={handleSwitchToReasonsTab}
                      >
                        Chuyển đến tab lý do chỉ định
                      </Button>
                    }
                  />
                )}
                <Row gutter={16}>
                  <Col span={showMimsContent ? 12 : 24}>
                    <VnInteractionList
                      interactions={builtInteractionList?.vnInteractions || []}
                      getThuocDangKe={getThuocDangKe}
                    />
                    <BuiltInteractionList
                      interactions={builtInteractionList?.interactions || []}
                      healthIssues={builtInteractionList?.healthIssues || []}
                      duplicateTherapies={
                        builtInteractionList?.duplicateTherapies || []
                      }
                      duplicateIngredients={
                        builtInteractionList?.duplicateIngredients || []
                      }
                      allergies={builtInteractionList?.allergies || []}
                      lactations={builtInteractionList?.lactations || []}
                      pregnancies={builtInteractionList?.pregnancies || []}
                      getThuocDangKe={getThuocDangKe}
                    />
                  </Col>
                  {showMimsContent && (
                    <Col span={12}>
                      {htmlContent ? (
                        <MimsIframeViewer htmlContent={htmlContent} />
                      ) : (
                        <Alert
                          message={t("khamBenh.khongCoThongTinChiTiet")}
                          type="info"
                          showIcon
                        />
                      )}
                    </Col>
                  )}
                </Row>
              </InteractionListContainer>
            </Tabs.TabPane>
            {blockedInteractions.length > 0 && (
              <Tabs.TabPane tab="Thuốc chống chỉ định" key="blockedDrugs">
                <InteractionListContainer>
                  <BlockedDrugsList
                    blockedInteractions={blockedInteractions || []}
                  />
                </InteractionListContainer>
              </Tabs.TabPane>
            )}

            {requiredInteractions && requiredInteractions.length > 0 && (
              <Tabs.TabPane tab="Lý do chỉ định" key="reasons">
                <InteractionListContainer>
                  <PrescriptionReasonInput
                    prescriptionReasons={prescriptionReasons}
                    onPrescriptionReasonChange={onPrescriptionReasonChange}
                    requiredInteractions={requiredInteractions}
                    builtInteractionList={builtInteractionList}
                  />
                </InteractionListContainer>
              </Tabs.TabPane>
            )}
          </Tabs>
        )}
      </Main>
    </Modal>
  );
};

const useCanhBaoTuongTacMims = ({ getDsThuocDangKe }) => {
  const mayTinhId = useStore("application.mayTinhId", "");

  const [dataKET_NOI_MIMS] = useThietLap(THIET_LAP_CHUNG.KET_NOI_MIMS);
  const [dataMUC_DO_YEU_CAU_LY_DO_CHI_DINH_THUOC_MIMS] = useThietLap(
    THIET_LAP_CHUNG.MUC_DO_YEU_CAU_LY_DO_CHI_DINH_THUOC_MIMS
  );
  const [dataMUC_DO_CANH_BAO_MIMS] = useThietLap(
    THIET_LAP_CHUNG.MUC_DO_CANH_BAO_MIMS
  );

  const [dataNGUON_CHI_DINH_TUONG_TAC_THUOC] = useThietLap(
    THIET_LAP_CHUNG.NGUON_CHI_DINH_TUONG_TAC_THUOC
  );

  const {
    logNguoiDung: { guiLog },
  } = useDispatch();

  const shouldCheckMims = useMemo(() => {
    const listNguonChiDinh = dataNGUON_CHI_DINH_TUONG_TAC_THUOC
      .split(",")
      .map((item) => parseInt(item))
      .filter((item) => !isNaN(item));

    return (chiDinhTuLoaiDichVu) => {
      if (!dataKET_NOI_MIMS?.eval() || !dataNGUON_CHI_DINH_TUONG_TAC_THUOC) {
        return false;
      }

      return listNguonChiDinh.includes(parseInt(chiDinhTuLoaiDichVu));
    };
  }, [dataKET_NOI_MIMS, dataNGUON_CHI_DINH_TUONG_TAC_THUOC]);

  const { mucDoYeuCauLyDoMap, mucDoCanhBaoMap } = useMemo(() => {
    const extractedMucDoYeuCau = extractSeverityLevels(
      dataMUC_DO_YEU_CAU_LY_DO_CHI_DINH_THUOC_MIMS
    );
    const extractedMucDoCanhBao = extractSeverityLevels(
      dataMUC_DO_CANH_BAO_MIMS
    );
    return {
      mucDoYeuCauLyDoMap: {
        D2D: extractedMucDoYeuCau.D2D || 5,
        D2H: extractedMucDoYeuCau.D2H || 3,
        DI: extractedMucDoYeuCau.DI || 1,
        DT: extractedMucDoYeuCau.DT || 1,
        D2L: extractedMucDoYeuCau.D2L || 1,
        D2P: extractedMucDoYeuCau.D2P || 5,
        DA: isNumber(extractedMucDoYeuCau.DA) ? extractedMucDoYeuCau.DA : 1,
      },
      mucDoCanhBaoMap: {
        D2D: extractedMucDoCanhBao.D2D || 4,
        D2H: extractedMucDoCanhBao.D2H || 3,
        DI: extractedMucDoCanhBao.DI || 1,
        DT: extractedMucDoCanhBao.DT || 1,
        D2L: extractedMucDoCanhBao.D2L || 1,
        D2P: extractedMucDoCanhBao.D2P || 5,
        DA: isNumber(extractedMucDoCanhBao.DA) ? extractedMucDoCanhBao.DA : 1,
      },
    };
  }, [dataMUC_DO_YEU_CAU_LY_DO_CHI_DINH_THUOC_MIMS, dataMUC_DO_CANH_BAO_MIMS]);

  const [visible, setVisible] = useState(false);
  const [htmlContent, setHtmlContent] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [action] = useState({});
  const [dsTacNhanDiUngId, setDsTacNhanDiUngId] = useState([]);
  const [builtInteractionList, setBuiltInteractionList] = useState({});
  const [rawMimsData, setRawMimsData] = useState(null); // dữ liệu gốc từ mims
  const [prescriptionReasons, setPrescriptionReasons] = useState({});
  const [requiredInteractions, setRequiredInteractions] = useState([]);
  const dsThuocDangKe = getDsThuocDangKe();

  const getColorTuongTacThuoc = useMemo(() => {
    const vnInteractions = builtInteractionList?.vnInteractions || [];

    const extractedPairs = requiredInteractions.map((item) => {
      const interactionKeys = item.split("_");
      return {
        type: interactionKeys[0],
        drugAId: interactionKeys[1],
        drugBId: interactionKeys[2], // Will be undefined for "single" type
        drugIds: interactionKeys.slice(1), // For duplicate therapy/ingredient cases
      };
    });

    const getOrderedVnInteractions = lazyGetRecordByKey(VN_SEVERITIES, "name");

    return (dichVuId, listAllDsThuoc) => {
      const getByDichVuId = lazyGetRecordByKey(listAllDsThuoc, "dichVuId");

      const countRecordsByDichVuId = (targetDichVuId) => {
        return listAllDsThuoc.filter((drug) => drug.dichVuId === targetDichVuId)
          .length;
      };

      /**
       * Check VN interactions (red color for "Chống chỉ định")
       * For self-interactions cần 2 bản ghi cùng dichVuId sử dụng hàm countRecordsByDichVuId để đếm số lượng bản ghi cùng dichVuId
       */
      const vnDrugInteractionBlocked = vnInteractions
        .sort((a, b) => {
          const aLevel =
            getOrderedVnInteractions(a.MucDoNghiemTrong)?.level || 4;
          const bLevel =
            getOrderedVnInteractions(b.MucDoNghiemTrong)?.level || 4;

          return aLevel - bLevel;
        })
        .find((item) => {
          const drugA = item.hoatChat1DTO?.dichVuId;
          const drugB = item.hoatChat2DTO?.dichVuId;

          if (drugA !== dichVuId && drugB !== dichVuId) {
            return false;
          }

          if (drugA === drugB) {
            return drugA === dichVuId && countRecordsByDichVuId(dichVuId) >= 2;
          }

          if (
            drugA === dichVuId &&
            (isNil(drugB) ? true : getByDichVuId(drugB))
          ) {
            return true;
          }
          if (
            drugB === dichVuId &&
            (isNil(drugA) ? true : getByDichVuId(drugA))
          ) {
            return true;
          }

          // Đối với thuốc chống chỉ định BYT chỉ cần check 1 thuốc mapping khơp
          return false;
        });

      const isRed =
        vnDrugInteractionBlocked?.MucDoNghiemTrong === "Chống chỉ định";
      const isOrange =
        !isRed &&
        (vnDrugInteractionBlocked?.MucDoNghiemTrong ===
          "Chống chỉ định có điều kiện" ||
          extractedPairs.some((item) => {
            const drugAId = item.drugAId ? Number(item.drugAId) : null;
            const drugBId = item.drugBId ? Number(item.drugBId) : null;
            const dichVuIdNum = Number(dichVuId);

            if (item.type === "single") {
              if (drugAId === dichVuIdNum && getByDichVuId(drugAId)) {
                return true;
              }
            } else if (drugAId && drugBId) {
              // Kiểm tra nếu thuốc hiện tại không liên quan
              if (drugAId !== dichVuIdNum && drugBId !== dichVuIdNum) {
                // Không liên quan, giữ result = false
              }
              // Kiểm tra trường hợp cùng thuốc
              else if (drugAId === drugBId) {
                if (
                  drugAId === dichVuIdNum &&
                  countRecordsByDichVuId(dichVuIdNum) >= 2
                ) {
                  return true;
                }
              }
              // Kiểm tra tương tác giữa hai thuốc khác nhau
              else if (drugAId === dichVuIdNum && getByDichVuId(drugBId)) {
                return true;
              } else if (drugBId === dichVuIdNum && getByDichVuId(drugAId)) {
                return true;
              }
            }

            // Handle duplicate therapy/ingredient cases with multiple drug IDs
            if (
              item.type === "duplicateTherapy" ||
              item.type === "duplicateIngredient"
            ) {
              const drugIds = item.drugIds.map((id) => Number(id));
              const currentDrugInvolved = drugIds.includes(dichVuIdNum);

              if (currentDrugInvolved) {
                const otherDrugsPresent = drugIds.filter((id) => {
                  if (id === dichVuIdNum) {
                    return countRecordsByDichVuId(id) > 1;
                  } else {
                    return getByDichVuId(id);
                  }
                });

                if (otherDrugsPresent.length > 0) {
                  return true;
                }
              }
            }

            // Kiểm tra các trường hợp đặc biệt
            if (
              item.type === "allergy" ||
              item.type === "lactation" ||
              item.type === "pregnancy"
            ) {
              if (drugAId === dichVuIdNum && getByDichVuId(drugAId)) {
                return true;
              }
            }

            return false;
          }));

      return isRed ? "red" : isOrange ? "orange" : null;
    };
  }, [builtInteractionList, requiredInteractions, dsThuocDangKe, rawMimsData]);

  const sortBySeverity = useCallback((interactions) => {
    return orderBy(interactions, ["MucDoNghiemTrong"], ["desc"]);
  }, []);

  const kiemTraMims = useCallback(
    async ({
      chiDinhTuDichVuId,
      chiDinhTuLoaiDichVu,
      nbDotDieuTriId,
      dsDichVuId,
      dsTacNhanDiUngId: dsTacNhanDiUngIdBanDau,
      thoiGianYLenh,
    }) => {
      if (!shouldCheckMims(chiDinhTuLoaiDichVu)) {
        const data = {
          thoiGianThucHien: moment().format("YYYY-MM-DD HH:mm:ss"),
          tenMay: `${mayTinhId || ""}|${window.tabId}`,
          url: window.location.href,
          noiDung:
            "Bỏ kiểm tra tương tác thuốc: " +
            JSON.stringify({
              chiDinhTuLoaiDichVu,
              dataNGUON_CHI_DINH_TUONG_TAC_THUOC,
              dataKET_NOI_MIMS,
              nbDotDieuTriId,
              dsDichVuId,
              dsTacNhanDiUngIdBanDau,
              thoiGianYLenh,
            }),
        };
        guiLog(data);
        return Promise.reject(
          new Error("Không đủ điều kiện để kiểm tra tương tác thuốc")
        );
      }

      const fetchFunction = async ({ dsTacNhanDiUngId }) => {
        setError(null);
        setLoading(true);
        setVisible(true);
        setHtmlContent("");
        setBuiltInteractionList({});
        setRawMimsData(null);

        try {
          const response = await mimsProvider.kiemTra({
            chiDinhTuDichVuId,
            chiDinhTuLoaiDichVu,
            nbDotDieuTriId,
            dsDichVuId,
            thoiGianYLenh,
            dsTacNhanDiUngId: chain([
              ...(dsTacNhanDiUngIdBanDau || []),
              ...(dsTacNhanDiUngId || []),
            ])
              .compact()
              .uniq()
              .value(),
          });

          if (response?.data) {
            if (response.data.enHtml) {
              setHtmlContent(response.data.enHtml);
            }
            const {
              interactions = [],
              healthIssues = [],
              vnInteractions = [],
              duplicateTherapies = [],
              duplicateIngredients = [],
              allergies = [],
              lactations = [],
              pregnancies = [],
            } = buildInteractionList(response.data, mucDoCanhBaoMap);

            const hasInteraction = interactions.length > 0;
            const hasHealthIssue = healthIssues.length > 0;
            const hasDuplicateTherapy = duplicateTherapies.length > 0;
            const hasDuplicateIngredient = duplicateIngredients.length > 0;
            const hasVnInteraction = vnInteractions.length > 0;
            const hasAllergy = allergies.length > 0;
            const hasLactation = lactations.length > 0;
            const hasPregnancy = pregnancies.length > 0;

            if (
              !(
                hasInteraction ||
                hasHealthIssue ||
                hasVnInteraction ||
                hasDuplicateTherapy ||
                hasDuplicateIngredient ||
                hasAllergy ||
                hasLactation ||
                hasPregnancy
              )
            ) {
              setVisible(false);
              return Promise.reject(
                new Error("Không có tương tác thuốc nguy hiểm")
              );
            }

            setRawMimsData(response.data);

            // Determine which interactions require prescription reasons
            const requiredKeys = [];
            const currentDsThuocDangKe = getDsThuocDangKe() || [];

            // Helper function to generate unique key for drug pair
            const generateDrugPairKey = (drugAId, drugBId) => {
              const sortedIds = [drugAId, drugBId].sort();
              return `pair_${sortedIds[0]}_${sortedIds[1]}`;
            };

            const generateSingleDrugKey = (drugId) => {
              return `single_${drugId}`;
            };

            // Group interactions by drug pairs

            const groupedInteractions = new Map();
            const groupedHealthIssues = new Map();
            const groupedVnInteractions = new Map();
            const groupedDuplicateTherapies = new Map();
            const groupedDuplicateIngredients = new Map();
            const groupedAllergies = new Map();
            const groupedLactations = new Map();
            const groupedPregnancies = new Map();

            // Group enXml interactions
            interactions.forEach((interaction, index) => {
              const drugAId = interaction.drugADetails?.dichVuId;
              const drugBId = interaction.drugBDetails?.dichVuId;

              if (drugAId && drugBId) {
                const isInvolved = currentDsThuocDangKe.some(
                  (item) =>
                    item.dichVuId === drugAId || item.dichVuId === drugBId
                );

                if (
                  isInvolved &&
                  interaction.severityRanking >= mucDoYeuCauLyDoMap.D2D
                ) {
                  const pairKey = generateDrugPairKey(drugAId, drugBId);
                  if (!groupedInteractions.has(pairKey)) {
                    groupedInteractions.set(pairKey, {
                      key: pairKey,
                      drugAId,
                      drugBId,
                      interactions: [],
                      reasons: [],
                    });
                  }
                  groupedInteractions.get(pairKey).interactions.push({
                    ...interaction,
                    originalIndex: index,
                    type: "interaction",
                  });
                }
              }
            });

            // Group health issues
            healthIssues.forEach((healthIssue, index) => {
              const drugId = healthIssue.drugADetails?.dichVuId;

              if (drugId) {
                const isInvolved = currentDsThuocDangKe.some(
                  (item) => item.dichVuId === drugId
                );

                if (
                  isInvolved &&
                  healthIssue.severityRanking >= mucDoYeuCauLyDoMap.D2H
                ) {
                  const singleKey = generateSingleDrugKey(drugId);
                  if (!groupedHealthIssues.has(singleKey)) {
                    groupedHealthIssues.set(singleKey, {
                      key: singleKey,
                      drugId,
                      healthIssues: [],
                      reasons: [],
                    });
                  }
                  groupedHealthIssues.get(singleKey).healthIssues.push({
                    ...healthIssue,
                    originalIndex: index,
                    type: "healthIssue",
                  });
                }
              }
            });

            // Group VN interactions
            vnInteractions.forEach((vnInteraction, index) => {
              const drugAId = vnInteraction.hoatChat1DTO?.dichVuId;
              const drugBId = vnInteraction.hoatChat2DTO?.dichVuId;

              if (drugAId || drugBId) {
                const isInvolved = currentDsThuocDangKe.some(
                  (item) =>
                    item.dichVuId === drugAId || item.dichVuId === drugBId
                );

                if (
                  isInvolved &&
                  vnInteraction.MucDoNghiemTrong ===
                    "Chống chỉ định có điều kiện"
                ) {
                  const pairKey = generateDrugPairKey(drugAId, drugBId);
                  if (!groupedVnInteractions.has(pairKey)) {
                    groupedVnInteractions.set(pairKey, {
                      key: pairKey,
                      drugAId,
                      drugBId,
                      vnInteractions: [],
                      reasons: [],
                    });
                  }
                  groupedVnInteractions.get(pairKey).vnInteractions.push({
                    ...vnInteraction,
                    originalIndex: index,
                    type: "vnInteraction",
                  });
                }
              }
            });

            // Group duplicate therapies
            duplicateTherapies.forEach((duplicateTherapy, index) => {
              const involvedDrugs = duplicateTherapy.drugs.filter((drug) =>
                currentDsThuocDangKe.some(
                  (item) => item.dichVuId === drug.dichVuId
                )
              );

              if (
                involvedDrugs.length > 0 &&
                duplicateTherapy.level <= mucDoYeuCauLyDoMap.DT
              ) {
                const drugIds = involvedDrugs
                  .map((drug) => drug.dichVuId)
                  .sort();
                const groupKey = `duplicateTherapy_${drugIds.join("_")}`;

                if (!groupedDuplicateTherapies.has(groupKey)) {
                  groupedDuplicateTherapies.set(groupKey, {
                    key: groupKey,
                    drugIds,
                    duplicateTherapies: [],
                    reasons: [],
                  });
                }
                groupedDuplicateTherapies
                  .get(groupKey)
                  .duplicateTherapies.push({
                    ...duplicateTherapy,
                    originalIndex: index,
                    type: "duplicateTherapy",
                  });
              }
            });

            // Group duplicate ingredients
            duplicateIngredients.forEach((duplicateIngredient, index) => {
              const involvedDrugs = duplicateIngredient.drugs.filter((drug) =>
                currentDsThuocDangKe.some(
                  (item) => item.dichVuId === drug.dichVuId
                )
              );

              if (
                involvedDrugs.length > 0 &&
                duplicateIngredient.level <= mucDoYeuCauLyDoMap.DI
              ) {
                const drugIds = involvedDrugs
                  .map((drug) => drug.dichVuId)
                  .sort();
                const groupKey = `duplicateIngredient_${drugIds.join("_")}`;

                if (!groupedDuplicateIngredients.has(groupKey)) {
                  groupedDuplicateIngredients.set(groupKey, {
                    key: groupKey,
                    drugIds,
                    duplicateIngredients: [],
                    reasons: [],
                  });
                }
                groupedDuplicateIngredients
                  .get(groupKey)
                  .duplicateIngredients.push({
                    ...duplicateIngredient,
                    originalIndex: index,
                    type: "duplicateIngredient",
                  });
              }
            });

            // Group allergies
            allergies.forEach((allergy, index) => {
              const drugId = allergy.dichVuId;

              if (drugId) {
                const isInvolved = currentDsThuocDangKe.some(
                  (item) => item.dichVuId === drugId
                );

                if (isInvolved) {
                  const allergyKey = `allergy_${drugId}_${allergy.groupIndex}`;
                  if (!groupedAllergies.has(allergyKey)) {
                    groupedAllergies.set(allergyKey, {
                      key: allergyKey,
                      drugId,
                      allergies: [],
                      reasons: [],
                    });
                  }
                  groupedAllergies.get(allergyKey).allergies.push({
                    ...allergy,
                    originalIndex: index,
                    type: "allergy",
                  });
                }
              }
            });

            // Group lactations
            lactations.forEach((lactation, index) => {
              const drugId = lactation.dichVuId;

              if (drugId) {
                const isInvolved = currentDsThuocDangKe.some(
                  (item) => item.dichVuId === drugId
                );

                // Lactation lower is riskier
                if (
                  isInvolved &&
                  lactation.severityRanking <= mucDoYeuCauLyDoMap.D2L
                ) {
                  const lactationKey = `lactation_${drugId}_${lactation.groupKey}`;
                  if (!groupedLactations.has(lactationKey)) {
                    groupedLactations.set(lactationKey, {
                      key: lactationKey,
                      drugId,
                      lactations: [],
                      reasons: [],
                    });
                  }
                  groupedLactations.get(lactationKey).lactations.push({
                    ...lactation,
                    originalIndex: index,
                    type: "lactation",
                  });
                }
              }
            });

            // Group pregnancies
            pregnancies.forEach((pregnancy, index) => {
              const drugId = pregnancy.dichVuId;

              if (drugId) {
                const isInvolved = currentDsThuocDangKe.some(
                  (item) => item.dichVuId === drugId
                );

                if (isInvolved) {
                  // Pregnancy higher is riskier
                  // D2P: 5=X, 4=D, 3=C, 2=B, 1=A
                  // Ví dụ thiết lập 4 thì chỉ hiển thị X, D
                  const severityRanking = pregnancy.severityRanking || 5;

                  const requiresReason =
                    severityRanking >= mucDoYeuCauLyDoMap.D2P;

                  if (requiresReason) {
                    const pregnancyKey = `pregnancy_${drugId}_${pregnancy.groupKey}`;
                    if (!groupedPregnancies.has(pregnancyKey)) {
                      groupedPregnancies.set(pregnancyKey, {
                        key: pregnancyKey,
                        drugId,
                        pregnancies: [],
                        reasons: [],
                      });
                    }
                    groupedPregnancies.get(pregnancyKey).pregnancies.push({
                      ...pregnancy,
                      originalIndex: index,
                      type: "pregnancy",
                    });
                  }
                }
              }
            });

            // Add all grouped keys to requiredKeys
            groupedInteractions.forEach((group) => {
              requiredKeys.push(group.key);
            });
            groupedHealthIssues.forEach((group) => {
              requiredKeys.push(group.key);
            });
            groupedVnInteractions.forEach((group) => {
              requiredKeys.push(group.key);
            });
            groupedDuplicateTherapies.forEach((group) => {
              requiredKeys.push(group.key);
            });
            groupedDuplicateIngredients.forEach((group) => {
              requiredKeys.push(group.key);
            });
            groupedAllergies.forEach((group) => {
              requiredKeys.push(group.key);
            });
            groupedLactations.forEach((group) => {
              requiredKeys.push(group.key);
            });
            groupedPregnancies.forEach((group) => {
              requiredKeys.push(group.key);
            });
            //debug consoles
            // console.log("Debug - requiredKeys:", requiredKeys);
            // console.log(
            //   "Debug - groupedInteractions:",
            //   Array.from(groupedInteractions.values())
            // );
            // console.log(
            //   "Debug - groupedHealthIssues:",
            //   Array.from(groupedHealthIssues.values())
            // );
            // console.log(
            //   "Debug - groupedVnInteractions:",
            //   Array.from(groupedVnInteractions.values())
            // );
            // console.log(
            //   "Debug - groupedDuplicateTherapies:",
            //   Array.from(groupedDuplicateTherapies.values())
            // );
            // console.log(
            //   "Debug - groupedDuplicateIngredients:",
            //   Array.from(groupedDuplicateIngredients.values())
            // );

            setRequiredInteractions(requiredKeys);
            setPrescriptionReasons({});

            setBuiltInteractionList({
              interactions,
              healthIssues,
              vnInteractions,
              duplicateTherapies,
              duplicateIngredients,
              allergies,
              lactations,
              pregnancies,
              groupedInteractions: Array.from(groupedInteractions.values()),
              groupedHealthIssues: Array.from(groupedHealthIssues.values()),
              groupedVnInteractions: Array.from(groupedVnInteractions.values()),
              groupedDuplicateTherapies: Array.from(
                groupedDuplicateTherapies.values()
              ),
              groupedDuplicateIngredients: Array.from(
                groupedDuplicateIngredients.values()
              ),
              groupedAllergies: Array.from(groupedAllergies.values()),
              groupedLactations: Array.from(groupedLactations.values()),
              groupedPregnancies: Array.from(groupedPregnancies.values()),
            });

            setHtmlContent(response?.data?.enHtml);

            return response;
          } else {
            setVisible(false);
            return Promise.reject(new Error("Không có dữ liệu phản hồi"));
          }
        } catch (error) {
          console.error("Error checking MIMS interactions:", error);
          setError("Đã xảy ra lỗi khi kiểm tra tương tác thuốc");
          setVisible(false);
          return Promise.reject(error);
        } finally {
          setLoading(false);
        }
      };
      action.fetchFunction = fetchFunction;
      const res = await fetchFunction({ dsTacNhanDiUngId });

      return res;
    },
    [
      dataKET_NOI_MIMS,
      shouldCheckMims,
      dsTacNhanDiUngId,
      mucDoYeuCauLyDoMap,
      mucDoCanhBaoMap,
    ]
  );

  const showMimsPopup = useCallback(() => {
    setVisible(true);
  }, []);

  // không clear builtInteractionList vì nó sẽ bị mất khi mở popup khác
  const closePopup = useCallback(() => {
    setVisible(false);
    action.onContinue = null;
    action.fetchFunction = null;
    setHtmlContent("");
    setError(null);
    setLoading(false);
    setDsTacNhanDiUngId([]);
  }, []);

  const onReset = useCallback(() => {
    setVisible(false);
    action.onContinue = null;
    action.fetchFunction = null;
    setHtmlContent("");
    setError(null);
    setLoading(false);
    setDsTacNhanDiUngId([]);
    setBuiltInteractionList({});
    setRawMimsData(null);
    setPrescriptionReasons({});
    setRequiredInteractions([]);
  }, []);

  const handlePrescriptionReasonChange = useCallback((reasons) => {
    setPrescriptionReasons(reasons);
  }, []);

  action.closePopup = closePopup;
  action.setVisible = setVisible;
  action.setHtmlContent = setHtmlContent;
  action.setError = setError;
  action.setLoading = setLoading;
  action.setDsTacNhanDiUngId = setDsTacNhanDiUngId;
  action.showMimsPopup = showMimsPopup;
  action.kiemTraMims = kiemTraMims;
  action.setBuiltInteractionList = setBuiltInteractionList;
  action.setRawMimsData = setRawMimsData;
  action.onReset = onReset;
  action.setPrescriptionReasons = handlePrescriptionReasonChange;

  return {
    MimsPopup: MimsPopup,
    action,
    visible,
    error,
    loading,
    dsTacNhanDiUngId,
    setDsTacNhanDiUngId,
    builtInteractionList,
    htmlContent,
    rawMimsData,
    prescriptionReasons,
    requiredInteractions,
    // Optimized functions for better performance
    getColorTuongTacThuoc,
    sortBySeverity,
  };
};

export default useCanhBaoTuongTacMims;
