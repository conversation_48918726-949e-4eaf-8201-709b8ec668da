import React, { useState, useEffect, useRef, useMemo } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Form, message, notification } from "antd";
import { DANH_SACH_DAU_TRANG, logicMaPhieuTheoHuongDieuTri } from "./config";
import NavigationBar from "./NavigationBar";
import StepWrapper from "../components/StepWrapper";
import CustomPopoverWithRef from "../components/CustomPopoverWithRef";
import { isEqual, cloneDeep, throttle, get, flatten, isNil } from "lodash";
import { ButtonFooter, Main, ButtonDetail } from "./styled";
import ConflictResolutionModal from "./ConflictResolutionModal";
import {
  compareEditedWithOriginal,
  compareEditedWithServer,
  mergeServerData,
} from "./jsonCompareUtils";

import {
  DOI_TUONG,
  DOI_TUONG_KCB,
  HOTKEY,
  LOAI_DICH_VU,
  LOAI_LAO,
  THIET_LAP_CHUNG,
  TRANG_THAI_DANG_KY_THUOC_LAO,
  TRANG_THAI_DICH_VU,
  LOAI_IN,
  LIST_PHIEU_IN_WORD_THEO_SO_PHIEU,
  LOAI_BIEU_MAU,
  MA_BIEU_MAU_EDITOR,
  ENUM,
} from "constants/index";
import {
  AuthWrapper,
  Button,
  LazyLoad,
  Tooltip,
  ModalSignPrint,
  SelectLargeData,
  Select,
} from "components";
import ModalKetThucKham from "./ModalKetThucKham";
import { ROLES, HUONG_DIEU_TRI_KHAM, KET_QUA_KHAM } from "constants/index";
import { checkRole } from "lib-utils/role-utils";
import { useTranslation } from "react-i18next";
import {
  useConfirm,
  useEnum,
  useInterval,
  useLoading,
  usePrevious,
  useQueryString,
  useStore,
  useThietLap,
} from "hooks";
import moment from "moment";
import { setQueryStringValue } from "hooks/useQueryString/queryString";
import ModalBoSungThongTinThamKham from "./ModalBoSungThongTinThamKham";
import { SVG } from "assets";
import { parseBatBuocNhapCanNang } from "utils/thiet-lap-chung-utils";
import ModalChiDinhPhacDo from "pages/chiDinhDichVu/PhacDoDieuTri/ModalDanhSachPhacDo";
import DropdownSangLocDD from "pages/quanLyDinhDuong/chiTietNbQuanLyDinhDuong/components/DropdownSangLocDD";
import { isArray, openInNewTab, isNumber } from "utils/index";
import { useKetLuanKham } from "./hooks/useKetLuanKham";
import ModalChuyenKhoa from "../components/StepWrapper/ModalChuyenKhoa";
import { centralizedErrorHandling, toSafePromise } from "lib-utils";
import { getState } from "redux-store/stores";
import printProvider from "data-access/print-provider";
import { checkKhamKLKhacBacSi } from "../utils";
import useDVKhongKham from "pages/khamBenh/hooks/useDVKhongKham";
import nbDvKhamProvider from "data-access/nb-dv-kham-provider";
import { guid } from "mainam-react-native-string-utils";
import { showError } from "utils/message-utils";
import { getValueByPath } from "./jsonPathUtils";
import { OBJ_LIST_HARDCODE_CSS } from "pages/thietLap/thietLapGiaTriCSS/configs";
import useKhamVeBoDon from "./hooks/useKhamVeBoDon";
import classNames from "classnames";
import useLienKetHoSoSinh from "./hooks/useLienKetHoSoSinh";

const { SelectChanDoan } = SelectLargeData;

export const refElement = React.createRef();
export const refActiveSaveBtn = React.createRef();

class DetailedError extends Error {
  constructor({ code, message, context }) {
    super(message);
    this.name = `DetailedError at function ${code}: ${message}`;
    this.code = code;
    this.context = context;
  }
}

const sleep = (ms) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

const ThongTin = ({ collapse, onCollapse, layerId }) => {
  const { showConfirm } = useConfirm();
  const [tab] = useQueryString("tab", 0);
  const { showLoading, hideLoading } = useLoading();
  const conflictModalRef = useRef(null);
  const { t } = useTranslation();

  const [isDvKhongKham] = useDVKhongKham();

  if (!refElement.current) {
    refElement.current = {};
  }
  const [GOI_Y_PHAC_DO_DVKHAM_LUU_CDCHINH] = useThietLap(
    THIET_LAP_CHUNG.GOI_Y_PHAC_DO_DVKHAM_LUU_CDCHINH
  );
  const [GOI_Y_PHAC_DO_DVKHAM_SUA_CDCHINH] = useThietLap(
    THIET_LAP_CHUNG.GOI_Y_PHAC_DO_DVKHAM_SUA_CDCHINH
  );
  const [XML130_TRUONG_BAT_BUOC] = useThietLap(
    THIET_LAP_CHUNG.XML130_TRUONG_BAT_BUOC
  );
  const [dataKHAM_BENH_CANH_BAO_THAY_DOI_DU_LIEU_KHI_LUU] = useThietLap(
    THIET_LAP_CHUNG.KHAM_BENH_CANH_BAO_THAY_DOI_DU_LIEU_KHI_LUU,
    "FALSE"
  );
  const [KET_THUC_KHAM_KHI_CHO_DON_THUOC] = useThietLap(
    THIET_LAP_CHUNG.KET_THUC_KHAM_KHI_CHO_DON_THUOC
  );
  const thongTinKetLuan = useStore("khamBenh.thongTinChiTiet.nbKetLuan");

  const [isEditted, setIsEditted] = useState(false);

  const [state, _setState] = useState({
    activeTab: parseInt(tab) || 0,
    isEditDonThuoc: false,
    isEditKhamLao: false,
    isEditThuocLao: false,
    isShowViTimes: true,
    isShowDangKyThuocLaoNgoaiGio: false,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const refModalKetThucKham = useRef(null);
  const refData = useRef({});
  const refConfirmSaveShowing = useRef(null);
  const refDataKetLuan = useRef({});
  const refKetLuanKham = useRef({});
  const refFuncNavi = useRef(null);
  const refModalBoSungThamKham = useRef(null);
  const refDataLoiDan = useRef({});
  const refStepWrapper = useRef(null);
  const refChiDinhThuoc = useRef(null);
  const refInKetLuanKham = useRef(null);
  const refChiDinhPhacDo = useRef(null);
  const refDieuTriLao = useRef(null);
  const refBtnSaveDieuTriLao = useRef(null);
  const refModalChuyenKhoa = useRef(null);
  const refModalSignPrint = useRef(null);

  const listPhongKham = useStore("khamBenh.listPhongKham", []);
  const thongTinBenhNhan = useStore("nbDotDieuTri.thongTinBenhNhan");
  const listThietLapGiaTriCSS = useStore(
    "thietLapGiaTriCSS.listThietLapGiaTriCSS",
    []
  );
  const {
    listDvThuoc = [],
    listDvThuocKeNgoai = [],
    listDvThuocNhaThuoc = [],
  } = useStore(
    "chiDinhDichVuKho",
    {},
    { fields: "listDvThuoc, listDvThuocKeNgoai, listDvThuocNhaThuoc" }
  );
  const { dsDichVuChiDinhXN, dsDichVuChiDinhKham, dsDichVuChiDinhCls } =
    useSelector((state) => state.chiDinhKhamBenh);
  const { nhanVienId, full_name } = useStore(
    "auth.auth",
    {},
    { fields: "full_name, nhanVienId" }
  );

  const [listTheBenhLao] = useEnum(ENUM.THE_BENH_LAO);

  const {
    chiDinhKhamBenh: {
      updateData: updateDataChiDinh,
      updateConfigData,
      luuLoiDan,
    },
    khamBenh: {
      dangKetLuan,
      updateNbDvKham,
      huyKetLuanKham,
      huyKetLuanGetMethod,
      thietLapTrangThai,
      updateData: updateDataKhamBenh,
      getStatisticsRoom,
      updateNbKhamChuyenKhoaTmh,
      updateNbKhamChuyenKhoaCmu,
      updateNbKhamChuyenKhoaRhm,
      updateNbKhamChuyenKhoaDaLieu,
      updateNbKhamChuyenKhoaKhamNgoai,
      updateNbKhamChuyenKhoaSan,
      updateNbKhamChuyenKhoaKhamNam,
      huyDieuTriNgoaiTru,
      getNbDotDieuTriTongHopTheoId,
      updateValidKetThucKham,
      setChiTietDvKham,
      updateLyDoDenKham,
      updateNbKhamChuyenKhoaIVF,
    },
    phimTat: { onRegisterHotkey },
    nbDichVuKhamKSK: { updateKhamKSK, hoanThanhKSK, huyHoanThanhKSK },
    nbPhacDoDieuTri: { onChangeInputSearch },
    phacDoDieuTri: { onChangeInputSearch: onSearchPhacDo },
    vitalSigns: { updateData: updateDataVitalSigns },
    doThiLuc: { updateThongTinChungMat },
    quanLyDieuTriLao: { postChuyenLaoKhang, createOrEdit, getThuocLaoById },
    nguoiBenhDieuTriLao: { getChiTietNbDieuTriLao, dayViTimesNb },
    nbDotDieuTri: { getById },
    nbMaBenhAn: { getDsMaBADaiHan },
    quanLyNoiTru: { huyBenhAnDaiHan },
    phieuIn: { getFilePhieuIn, getPhieuInTheoDsMa, showFileEditor },
    chiDinhDichVuKho: { getListThuocNhaThuoc, getListDichVuThuoc },
  } = useDispatch();

  useInterval(async () => {
    if (
      thongTinChiTiet?.id &&
      dataKHAM_BENH_CANH_BAO_THAY_DOI_DU_LIEU_KHI_LUU?.eval()
    ) {
      const res = await centralizedErrorHandling(
        nbDvKhamProvider.getById(thongTinChiTiet?.id)
      );
      if (res) {
        const localChanges = compareEditedWithOriginal(
          thongTinChiTietBanDau,
          res?.data,
          [
            "nbChiSoSong.thoiGianThucHien",
            "nbDvKyThuat.phongThucHien",
            "nbDvKyThuat.updatedAt",
            "nbDvKyThuat.updatedBy",
            "nbDvKyThuat.trangThai",
            "nbDichVu.*",
            "nbKetLuan.phongHenKham",
            "nbDvKyThuat.khoaChiDinh",
            "nbDvKyThuat.nbDichVu.*",
          ]
        );
        if (localChanges?.length) {
          const key = guid();
          const btn = (
            <Button
              type="primary"
              size="small"
              onClick={() => {
                window.location.reload(true);
                notification.close(key);
              }}
              rightIcon={<SVG.IcReload />}
              minWidth={100}
            >
              Tải lại
            </Button>
          );
          notification.open({
            message: (
              <strong style={{ fontSize: "18px", color: "orange" }}>
                Thông báo quan trọng
              </strong>
            ),
            icon: <SVG.IcWarning />,
            description: (
              <span
                style={{
                  fontSize: "16px",
                  fontWeight: "500",
                  color: "#000000d9",
                  marginTop: "8px",
                  display: "block",
                }}
              >
                {`Dữ liệu của người bệnh đã được thay đổi bởi người khác. Nhấn tải lại để cập nhật dữ liệu`}
              </span>
            ),
            btn,
            key,
            onClose: close,
            duration: 10,
            pauseOnHover: true,
          });
        }
      }
    }
  }, [30000]);

  const [BAT_BUOC_CD_BENH_DOI_TUONG_KHONG_BH] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_CD_BENH_DOI_TUONG_KHONG_BH,
    null
  );

  const [BAT_BUOC_NHAP_DIEN_BIEN_NGOAI_TRU] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_NHAP_DIEN_BIEN_NGOAI_TRU
  );

  const [BAT_BUOC_NHAP_CAN_NANG] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_NHAP_CAN_NANG
  );
  const [GIOI_HAN_SO_NGAY_CHO_THUOC_NGOAI_TRU] = useThietLap(
    THIET_LAP_CHUNG.GIOI_HAN_SO_NGAY_CHO_THUOC_NGOAI_TRU
  );
  const [BAT_BUOC_THOI_GIAN_DUNG_THUOC_NGOAI_TRU] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_THOI_GIAN_DUNG_THUOC_NGOAI_TRU
  );

  const [KHONG_DUOC_KHAM_KL_NB_KHAC_BSKHAM] = useThietLap(
    THIET_LAP_CHUNG.KHONG_DUOC_KHAM_KL_NB_KHAC_BSKHAM,
    "FALSE"
  );
  const [dataTU_DONG_IN_KHI_DONG_HO_SO] = useThietLap(
    THIET_LAP_CHUNG.TU_DONG_IN_KHI_DONG_HO_SO
  );
  const [dataMAC_DINH_TICK_BINH_THUONG_KHAM_LS_KSK] = useThietLap(
    THIET_LAP_CHUNG.MAC_DINH_TICK_BINH_THUONG_KHAM_LS_KSK,
    "FALSE"
  );

  const { batBuocCanNang, tuoiBatBuoc, batBuocKhongBHYT } = useMemo(() => {
    return parseBatBuocNhapCanNang(BAT_BUOC_NHAP_CAN_NANG);
  }, [BAT_BUOC_NHAP_CAN_NANG]);

  const thongTinChiTiet = useStore("khamBenh.thongTinChiTiet", {});
  const thongTinChiTietBanDau = useStore("khamBenh.thongTinChiTietBanDau", {});
  const infoNb = useStore("khamBenh.infoNb", {});
  const nbChanDoan = useStore("khamBenh.thongTinChiTiet.nbChanDoan", {});

  const trangThaiKham = useStore(
    "khamBenh.thongTinChiTiet.nbDvKyThuat.trangThai",
    {}
  );
  const detailDataThuocKhamLao = useStore("quanLyDieuTriLao.detailData", {});
  const phongThucHienId = useStore("nbKhamBenh.phongThucHienId", null);
  const { tienBhThanhToan, id: nbDotDieuTriId } = useStore(
    "khamBenh.thongTinKhamBN"
  );
  const preNbDvKhamId = usePrevious(thongTinChiTiet.id);
  const refPrevData = useRef({});

  const {
    dataHoSoChoSinh,
    refetch,
    mutateLienKetHoSoSinh,
    isLienKet,
    isHuyLienKet,
  } = useLienKetHoSoSinh();

  const {
    onSaveKetLuanKham,
    handleSetDataKetLuan,
    ketLuanKhamObject,
    setKetLuanKhamObject,
  } = useKetLuanKham({
    refDataKetLuan,
    refKetLuanKham,
    handleInitRef: (thongTinChiTiet, isClear) => {
      if (isClear) {
        refData.current = {};
        refDataKetLuan.current = {};
        refDataLoiDan.current = {};
      } else {
        refDataKetLuan.current = cloneDeep({
          ...refDataKetLuan.current,
          ...thongTinChiTiet,
        });
        refData.current = cloneDeep({ ...refData.current, ...thongTinChiTiet });
        refDataLoiDan.current = cloneDeep({
          ...thongTinChiTiet.nbKhamXet,
          ...thongTinChiTiet.nbKetLuan,
          ...refDataLoiDan.current,
        });
      }
      console.log({ logLength: null }, "handleInitRef", {
        refData: refData.current,
        refDataKetLuan: refDataKetLuan.current,
        refDataLoiDan: refDataLoiDan.current,
      });

      if (!isEqual(refPrevData.current.refData, refData.current)) {
        // update reactive
        refPrevData.current.refData = refData.current;
        setChiTietDvKham(refData.current);
      }
    },
  });

  useEffect(() => {
    Object.keys(refElement.current).forEach((key) => {
      refElement.current[key]?.reload && refElement.current[key].reload();
    });
    if (infoNb.id) {
      setState({ isEditDonThuoc: false });
      setIsEditted(false);
    }
  }, [infoNb.id]);

  const dsMaPhieuInKhiDongHS = useMemo(() => {
    if (
      dataTU_DONG_IN_KHI_DONG_HO_SO &&
      dataTU_DONG_IN_KHI_DONG_HO_SO.indexOf("/") > -1
    ) {
      //case tách thiết lập theo NB bảo hiểm và NB dịch vụ
      const _dsThietLap = dataTU_DONG_IN_KHI_DONG_HO_SO.split("/");
      if (infoNb?.doiTuong === DOI_TUONG.KHONG_BAO_HIEM && _dsThietLap[1]) {
        return _dsThietLap[1].split(",").map((item) => item.trim());
      } else if (infoNb?.doiTuong === DOI_TUONG.BAO_HIEM && _dsThietLap[0]) {
        return _dsThietLap[0].split(",").map((item) => item.trim());
      }
    } else {
      if (dataTU_DONG_IN_KHI_DONG_HO_SO) {
        return dataTU_DONG_IN_KHI_DONG_HO_SO
          .split(",")
          .map((item) => item.trim());
      }
    }

    return [];
  }, [dataTU_DONG_IN_KHI_DONG_HO_SO, infoNb?.doiTuong]);

  const daCoDichVu = useMemo(() => {
    return (
      dsDichVuChiDinhXN.length > 0 ||
      dsDichVuChiDinhKham.length > 0 ||
      dsDichVuChiDinhCls.length > 0
    );
  }, [dsDichVuChiDinhKham, dsDichVuChiDinhXN, dsDichVuChiDinhCls]);

  const [form] = Form.useForm();

  const isKsk = useMemo(() => {
    return infoNb?.khamSucKhoe || infoNb?.loaiDoiTuongKsk;
  }, [infoNb]);

  const isBADaiHan = useMemo(() => {
    if (thongTinBenhNhan?.maBenhAn) {
      const regexBADaiHan = /^([A-Za-z]{1}).*[0-9]{9}$/;

      return regexBADaiHan.test(thongTinBenhNhan?.maBenhAn);
    }

    return false;
  }, [thongTinBenhNhan?.maBenhAn]);

  useEffect(() => {
    thietLapTrangThai();
  }, []);

  useEffect(() => {
    onRegisterHotkey({
      layerId,
      hotKeys: [
        // {
        //   keyCode: 37,
        //   onEvent: () => {
        //     refFuncNavi.current &&
        //       refFuncNavi.current("previous", state.activeTab)();
        //   },
        // },
        // {
        //   keyCode: 39,
        //   onEvent: () => {
        //     refFuncNavi.current &&
        //       refFuncNavi.current("next", state.activeTab)();
        //   },
        // },
        {
          keyCode: HOTKEY.F9,
          onEvent: () => {
            if (state.activeTab === 1) {
              // NOTE: Nếu tab chỉ định dịch vụ thì in phiếu chỉ định
              refStepWrapper.current &&
                refStepWrapper.current.onInNhanhPhieuChiDinh();
            } else {
              // Nếu tab khác thì khi f9 sẽ focus vào tab chỉ định
              refFuncNavi.current && refFuncNavi.current("1");
            }
          },
        },
        {
          keyCode: HOTKEY.Q,
          ctrlKey: true,
          onEvent: () => {
            refStepWrapper.current &&
              refStepWrapper.current.onInNhanhPhieuKqCls();
          },
        },
        {
          keyCode: HOTKEY.F11,
          onEvent: () => {
            if (state.activeTab === 3) {
              // NOTE: Nếu tab Kết luận khám thì in phiếu kết luận
              refInKetLuanKham.current &&
                refInKetLuanKham.current.onPrintKetLuanKham();
            } else {
              // Nếu tab khác thì khi f11 sẽ focus vào tab Kết luận khám
              refFuncNavi.current && refFuncNavi.current("3");
            }
          },
        },
        {
          keyCode: HOTKEY.F12,
          onEvent: () => {
            if (state.activeTab === 4) {
              // NOTE: Nếu tab Đơn thuốc thì in tất cả đơn thuốc
              refStepWrapper.current &&
                refStepWrapper.current.onPrintDonThuoc();
            } else {
              // Nếu tab khác thì khi f12 sẽ focus vào tab đơn thuốc
              refFuncNavi.current && refFuncNavi.current("4");
            }
          },
        },
      ],
    });
    if (state.activeTab !== undefined) {
      setQueryStringValue("tab", state.activeTab);
    }
  }, [state.activeTab]);
  useEffect(() => {
    document.addEventListener("keydown", handleOnSave);
    return () => document.removeEventListener("keydown", handleOnSave);
  }, [
    state.activeTab,
    infoNb,
    thongTinChiTiet,
    dsDichVuChiDinhCls,
    dsDichVuChiDinhKham,
    dsDichVuChiDinhXN,
    ketLuanKhamObject.keyHuongDieuTri,
    ketLuanKhamObject.keyKetQua,
  ]);

  useEffect(() => {
    updateDataChiDinh({
      //nb nb chẩn đoán thay đổi thì cập nhật vào dataNb chiDinhKhamBenh
      dataNb: nbChanDoan,
    });
  }, [nbChanDoan]);

  useEffect(() => {
    if (preNbDvKhamId && preNbDvKhamId != thongTinChiTiet.id) {
      setTimeout(() => {
        setState({ activeTab: 0 });
      }, 500);
    }
  }, [thongTinChiTiet, preNbDvKhamId]);

  useEffect(() => {
    if (
      thongTinChiTiet.id &&
      thongTinChiTiet.nbDotDieuTriId == infoNb.id &&
      phongThucHienId
    ) {
      updateConfigData({
        configData: {
          nbDotDieuTriId: infoNb.id,
          nbThongTinId: infoNb.nbThongTinId,
          chiDinhTuDichVuId: thongTinChiTiet.id,
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
          dsChiDinhTuLoaiDichVu: [LOAI_DICH_VU.KHAM],
          khoaChiDinhId:
            listPhongKham.find((x) => x.id == phongThucHienId)?.khoaId ||
            thongTinChiTiet.nbDichVu?.khoaChiDinhId,
          thongTinNguoiBenh: infoNb,
          trangThaiKham: thongTinChiTiet?.nbDvKyThuat?.trangThai,
          phongThucHienId: thongTinChiTiet.nbDvKyThuat?.phongThucHienId,
          isNgoaiTru: true,
          isKhamSucKhoe: isKsk,
          doiTuongKcb: infoNb.doiTuongKcb,
          maBenhId: thongTinChiTiet?.nbChanDoan?.dsCdChinhId?.[0],
          canLamSang: false,
          thoiGianThucHien: moment().format(),
          dsTacNhanDiUngId: thongTinChiTiet?.nbHoiBenh?.dsTacNhanDiUngId,
        },
      });
    }
  }, [thongTinChiTiet, infoNb, listPhongKham, phongThucHienId, isKsk]);

  useEffect(() => {
    if (thongTinChiTiet.id && thongTinChiTiet.nbDotDieuTriId == infoNb.id) {
      updateDataVitalSigns({
        configData: {
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
          chiDinhTuDichVuId: thongTinChiTiet.id,
          dsChiDinhTuLoaiDichVu: [LOAI_DICH_VU.KHAM, LOAI_DICH_VU.TIEP_DON],
        },
      });
    }
  }, [thongTinChiTiet, infoNb]);

  const onSetActiveSaveBtn = () => {
    if (state.activeTab == 0) {
      setIsEditted(true);
    }
  };
  refActiveSaveBtn.current = onSetActiveSaveBtn;

  const handleOnSave = async (e) => {
    const { ctrlKey, metaKey, which, value } = e;
    if (
      (ctrlKey || metaKey) &&
      String.fromCharCode(which).toLowerCase() === "s"
    ) {
      e.preventDefault();
      if (
        daCoDichVu &&
        !refData?.current?.nbChanDoan?.cdSoBo &&
        refData?.current?.nbChanDoan?.dsCdChinhId?.length === 0
      ) {
        message.error(t("khamBenh.thongBaoKhongTheXoaChanDoanKhiDaKeDichVu"));
        // refData.current.nbChanDoan.cdSoBo = thongTinChiTiet.nbChanDoan.cdSoBo
        // var text = document.createTextNode(`${thongTinChiTiet.nbChanDoan.cdSoBo}`);
        // document.getElementById("chan-doan-so-bo").innerHTML = thongTinChiTiet.nbChanDoan.cdSoBo
      } else if (
        !(await checkKhamKLKhacBacSi(
          thongTinChiTiet,
          nhanVienId,
          KHONG_DUOC_KHAM_KL_NB_KHAC_BSKHAM
        ))
      ) {
        message.error(
          t("khamBenh.khongChoPhepThucHienKhamKetLuan", {
            bacSiKham: thongTinChiTiet?.bacSiKham?.ten,
            bacSiHienTai: full_name,
          })
        );
      } else {
        saveDataByKey();
      }
    }
  };

  const isValidate = () => {
    if (!refData.current) return false;
    const { nbChiSoSong } = refData.current;
    if (batBuocCanNang && !isNumber(nbChiSoSong?.canNang)) {
      message.error(t("khamBenh.batBuocNhapCanNangVoiNb"));
      return true;
    }

    if (!batBuocKhongBHYT && infoNb?.doiTuong === 1) return false;
    if (!isNumber(nbChiSoSong?.canNang)) {
      if (!infoNb?.tuoi && infoNb?.thangTuoi <= 12) {
        message.error(t("khamBenh.batBuocNhapCanNangVoiNbBHYT01Tuoi"));
        return true;
      }
      if (tuoiBatBuoc > (infoNb?.tuoi || 0)) {
        message.error(t("khamBenh.batBuocNhapCanNangVoiNb"));
        return true;
      }
    }
    return false;
  };

  const isValidCss = (arr, isEdit) => {
    const errors = [];
    arr.forEach((cur) => {
      const { ngaySinh, gioiTinh } = infoNb;
      let listData = isArray(listThietLapGiaTriCSS, true)
        ? listThietLapGiaTriCSS.filter((i) => i.active)
        : null;
      Object.keys(cur).forEach((key) => {
        const value = cur[key];
        if (isArray(listData, true)) {
          let thietLap = listData.find((i) => {
            let tuoi = ngaySinh ? moment().diff(ngaySinh, "years") : -1;
            return (
              i.tenCss === key &&
              i.gioiTinh === gioiTinh &&
              tuoi <= i.denTuoi &&
              tuoi >= i.tuTuoi
            );
          });
          if (thietLap && value) {
            const {
              giaTriToiDa,
              giaTriToiThieu,
              giaTriVuotNguongToiDa,
              giaTriVuotNguongToiThieu,
            } = thietLap || {};
            if (giaTriVuotNguongToiDa && value > giaTriVuotNguongToiDa) {
              errors.push({
                tenCss: OBJ_LIST_HARDCODE_CSS[key],
                giaTriVuotNguongToiDa,
                giaTri: value,
              });
            } else if (
              giaTriVuotNguongToiThieu &&
              value < giaTriVuotNguongToiThieu
            ) {
              errors.push({
                tenCss: OBJ_LIST_HARDCODE_CSS[key],
                giaTriVuotNguongToiThieu,
                giaTri: value,
              });
            } else if (value > giaTriToiDa || value < giaTriToiThieu) {
              errors.push({
                tenCss: OBJ_LIST_HARDCODE_CSS[key],
                giaTriToiThieu,
                giaTriToiDa,
                thoiGianThucHien: cur.thoiGianThucHien
                  ? moment(cur.thoiGianThucHien).format("HH:mm DD/MM/YYYY")
                  : "",
                giaTri: value,
              });
            }
          }
        }
      });
    }, []);
    if (errors?.length) {
      return (
        <div style={{ textAlign: "left", marginLeft: 50 }}>
          {errors.map((css) => (
            <div key={css.tenCss}>
              <span
                className={`${
                  css.giaTriVuotNguongToiThieu || css.giaTriVuotNguongToiDa
                    ? "text-danger"
                    : ""
                }`}
              >
                <b>
                  {css.tenCss}: {css.giaTri || 0}&nbsp;
                </b>
              </span>
              {css.giaTriVuotNguongToiThieu && (
                <span>
                  {t(
                    "sinhHieu.nhapNgoaiGiaTriVuotNguongToiThieu"
                  ).toLowerCase()}
                  &nbsp;
                  <b>&lt; {css.giaTriVuotNguongToiThieu}</b>
                </span>
              )}
              {css.giaTriVuotNguongToiDa && (
                <span>
                  {t("sinhHieu.nhapNgoaiGiaTriVuotNguongToiDa").toLowerCase()}
                  &nbsp;
                  <b>&gt; {css.giaTriVuotNguongToiDa}</b>
                </span>
              )}
              {css.giaTriToiThieu && css.giaTriToiDa && (
                <>
                  {t("sinhHieu.nhapNgoaiGiaTriThamChieu")}&nbsp;
                  <span>
                    <b>{css.giaTriToiThieu}</b>
                  </span>
                  &nbsp; - <b>{css.giaTriToiDa}</b>
                </>
              )}
            </div>
          ))}
          <div
            className="text-warning text-center italic"
            style={{ marginTop: 16 }}
          >
            {t("common.vuiLongKiemTraLai")}!
          </div>
        </div>
      );
    } else {
      return null;
    }
  };

  const onSaveDichVuKham = (isShowMessage = true) => {
    const isValidateResult = isValidate();
    refConfirmSaveShowing.current = false;
    if (isValidateResult) return null;
    return new Promise(async (resolve, reject) => {
      //case chọn mẫu kết quả có update lý do đến khám
      //=> call api update lý do đến khám
      if (infoNb?.isNeedUpdateLyDoDenKham) {
        await updateLyDoDenKham({
          id: infoNb.id,
          lyDoDenKham: infoNb?.lyDoDenKham,
        });
      }

      const {
        nbChanDoan,
        nbHoiBenh,
        nbKhamXet,
        nbKSK,
        nbChiSoSong,
        nbThongTinMat,
        nbChiTietKhamTmh,
        nbThongTinCmu,
        nbThongTinRhm,
        nbThongTinDaLieu,
        nbThongTinKhamNgoai,
        nbThongTinKhamSan,
        nbTomTatCls,
        nbThongTinChuyenKhoaNam,
        dsChuyenKhoaId,
        nbThongTinKhamIvf,
        tuanThai,
        choConBu,
      } = refData.current;

      const updateThongTin = async () => {
        if (isKsk) {
          const { trangThaiMoSanPhu, ...rest } = nbKSK;
          //trangThaiMoSanPhu là biến tự thêm vào state để check xem người dùng có chọn mổ sản phụ khoa hay không , trangThaiMoSanPhu = 1 thì người dùng chọn mổ sản phụ khoa

          if (trangThaiMoSanPhu === 1 && !nbKSK.soLanMoSanPhu) {
            message.error(t("khamBenh.khamSan.vuiLongNhapSoLanMoSanPhu"));
            reject(false);
            return;
          }

          const binhThuongFields = [
            "tuanHoanBinhThuong",
            "hoHapBinhThuong",
            "tieuHoaBinhThuong",
            "thanTietLieuBinhThuong",
            "noiTietBinhThuong",
            "coXuongKhopBinhThuong",
            "thanKinhBinhThuong",
            "tamThanBinhThuong",
            "ngoaiKhoaBinhThuong",
            "khamBaoQuyDauBinhThuong",
            "daLieuBinhThuong",
            "sanPhuKhoaBinhThuong",
            "tuyenVuBinhThuong",
            "taiMuiHongBinhThuong",
            "rangHamMatBinhThuong",
          ];

          let binhThuongValues = {};
          if (
            dataMAC_DINH_TICK_BINH_THUONG_KHAM_LS_KSK?.eval() &&
            trangThaiKham != TRANG_THAI_DICH_VU.DA_KET_LUAN
          ) {
            binhThuongFields.forEach((field) => {
              if (isNil(nbKSK?.[field])) {
                binhThuongValues[field] = true;
              } else {
                binhThuongValues[field] = nbKSK?.[field];
              }
            });
          }

          updateKhamKSK({
            nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
            ...rest,
            ...binhThuongValues,
          });
        }
        if (nbThongTinMat) {
          const { matPhaiTtChung, matTraiTtChung, ...rest } = nbThongTinMat;
          let params = {
            id: thongTinChiTiet?.id,
            ...rest,
            matPhaiTtChung: {
              ...matPhaiTtChung,
              ...(matPhaiTtChung.hasOwnProperty("dsCdChinhId") && {
                dsCdChinhId: matPhaiTtChung.dsCdChinhId
                  ? !isArray(matPhaiTtChung.dsCdChinhId, true)
                    ? [matPhaiTtChung.dsCdChinhId]
                    : matPhaiTtChung.dsCdChinhId
                  : null,
              }),
            },
            matTraiTtChung: {
              ...matTraiTtChung,
              ...(matTraiTtChung.hasOwnProperty("dsCdChinhId") && {
                dsCdChinhId: matTraiTtChung.dsCdChinhId
                  ? !isArray(matTraiTtChung.dsCdChinhId, true)
                    ? [matTraiTtChung.dsCdChinhId]
                    : matTraiTtChung.dsCdChinhId
                  : null,
              }),
            },
          };
          updateThongTinChungMat(params);
        }
        if (nbChiTietKhamTmh) {
          updateNbKhamChuyenKhoaTmh({
            id: thongTinChiTiet?.id,
            ...nbChiTietKhamTmh,
          });
        }

        if (nbThongTinCmu) {
          updateNbKhamChuyenKhoaCmu({
            id: thongTinChiTiet?.id,
            ...nbThongTinCmu,
          });
        }

        if (nbThongTinKhamSan) {
          // if (!nbThongTinKhamSan.mangThaiLan || !nbThongTinKhamSan.soLuongThai) {
          //   message.error(t("khamBenh.khamSan.vuiLongNhapMangThaiLanHoacSoThai"));
          //   reject(false);
          //   return;
          // }
          updateNbKhamChuyenKhoaSan({
            id: thongTinChiTiet?.id,
            ...nbThongTinKhamSan,
          });
        }

        if (nbThongTinRhm) {
          updateNbKhamChuyenKhoaRhm({
            id: thongTinChiTiet?.id,
            ...nbThongTinRhm,
          });
        }
        if (nbThongTinDaLieu) {
          updateNbKhamChuyenKhoaDaLieu({
            id: thongTinChiTiet?.id,
            ...nbThongTinDaLieu,
          });
        }
        if (nbThongTinKhamNgoai) {
          updateNbKhamChuyenKhoaKhamNgoai({
            id: thongTinChiTiet?.id,
            ...nbThongTinKhamNgoai,
          });
        }
        if (nbThongTinChuyenKhoaNam) {
          updateNbKhamChuyenKhoaKhamNam({
            id: thongTinChiTiet?.id,
            ...nbThongTinChuyenKhoaNam,
          });
        }
        if (nbThongTinKhamIvf) {
          updateNbKhamChuyenKhoaIVF({
            id: thongTinChiTiet?.id,
            ...nbThongTinKhamIvf,
          });
        }
        if (!nbChanDoan?.cdSoBo && XML130_TRUONG_BAT_BUOC?.eval()) {
          message.error(t("khamBenh.vuiLongNhapChanDoanSoBo"));
          reject(false);
          return;
        }
        if (
          infoNb?.doiTuong === DOI_TUONG.BAO_HIEM &&
          !isArray(nbChanDoan?.dsCdChinhId, 1) &&
          XML130_TRUONG_BAT_BUOC?.eval()
        ) {
          message.error(t("khamBenh.vuiLongNhapChanDoanBenh"));
          reject(false);
          return;
        }
        if (
          daCoDichVu &&
          !nbChanDoan?.cdSoBo &&
          !nbChanDoan?.dsCdChinhId?.length
        ) {
          message.error(t("khamBenh.thongBaoKhongTheXoaChanDoanKhiDaKeDichVu"));
          reject(false);
        } else {
          const dataSubmit = {
            id: thongTinChiTiet?.id,
            tuanThai,
            choConBu,
            nbChanDoan,
            dsChuyenKhoaId:
              !!dsChuyenKhoaId && !isArray(dsChuyenKhoaId)
                ? [dsChuyenKhoaId]
                : dsChuyenKhoaId,
            nbHoiBenh,
            nbKhamXet,
            nbTomTatCls,
            nbChiSoSong: {
              ...nbChiSoSong,
              id:
                thongTinChiTiet?.id === nbChiSoSong?.chiDinhTuDichVuId
                  ? nbChiSoSong?.id
                  : null,
              nguoiThucHienId:
                thongTinChiTiet?.id === nbChiSoSong?.chiDinhTuDichVuId
                  ? nbChiSoSong.nguoiThucHienId
                  : null,
              thoiGianThucHien: moment(),
              chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
              chiDinhTuDichVuId: thongTinChiTiet?.id,
              khoaChiDinhId:
                nbChiSoSong?.khoaChiDinhId ??
                thongTinChiTiet?.nbDvKyThuat?.phongThucHien?.khoaId,
            },
            isShowMessage, // trường để cho phép show message thành công hay không
          };

          const [e, s] = await toSafePromise(
            nbDvKhamProvider.getById(thongTinChiTiet.id)
          );
          if (e) {
            showError(e?.message);
            reject(e);
            return;
          }
          let finalJson = null;

          if (dataKHAM_BENH_CANH_BAO_THAY_DOI_DU_LIEU_KHI_LUU?.eval()) {
            const ignoredFields = [
              "nbDichVu.*",
              "nbDvKyThuat.*",
              "nbChanDoan.dsCdChinh",
              "nbChanDoan.dsCdKemTheo",
              "nbChiSoSong.thoiGianThucHien",
              "isShowMessage",
            ];
            const fieldMappings = {
              "nbChiSoSong.thoiGianThucHien": "Thời gian thực hiện chỉ số sống",
              "nbChanDoan.cdSoBo": t("khamBenh.chanDoan.chanDoanSoBo"),
              "nbChanDoan.dsCdChinhId": t("khamBenh.chanDoan.chanDoanBenh"),
              "nbChanDoan.dsCdKemTheoId": t(
                "khamBenh.chanDoan.chanDoanKemTheo"
              ),
              "nbChanDoan.moTa": t("khamBenh.chanDoan.moTaChiTiet"),
              "nbChanDoan.moTaKemTheo": t("khamBenh.chanDoan.moTaBenhKemTheo"),
              dsChuyenKhoaId: t("khamBenh.chanDoan.chuyenKhoaKham"),
              "nbHoiBenh.quaTrinhBenhLy": t("khamBenh.hoiBenh.quaTrinhBenhLy"),
              "nbHoiBenh.tienSuBanThan": t("khamBenh.hoiBenh.banThan"),
              "nbHoiBenh.tienSuGiaDinh": t("khamBenh.hoiBenh.giaDinh"),
              "nbHoiBenh.diUngThuoc": t("khamBenh.hoiBenh.diUngThuoc"),
              "nbKhamXet.toanThan": t("khamBenh.khamXet.toanThan"),
              "nbKhamXet.cacBoPhan": t("khamBenh.khamXet.cacBoPhan"),
              "nbKhamXet.ghiChu": t("khamBenh.khamXet.luuY"),
              "nbKhamXet.boSung": t("khamBenh.khamXet.khac"),
              nbTomTatCls: `${t("khamBenh.tomTatKetQuaCls")} - ${t(
                "khamBenh.tomTatKetQuaCls"
              )}`,
              "nbTomTatCls.congThucMau": `${t(
                "khamBenh.tomTatKetQuaCls"
              )} - ${t("khamBenh.khamSucKhoe.congThucMau")}`,
              "nbTomTatCls.sinhHoaMau": `${t("khamBenh.tomTatKetQuaCls")} - ${t(
                "khamBenh.sinhHoaMau"
              )}`,
              "nbTomTatCls.giaiPhauBenh": `${t(
                "khamBenh.tomTatKetQuaCls"
              )} - ${t("xetNghiem.giaiPhauBenh")}`,
              "nbTomTatCls.viSinh": `${t("khamBenh.tomTatKetQuaCls")} - ${t(
                "baoCao.viSinh"
              )}`,
              "nbTomTatCls.xetNghiemKhac": `${t(
                "khamBenh.tomTatKetQuaCls"
              )} - ${t("khamBenh.xetNghiemKhac")}`,
              "nbTomTatCls.cdhaTdcn": `${t("khamBenh.tomTatKetQuaCls")} - ${t(
                "khamBenh.chanDoanHinhAnhSieuAmXquang"
              )}`,
              "nbTomTatCls.khac": `${t("khamBenh.tomTatKetQuaCls")} - ${t(
                "khamBenh.khamXet.khac"
              )}`,
              "nbChiSoSong.phanLoaiTheLuc": t("khamBenh.chanDoan.plTheLuc"),
              "nbChiSoSong.mach": t("khamBenh.chanDoan.mach"),
              "nbChiSoSong.nhietDo": t("khamBenh.chanDoan.nhietDo"),
              "nbChiSoSong.huyetApTamThu": t("sinhHieu.huyetApTamThu"),
              "nbChiSoSong.huyetApTamTruong": t("sinhHieu.huyetApTamTruong"),
              "nbChiSoSong.nhipTho": t("khamBenh.chanDoan.nhipTho"),
              "nbChiSoSong.canNang": t("khamBenh.chanDoan.canNang"),
              "nbChiSoSong.chieuCao": t("khamBenh.chanDoan.chieuCao"),
              "nbChiSoSong.spo2": t("khamBenh.chanDoan.spo2"),
              "nbChiSoSong.nhomMau": t("khamBenh.chanDoan.nhomMau"),
              "nbChanDoan.dsCdChinhId[0]": t("khamBenh.chanDoan.chanDoanBenh"),
              "nbChanDoan.theBenhLao": t("khamBenh.chanDoan.theBenh"),
            };
            // 1. So sánh local data với original data để xem những gì đã thay đổi
            const localChanges = compareEditedWithOriginal(
              dataSubmit,
              thongTinChiTietBanDau,
              ignoredFields
            );
            // 2. So sánh local data với server data để tìm conflicts
            const conflicts = compareEditedWithServer(
              dataSubmit,
              s.data,
              thongTinChiTietBanDau,
              localChanges,
              ignoredFields
            );
            // Chỉ hiển thị modal nếu có conflicts thực sự (cả client và server đều sửa và giá trị khác nhau)
            const hasRealConflicts = conflicts.some((conflict) => {
              const originalValue = getValueByPath(
                thongTinChiTietBanDau,
                conflict.path
              );
              const clientValue = getValueByPath(dataSubmit, conflict.path);
              const serverValue = getValueByPath(s.data, conflict.path);

              // Chỉ là conflict thực sự khi cả client và server đều sửa và giá trị khác nhau
              return (
                !isEqual(clientValue, originalValue) &&
                !isEqual(serverValue, originalValue) &&
                !isEqual(clientValue, serverValue)
              );
            });

            if (hasRealConflicts) {
              finalJson = await centralizedErrorHandling(
                new Promise((resolve, reject) => {
                  hideLoading();
                  conflictModalRef.current.show(
                    {
                      original: thongTinChiTietBanDau,
                      current: dataSubmit,
                      server: s?.data,
                      fieldMappings: fieldMappings,
                      ignore: ignoredFields,
                      renderValue: (key, value) => {
                        switch (key) {
                          case "nbChanDoan.dsCdChinhId":
                          case "nbChanDoan.dsCdKemTheoId":
                            return {
                              key,
                              value: value ? (
                                <SelectChanDoan
                                  className="input-filter"
                                  style={{ width: "100%" }}
                                  mode="multiple"
                                  value={
                                    value?.map?.((item) => item + "") ?? value
                                  }
                                  allowClear
                                  readonly
                                />
                              ) : null,
                            };
                          case "nbChanDoan.theBenhLao":
                            return {
                              key,
                              value: value ? (
                                <Select
                                  data={listTheBenhLao}
                                  style={{ width: "100%" }}
                                  value={value}
                                />
                              ) : null,
                            };
                            break;
                          default:
                            return { key: false, value: value };
                        }
                      },
                    },
                    (success, resolvedJson) => {
                      if (success) resolve(resolvedJson);
                      else reject();
                    }
                  );
                })
              );
              if (!finalJson) {
                reject();
                return;
              } else {
                showLoading();
              }
            } else {
              // Nếu không có conflicts thực sự, merge dataSubmit với server data
              finalJson = mergeServerData(
                dataSubmit,
                s?.data,
                thongTinChiTietBanDau,
                localChanges
              );
            }
          } else {
            finalJson = dataSubmit;
          }

          updateNbDvKham(finalJson)
            .then((s) => {
              if (Object.keys(refElement.current).includes("useMauKetQua")) {
                refElement.current["useMauKetQua"].reload(false);
              }

              if ([8332, 8340].includes(s?.code)) {
                reject(false);
                let msgContent = s?.message;
                if (s?.code === 8340) {
                  msgContent += ` .${t(
                    "khamBenh.deTiepTucThucHienKhamBenhVuiLongChonDongY"
                  )}`;
                }
                showConfirm(
                  {
                    title: t("common.thongBao"),
                    content: msgContent,
                    cancelText:
                      s?.code === 8332 ? t("common.dong") : t("common.huy"),
                    okText: t("common.dongY"),
                    classNameOkText: "button-warning",
                    showBtnOk: s?.code !== 8332, //ẩn với lỗi vượt quá số lượng người bệnh khám tối đa 1 trong ngày, theo thiết lập SL_NB_KHAM_TOI_DA_1_NGAY
                  },
                  () => {
                    updateNbDvKham({ ...dataSubmit, boQuaSlKhamToiDa: true })
                      .then((s) => {
                        if ([8332, 8340].includes(s?.code)) {
                          message.error(s?.message);
                        }
                        showPopupChiDinhPhacDo(dataSubmit);
                        resolve(true);
                      })
                      .catch((e) => {
                        reject(false);
                      });
                  }
                );
              } else {
                resolve(true);
                showPopupChiDinhPhacDo(dataSubmit);
              }
            })
            .catch((e) => {
              reject(false);
            });
        }
      };
      const validCSS = isValidCss([nbChiSoSong]);
      if (validCSS) {
        resolve(true);
        showConfirm(
          {
            title: t("common.xacNhan"),
            content: validCSS,
            cancelText: t("common.huy"),
            okText: t("common.xacNhanNhap"),
            showImg: true,
            showBtnOk: true,
            isContentElement: true,
          },
          async () => {
            await updateThongTin();
          }
        );
      } else {
        await updateThongTin();
      }
    });
  };
  const showPopupChiDinhPhacDo = (data) => {
    onSearchPhacDo({
      maBenhId: data?.nbChanDoan?.dsCdChinhId[0],
      active: true,
    }).then((s) => {
      if (
        s?.length &&
        refData?.current?.nbChanDoan?.dsCdChinhId[0] &&
        ((!nbChanDoan?.dsCdChinhId?.length &&
          GOI_Y_PHAC_DO_DVKHAM_LUU_CDCHINH.eval()) ||
          (nbChanDoan?.dsCdChinhId?.length &&
            GOI_Y_PHAC_DO_DVKHAM_SUA_CDCHINH.eval() &&
            refData?.current?.nbChanDoan?.dsCdChinhId[0] !=
              nbChanDoan?.dsCdChinhId[0]))
      ) {
        refChiDinhPhacDo.current &&
          refChiDinhPhacDo.current.show(
            {
              maBenhId:
                data?.nbChanDoan?.dsCdChinhId[0] &&
                Number(data?.nbChanDoan?.dsCdChinhId[0]),
              nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
              chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
              chiDinhTuDichVuId: data?.id,
              khoaChiDinhId: thongTinChiTiet.nbDichVu?.khoaChiDinhId,
            },
            () => {
              onChangeInputSearch({
                nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
              });
            }
          );
      }
    });
  };
  const saveDataByKey = throttle(
    async (isShowMessage = true) => {
      console.log("saveDataByKey", {
        refData: refData.current,
        refDataKetLuan: refDataKetLuan.current,
        refDataLoiDan: refDataLoiDan.current,
      });

      showLoading();
      try {
        const key = state.activeTab;
        switch (key) {
          case 0:
            await onSaveDichVuKham(isShowMessage);
            break;
          case 3: {
            await onSaveKetLuanKham({});
            break;
          }
          case 4: {
            await onSaveDonThuoc();
            break;
          }
          case 8: {
            refBtnSaveDieuTriLao.current &&
              refBtnSaveDieuTriLao.current.click();
            break;
          }
          default:
            break;
        }
      } catch (err) {
        return Promise.reject(err);
      } finally {
        hideLoading();
      }
    },
    2000,
    {
      leading: true,
      trailing: false,
    }
  );
  const onActiveTab = (key) => {
    console.log("onActiveTab", {
      refData: refData.current,
      refDataKetLuan: refDataKetLuan.current,
      refDataLoiDan: refDataLoiDan.current,
      thongTinChiTietBanDau,
    });
    let compared = true;
    // let comparedNbCovid = true;
    const newKey = +key;
    const currentKey = state.activeTab;
    const dataCompare = thongTinChiTietBanDau;
    if (currentKey == 0 && newKey !== 0) {
      compared = isEqual(dataCompare, refData.current);
    } else if (currentKey == 3 && newKey !== 3) {
      compared = isEqual(dataCompare, refDataKetLuan.current);
    } else if (currentKey == 4 && newKey !== 4) {
      compared = !state.isEditDonThuoc;
    }
    if (
      [1, 4].includes(newKey) &&
      thongTinChiTiet?.nbDvKyThuat?.ngoaiVienId?.id
    ) {
      showConfirm({
        title: t("common.thongBao"),
        content: t("khamBenh.chiDinh.dichVuKhamCuaNbDuocChiDinhTuBve"),
        cancelText: t("common.dong"),
        classNameOkText: "button-warning",
        typeModal: "warning",
      });
      return;
    }
    if (!compared) {
      if (refConfirmSaveShowing.current) return;
      const isValidateResult = isValidate();
      if (isValidateResult) {
        setState({ activeTab: 0 });
        refConfirmSaveShowing.current = false;
        return null;
      }
      if (
        !refData.current?.nbChanDoan?.dsCdChinhId?.length &&
        !refData.current?.nbChanDoan?.cdSoBo
      ) {
        return null;
      }
      refConfirmSaveShowing.current = true;
      if (currentKey === 0) {
        onSaveDichVuKham()
          .then((s) => {
            setState({ activeTab: newKey });
            setIsEditted(false);

            refConfirmSaveShowing.current = false;
          })
          .catch((e) => {
            setState({ activeTab: 0 });
            refConfirmSaveShowing.current = false;
          });
      }
      if (currentKey === 3) {
        //Bỏ tự động lưu cập nhật khi người bệnh đã đóng hồ sơ
        if (trangThaiKham === TRANG_THAI_DICH_VU.DA_KET_LUAN) {
          setState({ activeTab: newKey });
          refConfirmSaveShowing.current = false;
          return;
        } else {
          onSaveKetLuanKham({})
            .then((s) => {
              setState({ activeTab: newKey });
              setIsEditted(false);

              refConfirmSaveShowing.current = false;
            })
            .catch((e) => {
              if (
                [
                  HUONG_DIEU_TRI_KHAM.CHO_VE,
                  HUONG_DIEU_TRI_KHAM.HEN_KHAM,
                ].includes(ketLuanKhamObject.keyHuongDieuTri) &&
                e?.code == 8322
              ) {
                setState({ activeTab: newKey });
              } else {
                setState({ activeTab: 3 });
              }
              refConfirmSaveShowing.current = false;
            });
        }
        return;
      }
      if (currentKey === 4) {
        onSaveDonThuoc()
          .then(() => {
            setState({ activeTab: newKey });
            setIsEditted(false);

            refConfirmSaveShowing.current = false;
          })
          .catch((e) => {
            setState({ activeTab: 4 });
            refConfirmSaveShowing.current = false;
          });
      }
    } else if (
      refData.current?.nbChanDoan?.cdSoBo ||
      refData.current?.nbChanDoan?.dsCdChinhId?.length ||
      newKey == "0"
    ) {
      setState({ activeTab: newKey });
    }
  };

  const handleSetData = (arrKey) => (e) => {
    let isEditted = true;
    const value = e?.currentTarget ? e.currentTarget.innerHTML : e;
    const [key1, key2] = arrKey;
    if (key2) {
      refData.current = {
        ...refData.current,
        [key1]: refData.current[key1]
          ? {
              ...refData.current[key1],
              [key2]: value,
            }
          : { [key2]: value },
      };
    } else {
      refData.current = {
        ...refData.current,
        [key1]: value,
      };
    }
    if (
      refData.current?.nbChiSoSong?.huyetApTamTruong >
      refData.current?.nbChiSoSong?.huyetApTamThu
    ) {
      isEditted = false;
    }

    //nếu update trường lưu ý trong khám xét => update field ghi chú trong lời dặn
    if (key1 == "nbKhamXet" && key2 == "ghiChu") {
      refDataLoiDan.current["ghiChu"] = value;
    }
    setChiTietDvKham(refData.current);
    console.log("handleSetData", {
      refData: refData.current,
      refDataKetLuan: refDataKetLuan.current,
      refDataLoiDan: refDataLoiDan.current,
    });
    setIsEditted(isEditted);
  };
  const handleGetData = (key) => {
    if (key) return { ...refData.current?.[key] };
    else return { ...refData.current };
  };

  const handleSetDataLoiDan = (key) => (e) => {
    const value = e?.currentTarget ? e.currentTarget.innerHTML : e;
    refDataLoiDan.current = {
      ...refDataLoiDan.current,
      [key]: value,
    };
    setState({ isEditDonThuoc: true });
  };
  const [showConfirmKhamVeBoDon] = useKhamVeBoDon({
    handleSetData: handleSetDataLoiDan,
  });

  const validateKetLuan = async (values) => {
    try {
      if (
        !refData?.current?.nbChanDoan?.dsCdChinhId ||
        refData?.current?.nbChanDoan?.dsCdChinhId?.length === 0
      ) {
        //False: Khi nhấn kết thúc khám với Hướng điều trị<> Nhập viện hoặc bất kỳ hành động nào liên quan tới ràng buộc Chẩn đoán bệnh --> Đều bỏ qua không bắt buộc phải điền chẩn đoán bệnh
        if (
          !(
            infoNb?.doiTuong === DOI_TUONG.KHONG_BAO_HIEM &&
            !BAT_BUOC_CD_BENH_DOI_TUONG_KHONG_BH?.eval() &&
            values?.keyHuongDieuTri != HUONG_DIEU_TRI_KHAM.NHAP_VIEN
          )
        ) {
          if (
            BAT_BUOC_CD_BENH_DOI_TUONG_KHONG_BH &&
            values?.keyHuongDieuTri != HUONG_DIEU_TRI_KHAM.KHONG_KHAM
          ) {
            message.error(t("khamBenh.canhBaoNhapChanDoanTruocKhiKetThucKham"));
            return Promise.reject();
          }
        }
      }
      // NB BHYT có tienBhThanhToan bắt buộc phải điền diễn biến
      if (
        tienBhThanhToan > 0 &&
        !refData?.current?.nbKhamXet?.dienBien &&
        BAT_BUOC_NHAP_DIEN_BIEN_NGOAI_TRU?.eval()
      ) {
        message.error(t("khamBenh.vuiLongNhapTruongDienBienOMucKhamXet"));
        return Promise.reject();
      }

      if (
        BAT_BUOC_THOI_GIAN_DUNG_THUOC_NGOAI_TRU?.eval() &&
        !(refDataLoiDan.current?.tuNgay && refDataLoiDan.current?.denNgay) &&
        [...listDvThuoc, ...listDvThuocKeNgoai, ...listDvThuocNhaThuoc]?.length
      ) {
        message.error(t("khamBenh.vuiLongNhapThoiGianTuNgayDenNgayChoDon"));
        return Promise.reject();
      }

      if (
        [...listDvThuoc, ...listDvThuocKeNgoai, ...listDvThuocNhaThuoc]
          ?.length &&
        GIOI_HAN_SO_NGAY_CHO_THUOC_NGOAI_TRU &&
        refDataLoiDan.current?.soNgayChoDon &&
        Number(refDataLoiDan.current?.soNgayChoDon) >
          Number(GIOI_HAN_SO_NGAY_CHO_THUOC_NGOAI_TRU)
      ) {
        message.error(
          t("khamBenh.vuiLongNhapSoNgayChoDon", {
            num: GIOI_HAN_SO_NGAY_CHO_THUOC_NGOAI_TRU,
          })
        );
        return Promise.reject();
      }

      if (
        [...listDvThuoc, ...listDvThuocKeNgoai, ...listDvThuocNhaThuoc]
          ?.length &&
        moment(refDataLoiDan.current.tuNgay).isBefore(
          moment(infoNb.thoiGianVaoVien).startOf("days")
        )
      ) {
        message.error(t("quanLyNoiTru.vuiLongNhapTuNgayLonHonNgayVaoVien"));
        return Promise.reject();
      }

      // Chặn không cho nhập viện nếu lượt khám có gắn mã bệnh án dài hạn

      if (
        !(await checkKhamKLKhacBacSi(
          thongTinChiTiet,
          nhanVienId,
          KHONG_DUOC_KHAM_KL_NB_KHAC_BSKHAM
        ))
      ) {
        message.error(
          t("khamBenh.khongChoPhepThucHienKhamKetLuan", {
            bacSiKham: thongTinChiTiet?.bacSiKham?.ten,
            bacSiHienTai: full_name,
          })
        );
        return Promise.reject();
      }
      return values;
    } catch (error) {
      return Promise.reject(error);
    }
  };

  const onKetLuanKham = throttle(
    async ({ isChuyenKhoa = false, isKetThucKham = false, ...formValues }) => {
      try {
        const values =
          isChuyenKhoa || isKetThucKham ? ketLuanKhamObject : formValues;
        const [err] = await toSafePromise(validateKetLuan(values));
        if (err !== null) {
          if (isChuyenKhoa)
            throw new DetailedError({
              code: "validateKetLuan",
              message: err?.message,
              context: err,
            });
          return;
        }

        const [errSaveDataByKey] = await toSafePromise(saveDataByKey(false));
        if (errSaveDataByKey !== null) {
          if (isChuyenKhoa)
            throw new DetailedError({
              code: "saveDataByKey",
              message: errSaveDataByKey?.message,
              context: errSaveDataByKey,
            });
          throw errSaveDataByKey;
        }

        const { keyHuongDieuTri, keyKetQua, ghiChu, thongTinTheoDoi } = values;
        refKetLuanKham.current = { keyHuongDieuTri, keyKetQua };

        if (
          [
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC, // 5
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC, // 8
          ].includes(infoNb?.doiTuongKcb) &&
          !thongTinBenhNhan?.maBenhAn
        ) {
          await huyDieuTriNgoaiTru(thongTinChiTiet?.nbDotDieuTriId);
        }

        async function ketLuan() {
          await dangKetLuan(
            {
              ketThucKham: true,
              huongDieuTri: isChuyenKhoa
                ? [
                    HUONG_DIEU_TRI_KHAM.NHAP_VIEN,
                    HUONG_DIEU_TRI_KHAM.CHUYEN_VIEN,
                    HUONG_DIEU_TRI_KHAM.CHUYEN_VIEN_THEO_YEU_CAU,
                  ].includes(keyHuongDieuTri)
                  ? null
                  : keyHuongDieuTri || null
                : keyHuongDieuTri,
              ketQuaDieuTri: isChuyenKhoa ? null : keyKetQua,
              ghiChu:
                keyHuongDieuTri == HUONG_DIEU_TRI_KHAM.CHUYEN_KHAM_CHUYEN_SAU
                  ? ghiChu
                  : undefined,
              thongTinTheoDoi:
                keyHuongDieuTri == HUONG_DIEU_TRI_KHAM.THEO_DOI_NGUOI_BENH
                  ? thongTinTheoDoi
                  : undefined,
            },
            {
              showErrorMessage: !isChuyenKhoa,
            }
          ).then((s) => {
            //sau khi chọn loại kết thúc khám thì đổi trạng thái sang đang kết luận
            // const { keyHuongDieuTri, keyKetQua } = values;
            // refKetLuanKham.current = { keyHuongDieuTri, keyKetQua };
            setState({ activeTab: 3 }); //cập nhật lại thông tin kết luận khám ngoài màn hình
            setKetLuanKhamObject({ keyHuongDieuTri, keyKetQua });
            if (
              [
                HUONG_DIEU_TRI_KHAM.CHO_VE,
                HUONG_DIEU_TRI_KHAM.CAP_DON_CHO_VE,
                HUONG_DIEU_TRI_KHAM.KHONG_KHAM,
              ].includes(keyHuongDieuTri) &&
              isKetThucKham
            ) {
              onSaveKetLuanKham({
                ketThuc: true,
                values: {
                  bacSiKetLuanId: nhanVienId,
                  thoiGianKetLuan: moment(),
                  id: thongTinChiTiet.id,
                  huongDieuTri: keyHuongDieuTri,
                  ketQuaDieuTri: keyKetQua,
                },
              });
            }
          });
        }

        if (
          values?.keyHuongDieuTri === HUONG_DIEU_TRI_KHAM.NHAP_VIEN &&
          isBADaiHan
        ) {
          showConfirm(
            {
              title: t("common.thongBao"),
              content: `${t(
                "khamBenh.neuNguoiBenhNhapVienMaHSSeDuocGanVoiMaBANoiTru"
              )}`,
              cancelText: t("common.huy"),
              okText: t("common.dongY"),
              classNameOkText: "button-warning",
              showBtnOk: true,
              typeModal: "warning",
            },
            () => {
              huyBenhAnDaiHan(thongTinChiTiet?.nbDotDieuTriId).then((res) => {
                getDsMaBADaiHan(thongTinBenhNhan?.nbThongTinId);
                getById(thongTinChiTiet?.nbDotDieuTriId);
                ketLuan();
              });
            },
            () => {}
          );
        } else if (
          values?.keyHuongDieuTri === HUONG_DIEU_TRI_KHAM.KHAM_VE_BO_DON
        ) {
          showConfirmKhamVeBoDon(() => {
            ketLuan();
          });
        } else {
          ketLuan();
        }

        // await saveDataByKey(false); // 2
      } catch (error) {
        console.log(error);
        // Lỗi thiếu thông tin hướng điều trị vẫn cho bật pop up chuyển khoa
        if (isChuyenKhoa) {
          if (error?.code === 8304) {
            return Promise.resolve();
          }
          if (error instanceof DetailedError) {
            return Promise.reject(error);
          }
          message.error(error?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          return Promise.reject(error);
        }

        return Promise.reject(error);
      }
    },
    2000,
    {
      leading: true,
      trailing: false,
    }
  );

  const onKetThucKham = throttle(
    async () => {
      const ketThucKham = () => {
        let _dsCdChinhId = refData?.current?.nbChanDoan?.dsCdChinhId || [];
        if (
          _dsCdChinhId.length === 0
          // ||
          // refData?.current?.nbCovid?.dsCdChinhId?.length === 0
        ) {
          //
          //False: Khi nhấn kết thúc khám với Hướng điều trị<> Nhập viện hoặc bất kỳ hành động nào liên quan tới ràng buộc Chẩn đoán bệnh --> Đều bỏ qua không bắt buộc phải điền chẩn đoán bệnh
          if (
            !(
              infoNb?.doiTuong === DOI_TUONG.KHONG_BAO_HIEM &&
              !BAT_BUOC_CD_BENH_DOI_TUONG_KHONG_BH?.eval() &&
              refKetLuanKham?.current?.keyHuongDieuTri !=
                HUONG_DIEU_TRI_KHAM.NHAP_VIEN
            )
          ) {
            if (
              BAT_BUOC_CD_BENH_DOI_TUONG_KHONG_BH &&
              refKetLuanKham?.current?.keyHuongDieuTri !=
                HUONG_DIEU_TRI_KHAM.KHONG_KHAM
            ) {
              message.error(
                t("khamBenh.canhBaoNhapChanDoanTruocKhiKetThucKham")
              );
              return;
            }
          }
        }

        const { nbKetLuan, nbNhapVien } = refDataKetLuan?.current || {};
        const { keyHuongDieuTri } = refKetLuanKham?.current || {};
        if (
          keyHuongDieuTri === HUONG_DIEU_TRI_KHAM.HEN_KHAM &&
          !nbKetLuan?.thoiGianHenKham
        ) {
          message.error(t("khamBenh.batBuocChonThoiGianHenKham"));
          return;
        }
        if (
          keyHuongDieuTri === HUONG_DIEU_TRI_KHAM.NHAP_VIEN &&
          !nbNhapVien?.khoaNhapVienId
        ) {
          message.error(
            t("khamBenh.ketLuanKham.nhapVien.vuiLongNhapChoDieuTriTaiKhoa")
          );
          return;
        }

        refModalKetThucKham.current &&
          refModalKetThucKham.current.show({}, (values) => {
            showLoading();

            setTimeout(async () => {
              try {
                await onSaveKetLuanKham({
                  ketThuc: true,
                  values,
                  dongHoSo: true,
                });

                let _isTonTaiThuoc = false;
                //Lấy danh sách thuốc để kiểm tra có tồn tại mã P027 hay không?
                if (dsMaPhieuInKhiDongHS.includes("P027")) {
                  const resThuoc = await Promise.all([
                    getListDichVuThuoc({
                      nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
                      dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
                      chiDinhTuDichVuId: thongTinChiTiet.id,
                      dsTrangThaiHoan: [0, 10, 20, 30],
                    }),
                    getListThuocNhaThuoc({
                      nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
                      dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
                      chiDinhTuDichVuId: thongTinChiTiet.id,
                      dsTrangThaiHoan: [0, 10, 20],
                    }),
                  ]);

                  _isTonTaiThuoc =
                    flatten(resThuoc.map((item) => item?.data || [])).length >
                    0;
                }

                const _dsMaPhieu = logicMaPhieuTheoHuongDieuTri(
                  dsMaPhieuInKhiDongHS,
                  refKetLuanKham?.current?.keyHuongDieuTri,
                  _isTonTaiThuoc
                );

                const res = await Promise.all([
                  await getPhieuInTheoDsMa({
                    nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
                    maManHinh: "003",
                    maViTri: "00303",
                    dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
                    chiDinhTuDichVuId: thongTinChiTiet.id,
                    dsMaPhieu: _dsMaPhieu,
                  }),

                  await getPhieuInTheoDsMa({
                    nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
                    maManHinh: "003",
                    maViTri: "00302",
                    dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
                    chiDinhTuDichVuId: thongTinChiTiet.id,
                    dsMaPhieu: _dsMaPhieu,
                  }),
                ]);
                const _flattenRes = flatten(res);
                let _listPhieus = [];
                _flattenRes.forEach((phieu) => {
                  if (LIST_PHIEU_IN_WORD_THEO_SO_PHIEU.includes(phieu.ma)) {
                    (phieu.dsSoPhieu || []).forEach((soPhieu) => {
                      _listPhieus.push({ ...phieu, dsSoPhieu: [soPhieu] });
                    });
                  } else {
                    _listPhieus.push(phieu);
                  }
                });
                //check lọc những phiếu ký số và ko phải editor
                const _listPhieusKySo = _listPhieus.filter(
                  (item) =>
                    item.kySo &&
                    item.loaiBieuMau === LOAI_BIEU_MAU.BIEU_MAU_CHI_XEM
                );
                if (_listPhieusKySo.length > 0) {
                  refModalSignPrint.current &&
                    refModalSignPrint.current.showToSign(
                      {
                        phieuKy: _listPhieusKySo,
                        payload: {
                          nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
                          dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
                          chiDinhTuDichVuId: thongTinChiTiet.id,
                        },
                        kyTheoTungPhieu: true,
                      },
                      async (maPhieu) => {
                        const _refreshPhieu = _listPhieusKySo.find(
                          (x) => x.ma === maPhieu
                        );

                        if (_refreshPhieu) {
                          let _newListPhieusKySo = _listPhieusKySo.filter(
                            (x) => x.ma !== maPhieu
                          );

                          const _newRefreshPhieu = await getPhieuInTheoDsMa({
                            nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
                            maManHinh: _refreshPhieu.maManHinh,
                            maViTri: _refreshPhieu.maViTri,
                            dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
                            chiDinhTuDichVuId: thongTinChiTiet.id,
                            dsMaPhieu: [maPhieu],
                          });

                          if (_newRefreshPhieu.length > 0) {
                            if (
                              LIST_PHIEU_IN_WORD_THEO_SO_PHIEU.includes(maPhieu)
                            ) {
                              (_newRefreshPhieu[0].dsSoPhieu || []).forEach(
                                (soPhieu) => {
                                  _newListPhieusKySo.push({
                                    ..._newRefreshPhieu[0],
                                    dsSoPhieu: [soPhieu],
                                  });
                                }
                              );
                            } else {
                              _newListPhieusKySo.push(_newRefreshPhieu[0]);
                            }

                            return _newListPhieusKySo;
                          }
                        }

                        return _listPhieusKySo;
                      }
                    );
                }

                //check lọc những phiếu ký số và là editor
                const _listPhieusKySoEditor = _listPhieus.filter(
                  (item) =>
                    item.kySo &&
                    item.loaiBieuMau === LOAI_BIEU_MAU.BIEU_MAU_CHINH_SUA
                );
                if (_listPhieusKySoEditor.length > 0) {
                  _listPhieusKySoEditor.forEach((item) => {
                    showFileEditor({
                      phieu: item,
                      id: thongTinChiTiet?.nbDotDieuTriId,
                      nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
                      ma: item.ma,
                      maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                        ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                        : "",
                      chiDinhTuDichVuId: thongTinChiTiet?.id,
                      dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
                      mhParams: {
                        chiDinhTuDichVuId: thongTinChiTiet?.id,
                        maManHinh: item.maManHinh,
                        maViTri: item.maViTri,
                        kySo: true,
                        maPhieuKy: item.ma,
                        baoCaoId: item.baoCaoId,
                      },
                      khongDongTab: true,
                    });
                  });
                }

                //những phiếu ko ký số thì cho in ra bình thường
                const _listPhieusKoKySo = _listPhieus.filter(
                  (item) => !item.kySo
                );

                if (_listPhieusKoKySo.length > 0) {
                  const { finalFile, dsPhieu } = await getFilePhieuIn({
                    listPhieus: _listPhieusKoKySo,
                    nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
                    chiDinhTuDichVuId: thongTinChiTiet?.id,
                    dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
                    isInHsba: true,
                  });
                  if (
                    (dsPhieu || []).every((x) => x?.loaiIn == LOAI_IN.MO_TAB)
                  ) {
                    openInNewTab(finalFile);
                  } else {
                    printProvider.printPdf(dsPhieu, {
                      merge: true,
                      mergePdfFile: finalFile,
                    });
                  }
                }
              } catch (error) {
                console.error(error);
              } finally {
                hideLoading();
              }
            }, 2000);
          });
      };

      if (
        !(await checkKhamKLKhacBacSi(
          thongTinChiTiet,
          nhanVienId,
          KHONG_DUOC_KHAM_KL_NB_KHAC_BSKHAM
        ))
      ) {
        message.error(
          t("khamBenh.khongChoPhepThucHienKhamKetLuan", {
            bacSiKham: thongTinChiTiet?.bacSiKham?.ten,
            bacSiHienTai: full_name,
          })
        );
        return;
      }

      if (state.activeTab != 3) {
        //nếu ở tab đơn thuốc => lưu đơn thuốc trước khi đóng HS
        if (state.activeTab == 4 && state.isEditDonThuoc) {
          onSaveDonThuoc();
        }
        ketThucKham();
      } else {
        showLoading();
        onSaveKetLuanKham({ dongHoSo: true })
          .then((s) => {
            hideLoading();
            ketThucKham();
          })
          .catch((e) => {
            hideLoading();
          });
      }
    },
    2000,
    {
      leading: true,
      trailing: false,
    }
  );

  const onHoanThanhKSK = throttle(
    async () => {
      showLoading();
      hoanThanhKSK({
        id: thongTinChiTiet?.nbDotDieuTriId,
      })
        .then(() => {})
        .catch((err) => {
          showConfirm(
            {
              title: t("common.thongBao"),
              content: `${err}. Xác nhận bỏ qua các dịch vụ chưa thực hiện?`,
              cancelText: t("common.huy"),
              okText: t("common.dongY"),
              classNameOkText: "button-warning",
              showImg: true,
              showBtnOk: true,
              typeModal: "warning",
            },
            () => {
              hoanThanhKSK({
                id: thongTinChiTiet?.nbDotDieuTriId,
                boQuaDvChuaThucHien: true,
              });
            }
          );
        })
        .finally(() => {
          hideLoading();
        });
    },
    2000,
    {
      leading: true,
      trailing: false,
    }
  );

  const onHuyHoanThanhKSK = throttle(
    async () => {
      showLoading();
      huyHoanThanhKSK({
        id: thongTinChiTiet?.nbDotDieuTriId,
      }).finally(() => {
        hideLoading();
      });
    },
    2000,
    {
      leading: true,
      trailing: false,
    }
  );

  const handleShowModalChuyenKhoa = async () => {
    try {
      await onKetLuanKham({ isChuyenKhoa: true });

      refModalChuyenKhoa.current && refModalChuyenKhoa.current.show({});
    } catch (err) {
      console.log(err);
    }
  };

  const processError = (data, key) => {
    const listAllChiSoSong = get(getState(), "chiSoSong.listAllChiSoSong", []);
    return data.reduce((a, c) => {
      if (key == "nbChanDoan" && c == "dsChuyenKhoaId") {
        !refData.current?.[c]?.length && a.push(c);
      } else if (key === "nbChiSoSong" && c === "dsChiSoSongKhac") {
        listAllChiSoSong.length &&
          listAllChiSoSong.length !== refData.current?.[key]?.[c]?.length &&
          a.push(c);
      } else if (
        key === "nbChiSoSong" &&
        [
          "mach",
          "huyetApTamThu",
          "huyetApTamTruong",
          "nhietDo",
          "nhipTho",
        ]?.includes(c)
      ) {
        if (refData.current?.[key]?.[c] < 0) {
          a.push(c);
        }
      } else {
        !refData.current?.[key]?.[c] && a.push(c);
      }
      return a;
    }, []);
  };

  const onValidateBeforeShow = (open) => {
    if (isKsk || !open) return true;
    //kiểm tra dịch vụ thuộc thiết lập không khám thì bỏ qua ko check lỗi
    if (isDvKhongKham) {
      return true;
    }

    const {
      nbHoiBenh: { requireFields: nbHoiBenhRequireFields },
      nbChanDoan: { requireFields: nbChanDoanRequireFields },
      nbChiSoSong: { requireFields: nbChiSoSongRequireFields },
      nbKhamXet: { requireFields: nbKhamXetRequireFields },
    } = get(getState(), "khamBenh.validKetThucKham", {});

    const errors = [];
    if (nbChanDoanRequireFields.length) {
      const nbChanDoan = processError(nbChanDoanRequireFields, "nbChanDoan");
      nbChanDoan.length && errors.push({ nbChanDoan });
    }
    if (nbHoiBenhRequireFields.length) {
      const nbHoiBenh = processError(nbHoiBenhRequireFields, "nbHoiBenh");
      nbHoiBenh.length && errors.push({ nbHoiBenh });
    }
    if (nbChiSoSongRequireFields.length) {
      const nbChiSoSong = processError(nbChiSoSongRequireFields, "nbChiSoSong");
      nbChiSoSong.length && errors.push({ nbChiSoSong });
    }
    if (nbKhamXetRequireFields.length) {
      const nbKhamXet = processError(nbKhamXetRequireFields, "nbKhamXet");
      nbKhamXet.length && errors.push({ nbKhamXet });
    }

    errors.forEach((e) => {
      updateValidKetThucKham(Object.keys(e)[0], {
        errorFields: Object.values(e)[0],
      });
    });
    if (errors.length) {
      const errorFields = [];
      errors.forEach((e) => {
        if (Object.values(e)[0]?.length) {
          errorFields.push(...Object.values(e)[0]);
        }
      });
      if (errorFields?.length) {
        message.error(
          t("khamBenh.vuiLongNhapDuCacTruongBatBuocDeKetThucKham", {
            tenTruong: t(`khamBenh.batBuoc.${errorFields[0]}`),
          })
        );
        return false;
      }
    }
    return true;
  };

  const renderButtonRight = () => {
    let showButtonHuy = false;

    //bổ sung thêm quyền
    //0500128 Được phép hủy kết luận khám khi người hủy là bác sĩ kết luận
    //0500129 Được phép hủy kết luận khám khác bác sĩ kết luận
    if (
      trangThaiKham === TRANG_THAI_DICH_VU.DA_KET_LUAN &&
      ((thongTinChiTiet?.bacSiKetLuanId === nhanVienId &&
        checkRole([ROLES["KHAM_BENH"].HUY_KET_LUAN_CUNG_BAC_SY])) ||
        checkRole([ROLES["KHAM_BENH"].HUY_KET_LUAN_KHAC_BAC_SY]))
      // infoNb.doiTuongKcb != 3 &&
      // infoNb.doiTuongKcb != 4
    ) {
      // điều kiện thay đổi hủy kết luận thay đổi thành :
      // + Điều kiện hiển thị button Hủy kết luận: DV khám ở trạng thái Đã kết luận + đối tượng KCB khác điều trị nội trú & điều trị nội trú ban ngày
      showButtonHuy = true;
    }
    if (showButtonHuy) {
      return (
        <Button
          type="default"
          onClick={() => {
            huyKetLuanGetMethod({
              nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
            }).then((res) => {
              console.log("res: ", res);
              let { data } = res;
              let content = "";
              let okText = "";
              let showBtnOk = true;
              let functionSubmit = huyKetLuanKham;

              if (data.phatHanhHoaDon) {
                content = t(
                  "khamBenh.nguoiBenhDaThanhToanBaoHiemHuyThanhToanDeMoLaiHoSo"
                );
                showBtnOk = false;
                if (checkRole([ROLES["KHAM_BENH"].MO_HO_SO])) {
                  // true : có quyền MO_HO_SO
                  showBtnOk = true;
                  content = t(
                    "khamBenh.nguoiBenhDaThanhToanBaoHiemMoHoSoAnhHuongQTBH"
                  );
                  okText = t("khamBenh.moHoSo");
                }
                // functionSubmit = huyKetLuanKham
              } else if (data.thanhToan) {
                content = t(
                  "khamBenh.nbDaThanhToanBhHuyThanhToanHoaDeMoLaiHoSo"
                );
                showBtnOk = false;
                if (checkRole([ROLES["KHAM_BENH"].MO_HO_SO_DA_THANH_TOAN])) {
                  // true : có quyền MO_HO_SO
                  showBtnOk = true;
                  content = t(
                    "khamBenh.nbDaThanhToanBhHuyThanhToanHoaDeMoLaiHoSo"
                  );
                  okText = t("khamBenh.moHoSo");
                }
                // functionSubmit = huyKetLuanKham
              } else {
                // false
                content = `${t(
                  "khamBenh.xacNhanThayDoiKetQuaKhiDaKetThucKham"
                )} <br/> ${t(
                  "khamBenh.bacSiLuuYDongHoSoLaiSauKhiChinhSuaDeDamBaoQuyenLoiCuaNguoiBenh"
                )}`;
                okText = t("common.dongY");
                showBtnOk = true;
              }
              showConfirm(
                {
                  content,
                  cancelText: t("common.quayLai"),
                  okText,
                  showBtnOk,
                  typeModal: "warning",
                  title: t("common.canhBao"),
                },
                async () => {
                  functionSubmit({ id: thongTinChiTiet?.id })
                    .then(() => {
                      setQueryStringValue("tab", 0);
                      window.location.href = window.location.href;
                    })
                    .catch((err) => {
                      showConfirm({
                        content: err?.message,
                        cancelText: t("common.quayLai"),
                        typeModal: "error",
                        title: t("common.thongBao"),
                      });
                    });
                }
              );
            });
          }}
        >
          {t("khamBenh.huyKetLuan")}
        </Button>
      );
    }

    if (
      // state.activeTab === 3 ||
      trangThaiKham == TRANG_THAI_DICH_VU.DANG_KET_LUAN
    ) {
      return (
        <Button className="custom-btn" type="primary" onClick={onKetThucKham}>
          {t("khamBenh.dongHoSo")}
        </Button>
      );
    }

    if (
      trangThaiKham != TRANG_THAI_DICH_VU.DA_KET_LUAN &&
      !(isKsk && [10, 30].includes(infoNb?.trangThaiKsk))
    )
      return (
        <CustomPopoverWithRef
          onValidateBeforeShow={onValidateBeforeShow}
          width={420}
          onKetLuanKham={onKetLuanKham}
          text={
            <Button className="custom-btn" type="primary">
              {t("khamBenh.ketThucKham")}
            </Button>
          }
        />
      );

    return null;
  };

  const onCancel = () => {
    Object.keys(refElement.current).forEach((key) => {
      refElement.current[key]?.reload && refElement.current[key].reload();
    });

    if (!Object.keys(refElement.current).includes("useMauKetQua")) {
      const cloneThongTinChiTiet = cloneDeep(thongTinChiTiet);
      setChiTietDvKham(refData.current);
      updateDataKhamBenh({ thongTinChiTiet: cloneThongTinChiTiet });
    }

    setIsEditted(false);
  };

  const onSave =
    ({ isKetThucKham }) =>
    async (e) => {
      e.preventDefault();
      if (
        daCoDichVu &&
        !refData?.current?.nbChanDoan?.cdSoBo &&
        refData?.current?.nbChanDoan?.dsCdChinhId?.length === 0
      ) {
        message.error(t("khamBenh.thongBaoKhongTheXoaChanDoanKhiDaKeDichVu"));
      } else if (
        !(await checkKhamKLKhacBacSi(
          thongTinChiTiet,
          nhanVienId,
          KHONG_DUOC_KHAM_KL_NB_KHAC_BSKHAM
        ))
      ) {
        message.error(
          t("khamBenh.khongChoPhepThucHienKhamKetLuan", {
            bacSiKham: thongTinChiTiet?.bacSiKham?.ten,
            bacSiHienTai: full_name,
          })
        );
      } else {
        saveDataByKey().then(() => {
          setIsEditted(false);

          getStatisticsRoom({ phongThucHienId });
          if (isKetThucKham) {
            onSaveKetLuanKham({
              ketThuc: true,
              values: {
                bacSiKetLuanId: nhanVienId,
                thoiGianKetLuan: moment(),
                id: thongTinChiTiet.id,
                ketQuaDieuTri: refDataLoiDan.current?.ketQuaDieuTri || null,
                huongDieuTri: refDataLoiDan.current?.huongDieuTri || null,
              },
              dongHoSo: true,
            });
          }
        });
      }
    };
console.log("refDataLoiDan", refDataLoiDan)
  const onSaveDonThuoc = throttle(
    async (e) => {
      if (e) e.preventDefault();
      if (
        refDataLoiDan?.current?.soNgayChoDon &&
        Number(refDataLoiDan?.current?.soNgayChoDon) <= 0
      ) {
        message.error(t("khamBenh.chiChoPhepNhapSoNguyenLonHon0"));
      } else {
        if (
          [...listDvThuoc, ...listDvThuocKeNgoai, ...listDvThuocNhaThuoc]
            ?.length &&
          GIOI_HAN_SO_NGAY_CHO_THUOC_NGOAI_TRU &&
          refDataLoiDan.current?.soNgayChoDon &&
          Number(refDataLoiDan.current?.soNgayChoDon) >
            Number(GIOI_HAN_SO_NGAY_CHO_THUOC_NGOAI_TRU)
        ) {
          message.error(
            t("khamBenh.vuiLongNhapSoNgayChoDon", {
              num: GIOI_HAN_SO_NGAY_CHO_THUOC_NGOAI_TRU,
            })
          );
          return;
        }

        if (
          [...listDvThuoc, ...listDvThuocKeNgoai, ...listDvThuocNhaThuoc]
            ?.length &&
          moment(refDataLoiDan.current.tuNgay).isBefore(
            moment(infoNb.thoiGianVaoVien).startOf("days")
          )
        ) {
          message.error(t("quanLyNoiTru.vuiLongNhapTuNgayLonHonNgayVaoVien"));
          return;
        }

        if (
          BAT_BUOC_THOI_GIAN_DUNG_THUOC_NGOAI_TRU?.eval() &&
          !(refDataLoiDan.current?.tuNgay && refDataLoiDan.current?.denNgay) &&
          [...listDvThuoc, ...listDvThuocKeNgoai, ...listDvThuocNhaThuoc]
            ?.length
        ) {
          message.error(t("khamBenh.vuiLongNhapThoiGianTuNgayDenNgayChoDon"));
          return;
        }

        showLoading();
        try {
          const _soNgayChoDon =
            refDataLoiDan.current?.soNgayChoDon == ""
              ? 0
              : refDataLoiDan.current?.soNgayChoDon;

          let obj = {
            id: thongTinChiTiet?.id,
            body: {
              ghiChu: refDataLoiDan.current?.ghiChu,
              loiDan: refDataLoiDan.current?.loiDan,
              soNgayChoDon: _soNgayChoDon,
              thoiGianHenKham: refDataLoiDan.current?.thoiGianHenKham,
              tuNgay: refDataLoiDan.current?.tuNgay,
              denNgay: refDataLoiDan.current?.denNgay,
              ketQuaDieuTri: thongTinKetLuan?.ketQuaDieuTri || null,
              huongDieuTri: thongTinKetLuan?.huongDieuTri || null,
            },
          };
          await luuLoiDan(obj);
          setState({ isEditDonThuoc: false });
          message.success(t("common.capNhatThanhCong"));
        } finally {
          hideLoading();
        }
      }
    },
    2000,
    {
      leading: true,
      trailing: false,
    }
  );

  const onKetThucKhamDonThuoc = throttle(async (e) => {
    await onSaveDonThuoc();
    await onKetLuanKham({ isKetThucKham: true });
  });

  const onSaveKhamLao = throttle(
    async (e) => {
      if (e) e.preventDefault();
      refDieuTriLao.current && refDieuTriLao.current.handleSubmit();
    },
    2000,
    {
      leading: true,
      trailing: false,
    }
  );

  const onFinishThuocLao = throttle(
    async (data) => {
      refDieuTriLao.current && refDieuTriLao.current.onFinish(data);
    },
    2000,
    {
      leading: true,
      trailing: false,
    }
  );

  const onActionHoSoSinh = (lienKet) => () => {
    showConfirm(
      {
        title: "",
        content: t(
          `khamBenh.chanDoan.xacNhan${
            lienKet ? "" : "Huy"
          }LienKetHoSoSinhNgayDateMaHoSoMaHoSoTenNb`,
          {
            date: dataHoSoChoSinh?.thoiGianVaoVien
              ? moment(dataHoSoChoSinh?.thoiGianVaoVien).format("DD/MM/YYYY")
              : null,
            maHoSo: dataHoSoChoSinh?.maHoSo,
            tenNb: dataHoSoChoSinh?.tenNb,
          }
        ),
        cancelText: t("common.huy"),
        okText: t("common.xacNhan"),
        showBtnOk: true,
      },
      () => {
        showLoading();
        let body = {
          id: thongTinChiTiet.nbDotDieuTriId,
          loaiLienKet: lienKet ? 30 : null,
          ...(lienKet && {
            nbLienKetId: dataHoSoChoSinh?.id,
          }),
        };
        mutateLienKetHoSoSinh(body, {
          onSuccess: () => {
            message.success(
              t(
                `khamBenh.chanDoan.${
                  lienKet ? "lienKet" : "huyLienKet"
                }HoSoSinhThanhCong`
              )
            );
            refetch();
            getNbDotDieuTriTongHopTheoId({
              nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
            });
            hideLoading();
          },
          onError: (error) => {
            hideLoading();
            message.error(
              error?.message ||
                t(
                  `khamBenh.chanDoan.${
                    lienKet ? "lienKet" : "huyLienKet"
                  }HoSoSinhThatBai`
                )
            );
          },
        });
      }
    );
  };

  const onDangKyLao = throttle(
    (type) => async () => {
      const onRegister = ({ loai, ...rest }) =>
        createOrEdit({
          nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
          loai,
          khoaChiDinhId: thongTinChiTiet?.nbDvKyThuat?.phongThucHien?.khoaId,
          ...rest,
        });
      if (type === "ngoaiGio") {
        //  Đăng ký ngoài giờ
        try {
          showLoading();
          const res = await onRegister({
            trangThai: TRANG_THAI_DANG_KY_THUOC_LAO.DANG_KY_THUOC_LAO_NGOAI_GIO,
          });
          if (res?.data?.id) {
            await sleep(300);
            getThuocLaoById(res?.data?.id);
          }
          hideLoading();
        } catch (err) {
          hideLoading();
        }
      }
    },
    2000,
    {
      leading: true,
      trailing: false,
    }
  );

  const onGuiViTimes = throttle(
    async () => {
      showLoading();
      try {
        await dayViTimesNb({ nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId });
        getChiTietNbDieuTriLao({
          id: thongTinChiTiet.nbDotDieuTriId,
          fromNoiTru: true,
        });
      } catch (error) {
        console.error(error);
      } finally {
        hideLoading();
      }
    },
    2000,
    {
      leading: true,
      trailing: false,
    }
  );

  const onChuyenKhangLao = throttle(
    async () => {
      showLoading();
      try {
        await postChuyenLaoKhang({
          id: detailDataThuocKhamLao?.id,
        });
        await sleep(300);
        const res = await getNbDotDieuTriTongHopTheoId({
          nbDotDieuTriId: thongTinChiTiet?.nbDotDieuTriId,
        });
        getThuocLaoById(res?.data?.thuocLaoId);
      } catch (error) {
        console.error(error);
      } finally {
        hideLoading();
      }
    },
    2000,
    {
      leading: true,
      trailing: false,
    }
  );

  const renderBody = () => {
    return (
      <div
        className={classNames("element element-page", {
          "h-full": state.activeTab === 12,
        })}
        key={state.activeTab}
      >
        {(() => {
          switch (state.activeTab) {
            case 0:
              return (
                <LazyLoad
                  component={() => import("../KhamCoBan")}
                  handleSetData={handleSetData}
                  handleGetData={handleGetData}
                  layerId={layerId}
                  nbDotDieuTriId={thongTinChiTiet.nbDotDieuTriId}
                  isKsk={isKsk}
                  trangThaiKham={trangThaiKham}
                  trangThaiKsk={infoNb?.trangThaiKsk}
                />
              );
            case 1:
            case 11:
              return (
                <LazyLoad
                  isTiepDon={state.activeTab === 11}
                  component={() => import("../ChiDinhDichVu")}
                  layerId={layerId}
                />
              );
            case 2:
            case 10:
              return (
                <LazyLoad
                  isTiepDon={state.activeTab === 10}
                  component={() => import("../KetQua")}
                />
              );
            case 12:
              return (
                <LazyLoad
                  isTiepDon={state.activeTab === 12}
                  component={() => import("../HoSoChoSinh")}
                />
              );
            case 3:
              return (
                <LazyLoad
                  component={() => import("../KetLuan")}
                  refData={refDataKetLuan}
                  ref={refInKetLuanKham}
                  ketLuanKham={ketLuanKhamObject}
                  setKetLuanKham={(data) => {
                    setIsEditted(true);
                    setKetLuanKhamObject(data);
                  }}
                  title={t("khamBenh.ketThucKham")}
                  onSaveKetLuanKham={onSaveKetLuanKham}
                  handleSetData={(arrKey) => (e) => {
                    if (
                      refData.current?.nbChiSoSong?.huyetApTamTruong >
                      refData.current?.nbChiSoSong?.huyetApTamThu
                    ) {
                      setIsEditted(false);
                    } else {
                      setIsEditted(true);
                    }

                    const [key1, key2] = arrKey;

                    //nếu update trường lưu ý trong khám xét => update field ghi chú trong lời dặn
                    if (key1 == "nbKhamXet" && key2 == "ghiChu") {
                      refDataLoiDan.current["ghiChu"] = e;
                    }

                    handleSetDataKetLuan(arrKey)(e);
                  }}
                  // handleSetData={handleSetData}
                />
              );
            case 4:
              return (
                <LazyLoad
                  layerId={layerId}
                  component={() => import("../DonThuoc")}
                  ref={refChiDinhThuoc}
                  handleSetData={handleSetDataLoiDan}
                  onKetThucKham={onKetLuanKham}
                />
              );
            case 5:
              return (
                <LazyLoad
                  component={() => import("../VatTu")}
                  layerId={layerId}
                />
              );
            case 6:
              return <LazyLoad component={() => import("../GoiDaChiDinh")} />;
            case 7:
              return <LazyLoad component={() => import("../PhacDoDieuTri")} />;
            case 8:
              return (
                <LazyLoad
                  component={() => import("../DieuTriLao")}
                  ref={refDieuTriLao}
                  setEdit={setState}
                />
              );
            case 9:
              return (
                <LazyLoad
                  component={() => import("../PhieuPhaCheThuocTheoDon")}
                />
              );
            default:
              break;
          }
        })()}
      </div>
    );
  };

  const onClickNavigate = (newElementKey) => {
    if (infoNb?.khamSucKhoe && infoNb?.trangThaiKsk == 10) return;
    const item = DANH_SACH_DAU_TRANG.find((i) => i.itemKey == newElementKey);
    if (!trangThaiKham || trangThaiKham < item?.trangThai) return;

    onActiveTab(Number(newElementKey));
    // setState({ activeTab: Number(newElementKey) });
  };

  refFuncNavi.current = onClickNavigate;

  const onBoSungThamKham = () => {
    refModalBoSungThamKham.current && refModalBoSungThamKham.current.show();
  };
  return (
    <Main className="wrapper-thong-tin-kham">
      <ConflictResolutionModal ref={conflictModalRef} />
      <div className="col-thong-tin-kham-left">
        <ButtonDetail onClick={onCollapse}>
          <Tooltip
            title={t(collapse ? "common.moRong" : "common.thuGon")}
            overlayStyle={{ whiteSpace: "nowrap" }}
            overlayInnerStyle={{ width: "fit-content" }}
            visible={state.off ? false : undefined}
            onVisibleChange={(e) => {
              if (e && state.off) {
                setState({ off: false });
              }
            }}
          >
            {collapse ? (
              <SVG.IcCollapse color={"white"} />
            ) : (
              <SVG.IcExtend color={"white"} />
            )}
          </Tooltip>
        </ButtonDetail>
        <StepWrapper
          customHeaderRight={renderButtonRight()}
          layerId={layerId}
          activeTab={state.activeTab}
          ref={refStepWrapper}
          handleShowModalChuyenKhoa={handleShowModalChuyenKhoa}
        >
          {renderBody()}
        </StepWrapper>

        <ButtonFooter className="btn">
          <div className="nav-bottom">
            {isKsk &&
              infoNb?.trangThaiKsk === 20 &&
              thongTinChiTiet?.ketLuanKham && (
                <Button type="primary" onClick={onHoanThanhKSK}>
                  {"Hoàn thành KSK"}
                </Button>
              )}

            {isKsk &&
              infoNb?.trangThaiKsk === 30 &&
              thongTinChiTiet?.ketLuanKham && (
                <Button type="default" onClick={onHuyHoanThanhKSK}>
                  Hủy hoàn thành KSK
                </Button>
              )}

            {isKsk && (
              <Button type="primary" onClick={onBoSungThamKham}>
                {t("khamBenh.thongTinThamKham.boSungThongTinThamKham")}
              </Button>
            )}
          </div>

          {state.activeTab != 8 && infoNb?.id && (
            <DropdownSangLocDD fromKhamBenh={true} />
          )}

          {[0, 3].includes(state.activeTab) &&
            !(isKsk && [10, 30].includes(infoNb?.trangThaiKsk)) && (
              <>
                <Button onClick={onCancel} type={"default"} minWidth={100}>
                  {t("common.huy")}
                </Button>
                {isEditted && (
                  <Button
                    onClick={onSave({})}
                    type={"primary"}
                    minWidth={100}
                    iconHeight={15}
                    rightIcon={<SVG.IcSave />}
                  >
                    {t("common.luu")}
                  </Button>
                )}
                {isEditted &&
                  state.activeTab == "3" &&
                  trangThaiKham !== TRANG_THAI_DICH_VU.DA_KET_LUAN && (
                    <Button
                      onClick={onSave({ isKetThucKham: true })}
                      type={"primary"}
                      minWidth={100}
                      iconHeight={15}
                      rightIcon={<SVG.IcSave />}
                    >
                      {t("khamBenh.luuVaKetThucKham")}
                    </Button>
                  )}
              </>
            )}
          {state.activeTab == 4 && (
            <>
              {state.isEditDonThuoc && (
                <Button
                  onClick={onSaveDonThuoc}
                  type={"primary"}
                  minWidth={100}
                  iconHeight={15}
                  rightIcon={<SVG.IcSave />}
                >
                  {t("common.luu")}
                </Button>
              )}
              {state.isEditDonThuoc &&
                KET_THUC_KHAM_KHI_CHO_DON_THUOC?.eval() && (
                  <Button
                    onClick={onKetThucKhamDonThuoc}
                    type={"primary"}
                    minWidth={100}
                    iconHeight={15}
                  >
                    {t("khamBenh.luuVaKetThucKham")}
                  </Button>
                )}
            </>
          )}
          {state.activeTab == 8 && (
            <>
              {state.isEditKhamLao && (
                <Button
                  onClick={onSaveKhamLao}
                  type={"primary"}
                  minWidth={100}
                  iconHeight={15}
                  rightIcon={<SVG.IcSave />}
                  ref={refBtnSaveDieuTriLao}
                >
                  {t("common.luu")}
                </Button>
              )}
              <AuthWrapper
                accessRoles={[
                  ROLES["QUAN_LY_DIEU_TRI_LAO"].THEM_MOI_CHINH_SUA_THUOC_LAO,
                ]}
              >
                {state.isEditThuocLao && (
                  <Button
                    type="primary"
                    rightIcon={<SVG.IcSave />}
                    onClick={() => {
                      onFinishThuocLao({});
                    }}
                    ref={refBtnSaveDieuTriLao}
                  >
                    {t("common.luu")}
                  </Button>
                )}
                {detailDataThuocKhamLao?.loai &&
                  detailDataThuocKhamLao.trangThai !==
                    TRANG_THAI_DANG_KY_THUOC_LAO.HOAN_THANH &&
                  detailDataThuocKhamLao.trangThai !==
                    TRANG_THAI_DANG_KY_THUOC_LAO.HUY && (
                    <Button
                      type="primary"
                      onClick={() => {
                        onFinishThuocLao({
                          trangThai: TRANG_THAI_DANG_KY_THUOC_LAO.HOAN_THANH,
                        });
                      }}
                    >
                      {t("common.hoanThanh")}
                    </Button>
                  )}
                {detailDataThuocKhamLao?.loai &&
                  detailDataThuocKhamLao.trangThai ===
                    TRANG_THAI_DANG_KY_THUOC_LAO.TAO_MOI &&
                  [LOAI_LAO.LAO_THUONG, LOAI_LAO.LAO_TIEM_AN]?.includes(
                    detailDataThuocKhamLao.loai
                  ) && (
                    <Button
                      onClick={() => {
                        showConfirm(
                          {
                            title: t("common.xacNhan"),
                            content: t(
                              "quanLyDieuTriLao.xacNhanChuyenNguoiBenhSangDangKyLaoKhangThuoc"
                            ),
                            cancelText: t("common.huy"),
                            okText: t("common.dongY"),
                            classNameOkText: "button-warning",
                            showImg: false,
                            showBtnOk: true,
                            typeModal: "warning",
                          },
                          () => {
                            onChuyenKhangLao();
                          }
                        );
                      }}
                    >
                      {t("quanLyDieuTriLao.chuyenLaoKhang")}
                    </Button>
                  )}
                {detailDataThuocKhamLao?.loai &&
                  detailDataThuocKhamLao.trangThai !==
                    TRANG_THAI_DANG_KY_THUOC_LAO.HOAN_THANH &&
                  detailDataThuocKhamLao.trangThai !==
                    TRANG_THAI_DANG_KY_THUOC_LAO.HUY && (
                    <Button
                      onClick={() => {
                        showConfirm(
                          {
                            title: t("common.xacNhan"),
                            content: t(
                              "quanLyDieuTriLao.xacNhanHuyDangKyThuocLaoChoNguoiBenh"
                            ),
                            cancelText: t("common.huy"),
                            okText: t("common.dongY"),
                            classNameOkText: "button-warning",
                            showImg: false,
                            showBtnOk: true,
                            typeModal: "error",
                          },
                          () =>
                            onFinishThuocLao({
                              trangThai: TRANG_THAI_DANG_KY_THUOC_LAO.HUY,
                            })
                        );
                      }}
                    >
                      {t("quanLyDieuTriLao.huyDangKyThuocLao")}
                    </Button>
                  )}
                {detailDataThuocKhamLao?.loai &&
                  detailDataThuocKhamLao.trangThai ===
                    TRANG_THAI_DANG_KY_THUOC_LAO.HUY && (
                    <Button
                      onClick={() => {
                        showConfirm(
                          {
                            title: t("common.xacNhan"),
                            content: t(
                              "quanLyDieuTriLao.hoanTacHuyDangKyThuocLaoChoNguoiBenh"
                            ),
                            cancelText: t("common.huy"),
                            okText: t("common.dongY"),
                            classNameOkText: "button-warning",
                            showImg: false,
                            showBtnOk: true,
                            typeModal: "warning",
                          },
                          () =>
                            onFinishThuocLao({
                              trangThai: TRANG_THAI_DANG_KY_THUOC_LAO.TAO_MOI,
                            })
                        );
                      }}
                    >
                      {t("quanLyDieuTriLao.hoanTacHuyDangKyThuocLao")}
                    </Button>
                  )}
              </AuthWrapper>
              {checkRole([
                ROLES["QUAN_LY_DIEU_TRI_LAO"].DANH_SACH_NB_DANG_KY_THUOC_LAO,
              ]) &&
                thongTinChiTiet?.nbDvKyThuat?.phongThucHien?.dsTinhChatKhoa?.includes(
                  140
                ) &&
                state.isShowDangKyThuocLaoNgoaiGio &&
                ![
                  TRANG_THAI_DANG_KY_THUOC_LAO.TAO_MOI,
                  TRANG_THAI_DANG_KY_THUOC_LAO.CHUYEN_LAO_KHANG,
                  TRANG_THAI_DANG_KY_THUOC_LAO.DANG_KY_THUOC_LAO_NGOAI_GIO,
                ].includes(detailDataThuocKhamLao.trangThai) && (
                  <Button onClick={onDangKyLao("ngoaiGio")}>
                    {t("quanLyDieuTriLao.dkThuocLaoNgoaiGio")}
                  </Button>
                )}
              {state.isShowViTimes && (
                <Button onClick={onGuiViTimes}>
                  {t("quanLyDieuTriLao.guiVitimes")}
                </Button>
              )}
            </>
          )}
          {isLienKet && (
            <AuthWrapper accessRoles={[ROLES["KHAM_BENH"].LIEN_KET_HO_SO_SINH]}>
              <Button type="primary" onClick={onActionHoSoSinh(true)}>
                {t("khamBenh.chanDoan.lienKetHoSoSinh")}
              </Button>
            </AuthWrapper>
          )}
          {isHuyLienKet && (
            <Button type="primary" onClick={onActionHoSoSinh(false)}>
              {t("khamBenh.chanDoan.huyLienKetHoSoSinh")}
            </Button>
          )}
        </ButtonFooter>
      </div>

      <div
        className="col-thong-tin-kham-right"
        style={{ width: collapse ? "42px" : "270px" }}
      >
        <NavigationBar
          onActiveTab={onActiveTab}
          layerId={layerId}
          activeKey={state.activeTab}
          collapse={collapse}
        />
      </div>
      {trangThaiKham == TRANG_THAI_DICH_VU.DANG_KET_LUAN && (
        <ModalKetThucKham ref={refModalKetThucKham} />
      )}
      {isKsk && <ModalBoSungThongTinThamKham ref={refModalBoSungThamKham} />}
      <ModalChiDinhPhacDo ref={refChiDinhPhacDo} />
      <ModalChuyenKhoa ref={refModalChuyenKhoa} />
      <ModalSignPrint ref={refModalSignPrint} />
    </Main>
  );
};
export default React.memo(ThongTin);
