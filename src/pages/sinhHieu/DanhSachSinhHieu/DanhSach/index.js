import React, { useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { message } from "antd";

import { checkRole } from "lib-utils/role-utils";
import { getAllQueryString } from "hooks/useQueryString/queryString";
import {
  useThietLap,
  useStore,
  useLoading,
  useQueryAll,
  useListAll,
} from "hooks";

import nbChiSoSongProvider from "data-access/nb-chi-so-song-provider";

import {
  TRANG_THAI_DO_SINH_HIEU,
  ROLES,
  THIET_LAP_CHUNG,
  LOAI_DICH_VU,
  LOAI_PHONG,
  PHAN_LOAI_CHI_SO_SONG,
} from "constants/index";
import { SVG } from "assets";
import { isArray } from "utils/index";
import ModalChinhSuaThongTin from "pages/tiepDon/TiepDon/ModalChinhSuaThongTin";
import ModalDoSinhHieu from "../../ModalDoSinhHieu";
import { TableWrapper, Pagination, Tooltip } from "components";
import { Main } from "./styled";
import { query } from "redux-store/stores";
import ModalCheckBaoHiem from "pages/tiepDon/components/ThongTinTiepDon/ModalCheckBaoHiem";

const sleep = (ms) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

const { Column, Setting } = TableWrapper;
const DanhSach = (props) => {
  const { t } = useTranslation();
  const { khoaLamViec } = props;
  const refModalDoSinhHieu = useRef(null);
  const refModalChinhSuaThongTin = useRef(null);
  const refModalCheckBaoHiem = useRef();
  const {
    dataSortColumn,
    listData,
    totalElements,
    page,
    size,
    isLoading,
    first,
    last,
  } = useSelector((state) => state.sinhHieu);
  const [dataPageSize, isFinish] = useThietLap(THIET_LAP_CHUNG.PAGE_SIZE, 10);
  const [LINK_DO_SINH_HIEU_GUT] = useThietLap(
    THIET_LAP_CHUNG.LINK_DO_SINH_HIEU_GUT
  );
  const [listAllKhoaTheoTaiKhoan] = useListAll(
    "khoa",
    {},
    true,
    "KhoaTheoTaiKhoan"
  );

  const { data: listPhong, isLoading: isLoadingPhong } = useQueryAll(
    query.phong.queryAllPhong({
      params: {
        dsLoaiPhong: [LOAI_PHONG.PHONG_KHAM],
      },
    })
  );

  const dataSearch = useStore("sinhHieu.dataSearch", []);
  const { showLoading, hideLoading } = useLoading();

  const {
    sinhHieu: {
      onSizeChange,
      onSortChange,
      onSearch,
      searchSinhHieuByParams,
      clearData,
    },
    vitalSigns: { onCreate },
    tiepDon: { giamDinhThe },
  } = useDispatch();
  const refSettings = useRef(null);

  useEffect(() => {
    if (!khoaLamViec || !isFinish || isLoadingPhong) return;
    const { page, size, dataSortColumn, ...queries } = getAllQueryString();
    if (!queries.tuThoiGianVaoVien) {
      queries.tuThoiGianVaoVien = moment().format("YYYY-MM-DD 00:00:00");
    } else if (queries.tuThoiGianVaoVien === "-") {
      queries.tuThoiGianVaoVien = undefined;
    } else if (queries.tuThoiGianVaoVien) {
      queries.tuThoiGianVaoVien = moment(
        queries.tuThoiGianVaoVien.toDateObject()
      ).format("YYYY-MM-DD 00:00:00");
    }
    if (!queries.denThoiGianVaoVien) {
      queries.denThoiGianVaoVien = moment().format("YYYY-MM-DD 23:59:59");
    } else if (queries.denThoiGianVaoVien === "-") {
      queries.denThoiGianVaoVien = undefined;
    } else if (queries.denThoiGianVaoVien) {
      queries.denThoiGianVaoVien = moment(
        queries.denThoiGianVaoVien.toDateObject()
      ).format("YYYY-MM-DD 23:59:59");
    }
    if (!queries.dsPhongThucHienId && isArray(listPhong, true)) {
      queries.dsPhongThucHienId = listPhong.map((item) => item.id);
    }
    if (queries.trangThaiChiSoSong)
      queries.trangThaiChiSoSong = parseInt(queries.trangThaiChiSoSong);

    queries.theoPhongKham = queries.theoPhongKham
      ? queries.theoPhongKham
      : "false";

    let _dsKhoaNbId = listAllKhoaTheoTaiKhoan.map((item) => item.id);
    if (checkRole([ROLES["SINH_HIEU"].XEM_NB_TAT_CA_KHOA])) {
      _dsKhoaNbId = null;
    }
    if (khoaLamViec?.id && khoaLamViec?.id != "") {
      _dsKhoaNbId = khoaLamViec?.id;
    }

    searchSinhHieuByParams({
      ...queries,
      page: page,
      size: size || dataPageSize,
      dsKhoaNbId: _dsKhoaNbId,
      active: true,
      // dsChiDinhTuLoaiDichVu: [LOAI_DICH_VU.TIEP_DON, LOAI_DICH_VU.KHAM],
    });
  }, [
    khoaLamViec,
    isFinish,
    dataPageSize,
    listPhong,
    isLoadingPhong,
    listAllKhoaTheoTaiKhoan,
  ]);

  useEffect(() => {
    return () => {
      clearData();
    };
  }, []);

  const onChangePage = (page) => {
    onSearch({ page: page - 1 });
  };

  const onClickSort = (key, value) => {
    onSortChange({ [key]: value });
  };

  const onChiTietNb = (item) => (e) => {
    e.stopPropagation();
    e.preventDefault();
    refModalDoSinhHieu.current &&
      refModalDoSinhHieu.current.show({
        nbDotDieuTriId:
          dataSearch.theoPhongKham == "false" ? item.id : item.nbDotDieuTriId,
      });
  };

  const widthSttCol = () => {
    const curPage = Number(page || 0) + 1;
    const curSize = Number(size || 0);
    const length = String(curPage * curSize).length;
    return length * 10 + 20;
  };

  const onDoSinhHieuGut = (data) => (e) => {
    e.stopPropagation();
    e.preventDefault();

    let linkUrl = (LINK_DO_SINH_HIEU_GUT || "").replace(
      "{checkin_id}",
      data?.maHoSoNgoaiVien
    );

    window.open(linkUrl);
  };

  const onEdit = (item) => (e) => {
    e.stopPropagation();
    e.preventDefault();
    refModalChinhSuaThongTin.current &&
      refModalChinhSuaThongTin.current.show(
        {
          id:
            dataSearch.theoPhongKham == "false" ? item.id : item.nbDotDieuTriId,
        },
        () => {
          refreshList();
        }
      );
  };

  const onClickButtonSinhHieu = (item) => async (e) => {
    e.stopPropagation();
    e.preventDefault();

    try {
      showLoading();
      const id =
        dataSearch.theoPhongKham == "false" ? item.id : item.nbDotDieuTriId;
      const res = await Promise.all([
        // nbChiSoSongProvider.getChiSoSongByNbDotDieuTriId({
        //   nbDotDieuTriId: id,
        //   dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.TIEP_DON,
        // }),
        nbChiSoSongProvider.getChiSoSongNbRfid(item.maThe),
      ]);
      const dataSinhHieu = [item];
      let dataCss = res?.[0]?.data || [];
      dataCss = dataCss.reduce((acc, cur) => {
        let thoiGian = moment(cur.thoiGianThucHien).valueOf();
        if (!cur.khoaChiDinhId) {
          cur.khoaChiDinhId = item.khoaNbId;
        }
        if (
          !dataSinhHieu.some(
            (i) => moment(i.thoiGianThucHien).valueOf() === thoiGian
          )
        ) {
          acc.push(cur);
        }
        return acc;
      }, []);
      if (isArray(dataCss, true)) {
        let params = {
          nbDotDieuTriId: id,
          listData: dataCss,
          tiepDon: true,
        };
        await onCreate(params);
        await sleep(300);
      }
      hideLoading();
      refModalDoSinhHieu.current &&
        refModalDoSinhHieu.current.show({
          nbDotDieuTriId: id,
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.TIEP_DON,
          khoaChiDinhId: item.khoaNbId,
        });
    } catch (err) {
      hideLoading();
      message.error(err?.message);
    }
  };

  const onCheckThe = (record) => (e) => {
    e.stopPropagation();
    e.preventDefault();
    let data = {
      hoTen: record.tenNb,
      maThe: record?.maTheBhyt,
      ngaySinh: moment(record?.ngaySinh).format("DD/MM/YYYY"),
      khoaId: record.khoaNbId,
    };
    giamDinhThe(data)
      .then((data) => {
        refModalCheckBaoHiem.current.show({
          show: true,
          data: data,
          hoTen: record.tenNb,
          diaChi: record.diaChi,
        });
      })
      .catch((e) => {});
  };

  const columns = [
    Column({
      title: t("common.stt"),
      width: widthSttCol(),
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
    }),
    Column({
      title: t("sinhHieu.ngayDangKy"),
      sort_key: "thoiGianVaoVien",
      dataSort: dataSortColumn["thoiGianVaoVien"] || "",
      onClickSort: onClickSort,
      width: "150px",
      dataIndex: "thoiGianVaoVien",
      key: "thoiGianVaoVien",
      i18Name: "sinhHieu.ngayDangKy",
      align: "left",
      render: (field, item, index) =>
        field ? moment(field).format("DD / MM / YYYY") : "",
    }),
    Column({
      title: t("common.maHs"),
      sort_key: "maHoSo",
      dataSort: dataSortColumn["maHoSo"] || "",
      onClickSort: onClickSort,
      width: "150px",
      dataIndex: "maHoSo",
      key: "maHoSo",
      align: "left",
      i18Name: "common.maHs",
    }),
    Column({
      title: t("common.tenNb"),
      sort_key: "tenNb",
      dataSort: dataSortColumn["tenNb"] || "",
      onClickSort: onClickSort,
      width: "250px",
      dataIndex: "tenNb",
      key: "tenNb",
      i18Name: "common.tenNb",
      render: (field, item, index) => {
        return (
          <div style={{ display: "flex", width: "100%" }}>
            <label>{field}</label>
            {item.phanLoaiChiSoSong == PHAN_LOAI_CHI_SO_SONG.BAT_THUONG && (
              <div style={{ marginLeft: "auto" }}>
                <Tooltip title={t("sinhHieu.nbCoChiSoSongBatThuong")}>
                  <SVG.IcCanhBaoCss className="ic-action" />
                </Tooltip>
              </div>
            )}
          </div>
        );
      },
    }),
    Column({
      title: t("common.maNb"),
      sort_key: "maNb",
      dataSort: dataSortColumn["maNb"] || "",
      onClickSort: onClickSort,
      width: "150px",
      dataIndex: "maNb",
      key: "maNb",
      i18Name: "common.maNb",
      render: (field, item, index) => {
        return <div>{item?.maNb}</div>;
      },
    }),
    Column({
      title: t("common.ngaySinh"),
      sort_key: "ngaySinh",
      dataSort: dataSortColumn["ngaySinh"] || "",
      onClickSort: onClickSort,
      width: "150px",
      dataIndex: "ngaySinh",
      key: "ngaySinh",
      align: "left",
      i18Name: "common.ngaySinh",
      render: (field, item) => {
        const _ngaySinh = field
          ? moment(field).format(item.chiNamSinh ? "YYYY" : "DD/MM/YYYY")
          : "";
        const _tuoi = item.tuoi2 || "";

        return <div>{`${_ngaySinh}${_tuoi && ` - ${_tuoi}`}`}</div>;
      },
    }),
    Column({
      title: t("common.diaChi"),
      sort_key: "diaChi",
      dataSort: dataSortColumn["diaChi"] || "",
      onClickSort: onClickSort,
      width: "250px",
      dataIndex: "diaChi",
      key: "diaChi",
      i18Name: "common.diaChi",
    }),
    Column({
      title: t("common.soDienThoai"),
      sort_key: "soDienThoai",
      dataSort: dataSortColumn["soDienThoai"] || "",
      onClickSort: onClickSort,
      width: "150px",
      dataIndex: "soDienThoai",
      key: "soDienThoai",
      i18Name: "common.soDienThoai",
    }),
    Column({
      title: t("sinhHieu.maTheBhyt"),
      sort_key: "maTheBhyt",
      dataSort: dataSortColumn["maTheBhyt"] || "",
      onClickSort: onClickSort,
      width: "150px",
      dataIndex: "maTheBhyt",
      key: "maTheBhyt",
      i18Name: "sinhHieu.maTheBhyt",
    }),
    Column({
      title: t("sinhHieu.trangThai"),
      sort_key: "trangThaiChiSoSong",
      dataSort: dataSortColumn["trangThaiChiSoSong"] || "",
      onClickSort: onClickSort,
      width: "150px",
      dataIndex: "trangThaiChiSoSong",
      key: "trangThaiChiSoSong",
      i18Name: "sinhHieu.trangThai",
      render: (item) => TRANG_THAI_DO_SINH_HIEU.find((x) => x.id === item)?.ten,
    }),
    Column({
      title: t("dashboard.phongKham"),
      sort_key: "phongThucHienId",
      dataSort: dataSortColumn["phongThucHienId"] || "",
      onClickSort: onClickSort,
      width: "150px",
      dataIndex: "phongThucHienId",
      key: "phongThucHienId",
      i18Name: "dashboard.phongKham",
      hidden: dataSearch.theoPhongKham == "false" ? true : false,
      render: (item) => listPhong.find((x) => x.id === item)?.ten,
    }),
    Column({
      title: t("quanLyNoiTru.giaHanThe.nguoiChuyenDoi"),
      sort_key: "tenNguoiThucHienTheBh",
      dataSort: dataSortColumn["tenNguoiThucHienTheBh"] || "",
      onClickSort: onClickSort,
      width: "150px",
      dataIndex: "tenNguoiThucHienTheBh",
      key: "tenNguoiThucHienTheBh",
      align: "left",
      i18Name: "quanLyNoiTru.giaHanThe.nguoiChuyenDoi",
    }),
    Column({
      title: t("quanLyNoiTru.giaHanThe.ngayChuyenDoi"),
      sort_key: "thoiGianThucHienTheBh",
      dataSort: dataSortColumn["thoiGianThucHienTheBh"] || "",
      onClickSort: onClickSort,
      width: "150px",
      dataIndex: "thoiGianThucHienTheBh",
      key: "thoiGianThucHienTheBh",
      i18Name: "quanLyNoiTru.giaHanThe.ngayChuyenDoi",
      align: "left",
      render: (field, item, index) =>
        field ? moment(field).format("DD/MM/YYYY HH:mm:ss") : "",
    }),
    Column({
      title: (
        <>
          {t("common.tienIch")}
          <Setting refTable={refSettings} />
        </>
      ),
      width: "150px",
      align: "center",
      fixed: "right",
      ignore: true,
      render: (item, data) => {
        return (
          <div className="action">
            {data?.maThe && (
              <Tooltip title={t("tiepDon.layDLSinhHieu")}>
                <SVG.IcReload
                  onClick={onClickButtonSinhHieu(data)}
                  className="ic-action"
                />
              </Tooltip>
            )}
            {data?.maHoSoNgoaiVien !== null && (
              <Tooltip title={t("khamBenh.vienGut.doSinhHieuGut")}>
                <SVG.IcDoSinhHieuGut
                  color={"var(--color-blue-primary)"}
                  onClick={onDoSinhHieuGut(data)}
                  className="ic-action"
                />
              </Tooltip>
            )}
            <Tooltip title={t("sinhHieu.nhapChiSo")}>
              <SVG.IcNhapChiSo
                onClick={onChiTietNb(item)}
                className="ic-action"
              />
            </Tooltip>
            {data?.maTheBhyt && (
              <Tooltip title={t("tiepDon.kiemTraTheBh")}>
                <SVG.IcTheBaoHiem
                  onClick={onCheckThe(data)}
                  className="ic-action"
                />
              </Tooltip>
            )}
            <Tooltip title={t("phaCheThuoc.suaThongTinNb")}>
              <SVG.IcEdit onClick={onEdit(item)} className="ic-action" />
            </Tooltip>
          </div>
        );
      },
    }),
  ];

  const onShowSizeChange = (size) => {
    onSizeChange({ size });
  };

  const refreshList = () => {
    searchSinhHieuByParams({});
  };

  const onRow = (record) => {
    return {
      onClick: (event) => {
        const selection = window.getSelection();
        if (selection.type !== "Range") {
          onChiTietNb(record)(event);
        }
      },
    };
  };

  return (
    <Main noPadding={true}>
      <TableWrapper
        columns={columns}
        dataSource={listData}
        onRow={onRow}
        // rowKey={(record) => `${record.id}`}
        tableName="table_SINHHIEU_DSNGUOIBENH"
        ref={refSettings}
        loading={!!isLoading}
      />
      <Pagination
        onChange={onChangePage}
        current={page + 1}
        pageSize={size}
        listData={listData}
        total={totalElements}
        onShowSizeChange={onShowSizeChange}
        first={first}
        last={last}
        isLoading={isLoading}
      />

      <ModalDoSinhHieu
        isSinhHieu
        refreshList={refreshList}
        ref={refModalDoSinhHieu}
      />
      <ModalChinhSuaThongTin ref={refModalChinhSuaThongTin} />
      <ModalCheckBaoHiem
        ref={refModalCheckBaoHiem}
        isShowButtonOk={false}
        isShowButtonCancel={true}
      />
    </Main>
  );
};

export default DanhSach;
