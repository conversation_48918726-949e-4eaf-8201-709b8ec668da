import React, { useEffect, useMemo, useRef, useState } from "react";
import { MainPage, Main } from "./styled";
import { useTranslation } from "react-i18next";
import { Row, Col, Menu } from "antd";
import { Dropdown, But<PERSON>, Card, Tabs, ThongTinBenh<PERSON>han } from "components";
import { useDispatch } from "react-redux";
import { useConfirm, useLoading, useQueryString, useStore } from "hooks";
import { useParams, useLocation } from "react-router-dom";
import { setQueryStringValue } from "hooks/useQueryString/queryString";
import ThongTinBenhAn from "./ThongTinBenhAn";
import ModalSuaBA from "../components/ModalSuaBA";
import {
  MA_BIEU_MAU_EDITOR,
  ROLES,
  TRANG_THAI_NB,
  LOAI_DICH_VU,
} from "constants/index";
import { openInNewTab } from "utils";
import { printJS } from "data-access/print-provider";
import { SVG } from "assets";
import ModalHoanThanhBA from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalHoanThanhBA";
import ModalLapBADaiHan from "pages/khamBenh/components/StepWrapper/ModalLapBADaiHan";
import ModalDienBienChiSoXetNghiem from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalDienBienChiSoXetNghiem";
import { checkIsPhieuKySo } from "utils/phieu-utils";
import ThongTinVoChong from "./ThongTinVoChong";
import ModalInPhieuChamSoc from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalInPhieuChamSoc";
import { checkRole } from "lib-utils/role-utils";
import ModalInToDieuTriNhieuNgay from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ToDieuTri/modals/ModalInToDieuTriNhieuNgay";
import ModalChonPhieuCongKhaiThuoc from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ToDieuTri/modals/ModalChonPhieuCongKhaiThuoc";
import ModalChonPhieuCongKhaiVtyt from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ToDieuTri/modals/ModalChonPhieuCongKhaiVtyt";
import ModalChonPhieuCongKhaiThuoc2 from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ToDieuTri/modals/ModalChonPhieuCongKhaiThuoc2";
import ModalChonPhieuTruyenDich from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ToDieuTri/modals/ModalChonPhieuTruyenDich";
import ModalInPhieuCongKhai from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalInPhieuCongKhai";

const ChiTietDieuTri = () => {
  const { showConfirm } = useConfirm();
  const { showLoading, hideLoading } = useLoading();

  const [tab] = useQueryString("tab", "0");
  const { t } = useTranslation();
  const { id: benhAnId, nbDotDieuTriId: id } = useParams();

  const refModalSuaBA = useRef(null);
  const refModalHoanThanhBA = useRef(null);
  const refModalLapBADaiHan = useRef(null);
  const refModalDienBienChiSoXetNghiem = useRef(null);
  const refModalInPhieuChamSoc = useRef(null);
  const refModalInToDieuTri = useRef(null);
  const refModalChonPhieuCongKhaiThuoc = useRef(null);
  const refModalChonPhieuCongKhaiVtyt = useRef(null);
  const refModalChonPhieuCongKhaiThuoc2 = useRef(null);
  const refModalChonPhieuTruyenDich = useRef(null);
  const refModalInPhieuCongKhai = useRef(null);

  const { state: { searchQueryString = "" } = {} } = useLocation();
  const {
    nbDotDieuTri: {
      getById,
      getThongTinRaVien,
      getByTongHopId,
      getThongTinCoBan,
    },
    nbMaBenhAn: { getDsMaBADaiHan, huyBenhAn, clearData },
    danhSachNguoiBenhNoiTru: { getNbNoiTruById },
    maBenh: { getListAllMaBenh },
    dieuTriDaiHan: { getNbDvKhamCmu },
    phieuIn: { getPhieuIn, showFileEditor, getFilePhieuIn },
    dsLuuTruBa: { getChiTietLuuTruBA },
    khoa: { getKhoaTheoTaiKhoan },
    quanLyNoiTru: { huyBenhAnDaiHan },
  } = useDispatch();

  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan");
  const chiTietLuuTru = useStore("dsLuuTruBa.chiTietLuuTru");
  const thongTinBenhNhan = useStore("nbDotDieuTri.thongTinBenhNhan");
  const chiTietNguoiBenhNoiTru = useStore(
    "danhSachNguoiBenhNoiTru.chiTietNguoiBenhNoiTru"
  );
  const listKhoaTheoTaiKhoan = useStore("khoa.listKhoaTheoTaiKhoan", []);

  const [state, _setState] = useState({
    activeKey: tab,
    listPhieu: [],
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    getListAllMaBenh({ active: true, page: "", size: "" });
    getKhoaTheoTaiKhoan({
      page: "",
      size: "",
      active: true,
    });

    return () => {
      clearData();
    };
  }, []);

  useEffect(() => {
    if (id) {
      getById(id);
      getNbNoiTruById(id).then((res) => {
        if (
          res?.trangThai &&
          [
            TRANG_THAI_NB.DA_THANH_TOAN_RA_VIEN,
            TRANG_THAI_NB.DA_RA_VIEN,
          ].includes(res?.trangThai)
        ) {
          getChiTietLuuTruBA(id);
        }
      });
      getThongTinRaVien(id);
      getByTongHopId(id);
      getNbDvKhamCmu(id);
    }
  }, [id]);

  useEffect(() => {
    if (thongTinCoBan.nbThongTinId) {
      getDsMaBADaiHan(thongTinCoBan.nbThongTinId);
    }
  }, [thongTinCoBan]);

  useEffect(() => {
    onChange(tab);
  }, [tab]);

  useEffect(() => {
    if (id) {
      getPhieuIn({
        nbDotDieuTriId: id,
        maManHinh: "016",
        maViTri: "01601",
      }).then((listPhieu) => {
        setState({
          listPhieu: listPhieu,
          show: true,
          data: {
            nbDotDieuTriId: id,
            maManHinh: "016",
            maViTri: "01601",
          },
        });
      });
    }
  }, [id]);

  const onPrintPhieu = (item) => async () => {
    if (item.type == "editor") {
      const lichSuKyId = item?.dsSoPhieu?.length
        ? item?.dsSoPhieu[0].lichSuKyId
        : "";
      let mhParams = {};
      if (checkIsPhieuKySo(item)) {
        mhParams = {
          nbDotDieuTriId: id,
          maManHinh: "016",
          maViTri: "01601",
          kySo: true,
          maPhieuKy: item.ma,
        };
      }

      if (["P136", "P185", "P514", "P558"].includes(item.ma)) {
        //Lọc bỏ phiếu có số phiếu = nbDotDieuTriId
        const _dsSoPhieu = (item.dsSoPhieu || []).filter(
          (x) => x.soPhieu != id
        );
        //Phiếu chăm sóc nặng và chăm sóc nhẹ
        refModalInPhieuChamSoc.current &&
          refModalInPhieuChamSoc.current.show(
            {
              khoaLamViec: { id: thongTinBenhNhan.khoaId },
              dsSoPhieu: _dsSoPhieu,
              ma: item.ma,
              tenPhieu: item.ten,
            },
            (data) => {
              const { thoiGianThucHien, khoaChiDinhId, id: idPhieu } = data;

              showFileEditor({
                phieu: item,
                id: idPhieu,
                nbDotDieuTriId: id,
                ma: item.ma,
                maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                  ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                  : "",
                chiDinhTuDichVuId: id,
                khoaChiDinhId,
                thoiGianThucHien,
                mhParams,
              });
            }
          );
      } else if (item.ma == "P183") {
        const _dsSoPhieu = (item.dsSoPhieu || []).filter(
          (x) =>
            x.soPhieu != id &&
            x.soPhieu != "null" &&
            x.baoCaoId == item.baoCaoId
        );
        //Phiếu thực hiện và công khai thuốc
        refModalChonPhieuCongKhaiThuoc2.current &&
          refModalChonPhieuCongKhaiThuoc2.current.show(
            {
              khoaChiDinhId: thongTinBenhNhan.khoaId,
              dsSoPhieu: _dsSoPhieu,
              ma: item.ma,
            },
            (filterData) => {
              showFileEditor({
                phieu: item,
                id: filterData?.id,
                nbDotDieuTriId: id,
                ma: item.ma,
                maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                  ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                  : "",
                chiDinhTuDichVuId: id,
                chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
                khoaChiDinhId: filterData?.khoaChiDinhId,
                tuThoiGian: filterData?.tuThoiGian,
                denThoiGian: filterData?.denThoiGian,
                dsLoaiChiDinh: filterData?.dsLoaiChiDinh,
                mhParams,
              });
            }
          );
      } else if (item.ma == "P606") {
        //Phiếu theo dõi truyền dịch
        refModalChonPhieuTruyenDich.current &&
          refModalChonPhieuTruyenDich.current.show(
            {
              khoaChiDinhId: thongTinBenhNhan.khoaId,
              dsSoPhieu: item.dsSoPhieu || [],
            },
            (filterData) => {
              showFileEditor({
                phieu: item,
                id: filterData?.id,
                nbDotDieuTriId: id,
                ma: item.ma,
                maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                  ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                  : "",
                khoaChiDinhId: filterData?.khoaChiDinhId,
                tuThoiGian: filterData?.tuThoiGian,
                denThoiGian: filterData?.denThoiGian,
                mhParams,
              });
            }
          );
      } else if (item.ma == "P539") {
        refModalChonPhieuCongKhaiThuoc.current &&
          refModalChonPhieuCongKhaiThuoc.current.show(
            {
              thoiGianYLenh: new Date(),
              khoaChiDinhId: thongTinBenhNhan.khoaId,
              listDanhSachKhoa: listKhoaTheoTaiKhoan,
            },
            (filterData) => {
              showFileEditor({
                phieu: item,
                id: id,
                nbDotDieuTriId: id,
                ma: item.ma,
                maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                  ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                  : "",
                khoaChiDinhId: filterData?.khoaChiDinhId,
                thoiGianYLenh: filterData?.thoiGianYLenh,
                mhParams,
              });
            }
          );
      } else if (item.ma == "P197") {
        //Phiếu thực hiện và công khai vật tư y tế tiêu hao
        refModalChonPhieuCongKhaiVtyt.current &&
          refModalChonPhieuCongKhaiVtyt.current.show(
            {
              khoaChiDinhId: thongTinBenhNhan.khoaId,
              dsSoPhieu: item.dsSoPhieu || [],
              ma: item.ma,
            },
            (filterData) => {
              showFileEditor({
                phieu: item,
                id: filterData?.id,
                nbDotDieuTriId: id,
                ma: item.ma,
                maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                  ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                  : "",
                chiDinhTuDichVuId: id,
                chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
                khoaChiDinhId: filterData?.khoaChiDinhId,
                tuThoiGian: filterData?.tuThoiGian,
                denThoiGian: filterData?.denThoiGian,
                mhParams,
              });
            }
          );
      } else if (item.ma == "P184") {
        refModalInToDieuTri.current &&
          refModalInToDieuTri.current.show({
            nbDotDieuTriId: id,
            khoaId: chiTietNguoiBenhNoiTru?.khoaNbId,
            maBaoCao: item.ma,
            chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
            mhParams: { ...mhParams, ngoaiTru: true },
            isInDieuTriDaiHan: true,
            baoCaoId: item.baoCaoId,
          });
      } else {
        showFileEditor({
          phieu: item,
          nbDotDieuTriId: id,
          nbThongTinId: thongTinCoBan.nbThongTinId,
          id,
          ma: item.ma,
          maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
            ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
            : "",
          lichSuKyId,
          mhParams,
        });
      }
    } else if (item.ma == "P092") {
      //Phiếu công khai dịch vụ khám, chữa bệnh nội trú
      refModalInPhieuCongKhai.current &&
        refModalInPhieuCongKhai.current.show(
          {
            khoaLamViec: { id: thongTinBenhNhan.khoaId },
            dsSoPhieu: item.dsSoPhieu || [],
          },
          async (data) => {
            const {
              tuThoiGian,
              denThoiGian,
              dsNhomDichVuCap1Id,
              khoaChiDinhId,
              dsLoaiDichVu,
              id: idPhieu,
            } = data;

            let mhParams = {};
            //kiểm tra phiếu ký số
            if (checkIsPhieuKySo(item)) {
              //nếu là ký số thì lấy các thông tin về lịch sử ký + tên chân ký + báo cáo id
              mhParams = {
                maManHinh: "016",
                maViTri: "01601",
                chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
                kySo: true,
                maPhieuKy: item.ma,
                nbDotDieuTriId: id,
              };
            }

            showFileEditor({
              phieu: item,
              id: idPhieu,
              nbDotDieuTriId: id,
              ma: item.ma,
              maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                : "",
              chiDinhTuDichVuId: id,
              khoaChiDinhId,
              tuThoiGian,
              denThoiGian,
              dsLoaiDichVu,
              dsNhomDichVuCap1Id,
              mhParams,
            });
          }
        );
    } else {
      try {
        showLoading();

        const { finalFile, dsPhieu } = await getFilePhieuIn({
          listPhieus: [item],
          nbDotDieuTriId: id,
          showError: true,
        });

        if ((dsPhieu || []).every((x) => x?.loaiIn == 20)) {
          openInNewTab(finalFile);
        } else {
          printJS({
            printable: finalFile,
            type: "pdf",
          });
        }
      } catch (error) {
        console.log("error", error);
      } finally {
        hideLoading();
      }
    }
  };

  const menu = useMemo(() => {
    return (
      <Menu
        items={(state.listPhieu || []).map((item, index) => ({
          key: index,
          label: (
            <a href={() => false} onClick={onPrintPhieu(item)}>
              {item.ten || item.tenBaoCao}
            </a>
          ),
        }))}
      />
    );
  }, [state.listPhieu]);

  const listTabs = [
    {
      name: t("quanLyNoiTru.thongTinBenhAn"),
      component: <ThongTinBenhAn />,
      iconTab: <SVG.IcVatTu />,
      isShow: true,
    },
    ...(checkRole([ROLES["DIEU_TRI_DAI_HAN"].BENH_AN_VO_CHONG])
      ? [
          {
            name: t("dieuTriDaiHan.voChong"),
            title: t("dieuTriDaiHan.lichSuKhamBenhVoChong"),
            component: <ThongTinVoChong />,
            iconTab: <SVG.IcEdit />,
            isShow: true,
          },
        ]
      : []),
  ];

  const onChange = (tab) => {
    setState({ activeKey: tab });
    setQueryStringValue("tab", tab);
  };

  const onEditBA = () => {
    refModalSuaBA.current &&
      refModalSuaBA.current.show({}, () => {
        getById(id);
        getNbNoiTruById(id);
      });
  };

  const onHuyBA = () => {
    showConfirm(
      {
        title: t("quanLyNoiTru.huyBenhAn"),
        content: t("quanLyNoiTru.banCoChacChanMuonHuyBenhAn"),
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        classNameOkText: "button-warning",
        showImg: true,
        showBtnOk: true,
        typeModal: "warning",
      },
      () => {
        showLoading();
        huyBenhAn(benhAnId)
          .then(() => getById(id))
          .finally(hideLoading);
      }
    );
  };

  const onHuyGanBaDaiHan = () => {
    showConfirm(
      {
        title: t("quanLyNoiTru.xoaBenhAn"),
        content: t("quanLyNoiTru.banCoChacChanMuonXoaBenhAn"),
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        classNameOkText: "button-warning",
        showImg: true,
        showBtnOk: true,
        typeModal: "warning",
      },
      () => {
        huyBenhAnDaiHan(id).then(() => {
          getById(id);
          getNbNoiTruById(id);
          getThongTinCoBan(id);
        });
      }
    );
  };

  const onHoanThanhBa = () => {
    refModalHoanThanhBA.current && refModalHoanThanhBA.current.show();
  };

  const onLapBenhAn = () => {
    refModalLapBADaiHan.current &&
      refModalLapBADaiHan.current.show({
        nbDotDieuTriId: thongTinBenhNhan.id,
        nbDotDieuTri: thongTinBenhNhan,
      });
  };

  const onShowDienBienChiSoXetNghiem = () => {
    refModalDienBienChiSoXetNghiem.current &&
      refModalDienBienChiSoXetNghiem.current.show();
  };

  return (
    <MainPage
      breadcrumb={[
        {
          link: "/dieu-tri-dai-han",
          title: t("dieuTriDaiHan.dieuTriDaiHan"),
        },
        {
          link:
            "/dieu-tri-dai-han/danh-sach-nb-dieu-tri-dai-han" +
            searchQueryString,
          title: t("dieuTriDaiHan.danhSachNBDieuTriDaiHan"),
        },
        {
          link: window.location.pathname,
          title: t("dieuTriDaiHan.chiTietBenhAn"),
        },
      ]}
      actionLeft={
        <>
          {[
            TRANG_THAI_NB.DA_THANH_TOAN_RA_VIEN,
            TRANG_THAI_NB.DA_RA_VIEN,
          ].includes(thongTinCoBan.trangThaiNb) && (
            <>
              {[20, 40].includes(chiTietLuuTru?.trangThaiBenhAn) && (
                <Button onClick={() => onHoanThanhBa()}>
                  {t("quanLyNoiTru.huyHoanThanhBa")}
                </Button>
              )}

              {[10].includes(chiTietLuuTru?.trangThaiBenhAn) && (
                <Button onClick={() => onHoanThanhBa()}>
                  {t("quanLyNoiTru.hoanThanhBa")}
                </Button>
              )}
            </>
          )}
        </>
      }
      actionRight={
        <>
          <Button onClick={onShowDienBienChiSoXetNghiem}>
            {t("quanLyNoiTru.dienBienChiSoXetNghiem")}
          </Button>

          {((!thongTinBenhNhan?.maBenhAn &&
            thongTinBenhNhan.trangThai !== TRANG_THAI_NB.MOI_TIEP_DON) ||
            (thongTinBenhNhan?.maBenhAn &&
              thongTinBenhNhan.trangThai !== TRANG_THAI_NB.HUY_BENH_AN)) && (
            <Button minWidth={100} onClick={onHuyGanBaDaiHan}>
              {t("quanLyNoiTru.huyGanBaDaiHan")}
            </Button>
          )}
          {((!thongTinBenhNhan?.maBenhAn &&
            thongTinBenhNhan.trangThai !== TRANG_THAI_NB.MOI_TIEP_DON) ||
            (thongTinBenhNhan?.maBenhAn &&
              thongTinBenhNhan.trangThai !== TRANG_THAI_NB.HUY_BENH_AN)) && (
            <Button minWidth={100} onClick={onHuyBA}>
              {t("quanLyNoiTru.huyBenhAn")}
            </Button>
          )}
          {thongTinBenhNhan.trangThai === TRANG_THAI_NB.HUY_BENH_AN && (
            <Button minWidth={100} onClick={onLapBenhAn}>
              {t("dieuTriDaiHan.lapBenhAn")}
            </Button>
          )}
          {((!thongTinBenhNhan?.maBenhAn &&
            thongTinBenhNhan.trangThai !== TRANG_THAI_NB.MOI_TIEP_DON) ||
            (thongTinBenhNhan?.maBenhAn &&
              thongTinBenhNhan.trangThai !== TRANG_THAI_NB.HUY_BENH_AN)) && (
            <Button minWidth={100} onClick={onEditBA}>
              {t("dieuTriDaiHan.suaThongTin")}
            </Button>
          )}
          <Dropdown overlay={menu} trigger={["click"]}>
            <Button type="primary" minWidth={100}>
              {t("common.inPhieu")}
            </Button>
          </Dropdown>
        </>
      }
      title={t("dieuTriDaiHan.chiTietBenhAn")}
    >
      <Main>
        <Row>
          <Col className="header-left" span={24}>
            <ThongTinBenhNhan
              style={{ marginTop: 0 }}
              nbDotDieuTriId={id}
              isDieuTriDaiHan={true}
              isShowThongTinDieuTriLao={true}
            />
          </Col>
        </Row>

        <Card className="content" bottom={50}>
          <Tabs.Left
            defaultActiveKey={state.activeKey}
            tabPosition={"left"}
            tabWidth={220}
            type="card"
            className={`tab-main ${
              state.collapse ? "collapse-tab" : "show-more"
            }`}
            onChange={onChange}
          >
            {listTabs.map((obj, i) => {
              return (
                <Tabs.TabPane
                  key={i}
                  tab={
                    <div>
                      {obj?.iconTab}
                      {obj?.name}
                    </div>
                  }
                  disabled={!obj.isShow}
                >
                  <Tabs.TabBox
                    title={obj?.title || obj?.name}
                    fixHeight={obj?.fixHeight}
                  >
                    {obj?.component}
                  </Tabs.TabBox>
                </Tabs.TabPane>
              );
            })}
          </Tabs.Left>
        </Card>
      </Main>

      <ModalSuaBA ref={refModalSuaBA} />
      <ModalHoanThanhBA ref={refModalHoanThanhBA} />
      <ModalLapBADaiHan ref={refModalLapBADaiHan} />
      <ModalDienBienChiSoXetNghiem ref={refModalDienBienChiSoXetNghiem} />
      <ModalInPhieuChamSoc ref={refModalInPhieuChamSoc} />
      <ModalInToDieuTriNhieuNgay ref={refModalInToDieuTri} />

      <ModalChonPhieuCongKhaiVtyt ref={refModalChonPhieuCongKhaiVtyt} />
      <ModalChonPhieuCongKhaiThuoc ref={refModalChonPhieuCongKhaiThuoc} />
      <ModalChonPhieuCongKhaiThuoc2 ref={refModalChonPhieuCongKhaiThuoc2} />
      <ModalChonPhieuTruyenDich ref={refModalChonPhieuTruyenDich} />
      <ModalInPhieuCongKhai ref={refModalInPhieuCongKhai} />
    </MainPage>
  );
};

export default ChiTietDieuTri;
