import React, {
  useEffect,
  useState,
  forwardRef,
  useImperativeHandle,
  useRef,
} from "react";
import { Form, Input } from "antd";
import { Main } from "./styled";
import { DatePicker, Button, ModalTemplate, Select } from "components";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import FormWraper from "components/FormWraper";
import moment from "moment";
import { useLoading, useStore, useEnum } from "hooks";
import { ENUM, LOAI_MUON } from "constants/index";

const { RangePicker } = DatePicker;

const ModalChoMuonBA = (props, ref) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const refModal = useRef(null);
  const refCallback = useRef(null);
  const { showLoading, hideLoading } = useLoading();

  const {
    dsLuuTruBa: { muonBA, getChiTietLuuTruBA },
    nhanVien: { getListAllNhanVien, getById },
    khoa: { getListAllKhoa },
  } = useDispatch();
  const listAllNhanVien = useStore("nhanVien.listAllNhanVien", []);
  const listAllKhoa = useStore("khoa.listAllKhoa");

  const [listLoaiMuon] = useEnum(ENUM.LOAI_MUON);

  const [state, _setState] = useState({
    show: false,
    showThoiGianMuonGiamDinh: false,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  useEffect(() => {
    getListAllNhanVien({ page: "", size: "", active: true });
    getListAllKhoa({ page: "", size: "", active: true });
  }, []);

  useImperativeHandle(ref, () => ({
    show: ({ data, listData }, callback) => {
      setState({ show: true, data, listData });
      const {
        nguoiMuon,
        lyDoMuon,
        mucDich,
        soDienThoaiMuonBa,
        khoaMuon,
        thoiGianMuon,
      } = data || {};

      form.setFieldsValue({
        nguoiMuon,
        lyDoMuon,
        mucDich,
        soDienThoaiMuonBa,
        khoaMuon,
        loaiMuon: LOAI_MUON.MUON_KHOA_PHONG,
        thoiGianMuon: thoiGianMuon ? moment(thoiGianMuon) : moment(),
      });

      refCallback.current = callback;
    },
  }));

  const onClose = () => {
    form.resetFields();
    setState({
      show: false,
      data: null,
      listData: null,
      showThoiGianMuonGiamDinh: false,
    });
  };

  const onChoMuonBa = async () => {
    try {
      showLoading();

      const values = await form.validateFields();
      const {
        nguoiMuon,
        lyDoMuon,
        mucDich,
        soDienThoaiMuonBa,
        khoaMuon,
        thoiGianMuon,
        loaiMuon,
        thoiGianMuonGiamDinh,
      } = values;

      const tenNguoiMuon =
        listAllNhanVien.find((x) => x.id == nguoiMuon)?.ten || "";

      const payload = state.listData
        ? state.listData.map((x) => ({
            ...x,
            nguoiMuon: tenNguoiMuon,
            nguoiMuonId: nguoiMuon,
            lyDoMuon,
            mucDich,
            soDienThoaiMuonBa,
            khoaMuon,
            thoiGianMuon,
            loaiMuon,
            ...(loaiMuon === LOAI_MUON.MUON_GIAM_DINH_BAO_HIEM
              ? {
                  tuNgay: thoiGianMuonGiamDinh
                    ? thoiGianMuonGiamDinh[0].format("YYYY-MM-DD 00:00:00")
                    : null,
                  denNgay: thoiGianMuonGiamDinh
                    ? thoiGianMuonGiamDinh[1].format("YYYY-MM-DD 23:59:59")
                    : null,
                }
              : {}),
          }))
        : [
            {
              ...state.data,
              nguoiMuon: tenNguoiMuon,
              nguoiMuonId: nguoiMuon,
              lyDoMuon,
              mucDich,
              soDienThoaiMuonBa,
              khoaMuon,
              thoiGianMuon,
              loaiMuon,
              ...(loaiMuon === LOAI_MUON.MUON_GIAM_DINH_BAO_HIEM
                ? {
                    tuNgay: thoiGianMuonGiamDinh
                      ? thoiGianMuonGiamDinh[0].format("YYYY-MM-DD 00:00:00")
                      : null,
                    denNgay: thoiGianMuonGiamDinh
                      ? thoiGianMuonGiamDinh[1].format("YYYY-MM-DD 23:59:59")
                      : null,
                  }
                : {}),
            },
          ];

      await muonBA(payload);
      getChiTietLuuTruBA(state.data?.id);
      if (refCallback.current) refCallback.current();
      onClose();
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onFinish = (values) => {
    console.log("Success:", values);
  };

  const onFinishFailed = (errorInfo) => {
    console.log("Failed:", errorInfo);
  };

  const onValuesChange = (changedValues, allValues) => {
    if (changedValues.nguoiMuon) {
      getById(changedValues.nguoiMuon).then((s) => {
        form.setFieldsValue({ soDienThoaiMuonBa: s?.data?.soDienThoai || "" });
      });
    }
    if (changedValues.loaiMuon) {
      setState({
        showThoiGianMuonGiamDinh:
          changedValues.loaiMuon === LOAI_MUON.MUON_GIAM_DINH_BAO_HIEM,
      });
    }
  };

  return (
    <ModalTemplate
      width={600}
      closable={true}
      ref={refModal}
      title={t("hsba.choMuonBenhAn")}
      onCancel={onClose}
      actionLeft={
        <Button.QuayLai
          onClick={() => {
            onClose();
          }}
        />
      }
      actionRight={
        <>
          <Button type="primary" onClick={onChoMuonBa}>
            {t("common.luu")}
          </Button>
        </>
      }
    >
      <Main>
        <FormWraper
          name="basic"
          initialValues={{ remember: true }}
          onFinish={onFinish}
          layout={"vertical"}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          form={form}
          onValuesChange={onValuesChange}
        >
          <Form.Item
            label={t("hsba.nguoiMuon")}
            name="nguoiMuon"
            rules={[
              {
                required: true,
                message: t("hsba.vuiLongChonNguoiMuon"),
              },
            ]}
          >
            <Select
              valueNumber={true}
              data={listAllNhanVien}
              placeholder={t("hsba.chonNguoiMuon")}
            />
          </Form.Item>

          <Form.Item label={t("hsba.khoaMuon")} name="khoaMuon">
            <Select
              valueNumber={true}
              data={listAllKhoa}
              placeholder={t("hsba.chonKhoaMuon")}
            />
          </Form.Item>

          <Form.Item label={t("hsba.soDienThoai")} name="soDienThoaiMuonBa">
            <Input placeholder={t("hsba.nhapSoDienThoai")} />
          </Form.Item>

          <Form.Item
            label={t("hsba.ngayMuon")}
            name="thoiGianMuon"
            rules={[
              {
                required: true,
                message: t("hsba.vuiLongChonNgayMuon") + "!",
              },
            ]}
          >
            <DatePicker
              showTime={{ format: "HH:mm:ss" }}
              placeholder={t("hsba.chonThoiGianMuon")}
              disabled={true}
            />
          </Form.Item>

          <Form.Item
            label={t("hsba.lyDoMuon")}
            name="lyDoMuon"
            rules={[
              {
                required: true,
                message: t("hsba.vuiLongNhapLyDoMuon"),
              },
            ]}
          >
            <Input placeholder={t("hsba.nhapLyDoMuon")} />
          </Form.Item>

          <Form.Item
            label={t("khth.loaiChoMuon")}
            name="loaiMuon"
            rules={[
              {
                required: true,
                message: t("khth.vuiLongChonLoaiChoMuon") + "!",
              },
            ]}
          >
            <Select
              valueNumber={true}
              data={listLoaiMuon}
              placeholder={t("khth.chonLoaiChoMuon")}
            />
          </Form.Item>

          {state.showThoiGianMuonGiamDinh && (
            <Form.Item
              label={t("khth.muonGiamDinhTuNgayDenNgay")}
              name="thoiGianMuonGiamDinh"
            >
              <RangePicker
                placeholder={[t("common.tuNgay"), t("common.denNgay")]}
                format={"DD/MM/YYYY"}
              />
            </Form.Item>
          )}

          <Form.Item label={t("hsba.mucDich")} name="mucDich">
            <Input placeholder={t("hsba.nhapMucDich")} />
          </Form.Item>
        </FormWraper>
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalChoMuonBA);
