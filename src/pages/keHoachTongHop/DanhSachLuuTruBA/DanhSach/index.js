import React, { useEffect, useMemo, useRef, useState } from "react";
import {
  TableWrapper,
  HeaderSearch,
  Pagination,
  Checkbox,
  Tooltip,
  EllipsisText,
} from "components";
import { Main } from "./styled";
import { useDispatch, useSelector } from "react-redux";
import { useHistory } from "react-router-dom";
import moment from "moment";
import { useTranslation } from "react-i18next";
import { useEnum } from "hooks";
import { ENUM, ROLES, THEME_KEY } from "constants/index";
import { message } from "antd";
import { SVG } from "assets";
import { getAllQueryString } from "hooks/useQueryString/queryString";
import { DS_TRANG_THAI_BA_GOP, LOAI_LUU_TRU_BENH_AN_DIEN_TU } from "../../components/config";
import { checkRole } from "lib-utils/role-utils";

const { Column, Setting } = TableWrapper;

const DanhSachLuuTru = ({ parentState, setParentState, onViewHsbaDuPhong }) => {
  const { t } = useTranslation();

  const refSettings = useRef(null);

  const [listTrangThaiBenhAn] = useEnum(ENUM.TRANG_THAI_BENH_AN);
  const [listDoiTuongKcb] = useEnum(ENUM.DOI_TUONG_KCB);
  const [listTrangThaiNb] = useEnum(ENUM.TRANG_THAI_NB);
  const [listHuongDieuTriNoiTru] = useEnum(ENUM.HUONG_DIEU_TRI_NOI_TRU);
  const [listHuongDieuTriKham] = useEnum(ENUM.HUONG_DIEU_TRI_KHAM);
  const [listKetQuaDieuTri] = useEnum(ENUM.KET_QUA_DIEU_TRI);
  const [listLoaiLuuTru] = useEnum(ENUM.LOAI_LUU_TRU);
  const [listDoiTuong] = useEnum(ENUM.DOI_TUONG);

  const {
    listDsLuuTru = [],
    totalElements,
    page,
    size,
    dataSortColumn,
    isLoading: loading,
  } = useSelector((state) => state.dsLuuTruBa);
  const {
    dsLuuTruBa: {
      onSizeChange,
      onSortChange,
      onSearch: onSearchLuuTruBa,
      updateData,
    },
  } = useDispatch();
  useEffect(() => {
    const {
      page,
      size,
      dataSortColumn = "{}",
      namSinh,
      ...queries
    } = getAllQueryString();
    let defaultState = {};

    if (queries.tuThoiGianVaoVien) {
      queries.tuThoiGianVaoVien = moment(
        queries.tuThoiGianVaoVien?.toDateObject()
      ).format("YYYY-MM-DD HH:mm:ss");
    }
    if (queries.denThoiGianVaoVien) {
      queries.denThoiGianVaoVien = moment(
        queries.denThoiGianVaoVien?.toDateObject()
      ).format("YYYY-MM-DD HH:mm:ss");
    }
    if (queries.tuThoiGianRaVien) {
      queries.tuThoiGianRaVien = moment(
        queries.tuThoiGianRaVien?.toDateObject()
      ).format("YYYY-MM-DD HH:mm:ss");
    }
    if (queries.denThoiGianRaVien) {
      queries.denThoiGianRaVien = moment(
        queries.denThoiGianRaVien?.toDateObject()
      ).format("YYYY-MM-DD HH:mm:ss");
    }
    if (queries.tuThoiGianLuuTru) {
      queries.tuThoiGianLuuTru = moment(
        queries.tuThoiGianLuuTru?.toDateObject()
      ).format("YYYY-MM-DD HH:mm:ss");
    }
    if (queries.denThoiGianLuuTru) {
      queries.denThoiGianLuuTru = moment(
        queries.denThoiGianLuuTru?.toDateObject()
      ).format("YYYY-MM-DD HH:mm:ss");
    }

    if (queries.dsTrangThaiBenhAn) {
      defaultState.dsTrangThaiBenhAnDefault = queries.dsTrangThaiBenhAn
        .split(",")
        .map(Number);

      let dsTrangThaiBenhAn = [];
      queries.dsTrangThaiBenhAn.split(",").map((item) => {
        const _dsTrangThaiGop =
          DS_TRANG_THAI_BA_GOP.find((x) => x.id == item)?.value || [];
        dsTrangThaiBenhAn = [
          ...new Set([...dsTrangThaiBenhAn, ..._dsTrangThaiGop]),
        ];
      });

      queries.dsTrangThaiBenhAn = dsTrangThaiBenhAn;
    }
    if (queries.dsKhoaNbId) {
      queries.dsKhoaNbId = queries.dsKhoaNbId.split(",").map(Number);
    }

    if (queries.doiTuongKcb) {
      queries.doiTuongKcb = Number(queries.doiTuongKcb);
    }
    if (queries.loaiLuuTru) {
      queries.loaiLuuTru = Number(queries.loaiLuuTru);
    }
    if (queries.doiTuong) {
      queries.doiTuong = Number(queries.doiTuong);
    }
    if (queries.luuTruBenhAnDienTu) {
      queries.luuTruBenhAnDienTu = Number(queries.luuTruBenhAnDienTu);
    }

    updateData({
      dataSearch: {
        ...queries,
        tuThoiGianSinh: namSinh
          ? moment(+namSinh).startOf("year").format("YYYY-MM-DD")
          : null,
        denThoiGianSinh: namSinh
          ? moment(+namSinh).endOf("year").format("YYYY-MM-DD")
          : null,
      },
    });
    onSizeChange({ size: 10 });
    setParentState({
      ...queries,
      ...defaultState,
      namSinh: namSinh ? moment(+namSinh) : null,
    });
  }, []);

  const [state, _setState] = useState({
    selectedRowKeys: [],
  });
  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };

  const onChangePage = (page) => {
    onSearchLuuTruBa({ page: page - 1 });
  };

  const handleSizeChange = (size) => {
    onSizeChange({ size });
  };

  const onClickSort = (key, value) => {
    onSortChange({ [key]: value });
  };

  const history = useHistory();

  const onRow = (record) => {
    return {
      onClick: (e) => {
        const selection = window.getSelection();
        const isSelection =
          selection.type === "Range" && selection.containsNode(e.target, true);
        if (
          !isSelection &&
          checkRole([ROLES["KE_HOACH_TONG_HOP"].CHI_TIET_LUU_TRU_BENH_AN])
        ) {
          const { id } = record;
          history.push({
            pathname: "/ke-hoach-tong-hop/chi-tiet-luu-tru-ba/" + id,
            state: getAllQueryString(),
          });
        }
      },
    };
  };

  const onViewDongGoiHsba = (record) => (e) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    history.push({
      pathname: "/ho-so-benh-an/dong-goi/" + record.id,
      state: { ...getAllQueryString(), fromToluuTruBA: true },
    });
  };

  const widthStt = () => {
    const num = String((page + 1) * size).length;
    return Number(num) * 8 + 30;
  };

  const columns = [
    Column({
      title: t("common.stt"),
      width: widthStt(),
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
    }),
    Column({
      title: t("hsba.hoTen"),
      width: "200px",
      dataIndex: "tenNb",
      key: "tenNb",
      sort_key: "tenNb",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["tenNb"] || "",
      i18Name: "hsba.hoTen",
    }),
    Column({
      title: t("common.maHoSo"),
      width: "120px",
      dataIndex: "maHoSo",
      key: "maHoSo",
      i18Name: "common.maHoSo",
      sort_key: "maHoSo",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["maHoSo"] || "",
    }),
    Column({
      title: t("common.maNb"),
      width: "120px",
      dataIndex: "maNb",
      key: "maNb",
      i18Name: "common.maNb",
      sort_key: "maNb",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["maNb"] || "",
    }),
    Column({
      title: t("common.diaChi"),
      width: "200px",
      dataIndex: "diaChi",
      key: "diaChi",
      i18Name: "common.diaChi",
      sort_key: "diaChi",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["diaChi"] || "",
    }),
    Column({
      title: t("kho.ngaySinh"),
      width: "120px",
      dataIndex: "ngaySinh2",
      key: "ngaySinh2",
      i18Name: "kho.ngaySinh",
      sort_key: "ngaySinh",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["ngaySinh"] || "",
    }),
    Column({
      title: t("hsba.maLuuTru"),
      width: "150px",
      dataIndex: "maLuuTru",
      key: "maLuuTru",
      i18Name: "hsba.maLuuTru",
      sort_key: "maLuuTru",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["maLuuTru"] || "",
      render: (item) => {
        return <span style={{ color: "red" }}>{item}</span>;
      },
    }),
    Column({
      title: t("hsba.maLuuTruKhoa"),
      width: "150px",
      dataIndex: "maLuuTruKhoa",
      key: "maLuuTruKhoa",
      i18Name: "hsba.maLuuTruKhoa",
      sort_key: "maLuuTruKhoa",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["maLuuTruKhoa"] || "",
    }),
    Column({
      title: t("hsba.thoiGianLuuTru"),
      width: "120px",
      dataIndex: "thoiGianLuuTru",
      key: "thoiGianLuuTru",
      i18Name: "hsba.thoiGianLuuTru",
      sort_key: "thoiGianLuuTru",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["thoiGianLuuTru"] || "",
      render: (field, item, index) =>
        field ? moment(field).format("DD/MM/YYYY") : "",
    }),
    Column({
      title: t("common.maBenhAn"),
      width: "120px",
      dataIndex: "maBenhAn",
      key: "maBenhAn",
      i18Name: "common.maBenhAn",
      sort_key: "maBenhAn",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["maBenhAn"] || "",
    }),
    Column({
      title: t("hsba.trangThaiNb"),
      width: "150px",
      dataIndex: "trangThaiNb",
      key: "trangThaiNb",
      i18Name: "hsba.trangThaiNb",
      sort_key: "trangThaiNb",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["trangThaiNb"] || "",
      render: (item) => listTrangThaiNb.find((x) => x.id === item)?.ten || "",
    }),
    Column({
      title: t("hsba.trangThaiBenhAn"),
      width: "150px",
      dataIndex: "trangThaiBenhAn",
      key: "trangThaiBenhAn",
      i18Name: "hsba.trangThaiBenhAn",
      sort_key: "trangThaiBenhAn",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["trangThaiBenhAn"] || "",
      render: (item) =>
        listTrangThaiBenhAn.find((x) => x.id === item)?.ten || "",
    }),
    Column({
      title: t("hsba.doiTuongKcb"),
      width: "150px",
      dataIndex: "doiTuongKcb",
      key: "doiTuongKcb",
      i18Name: "hsba.doiTuongKcb",
      sort_key: "doiTuongKcb",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["doiTuongKcb"] || "",
      render: (item) => listDoiTuongKcb.find((x) => x.id === item)?.ten || "",
    }),
    Column({
      title: t("hsba.thoiGianNhanBa"),
      width: "140px",
      dataIndex: "thoiGianNhanBa",
      key: "thoiGianNhanBa",
      i18Name: "hsba.thoiGianNhanBa",
      sort_key: "thoiGianNhanBa",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["thoiGianNhanBa"] || "",
      render: (field, item, index) =>
        field ? moment(field).format("DD-MM-YYYY HH:mm") : "",
    }),
    Column({
      title: t("hsba.thoiGianLapBenhAn"),
      width: "120px",
      dataIndex: "thoiGianLapBenhAn",
      key: "thoiGianLapBenhAn",
      i18Name: "hsba.thoiGianLapBenhAn",
      sort_key: "thoiGianLapBenhAn",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["thoiGianLapBenhAn"] || "",
      render: (field, item, index) =>
        field ? moment(field).format("DD/MM/YYYY") : "",
    }),
    Column({
      title: t("hsba.tenKhoaNb"),
      width: "200px",
      dataIndex: "tenKhoaNb",
      key: "tenKhoaNb",
      i18Name: "hsba.tenKhoaNb",
      sort_key: "tenKhoaNb",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["tenKhoaNb"] || "",
    }),
    Column({
      title: t("hsba.soNgayDieuTri"),
      width: "120px",
      key: "soNgayDieuTri",
      dataIndex: "soNgayDieuTri",
      i18Name: "hsba.soNgayDieuTri",
      align: "center",
      sort_key: "soNgayDieuTri",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["soNgayDieuTri"] || "",
    }),
    Column({
      title: t("hsba.dsCdChinh"),
      width: "320px",
      dataIndex: "dsCdChinh",
      key: "dsCdChinh",
      i18Name: "hsba.dsCdChinh",
      render: (item) => {
        return (
          <EllipsisText.Tooltip
            limitLine={3}
            content={(item || []).map((x) => x.ten).join(", ")}
          />
        );
      },
    }),
    Column({
      title: t("hsba.dsCdKemTheo"),
      width: "320px",
      dataIndex: "dsCdKemTheo",
      key: "dsCdKemTheo",
      i18Name: "hsba.dsCdKemTheo",
      render: (item) => {
        return (
          <EllipsisText.Tooltip
            limitLine={3}
            content={(item || []).map((x) => x.ten).join(", ")}
          />
        );
      },
    }),
    Column({
      title: t("hsba.thoiGianVaoVien"),
      width: "140px",
      dataIndex: "thoiGianVaoVien",
      key: "thoiGianVaoVien",
      i18Name: "hsba.thoiGianVaoVien",
      sort_key: "thoiGianVaoVien",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["thoiGianVaoVien"] || "",
      render: (field, item, index) =>
        field ? moment(field).format("DD/MM/YYYY") : "",
    }),
    Column({
      title: t("hsba.tienConLai"),
      width: "150px",
      dataIndex: "tienConLai",
      key: "tienConLai",
      align: "right",
      i18Name: "hsba.tienConLai",
      sort_key: "tienConLai",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["tienConLai"] || "",
      render: (item) => (item || 0).formatPrice(),
    }),
    Column({
      title: t("common.thoiGianRaVien"),
      width: "150px",
      dataIndex: "thoiGianRaVien",
      key: "thoiGianRaVien",
      i18Name: "common.thoiGianRaVien",
      sort_key: "thoiGianRaVien",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["thoiGianRaVien"] || "",
      render: (field, item, index) =>
        field ? moment(field).format("DD/MM/YYYY HH:mm:ss") : "",
    }),
    Column({
      title: t("hsba.huongDieuTri"),
      width: "140px",
      dataIndex: "huongDieuTri",
      key: "huongDieuTri",
      i18Name: "hsba.huongDieuTri",
      sort_key: "huongDieuTri",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["huongDieuTri"] || "",
      render: (item) =>
        [...listHuongDieuTriKham, ...listHuongDieuTriNoiTru].find(
          (x) => x.id === item
        )?.ten || "",
    }),
    Column({
      title: t("hsba.ketQuaDieuTri"),
      width: "150px",
      dataIndex: "ketQuaDieuTri",
      key: "ketQuaDieuTri",
      i18Name: "hsba.ketQuaDieuTri",
      sort_key: "ketQuaDieuTri",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["ketQuaDieuTri"] || "",
      render: (item) => listKetQuaDieuTri.find((x) => x.id === item)?.ten || "",
    }),
    Column({
      title: t("hsba.soNamLuuTru"),
      width: "120px",
      dataIndex: "soNamLuuTru",
      key: "soNamLuuTru",
      i18Name: "hsba.soNamLuuTru",
      sort_key: "soNamLuuTru",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["soNamLuuTru"] || "",
    }),
    Column({
      title: t("hsba.nguoiLuuTru"),
      sort_key: "tenNguoiLuuTru",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["tenNguoiLuuTru"] || "",
      width: "120px",
      dataIndex: "tenNguoiLuuTru",
      key: "tenNguoiLuuTru",
      i18Name: "hsba.nguoiLuuTru",
    }),
    Column({
      title: t("hsba.loaiLuuTru"),
      width: "150px",
      dataIndex: "loaiLuuTru",
      key: "loaiLuuTru",
      i18Name: "hsba.loaiLuuTru",
      sort_key: "loaiLuuTru",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["loaiLuuTru"] || "",
      render: (item) => listLoaiLuuTru.find((x) => x.id === item)?.ten || "",
    }),
    Column({
      title: t("khth.doiTuongBhyt"),
      sort_key: "doiTuong",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["doiTuong"] || "",
      dataIndex: "doiTuong",
      key: "doiTuong",
      width: "120px",
      i18Name: "khth.doiTuongBhyt",
      render: (item) => listDoiTuong.find((x) => x.id === item)?.ten || "",
    }),
    Column({
      title: t("hsba.luuTruBenhAnDienTu"),
      sort_key: "luuTruBenhAnDienTu",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["luuTruBenhAnDienTu"] || "",
      dataIndex: "luuTruBenhAnDienTu",
      key: "luuTruBenhAnDienTu",
      width: "120px",
      i18Name: "hsba.luuTruBenhAnDienTu",
      render: (item) => LOAI_LUU_TRU_BENH_AN_DIEN_TU.find((x) => x.id === item)?.ten || "",
    }),
    Column({
      title: (
        <>
          {t("common.tienIch")}
          <Setting refTable={refSettings} />
        </>
      ),
      width: "120px",
      align: "center",
      fixed: "right",
      ignore: true,
      render: (list, item, index) => {
        return (
          <>
            {checkRole([ROLES["HO_SO_BENH_AN"].XEM_HSBA_DONG_GOI]) && (
              <Tooltip title={t("hsba.xemHoSoBenhAnDongGoi")}>
                <SVG.IcDongGoiHSBA
                  color={"var(--color-blue-primary)"}
                  className="ic-action"
                  onClick={onViewDongGoiHsba(item)}
                />
              </Tooltip>
            )}
            {checkRole([ROLES["HO_SO_BENH_AN"].XEM_FILE_SCAN_HSBA_LUU_TRU]) && (
              <Tooltip title={t("hsba.hoSoBenhAnDuPhong")}>
                <SVG.IcHsba
                  className="ic-action"
                  onClick={onViewHsbaDuPhong(item)}
                />
              </Tooltip>
            )}
            {checkRole([
              ROLES["KE_HOACH_TONG_HOP"].CHI_TIET_LUU_TRU_BENH_AN,
            ]) && (
              <Tooltip title={t("hsba.hoSoBenhAnDuPhong")}>
                <SVG.IcEye className="ic-action" />
              </Tooltip>
            )}
          </>
        );
      },
    }),
  ];

  const onSelectChange = (selectedRowKeys, data) => {
    const listTrangThai = [...new Set(data.map((x) => x.trangThaiBenhAn))];
    if (listTrangThai.length > 1) {
      message.error(t("hsba.cacBenhAnCoTrangThaiKhacNhau"));
      return;
    }

    let updatedSelectedKeys = selectedRowKeys;

    updatedSelectedKeys = [...new Set(updatedSelectedKeys)];
    if (listDsLuuTru.length === updatedSelectedKeys.length) {
      setState({
        isCheckedAll: true,
        selectedRowKeys: updatedSelectedKeys,
      });
    } else {
      setState({
        isCheckedAll: false,
        selectedRowKeys: updatedSelectedKeys,
      });
    }

    updateData({
      selectedDichVu: data,
    });
  };

  const isCheckedAll = useMemo(() => {
    return (
      listDsLuuTru?.length &&
      listDsLuuTru
        .map((item) => item.id)
        .every((i) => state.selectedRowKeys.includes(i))
    );
  }, [listDsLuuTru, state.selectedRowKeys]);
  const onCheckAll = (e) => {
    let selectedRowKeys = e.target?.checked
      ? (listDsLuuTru || []).map((x) => x.id)
      : [];
    onSelectChange(selectedRowKeys, listDsLuuTru);
  };
  const rowSelection = {
    columnTitle: (
      <HeaderSearch
        title={
          <Checkbox onChange={onCheckAll} checked={isCheckedAll}></Checkbox>
        }
      />
    ),
    columnWidth: 45,
    onChange: onSelectChange,
    selectedRowKeys: state.selectedRowKeys,
    fixed: true,
  };

  return (
    <Main noPadding={true}>
      <TableWrapper
        columns={columns}
        dataSource={listDsLuuTru}
        onRow={onRow}
        rowKey={(record) => record.id}
        loading={loading}
        // scroll={{ x: 2000 }}
        rowSelection={rowSelection}
        ref={refSettings}
        tableName="table_HSBA_LuuTruBenhAn"
        themeKey={THEME_KEY.LUU_TRU_BENH_AN}
      />
      <Pagination
        onChange={onChangePage}
        current={page + 1}
        pageSize={size}
        listData={listDsLuuTru}
        total={totalElements}
        onShowSizeChange={handleSizeChange}
      />
    </Main>
  );
};

export default DanhSachLuuTru;
