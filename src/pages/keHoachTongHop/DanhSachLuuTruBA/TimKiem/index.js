import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import {
  useEnum,
  useListAll,
  useRefFunc,
  useStore,
  useLoading,
  useThietLap,
} from "hooks";
import { ENUM, THIET_LAP_CHUNG, TRANG_THAI_NB } from "constants/index";
import moment from "moment";
import {
  AddressFull,
  BaseSearch,
  DatePicker,
  Button,
  InputTimeout,
} from "components";
import {
  LOAI_XEM_DANH_SACH,
  DS_TRANG_THAI_BA_GOP,
  TRANG_THAI_LUU_TRU_BA,
  LOAI_LUU_TRU_BENH_AN_DIEN_TU
} from "pages/keHoachTongHop/components/config";
import useChangeScreen from "pages/keHoachTongHop/hooks/useChangeScreen";
import { cloneDeep } from "lodash";
import {
  getAllQueryString,
  setQueryStringValues,
} from "hooks/useQueryString/queryString";
import { SVG } from "assets";
import { Main, TemLuuTruWrapper } from "./styled";
import { Input } from "antd";

const TimKiemLuuTru = ({ parentState, setParentState }) => {
  const { t } = useTranslation();
  const { onChangeScreen } = useChangeScreen();
  const { showLoading, hideLoading } = useLoading();

  const [listTrangThaiNb] = useEnum(ENUM.TRANG_THAI_NB);
  const [listDoiTuongKcb] = useEnum(ENUM.DOI_TUONG_KCB);
  const [listLoaiLuuTru] = useEnum(ENUM.LOAI_LUU_TRU);
  const [listKetQuaDieuTri] = useEnum(ENUM.KET_QUA_DIEU_TRI);
  const [listDoiTuong] = useEnum(ENUM.DOI_TUONG);
  const [dataHIEN_THI_IN_TEM_THEO_MA_LUU_TRU] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_IN_TEM_THEO_MA_LUU_TRU
  );

  const [diaChi, setDiaChi] = useState(() => {
    const { tenXaPhuong, tenQuanHuyen, tenTinhThanhPho } = getAllQueryString();
    return [tenXaPhuong, tenQuanHuyen, tenTinhThanhPho]
      .filter(Boolean)
      .join(", ");
  });
  const [state, _setState] = useState({
    tuMaLuuTruBa: "",
    denMaLuuTruBa: "",
  });

  const setState = (data) => {
    _setState((prev) => {
      return { ...prev, ...data };
    });
  };

  const {
    dsLuuTruBa: {
      onChangeInputSearch,
      thongKeSoLuong,
      exportExcel,
      inTemLuuTruBenhAn,
    },
  } = useDispatch();

  const thongKeSL = useStore("dsLuuTruBa.thongKeSL", {});
  const [listAllKhoa] = useListAll("khoa");
  const dataSearch = useStore("dsLuuTruBa.dataSearch", {});

  useEffect(() => {
    thongKeSoLuong();
  }, []);

  const handleSearch = (data) => {
    let payload = cloneDeep(data);

    delete payload.khoaLamViec;
    delete payload.dsTrangThaiBenhAnDefault;
    delete payload.loaiXemDanhSach;
    delete payload.dsTrangThaiBenhAn;

    if (payload.dsKhoaYeuCauId && payload.dsKhoaYeuCauId.length > 0) {
      payload.dsTrangThaiBenhAn = [TRANG_THAI_LUU_TRU_BA.CHO_TAO_PHIEU_MUON];
    }

    onChangeInputSearch(payload);

    setParentState(data);
  };

  const onChangeAdrressText = useRefFunc((value) => {
    if (value !== diaChi) {
      setDiaChi(value);

      // handle clear input address khi clear text từ valid địa chỉ
      const isPreviousAddressSearch =
        parentState.tenXaPhuong ||
        parentState.tenQuanHuyen ||
        parentState.tenTinhThanhPho;

      if (!value && isPreviousAddressSearch) {
        handleSearchAddress(null);
      }
    }
  });

  const handleSearchAddress = async (data) => {
    let phuong = null;
    let huyen = null;
    let tinh = null;
    if (data?.tinhThanhPho && data?.quanHuyen) {
      phuong = data?.ten;
      huyen = data?.quanHuyen?.ten;
      tinh = data?.tinhThanhPho?.ten;
    } else if (data?.tinhThanhPho) {
      huyen = data?.quanHuyen === null ? null : data?.ten;
      phuong = data?.quanHuyen === null ? data?.ten : null;
      tinh = data?.tinhThanhPho?.ten;
    } else {
      tinh = data?.ten;
      huyen = null;
      phuong = null;
    }

    handleSearch({
      tenXaPhuong: phuong,
      tenQuanHuyen: huyen,
      tenTinhThanhPho: tinh,
    });
    setQueryStringValues({
      tenXaPhuong: phuong,
      tenQuanHuyen: huyen,
      tenTinhThanhPho: tinh,
    });
  };

  const onChangeMaLuuTru = (key) => (e) => {
    let value = e.target.value;
    setState({ [key]: value });
  };

  const onPrintMaLuuTru = async () => {
    try {
      showLoading();
      await inTemLuuTruBenhAn({
        tuMaLuuTruBa: state.tuMaLuuTruBa,
        denMaLuuTruBa: state.denMaLuuTruBa,
      });
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onChangeNamSinh = (value) => {
    setQueryStringValues({
      namSinh: value,
    });
    setParentState({
      namSinh: value,
    });

    onChangeInputSearch({
      tuThoiGianSinh: value
        ? moment(value).startOf("year").format("YYYY-MM-DD")
        : null,
      denThoiGianSinh: value
        ? moment(value).endOf("year").format("YYYY-MM-DD")
        : null,
    });
  };

  const onClickXuatDS = async () => {
    try {
      showLoading();
      await exportExcel(dataSearch);
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  return (
    <Main>
      <div className="header-info">
        <div className="soLieu">
          <div className="soLieuNgay">
            <div>
              {t("hsba.daNhan")}/ {t("hsba.nBRaVien")} (
              {moment().format("DD/MM/YYYY")}):&nbsp;
            </div>
            <div className="bold">
              {thongKeSL.slDaNhanNgay}/{thongKeSL.slNbRaVien}
            </div>
          </div>
          <div className="soLieuNam">
            <div>
              {t("hsba.daLuuTru")}/ {t("hsba.daNhan")} ({moment().year()}
              ):&nbsp;
            </div>
            <div className="bold">
              {thongKeSL.slDaLuuTruNam}/{thongKeSL.slDaNhanNam}
            </div>
          </div>
        </div>
        {dataHIEN_THI_IN_TEM_THEO_MA_LUU_TRU?.eval() && (
          <TemLuuTruWrapper>
            <div className="group-input">
              <span style={{ fontWeight: 500 }}>{t("khth.tuMaLuuTru")}</span>
              <Input
                placeholder={t("khth.tuMaLuuTru")}
                value={state.tuMaLuuTruBa}
                onChange={onChangeMaLuuTru("tuMaLuuTruBa")}
              />
            </div>
            <div className="group-input">
              <span style={{ fontWeight: 500 }}>{t("khth.denMaLuuTru")}</span>
              <Input
                placeholder={t("khth.denMaLuuTru")}
                value={state.denMaLuuTruBa}
                onChange={onChangeMaLuuTru("denMaLuuTruBa")}
              />
            </div>
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "flex-end",
              }}
            >
              <Button onClick={onPrintMaLuuTru} height={28}>
                {t("khth.inTemTheoMaLuuTru")}
              </Button>
            </div>
          </TemLuuTruWrapper>
        )}
      </div>
      <BaseSearch
        cacheData={{ ...parentState, loaiXemDanhSach: 1 }}
        componentRight={
          <Button
            rightIcon={<SVG.IcList />}
            iconHeight={15}
            type="primary"
            onClick={onClickXuatDS}
          >
            {t("sinhHieu.xuatDS")}
          </Button>
        }
        dataInput={[
          {
            widthInput: "200px",
            placeholder: t("khth.danhSach.loaiXem"),
            keyValueInput: "loaiXemDanhSach",
            functionChangeInput: onChangeScreen,
            type: "select",
            listSelect: LOAI_XEM_DANH_SACH,
            allowClear: true,
            putToQuerry: false,
          },
          {
            widthInput: "320px",
            placeholder: t("common.timTenNbQrNbMaNbMaHoSo"),
            functionChangeInput: handleSearch,
            isScanQR: true,
            qrGetValue: "maHoSo",
            keysFlexible: [
              {
                key: "tenNb",
                type: "string",
              },
              {
                key: "maHoSo",
                type: "maHoSo",
              },
              {
                key: "maNb",
                type: "startLettersEndNumbers",
              },
            ],
          },
          {
            widthInput: "220px",
            placeholder: t("common.nhapMaNguoiBenh"),
            keyValueInput: "maNb",
            functionChangeInput: handleSearch,
          },
          {
            widthInput: "200px",
            placeholder: t("common.nhapMaBenhAn"),
            keyValueInput: "maBenhAn",
            functionChangeInput: handleSearch,
          },
          {
            widthInput: "200px",
            placeholder: t("hsba.maLuuTru"),
            keyValueInput: "maLuuTru",
            functionChangeInput: handleSearch,
          },
          {
            widthInput: "180px",
            title: t("khth.khoaNB"),
            keyValueInput: "dsKhoaNbId",
            functionChangeInput: onChangeInputSearch,
            type: "selectCheckbox",
            hasSearch: true,
            hasCheckAll: true,
            virtual: true,
            defaultValue: parentState.dsKhoaNbId || [],
            listSelect: listAllKhoa,
          },
          {
            widthInput: "160px",
            title: t("hsba.trangThaiBenhAn"),
            keyValueInput: "dsTrangThaiBenhAn",
            defaultValue: parentState.dsTrangThaiBenhAnDefault || [],
            functionChangeInput: (e) => {
              let _dsTrangThaiBenhAn = [];

              (e.dsTrangThaiBenhAn || []).forEach((id) => {
                const _valueItems =
                  DS_TRANG_THAI_BA_GOP.find((x) => x.id === id)?.value || [];

                _dsTrangThaiBenhAn = [
                  ...new Set([..._dsTrangThaiBenhAn, ..._valueItems]),
                ];
              });

              setParentState({ dsTrangThaiBenhAn: e.dsTrangThaiBenhAn });
              onChangeInputSearch({ dsTrangThaiBenhAn: _dsTrangThaiBenhAn });
            },
            type: "selectCheckbox",
            hasCheckAll: true,
            virtual: true,
            listSelect: DS_TRANG_THAI_BA_GOP,
          },
          {
            widthInput: "160px",
            placeholder: t("hsba.doiTuongKcb"),
            keyValueInput: "doiTuongKcb",
            functionChangeInput: onChangeInputSearch,
            type: "select",
            listSelect: listDoiTuongKcb,
          },
          {
            widthInput: "250px",
            type: "dateRange2",
            state: parentState,
            setState: setParentState,
            functionChangeInput: handleSearch,
            keyValueInput: ["tuThoiGianVaoVien", "denThoiGianVaoVien"],
            title: t("hsba.ngayDangKy"),
            placeholder: [t("common.tuNgay"), t("common.denNgay")],
            format: "DD/MM/YYYY",
            allowEmpty: [true, true],
          },
          {
            widthInput: "350px",
            type: "dateRange2",
            state: parentState,
            setState: setParentState,
            functionChangeInput: handleSearch,
            keyValueInput: ["tuThoiGianRaVien", "denThoiGianRaVien"],
            title: t("hsba.ngayRaVien"),
            placeholder: [t("common.tuNgay"), t("common.denNgay")],
            format: "DD/MM/YYYY HH:mm:ss",
            allowEmpty: [true, true],
          },
          {
            widthInput: "350px",
            type: "dateRange2",
            state: parentState,
            setState: setParentState,
            functionChangeInput: handleSearch,
            keyValueInput: ["tuThoiGianLuuTru", "denThoiGianLuuTru"],
            title: t("hsba.thoiGianLuuTru"),
            placeholder: [t("common.tuNgay"), t("common.denNgay")],
            format: "DD/MM/YYYY HH:mm:ss",
            allowEmpty: [true, true],
          },
          {
            widthInput: "220px",
            placeholder: t("danhMuc.nhapTitle", {
              title: t("tiepDon.soNhaThonXom"),
            }),
            keyValueInput: "soNha",
            functionChangeInput: handleSearch,
          },
          {
            widthInput: "300px",
            type: "addition",
            component: (
              <div className="input-address">
                <AddressFull
                  className="form-item_address"
                  placeholder={t("danhMuc.nhapTitle", {
                    title: t("tiepDon.phuongXaTinhThanh"),
                  })}
                  value={diaChi}
                  onChangeAdrressText={onChangeAdrressText}
                  delayTyping={300}
                  onSelectAddress={handleSearchAddress}
                  suffix={<SVG.IcSearch />}
                />
              </div>
            ),
          },
          {
            widthInput: "150px",
            type: "addition",
            component: (
              <div className="input-nam-sinh">
                <DatePicker
                  onChange={onChangeNamSinh}
                  picker="year"
                  placeholder={t("danhMuc.nhapTitle", {
                    title: t("common.namSinh").toLowerCase(),
                  })}
                  value={parentState.namSinh}
                />
              </div>
            ),
          },
          {
            widthInput: "160px",
            placeholder: t("hsba.loaiLuuTru"),
            keyValueInput: "loaiLuuTru",
            functionChangeInput: onChangeInputSearch,
            type: "select",
            listSelect: listLoaiLuuTru,
          },
          {
            widthInput: "160px",
            title: t("hsba.ketQuaDieuTri"),
            keyValueInput: "dsKetQuaDieuTri",
            functionChangeInput: onChangeInputSearch,
            type: "selectCheckbox",
            hasCheckAll: true,
            virtual: true,
            listSelect: listKetQuaDieuTri,
          },
          {
            widthInput: "160px",
            placeholder: t("khth.doiTuongBhyt"),
            keyValueInput: "doiTuong",
            functionChangeInput: onChangeInputSearch,
            type: "select",
            listSelect: listDoiTuong,
          },
          {
            widthInput: "220px",
            placeholder: t("hsba.luuTruBenhAnDienTu"),
            keyValueInput: "luuTruBenhAnDienTu",
            functionChangeInput: onChangeInputSearch,
            type: "select",
            listSelect: LOAI_LUU_TRU_BENH_AN_DIEN_TU,
          },
        ]}
        filter={{
          open: true,
          width: "110px",
          funcSearchData: handleSearch,
          data: [
            {
              key: ["tuThoiGianYeuCauMuon", "denThoiGianYeuCauMuon"],
              placeholder: [t("common.tuNgay"), t("common.denNgay")],
              type: "date-1",
              title: t("khth.baMuon.thoiGianYeuCauMuon"),
            },
            {
              widthInput: "212px",
              placeholder: t("khth.baMuon.khoaYeuCauMuon"),
              key: "dsKhoaYeuCauId",
              type: "select",
              title: t("khth.baMuon.khoaYeuCauMuon"),
              dataSelect: listAllKhoa,
              defaultValue: parentState.dsKhoaYeuCauId,
              mode: "multiple",
            },
            {
              widthInput: "212px",
              placeholder: t("hsba.trangThaiNb"),
              key: "trangThaiNb",
              type: "select",
              dataSelect: listTrangThaiNb.filter((x) =>
                [
                  TRANG_THAI_NB.DA_RA_VIEN,
                  TRANG_THAI_NB.DA_THANH_TOAN_RA_VIEN,
                ].includes(x.id)
              ),
            },
            {
              key: "maLuuTruKhoa",
              placeholder: t("hsba.timTheoMaLuuTruKhoa"),
              type: "normal",
            },
          ],
        }}
      />
    </Main>
  );
};

export default TimKiemLuuTru;
