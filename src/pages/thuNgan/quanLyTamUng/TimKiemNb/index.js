import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { BaseSearch } from "components";
import {
  useConfirm,
  useEnum,
  useLazyKVMap,
  useListAll,
  useStore,
  useThietLap,
} from "hooks";
import {
  ENUM,
  THIET_LAP_CHUNG,
  TRANG_THAI_NB,
  PHAN_LOAI_DOI_TUONG_KCB,
} from "constants/index";
import moment from "moment";
import { useHistory } from "react-router-dom";
import { orderBy } from "lodash";
import printProvider from "data-access/print-provider";
import { transformQueryString, isArray } from "utils/index";
import { setQueryStringValue } from "hooks/useQueryString/queryString";

const listTrangThaiNbIds = [
  5, 10, 20, 30, 40, 45, 50, 100, 102, 103, 106, 110, 112, 113, 116,
]; // https://jira.isofh.com.vn/browse/SAKURA-54513

const TimKiemPhieu = (props) => {
  const { showConfirm } = useConfirm();
  const { nhaTamUng, titleRight } = props;

  const {
    quanLyTamUng: {
      onChangeInputSearch,
      onSearch: onSearchQuanLyTamUng,
      updateData,
    },
    deNghiTamUng: { onSearch },
    thuTamUng: {
      onChangeInputSearch: onChangeInputSearchThuTamUng,
      inPhieuTamUng,
    },
    danhSachPhieuThu: { goiNbTiepTheo },
  } = useDispatch();

  const dataSearch = useStore("quanLyTamUng.dataSearch");
  const [listTrangThaiNb] = useEnum(ENUM.TRANG_THAI_NB);
  const [dataPHIEU_TAM_UNG] = useThietLap(THIET_LAP_CHUNG.PHIEU_TAM_UNG);
  const [dataPHIEU_DE_NGHI_TAM_UNG] = useThietLap(
    THIET_LAP_CHUNG.PHIEU_DE_NGHI_TAM_UNG
  );
  const [dataCANH_BAO_CHUA_TAM_UNG_NOI_TRU] = useThietLap(
    THIET_LAP_CHUNG.CANH_BAO_CHUA_TAM_UNG_NOI_TRU
  );
  const [listAllPhanLoaiNB] = useListAll("phanLoaiNB", {}, true);

  const history = useHistory();

  const { t } = useTranslation();
  const [state, _setState] = useState({});
  const setState = (data) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const onChangeInputSearchWrapper = (...args) => {
    setQueryStringValue("page", 0);
    return onChangeInputSearch(...args);
  };

  const onSearchDate = (data) => {
    onChangeInputSearchWrapper(
      data.tuThoiGianVaoVien
        ? data
        : {
            tuThoiGianVaoVien: "",
            denThoiGianVaoVien: "",
          }
    );
  };

  //get Function
  const [getPhanLoaiDoiTuongKcb] = useLazyKVMap(PHAN_LOAI_DOI_TUONG_KCB);

  useEffect(() => {
    const {
      page,
      size,
      dataSortColumn = "{}",
      ...queries
    } = transformQueryString({
      tuThoiGianVaoVien: {
        defaultValue: moment().startOf("day"),
        type: "dateOptions",
      },
      denThoiGianVaoVien: {
        defaultValue: moment().endOf("day"),
        type: "dateOptions",
      },
      dsTrangThaiNb: {
        format: (value) => value.split(",").map(Number),
        defaultValue: listTrangThaiNbIds,
      },
      dsDoiTuongKcb: {
        format: (value) => parseInt(value),
      },
      dsPhanLoaiNbId: {
        format: (value) => value.split(",").map(Number),
      },
    });

    if (isArray(listTrangThaiNb, true)) {
      if (
        queries.maHoSo ||
        queries.maThe ||
        queries.tenNb ||
        queries.maBenhAn ||
        queries.maNb
      ) {
        queries.tuThoiGianVaoVien = null;
        queries.denThoiGianVaoVien = null;
      }
      setState(queries);
      updateData({
        size: parseInt(size || 10),
        dataSortColumn: JSON.parse(dataSortColumn),
        dataSearch: {
          ...queries,
          tuThoiGianVaoVien: queries.tuThoiGianVaoVien?.format(
            "YYYY-MM-DD HH:mm:ss"
          ),
          denThoiGianVaoVien: queries.denThoiGianVaoVien?.format(
            "YYYY-MM-DD HH:mm:ss"
          ),
          dsDoiTuongKcb: queries.dsDoiTuongKcb
            ? getPhanLoaiDoiTuongKcb(queries.dsDoiTuongKcb).referIds
            : null,
        },
      });

      onSearchQuanLyTamUng({
        page: parseInt(page || 0),
      });
    }
  }, [listTrangThaiNb]);

  const onPushChiTiet = (record) => {
    if (
      [TRANG_THAI_NB.CHO_LAP_BENH_AN, TRANG_THAI_NB.HUY_BENH_AN].includes(
        record.trangThai
      )
    ) {
      if (dataCANH_BAO_CHUA_TAM_UNG_NOI_TRU.toLowerCase() !== "true") {
        showConfirm({
          title: t("common.thongBao"),
          content: `${t("thuNgan.quanLyTamUng.nbNhapVienChuaLapBenhAn")}`,
          cancelText: t("common.huy"),
          okText: t("common.dongY"),
          classNameOkText: "button-warning",
          typeModal: "warning",
        });
      }
      history.push(`/thu-ngan/quan-ly-tam-ung/${record.id}`);
    } else {
      onSearch({
        dataSearch: { nbDotDieuTriId: record.id, trangThai: 10 },
      }).then((data) => {
        if (!data?.length && dataPHIEU_TAM_UNG?.eval()) {
          history.push({
            pathname: `/thu-ngan/quan-ly-tam-ung/${record.id}`,
            state: { thuTamUng: true },
          });
        } else if (data?.length === 1 && dataPHIEU_DE_NGHI_TAM_UNG?.eval()) {
          history.push({
            pathname: `/thu-ngan/quan-ly-tam-ung/${record.id}`,
            state: { duyetDeNghiTamUng: true, data: data[0], nhaTamUng },
          });
        } else {
          history.push(`/thu-ngan/quan-ly-tam-ung/${record.id}`);
        }
      });
    }
  };

  const onSearchInput = (data) => {
    if (data.maHoSo || data.maThe || data.tenNb || data.maBenhAn || data.maNb) {
      onChangeInputSearchWrapper({
        ...data,
        tuThoiGianVaoVien: null,
        denThoiGianVaoVien: null,
      }).then((s) => {
        if (s?.data?.length) {
          if (s?.data?.length === 1) {
            if (nhaTamUng?.khongLaySo) {
              goiNbTiepTheo({
                quayId: nhaTamUng?.id,
                nbDotDieuTriId: s?.data[0].id,
              });
            }

            onPushChiTiet(s?.data[0]);
          }
        } else {
          message.error(`${t("thuNgan.khongTimThayThongTinTamUng")}`);
        }
      });
    } else {
      onChangeInputSearchWrapper({
        tuThoiGianVaoVien: dataSearch?.tuThoiGianVaoVien
          ? dataSearch?.tuThoiGianVaoVien
          : moment()
              .set("hour", 0)
              .set("minute", 0)
              .set("second", 0)
              .format("YYYY-MM-DD HH:mm:ss"),
        denThoiGianVaoVien: dataSearch?.denThoiGianVaoVien
          ? dataSearch?.denThoiGianVaoVien
          : moment()
              .set("hour", 23)
              .set("minute", 59)
              .set("second", 59)
              .format("YYYY-MM-DD HH:mm:ss"),
        maHoSo: null,
        maThe: null,
        tenNb: null,
        maBenhAn: null,
        maNb: null,
      });
    }
  };

  const onChangeInput = (data) => {
    let res = { ...dataSearch, ...data };
    if (!res.maBenhAn && !res.maTheBhyt && !res.soDienThoai) {
      onChangeInputSearchWrapper({
        ...data,
        tuThoiGianVaoVien: dataSearch?.tuThoiGianVaoVien
          ? dataSearch?.tuThoiGianVaoVien
          : moment().startOf("day").format("YYYY-MM-DD HH:mm:ss"),
        denThoiGianVaoVien: dataSearch?.denThoiGianVaoVien
          ? dataSearch?.denThoiGianVaoVien
          : moment().endOf("day").format("YYYY-MM-DD HH:mm:ss"),
      });
    } else {
      onChangeInputSearchWrapper({
        ...data,
        tuThoiGianVaoVien: null,
        denThoiGianVaoVien: null,
      });
    }
  };

  const onChangeDoiTuongKcb = (data) => {
    setQueryStringValue("dsDoiTuongKcb", data.dsDoiTuongKcb);
    onChangeInputSearch({
      dsDoiTuongKcb: data.dsDoiTuongKcb
        ? getPhanLoaiDoiTuongKcb(data.dsDoiTuongKcb).referIds
        : null,
    });
  };

  return (
    <BaseSearch
      cacheData={state}
      componentRight={titleRight}
      dataInput={[
        {
          widthInput: "180px",
          type: "dateOptions",
          state: state,
          setState: setState,
          keyValueInput: ["tuThoiGianVaoVien", "denThoiGianVaoVien"],
          functionChangeInput: (e) => {
            onSearchDate(
              {
                tuThoiGianVaoVien: e.tuThoiGianVaoVien?.format(
                  "YYYY-MM-DD 00:00:00"
                ),
                denThoiGianVaoVien: e.denThoiGianVaoVien?.format(
                  "YYYY-MM-DD 23:59:59"
                ),
              },
              !!e.tuThoiGianVaoVien
            );
          },
          title: t("thuNgan.thoiGian"),
          format: "DD/MM/YYYY",
        },
        {
          widthInput: "450px",
          placeholder: t("thuNgan.timKiemTheoMaNbMaHoSoTenNbQrNbRfId"),
          functionChangeInput: (e) => onSearchInput(e),
          isScanQR: true,
          qrGetValue: "maHoSo",
          searchTriggerKeys: ["Enter", "Tab"],
          keysFlexible: [
            {
              key: "maThe",
              type: "hex",
            },
            {
              key: "maHoSo",
              type: "maHoSo",
            },
            {
              key: "maBenhAn",
              type: "number7",
            },
            {
              key: "tenNb",
              type: "string",
            },
            {
              key: "maNb",
              type: "startLettersEndNumbers",
            },
          ],
        },
        {
          widthInput: "150px",
          placeholder: t("common.soBHYT"),
          keyValueInput: "maTheBhyt",
          functionChangeInput: (e) => onChangeInput(e),
        },
        {
          widthInput: "150px",
          placeholder: t("common.sdt"),
          keyValueInput: "soDienThoai",
          functionChangeInput: (e) => onChangeInput(e),
        },
        {
          widthInput: "170px",
          placeholder: t("common.chonDoiTuongKCB"),
          keyValueInput: "dsDoiTuongKcb",
          functionChangeInput: onChangeDoiTuongKcb,
          value: state.dsDoiTuongKcb,
          type: "select",
          listSelect: PHAN_LOAI_DOI_TUONG_KCB,
          hasAllOption: true,
        },
        {
          widthInput: "170px",
          title: t("quanLyNoiTru.trangThaiNb"),
          keyValueInput: "dsTrangThaiNb",
          type: "selectCheckbox",
          hasCheckAll: true,
          hasSearch: true,
          virtual: true,
          listSelect: listTrangThaiNb,
          defaultValue: state.dsTrangThaiNb,
          functionChangeInput: ({ dsTrangThaiNb }) => {
            onChangeInputSearch({
              dsTrangThaiNb:
                !dsTrangThaiNb || dsTrangThaiNb.length == 0
                  ? dsTrangThaiNb.map((x) => x.id)
                  : dsTrangThaiNb,
            });
            setState({ dsTrangThaiNb });
          },
        },
        {
          widthInput: "170px",
          title: t("common.phanLoaiNb"),
          keyValueInput: "dsPhanLoaiNbId",
          type: "selectCheckbox",
          hasCheckAll: true,
          hasSearch: true,
          virtual: true,
          listSelect: listAllPhanLoaiNB,
          defaultValue: state.dsPhanLoaiNbId,
          functionChangeInput: ({ dsPhanLoaiNbId }) => {
            onChangeInputSearch({
              dsPhanLoaiNbId:
                dsPhanLoaiNbId.length === listAllPhanLoaiNB.length
                  ? null
                  : dsPhanLoaiNbId,
            });
            setState({ dsPhanLoaiNbId });
          },
        },
        {
          widthInput: "150px",
          placeholder: t("common.maTheRFID"),
          keyValueInput: "maThe",
          functionChangeInput: (e) => onChangeInput(e),
        },
      ]}
    />
  );
};

export default TimKiemPhieu;
