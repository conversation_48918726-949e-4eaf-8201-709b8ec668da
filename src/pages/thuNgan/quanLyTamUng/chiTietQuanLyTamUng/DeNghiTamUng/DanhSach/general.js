import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "components";
import moment from "moment";
import { checkRole } from "lib-utils/role-utils";

import { SVG } from "assets";
import {
  LOAI_PHUONG_THUC_TT,
  ROLES,
  TRANG_THAI_THANH_TOAN_QR,
} from "constants/index";
import { parseListConfig } from "utils/index";

export const columns = ({
  onClickSort,
  onSettings,
  dataSortColumn,
  showModaConfirmRemove,
  t,
  onShowDuyetTamUng,
  onShowEditDeNghiTamUng,
  onInPhieuTamUng,
  onHuyQrCode,
  onViewQrCode,
  isNoiTru,
  listAllPhuongThucThanhToan,
  listAllDoiTac,
  listTrangThaiThanhToan,
  maDoiTacQrCodeCapNhatGiaoDich,
  onCapNhatTrangThaiGiaoDichQr,
  listAllQuayTiepDon,
  listAllToaNha,
}) => {
  return [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: "50px",
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.quanLyTamUng.soTienDeNghi")}
          sort_key="tongTien"
          dataSort={dataSortColumn["tongTien"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "100px",
      dataIndex: "tongTien",
      key: "tongTien",
      columnName: t("thuNgan.quanLyTamUng.soTienDeNghi"),
      show: true,
      align: "right",
      render: (field, item, index) => {
        return <div>{field && field?.formatPrice()}</div>;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.quanLyTamUng.lan")}
          sort_key="lan"
          dataSort={dataSortColumn["lan"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "80px",
      dataIndex: "stt",
      key: "stt",
      columnName: t("thuNgan.quanLyTamUng.lan"),
      show: true,
      align: "right",
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.quanLyTamUng.lyDoDeNghi")}
          sort_key="tenLyDoTamUng"
          dataSort={dataSortColumn["tenLyDoTamUng"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "120px",
      dataIndex: "tenLyDoTamUng",
      key: "tenLyDoTamUng",
      columnName: t("thuNgan.quanLyTamUng.lyDoDeNghi"),
      show: true,
      align: "left",
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.quanLyTamUng.ngayDeNghi")}
          sort_key="thoiGianDeNghi"
          dataSort={dataSortColumn["thoiGianDeNghi"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "110px",
      dataIndex: "thoiGianDeNghi",
      key: "thoiGianDeNghi",
      columnName: t("thuNgan.quanLyTamUng.ngayDeNghi"),
      show: true,
      render: (field, item, index) => {
        return field && moment(field).format("DD/MM/YYYY HH:mm:ss");
      },
    },

    {
      title: (
        <HeaderSearch
          title={t("common.khoa")}
          sort_key="tenKhoa"
          dataSort={dataSortColumn["tenKhoa"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "150px",
      dataIndex: "tenKhoa",
      key: "tenKhoa",
      columnName: t("common.khoa"),
      show: true,
    },
    {
      title: <HeaderSearch title={t("danhMuc.toaNha")} />,
      width: "80px",
      dataIndex: "quayId",
      key: "quayId",
      columnName: t("danhMuc.toaNha"),
      show: true,
      render: (item) => {
        const toaNhaId =
          (listAllQuayTiepDon || []).find((x) => x.id === item)?.toaNhaId ||
          null;

        if (toaNhaId) {
          return (
            (listAllToaNha || []).find((x) => x.id === toaNhaId)?.ten || ""
          );
        }

        return "";
      },
    },

    {
      title: (
        <HeaderSearch
          title={t("thuNgan.quanLyTamUng.nguoiDeNghi")}
          sort_key="tenNguoiDeNghi"
          dataSort={dataSortColumn["tenNguoiDeNghi"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "150px",
      dataIndex: "tenNguoiDeNghi",
      key: "tenNguoiDeNghi",
      columnName: t("thuNgan.quanLyTamUng.nguoiDeNghi"),
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.tenNguoiTaoQr")}
          sort_key="tenNguoiTaoQr"
          dataSort={dataSortColumn["tenNguoiTaoQr"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "150px",
      dataIndex: "tenNguoiTaoQr",
      key: "tenNguoiTaoQr",
      columnName: t("thuNgan.tenNguoiTaoQr"),
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.thoiGianTaoQr")}
          sort_key="thoiGianTaoQr"
          dataSort={dataSortColumn["thoiGianTaoQr"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "180px",
      dataIndex: "thoiGianTaoQr",
      key: "thoiGianTaoQr",
      columnName: t("thuNgan.thoiGianTaoQr"),
      show: true,
      render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.quanLyTamUng.soPhieuThu")}
          sort_key="soPhieu"
          dataSort={dataSortColumn["soPhieu"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "100px",
      dataIndex: "soPhieu",
      key: "soPhieu",
      align: "left",
      columnName: t("thuNgan.quanLyTamUng.soPhieuThu"),
      show: true,
      render: (field, item, index) => {
        return <div>{field}</div>;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.quanLyTamUng.hinhThucTT")}
          sort_key="phuongThucTtId"
          dataSort={dataSortColumn["phuongThucTtId"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "100px",
      dataIndex: "phuongThucTtId",
      key: "phuongThucTtId",
      columnName: t("thuNgan.quanLyTamUng.hinhThucTT"),
      show: true,
      render: (item, list, index) => {
        if (listAllPhuongThucThanhToan?.length) {
          return (
            listAllPhuongThucThanhToan.find((x) => x.id === item)?.ten || ""
          );
        }
        return "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.tenNganHang")}
          sort_key="nganHangId"
          dataSort={dataSortColumn["nganHangId"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "120px",
      dataIndex: "nganHangId",
      key: "nganHangId",
      columnName: t("thuNgan.tenNganHang"),
      show: true,
      render: (item) => {
        if (listAllDoiTac?.length) {
          return listAllDoiTac.find((x) => x.id === item)?.ten || "";
        }
        return "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.trangThaiTaiNganHang")}
          sort_key="trangThaiThanhToan"
          dataSort={dataSortColumn["trangThaiThanhToan"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "120px",
      dataIndex: "trangThaiThanhToan",
      key: "trangThaiThanhToan",
      columnName: t("thuNgan.trangThaiTaiNganHang"),
      show: true,
      render: (item) => {
        return (
          (listTrangThaiThanhToan || []).find((i) => i.id === item)?.ten || ""
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.maGdKetNoi")}
          sort_key="soPhieuThanhToan"
          dataSort={dataSortColumn["soPhieuThanhToan"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "120px",
      dataIndex: "soPhieuThanhToan",
      key: "soPhieuThanhToan",
      columnName: t("thuNgan.maGdKetNoi"),
      show: true,
    },
    ...(checkRole([ROLES["THU_NGAN"].DUYET_DE_NGHI_TAM_UNG])
      ? [
          {
            title: <HeaderSearch title={t("thuNgan.quanLyTamUng.xacNhan")} />,
            width: "100px",
            align: "center",
            columnName: t("thuNgan.quanLyTamUng.xacNhan"),
            show: true,
            fixed: "right",
            hidden: isNoiTru,
            render: (field, item, index) => {
              return (
                <Button
                  style={{ margin: "auto" }}
                  type="primary"
                  onClick={() => onShowDuyetTamUng(item)}
                >
                  {t("thuNgan.quanLyTamUng.duyet")}
                </Button>
              );
            },
          },
        ]
      : []),
    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("common.thaoTac")}
              <SVG.IcSetting onClick={onSettings} className="icon" />
            </>
          }
        />
      ),
      width: "200px",
      dataIndex: "",
      key: "",
      align: "center",
      fixed: "right",
      ignore: true,
      render: (record, item) => {
        const pttt = (listAllPhuongThucThanhToan || []).find(
          (i) => i.id === item.phuongThucTtId
        );
        let doiTac = listAllDoiTac.find((x) => x.id === item.nganHangId) || {};
        let listMaDoiTac = parseListConfig(maDoiTacQrCodeCapNhatGiaoDich);
        let showCapNhatGiaoDichQr = listMaDoiTac.includes(doiTac?.ma);

        return (
          <div className="action">
            {checkRole([ROLES["THU_NGAN"].SUA_DE_NGHI_TAM_UNG]) && (
              <Tooltip
                title={t("thuNgan.quanLyTamUng.suaPhieuDeNghiTamUng")}
                onClick={() => onShowEditDeNghiTamUng(item)}
              >
                <SVG.IcEdit className="ic-action" />
              </Tooltip>
            )}
            {checkRole([ROLES["THU_NGAN"].XOA_DE_NGHI_TAM_UNG]) && (
              <Tooltip
                title={t("thuNgan.quanLyTamUng.xoaPhieuDeNghi")}
                onClick={() => showModaConfirmRemove(item)}
              >
                <SVG.IcDelete className="ic-action" />
              </Tooltip>
            )}
            {pttt?.loaiPhuongThucTt === LOAI_PHUONG_THUC_TT.QR_CODE &&
              [
                TRANG_THAI_THANH_TOAN_QR.MOI,
                TRANG_THAI_THANH_TOAN_QR.TAO_QR,
                TRANG_THAI_THANH_TOAN_QR.THANH_TOAN,
                TRANG_THAI_THANH_TOAN_QR.TAO_QR_LOI,
              ].includes(item.trangThaiThanhToan) && (
                <Tooltip
                  title={t("thuNgan.xemQrCode")}
                  onClick={() => onViewQrCode(item)}
                >
                  <SVG.IcQrCode
                    color={"var(--color-blue-primary)"}
                    className="ic-action"
                  />
                </Tooltip>
              )}
            {[
              TRANG_THAI_THANH_TOAN_QR.MOI,
              TRANG_THAI_THANH_TOAN_QR.TAO_QR,
              TRANG_THAI_THANH_TOAN_QR.THANH_TOAN,
              TRANG_THAI_THANH_TOAN_QR.TAO_QR_LOI,
            ].includes(item.trangThaiThanhToan) && (
              <Tooltip
                title={t("thuNgan.huyQrcode")}
                onClick={() => onHuyQrCode(item.thanhToanId)}
              >
                <SVG.IcQrCodeOff className="ic-action ic-qr" />
              </Tooltip>
            )}
            {[
              TRANG_THAI_THANH_TOAN_QR.MOI,
              TRANG_THAI_THANH_TOAN_QR.TAO_QR,
            ].includes(item.trangThaiThanhToan) &&
              showCapNhatGiaoDichQr && (
                <Tooltip
                  title={t("thuNgan.capNhatTrangThaiGiaoDichQr")}
                  onClick={() => onCapNhatTrangThaiGiaoDichQr(item.thanhToanId)}
                >
                  <SVG.IcUpdateStatusQr
                    color={"var(--color-blue-primary)"}
                    className="ic-action"
                  />
                </Tooltip>
              )}

            {checkRole([ROLES["THU_NGAN"].IN_DE_NGHI_TAM_UNG]) && (
              <Tooltip
                title={t("common.inPhieu")}
                onClick={() => onInPhieuTamUng(item.id)}
              >
                <SVG.IcPrint className="ic-action" />
              </Tooltip>
            )}
          </div>
        );
      },
    },
  ];
};
