import React, {
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
  useEffect,
  useMemo,
  memo,
} from "react";
import { useDispatch } from "react-redux";
import { Main } from "./styled";
import { Row, Input, message } from "antd";
import { useTranslation } from "react-i18next";
import {
  useListAll,
  useThietLap,
  useStore,
  useEnum,
  useLoading,
  useDelayedState,
  useQueryAll,
} from "hooks";
import { isObject } from "lodash";
import {
  Select,
  Button,
  ModalTemplate,
  InputNumberField,
  ChonQuay,
  ChonCaLamViec,
} from "components";
import {
  DOI_TUONG_KCB,
  THIET_LAP_CHUNG,
  HOTKEY,
  LOAI_PHUONG_THUC_TT,
  ENUM,
  TRANG_THAI_THANH_TOAN_QR,
  LOAI_DOI_TAC,
  LOAI_QUAY,
  ROLES,
  LOAI_DICH_VU,
} from "constants/index";
import { SVG } from "assets";
import cacheKey from "constants/cacheKey";
import { isArray } from "utils/index";
import classNames from "classnames";
import { checkRole } from "lib-utils/role-utils";
import PhuongThucThanhToan from "pages/goiDichVu/ChiTietNguoiBenhSuDungGoi/ModalThemMoiPhieuThuThanhToan/PhuongThucThanhToan";
import { query } from "redux-store/stores";
import { customSortBySttAndName } from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/PhieuLinh/utils";
import nbTamUngProvider from "data-access/nb-tam-ung-provider";
import { toSafePromise } from "lib-utils";

const { useCaLamViec } = ChonCaLamViec;
const { TextArea } = Input;

const ModalTamUng = ({ putDataCenter = () => {} }, ref) => {
  const [refIsSubmit, onChangeStateSubmit] = useDelayedState(3000);
  const [state, _setState] = useState({
    isModalVisible: false,
    discount: 1,
    tienMat: true,
    isQrCode: false,
    nhaTamUng: null,
    data: {},
    nhaTamUng: {},
    renderReady: false,
    tenTaiKhoan: "",
    dsPhuongThucTt: {},
  });
  const setState = (data = {}) => {
    _setState((prevState) => {
      return { ...prevState, ...data };
    });
  };

  const { t } = useTranslation();
  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan");
  const listPhuongThucTTQRHoaDon = useStore(
    "phuongThucTT.listPhuongThucTTQRHoaDon",
    []
  );
  const listDoiTuongPhuongThucTT = useStore(
    "loaiDoiTuongPhuongThucTT.listData",
    []
  );
  const listPhuongThucTT = useStore("phuongThucTT.listPhuongThucTT", []);

  const {
    nbDotDieuTri: { getThongTinCoBan },
    loaiDoiTuongPhuongThucTT: { getList },
    phuongThucTT: { getListPhuongThucTTQRHoaDon },
    doiTacThanhToan: { getListByDoiTacId },
    thuNgan: { huyQrThanhToan },
    nbTheNb: { createOrEdit: postRecord },
  } = useDispatch();
  const [dataLY_DO_TAM_UNG, loadFinishLyDo] = useThietLap(
    THIET_LAP_CHUNG.LY_DO_TAM_UNG
  );
  const [dataHINH_THUC_THANH_TOAN, loadFinishHinhThuc] = useThietLap(
    THIET_LAP_CHUNG.HINH_THUC_THANH_TOAN
  );
  const [dataMA_DOI_TAC_QRCODE_NGOAI_TRU, loadFinishMaDoiTacNgoaiTru] =
    useThietLap(THIET_LAP_CHUNG.MA_DOI_TAC_QRCODE_NGOAI_TRU);
  const [dataMA_DOI_TAC_QRCODE_NOI_TRU, loadFinishMaDoiTacNoiTru] = useThietLap(
    THIET_LAP_CHUNG.MA_DOI_TAC_QRCODE_NOI_TRU
  );
  const [dataCO_TIEN_TAM_UNG_MOI_DUOC_DE_NGHI_TAM_UNG_BANG_QR] = useThietLap(
    THIET_LAP_CHUNG.CO_TIEN_TAM_UNG_MOI_DUOC_DE_NGHI_TAM_UNG_BANG_QR
  );

  const [HINH_THUC_THANH_TOAN_MAC_DINH] = useThietLap(
    THIET_LAP_CHUNG.HINH_THUC_THANH_TOAN_MAC_DINH
  );
  const [dataPHIEU_TAM_UNG_NHIEU_PTTT] = useThietLap(
    THIET_LAP_CHUNG.PHIEU_TAM_UNG_NHIEU_PTTT
  );

  const [dataGOI_Y_TIEN_TAM_UNG_THU_THEM, loadFinishGoiYTienTamUngThuThem] =
    useThietLap(THIET_LAP_CHUNG.GOI_Y_TIEN_TAM_UNG_THU_THEM);
  const [
    dataMA_DOI_TAC_QRCODE_KIEM_TRA_HIEN_THI_THEO_LOAI_DICH_VU,
    loadFinishMaDoiTacKiemTraTheoLoaiDichVu,
  ] = useThietLap(
    THIET_LAP_CHUNG.MA_DOI_TAC_QRCODE_KIEM_TRA_HIEN_THI_THEO_LOAI_DICH_VU
  );
  const [dataMAP_THE_KHI_TAM_UNG_L1] = useThietLap(
    THIET_LAP_CHUNG.MAP_THE_KHI_TAM_UNG_L1
  );

  const [listTrangThaiThanhToan] = useEnum(ENUM.TRANG_THAI_THANH_TOAN);

  const refModal = useRef(null);
  const refCallBack = useRef(null);
  const refF4Func = useRef(null);
  const refInputSoTien = useRef(null);
  const refActionPost = useRef(null);
  const refActionHotkey = useRef(null);
  const [listAllToaNha] = useListAll("toaNha", {}, true);
  const { data: listAllPhuongThucTT } = useQueryAll(
    query.phuongThucTT.queryAllPhuongThucTT({
      enabled: state.show,
    })
  );

  const [listAllLyDoTamUng] = useListAll("lyDoTamUng", {}, state.show);
  const [listAllLoaiDoiTuong] = useListAll("loaiDoiTuong", {}, state.show);
  const { showLoading, hideLoading } = useLoading();
  const [caLamViec] = useCaLamViec();

  useImperativeHandle(ref, () => ({
    show: (
      {
        nhaTamUngId,
        quayId,
        isDuyet,
        focusInput,
        toaNhaId,
        quayMacDinhNoiTruQRCode = null,
        actionType,
        ...data
      } = {},
      callback
    ) => {
      setState({
        data,
        nhaTamUngId,
        quayId,
        show: true,
        isQrCode: false,
        isDuyet,
        errGhichu: false,
        batBuocNganHang: false,
        tab: data.tab,
        focusInput,
        renderReady: false,
        toaNhaId,
        actionType,
        maThe: null,
        showMaThe: false,
        ...(quayMacDinhNoiTruQRCode
          ? { nhaTamUng: quayMacDinhNoiTruQRCode, hideChonQuay: true }
          : { hideChonQuay: false }),
      });
      refCallBack.current = callback;
      refActionPost.current = data?.action;
      refActionHotkey.current = data?.actionHotkey;
    },
  }));

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
      getList({ loaiDoiTuongId: thongTinCoBan.loaiDoiTuongId, active: true });
      getListPhuongThucTTQRHoaDon({ active: true });
      nbTamUngProvider
        .search({
          nbDotDieuTriId: thongTinCoBan.id,
          dsTrangThai: [40, 50, 55, 15, 56],
          page: 0,
          size: 500,
        })
        .then((res) => {
          setState({
            showMaThe: isArray(res?.data, true)
              ? res.data.some(
                  (x) =>
                    x.trangThai !== 100 &&
                    [2, 3, 4, 6, 9].includes(x.doiTuongKcb)
                )
              : true,
          });
        });
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show, thongTinCoBan]);

  const listAllPhuongThucThanhToan = useMemo(() => {
    return customSortBySttAndName(listAllPhuongThucTT, ["uuTien", "ten"]);
  }, [listAllPhuongThucTT]);

  const isNoiTru = [2, 3, 4, 6, 9].includes(thongTinCoBan?.doiTuongKcb);

  const setListSelectNganHang = (data) => {
    let listSelectNganHang = [];
    if (isArray(data, true)) {
      listSelectNganHang = data;
      let listMaDoiTac =
        dataMA_DOI_TAC_QRCODE_KIEM_TRA_HIEN_THI_THEO_LOAI_DICH_VU
          ?.split(",")
          .map((x) => x?.trim())
          .filter(Boolean);
      if (isArray(listMaDoiTac, true)) {
        let dsDoiTac = data.filter((x) => listMaDoiTac.includes(x.ma));

        if (isNoiTru) {
          dsDoiTac = dsDoiTac.filter((x) =>
            (x.dsLoaiDichVu || []).includes(LOAI_DICH_VU.NOI_TRU)
          );
        } else {
          dsDoiTac = dsDoiTac.filter((x) =>
            (x.dsLoaiDichVu || []).includes(LOAI_DICH_VU.KHAM)
          );
        }

        listSelectNganHang = dsDoiTac;
      }
    }

    listSelectNganHang = listSelectNganHang.filter((item) => item.active);

    return listSelectNganHang;
  };

  useEffect(() => {
    if (
      state.show &&
      loadFinishHinhThuc &&
      loadFinishLyDo &&
      loadFinishMaDoiTacNgoaiTru &&
      loadFinishMaDoiTacNoiTru &&
      loadFinishGoiYTienTamUngThuThem &&
      loadFinishMaDoiTacKiemTraTheoLoaiDichVu
    ) {
      const phuongThuc = (listAllPhuongThucThanhToan || []).find(
        (item) => item.ma === dataHINH_THUC_THANH_TOAN
      );
      const phuongThucTTDoiTuong = listDoiTuongPhuongThucTT.find(
        (x) => x.phuongThucTtId === phuongThuc?.id
      );
      const tongTien = (() => {
        const {
          tienTamUng = 0,
          tienHoanUng = 0,
          tienChuaThanhToan = 0,
          doiTuongKcb,
          capCuu,
        } = thongTinCoBan || {};

        // Nếu đủ điều kiện gợi ý tiền tạm ứng thu thêm
        const goiYTienTamUng = dataGOI_Y_TIEN_TAM_UNG_THU_THEM?.split("/");
        if (
          goiYTienTamUng?.length &&
          goiYTienTamUng[0]?.eval() &&
          state.tab === "thuTamUng" &&
          !state?.data?.tongTien
        ) {
          // dataGOI_Y_TIEN_TAM_UNG_THU_THEM 1 là viện thủ đức, 2 là nội tiết
          if (
            ![2, 3, 4].includes(doiTuongKcb) &&
            !capCuu &&
            goiYTienTamUng[1] == "1"
          ) {
            const soTienConLai = tienTamUng - tienHoanUng - tienChuaThanhToan;
            if (soTienConLai >= 0) return 0;
            const soTienCanTamUng = Math.abs(soTienConLai);

            return soTienCanTamUng <= 200000
              ? 200000
              : Math.ceil(soTienCanTamUng / 100000) * 100000;
          } else if (goiYTienTamUng[1] == "2") {
            const soTienConLai = tienTamUng - tienHoanUng - tienChuaThanhToan;
            if (soTienConLai >= 0) return 0;
            const soTienCanTamUng = Math.abs(soTienConLai);
            return soTienCanTamUng;
          }
        }

        // Lấy ngưỡng tạm ứng từ phương thức thanh toán hoặc loại đối tượng
        const nguongTamUng =
          phuongThucTTDoiTuong?.nguongTamUng ||
          listAllLoaiDoiTuong.find((x) => x.id === thongTinCoBan.loaiDoiTuongId)
            ?.nguongTamUng;
        // Trả về tổng tiền từ state hoặc tính toán dựa trên đối tượng KCB
        return (
          state?.data?.tongTien ||
          (thongTinCoBan?.doiTuongKcb === DOI_TUONG_KCB.NGOAI_TRU
            ? nguongTamUng
            : null)
        );
      })();

      const listData =
        state.tab === "deNghiTamUng"
          ? listPhuongThucTTQRHoaDon
          : listPhuongThucTT;
      const selectedHtttQrHoaDon = (listData || []).find(
        (x) => x.id == state?.data.phuongThucTtId
      );
      const selectedPttt = (listAllPhuongThucThanhToan || []).find(
        (x) => x.id == state.data.phuongThucTtId
      );

      //hiển thị thông tin tài khoản
      if (
        selectedHtttQrHoaDon?.loaiPhuongThucTt ===
          LOAI_PHUONG_THUC_TT.QR_CODE &&
        state.data?.nganHangId
      ) {
        onGetListTaiKhoan(state.data?.nganHangId);
      }

      let _state = {
        tongTien: tongTien,
        phuongThucTtId:
          (selectedHtttQrHoaDon ? state?.data?.phuongThucTtId : null) ||
          (state.tab === "thuTamUng" ? phuongThuc?.id : null),
        isDisabled:
          [
            TRANG_THAI_THANH_TOAN_QR.MOI,
            TRANG_THAI_THANH_TOAN_QR.TAO_QR,
            TRANG_THAI_THANH_TOAN_QR.THANH_TOAN,
          ].includes(state?.data?.trangThaiThanhToan) &&
          selectedHtttQrHoaDon &&
          state.isDuyet,
        maChuanChi: state.isDuyet
          ? state?.data?.soPhieuThanhToan || state?.data?.maChuanChi
          : state?.data?.maChuanChi,
        ghiChu: state?.data?.ghiChu,
        lyDoTamUngId:
          state?.data?.lyDoTamUngId ||
          (listAllLyDoTamUng || []).find(
            (item) => item.ma === dataLY_DO_TAM_UNG
          )?.id,
        id: state.data?.id,
        loaiDoiTac: selectedPttt ? selectedPttt.loaiDoiTac : null,
        nganHangId: state.data?.nganHangId || null,
        nbDotDieuTriId: state.data?.nbDotDieuTriId,
        tienMat: state.data?.phuongThucTtId
          ? listAllPhuongThucThanhToan.find(
              (x) => x.id === state.data.phuongThucTtId
            )?.tienMat
          : phuongThuc?.tienMat,
        parentNhaTamUng: state.data?.parentNhaTamUng,
        trangThaiThanhToan: state.data?.trangThaiThanhToan,
        thanhToanId: state.data?.thanhToanId,
        ...(selectedHtttQrHoaDon && {
          batBuocNganHang:
            selectedHtttQrHoaDon.loaiPhuongThucTt ===
            LOAI_PHUONG_THUC_TT.QR_CODE,
          listSelectNganHang:
            selectedHtttQrHoaDon.loaiPhuongThucTt ===
            LOAI_PHUONG_THUC_TT.QR_CODE
              ? setListSelectNganHang(selectedHtttQrHoaDon.dsNhaCungCap)
              : selectedHtttQrHoaDon.dsNhaCungCap,
        }),
        renderReady: true,
        ...(state.data?.id && {
          orgData: {
            phuongThucTtId:
              (selectedHtttQrHoaDon ? state?.data?.phuongThucTtId : null) ||
              (state.tab === "thuTamUng" ? phuongThuc?.id : null),
            tongTien: tongTien,
            nganHangId: state.data?.nganHangId || null,
          },
        }),
      };

      if (!_state.ghiChu && state.isDuyet) {
        _state.ghiChu = listAllLyDoTamUng.find(
          (item) => item.id === _state.lyDoTamUngId
        )?.ten;
      }

      setState({ ..._state });
    }
  }, [
    listAllLyDoTamUng,
    dataLY_DO_TAM_UNG,
    listAllPhuongThucThanhToan,
    listPhuongThucTTQRHoaDon,
    dataHINH_THUC_THANH_TOAN,
    loadFinishHinhThuc,
    loadFinishLyDo,
    state.data,
    listDoiTuongPhuongThucTT,
    listPhuongThucTT,
    state.show,
    thongTinCoBan,
    state.isDuyet,
    state.tab,
    state.focusInput,
    loadFinishMaDoiTacNgoaiTru,
    loadFinishMaDoiTacNoiTru,
    dataMA_DOI_TAC_QRCODE_NGOAI_TRU,
    dataMA_DOI_TAC_QRCODE_NOI_TRU,
    loadFinishMaDoiTacKiemTraTheoLoaiDichVu,
    dataMA_DOI_TAC_QRCODE_KIEM_TRA_HIEN_THI_THEO_LOAI_DICH_VU,
    dataGOI_Y_TIEN_TAM_UNG_THU_THEM,
    loadFinishGoiYTienTamUngThuThem,
  ]);

  useEffect(() => {
    if (state.show && state.focusInput && state.renderReady) {
      setTimeout(() => {
        refInputSoTien.current && refInputSoTien.current.focus();
      }, 300);
    }
  }, [state.show, state.focusInput, state.renderReady]);

  const loaiPhuongThucTt = useMemo(() => {
    return (listAllPhuongThucThanhToan || []).find(
      (i) => i.id === state.phuongThucTtId
    )?.loaiPhuongThucTt;
  }, [listAllPhuongThucThanhToan, state.phuongThucTtId]);

  const batBuocGhiChu = useMemo(() => {
    return (
      state.tab === "thuTamUng" &&
      [
        TRANG_THAI_THANH_TOAN_QR.MOI,
        TRANG_THAI_THANH_TOAN_QR.TAO_QR,
        TRANG_THAI_THANH_TOAN_QR.TAO_QR_LOI,
      ].includes(state.trangThaiThanhToan)
    );
  }, [state.trangThaiThanhToan, state.tab]);

  const isShowPtttQrHoaDon = useMemo(() => {
    if (state.tab === "deNghiTamUng") {
      if (dataCO_TIEN_TAM_UNG_MOI_DUOC_DE_NGHI_TAM_UNG_BANG_QR?.eval()) {
        const { tienTamUng = 0, tienHoanUng = 0 } = thongTinCoBan || {};
        return tienTamUng - tienHoanUng > 0;
      } else {
        return true;
      }
    }
    return false;
  }, [
    dataCO_TIEN_TAM_UNG_MOI_DUOC_DE_NGHI_TAM_UNG_BANG_QR,
    state.tab,
    thongTinCoBan,
  ]);

  const isPhieuTamUngNhieuPTTT = useMemo(() => {
    return (
      state.tab === "thuTamUng" &&
      !state.isDuyet &&
      dataPHIEU_TAM_UNG_NHIEU_PTTT?.eval()
    );
  }, [dataPHIEU_TAM_UNG_NHIEU_PTTT, state.tab, state.isDuyet]);

  const hotKeys = [
    {
      keyCode: HOTKEY.ESC,
      onEvent: () => handleClickBack(),
    },
    {
      keyCode: HOTKEY.F4,
      onEvent: async () => {
        try {
          refF4Func.current && (await refF4Func.current());
          refActionHotkey.current && refActionHotkey.current();
        } catch (error) {}
      },
    },
    {
      keyCode: HOTKEY.F2,
      onEvent: () => {
        refInputSoTien.current && refInputSoTien.current.focus();
      },
    },
  ];

  const onGetListTaiKhoan = async (nganHangId) => {
    const resTK = await getListByDoiTacId({ dsDoiTacId: nganHangId });

    if (resTK.length) {
      //Check thông tin thanh toán nếu có tòa nhà = tòa nhà quầy thu ngân chọn → lấy line này (>1 line thỏa mãn thì lấy line đầu tiên)
      const _selectTK = resTK.find((x) => x.toaNhaId == state.toaNhaId);
      if (_selectTK) {
        setState({ tenTaiKhoan: _selectTK.taiKhoan });
      } else {
        setState({ tenTaiKhoan: resTK[0]?.taiKhoan });
      }
    } else {
      setState({ tenTaiKhoan: "" });
    }
  };

  const onChange = (key) => (e) => {
    let value = "";
    if (e?.target) {
      value = e?.target?.value || e?.floatValue;
    } else if (isObject(e)) {
      value = e?.floatValue || "";
    } else {
      value = e;
    }

    if (key == "nganHangId" && !!value) {
      onGetListTaiKhoan(value);
    }

    if (key == "phuongThucTtId") {
      const selectedPttt = (listAllPhuongThucThanhToan || []).find(
        (x) => x.id == value
      );
      const phuongThucTTDoiTuong = listDoiTuongPhuongThucTT.find(
        (x) => x.phuongThucTtId === value
      );
      if (selectedPttt) {
        setState({
          [key]: value,
          loaiDoiTac: selectedPttt.loaiDoiTac,
          tienMat: selectedPttt.tienMat,
          ...(selectedPttt.loaiDoiTac !== 90 && { nganHangId: null }),
          tongTien: phuongThucTTDoiTuong
            ? phuongThucTTDoiTuong?.nguongTamUng
            : state.tongTien,
        });
      }
      const listData =
        state.tab === "deNghiTamUng"
          ? listPhuongThucTTQRHoaDon
          : listPhuongThucTT;
      const selectedHtttQrHoaDon = (listData || []).find((x) => x.id == value);
      if (selectedHtttQrHoaDon) {
        let listSelectNganHang =
          selectedHtttQrHoaDon.loaiPhuongThucTt === LOAI_PHUONG_THUC_TT.QR_CODE
            ? setListSelectNganHang(selectedHtttQrHoaDon.dsNhaCungCap)
            : selectedHtttQrHoaDon.dsNhaCungCap;

        //hiển thị thông tin tài khoản
        let maDoiTac = isNoiTru
          ? dataMA_DOI_TAC_QRCODE_NOI_TRU
          : dataMA_DOI_TAC_QRCODE_NGOAI_TRU;
        let _state = {
          listSelectNganHang,
          batBuocNganHang:
            selectedHtttQrHoaDon.loaiPhuongThucTt ===
            LOAI_PHUONG_THUC_TT.QR_CODE,
          isQrCode:
            selectedHtttQrHoaDon.loaiPhuongThucTt ===
            LOAI_PHUONG_THUC_TT.QR_CODE,
        };
        if (
          selectedHtttQrHoaDon.loaiPhuongThucTt === LOAI_PHUONG_THUC_TT.QR_CODE
        ) {
          if (maDoiTac) {
            const nganHangId = listSelectNganHang.find(
              (i) => i.ma === maDoiTac
            )?.id;
            if (nganHangId) {
              _state.nganHangId = nganHangId;
              onGetListTaiKhoan(nganHangId);
            }
          } else if (listSelectNganHang?.length === 1) {
            _state.nganHangId = listSelectNganHang[0]?.id;
            onGetListTaiKhoan(listSelectNganHang[0]?.id);
          }
        }
        setState({ ..._state });
      } else {
        setState({
          [key]: value,
          nganHangId: null,
          listSelectNganHang: [],
          batBuocNganHang: false,
          isQrCode: false,
        });
      }
    } else {
      let errGhichu = false;
      if (key === "ghiChu") {
        errGhichu = !value;
      }
      setState({ [key]: value, errGhichu });
    }
  };

  const blockInvalidChar = (e) =>
    ["e", "E", "+", "-"].includes(e.key) && e.preventDefault();

  const handleClickBack = () => {
    setState({
      tongTien: null,
      phuongThucTtId: null,
      maChuanChi: null,
      ghiChu: null,
      lyDoTamUngId: null,
      nbDotDieuTriId: null,
      show: false,
      nganHangId: null,
      listSelectNganHang: [],
      dsPhuongThucTt: {},
    });
  };

  const submitHandler = async () => {
    if (refIsSubmit.current) return;
    onChangeStateSubmit(true);
    if (isPhieuTamUngNhieuPTTT) {
      if (Object.values(state.dsPhuongThucTt).every((item) => !item.tongTien)) {
        message.error(t("thuNgan.thieuThongTinSoTienDeNghi"));
        throw new Error(t("thuNgan.thieuThongTinSoTienDeNghi"));
      }
    } else {
      if (!state.tongTien) {
        message.error(t("thuNgan.thieuThongTinSoTienDeNghi"));
        throw new Error(t("thuNgan.thieuThongTinSoTienDeNghi"));
      }
      if (state.batBuocNganHang && !state.nganHangId && state.phuongThucTtId) {
        message.error(t("thuNgan.vuiLongChonTenNganHang"));
        throw new Error(t("thuNgan.vuiLongChonTenNganHang"));
      }
    }
    if (batBuocGhiChu && !state.ghiChu) {
      message.error(t("danhMuc.vuiLongNhapGhiChu"));
      setState({ errGhichu: true });
      throw new Error(t("danhMuc.vuiLongNhapGhiChu"));
    }
    const {
      tongTien,
      phuongThucTtId,
      maChuanChi,
      lyDoTamUngId,
      ghiChu,
      nganHangId,
      parentNhaTamUng,
      quayId,
      nhaTamUngId,
      orgData,
      isQrCode,
      tab,
      thanhToanId,
      isDuyet,
      data,
    } = state || {};
    let _isQrCode = !!isQrCode;
    const listData =
      tab === "deNghiTamUng" ? listPhuongThucTTQRHoaDon : listPhuongThucTT;
    const selectedHtttQrHoaDon = (listData || []).find(
      (x) => x.id == phuongThucTtId
    );

    const isQrCodePaymentMethod =
      selectedHtttQrHoaDon?.loaiPhuongThucTt === LOAI_PHUONG_THUC_TT.QR_CODE;

    // Case: Sửa đề nghị tạm ứng
    if (orgData && isQrCodePaymentMethod && nganHangId) {
      const hasChangedAmount = tongTien !== orgData.tongTien;
      const hasChangedPaymentMethod = phuongThucTtId !== orgData.phuongThucTtId;
      const hasChangedBank = nganHangId !== orgData.nganHangId;

      // Set _isQrCode = true nếu 1 trong 3 biến tongTien, phuongThucTtId, nganHangId thay đổi
      if (hasChangedAmount || hasChangedPaymentMethod || hasChangedBank) {
        _isQrCode = true;
      }
    }

    // Case: huỷ qr sau đó sửa qr lúc này cũng gen lại qr mới
    if (
      isQrCodePaymentMethod &&
      nganHangId &&
      !data?.qrThanhToan?.qr &&
      !!data?.id
    ) {
      _isQrCode = true;
    }
    if (refActionPost.current) {
      showLoading();
      let _nhaTamUngId, _quayId;
      if (isDuyet) {
        // khi duyệt lấy theo toaNhaId và quayId của quầy hiện tại
        _nhaTamUngId =
          state.nhaTamUng?.toaNhaId || parentNhaTamUng?.toaNhaId || toaNhaId;
        _quayId = state.nhaTamUng?.id || quayId;
      } else {
        _nhaTamUngId =
          nhaTamUngId || parentNhaTamUng?.toaNhaId || state.nhaTamUng?.toaNhaId;
        _quayId = quayId || state.nhaTamUng?.id;
      }

      if (
        isDuyet &&
        data?.qrThanhToan?.qr &&
        phuongThucTtId !== orgData.phuongThucTtId
      ) {
        await huyQrThanhToan({ thanhToanId: data?.thanhToanId });
        putDataCenter({ ma: "QR_VALUE", value: null });
        _isQrCode = false;
      }

      if (isPhieuTamUngNhieuPTTT) {
        await Promise.all(
          Object.values(state.dsPhuongThucTt).map(async (item) => {
            return await refActionPost.current({
              tongTien: item.tongTien,
              phuongThucTtId: item.phuongThucTtId,
              maChuanChi: item.maChuanChi,
              lyDoTamUngId,
              ghiChu,
              nbDotDieuTriId: thongTinCoBan?.id || state?.nbDotDieuTriId,
              id: state?.id,
              nhaTamUngId: _nhaTamUngId,
              quayId: _quayId,
              caLamViecId: caLamViec?.id,
              nganHangId: item.nganHangId ? item.nganHangId : null,
              thanhToanId: thanhToanId || null,
            });
          })
        )
          .then((res) => {
            setState({ dsPhuongThucTt: {} });
            //lọc phiếu tạm ứng thanh toán QR
            const phieuQR = (res || []).find(
              (item) =>
                item?.data?.phuongThucTt?.loaiPhuongThucTt ===
                LOAI_PHUONG_THUC_TT.QR_CODE
            );
            const dsPhieuKhacQR = (res || []).filter(
              (item) =>
                item?.data?.phuongThucTt?.loaiPhuongThucTt !==
                LOAI_PHUONG_THUC_TT.QR_CODE
            );
            //nếu có phiếu QR thì callback riêng để xử lý call tạo QR thanh toán
            if (phieuQR) {
              refCallBack.current(
                phieuQR?.data?.id,
                true,
                phieuQR?.data?.trangThai,
                phieuQR
              );
            }

            //nếu còn các pttt khác thì call để in phiếu
            if (dsPhieuKhacQR.length > 0) {
              refCallBack.current(
                (dsPhieuKhacQR || []).map((item) => item?.data?.id),
                null,
                null,
                {}
              );
            }
            getThongTinCoBan(thongTinCoBan?.id || state?.nbDotDieuTriId);
          })
          .catch((err) => {
            console.error(err);
          })
          .finally(() => {
            hideLoading();
            handleClickBack();
            onChangeStateSubmit(false);
          });
      } else {
        await refActionPost
          .current({
            tongTien,
            phuongThucTtId: phuongThucTtId
              ? phuongThucTtId
              : tab === "thuTamUng"
              ? listPhuongThucTT.find(
                  (x) => x.ma === HINH_THUC_THANH_TOAN_MAC_DINH
                )?.id
              : undefined,
            maChuanChi,
            lyDoTamUngId,
            ghiChu,
            nbDotDieuTriId: thongTinCoBan?.id || state?.nbDotDieuTriId,
            id: state?.id,
            nhaTamUngId: _nhaTamUngId,
            quayId: _quayId,
            caLamViecId: caLamViec?.id,
            nganHangId: nganHangId ? nganHangId : null,
            thanhToanId: thanhToanId || null,
          })
          .then(async (s) => {
            // ---------Đặt log để theo dõi trạng thái qr----------
            console.log("🚀 _isQrCode", _isQrCode);
            console.log("🚀 s", s);
            // -----------------------------------------------------
            if (state.maThe) {
              await toSafePromise(
                postRecord(
                  {
                    nbDotDieuTriId: thongTinCoBan?.id || state?.nbDotDieuTriId,
                    maThe: state.maThe?.trim(),
                  },
                  { ignoreMessage: true }
                )
              );
            }
            hideLoading();
            refCallBack.current(s?.data?.id, _isQrCode, s?.data?.trangThai, s);
            if (s?.code !== 1025) {
              handleClickBack();
            }
            getThongTinCoBan(thongTinCoBan?.id || state?.nbDotDieuTriId);
          })
          .catch((err) => {
            console.log("🚀 err", err);
            // 1028: Tồn tại hóa đơn bảo hiểm ngoại trú đã thanh toán có tổng tiền BH < 15% tháng lương cơ bản.
            // 1036: SAKURA-56127
            if ([1028, 1036].includes(err?.code)) {
              refCallBack.current(null, null, null, err);
            }
            hideLoading();
            handleClickBack();
          })
          .finally(() => {
            onChangeStateSubmit(false);
          });
      }
    }
  };

  const handleClickNext = async () => {
    try {
      await submitHandler();
    } catch (error) {}
  };
  refF4Func.current = handleClickNext;

  const disabledOther = checkRole([
    ROLES["THU_NGAN"].SUA_PHIEU_THU_TAM_UNG_CAC_TRUONG_KHAC_CHUA_TAM_UNG_QR,
  ])
    ? false
    : !!state.isDisabled;

  const renderTenNganHang = () => {
    return (
      <div className="line__content__item" style={{ paddingLeft: "25px" }}>
        <div className="line__content__item__label">
          {t("thuNgan.tenNganHang")}
          {!state.nganHangId &&
            state.phuongThucTtId &&
            state.batBuocNganHang && <b style={{ color: "red" }}>* </b>}
        </div>
        <div className="line__content__item__content">
          <Select
            onChange={onChange("nganHangId")}
            value={state.nganHangId}
            placeholder={t("thuNgan.chonNganHang")}
            data={state.listSelectNganHang || []}
            disabled={disabledOther}
            className={"select-disabled"}
          />
          {!state.nganHangId &&
            state.phuongThucTtId &&
            state.batBuocNganHang && (
              <div className="error" style={{ height: "0px" }}>
                {t("thuNgan.vuiLongChonTenNganHang")}
              </div>
            )}
        </div>
      </div>
    );
  };

  const renderToaNha = () => {
    const _toaNhaId =
      state.nhaTamUngId ||
      (state.tab === "thuTamUng"
        ? state.parentNhaTamUng?.toaNhaId || state.nhaTamUng?.toaNhaId
        : "");

    return (
      <div className="line__content__item" style={{ paddingLeft: "25px" }}>
        <div className="line__content__item__label">{t("danhMuc.toaNha")}</div>
        <div className="line__content__item__content">
          <Input
            value={listAllToaNha.find((x) => x.id === _toaNhaId)?.ten || ""}
            disabled={true}
          />
        </div>
      </div>
    );
  };

  const onChangePhuongThucThanhToan = (dsPhuongThucTt) => {
    setState({ dsPhuongThucTt: { ...dsPhuongThucTt } });
  };

  return (
    <ModalTemplate
      width={850}
      ref={refModal}
      closable={false}
      onCancel={handleClickBack}
      destroyOnClose={true}
      title={
        state.tab === "thuTamUng"
          ? t("thuNgan.quanLyTamUng.phieuThuTamUng")
          : t("thuNgan.quanLyTamUng.phieuDeNghiTamUng")
      }
      rightTitle={
        <span>
          {thongTinCoBan?.tenNb && thongTinCoBan?.tenNb}
          {thongTinCoBan?.tuoi
            ? ` - ${thongTinCoBan?.tuoi} tuổi`
            : thongTinCoBan?.thangTuoi
            ? ` - ${thongTinCoBan?.thangTuoi} tháng`
            : ""}
        </span>
      }
      actionLeft={<Button.QuayLai onClick={handleClickBack} />}
      actionRight={
        <Button
          minWidth={100}
          type="primary"
          onClick={handleClickNext}
          iconHeight={20}
          rightIcon={<SVG.IcSuccess />}
        >
          {t("thuNgan.quanLyTamUng.xacNhan")} [F4]
        </Button>
      }
      hotKeys={hotKeys}
    >
      <Main>
        {!state.hideChonQuay && (
          <ChonQuay
            dsLoaiQuay={LOAI_QUAY.THU_NGAN}
            cacheKey={cacheKey.DATA_NHA_TAM_UNG}
            onChange={(value) => {
              setState({ nhaTamUng: value });
            }}
            showContent={false}
          />
        )}

        <Row>
          {isPhieuTamUngNhieuPTTT && (
            <PhuongThucThanhToan
              onChange={onChangePhuongThucThanhToan}
              dsPhuongThucTt={state.dsPhuongThucTt}
              toaNhaId={state.nhaTamUngId}
            />
          )}

          {!isPhieuTamUngNhieuPTTT && (
            <div className="line">
              <div className="line__label">
                <span>
                  {state.tab === "thuTamUng"
                    ? t("thuNgan.quanLyTamUng.soTienTamUng")
                    : t("thuNgan.quanLyTamUng.soTienDeNghi")}{" "}
                  <b style={{ color: "red" }}>*</b>
                </span>
              </div>
              <div className="line__content">
                <InputNumberField
                  onKeyDown={blockInvalidChar}
                  onValueChange={onChange("tongTien")}
                  min={0}
                  value={state.tongTien}
                  style={{
                    textAlign: "right",
                    fontWeight: "bold",
                  }}
                  // defaultValue={infoPatient?.phieuThu?.tienMienGiam}
                  placeholder={t("thuNgan.nhapSoTien")}
                  decimalScale={0}
                  getInputRef={refInputSoTien}
                  disabled={!!state.isDisabled}
                />
              </div>
            </div>
          )}

          {!isPhieuTamUngNhieuPTTT && state.tab === "thuTamUng" && (
            <>
              <div className="line">
                <div className="line__label">
                  <span>
                    {t("thuNgan.quanLyTamUng.hinhThucTT")}{" "}
                    <b style={{ color: "red" }}>*</b>
                  </span>
                </div>

                <div className="line__content">
                  <div className="line__content__first-item">
                    <Select
                      data={listAllPhuongThucThanhToan}
                      value={state.phuongThucTtId}
                      onChange={onChange("phuongThucTtId")}
                      className="input-option select-disabled"
                      placeholder={t("thuNgan.nhapPhuongThucThanhToan")}
                      disabled={disabledOther}
                    />
                  </div>
                  {state?.loaiDoiTac == LOAI_DOI_TAC.THANH_TOAN &&
                    renderTenNganHang()}
                </div>
              </div>
              {!state?.tienMat && (
                <div className="line">
                  <div className="line__label" style={{ width: 150 }}>
                    <span>
                      {t("thuNgan.quanLyTamUng.maChuanChi/maGiaoDich")}
                    </span>
                  </div>
                  <div className="line__content">
                    <div className="line__content__first-item">
                      <TextArea
                        value={state.maChuanChi}
                        onChange={onChange("maChuanChi")}
                        autoSize
                        style={{ width: "100%" }}
                        className="input-option"
                        placeholder={t("thuNgan.nhapMaChuanChi")}
                      />
                    </div>
                    {state.batBuocNganHang && renderToaNha()}
                  </div>
                </div>
              )}
            </>
          )}

          {!isPhieuTamUngNhieuPTTT &&
            loaiPhuongThucTt === LOAI_PHUONG_THUC_TT.QR_CODE &&
            state.tab === "thuTamUng" && (
              <div className="line">
                <div className="line__label" style={{ width: 150 }}>
                  {t("common.taiKhoan")}
                </div>
                <div className="line__content">
                  <Input
                    value={state.nganHangId ? state.tenTaiKhoan || "" : ""}
                    disabled={true}
                  />
                </div>
              </div>
            )}

          <div className="line">
            <div className="line__label">
              <span>
                {state.tab === "thuTamUng"
                  ? t("thuNgan.quanLyTamUng.lyDoTamUng")
                  : t("thuNgan.quanLyTamUng.lyDoDeNghi")}
                {""}
                <b style={{ color: "red" }}> *</b>
              </span>
            </div>
            <div className="line__content">
              <Select
                data={listAllLyDoTamUng}
                value={state.lyDoTamUngId}
                onChange={onChange("lyDoTamUngId")}
                className={classNames({
                  "select-disabled": !checkRole([
                    ROLES["THU_NGAN"].SUA_LY_DO_TAM_UNG,
                  ]),
                })}
                placeholder={
                  state.tab === "thuTamUng"
                    ? t("thuNgan.nhapLyDoTamUng")
                    : t("thuNgan.nhapLyDoDeNghi")
                }
                disabled={!checkRole([ROLES["THU_NGAN"].SUA_LY_DO_TAM_UNG])}
              />
            </div>
          </div>

          {!isPhieuTamUngNhieuPTTT &&
            loaiPhuongThucTt === LOAI_PHUONG_THUC_TT.QR_CODE &&
            state.trangThaiThanhToan &&
            state.tab === "thuTamUng" && (
              <div className="line">
                <div className="line__label" style={{ width: 150 }}>
                  {" "}
                  {t("thuNgan.trangThaiTaiNganHang")}
                </div>
                <div className="line__content">
                  <Select
                    data={listTrangThaiThanhToan}
                    value={state.trangThaiThanhToan}
                    placeholder={t("thuNgan.trangThaiTaiNganHang")}
                    className="select-disabled"
                    disabled={true}
                  />
                </div>
              </div>
            )}

          <div className="line">
            <div className="line__label">
              {" "}
              {t("thuNgan.quanLyTamUng.ghiChu")}
              {batBuocGhiChu && <b style={{ color: "red" }}> *</b>}
            </div>
            <div className="line__content__item__content">
              <Input
                value={state.ghiChu}
                onChange={onChange("ghiChu")}
                className="input-option"
                placeholder={t("common.nhapGhiChu")}
              />
              {state.errGhichu && batBuocGhiChu && (
                <div className="error" style={{ height: "0px" }}>
                  {t("danhMuc.vuiLongNhapGhiChu")}
                </div>
              )}
            </div>
          </div>

          {!isPhieuTamUngNhieuPTTT && isShowPtttQrHoaDon && (
            <div className="line">
              <div className="line__label">
                <span>{t("thuNgan.quanLyTamUng.hinhThucTT")}</span>
              </div>

              <div className="line__content">
                <div className="line__content__first-item">
                  <Select
                    data={listPhuongThucTTQRHoaDon || []}
                    value={state.phuongThucTtId}
                    onChange={onChange("phuongThucTtId")}
                    className="input-option"
                    placeholder={t("thuNgan.nhapHinhThucTT")}
                  />
                </div>

                {renderTenNganHang()}
              </div>
            </div>
          )}
          {state.tab === "thuTamUng" &&
            !thongTinCoBan?.maThe &&
            dataMAP_THE_KHI_TAM_UNG_L1?.eval() &&
            state.showMaThe && (
              <div className="line">
                <div className="line__label">
                  <span>{t("common.maTheRFID")}</span>
                </div>
                <div className="line__content">
                  <Input
                    value={state.maThe}
                    onChange={onChange("maThe")}
                    className="input-option"
                    placeholder={t("tiepDon.vuiLongNhapMaSoThe")}
                  />
                </div>
              </div>
            )}
        </Row>
      </Main>
    </ModalTemplate>
  );
};

export default memo(forwardRef(ModalTamUng));
