import React, { memo, useRef, useMemo } from "react";
import { useDispatch } from "react-redux";
import { Avatar, Row, Col } from "antd";
import { UserOutlined } from "@ant-design/icons";
import { PatientInfoWrapper, Main } from "./styled";
import { Tooltip, Image } from "components";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { useEnum, useStore } from "hooks";
import { DOI_TUONG, ENUM } from "constants/index";
import ModalHoSoBenhAn from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalHoSoBenhAn";
import { SVG } from "assets";
import ModalChinhSuaThongTin from "pages/tiepDon/TiepDon/ModalChinhSuaThongTin";
const ThongTinBenhNhan = () => {
  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan");
  const thongTinBenhNhan = useStore("nbDotDieuTri.thongTinBenhNhan");
  const [listTrangThaiNb] = useEnum(ENUM.TRANG_THAI_NB);
  const [listGioiTinh] = useEnum(ENUM.GIOI_TINH);
  const refModalChinhSuaThongTin = useRef(null);
  const refModalHoSoBenhAn = useRef(null);
  const {
    nbDotDieuTri: { getById, getThongTinCoBan },
  } = useDispatch();

  const { t } = useTranslation();

  const gioiTinh =
    (listGioiTinh || []).find((item) => item.id === thongTinCoBan?.gioiTinh) ||
    {};
  const onShowThongTinNb = (isEdit) => {
    refModalChinhSuaThongTin.current &&
      refModalChinhSuaThongTin.current.show(
        { id: thongTinCoBan.id, isEdit },
        () => {
          getById(thongTinCoBan.id);
          getThongTinCoBan(thongTinCoBan.id);
        }
      );
  };
  const onShowHoSoBenhAn = () => {
    refModalHoSoBenhAn.current &&
      refModalHoSoBenhAn.current.show({
        nbThongTinId: thongTinCoBan?.nbThongTinId,
        nbDotDieuTriId: thongTinCoBan?.id,
      });
  };

  const renderDoiTuong1 = () => {
    const tenDoiTuong = thongTinCoBan.traiTuyen
      ? t("thuNgan.traiTuyen")
      : thongTinCoBan.dungTuyen
      ? t("thuNgan.dungTuyen")
      : "";
    return tenDoiTuong ? ` - ${tenDoiTuong}` : "";
  };

  const renderMienCungChiTra = () => {
    if (thongTinBenhNhan?.nbTheBaoHiem?.mienCungChiTra)
      return ` - ${t("common.mienCCT")}`;
    return "";
  };

  const renderThongTinBaoHiem = () => {
    if (thongTinCoBan.doiTuong !== DOI_TUONG.BAO_HIEM) return "";
    return thongTinCoBan.mucHuongTheBhyt
      ? ` - (${thongTinCoBan.mucHuongTheBhyt}%)`
      : "";
  };

  return (
    <Main>
      <PatientInfoWrapper>
        <div className="img-avatar">
          {thongTinCoBan?.anhDaiDien ? (
            <Image
              preview={false}
              src={thongTinCoBan?.anhDaiDien}
              width={100}
              height={100}
            />
          ) : (
            <Avatar icon={<UserOutlined />} size={100} shape={"square"} />
          )}
        </div>

        <div className="patient-content">
          <div className="head">
            <Col span={10}>
              <div style={{ display: "flex" }}>
                <div className="benhAn">
                  {t("common.maNb")}: {thongTinCoBan?.maNb} - {t("common.maHs")}
                  : {thongTinCoBan?.maHoSo} - {t("common.maBa")}:{" "}
                  {thongTinCoBan?.maBenhAn}
                  {thongTinCoBan?.maThe && (
                    <>
                      {" "}
                      - {t("common.maTheRFID")}: {thongTinCoBan.maThe}
                    </>
                  )}
                </div>
              </div>
            </Col>
            <Col span={14} className="bunch-icon">
              <div>
                <div style={{ marginRight: "20px" }}>
                  {t("common.khoa")}: {thongTinCoBan?.tenKhoaNb}
                </div>
                {t("common.trangThai")}:{" "}
                {
                  listTrangThaiNb.find(
                    (x) => x.id === thongTinCoBan?.trangThaiNb
                  )?.ten
                }
                {!!thongTinCoBan?.tenPhanLoaiNb && (
                  <span
                    style={{
                      background: thongTinCoBan?.mauNen,
                      color: thongTinCoBan?.mauChu,
                      padding: "0 3px",
                      margin: "0 3px",
                      borderRadius: "2px",
                    }}
                  >
                    {thongTinCoBan?.tenPhanLoaiNb}
                  </span>
                )}
              </div>
              <div>
                <Tooltip title={t("common.xemChiTietThongTin")}>
                  <SVG.IcEye onClick={() => onShowThongTinNb(false)} />
                </Tooltip>
                <Tooltip title={t("common.suaChiTietThongTin")}>
                  <SVG.IcEdit onClick={() => onShowThongTinNb(true)} />
                </Tooltip>
                <Tooltip title={t("quanLyNoiTru.xemHoSoBenhAn")}>
                  <SVG.IcHsba onClick={() => onShowHoSoBenhAn()} />
                </Tooltip>
              </div>
            </Col>
          </div>
          <div className="patient-information">
            <Col xl={{ span: 10 }} lg={{ span: 24 }}>
              <Row>
                <div className="name">
                  <b>{thongTinCoBan?.tenNb}</b>{" "}
                  <span style={{ color: "black", fontWeight: "normal" }}>
                    {gioiTinh.ten &&
                      `(${gioiTinh.ten} - ${
                        thongTinCoBan?.tuoi2 ? thongTinCoBan?.tuoi2 : ""
                      } - ${
                        thongTinCoBan?.ngaySinh
                          ? moment(thongTinCoBan?.ngaySinh).format("DD/MM/YYYY")
                          : ""
                      }${renderDoiTuong1()}${renderMienCungChiTra()}${renderThongTinBaoHiem()})`}
                  </span>
                  <span style={{ color: "black", fontWeight: "normal" }}>
                    {" "}
                    - {t("common.sdt")}: {thongTinCoBan?.soDienThoai}
                  </span>
                </div>
              </Row>
              <Row>
                <span>{t("common.diaChi")}:</span>
                <b className="info">&nbsp;{thongTinCoBan?.diaChi}</b>
              </Row>
            </Col>
            <Col xl={{ span: 7 }} lg={{ span: 24 }}>
              <Row>
                <span>{t("common.soBHYT")}:</span>
                <b className="info">&nbsp;{thongTinCoBan?.maTheBhyt}</b>
              </Row>
              <Row>
                <span>{t("common.giaTriThe")}:</span>
                {thongTinCoBan?.maTheBhyt && (
                  <b className="info">
                    &nbsp;{t("common.tu")}{" "}
                    {moment(thongTinCoBan?.tuNgayTheBhyt)?.format("DD/MM/YYYY")}{" "}
                    {t("common.den")}{" "}
                    {moment(thongTinCoBan?.denNgayTheBhyt)?.format(
                      "DD/MM/YYYY"
                    )}
                  </b>
                )}
              </Row>
            </Col>
            <Col xl={{ span: 7 }} lg={{ span: 24 }}>
              <Row>
                <span>{t("thuNgan.ngayDangKy")}:</span>
                <b className="info">
                  &nbsp;
                  {thongTinCoBan?.thoiGianVaoVien &&
                    moment(thongTinCoBan.thoiGianVaoVien).format(
                      "DD/MM/YYYY HH:mm:ss"
                    )}
                </b>
              </Row>
              <Row>
                <span>{t("thuNgan.ngayNhapVien")}:</span>
                <b className="info">
                  &nbsp;
                  {thongTinBenhNhan?.thoiGianLapBenhAn &&
                    moment(thongTinBenhNhan.thoiGianLapBenhAn).format(
                      "DD/MM/YYYY HH:mm:ss"
                    )}
                </b>
              </Row>
            </Col>
          </div>
        </div>
      </PatientInfoWrapper>
      <ModalChinhSuaThongTin ref={refModalChinhSuaThongTin} />
      <ModalHoSoBenhAn ref={refModalHoSoBenhAn} />
    </Main>
  );
};

export default memo(ThongTinBenhNhan);
