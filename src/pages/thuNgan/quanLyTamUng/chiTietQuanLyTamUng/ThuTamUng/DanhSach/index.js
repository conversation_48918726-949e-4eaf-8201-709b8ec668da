import React, { useEffect, useRef, useCallback, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { useLocation, useParams } from "react-router-dom";
import printProvider from "data-access/print-provider";
import {
  useEnum,
  useQueryString,
  useListAll,
  useStore,
  useConfirm,
  useLoading,
  useThietLap,
} from "hooks";
import ModalTamUng from "pages/thuNgan/quanLyTamUng/chiTietQuanLyTamUng/Modal/ModalTamUng";
import ModalXemPhieuThu from "pages/thuNgan/quanLyTamUng/chiTietQuanLyTamUng/Modal/ModalXemPhieuThu";
import {
  CACHE_KEY,
  ENUM,
  LOAI_MH_PHU,
  LOAI_PHUONG_THUC_TT,
  LOAI_QUAY,
  THIET_LAP_CHUNG,
  TRANG_THAI_THANH_TOAN_QR,
  VI_TRI_PHIEU_IN,
} from "constants/index";
import { TableWrapper, Pagination, ChonQuay } from "components";
import { Main } from "./styled";
import { columns } from "./general";
import ModalHoanUng from "../../Modal/ModalHoanTamUng";
import ModalSuaPhieuThuTamUng from "../../Modal/ModalSuaPhieuThuTamUng";
import ModalHuyPhieuThuTamUng from "../../Modal/ModalHuyPhieuThuTamUng";
import ModalTaoQrCode from "../../Modal/ModalTaoQrCode";
import ModalTaoQrCodeLoi from "../../Modal/ModalTaoQrCodeLoi";
import ModalThongBaoThanhToanQrCode from "pages/thuNgan/chiTietPhieuThu/ModalThongBaoThanhToanQrCode";
import { message } from "antd";
import { isArray, isNumber, isObject, sleep } from "utils/index";
import moment from "moment";
import nbTamUngProvider from "data-access/nb-tam-ung-provider";
import isofhToolProvider from "data-access/isofh-tool-provider";
import { toSafePromise } from "lib-utils";
import nbHoanThanhToanProvider from "data-access/thuNgan/nb-hoan-thanh-toan-provider";

export const functionCheckIsValidDate = (thietLap, thoiGian) => {
  function checkDateOrMonth(input) {
    const regex = /^(DD|MM)\/(\d+)$/;
    // DD/1 hoặc MM/1 chỉ có 2 định dạng này
    // DD/1 là 1 ngày
    // MM/1 là 1 tháng
    const match = input.match(regex);

    if (!match) {
      return 0;
    }

    const type = match[1];
    const number = parseInt(match[2], 10);

    if (type === "DD" && number >= 1 && number <= 31) {
      return { type: "day", number };
    } else if (type === "MM" && number >= 1 && number <= 12) {
      return { type: "month", number };
    } else {
      return 0;
    }
  }

  const dateData = checkDateOrMonth(thietLap);
  let validDate = true;
  if (isObject(dateData, true)) {
    const { type, number } = dateData;
    let now = moment();
    let futureDate;
    if (number === 1) {
      futureDate = moment(thoiGian).endOf(type);
    } else if (number > 1) {
      let dataAdd = number - 1;
      futureDate = moment(thoiGian)
        .startOf(type)
        .add(dataAdd, type === "day" ? "days" : "months")
        .endOf(type);
    }
    validDate = futureDate ? futureDate.isAfter(now) : false;
  }
  return validDate;
};

const DanhSach = (props) => {
  const { showConfirm } = useConfirm();
  const {
    hiddenHeader,
    hideSearch,
    isTiepDon,
    isManHinhTongKetThanhToan,
    nhaTamUngParent = null,
    isNoiTru,
    nbDotDieuTriId,
  } = props;
  const { t } = useTranslation();
  const { showLoading, hideLoading } = useLoading();
  const refModalTamUng = useRef(null);
  const refModalTaoQrCode = useRef(null);
  const refModalTaoQrCodeLoi = useRef(null);
  const refInterval = useRef(null);
  const refModalThongBaoThanhToanQrCode = useRef(null);
  const refCurrentMsg = useRef(null);
  const refChonQuayTiepDon = useRef(null);

  const { dataSortColumn, listDsThuTamUng, totalElements, page, size } =
    useSelector((state) => state.thuTamUng);
  // dùng useSelector để watch chính xác sự thay đổi của listDsThuTamUng
  // dùng useStore sẽ bị 2 lần trong useEffect
  const thongTinBenhNhan = useStore("nbDotDieuTri.thongTinBenhNhan");
  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan");
  const listAllDoiTacThanhToan = useStore("doiTac.listAllDoiTacThanhToan", []);
  const [nhaTamUngId] = useQueryString("nhaTamUngId", null);
  const nhanVienId = useStore("auth.auth.nhanVienId");
  const [listTrangThaiTamUng] = useEnum(ENUM.TRANG_THAI_TAM_UNG);
  const [listdoiTuongKcb] = useEnum(ENUM.DOI_TUONG_KCB);
  const [listTrangThaiThanhToan] = useEnum(ENUM.TRANG_THAI_THANH_TOAN);
  const [dataMA_DOI_TAC_QRCODE_CAP_NHAT_GIAO_DICH] = useThietLap(
    THIET_LAP_CHUNG.MA_DOI_TAC_QRCODE_CAP_NHAT_GIAO_DICH
  );
  const [dataSO_NGAY_HOAN_TAM_UNG] = useThietLap(
    THIET_LAP_CHUNG.SO_NGAY_HOAN_TAM_UNG
  );
  const [dataTU_DONG_IN_PHIEU_TAM_UNG_KHI_DUYET] = useThietLap(
    THIET_LAP_CHUNG.TU_DONG_IN_PHIEU_TAM_UNG_KHI_DUYET
  );

  const [state, _setState] = useState({
    nhaTamUng: null,
    listAllQuayTiepDon: [],
  });
  const setState = (data = {}) => {
    _setState((preState) => {
      return { ...preState, ...data };
    });
  };

  const { state: locationState } = useLocation();
  const [listAllPhuongThucThanhToan] = useListAll("phuongThucTT", {}, true);
  const [listAllToaNha] = useListAll("toaNha", {}, true);

  const {
    thuTamUng: {
      onSizeChange,
      onSortChange,
      onSearch,
      postNbTamUng,
      onChangeInputSearch,
      hoanUng,
      inPhieuTamUng,
    },
    deNghiTamUng: { duyetDeNghiTamUng },
    hoanTamUng: { onChangeInputSearch: onChangeInputSearchToanTamUng },
    quanLyTamUng: { onDelete: onDeleteThuTamUng },
    doiTac: { getListAllDoiTacThanhToan },
    thuNgan: {
      kiemTraTrangThaiThanhToanQr,
      inPhieuHuongDanThanhToanQr,
      huyQrThanhToan,
      taoQrThanhToan,
      kiemTraGiaoDich,
    },
    quayTiepDon: { getListAllQuayTiepDon },
    nbDotDieuTri: { getThongTinCoBan },
  } = useDispatch();

  const { id } = useParams();
  const refSettings = useRef(null);
  const refModalXemPhieuThu = useRef(null);
  const refModalEditPhieuThu = useRef(null);
  const refModalHoanUng = useRef(null);
  const refModalCancelPhieuThu = useRef(null);

  const isDoiTuongKcbNoiTru = [2, 3, 4, 6, 9].includes(
    thongTinBenhNhan?.doiTuongKcb ?? thongTinCoBan?.doiTuongKcb
  );

  const paramsLienin = {
    maViTri: VI_TRI_PHIEU_IN.THU_NGAN.IN_PHIEU_THU_TAM_UNG,
    maPhieuIn: isDoiTuongKcbNoiTru ? "P1100" : "P1099",
  };

  useEffect(() => {
    if (locationState?.thuTamUng) {
      refModalTamUng.current &&
        refModalTamUng.current.show(
          { tab: "thuTamUng", focusInput: true, action: postNbTamUng },
          (idPhieuThu) => {
            onOk(idPhieuThu);
          }
        );
    }
  }, [locationState]);

  useEffect(() => {
    getListAllDoiTacThanhToan({ page: "", size: "", active: true });
    getListAllQuayTiepDon({ page: "", size: "", active: true }).then((res) => {
      setState({ listAllQuayTiepDon: res });
    });
    return () => {
      refInterval.current && clearInterval(refInterval.current);
      clearFunc(true);
    };
  }, []);

  useEffect(() => {
    if (nhaTamUngParent) {
      //update lại quầy / nhà tạm ứng khi chọn lại quầy ở component cha
      setState({ nhaTamUng: nhaTamUngParent });
    }
  }, [nhaTamUngParent]);

  const kiemTraTrangThaiQrThanhToan = async (item) => {
    try {
      let params = {
        nbDotDieuTriId,
        loai: 10,
        tuBanGhiId: item?.id,
      };
      const res = await kiemTraTrangThaiThanhToanQr(params);
      const { data } = res || {};
      const message = data?.phanHoi?.message;
      if (data?.trangThai === TRANG_THAI_THANH_TOAN_QR.THANH_TOAN) {
        // Show popup thành công
        refInterval.current && clearInterval(refInterval.current);
        isofhToolProvider.putDataCenter({
          ma: "TT_TAM_UNG",
          value: { ...item, trangThaiThanhToan: data.trangThai },
        });
        // ẩn popup qr xem qr
        refModalTaoQrCode.current && refModalTaoQrCode.current.hide();

        // show popup thanh toán qr thành công
        refModalThongBaoThanhToanQrCode.current &&
          refModalThongBaoThanhToanQrCode.current.show(
            {
              ...data,
              type: "success",
              title: t("thuNgan.thanhToanThanhCong"),
            },
            () => {
              onOk(item.id);
            }
          );
      } else if (data?.trangThai !== TRANG_THAI_THANH_TOAN_QR.TAO_QR) {
        refInterval.current && clearInterval(refInterval.current);
      } else {
        const showWarningPopup = () => {
          refModalThongBaoThanhToanQrCode.current &&
            refModalThongBaoThanhToanQrCode.current.show({
              ...data,
              type: "warning",
              title: t("common.canhBao"),
            });
        };
        if (message === null) {
          refCurrentMsg.current = message;
        } else if (!!message) {
          if (
            refCurrentMsg.current === null &&
            refCurrentMsg.current !== message
          ) {
            // Show popup lỗi
            showWarningPopup();
          } else if (refCurrentMsg.current !== message) {
            // Show popup lỗi
            showWarningPopup();
          }
          refCurrentMsg.current = message;
        }
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleCheckQrTamUng = useCallback(async () => {
    if (
      isArray(listDsThuTamUng, true) &&
      isArray(listAllPhuongThucThanhToan, true)
    ) {
      try {
        let res = await nbTamUngProvider.search({
          nbDotDieuTriId,
          dsTrangThai: [40, 50, 55, 15, 56],
          page: 0,
          size: 500,
          sort: "thoiGianDeNghi,desc",
        });
        res = res?.data;
        let qrData = null;
        if (isArray(res, true)) {
          for (const item of res) {
            const pttt = (listAllPhuongThucThanhToan || []).find(
              (i) => i.id === item.phuongThucTtId
            );
            if (
              pttt?.loaiPhuongThucTt === LOAI_PHUONG_THUC_TT.QR_CODE &&
              item.trangThaiThanhToan === TRANG_THAI_THANH_TOAN_QR.TAO_QR
            ) {
              qrData = item;
              break;
            }
          }
        }
        if (qrData) {
          refInterval.current && clearInterval(refInterval.current);
          await kiemTraTrangThaiQrThanhToan(qrData);
          refInterval.current = setInterval(() => {
            kiemTraTrangThaiQrThanhToan(qrData);
          }, 5000);
        }
      } catch (error) {
        console.error(error);
      }

      return () => {
        refInterval.current && clearInterval(refInterval.current);
      };
    }
  }, [listAllPhuongThucThanhToan, listDsThuTamUng]);

  useEffect(() => {
    handleCheckQrTamUng();
    return () => {
      refInterval.current && clearInterval(refInterval.current);
    };
  }, [handleCheckQrTamUng]);

  const onChangePage = (page) => {
    onSearch({ page: page - 1 });
  };

  const handleSizeChange = (size) => {
    onSizeChange({ size });
  };

  const onClickSort = (key, value) => {
    onSortChange({ [key]: value });
  };

  const onRow = (record) => {
    return {
      onClick: () => {
        // history.push("/thu-ngan/quan-ly-tam-ung/" + record.id);
      },
    };
  };

  const onSettings = () => {
    refSettings && refSettings.current.settings();
  };
  const onOk = (idPhieuThu) => {
    onSearch({ dataSeatch: { nbDotDieuTriId } });

    onInPhieuThuTamUng(idPhieuThu);
  };

  const onInPhieuThuTamUng = async (idPhieuThu) => {
    try {
      showLoading();

      //xử lý case nếu idPhieuThu là dạng mảng
      if (isArray(idPhieuThu, true)) {
        const resArr = await Promise.all(
          idPhieuThu.map(async (item) => {
            return await inPhieuTamUng(item, paramsLienin);
          })
        );

        printProvider.printPdf((resArr || []).map((item) => item?.data));
      } else {
        const baoCao = await inPhieuTamUng(idPhieuThu, paramsLienin);
        printProvider.printPdf(baoCao?.data);
      }
    } catch (error) {
    } finally {
      hideLoading();
    }
  };

  const onInPhieuHuongDanThanhToanQr = async (thanhToanId) => {
    try {
      showLoading();
      const baoCao = await inPhieuHuongDanThanhToanQr({ thanhToanId });
      printProvider.printPdf(baoCao?.data);
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onDelete = (item) => {
    refModalCancelPhieuThu.current &&
      refModalCancelPhieuThu.current.show({ ...item, _idPhieuThu: id });
  };

  const onReturn = (item) => {
    const callback = (s) => {
      if (s?.code === 1036) {
        let msg = s?.message || "";
        msg = msg.replace(
          /\((.*?)\)/,
          "<b style='color: red; font-size: 16px;text-transform: uppercase;'>($1)</b>"
        );
        showConfirm({
          title: t("common.thongBao"),
          content: msg,
          cancelText: t("common.huy"),
          classNameOkText: "button-warning",
          showBtnOk: false,
          typeModal: "warning",
        });
      } else {
        onOkHoanUng(s);
        onChangeInputSearchToanTamUng({
          nbDotDieuTriId,
          trangThai: 60,
        });
      }
    };
    refModalHoanUng.current &&
      refModalHoanUng.current.show(
        { ...item, nhaTamUng: state.nhaTamUng },
        { hoanUng, callback }
      );
  };

  const onViewDetail = (data) => {
    refModalXemPhieuThu.current &&
      refModalXemPhieuThu.current.show({
        tab: "thuTamUng",
        data,
      });
  };
  const onOkHoanUng = (s) => {
    onChangeInputSearch({
      nbDotDieuTriId,
      dsTrangThai: [40, 50, 15],
    });
    if (isNumber(s.data.code) && Number(s.data.code) === 200) return;
    onInPhieuThuTamUng(s?.data?.id);
  };

  const onSearchInput = (key) => (e) => {
    onChangeInputSearch({ [key]: e });
  };

  const clearFunc = (isLeave = false) => {
    if (isLeave) {
      isofhToolProvider.putDataCenter({ ma: "LOAI_MH_PHU", value: null });
    }
    isofhToolProvider.putDataCenter({ ma: "QR_VALUE", value: null });
    isofhToolProvider.putDataCenter({ ma: "TT_NB", value: {} });
    isofhToolProvider.putDataCenter({ ma: "TT_TAM_UNG", value: {} });
  };

  const showModaConfirmRemove = (data) => {
    showConfirm(
      {
        title: "",
        content: `${t("thuNgan.quanLyTamUng.xacNhanXoaPhieuThu")}`,
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        typeModal: "warning",
        showBtnOk: true,
      },
      () => {
        showLoading();
        onDeleteThuTamUng(data)
          .then(() => {
            onChangeInputSearch({
              nbDotDieuTriId,
              dsTrangThai: [40, 50, 15],
            });
            getThongTinCoBan(nbDotDieuTriId);
          })
          .finally(() => {
            hideLoading();
          });
      },
      () => {}
    );
  };

  const onTaoQrThanhToan = (item) => {
    showLoading({ title: t("thuNgan.dangGenQrCode"), width: 300 });
    taoQrThanhToan({
      nbDotDieuTriId,
      loai: 10,
      tuBanGhiId: item.id,
    })
      .then((res) => {
        const { qr, phanHoi } = res?.data || {};
        if (phanHoi && phanHoi.code !== "00") {
          refModalTaoQrCodeLoi.current &&
            refModalTaoQrCodeLoi.current.show(
              { message: phanHoi?.message, isThuTamUng: true },
              () => {
                onTaoQrThanhToan(item);
              },
              () => {
                showModaConfirmRemove(item.id);
              }
            );
        } else if (item.trangThai !== LOAI_PHUONG_THUC_TT.CHUA_TAM_UNG) {
          onInPhieuThuTamUng(item.id);
        } else {
          //đẩy dữ liệu sang mh phụ
          isofhToolProvider.putDataCenter({
            ma: "LOAI_MH_PHU",
            value: LOAI_MH_PHU.TAM_UNG,
          });
          isofhToolProvider.putDataCenter({ ma: "QR_VALUE", value: res?.data });
          isofhToolProvider.putDataCenter({
            ma: "TT_NB",
            value: thongTinBenhNhan,
          });
          isofhToolProvider.putDataCenter({ ma: "TT_TAM_UNG", value: item });

          //kiểm tra trạng thái đề nghị tạm ứng
          if (item.trangThaiThanhToan === TRANG_THAI_THANH_TOAN_QR.TAO_QR) {
            refInterval.current && clearInterval(refInterval.current);
            refInterval.current = setInterval(() => {
              kiemTraTrangThaiQrThanhToan(item);
            }, 5000);
          }
          refModalTaoQrCode.current &&
            refModalTaoQrCode.current.show({ qrData: qr }, () => {
              clearFunc(false);
              if (refInterval.current) {
                clearInterval(refInterval.current);
              }
            });
        }
      })
      .catch((err) => {
        refModalTaoQrCodeLoi.current &&
          refModalTaoQrCodeLoi.current.show(
            { message: err?.message, isThuTamUng: true },
            () => {
              onTaoQrThanhToan(item);
            },
            () => {
              showModaConfirmRemove(item.id);
            }
          );
      })
      .finally(() => {
        onChangeInputSearch({
          nbDotDieuTriId,
          dsTrangThai: [40, 50, 15],
        });
        hideLoading();
      });
  };

  const onEditPhieuThuTamUng = (item) => {
    refModalEditPhieuThu.current &&
      refModalEditPhieuThu.current.show(
        { tab: "thuTamUng", data: item, nhaTamUng: state.nhaTamUng },
        (_, isQrCode, trangThai, data) => {
          if (data?.code === 1025) {
            if (
              [
                TRANG_THAI_THANH_TOAN_QR.MOI,
                TRANG_THAI_THANH_TOAN_QR.TAO_QR,
                TRANG_THAI_THANH_TOAN_QR.TAO_QR_LOI,
              ].includes(item.trangThaiThanhToan)
            ) {
              refModalTaoQrCodeLoi.current &&
                refModalTaoQrCodeLoi.current.show(
                  { message: data?.message, isHuyQrCode: true },
                  () => {
                    onHuyQrCode(item?.thanhToanId);
                  }
                );
            } else {
              message.error(data?.message);
              return;
            }
          } else if (isQrCode) {
            setTimeout(() => {
              onTaoQrThanhToan(item);
            }, 301);
          }
          onChangeInputSearch({
            nbDotDieuTriId,
            dsTrangThai: [40, 50, 15],
          });
        }
      );
  };

  const onDuyetTamUng = (item) => {
    const { nhaTamUng: _nhaTamUng, ...rest } = item || {};
    if (!state.nhaTamUng && !isNumber(nhaTamUngId) && !_nhaTamUng) {
      refChonQuayTiepDon.current &&
        refChonQuayTiepDon.current.onSelectQuay((data) => {
          setState({ nhaTamUng: data });
        });
    } else {
      refModalTamUng.current &&
        refModalTamUng.current.show(
          {
            ...rest,
            tab: "thuTamUng",
            action: duyetDeNghiTamUng,
            parentNhaTamUng: state.nhaTamUng,
            isDuyet: true,
          },
          (_, isQrCode, ___, s) => {
            if (s?.code === 1036) {
              let msg = s?.message || "";
              msg = msg.replace(
                /\((.*?)\)/,
                "<b style='color: red; font-size: 16px;text-transform: uppercase;'>($1)</b>"
              );
              showConfirm({
                title: t("common.thongBao"),
                content: msg,
                cancelText: t("common.huy"),
                classNameOkText: "button-warning",
                showBtnOk: false,
                typeModal: "warning",
              });
            } else if (s?.code === 1025) {
              if (
                [
                  TRANG_THAI_THANH_TOAN_QR.MOI,
                  TRANG_THAI_THANH_TOAN_QR.TAO_QR,
                  TRANG_THAI_THANH_TOAN_QR.TAO_QR_LOI,
                ].includes(item.trangThaiThanhToan)
              ) {
                refModalTaoQrCodeLoi.current &&
                  refModalTaoQrCodeLoi.current.show(
                    { message: s?.message, isHuyQrCode: true },
                    () => {
                      onHuyQrCode(item?.thanhToanId);
                    }
                  );
              } else {
                message.error(s?.message);
                return;
              }
            } else if (isQrCode) {
              setTimeout(() => {
                onTaoQrThanhToan(item);
              }, 301);
            } else {
              if (dataTU_DONG_IN_PHIEU_TAM_UNG_KHI_DUYET?.eval()) {
                onInPhieuThuTamUng(item.id);
              }
              onChangeInputSearch({
                nbDotDieuTriId,
                dsTrangThai: [40, 50, 15],
              });
            }
          }
        );
    }
  };

  const onViewQrCode = async (item) => {
    const qrData = item?.qrThanhToan;

    //đẩy dữ liệu sang mh phụ
    isofhToolProvider.putDataCenter({
      ma: "LOAI_MH_PHU",
      value: LOAI_MH_PHU.TAM_UNG,
    });
    isofhToolProvider.putDataCenter({ ma: "QR_VALUE", value: item });
    isofhToolProvider.putDataCenter({ ma: "TT_NB", value: thongTinBenhNhan });
    isofhToolProvider.putDataCenter({ ma: "TT_TAM_UNG", value: item });

    //kiểm tra trạng thái đề nghị tạm ứng
    if (item.trangThaiThanhToan === TRANG_THAI_THANH_TOAN_QR.TAO_QR) {
      refInterval.current && clearInterval(refInterval.current);
      await kiemTraTrangThaiQrThanhToan(item);
      refInterval.current = setInterval(() => {
        kiemTraTrangThaiQrThanhToan(item);
      }, 5000);
    }

    refModalTaoQrCode.current &&
      refModalTaoQrCode.current.show({ qrData }, () => {
        clearFunc(false);
        if (refInterval.current) {
          clearInterval(refInterval.current);
        }
      });
  };

  const onHuyQrCode = (item) => {
    showConfirm(
      {
        title: "",
        content: `${t("thuNgan.quanLyTamUng.xacNhanHuyQrCode")}`,
        cancelText: t("common.quayLai"),
        okText: t("common.xacNhan"),
        typeModal: "warning",
        showBtnOk: true,
      },
      () => {
        showLoading();
        huyQrThanhToan({ thanhToanId: item })
          .then(() => {
            isofhToolProvider.putDataCenter({ ma: "QR_VALUE", value: null });
            onChangeInputSearch({
              nbDotDieuTriId,
              dsTrangThai: [40, 50, 15],
            });
          })
          .finally(() => {
            hideLoading();
          });
      },
      () => {}
    );
  };

  const onCapNhatTrangThaiGiaoDichQr = async (thanhToanId) => {
    try {
      showLoading();

      await kiemTraGiaoDich(thanhToanId);
      await sleep(300);

      onChangeInputSearch({
        nbDotDieuTriId,
        dsTrangThai: [40, 50, 15],
      });
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onKiemTraGiaoDichHoanNganHang = async (record) => {
    showLoading();
    const [err] = await toSafePromise(
      nbHoanThanhToanProvider.kiemTraGiaoDich(record.nbHoanThanhToanId)
    );
    await sleep(300);
    hideLoading();
    if (err !== null) {
      message.error(err?.message || t("thuNgan.kiemTraGiaoDichThatBai"));
    } else {
      onChangeInputSearch({
        nbDotDieuTriId,
        dsTrangThai: [40, 50, 15],
      });
      message.success(t("thuNgan.kiemTraGiaoDichThanhCong"));
    }
  };

  return (
    <Main>
      <TableWrapper
        columns={columns({
          onClickSort,
          onSettings,
          dataSortColumn,
          onDelete,
          onReturn,
          onViewDetail,
          onInPhieuThuTamUng,
          onInPhieuHuongDanThanhToanQr,
          listTrangThaiTamUng,
          hiddenHeader,
          listdoiTuongKcb,
          onSearchInput,
          onEditPhieuThuTamUng,
          onDuyetTamUng,
          onHuyQrCode,
          onViewQrCode,
          showModaConfirmRemove,
          hideSearch,
          isTiepDon,
          listTrangThaiThanhToan,
          listAllDoiTac: listAllDoiTacThanhToan,
          listAllPhuongThucThanhToan,
          isManHinhTongKetThanhToan,
          maDoiTacQrCodeCapNhatGiaoDich:
            dataMA_DOI_TAC_QRCODE_CAP_NHAT_GIAO_DICH?.eval(),
          onCapNhatTrangThaiGiaoDichQr,
          listAllQuayTiepDon: state.listAllQuayTiepDon,
          listAllToaNha,
          functionCheckIsValidDate,
          nhanVienId,
          dataSO_NGAY_HOAN_TAM_UNG,
          isNoiTru,
          onKiemTraGiaoDichHoanNganHang,
        })}
        dataSource={listDsThuTamUng}
        onRow={onRow}
        rowKey={(record) => `${record.id}`}
        scroll={{ x: 1700 }}
        tableName="tableThuTamUng"
        ref={refSettings}
      />
      {!!totalElements && (
        <Pagination
          onChange={onChangePage}
          current={page + 1}
          pageSize={size}
          listData={listDsThuTamUng}
          total={totalElements}
          onShowSizeChange={handleSizeChange}
        />
      )}
      <ModalTamUng
        ref={refModalTamUng}
        putDataCenter={isofhToolProvider.putDataCenter}
      />
      <ModalXemPhieuThu ref={refModalXemPhieuThu} />
      <ModalHoanUng ref={refModalHoanUng} />
      <ModalSuaPhieuThuTamUng ref={refModalEditPhieuThu} />
      <ModalHuyPhieuThuTamUng ref={refModalCancelPhieuThu} />
      <ModalTaoQrCode nbDotDieuTriId={nbDotDieuTriId} ref={refModalTaoQrCode} />
      <ModalTaoQrCodeLoi
        nbDotDieuTriId={nbDotDieuTriId}
        ref={refModalTaoQrCodeLoi}
      />
      <ModalThongBaoThanhToanQrCode
        nbDotDieuTriId={nbDotDieuTriId}
        ref={refModalThongBaoThanhToanQrCode}
      />
      <ChonQuay
        ref={refChonQuayTiepDon}
        dsLoaiQuay={LOAI_QUAY.THU_NGAN}
        cacheKey={CACHE_KEY.DATA_NHA_TAM_UNG}
        onChange={(value) => {
          setState({ nhaTamUng: value });
        }}
        showContent={false}
      />
    </Main>
  );
};

export default DanhSach;
