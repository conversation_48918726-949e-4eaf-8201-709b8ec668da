import React from "react";
import moment from "moment";
import { checkRole, checkRoleOr } from "lib-utils/role-utils";
import {
  ROLES,
  LOAI_PHUONG_THUC_TT,
  TRANG_THAI_THANH_TOAN_QR,
  VI_TRI_PHIEU_IN,
  MAN_HINH_PHIEU_IN,
} from "constants/index";
import { t } from "i18next";
import { Select, HeaderSearch, Button, Tooltip } from "components";
import { SVG } from "assets";
import DropDownPrintRecord from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/PhongGiuong/DSPhongGiuong/DropDownPrintRecord";
import { parseListConfig } from "utils/index";

const DA_TAM_UNG = [40, 50];

export const columns = ({
  onClickSort,
  onSettings,
  dataSortColumn,
  onDelete,
  onReturn,
  onViewDetail,
  listTrangThaiTamUng,
  hiddenHeader,
  listdoiTuongKcb,
  onSearchInput,
  onEditPhieuThuTamUng,
  onDuyetTamUng,
  onHuyQrCode,
  onViewQrCode,
  hideSearch,
  showModaConfirmRemove,
  listAllDoiTac,
  listTrangThaiThanhToan,
  listAllPhuongThucThanhToan,
  isHiddenColumn,
  maDoiTacQrCodeCapNhatGiaoDich,
  onCapNhatTrangThaiGiaoDichQr,
  listAllQuayTiepDon,
  listAllToaNha,
  functionCheckIsValidDate,
  nhanVienId,
  dataSO_NGAY_HOAN_TAM_UNG,
  isNoiTru,
  onKiemTraGiaoDichHoanNganHang,
}) => {
  return [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: "50px",
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.soTienTamUng")}
          sort_key="tongTien"
          dataSort={dataSortColumn["tongTien"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "120px",
      dataIndex: "tongTien",
      key: "tongTien",
      columnName: t("thuNgan.soTienTamUng"),
      show: true,
      align: "right",
      render: (field, item, index) => {
        return <div>{field && field?.formatPrice()}</div>;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.kyHieuPhieuThu")}
          sort_key="kyHieu"
          dataSort={dataSortColumn["kyHieu"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "120px",
      dataIndex: "kyHieu",
      key: "kyHieu",
      align: "left",
      columnName: t("thuNgan.kyHieuPhieuThu"),
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.soPhieuTamUng")}
          sort_key="soPhieu"
          dataSort={dataSortColumn["soPhieu"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "140px",
      dataIndex: "soPhieu",
      key: "soPhieu",
      align: "left",
      columnName: t("thuNgan.soPhieuTamUng"),
      show: true,
      render: (field, item, index) => {
        return <div>{field}</div>;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.lan")}
          sort_key="stt"
          dataSort={dataSortColumn["stt"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "50px",
      dataIndex: "stt",
      key: "stt",
      columnName: t("common.lan"),
      show: true,
      align: "right",
    },
    {
      title: (
        <HeaderSearch
          title={t("goiDichVu.ngayPhieuThu")}
          sort_key="thoiGianThucHien"
          dataSort={dataSortColumn["thoiGianThucHien"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "150px",
      dataIndex: "thoiGianThucHien",
      key: "thoiGianThucHien",
      columnName: t("goiDichVu.ngayPhieuThu"),
      show: true,
      render: (field, item, index) => {
        return field && moment(field).format("DD/MM/YYYY HH:mm:ss");
      },
    },
    {
      title: <HeaderSearch title={t("thuNgan.phuongThucThanhToan")} />,
      width: "150px",
      dataIndex: "tenPhuongThucTt",
      key: "tenPhuongThucTt",
      columnName: t("thuNgan.phuongThucThanhToan"),
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.quanLyTamUng.lyDoTamUng")}
          sort_key="tenLyDoTamUng"
          dataSort={dataSortColumn["tenLyDoTamUng"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "120px",
      dataIndex: "tenLyDoTamUng",
      key: "tenLyDoTamUng",
      columnName: t("thuNgan.quanLyTamUng.lyDoTamUng"),
      show: true,
      align: "left",
    },
    {
      title: (
        <HeaderSearch
          title={t("common.khoa")}
          sort_key="tenKhoa"
          dataSort={dataSortColumn["tenKhoa"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "120px",
      dataIndex: "tenKhoa",
      key: "tenKhoa",
      columnName: t("common.khoa"),
      show: true,
    },
    {
      title: <HeaderSearch title={t("danhMuc.toaNha")} />,
      width: "80px",
      dataIndex: "quayId",
      key: "quayId",
      columnName: t("danhMuc.toaNha"),
      show: true,
      render: (item) => {
        const toaNhaId =
          (listAllQuayTiepDon || []).find((x) => x.id === item)?.toaNhaId ||
          null;

        if (toaNhaId) {
          return (
            (listAllToaNha || []).find((x) => x.id === toaNhaId)?.ten || ""
          );
        }

        return "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.thuNgan")}
          sort_key="tenThuNgan"
          dataSort={dataSortColumn["tenThuNgan"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "120px",
      dataIndex: "tenThuNgan",
      key: "tenThuNgan",
      align: "left",
      columnName: t("thuNgan.thuNgan"),
      show: true,
      render: (field, item, index) => {
        return field;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.ghiChu")}
          sort_key="ghiChu"
          dataSort={dataSortColumn["ghiChu"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "100px",
      dataIndex: "ghiChu",
      key: "ghiChu",
      align: "left",
      columnName: t("common.ghiChu"),
      show: true,
      render: (field, item, index) => {
        return field;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.trangThaiTamUng")}
          sort_key="trangThai"
          dataSort={dataSortColumn["trangThai"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "120px",
      dataIndex: "trangThai",
      key: "trangThai",
      align: "left",
      columnName: t("thuNgan.trangThaiTamUng"),
      show: true,
      render: (item, list, index) => {
        if (list?.hoanUngId) {
          return (
            listTrangThaiTamUng.find((x) => x.id === list?.trangThaiHoanUng)
              ?.ten || ""
          );
        }
        return listTrangThaiTamUng.find((x) => x.id === item)?.ten || "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.doiTuongKCB")}
          sort_key="doiTuongKcb"
          dataSort={dataSortColumn["doiTuongKcb"] || ""}
          onClickSort={onClickSort}
          searchSelect={
            !hideSearch ? (
              <Select
                placeholder={t("thuNgan.doiTuongKCB")}
                onChange={onSearchInput("doiTuongKcb")}
                data={listdoiTuongKcb}
                dropdownMatchSelectWidth={400}
              />
            ) : null
          }
        />
      ),
      width: "120px",
      dataIndex: "doiTuongKcb",
      key: "doiTuongKcb",
      align: "left",
      columnName: t("thuNgan.doiTuongKCB"),
      show: true,
      render: (item, index) => {
        return listdoiTuongKcb.find((x) => x.id === item)?.ten || "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.tenTK")}
          sort_key="taiKhoan"
          dataSort={dataSortColumn["taiKhoan"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "120px",
      dataIndex: "taiKhoan",
      key: "taiKhoan",
      columnName: t("thuNgan.tenTK"),
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.tenNganHang")}
          sort_key="nganHangId"
          dataSort={dataSortColumn["nganHangId"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "120px",
      dataIndex: "nganHangId",
      key: "nganHangId",
      columnName: t("thuNgan.tenNganHang"),
      show: true,
      render: (item) => {
        if (listAllDoiTac?.length) {
          return listAllDoiTac.find((x) => x.id === item)?.ten || "";
        }
        return "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.tenNguoiTaoQr")}
          sort_key="tenNguoiTaoQr"
          dataSort={dataSortColumn["tenNguoiTaoQr"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "150px",
      dataIndex: "tenNguoiTaoQr",
      key: "tenNguoiTaoQr",
      columnName: t("thuNgan.tenNguoiTaoQr"),
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.thoiGianTaoQr")}
          sort_key="thoiGianTaoQr"
          dataSort={dataSortColumn["thoiGianTaoQr"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "180px",
      dataIndex: "thoiGianTaoQr",
      key: "thoiGianTaoQr",
      columnName: t("thuNgan.thoiGianTaoQr"),
      show: true,
      render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.trangThaiTaiNganHang")}
          sort_key="trangThaiThanhToan"
          dataSort={dataSortColumn["trangThaiThanhToan"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "120px",
      dataIndex: "trangThaiThanhToan",
      key: "trangThaiThanhToan",
      columnName: t("thuNgan.trangThaiTaiNganHang"),
      show: true,
      render: (item) => {
        return (
          (listTrangThaiThanhToan || []).find((i) => i.id === item)?.ten || ""
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.maGdKetNoi")}
          sort_key="soPhieuThanhToan"
          dataSort={dataSortColumn["soPhieuThanhToan"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "120px",
      dataIndex: "soPhieuThanhToan",
      key: "soPhieuThanhToan",
      columnName: t("thuNgan.maGdKetNoi"),
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.soPhieuTamUngToanVien")}
          sort_key="soPhieuToanVien"
          dataSort={dataSortColumn["soPhieuToanVien"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "120px",
      dataIndex: "soPhieuToanVien",
      key: "soPhieuToanVien",
      columnName: t("thuNgan.soPhieuTamUngToanVien"),
      show: false,
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.tenKhoaNhapVien")}
          sort_key="tenKhoaNhapVien"
          dataSort={dataSortColumn["tenKhoaNhapVien"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "200px",
      dataIndex: "tenKhoaNhapVien",
      key: "tenKhoaNhapVien",
      columnName: t("thuNgan.tenKhoaNhapVien"),
      show: true,
    },
    {
      title: <HeaderSearch title={t("thuNgan.quanLyTamUng.xacNhan")} />,
      width: "90px",
      align: "center",
      fixed: "right",
      ignore: true,
      render: (field, item, index) => {
        return (
          <>
            {item.trangThai === LOAI_PHUONG_THUC_TT.CHUA_TAM_UNG && (
              <Button
                style={{ margin: "auto" }}
                type="primary"
                onClick={() => onDuyetTamUng(item)}
              >
                {t("thuNgan.quanLyTamUng.duyet")}
              </Button>
            )}
          </>
        );
      },
    },
    ...(!isHiddenColumn
      ? [
          {
            title: (
              <HeaderSearch
                title={
                  <>
                    {t("common.thaoTac")}
                    <SVG.IcSetting onClick={onSettings} className="icon" />
                  </>
                }
              />
            ),
            width: isNoiTru ? "100px" : "270px",
            align: "center",
            fixed: "right",
            ignore: true,
            render: (field, item, index) => {
              const pttt = (listAllPhuongThucThanhToan || []).find(
                (i) => i.id === item.phuongThucTtId
              );
              let doiTac =
                listAllDoiTac.find((x) => x.id === item.nganHangId) || {};
              let listMaDoiTac = parseListConfig(maDoiTacQrCodeCapNhatGiaoDich);
              let showCapNhatGiaoDichQr = listMaDoiTac.includes(doiTac?.ma);

              let isHoan = false;

              if (
                checkRole([
                  ROLES["THU_NGAN"]
                    .HOAN_PHIEU_THU_TAM_UNG_VUOT_SO_NGAY_QUY_DINH,
                ])
              ) {
                isHoan = true;
              } else {
                let isValidDate =
                  typeof functionCheckIsValidDate === "function" &&
                  functionCheckIsValidDate(
                    dataSO_NGAY_HOAN_TAM_UNG,
                    item.thoiGianThucHien
                  );
                isHoan =
                  checkRole([ROLES["THU_NGAN"].HOAN_PHIEU_THU_TAM_UNG]) &&
                  isValidDate &&
                  item.thuNganId === nhanVienId;
              }

              return (
                <>
                  {!isNoiTru && (
                    <>
                      <Tooltip title={t("common.xemChiTiet")}>
                        <SVG.IcEye
                          className="ic-action"
                          onClick={() => onViewDetail(item)}
                        />
                      </Tooltip>
                      {checkRoleOr([
                        ROLES["THU_NGAN"].SUA_PHIEU_THU_TAM_UNG,
                      ]) && (
                        <Tooltip title={t("thuNgan.suaPhieuTamUng")}>
                          <SVG.IcEdit
                            className="ic-action"
                            onClick={() => onEditPhieuThuTamUng(item)}
                          />
                        </Tooltip>
                      )}
                      {isHoan && !hiddenHeader && (
                        <Tooltip title={t("thuNgan.quanLyTamUng.hoanTamUng")}>
                          <SVG.IcReload
                            className="ic-action"
                            onClick={() => onReturn(item)}
                          />
                        </Tooltip>
                      )}
                      {checkRoleOr([ROLES["THU_NGAN"].HUY_PHIEU_THU_TAM_UNG]) &&
                        !hiddenHeader &&
                        item?.trangThai !==
                          LOAI_PHUONG_THUC_TT.CHUA_TAM_UNG && (
                          <Tooltip title={t("thuNgan.quanLyTamUng.huyTamUng")}>
                            <SVG.IcDelete
                              className="ic-action"
                              onClick={() => onDelete(item)}
                            />
                          </Tooltip>
                        )}
                      {item?.trangThai === LOAI_PHUONG_THUC_TT.CHUA_TAM_UNG && (
                        <Tooltip title={t("thuNgan.quanLyTamUng.xoaTamUng")}>
                          <SVG.IcCloseCircle
                            color={"var(--color-red-primary)"}
                            className="ic-action"
                            onClick={() => showModaConfirmRemove(item?.id)}
                          />
                        </Tooltip>
                      )}
                      {pttt?.loaiPhuongThucTt === LOAI_PHUONG_THUC_TT.QR_CODE &&
                        [
                          TRANG_THAI_THANH_TOAN_QR.MOI,
                          TRANG_THAI_THANH_TOAN_QR.TAO_QR,
                          TRANG_THAI_THANH_TOAN_QR.THANH_TOAN,
                          TRANG_THAI_THANH_TOAN_QR.TAO_QR_LOI,
                        ].includes(item.trangThaiThanhToan) && (
                          <Tooltip title={t("thuNgan.xemQrCode")}>
                            <SVG.IcQrCode
                              color={"var(--color-blue-primary)"}
                              className="ic-action"
                              onClick={() => onViewQrCode(item)}
                            />
                          </Tooltip>
                        )}
                      {[
                        TRANG_THAI_THANH_TOAN_QR.MOI,
                        TRANG_THAI_THANH_TOAN_QR.TAO_QR,
                        TRANG_THAI_THANH_TOAN_QR.THANH_TOAN,
                        TRANG_THAI_THANH_TOAN_QR.TAO_QR_LOI,
                      ].includes(item.trangThaiThanhToan) && (
                        <Tooltip title={t("thuNgan.huyQrcode")}>
                          <SVG.IcQrCodeOff
                            className="ic-action ic-qr"
                            onClick={() => onHuyQrCode(item.thanhToanId)}
                          />
                        </Tooltip>
                      )}
                      {[
                        TRANG_THAI_THANH_TOAN_QR.MOI,
                        TRANG_THAI_THANH_TOAN_QR.TAO_QR,
                      ].includes(item.trangThaiThanhToan) &&
                        showCapNhatGiaoDichQr && (
                          <Tooltip
                            title={t("thuNgan.capNhatTrangThaiGiaoDichQr")}
                            onClick={() =>
                              onCapNhatTrangThaiGiaoDichQr(item.thanhToanId)
                            }
                          >
                            <SVG.IcUpdateStatusQr
                              color={"var(--color-blue-primary)"}
                              className="ic-action"
                            />
                          </Tooltip>
                        )}
                      {item.trangThaiHoanThanhToan !== null &&
                        item.trangThaiHoanThanhToan !== 40 && (
                          <Tooltip title={t("thuNgan.kiemTraGiaoDichHoan")}>
                            <SVG.IcUpdateStatusQr
                              color={"var(--color-blue-primary)"}
                              className="ic-action"
                              onClick={() =>
                                onKiemTraGiaoDichHoanNganHang(item)
                              }
                            />
                          </Tooltip>
                        )}
                    </>
                  )}
                  <DropDownPrintRecord
                    record={item}
                    maViTri={VI_TRI_PHIEU_IN.THU_NGAN.IN_PHIEU_THU_TAM_UNG}
                    maManHinh={MAN_HINH_PHIEU_IN.THU_NGAN}
                    otherParams={{
                      thanhToanId: item?.thanhToanId,
                    }}
                  />
                </>
              );
            },
          },
        ]
      : []),
  ];
};
