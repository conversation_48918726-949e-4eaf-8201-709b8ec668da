import React, { useEffect, useMemo, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { Main } from "./styled";
import { Input, message } from "antd";
import { orderBy } from "lodash";
import { useHistory } from "react-router-dom";
import {
  CACHE_KEY,
  LOAI_QUAY,
  THIET_LAP_CHUNG,
  TRANG_THAI_NB,
} from "constants/index";
import { useConfirm, useInterval, useStore, useThietLap } from "hooks";
import { SVG } from "assets";
import {
  Button,
  Tooltip,
  IconAction,
  ChonQuay,
  HotKeyTrigger,
  BarcodeScannerInput,
} from "components";
import TabThongKe from "pages/thuNgan/component/TabThongKe";
import { checkRoleXemDsPhieuThu } from "pages/thuNgan/danhSachPhieuThu/configs";
import PhatHanhThe from "pages/tiepDon/components/PhatHanhThe";
import { isArray, safeConvertToArray } from "utils/index";

const HeaderSearch = ({ layerId }) => {
  const { showConfirm } = useConfirm();
  const refTabThongKe = useRef(null);
  const refInput = useRef(null);
  const {
    quanLyTamUng: { onSearch },
    deNghiTamUng: { onSearch: onSearchDeNghiTamUng },
    danhSachPhieuThu: {
      onSearch: onSearchDanhSachPhieuThu,
      goiNbTiepTheo,
      getThongTinNb,
      getNbTiepTheo,
    },
  } = useDispatch();
  const history = useHistory();
  const { t } = useTranslation();
  const [state, _setState] = useState({ nhaTamUng: null });
  const { nbTiepTheo, ttNbTiepTheo } = useStore(
    "danhSachPhieuThu",
    {},
    { fields: "nbTiepTheo,ttNbTiepTheo" }
  );

  const setState = (data) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const [dataPHIEU_TAM_UNG] = useThietLap(THIET_LAP_CHUNG.PHIEU_TAM_UNG);
  const [dataPHIEU_DE_NGHI_TAM_UNG] = useThietLap(
    THIET_LAP_CHUNG.PHIEU_DE_NGHI_TAM_UNG
  );
  const [dataCANH_BAO_CHUA_TAM_UNG_NOI_TRU] = useThietLap(
    THIET_LAP_CHUNG.CANH_BAO_CHUA_TAM_UNG_NOI_TRU
  );

  const onChangeCode = (e) => {
    setState({ valueSearch: e?.target?.value });
  };

  const onPushChiTiet = (record) => {
    if (
      [TRANG_THAI_NB.CHO_LAP_BENH_AN, TRANG_THAI_NB.HUY_BENH_AN].includes(
        record.trangThai
      )
    ) {
      if (dataCANH_BAO_CHUA_TAM_UNG_NOI_TRU.toLowerCase() !== "true") {
        showConfirm({
          title: t("common.thongBao"),
          content: `${t("thuNgan.quanLyTamUng.nbNhapVienChuaLapBenhAn")}`,
          cancelText: t("common.huy"),
          okText: t("common.dongY"),
          classNameOkText: "button-warning",
          typeModal: "warning",
        });
      }
      history.push(`/thu-ngan/quan-ly-tam-ung/${record.id}`);
    } else {
      onSearchDeNghiTamUng({
        dataSearch: { nbDotDieuTriId: record.id, trangThai: 10 },
      }).then((data) => {
        if (!data?.length && dataPHIEU_TAM_UNG?.eval()) {
          history.push({
            pathname: `/thu-ngan/quan-ly-tam-ung/${record.id}`,
            state: { thuTamUng: true },
          });
        } else if (data?.length === 1 && dataPHIEU_DE_NGHI_TAM_UNG?.eval()) {
          history.push({
            pathname: `/thu-ngan/quan-ly-tam-ung/${record.id}`,
            state: {
              duyetDeNghiTamUng: true,
              data: data[0],
              nhaTamUng: state.nhaTamUng,
            },
          });
        } else {
          history.push(`/thu-ngan/quan-ly-tam-ung/${record.id}`);
        }
      });
    }
  };

  const submit = (param) => {
    if (param.maHoSo || param.maThe || param.tenNb || param.maNb) {
      onSearch({
        dataSearch: {
          ...param,
        },
      }).then((s) => {
        if (isArray(s?.data, true)) {
          if (s.data.length === 1) {
            onPushChiTiet(s.data[0]);
          } else {
            let record = orderBy(s.data, "thoiGianVaoVien", "desc");
            onPushChiTiet(record[0]);
          }
        } else {
          message.error(`${t("thuNgan.khongTimThayThongTinTamUng")}`);
        }
      });
    } else {
      message.error(t("thuNgan.khongTimThayThongTinTamUng"));
    }
    setState({ valueSearch: "" });
    onSearchDanhSachPhieuThu({
      dataSearch: {
        ...checkRoleXemDsPhieuThu(),
        tuThoiGianVaoVien: null,
        denThoiGianVaoVien: null,
        ...param,
      },
    }).then(async (s) => {
      if (s?.data?.length) {
        if (state.nhaTamUng?.khongLaySo) {
          try {
            await goiNbTiepTheo({
              quayId: state.nhaTamUng?.id,
              nbDotDieuTriId: s?.data?.[0]?.nbDotDieuTriId,
            });
          } catch (error) {
            console.log("error", error);
          }
        }
      } else {
        // message.error(`${t("thuNgan.khongTimThayThongTinPhieuThu")}`);
      }
    });
  };

  const goToNbTiepTheoPage = (nbDotDieuTriId) => {
    getThongTinNb(nbDotDieuTriId).then((res) => {
      const { nbDotDieuTriId } = res;
      history.push(
        `/thu-ngan/quan-ly-tam-ung/${nbDotDieuTriId}?nhaTamUngId=${state.nhaTamUng?.toaNhaId}`
      );
    });
  };

  const onGoiNbTiepTheo = () => {
    let ttNb = ttNbTiepTheo;
    goiNbTiepTheo({
      quayId: state.nhaTamUng?.id,
    }).then((res) => {
      if (!res) {
        getNbTiepTheo(state.nhaTamUng?.id);
      }
      if (ttNb?.nbDotDieuTriId) {
        goToNbTiepTheoPage(ttNb?.nbDotDieuTriId);
      }
    });
  };

  const onViewThongKe = (show) => {
    refTabThongKe.current && refTabThongKe.current.show({ show });
  };

  useInterval(() => {
    getNbTiepTheo(state.nhaTamUng?.id);
  }, [5000]);

  useEffect(() => {
    if (nbTiepTheo) {
      getThongTinNb(nbTiepTheo?.nbDotDieuTriId);
    }
  }, [nbTiepTheo]);

  useEffect(() => {
    if (refInput && refInput.current) {
      refInput.current.focus();
    }
  }, [refInput]);

  return (
    <Main>
      <div className="left">
        <div className="title">{t("thuNgan.timNguoiBenh")}</div>
        <div className="input-search">
          <HotKeyTrigger
            layerIds={safeConvertToArray(layerId)}
            hotKey="F6"
            triggerEvent={() => {
              refInput.current?.focus();
            }}
          >
            <BarcodeScannerInput
              ref={refInput}
              placeholder={t("thuNgan.timKiemTheoMaNbMaHoSoTenNbQrNbRfIdF6")}
              value={state?.valueSearch}
              onChange={onChangeCode}
              onSubmit={(params) => {
                submit(params);
                setState({ valueSearch: "" });
              }}
              showIcon={false}
            />
          </HotKeyTrigger>
          <SVG.IcQrCode />
        </div>
      </div>
      <div className="right flex justify-end flex1 ">
        <PhatHanhThe isTamUng />
        <div className="item btn btn-nb-tiep-theo">
          <Button
            type="primary"
            onClick={onGoiNbTiepTheo}
            rightIcon={<SVG.IcExtend />}
          >
            {t("thuNgan.nbTiepTheo")}
          </Button>

          {nbTiepTheo && (
            <span
              className="text"
              title={`${nbTiepTheo.stt} - ${ttNbTiepTheo?.tenNb || ""}`}
            >{`${nbTiepTheo.stt} - ${ttNbTiepTheo?.tenNb || ""}`}</span>
          )}
        </div>
        <Tooltip title={t("tiepDon.xemThongKe")} placement="left">
          <IconAction onClick={() => onViewThongKe(true)}>
            <SVG.IcXemThongKe2 />
          </IconAction>
        </Tooltip>
      </div>
      <TabThongKe
        quayId={state.nhaTamUng?.id}
        store="danhSachPhieuThu"
        ref={refTabThongKe}
      />
      <ChonQuay
        dsLoaiQuay={LOAI_QUAY.THU_NGAN}
        cacheKey={CACHE_KEY.DATA_NHA_TAM_UNG}
        onChange={(value) => {
          setState({ nhaTamUng: value });
        }}
        showContent={false}
      />
    </Main>
  );
};

export default HeaderSearch;
