import React, {
  useState,
  useMemo,
  useEffect,
  forwardRef,
  useImperativeHandle,
  useRef,
} from "react";
import { HeaderSearch, Checkbox, TableWrapper, InputTimeout } from "components";
import { Input, InputNumber } from "antd";
import { useTranslation } from "react-i18next";
import { formatDecimal, parseFloatNumber } from "utils";
import { InputNumberFormat } from "components/common";
import { cloneDeep, groupBy } from "lodash";
import { useStore } from "hooks";
import { useDispatch } from "react-redux";

const DanhSachDichVu = (props, ref) => {
  const { t } = useTranslation();
  const { currentItem, onChangeTienTaiTro = () => () => {} } = props;
  const refFormattedData = useRef([]);

  const [state, _setState] = useState({
    data: [],
    selectedRowKeys: [],
    dataSearch: {},
    listData: [],
    checkAll: false,
    tongTien: null,
    phanTram: null,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const thongTinPhieuThu = useStore("thuNgan.thongTinPhieuThu", {});

  const { getDsDichVuTaiTro } = useDispatch().nguonTaiTro;

  useImperativeHandle(ref, () => ({
    getData: () => {
      return (listDVKT || [])
        .filter((item) => !item.isGroup && item.tienTaiTro)
        .map((item) => ({
          id: item.id,
          tongTien: item.tienTaiTro,
        }));
    },
    clearData: () => {
      setState({
        selectedRowKeys: [],
        dataSearch: {},
        listData: [],
        checkAll: false,
        tongTien: null,
        phanTram: null,
        data: state.defaultData,
      });
    },
  }));

  useEffect(() => {
    if (thongTinPhieuThu.nbDotDieuTriId && thongTinPhieuThu.id) {
      getDsDichVuTaiTro({
        nbDotDieuTriId: thongTinPhieuThu.nbDotDieuTriId,
        phieuThuId: thongTinPhieuThu.id,
        hoaDonId: currentItem?.id,
      }).then((res) => {
        const data = res.map((item) => ({
          ...item,
          tongTien: currentItem?.id ? item.tienTaiTro : null, // case edit thì api đang trả về tienTaiTro chứ ko trả về phanTram
          phanTram: null,
        }));
        setState({ data, defaultData: data });
      });
    }
  }, [thongTinPhieuThu, currentItem?.id]);

  useEffect(() => {
    if (!currentItem?.id) {
      const giamGia = { tongTien: null, phanTram: 100 };
      setState(giamGia);

      onUpdateTienTaiTro(giamGia);
    }
  }, [currentItem?.id]);

  const onSearchInput = (key) => (e) => {
    const dataSearch = { ...state.dataSearch, [key]: e };
    setState({ dataSearch });
  };

  const listDVKT = useMemo(() => {
    const _dsDichVuTaiTro = (state.data || [])
      .filter((option) =>
        Object.keys(state.dataSearch).every(
          (key) =>
            option[key]
              ?.toLowerCase()
              .unsignText()
              .indexOf(
                state.dataSearch[key]?.trim().toLowerCase().unsignText()
              ) >= 0
        )
      )
      .map((item) => ({
        ...item,
        tenNhomDichVuCap1: item?.tenNhomDichVuCap1 || t("common.dichVu"),
      }));
    const _group = groupBy(_dsDichVuTaiTro, "tenNhomDichVuCap1");
    let data = [];

    Object.keys(_group).forEach((key) => {
      data.push({
        id: key,
        key: key,
        isGroup: true,
        tenDichVu: key,
      });

      (_group[key] || []).forEach((element, index) => {
        data.push({ ...element, index: index + 1, keyParent: key });
      });
    });

    return data;
  }, [state.data, state.dataSearch]);

  const sharedOnCell = (row, index) => {
    if (row.isGroup) {
      return { colSpan: 0 };
    }
  };

  const tinhTienTaiTro = (data, value) => {
    let tienTaiTroCungChiTra = 0;
    let tienTaiTroNgoaiBaoHiem = 0;
    tienTaiTroCungChiTra =
      data.tienNbCungChiTra >= value ? value : data.tienNbCungChiTra;
    tienTaiTroNgoaiBaoHiem =
      value > tienTaiTroCungChiTra ? value - tienTaiTroCungChiTra : 0;
    return { tienTaiTroCungChiTra, tienTaiTroNgoaiBaoHiem };
  };

  const onChangeInput = (id, index) => (e) => {
    const item = state.data?.find((x) => x?.id === id);
    item.tienTaiTro = e;
    if (e) {
      const { tienTaiTroCungChiTra, tienTaiTroNgoaiBaoHiem } = tinhTienTaiTro(
        item,
        e
      );
      item.tienTaiTroCungChiTra = tienTaiTroCungChiTra;
      item.tienTaiTroNgoaiBaoHiem = tienTaiTroNgoaiBaoHiem;

      const tienKhongBaoHiem = state?.data.reduce(
        (prev, current) => prev + (current.tienTaiTroNgoaiBaoHiem || 0),
        0
      );

      const tienBaoHiem = state?.data.reduce(
        (prev, current) => prev + (current.tienTaiTroCungChiTra || 0),
        0
      );
      onChangeTienTaiTro({ tienKhongBaoHiem, tienBaoHiem });
    }
    const data = cloneDeep(state?.data);
    setState({ data: data });
  };

  const onChangeInputTien = (key, id) => (e) => {
    const item = state.data?.find((x) => x?.id === id);
    item[key] = parseFloat(e || 0);
    item.tienTaiTro =
      parseFloat(item.tienTaiTroCungChiTra || 0) +
      parseFloat(item.tienTaiTroNgoaiBaoHiem || 0);

    const tienKhongBaoHiem = state?.data.reduce(
      (prev, current) => prev + (current.tienTaiTroNgoaiBaoHiem || 0),
      0
    );

    const tienBaoHiem = state?.data.reduce(
      (prev, current) => prev + (current.tienTaiTroCungChiTra || 0),
      0
    );
    onChangeTienTaiTro({ tienKhongBaoHiem, tienBaoHiem });

    const data = cloneDeep(state?.data);
    setState({ data: data });
  };

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} className="text-center" />,
      width: 40,
      dataIndex: "index",
      key: "index",
      align: "right",
    },
    {
      title: (
        <HeaderSearch
          title={t("common.tenDichVu")}
          search={
            <InputTimeout
              placeholder={t("thuNgan.timTenDichVu")}
              onChange={onSearchInput("tenDichVu")}
              value={state.dataSearch?.tenDichVu}
            />
          }
        />
      ),
      width: 250,
      dataIndex: "tenDichVu",
      key: "tenDichVu",
      align: "left",
      onCell: (row, index) => ({
        colSpan: row.isGroup ? 8 : 1,
      }),
      render: (item, list) => (
        <span className={list?.isGroup ? "nhom-dv-cap-1" : ""}>{item}</span>
      ),
    },
    {
      title: (
        <HeaderSearch title={t("common.thanhTien")} className="text-center" />
      ),
      width: 110,
      dataIndex: "thanhTien",
      key: "thanhTien",
      align: "right",
      onCell: sharedOnCell,
      render: (item) => formatDecimal(String(item)),
    },
    {
      title: (
        <HeaderSearch title={t("common.soLuong")} className="text-center" />
      ),
      width: 90,
      dataIndex: "soLuong",
      key: "soLuong",
      align: "right",
      onCell: sharedOnCell,
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.nguonTaiTro.tienTaiTroBHBL")}
          className="text-center"
        />
      ),
      width: 110,
      dataIndex: "tienTaiTro",
      key: "tienTaiTro",
      align: "right",
      onCell: sharedOnCell,
      render: (item, record, index) => {
        if (index === state.currentIndex) {
          return (
            <InputTimeout
              placeholder={t("thuNgan.nguonTaiTro.tienTaiTroBHBL")}
              onChange={onChangeInput(record?.id)}
              value={item}
            />
          );
        } else {
          return state?.selectedRowKeys.includes(record.key)
            ? item?.formatPrice()
            : null;
        }
      },
    },
    {
      title: <HeaderSearch title={t("thuNgan.nguonTaiTro.tienKhongBaoHiem")} />,
      width: 100,
      dataIndex: "tienTaiTroNgoaiBaoHiem",
      key: "tienTaiTroNgoaiBaoHiem",
      align: "right",
      onCell: sharedOnCell,
      render: (item, data, index) => {
        if (index === state.currentIndex) {
          return (
            <InputTimeout
              placeholder={t("thuNgan.nguonTaiTro.tienKhongBaoHiem")}
              onChange={onChangeInputTien("tienTaiTroNgoaiBaoHiem", data?.id)}
              value={item}
            />
          );
        } else {
          return state?.selectedRowKeys.includes(data.key)
            ? item?.formatPrice()
            : null;
        }
      },
    },
    {
      title: <HeaderSearch title={t("thuNgan.nguonTaiTro.tienBaoHiem")} />,
      width: 100,
      dataIndex: "tienTaiTroCungChiTra",
      key: "tienTaiTroCungChiTra",
      align: "right",
      onCell: sharedOnCell,
      render: (item, data, index) => {
        if (index === state.currentIndex) {
          return (
            <InputTimeout
              placeholder={t("thuNgan.nguonTaiTro.tienBaoHiem")}
              onChange={onChangeInputTien("tienTaiTroCungChiTra", data?.id)}
              value={item}
            />
          );
        } else {
          return state?.selectedRowKeys.includes(data.key)
            ? item?.formatPrice()
            : null;
        }
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.maBoChiDinh")}
          search={
            <Input
              placeholder={t("thuNgan.maBoChiDinh")}
              onChange={onSearchInput("maBoChiDinh")}
            />
          }
        />
      ),
      width: 100,
      dataIndex: "maBoChiDinh",
      key: "maBoChiDinh",
      align: "left",
      onCell: sharedOnCell,
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.tenBoChiDinh")}
          search={
            <Input
              placeholder={t("thuNgan.tenBoChiDinh")}
              onChange={onSearchInput("tenBoChiDinh")}
            />
          }
        />
      ),
      width: 180,
      dataIndex: "tenBoChiDinh",
      key: "tenBoChiDinh",
      align: "left",
      onCell: sharedOnCell,
    },
  ];

  const onSelectChange = (record, selected) => {
    let selectedRowKeys = state.selectedRowKeys;
    let listData = [];
    if (record.isGroup) {
      const data = listDVKT.filter((x) => record.key === x.keyParent);
      if (selected) {
        selectedRowKeys = [
          ...state.selectedRowKeys,
          ...data.map((item) => item.key),
          record.key,
        ];
        listData = [...state.listData, ...data];
      } else {
        selectedRowKeys = state.selectedRowKeys.filter(
          (x) => !data.map((item) => item.key).includes(x) && x != record.key
        );
        listData = state.listData.filter(
          (x) => record.key !== x.keyParent && !x.isGroup
        );
      }
    } else {
      if (selected) {
        const data = listDVKT.filter((x) => record.keyParent === x.keyParent);
        const selectedKey = data.filter((x) =>
          [...state.selectedRowKeys, record.key].includes(x.key)
        );
        if (data.length === selectedKey.length) {
          selectedRowKeys = [
            ...state.selectedRowKeys,
            record.key,
            record.keyParent,
          ];
        } else {
          selectedRowKeys = [...state.selectedRowKeys, record.key];
        }
        listData = [...state.listData, record];
      } else {
        selectedRowKeys = state.selectedRowKeys.filter(
          (x) => x != record.key && x !== record.keyParent
        );
        listData = state.listData.filter((x) => x.key !== record.key);
      }
    }

    const { phanTram, tongTien } = state;
    const formattedData = listDVKT
      .filter((x) => !x.isGroup)
      .map((item) => {
        if (selectedRowKeys.includes(item.key)) {
          const tienTaiTro = phanTram
            ? (phanTram * item.thanhTien) / 100
            : Math.min(tongTien, item.thanhTien);

          //nếu chưa nhập tiền tài trợ cùng chi trả và tiền tài trợ ngoài bảo hiểm thì mới thực hiện tính lại
          //case ND đã sửa => giữ nguyên giá trị
          if (!item.tienTaiTroCungChiTra && !item.tienTaiTroNgoaiBaoHiem) {
            const { tienTaiTroCungChiTra, tienTaiTroNgoaiBaoHiem } =
              tinhTienTaiTro(item, tienTaiTro);

            item.tienTaiTroCungChiTra = tienTaiTroCungChiTra;
            item.tienTaiTroNgoaiBaoHiem = tienTaiTroNgoaiBaoHiem;
            item.tienTaiTro = tienTaiTro;
          }

          item.tongTien = tongTien;
          item.phanTram = phanTram;

          return item;
        }
        item.tongTien = null;
        item.phanTram = null;
        item.tienTaiTro = null;
        item.tienTaiTroCungChiTra = null;
        item.tienTaiTroNgoaiBaoHiem = null;
        return item;
      })
      .filter((item1) => item1);

    setState({ selectedRowKeys, listData, data: formattedData });
    refFormattedData.current = formattedData;
    const tienKhongBaoHiem = listData.reduce(
      (prev, current) => prev + (current.tienTaiTroNgoaiBaoHiem || 0),
      0
    );

    const tienBaoHiem = listData.reduce(
      (prev, current) => prev + (current.tienTaiTroCungChiTra || 0),
      0
    );

    onChangeTienTaiTro({ tienKhongBaoHiem, tienBaoHiem });
  };

  const onSelectAll = (e) => {
    const { phanTram, tongTien } = state;
    const formattedData = listDVKT
      .filter((x) => !x.isGroup)
      .map((item) => {
        if (e.target?.checked && !item.isGroup) {
          const tienTaiTro = phanTram
            ? (phanTram * item.thanhTien) / 100
            : Math.min(tongTien, item.thanhTien);

          //nếu chưa nhập tiền tài trợ cùng chi trả và tiền tài trợ ngoài bảo hiểm thì mới thực hiện tính lại
          //case ND đã sửa => giữ nguyên giá trị
          if (!item.tienTaiTroCungChiTra && !item.tienTaiTroNgoaiBaoHiem) {
            const { tienTaiTroCungChiTra, tienTaiTroNgoaiBaoHiem } =
              tinhTienTaiTro(item, tienTaiTro);

            item.tienTaiTroCungChiTra = tienTaiTroCungChiTra;
            item.tienTaiTroNgoaiBaoHiem = tienTaiTroNgoaiBaoHiem;
            item.tienTaiTro = tienTaiTro;
          }

          item.tongTien = tongTien;
          item.phanTram = phanTram;

          return item;
        }
        item.tongTien = null;
        item.phanTram = null;
        item.tienTaiTro = null;
        item.tienTaiTroCungChiTra = null;
        item.tienTaiTroNgoaiBaoHiem = null;
        return item;
      });

    setState({
      selectedRowKeys: e.target?.checked
        ? listDVKT.map((item) => item.key)
        : [],
      listData: e.target?.checked ? listDVKT.filter((x) => !x.isGroup) : [],
      checkAll: e.target?.checked,
      data: formattedData,
    });
    refFormattedData.current = formattedData;
    const tienKhongBaoHiem = listDVKT.reduce(
      (prev, current) => prev + (current.tienTaiTroNgoaiBaoHiem || 0),
      0
    );

    const tienBaoHiem = listDVKT.reduce(
      (prev, current) => prev + (current.tienTaiTroCungChiTra || 0),
      0
    );
    onChangeTienTaiTro({ tienKhongBaoHiem, tienBaoHiem });
  };
  const rowSelection = {
    columnTitle: (
      <HeaderSearch
        title={<Checkbox onChange={onSelectAll} checked={state.checkAll} />}
      />
    ),
    columnWidth: 30,
    onSelect: onSelectChange,
    selectedRowKeys: state.selectedRowKeys,
    onCell: sharedOnCell,
  };

  const renderEmptyTextLeftTable = () => {
    return (
      <div style={{ marginTop: 130 }}>
        <div style={{ color: "#c3c3c3", fontSize: 14 }}>
          {t("common.khongCoDuLieuPhuHop")}
        </div>
      </div>
    );
  };

  const onUpdateTienTaiTro = (giamGia) => {
    let tienKhongBaoHiem = 0;
    let tienBaoHiem = 0;
    const formattedData = listDVKT
      .filter((x) => !x.isGroup)
      .map((item) => {
        if (state.selectedRowKeys.includes(item.key)) {
          const tienTaiTro = giamGia.phanTram
            ? (giamGia.phanTram * item.thanhTien) / 100
            : Math.min(giamGia.tongTien, item.thanhTien);

          //nếu chưa nhập tiền tài trợ cùng chi trả và tiền tài trợ ngoài bảo hiểm thì mới thực hiện tính lại
          //case ND đã sửa => giữ nguyên giá trị
          if (!item.tienTaiTroCungChiTra && !item.tienTaiTroNgoaiBaoHiem) {
            const { tienTaiTroCungChiTra, tienTaiTroNgoaiBaoHiem } =
              tinhTienTaiTro(item, tienTaiTro);

            tienKhongBaoHiem += tienTaiTroNgoaiBaoHiem;
            tienBaoHiem += tienTaiTroCungChiTra;
          } else {
            tienKhongBaoHiem += parseFloat(item.tienTaiTroNgoaiBaoHiem || 0);
            tienBaoHiem += parseFloat(item.tienTaiTroCungChiTra || 0);
          }

          return {
            ...item,
            tongTien: giamGia.tongTien,
            phanTram: giamGia.phanTram,
            tienTaiTro,
            tienTaiTroCungChiTra,
            tienTaiTroNgoaiBaoHiem,
          };
        } else {
          tienKhongBaoHiem += item.tienTaiTroNgoaiBaoHiem || 0;
          tienBaoHiem += item.tienTaiTroCungChiTra || 0;
          return item;
        }
      });

    setState({ data: formattedData });
    refFormattedData.current = formattedData.filter((x) =>
      state.selectedRowKeys.includes(x.key)
    );

    onChangeTienTaiTro({ tienKhongBaoHiem, tienBaoHiem });
  };

  const onRow = (record, index) => {
    return {
      onClick: (event) => {
        if (state.currentIndex !== index) {
          setState({
            currentIndex: index,
          });
        }
      },
    };
  };

  return (
    <>
      <div className="item-row">
        <div className="title text-bold">
          {t("thuNgan.nguonTaiTro.dienPhanTram")}:
        </div>
        <div className="num">
          <InputNumber
            type="number"
            placeholder={t("thuNgan.nhapSoPhanTram")}
            value={state.phanTram}
            onChange={(e) => {
              const giamGia = { tongTien: null, phanTram: e };
              setState(giamGia);

              onUpdateTienTaiTro(giamGia);
            }}
          />
          <span>%</span>
        </div>
        <div className="text-or">{t("common.hoac")}</div>
        <div className="title text-bold">
          {t("thuNgan.nguonTaiTro.dienSoTien")}:
        </div>
        <div className="num">
          <InputNumberFormat
            width="240px"
            placeholder={t("thuNgan.nhapSoTien")}
            value={state.tongTien}
            onChange={(e) => {
              const value = e?.target.value && parseFloatNumber(e.target.value);

              const giamGia = { tongTien: value, phanTram: null };
              setState(giamGia);

              onUpdateTienTaiTro(giamGia);
            }}
          />
        </div>
      </div>

      <div className="danh-sach-dich-vu">
        <div className="title-2 text-bolder">
          {t("common.daChon")} {state.listData.length} {t("common.dichVu")}
        </div>
        <TableWrapper
          columns={columns}
          dataSource={listDVKT}
          rowSelection={rowSelection}
          rowKey={(record) => record.key}
          onRow={onRow}
          style={{
            marginTop: 0,
          }}
          scroll={{
            y: 200,
            x: 1000,
          }}
          locale={{
            emptyText: renderEmptyTextLeftTable(),
          }}
        />
      </div>
    </>
  );
};

export default forwardRef(DanhSachDichVu);
