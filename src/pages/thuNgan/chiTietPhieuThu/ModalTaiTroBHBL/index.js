import React, {
  useState,
  useRef,
  useImperativeHandle,
  forwardRef,
  useEffect,
  useMemo,
} from "react";
import { useDispatch, useSelector } from "react-redux";
import { Main, RightTitleWrapper } from "./styled";
import { useTranslation } from "react-i18next";
import {
  Tooltip,
  Button,
  InputNumberField,
  InputTimeout,
  ModalTemplate,
  Select,
  TableWrapper,
  AuthWrapper,
} from "components";
import {
  HOTKEY,
  ENUM,
  THIET_LAP_CHUNG,
  ROLES,
  TRANG_THAI_PHIEU_THU_THANH_TOAN,
} from "constants/index";
import moment from "moment";
import { useConfirm, useEnum, useLoading, useStore, useThietLap } from "hooks";
import { message, Row } from "antd";
import ModalXoaHoaDon from "../ModalXoaHoaDon";
import { SVG } from "assets";
import { checkRole } from "lib-utils/role-utils";
import ModalThemMoiDonViCaNhan from "../ModalThemMoiDonViCaNhan";
import ModalThemMoiTaiTroBHBLTheoDV from "../ModalThemMoiTaiTroBHBLTheoDV";

const { Column } = TableWrapper;
const ModalTaiTroBHBL = (props, ref) => {
  const { showConfirm } = useConfirm();
  const { showLoading, hideLoading } = useLoading();
  const { t } = useTranslation();
  const refModal = useRef(null);
  const refModalDeleteHoaDon = useRef(null);

  const { listData, listDoiTac } = useSelector((state) => state.nguonTaiTro);
  const thongTinPhieuThu = useStore(
    (state) => state.thuNgan.thongTinPhieuThu,
    {}
  );
  const { listAllPhuongThucThanhToan } = useSelector(
    (state) => state.phuongThucTT
  );
  const { nbDotDieuTriId, id } = thongTinPhieuThu || {};

  const [listNguonTaiTro] = useEnum(ENUM.NGUON_TAI_TRO);
  const [listTrangThaiHoaDon] = useEnum(ENUM.TRANG_THAI_HOA_DON);

  const [dataDINH_DANG_XEM_HOA_DON] = useThietLap(
    THIET_LAP_CHUNG.DINH_DANG_XEM_HOA_DON
  );
  const refThemMoiCaNhanDonVi = useRef(null);
  const refModalThemMoiTaiTroBHBLTheoDV = useRef(null);
  const addNewRowRef = useRef(false);
  const {
    nguonTaiTro: {
      getDsHoaDonTaiTro,
      getDsDoiTac,
      themMoiTaiTro,
      chinhSuaTaiTro,
      phatHanhHoaDon,
    },
    phuongThucTT: { getListAllPhuongThucThanhToan },
    dsHoaDonDienTu: { deleteHoaDon },
    thuNgan: { getThongTinPhieuThu },
    dsHoaDonDienTu: { inHoaDon },
  } = useDispatch();
  const [state, _setState] = useState({
    currentItem: {},
    currentIndex: -1,
    data: [],
  });
  const setState = (newState) => {
    _setState((state) => {
      return { ...state, ...newState };
    });
  };

  const handleClickBack = () => {
    setState({
      show: false,
      currentItem: {},
      currentIndex: -1,
    });
  };
  useImperativeHandle(ref, () => ({
    show: () => {
      setState({ show: true });
    },
    showPopupThemMoi: () => {
      addNewRowRef.current = true;
      setState({ show: true, data: [] });
    },
  }));

  useEffect(() => {
    if (Array.isArray(listData)) {
      setState({ data: listData });
    }
    if (addNewRowRef.current) {
      addNewRowRef.current = false;
      onAddNewRow();
    }
  }, [listData]);

  const listlistNguonTaiTroMemo = useMemo(() => {
    //ẩn NB thanh toán - id = 0
    return (listNguonTaiTro || []).filter((item) => item.id != 0);
  }, [listNguonTaiTro]);

  const onOk = (isOk) => async () => {
    if (isOk) {
      const {
        nguonTaiTro,
        donViTaiTroId,
        phieuThuId,
        tienBaoHiem,
        tienKhongBaoHiem,
        phuongThucTtId,
        ghiChu,
        maChuanChi,
      } = state.currentItem;

      let payload = {};

      const callApi = state.currentItem?.id ? chinhSuaTaiTro : themMoiTaiTro;

      if (state.currentItem?.id) {
        payload = {
          id: state.currentItem?.id,
          tienBaoHiem,
          tienKhongBaoHiem,
          phuongThucTtId,
          donViTaiTroId,
        };
      } else {
        payload = {
          nguonTaiTro,
          donViTaiTroId,
          phieuThuId,
          tienBaoHiem,
          tienKhongBaoHiem,
          phuongThucTtId,
          ghiChu,
          maChuanChi,
        };
      }
      callApi(payload)
        .then(() => {
          setState({
            currentItem: {},
            currentIndex: -1,
          });
          getThongTinPhieuThu(id);
          getDsHoaDonTaiTro({
            nbDotDieuTriId,
            phieuThuId: id,
            page: "",
            size: "",
          });
        })
        .catch((e) => {
          if (e?.code === 1030) {
            showConfirm(
              {
                title: t("common.thongBao"),
                content: e?.message,
                cancelText: t("common.huy"),
                okText: t("common.xacNhan"),
                showBtnOk: true,
              },
              () => {
                callApi({ ...payload, boQuaHanMuc: true }).then(() => {
                  setState({
                    currentItem: {},
                    currentIndex: -1,
                  });
                  getThongTinPhieuThu(id);
                  getDsHoaDonTaiTro({
                    nbDotDieuTriId,
                    phieuThuId: id,
                    page: "",
                    size: "",
                  });
                });
              }
            );
          }
        });
    } else {
      handleClickBack();
    }
  };
  const hotKeys = [
    {
      keyCode: HOTKEY.ESC,
      onEvent: () => {
        onOk(false)();
      },
    },
    {
      keyCode: HOTKEY.F4,
      onEvent: () => {
        onOk(true)();
      },
    },
  ];
  useEffect(() => {
    if (state.show) {
      getListAllPhuongThucThanhToan({ page: "", size: "" });
      getDsDoiTac({ dsLoaiDoiTac: [60] });
      getDsHoaDonTaiTro({
        nbDotDieuTriId,
        phieuThuId: id,
        page: "",
        size: "",
      });
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  const onAddNewRow = () => {
    if (state.currentIndex === 0) {
      return;
    }

    let item = {
      nguonTaiTro: 60,
      donViTaiTroId: null,
      phieuThuId: id,
      tienBaoHiem: 0,
      tienKhongBaoHiem: 0,
      phuongThucTtId: 1,
    };

    setState({
      currentItem: item,
      currentIndex: 0,
      data: [item, ...state.data],
    });
  };

  const onThemMoiTheoDV = () => {
    refModalThemMoiTaiTroBHBLTheoDV.current &&
      refModalThemMoiTaiTroBHBLTheoDV.current.show();
  };

  const onChange = (key) => (e) => {
    let value = e;

    if (key == "nguonTaiTro") {
      getDsDoiTac({ dsLoaiDoiTac: value });
    }

    if (key == "tienKhongBaoHiem" || key == "tienBaoHiem") {
      // value = parseFloatNumber(e?.target?.value) || 0;
      const otherKey =
        key == "tienKhongBaoHiem" ? "tienBaoHiem" : "tienKhongBaoHiem";

      setState({
        currentItem: {
          ...state.currentItem,
          [key]: value,
          thanhTien: value + state.currentItem[otherKey],
        },
      });
    } else {
      const oldDonViTaiTroId = state.currentItem?.donViTaiTroId;

      setState({ currentItem: { ...state.currentItem, [key]: value } });

      if (key == "donViTaiTroId" && state.currentItem?.trangThai === 20) {
        //nếu chỉnh sửa tên cá nhân - đơn vị tài trợ với line đã phát hành thì show popup cảnh báo
        showConfirm(
          {
            title: t("common.canhBao"),
            content: t("thuNgan.thongTinCaNhanDonViDaDuocPhatHanhHoaDon"),
            cancelText: t("common.quayLai"),
            okText: t("thuNgan.suaThongTin"),
            showImg: true,
            showBtnOk: true,
            typeModal: "warning",
          },
          () => {},
          () => {
            setState({
              currentItem: {
                ...state.currentItem,
                donViTaiTroId: oldDonViTaiTroId,
              },
            });
          }
        );
      }
    }
  };

  const onPhatHanhHoaDon = (item) => () => {
    if (
      thongTinPhieuThu?.thanhToan !==
      TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN
    ) {
      message.error(
        t("thuNgan.phieuThuChuaThanhToanKhongThePhatHanhHoaDonDienTu")
      );
      return;
    }
    phatHanhHoaDon(item?.id).then(() => {
      onPrint(item);
      getDsHoaDonTaiTro({
        nbDotDieuTriId,
        phieuThuId: id,
        page: "",
        size: "",
      });
    });
  };

  const onDelete = (record) => () => {
    if (refModalDeleteHoaDon.current) {
      refModalDeleteHoaDon.current.show(record, handleDelete);
    }
  };

  const handleDelete = (hoaDonid, lyDo) => {
    deleteHoaDon({ id: hoaDonid, lyDo }).then((s) => {
      getDsHoaDonTaiTro({
        nbDotDieuTriId,
        phieuThuId: id,
        page: "",
        size: "",
      });

      getThongTinPhieuThu(id);
    });
  };

  const onEditRow = (record, index) => () => {
    // getDsDoiTac({ dsLoaiDoiTac: record?.nguonTaiTro });
    // setState({
    //   currentItem: record,
    //   currentIndex: index,
    // });

    refModalThemMoiTaiTroBHBLTheoDV.current &&
      refModalThemMoiTaiTroBHBLTheoDV.current.show({ currentItem: record });
  };
  const onPrint = async ({ id }) => {
    try {
      showLoading();
      await inHoaDon({ hoaDonId: id, dinhDang: dataDINH_DANG_XEM_HOA_DON });
    } catch (error) {
    } finally {
      hideLoading();
    }
  };

  const columns = [
    Column({
      title: t("common.stt"),
      width: 60,
      dataIndex: "index",
      key: "index",
      align: "center",
      i18Name: "common.stt",
    }),
    Column({
      title: t("thuNgan.nguonTaiTro.loaiNguonTaiTro"),
      width: 200,
      dataIndex: "nguonTaiTro",
      key: "nguonTaiTro",
      align: "left",
      i18Name: "thuNgan.nguonTaiTro.loaiNguonTaiTro",
      render: (item, list, index) => {
        if (index === state.currentIndex) {
          return (
            <Select
              data={listlistNguonTaiTroMemo}
              defaultValue={state.currentItem.nguonTaiTro}
              placeholder={t("thuNgan.chonLoaiNguonTaiTro")}
              onChange={onChange("nguonTaiTro")}
              dropdownMatchSelectWidth={320}
            />
          );
        } else
          return listlistNguonTaiTroMemo.find((x) => x.id == item)?.ten || "";
      },
    }),
    Column({
      title: t("thuNgan.nguonTaiTro.tenDonViTaiTro"),
      width: 200,
      dataIndex: "tenDonViTaiTro",
      key: "tenDonViTaiTro",
      align: "left",
      i18Name: "thuNgan.nguonTaiTro.tenDonViTaiTro",
      render: (item, list, index) => {
        if (
          index === state.currentIndex &&
          (!state.currentItem?.id ||
            checkRole([ROLES["THU_NGAN"].SUA_TEN_DON_VI_PHAT_HANH_HOA_DON]))
        ) {
          return (
            <Select
              onChange={onChange("donViTaiTroId")}
              placeholder={t("thuNgan.nguonTaiTro.chonDonViTaiTro")}
              value={state.currentItem.donViTaiTroId}
              data={listDoiTac}
              notFoundContent={
                <div>
                  <div style={{ color: "#7A869A", textAlign: "center" }}>
                    <small>{t("common.khongCoDuLieuPhuHop")}</small>
                  </div>
                  <Row justify="center">
                    <Button
                      style={{
                        border: "1px solid",
                        borderRadius: "10px",
                        margin: "auto",
                        lineHeight: 0,
                        cursor: "pointer",
                      }}
                      onClick={() => {
                        refThemMoiCaNhanDonVi.current &&
                          refThemMoiCaNhanDonVi.current.show({}, () => {
                            getDsDoiTac({ dsLoaiDoiTac: [60] });
                          });
                      }}
                    >
                      {t("thuNgan.nguonTaiTro.themNhanhCaNhanDonVi")}
                    </Button>
                  </Row>
                </div>
              }
            />
          );
        } else return item;
      },
    }),
    Column({
      title: t("thuNgan.nguonTaiTro.tienKhongBaoHiem"),
      width: "120px",
      dataIndex: "tienKhongBaoHiem",
      key: "tienKhongBaoHiem",
      align: "left",
      i18Name: "thuNgan.nguonTaiTro.tienKhongBaoHiem",
      render: (item, list, index) => {
        if (index === state.currentIndex && list?.trangThai !== 20) {
          return (
            <InputNumberField
              // type="number"
              // formatPrice={true}
              value={item}
              isAllowed={(values) => {
                const { floatValue } = values;
                return floatValue >= 0;
              }}
              placeholder={t("thuNgan.nhapTienTaiTroNgoaiBaoHiem")}
              onChange={onChange("tienKhongBaoHiem")}
            />
          );
        } else {
          return (item || 0).formatPrice();
        }
      },
    }),
    Column({
      title: t("thuNgan.nguonTaiTro.tienBaoHiem"),
      width: "120px",
      dataIndex: "tienBaoHiem",
      key: "tienBaoHiem",
      align: "left",
      i18Name: "thuNgan.nguonTaiTro.tienBaoHiem",
      render: (item, list, index) => {
        if (index === state.currentIndex && list?.trangThai !== 20) {
          return (
            <InputNumberField
              // type="number"
              // formatPrice={true}
              value={item}
              isAllowed={(values) => {
                const { floatValue } = values;
                return floatValue >= 0;
              }}
              placeholder={t("thuNgan.nhapTienTaiTroBnCungChiTra")}
              onChange={onChange("tienBaoHiem")}
            />
          );
        } else {
          return (item || 0).formatPrice();
        }
      },
    }),
    Column({
      title: t("thuNgan.nguonTaiTro.hinhThucThanhToan"),
      width: "180px",
      dataIndex: "tenPhuongThucTt",
      key: "tenPhuongThucTt",
      align: "center",
      i18Name: "thuNgan.nguonTaiTro.hinhThucThanhToan",
      render: (item, list, index) => {
        if (
          index === state.currentIndex &&
          ((list.trangThai && list.trangThai < 20) || !state.currentItem?.id)
        ) {
          return (
            <Select
              placeholder={t("thuNgan.chonPhuongThucThanhToan")}
              data={listAllPhuongThucThanhToan}
              defaultValue={state.currentItem.phuongThucTtId}
              onChange={onChange("phuongThucTtId")}
            />
          );
        } else {
          return item;
        }
      },
    }),
    Column({
      title: t("thuNgan.nguonTaiTro.maChuanChi"),
      width: "180px",
      dataIndex: "maChuanChi",
      key: "maChuanChi",
      align: "left",
      i18Name: "thuNgan.nguonTaiTro.maChuanChi",
      render: (item, list, index) => {
        if (index === state.currentIndex && !state.currentItem?.id) {
          return (
            <InputTimeout
              placeholder={t("thuNgan.nhapMaChuanChi")}
              defaultValue={state.currentItem.maChuanChi}
              onChange={onChange("maChuanChi")}
            />
          );
        } else {
          return item;
        }
      },
    }),
    Column({
      title: t("thuNgan.nguonTaiTro.thanhTien"),
      width: "120px",
      dataIndex: "thanhTien",
      key: "thanhTien",
      align: "left",
      i18Name: "thuNgan.nguonTaiTro.thanhTien",
      render: (item, list, index) => {
        if (index === state.currentIndex) {
          return (state.currentItem?.thanhTien || 0).formatPrice();
        } else return (item || 0).formatPrice();
      },
    }),
    Column({
      title: t("thuNgan.nguonTaiTro.ghiChu"),
      width: "180px",
      dataIndex: "ghiChu",
      key: "ghiChu",
      align: "left",
      i18Name: "thuNgan.nguonTaiTro.ghiChu",
      render: (item, list, index) => {
        if (index === state.currentIndex && !state.currentItem?.id) {
          return (
            <InputTimeout
              placeholder={t("common.nhapGhiChu")}
              defaultValue={state.currentItem.ghiChu}
              onChange={onChange("ghiChu")}
            />
          );
        } else {
          return item;
        }
      },
    }),
    Column({
      title: t("common.trangThai"),
      width: "140px",
      dataIndex: "ghiChu",
      key: "ghiChu",
      i18Name: "common.trangThai",
      render: (text) =>
        (listTrangThaiHoaDon || []).find((item) => item.id == text)?.ten || "",
    }),
    Column({
      title: t("thuNgan.nguonTaiTro.kyHieuHoaDon"),
      width: "100px",
      dataIndex: "kyHieuHoaDon",
      key: "kyHieuHoaDon",
      align: "left",
      i18Name: "thuNgan.nguonTaiTro.kyHieuHoaDon",
    }),
    Column({
      title: t("thuNgan.soHoaDon"),
      width: "100px",
      dataIndex: "soHoaDon",
      key: "soHoaDon",
      align: "left",
      i18Name: "thuNgan.soHoaDon",
    }),
    Column({
      title: t("thuNgan.nguonTaiTro.thoiGianPhatHanhHd"),
      width: "160px",
      dataIndex: "thoiGianPhatHanhHd",
      key: "thoiGianPhatHanhHd",
      align: "left",
      i18Name: "thuNgan.nguonTaiTro.thoiGianPhatHanhHd",
      render: (field, item, index) =>
        field ? moment(field).format("DD-MM-YYYY HH:mm:ss") : "",
    }),
    Column({
      title: t("common.thaoTac"),
      width: "120px",
      align: "center",
      fixed: "right",
      ignore: true,
      render: (item, list, index) => {
        return (
          <>
            {index === state.currentIndex && (
              <>
                <Tooltip title={t("common.luu")}>
                  <SVG.IcSave
                    color={"var(--color-blue-primary)"}
                    className="ic-action"
                    onClick={onOk(true)}
                  />
                </Tooltip>

                <Tooltip title={t("common.huy")}>
                  <SVG.IcCancel
                    className="ic-action"
                    onClick={() => {
                      setState({
                        currentItem: {},
                        currentIndex: -1,
                        data: state.data.filter((x) => !!x.id),
                      });
                    }}
                  />
                </Tooltip>
              </>
            )}

            {index !== state.currentIndex && (
              <>
                {/* 20: HĐ đã phát hành */}
                {item?.trangThai !== 20 && (
                  <Tooltip title={t("thuNgan.phatHanhHoaDon")}>
                    {index !== state.currentIndex && (
                      <SVG.IcSend
                        color={"var(--color-blue-primary)"}
                        onClick={onPhatHanhHoaDon(item)}
                      />
                    )}
                  </Tooltip>
                )}
                {/* 10: HĐ tạo mới, 15: HĐ phát hành lỗi */}
                {([10, 15].includes(list?.trangThai) ||
                  checkRole([
                    ROLES["THU_NGAN"].SUA_TEN_DON_VI_PHAT_HANH_HOA_DON,
                  ])) && (
                  <Tooltip title={t("thuNgan.suaHoaDonTaiTro")}>
                    <SVG.IcEdit
                      className="ic-action"
                      onClick={onEditRow(list, index)}
                    />
                  </Tooltip>
                )}

                {/* 10: HĐ tạo mới, 15: HĐ phát hành lỗi */}
                {[10, 15].includes(list?.trangThai) && (
                  <Tooltip title={t("thuNgan.xoaHoaDonTaiTro")}>
                    <SVG.IcDelete
                      className="ic-action"
                      onClick={onDelete(list)}
                    />
                  </Tooltip>
                )}
                {item?.trangThai === 20 && (
                  <Tooltip title={t("thuNgan.inHoaDon")}>
                    <SVG.IcPrint
                      className="ic-action"
                      onClick={() => onPrint(item)}
                    />
                  </Tooltip>
                )}
              </>
            )}
          </>
        );
      },
    }),
  ];

  return (
    <ModalTemplate
      width={"80%"}
      ref={refModal}
      title={t("thuNgan.nguonTaiTro.nguonTaiTroBHBL")}
      onCancel={onOk(false)}
      closable={false}
      actionLeft={<Button.QuayLai onClick={onOk(false)} />}
      rightTitle={
        <RightTitleWrapper>
          <AuthWrapper accessRoles={[ROLES["THU_NGAN"].TAI_TRO_THEO_DICH_VU]}>
            <Button type="primary" minWidth={100} onClick={onThemMoiTheoDV}>
              {t("thuNgan.themMoiTheoDV")}
            </Button>
          </AuthWrapper>
          <AuthWrapper
            accessRoles={[ROLES["THU_NGAN"].THEM_TAI_TRO_THEO_TONG_TIEN]}
          >
            <Button type="primary" minWidth={100} onClick={onAddNewRow}>
              {t("common.themMoi")}
            </Button>
          </AuthWrapper>
        </RightTitleWrapper>
      }
      hotKeys={hotKeys}
      destroyOnClose
    >
      <Main>
        <TableWrapper
          columns={columns}
          dataSource={state.data}
          styleWrap={{ height: 300 }}
        />
      </Main>

      <ModalXoaHoaDon ref={refModalDeleteHoaDon}></ModalXoaHoaDon>
      <ModalThemMoiDonViCaNhan ref={refThemMoiCaNhanDonVi} />
      <ModalThemMoiTaiTroBHBLTheoDV ref={refModalThemMoiTaiTroBHBLTheoDV} />
    </ModalTemplate>
  );
};

export default forwardRef(ModalTaiTroBHBL);
