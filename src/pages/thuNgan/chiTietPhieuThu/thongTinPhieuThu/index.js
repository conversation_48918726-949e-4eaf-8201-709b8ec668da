import React, { useEffect, useRef, useState, useMemo } from "react";
import {
  ROLES,
  DOI_TUONG,
  MA_BIEU_MAU_EDITOR,
  HOTKEY,
  LOAI_BANG_KE,
  DOI_TUONG_KCB,
  LOAI_PHIEU_THU,
  LOAI_IN,
  LOAI_DICH_VU,
  TRANG_THAI_DICH_VU,
  TRANG_THAI_NB,
  TRANG_THAI_THANH_TOAN_QR,
  TRANG_THAI_PHIEU_THU_THANH_TOAN,
  ENUM,
  LOAI_PHUONG_THUC_TT,
  TRANG_THAI_HOA_DON,
  TRANG_THAI_DUYET_BH,
  TRANG_THAI_XAC_NHAN_BHYT,
  THIET_LAP_CHUNG,
  CACHE_KEY,
} from "constants/index";
import { Collapse, Menu, Spin, message } from "antd";
import {
  DatePicker,
  Dropdown,
  Too<PERSON><PERSON>,
  But<PERSON>,
  <PERSON>th<PERSON>rapper,
  <PERSON>,
  <PERSON><PERSON>Sign<PERSON><PERSON>t,
  Checkbox,
  TableWrapper,
  <PERSON>er<PERSON>earch as Header<PERSON>earchT<PERSON>,
  Chon<PERSON>ho,
} from "components";
import moment from "moment";
import { SVG } from "assets";
import ModalPhuongThucThanhToan from "../ModalPhuongThucThanhToan";
import ModalDichVuThanhToan from "../ModalDichVuThanhToan";
import {
  combineUrlParams,
  formatDecimal,
  formatDecimalNoComma,
  isArray,
  openInNewTab,
  parseListConfig,
  sleep,
} from "utils/index";
import { useDispatch, useSelector } from "react-redux";
import { useHistory, useLocation } from "react-router-dom";
import nbPhieuThuProvider from "data-access/nb-phieu-thu-provider";
import { useTranslation } from "react-i18next";
import printProvider, { printJS } from "data-access/print-provider";
import {
  useCache,
  useConfirm,
  useEnum,
  useLoading,
  useStore,
  useThietLap,
} from "hooks";
import { flatten, get, isEmpty } from "lodash";
import { checkRole, checkRoleOr } from "lib-utils/role-utils";
import fileUtils from "utils/file-utils";
import DsPtTt from "./dsPtTt";
import ModalChonTieuChiBangKe from "components/ModalChonTieuChiBangKe";
import ModalThongTinHoaDon from "../ModalThongTinHoaDon";
import ModalThongBaoThanhToanQrCode from "../ModalThongBaoThanhToanQrCode";
import ModalDayQuyetToanBYHT, {
  FormCanhBaoTaoHoSo,
} from "../ModalDayQuyetToanBYHT";
import ModalCheckBaoHiem from "pages/tiepDon/components/ThongTinTiepDon/ModalCheckBaoHiem";
import ModalNhapLyDoHuyThanhToan from "./ModalNhapLyDoHuyThanhToan";
import ModalNhapLyDoTuChoiDuyetChiPhi from "./ModalNhapLyDoTuChoiDuyetChiPhi";
import ModalTaoQrCode from "pages/thuNgan/quanLyTamUng/chiTietQuanLyTamUng/Modal/ModalTaoQrCode";
import ModalTaoQrCodeLoi from "pages/thuNgan/quanLyTamUng/chiTietQuanLyTamUng/Modal/ModalTaoQrCodeLoi";
import { checkIsPhieuKySo } from "utils/phieu-utils";
import DOMPurify from "dompurify";
import ModalSinhPhieuChi from "../ModalSinhPhieuChi";
import {
  contentMsgConfirm,
  LIST_FILE_SHOW_MODAL,
  TRANG_THAI_TKTT,
} from "./utils";
import useDataCenter from "./useDataCenter";
import useThietLapChung from "./useThietLapChung";
import useTinhTienBienLaiHoaDon from "./hooks/useTinhTienBienLaiHoaDon";
import ModalXacNhanHuyThanhToan from "../ModalXacNhanHuyThanhToan";
import ModalXacNhanBhyt from "../ModalXacNhanBhyt";
import { Main } from "./styled";
import { centralizedErrorHandling, toSafePromise } from "lib-utils";
import ModalInPhieuTomTatBenhAn from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalInPhieuTomTatBenhAn";
import nbXacNhanBaoHiemProvider from "data-access/nb-xac-nhan-bao-hiem-provider";
import { onCheckThongTinTheBh } from "utils/index";
import MenuPhieuIn from "./components/MenuPhieuIn";
import TenThuNgan from "./components/TenThuNgan";
import ThongTinHoanUng from "./components/ThongTinHoanUng";
const { Panel } = Collapse;

function ThongTinPhieuThu(props) {
  const { showConfirm } = useConfirm();
  const { t, i18n } = useTranslation();
  const MESS_WARN = t("common.nbCoPhieuThuChuaTTOrPhieuHoanChuaDuyetHoan");
  const { getKho } = ChonKho.useChonKho(CACHE_KEY.CHON_KHO_THU_NGAN);
  const refModalPhuongThucThanhToan = useRef(null);
  const refModalThongTinHoaDon = useRef(null);
  const refDropDownPrint = useRef(null);
  const refModalChonTieuChiBangKe = useRef(null);
  const refModalCheckBaoHiem = useRef();
  const refNhapLyDoHuyThanhToan = useRef();
  const refModalNhapLyDoTuChoiDuyetChiPhi = useRef();
  const refCanhBaoTaoHoSoQuyetToan = useRef(null);
  const refBtnXacNhanBhyt = useRef(null);
  const refTimeout = useRef(null);
  const refTimeoutQr = useRef(null);
  const refCurrentMsg = useRef(null);
  const refModalXacNhanHuyThanhToan = useRef(null);
  const refBtnDuyetBh = useRef(null);
  const refModalXacNhanBhyt = useRef(null);
  const refModalInPhieuTomTatBA = useRef(null);
  const thongTinPhieuThu = useStore("thuNgan.thongTinPhieuThu", {});

  const ttNbHoanUng = useStore("thuNgan.ttNbHoanUng", {});

  const { listAllService } = useSelector((state) => state.danhSachDichVu);
  const isLoadingThongTinPhieuThu = useSelector(
    (state) => state.thuNgan.isLoadingThongTinPhieuThu
  );
  const { listAllPhuongThucThanhToan } = useSelector(
    (state) => state.phuongThucTT
  );

  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan");
  const nbDangThucHien = useStore("danhSachPhieuThu.nbDangThucHien", {});
  const listDv = useStore("danhSachDichVu.listData", []);
  const trangThaiDuyetBh = useStore(
    "dsDuyetBH.chiTietDuyetBH.trangThaiDuyetBh",
    null
  );
  const listAllDoiTacThanhToan = useStore("doiTac.listAllDoiTacThanhToan", []);
  const [listtrangThaiPhieuThu] = useEnum(ENUM.TRANG_THAI_PHIEU_THU);
  const [listGioiTinh] = useEnum(ENUM.GIOI_TINH);
  const [dataBAM_THANH_TOAN_TU_DONG_BAT_BANG_KE_6556] = useThietLap(
    THIET_LAP_CHUNG.BAM_THANH_TOAN_TU_DONG_BAT_BANG_KE_6556
  );
  const [dataIN_BANG_KE_TRUOC_THANH_TOAN] = useThietLap(
    THIET_LAP_CHUNG.IN_BANG_KE_TRUOC_THANH_TOAN,
    "false"
  );
  const [dataTHU_NGAN_TU_DONG_IN_KHONG_HIEN_THI_BANG_KE] = useThietLap(
    THIET_LAP_CHUNG.THU_NGAN_TU_DONG_IN_KHONG_HIEN_THI_BANG_KE,
    "false"
  );
  const [dataTU_DONG_CHECK_CO_HOAN_TAM_UNG, isLoadFinish] = useThietLap(
    THIET_LAP_CHUNG.TU_DONG_CHECK_CO_HOAN_TAM_UNG
  );

  const [expandTongTien, setExpandTongTien] = useCache(
    "",
    CACHE_KEY.THU_NGAN_EXPAND_TONG_TIEN,
    null,
    false,
    true
  );

  const { tienTamUng } = thongTinPhieuThu || {};

  const {
    phieuIn: {
      getListPhieu,
      showFileEditor,
      getFilePhieuIn,
      getPhieuInTheoDsMa,
    },
    phimTat: { onRegisterHotkey },
    thuNgan: {
      getThongTinPhieuThu,
      kiemTraQuyetToanBH,
      taoDayHoSo,
      thanhToanPhieuThu,
      kiemTraTrangThaiThanhToanQr,
      huyThanhToan,
      updateThoiGianTT,
      inDanhSachChiTietDichVuNb,
      xuatHoaDonNhap,
      hoanThanhTongKetThanhToan,
      huyTongKetThanhToan,
      getThongTinHoanUng,
      inListPhieuKhiThanhToan,
      kiemTraGiaoDich,
    },
    dsHoaDonDienTu: {
      getTrangThaiDaPhatHanhHDDVBH,
      inHoaDon,
      getDanhSachHoaDon,
      getBienBanDieuChinh,
    },
    nbTheNbHuy: { createOrEdit: postRecordHuy },
    nbDotDieuTri: { getThongTinCoBan },
    danhSachPhieuThu: { onSearch, clearData: clearDataDsPhieuThu },
    danhSachDichVu: { searchAll: getAllListServices },
    danhSachPhieuYeuCauHoan: { sinhPhieuChi, chiPhieuThu, phieuChi },
    tiepDon: { giamDinhThe, onUpdate },
    danhSachNguoiBenhNoiTru: { duyetChiPhi, huyDuyetChiPhi },
    danhSachNguoiBenhChoTaoHoSoQuyetToanBHYT: { kiemTraThoiGianQuyetToan },
    dsDuyetBH: { getChiTietDuyetBH, duyetBaoHiem, huyDuyetBaoHiem },
    thuNgan: { huyQrThanhToan, taoQrThanhToan },
    nbDvHoan: { hoanTienNganHang },
    khoa: { getKhoaTheoTaiKhoan },
    doiTac: { getListAllDoiTacThanhToan },
  } = useDispatch();
  const { showLoading, hideLoading } = useLoading();
  const { state: locationState } = useLocation();

  const {
    phieuThuId,
    nbDotDieuTriId,
    layerId,
    refSinhPhieuChi,
    handleAfterSubmit,
    nhaTamUngId, //tòa nhà id
    caLamViecId,
    quayId,
    listDsPhieuThu,
    hideBtnXuatHDDT = false,
    maViTriManHinh = {
      maManHinh: "004",
      maViTri: "00401",
    },
    onFocusSearch = () => {},
  } = props;

  const [putDataCenter, clearFunc] = useDataCenter({ nhaTamUngId });
  const {
    isIn2LinePhieuThu,
    isXuatHoaDonDayDv0Dong,
    isChoChonLaiNgayTaoXMLKhiNgayTaoKhacNgayRa,
    isPhatHanhHoaDonNhanh,
    isCanhBaoDuyetDLSKhiPhatThuocTaoPhieuLinh,
    isPhatThuocNhaThuocKhiThanhToan,
    dsMaBaoCaoTuDongInBangKe6556,
    kiemTra,
    dsManHinh,
    isCheckThoiGianQuyetToanHSXMLVoiNgayRaVien,
    isHoanUngTTVaSinhPhieuTamUng,
    isCanhBaoChuaThanhToanManThuNgan,
    isPhatHanhHoaDonKhiThanhToanNgoaiTru,
    isNgoaiTruNhieuPhieuThuBH,
    isTuDongThanhToanPhieuThuTienMat,
    isHienThiTongTienTheoDoiTuong,
    isCanhBaoChuaDongHoSoKhamKhiThanhToan,
    isHuyHoaDonKhiHuyThanhToanPT,
    isInPhieuThuKhiThanhToan,
    isKhongTuDongDongTab,
    dataDinhDangXemHoaDon,
    loaiQuyetToan,
    isBangKeInTheoKhoaChiDinh,
    isBangKeTongHopExcel,
    dataPhuongThucThanhToanMacDinh,
    dataMaLoiBHDuocThanhToan,
    dataKiemTraTheBaoHiem,
    isShowTongKetThanhToan,
    dataMA_DOI_TAC_HOAN_TIEN,
    dataMA_PTTT_CHUYEN_KHOAN,
    checkDayQuyetToanKhiThanhToan,
    isKiemTraThuocDuocLamSang,
    isShowThanhTienBienLaiHoaDon,
    listMaPhieuInKhiXacNhanBHYT,
    isChoInBangKeTienIchNbNgoaiTru,
    dataMA_DOI_TAC_QRCODE_CAP_NHAT_GIAO_DICH,
    isKhongHienThiTienPhuThu,
  } = useThietLapChung();
  const [tinhTongTienBienLai, tinhTongTienHoaDon] = useTinhTienBienLaiHoaDon();

  const {
    thanhTien,
    tienNbCungChiTra,
    tongTien,
    tienMienGiam,
    tienMienGiamPhieuThu,
    tienBhThanhToan,
    dsPhuongThucTt,
    tienNbTuTra,
    tienNbPhuThu,
    tienNguonKhac,
    tienMienGiamDichVu,
    phanTramMienGiam,
    thanhToan,
    tenThuNgan,
    thoiGianThanhToan,
    dsMaGiamGia = [],
    maChuanChi,
    soPhieu,
    tienGiamGia,
    loaiPhieuThu,
    doiTuongKcb,
    kyHieu,
    tienTaiTroKhongBaoHiem,
    tienTaiTroBaoHiem,
    dsHoaDonTaiTro,
    tienBhThanhToanTrongGoi,
    tienNbCungChiTraTrongGoi,
    dsBangKe = [],
    trangThaiHoaDon,
    dsHoaDon = [],
    thoiGianHuyThanhToan,
    tenNguoiYeuCau,
  } = thongTinPhieuThu;
  const refModalDichVuThanhToan = useRef();
  const refBtnXuatHDDT = useRef(null);
  const refBtnSinhPhieuChi = useRef(null);
  const refClickThanhToanFunc = useRef(null);
  const history = useHistory();
  const refModalSignPrint = useRef(null);
  const refNhapLyDo = useRef(null);
  const refSelectPhuongThucTt = useRef(null);
  const refModalDayQuyetToanBYHT = useRef(null);
  const refClickQuyetToan = useRef(null);
  const refClickQuyetToan130 = useRef(null);
  const refModalTaoQrCode = useRef(null);
  const refModalTaoQrCodeLoi = useRef(null);
  const refModalThongBaoThanhToanQrCode = useRef(null);
  const refPtttChanged = useRef(false);
  const refIsWaitConfirmHoanTamUng = useRef(false);

  const [editTime, setEditTime] = useState(false);
  const [updateTime, setUpdateTime] = useState(null);
  const [listPhieu, setListPhieu] = useState([]);
  const [showBtnQuyetToan, setShowBtnQuyetToan] = useState(false);
  const [showBtnQuyetToanChuaThanhToan, setShowBtnQuyetToanChuaThanhToan] =
    useState(false);
  const [phuongThucTtId, setPhuongThucTtId] = useState();
  const [ptttMacDinhTheoThietLap, setPtttMacDinhTheoThietLap] = useState(); // convert từ mã PHUONG_THUC_THANH_TOAN_MAC_DINH sang id
  const [activeKey, setActiveKey] = useState([1, 2]);
  const [openPrint, setOpenPrint] = useState(false);
  const [curBaocao, setCurBaocao] = useState({});
  const [openSelectPhuongThucTt, setOpenSelectPhuongThucTt] = useState(false);
  const [showDuyetBh, setShowDuyetBh] = useState(false);

  const [state, _setState] = useState({ sinhPhieuThuTamUng: false });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const tienNbTuTraMemo = useMemo(() => {
    return (
      (tienNbTuTra || 0) -
      (tienBhThanhToanTrongGoi || 0) -
      (tienNbCungChiTraTrongGoi || 0)
    );
  }, [tienNbTuTra, tienBhThanhToanTrongGoi, tienNbCungChiTraTrongGoi]);

  const tongTienMemo = useMemo(() => {
    return (
      (tongTien || 0) -
      (tienBhThanhToanTrongGoi || 0) -
      (tienNbCungChiTraTrongGoi || 0)
    );
  }, [tongTien, tienBhThanhToanTrongGoi, tienNbCungChiTraTrongGoi]);

  const qrThanhToan = useMemo(() => {
    let result = null;
    if (isArray(dsPhuongThucTt, true)) {
      result = dsPhuongThucTt.find(
        (i) => i.loaiPhuongThucTt === LOAI_PHUONG_THUC_TT.QR_CODE
      );
    }
    return result;
  }, [dsPhuongThucTt]);

  const bienBanDieuChinhHoaDonId = useMemo(() => {
    if (!isArray(dsHoaDon, true)) return null;
    return dsHoaDon.find(
      (i) => i.trangThai === TRANG_THAI_HOA_DON.HD_DA_PHAT_HANH
    )?.hoaDonId;
  }, [dsHoaDon]);

  const hienThiThanhToan = useMemo(() => {
    if (!isEmpty(thongTinPhieuThu) && !isEmpty(thongTinCoBan)) {
      return (
        thongTinPhieuThu?.nhoHonMucCungChiTra ||
        !thongTinCoBan?.phieuThuNhoHonMucCungChiTra ||
        thongTinPhieuThu?.loaiPhieuThu == 1 ||
        thongTinCoBan.mucHuongTheBhyt === 100 ||
        thongTinPhieuThu.tienBhThanhToan == 0
      );
    }
    return true;
  }, [thongTinCoBan, thongTinPhieuThu]);

  const showActions = useMemo(() => {
    if (!phieuThuId) return { showBtnThanhToan: false, showBtnDuyet: false };
    let showBtnThanhToan =
      thanhToan !== TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN &&
      hienThiThanhToan;
    let showBtnDuyet = false;
    if (qrThanhToan) {
      const isQrCode = [
        TRANG_THAI_THANH_TOAN_QR.MOI,
        TRANG_THAI_THANH_TOAN_QR.TAO_QR,
        TRANG_THAI_THANH_TOAN_QR.THANH_TOAN,
        TRANG_THAI_THANH_TOAN_QR.THANH_TOAN_LOI,
        TRANG_THAI_THANH_TOAN_QR.TAO_QR_LOI,
      ].includes(qrThanhToan.trangThaiThanhToan);
      showBtnDuyet = showBtnThanhToan && isQrCode;
      showBtnThanhToan = showBtnThanhToan && !isQrCode;
    }
    return {
      showBtnThanhToan,
      showBtnDuyet,
    };
  }, [
    phieuThuId,
    qrThanhToan,
    thanhToan,
    hienThiThanhToan,
    TRANG_THAI_PHIEU_THU_THANH_TOAN,
    TRANG_THAI_THANH_TOAN_QR,
  ]);

  const showBtnCapNhatGiaoDichQr = useMemo(() => {
    let listMaDoiTac = parseListConfig(
      dataMA_DOI_TAC_QRCODE_CAP_NHAT_GIAO_DICH
    );
    if (
      !qrThanhToan ||
      !isArray(listAllDoiTacThanhToan, true) ||
      !isArray(listMaDoiTac, true)
    ) {
      return false;
    }
    let doiTac =
      listAllDoiTacThanhToan.find((x) => x.id === qrThanhToan.nganHangId) || {};

    return (
      listMaDoiTac.includes(doiTac.ma) &&
      [TRANG_THAI_THANH_TOAN_QR.MOI, TRANG_THAI_THANH_TOAN_QR.TAO_QR].includes(
        qrThanhToan.trangThaiThanhToan
      ) &&
      thanhToan !== TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN &&
      hienThiThanhToan
    );
  }, [
    qrThanhToan,
    dataMA_DOI_TAC_QRCODE_CAP_NHAT_GIAO_DICH,
    listAllDoiTacThanhToan,
    thanhToan,
    hienThiThanhToan,
  ]);

  const onConfirmHoanTamUng = () => {
    refIsWaitConfirmHoanTamUng.current = true;

    showConfirm(
      {
        title: t("common.canhBao"),
        content: `${t(
          "thuNgan.nbCoTonTaiPhieuTamUngChuaHoanBanCoMuonHoanUngKhong"
        )}`,
        cancelText: t("common.quayLai"),
        okText: t("common.xacNhan"),
        typeModal: "warning",
        showBtnOk: true,
        maskClosable: false,
      },
      () => {
        setState({
          isTamUng: true,
        });

        refIsWaitConfirmHoanTamUng.current = false;
      },
      () => {
        refIsWaitConfirmHoanTamUng.current = false;
      }
    );
  };

  useEffect(() => {
    if (isLoadFinish) {
      if (
        thongTinPhieuThu?.nbDotDieuTriId != nbDotDieuTriId ||
        thongTinPhieuThu?.id != phieuThuId ||
        thanhToan === TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN
      ) {
        return;
      }
      let _isTamUng = false;

      if (
        tienTamUng > 0 &&
        ![LOAI_PHIEU_THU.NGOAI_DIEU_TRI, LOAI_PHIEU_THU.NHA_THUOC].includes(
          loaiPhieuThu
        )
      ) {
        if (dataTU_DONG_CHECK_CO_HOAN_TAM_UNG?.eval()) {
          //check thêm điều kiện trạng thái NB ==100
          if (thongTinCoBan?.trangThaiNb == TRANG_THAI_NB.DA_RA_VIEN) {
            _isTamUng = true;
          } else if (
            !checkRole([ROLES["THU_NGAN"].XAC_NHAN_BHYT]) &&
            !thongTinCoBan?.maThe
          ) {
            onConfirmHoanTamUng();
          }
        } else {
          _isTamUng = true;
        }
      }

      setState({
        isTamUng: _isTamUng,
      });
    }
  }, [
    tienTamUng,
    loaiPhieuThu,
    thongTinCoBan?.trangThaiNb,
    thongTinCoBan?.maThe,
    isLoadFinish,
    phieuThuId,
    nbDotDieuTriId,
    thanhToan,
  ]);

  useEffect(() => {
    if (nbDotDieuTriId) {
      getChiTietDuyetBH(nbDotDieuTriId, true);
    }
  }, [nbDotDieuTriId]);

  useEffect(() => {
    if (nbDotDieuTriId && phieuThuId) {
      getAllListServices({
        page: "",
        size: "",
        nbDotDieuTriId: nbDotDieuTriId,
        phieuThuId: phieuThuId,
        dataSortColumn: { tenDichVu: 1 },
      });

      getThongTinHoanUng({
        page: 0,
        size: 10,
        nbDotDieuTriId,
        phieuThuId,
        dsTrangThai: 60,
      });
    }
  }, [nbDotDieuTriId, phieuThuId]);

  useEffect(() => {
    if (
      qrThanhToan &&
      [
        TRANG_THAI_THANH_TOAN_QR.MOI,
        TRANG_THAI_THANH_TOAN_QR.TAO_QR,
        TRANG_THAI_THANH_TOAN_QR.TAO_QR_LOI,
      ].includes(qrThanhToan.trangThaiThanhToan) &&
      qrThanhToan.phuongThucTtId
    ) {
      setPhuongThucTtId(qrThanhToan.phuongThucTtId);
    } else if (
      dataPhuongThucThanhToanMacDinh &&
      isArray(listAllPhuongThucThanhToan, true) &&
      !qrThanhToan
    ) {
      let ptttId = listAllPhuongThucThanhToan.find(
        (i) => i.ma === dataPhuongThucThanhToanMacDinh
      )?.id;
      if (ptttId) {
        setPhuongThucTtId(+ptttId);
        setPtttMacDinhTheoThietLap(+ptttId);
      }
    }
  }, [
    qrThanhToan,
    dataPhuongThucThanhToanMacDinh,
    listAllPhuongThucThanhToan,
    phieuThuId,
  ]);

  // Chờ 60s nếu không có thay đổi thì clear thông tin
  useEffect(() => {
    getKhoaTheoTaiKhoan({ page: "", size: "" });
    getListAllDoiTacThanhToan({ page: "", size: "", active: true });
    return () => {
      // clearTimeout(timeout);
      clearFunc();
      refPtttChanged.current = false;
    };
  }, []);

  useEffect(() => {
    onRegisterHotkey({
      layerId: layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.F3,
          onEvent: () => {
            onOpenChange(setOpenPrint)(true);
            setOpenSelectPhuongThucTt(false);
            refSelectPhuongThucTt.current &&
              refSelectPhuongThucTt.current.blur();
          },
        },
        {
          keyCode: HOTKEY.F7,
          onEvent: () => {
            thanhToan !== TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN &&
              refSelectPhuongThucTt.current &&
              refSelectPhuongThucTt.current.focus();
            setOpenSelectPhuongThucTt(true);
            setOpenPrint(false);
          },
        },
        {
          keyCode: HOTKEY.F8,
          onEvent: () => {
            refBtnXuatHDDT.current && refBtnXuatHDDT.current.click();
          },
        },
        {
          keyCode: HOTKEY.F10,
          onEvent: () => {
            refClickQuyetToan130.current &&
              refClickQuyetToan130.current.click();
          },
        },
        {
          keyCode: HOTKEY.F11,
          onEvent: () => {
            refBtnDuyetBh.current && refBtnDuyetBh.current.click();
          },
        },
        {
          keyCode: HOTKEY.F12,
          onEvent: () => {
            //nếu đang chờ confirm hoàn tạm ứng thì ko cho ấn F12 để thanh toán
            if (refIsWaitConfirmHoanTamUng.current) {
              return;
            }
            showActions.showBtnThanhToan &&
              refClickThanhToanFunc.current &&
              refClickThanhToanFunc.current();
            setOpenSelectPhuongThucTt(false);
            setOpenPrint(false);
          },
        },
        {
          keyCode: HOTKEY.F9,
          onEvent: () => {
            refBtnXacNhanBhyt.current?.click(true);
          },
        },
      ],
    });
  }, [
    openPrint,
    thanhToan,
    openSelectPhuongThucTt,
    showActions?.showBtnThanhToan,
  ]);

  useEffect(() => {
    if (
      isHienThiTongTienTheoDoiTuong &&
      doiTuongKcb != DOI_TUONG_KCB.NGOAI_TRU
    ) {
      setActiveKey([2, 3]);
    }
  }, [isHienThiTongTienTheoDoiTuong, doiTuongKcb]);

  useEffect(() => {
    if (expandTongTien) {
      setActiveKey([1, ...(activeKey || [])]);
    }
  }, [expandTongTien]);

  useEffect(() => {
    if (nbDotDieuTriId && phieuThuId) {
      getListPhieu({
        nbDotDieuTriId,
        chiDinhTuDichVuId: phieuThuId,
        ...maViTriManHinh,
      }).then((listPhieu) => {
        setListPhieu(listPhieu);
      });

      kiemTraQuyetToanBH(nbDotDieuTriId).then((res) => {
        setShowBtnQuyetToan(res || false);
      });
    }
  }, [phieuThuId]);

  useEffect(() => {
    if (openPrint && phieuThuId && nbDotDieuTriId) {
      getListPhieu({
        nbDotDieuTriId,
        chiDinhTuDichVuId: phieuThuId,
        ...maViTriManHinh,
      }).then((listPhieu) => {
        setListPhieu(listPhieu);
      });
    }
  }, [openPrint, phieuThuId, nbDotDieuTriId]);

  // Check showBtnQuyetToan cho TH chưa thanh toán
  useEffect(() => {
    const { trangThai } = thongTinPhieuThu || {};

    let tienBHYT = 0;
    if (isArray(listAllService, true)) {
      tienBHYT = listAllService.reduce(
        (acc, cur) => acc + (cur?.tienBhThanhToan || 0),
        0
      );
    }
    let isValid = false;
    if (
      [
        DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU,
        DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_BAN_NGAY,
        DOI_TUONG_KCB.DIEU_TRI_NOI_TRU,
      ].includes(doiTuongKcb)
    ) {
      //Với Đối tượng KCB = điều trị Nội trú, điều trị nội trú ban ngày, điều trị ngoại trú thì check thêm trạng thái nb = đã ra viện
      isValid = [
        TRANG_THAI_NB.DA_RA_VIEN,
        TRANG_THAI_NB.DA_DUYET_CHI_PHI,
        TRANG_THAI_NB.DA_DUYET_CHI_PHI_HEN_DIEU_TRI,
      ].includes(thongTinCoBan.trangThaiNb);
    } else {
      // Với Đối tượng KCB khác điều trị Nội trú, điều trị nội trú ban ngày, điều trị ngoại trú
      isValid = true;
    }

    if (
      thongTinCoBan.doiTuong === DOI_TUONG.BAO_HIEM && // Đối tượng của NB = BHYT
      isValid &&
      thanhToan !== TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN &&
      hienThiThanhToan &&
      tienBHYT > 0 && // Phiếu thu chưa thanh toán có dịch vụ hưởng BHYT
      checkRole([ROLES["THU_NGAN"].DAY_QUYET_TOAN]) &&
      trangThai == null // Mã hồ sơ chưa tạo hồ sơ XML 79A (trangThai = null)
    ) {
      setShowBtnQuyetToanChuaThanhToan(true);
    } else {
      setShowBtnQuyetToanChuaThanhToan(false);
    }
  }, [
    thongTinCoBan,
    thanhToan,
    doiTuongKcb,
    thongTinPhieuThu,
    listAllService,
    hienThiThanhToan,
    checkDayQuyetToanKhiThanhToan,
  ]);

  const isDaThanhToanTatCaPhieuThu = useMemo(() => {
    //Tất cả phiếu thu nb đã thanh toán (trừ phiếu thu nhà thuốc, thu ngoài, gói dv, khám sức khỏe)
    return (listDsPhieuThu || [])
      .filter(
        (item) =>
          ![
            LOAI_PHIEU_THU.NHA_THUOC,
            LOAI_PHIEU_THU.NGOAI_DIEU_TRI,
            LOAI_PHIEU_THU.GHI_DICH_VU,
            LOAI_PHIEU_THU.KHAM_SUC_KHOE,
          ].includes(item.loaiPhieuThu)
      )
      .every(
        (item) =>
          item.thanhToan === TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN
      );
  }, [listDsPhieuThu]);

  const showXacNhanBhyt = useMemo(() => {
    let result =
      thongTinCoBan.trangThaiXacNhanBaoHiem ===
        TRANG_THAI_XAC_NHAN_BHYT.CHUA_XAC_NHAN &&
      thongTinCoBan.doiTuong === DOI_TUONG.BAO_HIEM &&
      checkRole([ROLES["THU_NGAN"].XAC_NHAN_BHYT]);

    if (isDaThanhToanTatCaPhieuThu) {
      // Trường hợp NB BHYT không có thuốc BH, có phát sinh chi phí:
      // Sau khi đã thanh toán hết các phiếu thu thì cho ẩn nút Xác nhận BHYT ở MH thu ngân đi
      let valid = listAllService.some(
        (i) => i.loaiDichVu !== LOAI_DICH_VU.THUOC && i.tienBhThanhToan > 0
      );
      result = result && !valid;
    }
    return result;
  }, [thongTinCoBan, isDaThanhToanTatCaPhieuThu, listAllService]);

  useEffect(() => {
    const { trangThai } = thongTinCoBan || {};

    let tienBHYT = 0;
    if (isArray(listAllService, true)) {
      tienBHYT = listAllService.reduce(
        (acc, cur) => acc + (cur?.tienBhThanhToan || 0),
        0
      );
    }
    if (
      [
        TRANG_THAI_NB.DA_RA_VIEN,
        TRANG_THAI_NB.DA_THANH_TOAN_RA_VIEN,
        TRANG_THAI_NB.HEN_DIEU_TRI,
        TRANG_THAI_NB.DA_THANH_TOAN_HEN_DIEU_TRI,
      ].includes(trangThai) &&
      tienBHYT > 0
    ) {
      setShowDuyetBh(true);
    } else {
      setShowDuyetBh(false);
    }
  }, [thongTinCoBan, listAllService]);

  const showHuyDuyetChiPhi = useMemo(() => {
    return [
      TRANG_THAI_NB.DA_DUYET_CHI_PHI,
      TRANG_THAI_NB.DA_DUYET_CHI_PHI_HEN_DIEU_TRI,
    ].includes(thongTinCoBan.trangThaiNb);
  }, [thongTinCoBan]);

  const showDuyetChiPhi = useMemo(() => {
    return (
      [2, 3, 4].includes(thongTinPhieuThu.doiTuongKcb) &&
      loaiPhieuThu !== 5 &&
      [
        TRANG_THAI_NB.CHO_DUYET_CHI_PHI,
        TRANG_THAI_NB.CHO_DUYET_CHI_PHI_HEN_DIEU_TRI,
      ].includes(thongTinCoBan.trangThaiNb)
    );
  }, [thongTinCoBan, thongTinPhieuThu]);

  const kiemTraTrangThaiQrThanhToan = async () => {
    try {
      let params = {
        nbDotDieuTriId,
        loai: 20,
        tuBanGhiId: qrThanhToan?.id,
      };
      const res = await kiemTraTrangThaiThanhToanQr(params);
      const { data } = res || {};
      const message = data?.phanHoi?.message;
      if (data?.trangThai === TRANG_THAI_THANH_TOAN_QR.THANH_TOAN) {
        // Show popup thành công
        refTimeoutQr.current && clearInterval(refTimeoutQr.current);
        putDataCenter({
          ma: "QR_DATA",
          value: { ...qrThanhToan, trangThaiThanhToan: data.trangThai },
        });

        // ẩn popup qr xem qr
        refModalTaoQrCode.current && refModalTaoQrCode.current.hide();

        // show popup thanh toán qr thành công
        refModalThongBaoThanhToanQrCode.current &&
          refModalThongBaoThanhToanQrCode.current.show(
            {
              ...data,
              type: "success",
              title: t("thuNgan.thanhToanThanhCong"),
            },
            onThanhToanQrThanhCong
          );
      } else {
        const showWarningPopup = () => {
          refModalThongBaoThanhToanQrCode.current &&
            refModalThongBaoThanhToanQrCode.current.show({
              ...data,
              type: "warning",
              title: t("common.canhBao"),
            });
        };
        if (message === null) {
          refCurrentMsg.current = message;
        } else if (!!message) {
          if (
            refCurrentMsg.current === null &&
            refCurrentMsg.current !== message
          ) {
            // Show popup lỗi
            showWarningPopup();
          } else if (refCurrentMsg.current !== message) {
            // Show popup lỗi
            showWarningPopup();
          }
          refCurrentMsg.current = message;
        }
      }
    } catch (error) {}
  };

  useEffect(() => {
    if (
      qrThanhToan &&
      qrThanhToan.trangThaiThanhToan === TRANG_THAI_THANH_TOAN_QR.TAO_QR
    ) {
      kiemTraTrangThaiQrThanhToan();

      refTimeoutQr.current = setInterval(() => {
        kiemTraTrangThaiQrThanhToan();
      }, 3000);

      return () => {
        if (refTimeoutQr.current) clearInterval(refTimeoutQr.current);
      };
    }
  }, [qrThanhToan]);

  useEffect(() => {
    if (
      dataPhuongThucThanhToanMacDinh &&
      isArray(listAllPhuongThucThanhToan, true)
    ) {
      let isTienMat = listAllPhuongThucThanhToan.find(
        (i) => i.ma === dataPhuongThucThanhToanMacDinh
      )?.tienMat;
      if (
        refPtttChanged.current &&
        isTienMat &&
        isTuDongThanhToanPhieuThuTienMat &&
        showActions?.showBtnThanhToan &&
        phuongThucTtId &&
        ptttMacDinhTheoThietLap !== phuongThucTtId
      ) {
        onClickThanhToan(null);
        refPtttChanged.current = false;
      }
    }
  }, [
    showActions,
    ptttMacDinhTheoThietLap,
    phuongThucTtId,
    dataPhuongThucThanhToanMacDinh,
    listAllPhuongThucThanhToan,
    isTuDongThanhToanPhieuThuTienMat,
    refPtttChanged,
  ]);

  useEffect(() => {
    if (
      thanhToan !== TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN &&
      hienThiThanhToan &&
      isArray(listAllPhuongThucThanhToan, true) &&
      qrThanhToan &&
      [
        TRANG_THAI_THANH_TOAN_QR.MOI,
        TRANG_THAI_THANH_TOAN_QR.TAO_QR,
        TRANG_THAI_THANH_TOAN_QR.TAO_QR_LOI,
      ].includes(qrThanhToan.trangThaiThanhToan) &&
      refPtttChanged.current
    ) {
      let notQrcode =
        listAllPhuongThucThanhToan.find((i) => i.id === phuongThucTtId)
          ?.loaiPhuongThucTt !== LOAI_PHUONG_THUC_TT.QR_CODE;
      if (notQrcode) {
        onClickThanhToan(null, { isHuyQrCode: true });
        refPtttChanged.current = false;
      }
    }
  }, [
    phuongThucTtId,
    listAllPhuongThucThanhToan,
    refPtttChanged,
    qrThanhToan,
    thanhToan,
    hienThiThanhToan,
  ]);

  const onTaoLaiQrThanhToanLoi = async ({
    nbDotDieuTriId,
    loai,
    bodyThanhToan,
  }) => {
    try {
      if (!bodyThanhToan) {
        throw new Error("");
      }
      showLoading();
      const res = await thanhToanPhieuThu(bodyThanhToan);
      let params = { nbDotDieuTriId, loai };
      hideLoading();
      if (isArray(res._dsPtt, true)) {
        const dsPtt = res._dsPtt.find(
          (i) => i.phuongThucTt.loaiPhuongThucTt === LOAI_PHUONG_THUC_TT.QR_CODE
        );
        if (dsPtt) {
          if (dsPtt.tongTien > 0 && dsPtt.nganHangId) {
            Object.assign(params, { tuBanGhiId: dsPtt.id });
          }
        }
      }
      if (params.tuBanGhiId) {
        // setTimeout vì hideLoading cần thời gian  300 để ẩn
        setTimeout(() => {
          onTaoQrThanhToan({ ...params, bodyThanhToan });
        }, 301);
      } else {
        handleAfterSubmit();
      }
    } catch (error) {
      console.error(error);
      hideLoading();
    }
  };

  const onTaoQrThanhToan = ({
    nbDotDieuTriId,
    loai,
    tuBanGhiId,
    bodyThanhToan,
  }) => {
    showLoading({ title: t("thuNgan.dangGenQrCode"), width: 300 });
    taoQrThanhToan({ nbDotDieuTriId, loai, tuBanGhiId })
      .then((res) => {
        const { trangThai, qr, phanHoi } = res?.data || {};
        hideLoading();
        handleAfterSubmit();
        if (phanHoi && phanHoi.code !== "00") {
          refModalTaoQrCodeLoi.current &&
            refModalTaoQrCodeLoi.current.show(
              { message: phanHoi?.message, isPhieuThu: true },
              () => {
                onTaoLaiQrThanhToanLoi({ nbDotDieuTriId, loai, bodyThanhToan });
              },
              () => {}
            );
        } else {
          if (
            [
              TRANG_THAI_THANH_TOAN_QR.MOI,
              TRANG_THAI_THANH_TOAN_QR.TAO_QR,
              TRANG_THAI_THANH_TOAN_QR.THANH_TOAN,
            ].includes(trangThai)
          ) {
            if (thanhToan !== TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN) {
              putDataCenter({ ma: "QR_DATA", value: res?.data });
            }

            refModalTaoQrCode.current &&
              refModalTaoQrCode.current.show({ qrData: qr }, () => {
                if (
                  thanhToan !== TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN
                ) {
                  putDataCenter({ ma: "QR_DATA", value: null });
                }
                if (refTimeout.current) {
                  clearInterval(refTimeout.current);
                }
              });
          } else if (TRANG_THAI_THANH_TOAN_QR.TAO_QR_LOI === trangThai) {
            refModalTaoQrCodeLoi.current &&
              refModalTaoQrCodeLoi.current.show(
                { isPhieuThu: true },
                () => {
                  onTaoLaiQrThanhToanLoi({
                    nbDotDieuTriId,
                    loai,
                    bodyThanhToan,
                  });
                },
                () => {}
              );
          }
        }
      })
      .catch((err) => {
        hideLoading();
        refModalTaoQrCodeLoi.current &&
          refModalTaoQrCodeLoi.current.show(
            { message: err?.message, isPhieuThu: true },
            () => {
              onTaoLaiQrThanhToanLoi({ nbDotDieuTriId, loai, bodyThanhToan });
            },
            () => {}
          );
      });
  };

  const onXemQrCode = () => {
    const { qrThanhToan: qrData } = qrThanhToan;

    if (thanhToan !== TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN) {
      putDataCenter({ ma: "QR_DATA", value: qrThanhToan });
    }

    refModalTaoQrCode.current &&
      refModalTaoQrCode.current.show({ qrData }, () => {
        if (thanhToan !== TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN) {
          putDataCenter({ ma: "QR_DATA", value: null });
        }
        if (refTimeout.current) {
          clearInterval(refTimeout.current);
        }
      });
  };

  const onHuyQrCode = () => {
    const { thanhToanId, phieuThuId } = qrThanhToan || {};
    showConfirm(
      {
        title: "",
        content: `${t("thuNgan.quanLyTamUng.xacNhanHuyQrCode")}`,
        cancelText: t("common.quayLai"),
        okText: t("common.xacNhan"),
        typeModal: "warning",
        showBtnOk: true,
      },
      () => {
        showLoading();
        huyQrThanhToan({ thanhToanId, phieuThuId })
          .then(() => {
            putDataCenter({ ma: "QR_DATA", value: null });
            handleAfterSubmit();
          })
          .finally(() => {
            hideLoading();
          });
      },
      () => {}
    );
  };

  const onTuDongInBangKe6556 = () => {
    return new Promise(async (resolve, reject) => {
      const timeout = setTimeout(() => {
        resolve();
      }, 3000);

      const dsPhieuTuDongInBangKe = listPhieu.filter((i) =>
        dsMaBaoCaoTuDongInBangKe6556.includes(i.maBaoCao)
      );
      const maPhieuP062 = listPhieu.find((i) => i.ma === "P062");
      if (dsPhieuTuDongInBangKe.length > 0) {
        await getThongTinCoBan(nbDotDieuTriId);

        if (
          thongTinCoBan.trangThaiNb >= TRANG_THAI_NB.DA_RA_VIEN &&
          [
            DOI_TUONG_KCB.NGOAI_TRU,
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
            DOI_TUONG_KCB.NHAN_THUOC_THEO_HEN,
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
            DOI_TUONG_KCB.CAC_TRUONG_HOP_KHAC,
          ].includes(thongTinCoBan.doiTuongKcb) &&
          thongTinCoBan.doiTuong === DOI_TUONG.BAO_HIEM
        ) {
          await Promise.all(
            dsPhieuTuDongInBangKe.map(
              async (item) => await onPrintPhieu(item)()
            )
          );
        }
      } else if (
        maPhieuP062 &&
        dataBAM_THANH_TOAN_TU_DONG_BAT_BANG_KE_6556?.eval() &&
        thongTinCoBan.trangThaiNb >= TRANG_THAI_NB.DA_RA_VIEN &&
        [
          DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU,
          DOI_TUONG_KCB.DIEU_TRI_NOI_TRU,
          DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_BAN_NGAY,
          DOI_TUONG_KCB.DIEU_TRI_LUU_TRU_TAI_TRAM_Y_TE_TUYEN_XA,
          DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_DUOI_BON_GIO,
        ].includes(thongTinCoBan.doiTuongKcb)
      ) {
        await onPrintPhieu(maPhieuP062)();
      }
      clearTimeout(timeout);
      resolve();
    });
  };

  const onThanhToanPhieuThu = async (
    boQuaChuaKetLuanKham,
    isDuyetChiPhi,
    isDuyetPhieuThu,
    boQuaTheLoi,
    isHuyQrCode
  ) => {
    const refreshListPhieu = () => {
      getListPhieu({
        nbDotDieuTriId,
        chiDinhTuDichVuId: phieuThuId,
        ...maViTriManHinh,
      }).then((listPhieu) => {
        setListPhieu(listPhieu);
        setShowBtnQuyetToanChuaThanhToan(false);
      });
    };
    //Check NB có Số TK # null + số tiền trả lại > 0 (hoặc tiền tạm ứng > tiền phiếu thu)
    let forceMoPopupPTTT =
      !!thongTinCoBan?.soTaiKhoan && tienTamUng > thanhTien; //luôn mở popup PTTT

    if (
      !forceMoPopupPTTT &&
      isTuDongThanhToanPhieuThuTienMat &&
      phuongThucTtId === ptttMacDinhTheoThietLap &&
      !isDuyetPhieuThu &&
      !isDuyetChiPhi &&
      !isHuyQrCode
    ) {
      try {
        showLoading();
        const hoanUng =
          tienTamUng > 0 &&
          ![LOAI_PHIEU_THU.NGOAI_DIEU_TRI, LOAI_PHIEU_THU.NHA_THUOC].includes(
            loaiPhieuThu
          );
        const dsPhuongThucTt = listAllPhuongThucThanhToan.reduce((a, c) => {
          if (c.id === phuongThucTtId)
            return [{ phuongThucTtId: c.id, tongTien: thanhTien }];
          return [...a];
        }, []);
        if (dataIN_BANG_KE_TRUOC_THANH_TOAN?.eval())
          await onTuDongInBangKe6556();
        const isNoiTru = [
          DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU,
          DOI_TUONG_KCB.DIEU_TRI_NOI_TRU,
          DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_BAN_NGAY,
          DOI_TUONG_KCB.DIEU_TRI_LUU_TRU_TAI_TRAM_Y_TE_TUYEN_XA,
          DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_DUOI_BON_GIO,
        ].includes(doiTuongKcb);
        const isHoanUng = hoanUng && tienTamUng >= thanhTien;
        const resThanhToan = await thanhToanPhieuThu({
          id: phieuThuId,
          dsPhuongThucTt,
          nbDotDieuTriId: nbDotDieuTriId,
          nhaThuNganId: nhaTamUngId,
          quayId: quayId,
          caLamViecId: caLamViecId,
          tongTien: thanhTien,
          hoanUng: dataTU_DONG_CHECK_CO_HOAN_TAM_UNG?.eval()
            ? state.isTamUng
              ? true
              : null
            : hoanUng,
          inPhieuThuKhiThanhToan: isInPhieuThuKhiThanhToan, //bỏ điều kiện hoàn ứng
          // inPhieuThuKhiThanhToan:
          //   isInPhieuThuKhiThanhToan && (isNoiTru ? !isHoanUng : true),
          nbLaySoId:
            nbDotDieuTriId == nbDangThucHien?.nbDotDieuTriId
              ? nbDangThucHien?.id
              : null,
          boQuaChuaKetLuanKham,
          boQuaTheLoi,
          sinhPhieuThuTamUng: state.sinhPhieuThuTamUng,
          inPhieuBienNhanKhiThanhToan:
            isNgoaiTruNhieuPhieuThuBH &&
            [
              DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU,
              DOI_TUONG_KCB.DIEU_TRI_NOI_TRU,
              DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_BAN_NGAY,
              DOI_TUONG_KCB.DIEU_TRI_LUU_TRU_TAI_TRAM_Y_TE_TUYEN_XA,
              DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_DUOI_BON_GIO,
            ].includes(doiTuongKcb) &&
            isHoanUng,
          isPhatHanhHoaDonKhiThanhToanNgoaiTru,
          tienNbDua: thanhTien,
          onPrintDialogClose: () => {
            document.getElementById("input-search").focus();
          },
          dataDinhDangXemHoaDon,
          callback: () => {
            if (!dataIN_BANG_KE_TRUOC_THANH_TOAN?.eval())
              onTuDongInBangKe6556();
          },
        });

        if ((resThanhToan?.data?.[0]?.message || "").indexOf(MESS_WARN) >= 0) {
          showConfirm(
            {
              title: t("common.thongBao"),
              content: `${resThanhToan?.data?.[0]?.message || ""}`,
              cancelText: t("common.dong"),
              typeModal: "warning",
              showImg: true,
              classNameOkText: "button-warning",
            },
            () => {}
          );
        } else if (resThanhToan?.data?.[0]?.message) {
          message.error(resThanhToan?.data?.[0]?.message);
        }
        //get lại ds phiếu sau khi thanh toán
        refreshListPhieu();
        onFocusSearch();
        kiemTraQuyetToanBH(nbDotDieuTriId).then(async (res) => {
          if (
            checkDayQuyetToanKhiThanhToan(thongTinCoBan.doiTuong) &&
            loaiPhieuThu !== LOAI_PHIEU_THU.NHA_THUOC &&
            res
          ) {
            //chờ get lại thông tin phiếu thu để hiển thị nút quyết toán
            await getThongTinPhieuThu(phieuThuId);
            setTimeout(() => {
              if (loaiQuyetToan == "130") {
                refClickQuyetToan130.current &&
                  refClickQuyetToan130.current.click();
              } else {
                refClickQuyetToan.current && refClickQuyetToan.current.click();
              }
            }, 500);
          } else {
            setShowBtnQuyetToan(res || false);
          }
        });
        handleAfterSubmit({ isThanhToan: true });
      } catch (error) {
        console.log(error);
      } finally {
        hideLoading();
      }
    } else {
      let phuongThucMacDinhId = isTuDongThanhToanPhieuThuTienMat
        ? phuongThucTtId
        : null;
      let _nganHangIdTamUng = null;
      let _phuongThucTtIdTamUng = null;

      if (forceMoPopupPTTT) {
        const ptttCK = listAllPhuongThucThanhToan.find(
          (x) => x?.ma == dataMA_PTTT_CHUYEN_KHOAN
        );
        if (ptttCK) {
          _phuongThucTtIdTamUng = ptttCK.id;
          _nganHangIdTamUng = (ptttCK?.dsNhaCungCap || []).find(
            (x) => x.ma == dataMA_DOI_TAC_HOAN_TIEN
          )?.id;
        }
      }

      refModalPhuongThucThanhToan.current &&
        refModalPhuongThucThanhToan.current.show(
          {
            tongTien: thanhTien,
            phieuThuId,
            nbDotDieuTriId,
            nbThongTinId: thongTinCoBan.nbThongTinId,
            maChuanChi,
            nhaTamUngId,
            quayId,
            caLamViecId,
            loaiPhieuThu,
            doiTuongKcb,
            phuongThucMacDinhId,
            boQuaChuaKetLuanKham,
            boQuaTheLoi,
            sinhPhieuThuTamUng: state.sinhPhieuThuTamUng,
            isDuyetChiPhi,
            isDuyetPhieuThu,
            dsPhuongThucTt: thongTinPhieuThu?.dsPhuongThucTt,
            hoanUng: thongTinPhieuThu?.hoanUng,
            onPrintDialogClose: () => {
              document.getElementById("input-search").focus();
            },
            beforeProcessing: async () => {
              if (dataIN_BANG_KE_TRUOC_THANH_TOAN?.eval())
                await onTuDongInBangKe6556();
            },
            afterProcessing: () => {
              if (!dataIN_BANG_KE_TRUOC_THANH_TOAN?.eval())
                onTuDongInBangKe6556();
            },
            phuongThucTtIdTamUng: _phuongThucTtIdTamUng,
            nganHangIdTamUng: _nganHangIdTamUng,
            isHuyQrCode,
            qrThanhToan,
          },
          ({ tongTien, tienDaNhan, dsPhuongThucTt, hoanUng, isQrCode }) => {
            //get lại ds phiếu sau khi thanh toán
            refreshListPhieu();
            if (tongTien <= tienDaNhan) {
              kiemTraQuyetToanBH(nbDotDieuTriId).then(async (res) => {
                if (
                  checkDayQuyetToanKhiThanhToan(thongTinCoBan.doiTuong) &&
                  loaiPhieuThu !== LOAI_PHIEU_THU.NHA_THUOC &&
                  res
                ) {
                  //chờ get lại thông tin phiếu thu để hiển thị nút quyết toán
                  await getThongTinPhieuThu(phieuThuId);
                  setTimeout(() => {
                    if (loaiQuyetToan == "130") {
                      refClickQuyetToan130.current &&
                        refClickQuyetToan130.current.click();
                    } else {
                      refClickQuyetToan.current &&
                        refClickQuyetToan.current.click();
                    }
                  }, 500);
                } else {
                  setShowBtnQuyetToan(res || false);
                }
              });
              if (!isQrCode) handleAfterSubmit({ isThanhToan: true });
            } else {
              refModalDichVuThanhToan.current &&
                refModalDichVuThanhToan.current.show(
                  {
                    phieuThuId,
                    nbDotDieuTriId,
                    tienDaNhan,
                    dsPhuongThucTt,
                    hoanUng,
                  },
                  () => {
                    //get lại ds phiếu sau khi thanh toán
                    refreshListPhieu();
                  }
                );
            }
          },
          ({ nbDotDieuTriId, loai, tuBanGhiId, bodyThanhToan }) => {
            if (!isDuyetPhieuThu) {
              setTimeout(() => {
                onTaoQrThanhToan({
                  nbDotDieuTriId,
                  loai,
                  tuBanGhiId,
                  bodyThanhToan,
                });
              }, 301);
            }
          }
        );
    }
  };
  const kiemTraDongHoSo = (
    isDuyetChiPhi,
    isDuyetPhieuThu,
    boQuaTheLoi,
    isHuyQrCode
  ) => {
    if (isCanhBaoChuaDongHoSoKhamKhiThanhToan) {
      if (
        doiTuongKcb === DOI_TUONG_KCB.NGOAI_TRU &&
        listDv?.some(
          (o) =>
            o.loaiDichVu === LOAI_DICH_VU.KHAM &&
            o.trangThai !== TRANG_THAI_DICH_VU.DA_KET_LUAN
        )
      ) {
        showConfirm(
          {
            title: t("common.xacNhan"),
            content: t("thuNgan.nguoiBenhchuaKetThucKhamVuiLongKiemTraLai"),
            cancelText: t("common.khongThanhToan"),
            okText: t("common.thanhToan"),
            showBtnOk: true,
          },
          () => {
            onThanhToanPhieuThu(
              true,
              isDuyetChiPhi,
              isDuyetPhieuThu,
              boQuaTheLoi,
              isHuyQrCode
            );
          },
          () => {}
        );
      } else {
        onThanhToanPhieuThu(
          true,
          isDuyetChiPhi,
          isDuyetPhieuThu,
          boQuaTheLoi,
          isHuyQrCode
        );
      }
    } else {
      onThanhToanPhieuThu(
        false,
        isDuyetChiPhi,
        isDuyetPhieuThu,
        boQuaTheLoi,
        isHuyQrCode
      );
    }
  };
  const onOpenChange = (fn) => (isOpen) => {
    fn(isOpen);
  };

  const onConfirm = (callback) =>
    showConfirm(
      {
        title: t("common.canhBao"),
        content: `${t("kho.donThuocChuaDuocDuyetLamSang")}.\r\n${t(
          "kho.tiepTucThanhToan"
        )}?`,
        cancelText: t("common.dong"),
        okText: t("common.xacNhan"),
        showBtnOk: true,
        typeModal: "warning",
        classNameOkText: "button-warning",
      },
      () => {
        callback();
      },
      () => {}
    );

  const columnsTable = [
    {
      title: <HeaderSearchTable title={t("thuNgan.truongThongTin")} />,
      width: 100,
      dataIndex: "title",
      key: "title",
    },
    {
      title: <HeaderSearchTable title={t("baoCao.nguoiBenh")} />,
      width: 150,
      dataIndex: "nguoiBenh",
      key: "nguoiBenh",
    },
    {
      title: <HeaderSearchTable title={t("thuNgan.congBaoHiem")} />,
      width: 150,
      dataIndex: "congBh",
      key: "congBh",
    },
  ];
  const onConfirmThanhToan = (showBtnOk, data, callback) => {
    let content = (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          padding: "0 16px",
          gap: "16px",
        }}
      >
        <div
          className="content content-2"
          style={{ paddingBottom: 0, color: "red" }}
        >
          {t("thuNgan.nguoiBenhBhytCoThongTinSaiCanKiemTraLai")}
        </div>
        <div>
          <b>{data.maKetQua}</b> - {data.ghiChu}
        </div>
        {showBtnOk && (
          <div style={{ color: "red", fontWeight: 500 }}>
            {t("thuNgan.vanTiepTucThanhToan")}
          </div>
        )}
      </div>
    );
    if (data.code === "err_custom") {
      content = (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            padding: "0 16px",
          }}
        >
          <div
            className="content content-2"
            style={{ color: "red" }}
            dangerouslySetInnerHTML={{
              __html: `${t(
                "thuNgan.thongTinNbChuaKhopVoiCongOCacTruongBenDuoi"
              )} ${showBtnOk ? `${t("thuNgan.vanTiepTucThanhToan")}` : ""}`,
            }}
          />
          <TableWrapper columns={columnsTable} dataSource={data.errors || []} />
        </div>
      );
    }
    showConfirm(
      {
        title: t("common.canhBao"),
        content,
        cancelText: t(showBtnOk ? "common.huy" : "common.dong"),
        okText: t("common.xacNhan"),
        showBtnOk,
        isContentElement: true,
        typeModal: "warning",
        classNameOkText: "button-warning",
        ...(data.code === "err_custom" && { width: 768 }),
      },
      () => {
        callback(true);
      }
    );
  };

  const onClickThanhToan = (
    e,
    { isDuyetChiPhi, isDuyetPhieuThu, isHuyQrCode } = {}
  ) => {
    const func = (boQuaTheLoi) => {
      if (
        thanhToan !== TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN &&
        isCanhBaoChuaThanhToanManThuNgan &&
        locationState?.khongCanhBao != true
      ) {
        showLoading();
        onSearch({
          nbDotDieuTriId: "",
          dataSearch: {
            maNb: thongTinCoBan?.maNb,
            thanhToan: -1,
            dsLoaiPhieuThu: [0, 1, 2, 3],
          },
          noUpdateData: true,
        })
          .then((res) => {
            if (res.data) {
              const phieuThu = res.data.filter(
                (i) => i.id != phieuThuId && i.nbDotDieuTriId != nbDotDieuTriId
              );
              if (phieuThu.length > 0) {
                showConfirm(
                  {
                    title: t("common.xacNhan"),
                    content: t("thuNgan.benhNhanChuaHoanTatThanhToanTuNgay", {
                      date:
                        phieuThu[0].thoiGianVaoVien &&
                        moment(phieuThu[0].thoiGianVaoVien).format(
                          "DD/MM/YYYY"
                        ),
                    }),
                    subContent: contentMsgConfirm[i18n?.language || "vi"],
                    showBtnOk: true,
                  },
                  () => {
                    kiemTraDongHoSo(
                      false,
                      isDuyetPhieuThu,
                      boQuaTheLoi,
                      isHuyQrCode
                    );
                  },
                  () => {
                    history.push({
                      pathname: `/thu-ngan/chi-tiet-phieu-thu/${phieuThu[0].maHoSo}/${phieuThu[0].id}/${phieuThu[0].nbDotDieuTriId}`,
                      state: { khongCanhBao: true },
                    });
                  }
                );
              } else {
                kiemTraDongHoSo(
                  isDuyetChiPhi,
                  isDuyetPhieuThu,
                  boQuaTheLoi,
                  isHuyQrCode
                );
              }
            } else {
              kiemTraDongHoSo(
                isDuyetChiPhi,
                isDuyetPhieuThu,
                boQuaTheLoi,
                isHuyQrCode
              );
            }
          })
          .finally(() => {
            hideLoading();
          });
      } else {
        kiemTraDongHoSo(
          isDuyetChiPhi,
          isDuyetPhieuThu,
          boQuaTheLoi,
          isHuyQrCode
        );
      }
    };
    const onCheckTheBhyt = () => {
      const {
        doiTuong,
        tenNb,
        ngaySinh,
        maTheBhyt,
        tuNgayTheBhyt,
        denNgayTheBhyt,
        diaChi,
        gioiTinh,
        maNoiDangKy,
      } = thongTinCoBan || {};
      const { theTam } = thongTinCoBan || {};
      const role = checkRole([
        ROLES["THU_NGAN"].THANH_TOAN_PHIEU_THU_CUA_NBYT_CO_THONG_TIN_THE_SAI,
      ]);
      if (
        doiTuong === DOI_TUONG.BAO_HIEM &&
        [true, null, undefined].includes(dataKiemTraTheBaoHiem) &&
        !theTam
      ) {
        const onCheckThongTinTheBh = (dataBaoHiem) => {
          const format = "DD/MM/YYYY";
          let tuNgay = moment(tuNgayTheBhyt).format(format);
          let denNgay = moment(denNgayTheBhyt).format(format);
          const {
            gtTheTu,
            gtTheDen,
            gioiTinh: congBhGioiTinh,
            maDKBD,
          } = dataBaoHiem || {};
          let diffTuNgay = moment(gtTheTu, format).diff(
            moment(tuNgay, format),
            "days"
          );
          let diffDenNgay = moment(gtTheDen, format).diff(
            moment(denNgay, format),
            "days"
          );
          const nbGioiTinh = listGioiTinh.find((i) => i.id === gioiTinh)?.ten;
          let obj = {
            tuNgay: {
              nguoiBenh: tuNgay,
              congBh: gtTheTu,
              condition: gtTheTu && diffTuNgay !== 0,
              title: t("thuNgan.giaTriTheTu"),
            },
            denNgay: {
              nguoiBenh: denNgay,
              congBh: gtTheDen,
              condition: gtTheDen && diffDenNgay !== 0,
              title: t("thuNgan.giaTriTheDen"),
            },
            gioiTinh: {
              nguoiBenh: nbGioiTinh,
              congBh: congBhGioiTinh,
              condition: congBhGioiTinh && congBhGioiTinh !== nbGioiTinh,
              title: t("common.gioiTinh"),
            },
            maNoiDangKy: {
              nguoiBenh: maNoiDangKy,
              congBh: maDKBD,
              condition: maDKBD && maNoiDangKy !== maDKBD,
              title: t("tiepDon.noiDangKy"),
            },
          };
          let errors = [];
          Object.entries(obj).forEach(([key, value]) => {
            if (value.condition) {
              errors.push({
                key,
                title: value.title,
                nguoiBenh: value.nguoiBenh,
                congBh: value.congBh,
              });
            }
          });

          return errors;
        };
        showLoading({ title: t("tiepDon.dangKiemTraTheBHYT"), width: 500 });
        giamDinhThe({
          ngaySinh: moment(ngaySinh).format("DD/MM/YYYY"),
          maThe: maTheBhyt,
          hoTen: tenNb,
          diaChi,
          khoaId: thongTinCoBan.khoaNbId,
        })
          .then((res) => {
            if (
              (res.code === 0 && res.data) ||
              dataMaLoiBHDuocThanhToan.split(",").includes(res.data?.maKetQua)
            ) {
              hideLoading();
              const errors = onCheckThongTinTheBh(res.data);
              if (isArray(errors, true)) {
                let _role = role || ["001", "002"].includes(res.data?.maKetQua);
                onConfirmThanhToan(_role, { errors, code: "err_custom" }, func);
              } else {
                setTimeout(func, 301);
              }
            } else {
              hideLoading();
              onConfirmThanhToan(role, res.data, func);
            }
          })
          .catch((err) => {
            hideLoading();
          });
      } else {
        func(dataKiemTraTheBaoHiem === false && role);
      }
    };
    if (
      isCanhBaoDuyetDLSKhiPhatThuocTaoPhieuLinh &&
      isPhatThuocNhaThuocKhiThanhToan &&
      listDv?.some((o) => [10, 15, 22, 25].includes(o.trangThaiDuocLamSang))
    ) {
      onConfirm(onCheckTheBhyt);
    } else {
      onCheckTheBhyt();
    }
  };

  refClickThanhToanFunc.current = onClickThanhToan;

  const onHuyThanhToan = (huyHoanUng, huyHoaDon, lyDo) => {
    showLoading();
    huyThanhToan({ id: phieuThuId, huyHoanUng, huyHoaDon, lyDo })
      .then(async () => {
        handleAfterSubmit();
        const _dsHoaDon = await getDanhSachHoaDon({
          maHoSo: thongTinCoBan.maHoSo,
          page: "",
          size: "",
        });
        if (
          !checkRole([ROLES["THU_NGAN"].HUY_THANH_TOAN_KHONG_HUY_HOA_DON]) &&
          _dsHoaDon?.find((x) => x.trangThai === 30)
        ) {
          message.error(t("thuNgan.huyBoHoaDonKhongThanhCong"));
        }
      })
      .catch((s) => {
        let cancelText = t("common.quayLai");
        let okText;
        let content = s?.message;
        let showBtnOk = false;
        let onOk = () => {
          hideLoading();
        };
        if (isHuyHoaDonKhiHuyThanhToanPT && s?.code === 9214) {
          content = `${content}. ${t("thuNgan.banCoMuonXoaHoaDonDaPhatHanh")}`;
          refNhapLyDoHuyThanhToan.current &&
            refNhapLyDoHuyThanhToan.current.show(
              { content, phieuThuId: phieuThuId },
              () => {
                hideLoading();
                handleAfterSubmit();
              }
            );
        } else {
          showConfirm(
            {
              title: t("common.thongBao"),
              typeModal: "warning",
              cancelText,
              okText,
              content,
              showBtnOk,
            },
            onOk
          );
        }
      })
      .finally(() => {
        hideLoading();
      });
  };

  const onClickHuyThanhToan = (huyHoanUng) => {
    refModalXacNhanHuyThanhToan.current &&
      refModalXacNhanHuyThanhToan.current.show(
        { huyHoanUng },
        ({ huyHoanUng: _huyHoanUng, huyHoaDon, lyDo }) => {
          onHuyThanhToan(_huyHoanUng, huyHoaDon, lyDo);
        }
      );
  };

  const onClickXuatHoaDonNhap = () => {
    showLoading();
    xuatHoaDonNhap({ dsPhieuThuId: [phieuThuId] })
      .then((s) => {
        const blob = new Blob([new Uint8Array(s)], {
          type: "application/pdf",
        });
        const blobUrl = window.URL.createObjectURL(blob);
        printJS({ printable: blobUrl, type: "pdf" });

        getThongTinPhieuThu(phieuThuId);
      })
      .finally(() => {
        hideLoading();
      });
  };

  const onActionBaoHiem = (isDuyet) => async () => {
    if (thongTinCoBan.doiTuong === DOI_TUONG.BAO_HIEM) {
      showLoading();
      giamDinhThe({
        ngaySinh: moment(thongTinCoBan?.ngaySinh).format("DD/MM/YYYY"),
        maThe: thongTinCoBan?.maTheBhyt,
        hoTen: thongTinCoBan?.tenNb,
      })
        .then(async (res) => {
          const errors = onCheckThongTinTheBh({
            thongTinCoBan,
            dataBaoHiem: res.data,
            listGioiTinh,
          });
          if (errors?.length) {
            hideLoading();
            const content = (
              <div>
                <div style={{ color: "red", paddingBottom: "10px" }}>
                  {t("common.thongTinBaoHiemCuaNguoiBenhDangKhacTrenCong")}{" "}
                </div>
                <TableWrapper
                  columns={columnsTable}
                  dataSource={errors || []}
                />
              </div>
            );
            showConfirm({
              title: t("common.thongBao"),
              content: content,
              cancelText: t("common.huy"),
              classNameOkText: "button-warning",
              typeModal: "warning",
              isContentElement: true,
              width: 768,
            });
          } else {
            try {
              isDuyet
                ? await duyetBaoHiem(nbDotDieuTriId)
                : await huyDuyetBaoHiem(nbDotDieuTriId);
              getChiTietDuyetBH(nbDotDieuTriId, true);
              getThongTinCoBan(nbDotDieuTriId);
            } finally {
              hideLoading();
            }
          }
        })
        .catch((err) => {
          hideLoading();
        });
    } else {
      showLoading();
      try {
        isDuyet
          ? await duyetBaoHiem(nbDotDieuTriId)
          : await huyDuyetBaoHiem(nbDotDieuTriId);
        getChiTietDuyetBH(nbDotDieuTriId, true);
        getThongTinCoBan(nbDotDieuTriId);
      } finally {
        hideLoading();
      }
    }
  };

  const onClickQuyetToan = (isXml130) => () => {
    const onTaoHoSo = (payload) => {
      showLoading();
      taoDayHoSo({
        nbDotDieuTriId,
        ...payload,
        ...(isXml130 ? { xml130: true } : {}),
      })
        .then(() => {
          setTimeout(() => {
            hideLoading();
            history.go();
          }, 2000);
        })
        .catch(hideLoading);
    };

    //show modal cho th da thanh toan
    const showModalChonThoiGianTaoHoSo = () => {
      const thoiGianRaVien = get(
        getState(),
        "nbDotDieuTri.thongTinCoBan.thoiGianRaVien"
      );

      if (
        (isChoChonLaiNgayTaoXMLKhiNgayTaoKhacNgayRa &&
          !(thoiGianRaVien && moment().isSame(thoiGianRaVien, "day"))) ||
        !(thoiGianRaVien && moment().isSame(thoiGianRaVien, "month"))
      ) {
        refModalDayQuyetToanBYHT.current &&
          refModalDayQuyetToanBYHT.current.show({}, (values) => {
            onTaoHoSo(values);
          });
      } else {
        onTaoHoSo();
      }
    };

    const renderConfirmOptions = (data) => {
      return {
        title: t("common.canhBao"),
        cancelText: t("common.dong"),
        okText: t("common.xacNhan"),
        showBtnOk: true,
        typeModal: "warning",
        classNameOkText: "button-warning",
        showIconContent: false,
        isContentElement: true,
        content: (
          <div>
            <span
              style={{ padding: "0 24px 12px", display: "block" }}
              dangerouslySetInnerHTML={{
                __html: DOMPurify.sanitize(
                  t("quyetToanBhyt.messageCanhBaoTaoHoSo", {
                    maHoSo: data.maHoSo,
                    hoTen: data.tenNb,
                    ngayRaVien:
                      data.ngayRa && moment(data.ngayRa).format("DD/MM/YYYY"),
                    text: data.text,
                  })
                ),
              }}
            ></span>
            {data.thanhToan && (
              <FormCanhBaoTaoHoSo
                data={{
                  NAM_QT: data?.namQt,
                  THANG_QT: data?.thangQt,
                  thoiGianTaoHoSo: moment(),
                }}
                ref={refCanhBaoTaoHoSoQuyetToan}
              />
            )}
          </div>
        ),
      };
    };

    const _thanhToan =
      thanhToan === TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN;

    if (!_thanhToan) {
      refModalDayQuyetToanBYHT.current &&
        refModalDayQuyetToanBYHT.current.show({}, (values) => {
          onTaoHoSo(values);
        });
      return;
    }

    // Case còn lại là đã thanh toán
    if (isCheckThoiGianQuyetToanHSXMLVoiNgayRaVien) {
      // TH đã thanh toán và không có thiết lập dataCHECK_THOI_GIAN_QUYET_TOAN_HS_XML_VOI_NGAY_RA_VIEN
      // thì check co thiet lap  dataCHO_CHON_LAI_NGAY_TAO_XML_KHI_NGAY_TAO_KHAC_NGAY_RA thì show popup chọn ngày tạo xml
      showModalChonThoiGianTaoHoSo();
      return;
    }
    // /dataCHECK_THOI_GIAN_QUYET_TOAN_HS_XML_VOI_NGAY_RA_VIEN ===true && thanhToan
    kiemTraThoiGianQuyetToan({ nbDotDieuTriId }).then((res) => {
      const data = res?.[0];

      // nếu có data thì BE trả về dữ liệu thì show pop up , ngược lại đẩy hồ sơ luôn
      if (data) {
        const month = moment(data.ngayRa).month() + 1;
        const year = moment(data.ngayRa).year();
        showConfirm(
          renderConfirmOptions({
            ...data,
            text:
              month != data.thangQt
                ? year != data.namQt
                  ? "THANG_QT/NAM_QT"
                  : "THANG_QT"
                : "NAM_QT",
          }),
          async () => {
            const values = await refCanhBaoTaoHoSoQuyetToan.current?.onSubmit();
            onTaoHoSo(values);
          }
        );
      } else {
        showModalChonThoiGianTaoHoSo();
      }
    });
  };

  const onClickHuyDuyetChiPhi = async () => {
    try {
      showLoading();

      await huyDuyetChiPhi(nbDotDieuTriId);

      getThongTinCoBan(nbDotDieuTriId);
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onClickDuyetChiPhi = async () => {
    try {
      showLoading();

      await duyetChiPhi(nbDotDieuTriId);
      const res = await getThongTinCoBan(nbDotDieuTriId);
      if (res.data.tienConLai < 0) {
        onClickThanhToan(null, { isDuyetChiPhi: true });
      }
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onClickTuChoiDuyetChiPhi = async () => {
    refModalNhapLyDoTuChoiDuyetChiPhi.current &&
      refModalNhapLyDoTuChoiDuyetChiPhi.current.show({}, () => {
        getThongTinCoBan(nbDotDieuTriId);
      });
  };

  const onClickHoaDonChuyenDoi = async () => {
    showLoading();
    try {
      await inHoaDon({
        hoaDonId: (dsHoaDon || [])
          .filter((o) => o.trangThai === TRANG_THAI_HOA_DON.HD_DA_PHAT_HANH)
          .map((item) => item.hoaDonId),
        dinhDang: dataDinhDangXemHoaDon,
        chuyenDoi: true,
      });
      getThongTinCoBan(nbDotDieuTriId);
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onXuatHDDT = async () => {
    const processPrinter = () => {
      const { tenCongTy, diaChiCongTy, mstCongTy, stkCongTy } =
        thongTinCoBan || {};
      //1 trong 4 trường # null -> Khi xuất hóa đơn thì tích Xuất hóa đơn cho công ty = true + Gửi thông tin công ty xuất hóa đơn
      const phatHanhHdCongTy = !(
        !tenCongTy &&
        !diaChiCongTy &&
        !mstCongTy &&
        !stkCongTy
      );

      if (isPhatHanhHoaDonNhanh) {
        refModalThongTinHoaDon.current &&
          refModalThongTinHoaDon.current.show(
            {
              phieuThuId,
              nbDotDieuTriId,
              thanhTien,
              phatHanhHdCongTy,
            },
            handleAfterSubmit
          );
      } else {
        clearDataDsPhieuThu();
        history.push(
          `/thu-ngan/tao-hoa-don-dien-tu/${nbDotDieuTriId}/${thongTinPhieuThu?.soPhieu}/${phieuThuId}`
        );
      }
    };
    // Kiểm tra trạng thái NB đã phát hành dịch vụ BH
    getTrangThaiDaPhatHanhHDDVBH(nbDotDieuTriId)
      .then((res) => {
        if (!res?.data) return processPrinter();
        showConfirm(
          {
            title: t("common.canhBao"),
            content: `${t("thuNgan.nbDaXuatHDDienTuLuongBaoHiem")}.\r\n${t(
              "thuNgan.tiepTucXuatHDDT"
            )}?`,
            cancelText: t("common.dong"),
            okText: t("common.xacNhan"),
            showBtnOk: true,
            typeModal: "warning",
            classNameOkText: "button-warning",
          },
          () => {
            processPrinter();
          },
          () => {}
        );
      })
      .catch((e) => {
        processPrinter();
      });
  };

  const onCapNhatTrangThaiGiaoDichQr = async () => {
    try {
      showLoading();

      await kiemTraGiaoDich(qrThanhToan.thanhToanId);
      await sleep(300);

      handleAfterSubmit();
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const customIcon = (panelProps) => {
    const { isActive } = panelProps;
    return (
      <span
        className={`anticon anticon-right ant-collapse-arrow ${
          isActive ? "" : "ant-collapse-arrow--revert"
        }`}
      >
        <SVG.IcExpandDown />
      </span>
    );
  };

  const onPrintPhieuThu = async () => {
    if (refModalSignPrint.current) {
      refModalSignPrint.current.show({
        nbDotDieuTriId,
        phieuThuId,
        ...maViTriManHinh,
      });
    }
  };

  const onPrintPhieu = (item) => () => {
    return new Promise(async (resolve, reject) => {
      setOpenPrint(false);
      if (item.key == 0) {
        onPrintPhieuThu();
      } else if (item.ma === "P291") {
        // Hoá đơn điện tử
        let _dsHDDT = isArray(dsHoaDon, true)
          ? dsHoaDon.filter((i) =>
              [
                TRANG_THAI_HOA_DON.HD_DA_PHAT_HANH,
                TRANG_THAI_HOA_DON.HD_DIEU_CHINH,
              ].includes(i.trangThai)
            )
          : [];
        if (isArray(_dsHDDT, true)) {
          try {
            showLoading();
            await inHoaDon({
              hoaDonId: _dsHDDT.map((item) => item.hoaDonId),
              dinhDang: dataDinhDangXemHoaDon,
            });
          } finally {
            hideLoading();
          }
        } else {
          message.error(t("thuNgan.khongTonTaiHoaDonTrongPhieuThu"));
        }
      } else {
        if (item.ma === "P657") {
          let _dsHDDT = isArray(dsHoaDon, true)
            ? dsHoaDon.filter((i) => 20 == i.trangThai)
            : [];
          getBienBanDieuChinh({ id: _dsHDDT[0]?.hoaDonId })
            .then((s) => {
              const blob = new Blob([new Uint8Array(s)], {
                type: "application/pdf",
              });
              const blobUrl = window.URL.createObjectURL(blob);
              printJS({
                printable: blobUrl,
                type: "pdf",
              });
            })
            .finally(() => hideLoading());
        } else if (item.type == "editor") {
          const lichSuKyId = item?.dsSoPhieu?.length
            ? item?.dsSoPhieu[0].lichSuKyId
            : "";
          let mhParams = {};
          //kiểm tra phiếu ký số
          if (checkIsPhieuKySo(item)) {
            //nếu là ký số thì lấy các thông tin về lịch sử ký + tên chân ký + báo cáo id
            mhParams = {
              ...maViTriManHinh,
              kySo: true,
              maPhieuKy: item.ma,
              nbDotDieuTriId,
              chiDinhTuDichVuId: phieuThuId,
            };
          }

          if (
            isBangKeInTheoKhoaChiDinh &&
            ["P178", "P107"].includes(item.ma)
            //P178: Bảng kê chi tiết KCB nội trú
            //P107: Bảng kê chi phí nội trú
          ) {
            //in bảng kê Tiện ích với NB ngoại trú
            if (
              isChoInBangKeTienIchNbNgoaiTru &&
              [
                DOI_TUONG_KCB.NGOAI_TRU,
                DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
                DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
              ].includes(doiTuongKcb) &&
              item.ma === "P178"
            ) {
              let data = {
                ma: item.ma,
                maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                  ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                  : "",
                loai: 60,
                loaiIn:
                  item.dsBaoCao && item.dsBaoCao.length > 0
                    ? item.dsBaoCao[0]?.loaiIn
                    : null,
                ...maViTriManHinh,
                maPhieuKy: item.ma,
                nbDotDieuTriId,
                ...mhParams,
                ...(isKhongTuDongDongTab && mhParams?.kySo
                  ? { notPrint: true }
                  : {}), //setting kySo = true -> Vẫn bật tab nhưng không tự động in phiếu
              };

              if (isKhongTuDongDongTab) {
                data.khongDongTab = true;
              }

              const url = combineUrlParams(
                `/print-file/bang-ke/${nbDotDieuTriId}`,
                {
                  ...data,
                }
              );
              window.open(url);
            } else {
              refModalChonTieuChiBangKe.current.show(
                {
                  khoaNbId: [thongTinCoBan.khoaNbId],
                },
                (values) => {
                  let data = {
                    id: nbDotDieuTriId,
                    ma: item.ma,
                    maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                      ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                      : "",
                    loai: LIST_FILE_SHOW_MODAL[item.ma],
                    loaiIn:
                      item.dsBaoCao && item.dsBaoCao.length > 0
                        ? item.dsBaoCao[0]?.loaiIn
                        : null,
                    tuThoiGianThucHien: values.tuThoiGianThucHien
                      ? moment(values.tuThoiGianThucHien).format()
                      : null,
                    denThoiGianThucHien: values.denThoiGianThucHien
                      ? moment(values.denThoiGianThucHien).format()
                      : null,
                    dsKhoaChiDinhId: values.dsKhoaChiDinhId,
                    ...maViTriManHinh,
                    maPhieuKy: item.ma,
                    nbDotDieuTriId,
                    ...mhParams,
                    ...(isKhongTuDongDongTab && mhParams?.kySo
                      ? { notPrint: true }
                      : {}), //setting kySo = true -> Vẫn bật tab nhưng không tự động in phiếu
                  };

                  if (isKhongTuDongDongTab) {
                    data.khongDongTab = true;
                  }

                  const url = combineUrlParams(
                    `/print-file/bang-ke/${data.id}`,
                    {
                      ...data,
                    }
                  );
                  window.open(url);
                }
              );
            }
          } else if (
            item.ma == "P032" &&
            kiemTra &&
            (!dsManHinh?.length || dsManHinh?.includes("004"))
          ) {
            let data = {
              hoTen: thongTinCoBan.tenNb,
              maThe: thongTinCoBan?.maTheBhyt,
              ngaySinh: moment(thongTinCoBan?.ngaySinh).format("DD/MM/YYYY"),
            };

            const showFileEditorFunc = () =>
              showFileEditor({
                phieu: item,
                nbDotDieuTriId,
                nbThongTinId: thongTinCoBan.nbThongTinId,
                id: nbDotDieuTriId,
                ma: item.ma,
                maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                  ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                  : "",
                lichSuKyId,
                khongDongTab: isKhongTuDongDongTab ? true : undefined,
                mhParams,
              });

            const showModalCheckBH = (_data) =>
              refModalCheckBaoHiem.current.show(
                {
                  show: true,
                  data: _data,
                  hoTen: thongTinCoBan.tenNb,
                  diaChi: thongTinCoBan.diaChi,
                },
                (res) => {
                  if (res.boQuaTheLoi) {
                    onUpdate({
                      id: nbDotDieuTriId,
                      nbTheBaoHiem: { boQuaTheLoi: true },
                    });

                    showFileEditorFunc();
                  }
                }
              );
            giamDinhThe({ ...data, keepCode: true })
              .then((res) => {
                if (res?.code === 0) {
                  showFileEditorFunc();
                } else {
                  showModalCheckBH(res);
                }
              })
              .catch((e) => {
                showModalCheckBH(e);
              });
          } else if (["P712"].includes(item.ma)) {
            //Lọc bỏ phiếu có số phiếu = nbDotDieuTriId
            const _dsSoPhieu = (item.dsSoPhieu || []).filter(
              (x) =>
                x.soPhieu != nbDotDieuTriId && x.soPhieu && x.soPhieu !== "null"
            );
            refModalInPhieuTomTatBA.current &&
              refModalInPhieuTomTatBA.current.show(
                {
                  dsSoPhieu: _dsSoPhieu,
                  ten: item.ten,
                  khoaChiDinhId: thongTinCoBan?.khoaNbId,
                },
                (data) => {
                  const { thoiGianThucHien, khoaChiDinhId, id: idPhieu } = data;
                  showFileEditor({
                    phieu: item,
                    id: idPhieu,
                    nbDotDieuTriId: nbDotDieuTriId,
                    ma: item.ma,
                    maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                      ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                      : "",
                    chiDinhTuDichVuId: nbDotDieuTriId,
                    khoaChiDinhId,
                    thoiGianThucHien,
                    mhParams,
                  });
                }
              );
          } else {
            let _maBaoCao = MA_BIEU_MAU_EDITOR[item.ma]
              ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
              : "";
            if (
              dataTHU_NGAN_TU_DONG_IN_KHONG_HIEN_THI_BANG_KE?.eval() &&
              ["P062", "P554"].includes(item.ma)
            ) {
              window.refModalViewPhieu.current &&
                window.refModalViewPhieu.current.show({
                  id: nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: _maBaoCao,
                  loai: LIST_FILE_SHOW_MODAL[item.ma],
                  loaiIn: item.loaiIn,
                  hinhThucIn: item.hinhThucIn,
                  lichSuKyId,
                  ...mhParams,
                });
            } else {
              showFileEditor({
                phieu: item,
                nbDotDieuTriId,
                nbThongTinId: thongTinCoBan.nbThongTinId,
                id: nbDotDieuTriId,
                ma: item.ma,
                maBaoCao: _maBaoCao,
                lichSuKyId,
                khongDongTab:
                  [
                    "P032",
                    "P062",
                    "P107",
                    "P541",
                    "P554",
                    "P678",
                    "P677",
                    "P746",
                  ].includes(item.ma) && isKhongTuDongDongTab
                    ? true
                    : undefined,
                mhParams,
                ...(isKhongTuDongDongTab && mhParams?.kySo
                  ? { notPrint: true }
                  : {}), //setting kySo = true -> Vẫn bật tab nhưng không tự động in phiếu
                ...(item.ma === "P878" && {
                  hoaDonId: bienBanDieuChinhHoaDonId,
                }),
              });
            }
          }
        } else {
          if (
            [
              "P062",
              "P678",
              "P554",
              "P677",
              "P033",
              "P034",
              "P359",
              "P443",
              "P304",
            ].includes(item.ma) &&
            checkIsPhieuKySo(item)
          ) {
            const payload = {
              nbDotDieuTriId,
              ...maViTriManHinh,
            };
            if (item.ma === "P359") {
              payload.id = item.dsSoPhieu[0].soPhieu;
            }
            let listItems = [item];
            //nếu là phiếu thu thì in phiếu hiện tại
            if (item.ma == "P033") {
              const dsSoPhieu = (item.dsSoPhieu || []).filter(
                (x) => x.soPhieu == phieuThuId
              );
              if (dsSoPhieu.length === 0) {
                message.error(t("thuNgan.khongTonTaiPhieuThuPhuHop"));
                return;
              }
              listItems = [
                { ...item, dsSoPhieu },
                ...(isIn2LinePhieuThu ? [{ ...item, dsSoPhieu }] : []),
              ];
            }
            //nếu là phiếu chi thì in tất cả phiếu
            if (item.ma == "P034") {
              listItems = [];
              (item.dsSoPhieu || []).forEach((element) => {
                listItems.push({ ...item, dsSoPhieu: [element] });
              });
            }
            refModalSignPrint.current &&
              refModalSignPrint.current.showToSign(
                {
                  phieuKy: ["P033", "P034"].includes(item.ma)
                    ? listItems
                    : item,
                  payload: payload,
                },
                async (maPhieu) => {
                  const resfreshPhieu = await getListPhieu({
                    ...payload,
                  });
                  let _returnPhieu = resfreshPhieu.find(
                    (x) => x.ma === maPhieu || x.ma === item?.ma
                  );
                  if (maPhieu == "P033") {
                    _returnPhieu = {
                      ..._returnPhieu,
                      dsSoPhieu: (_returnPhieu.dsSoPhieu || []).filter(
                        (x) => x.soPhieu == phieuThuId
                      ),
                    };
                  }
                  return _returnPhieu;
                }
              );
          } else {
            try {
              showLoading();
              let listItems = [item];
              //nếu là phiếu thu thì in phiếu hiện tại
              if (item.ma == "P033") {
                const dsSoPhieu = (item.dsSoPhieu || []).filter(
                  (x) => x.soPhieu == phieuThuId
                );
                if (dsSoPhieu.length === 0) {
                  message.error(t("thuNgan.khongTonTaiPhieuThuPhuHop"));
                  return;
                }
                listItems = [
                  { ...item, dsSoPhieu },
                  ...(isIn2LinePhieuThu ? [{ ...item, dsSoPhieu }] : []),
                ];
              }
              //nếu là phiếu chi thì in tất cả phiếu
              if (item.ma == "P034") {
                listItems = [];
                (item.dsSoPhieu || []).forEach((element) => {
                  listItems.push({ ...item, dsSoPhieu: [element] });
                });
              }

              const { finalFile, dsPhieu } = await getFilePhieuIn({
                listPhieus: listItems,
                nbDotDieuTriId: nbDotDieuTriId,
                showError: true,
                thanhToanId: qrThanhToan?.thanhToanId,
                ...([
                  "P187",
                  "P359",
                  "P360",
                  "P555",
                  "P707",
                  "P775",
                  "P999",
                ].includes(item.ma) && {
                  phieuThuId,
                  baoCaoId: item.baoCaoId,
                }),
                ...(["P555", "P999"].includes(item.ma) && {
                  ...maViTriManHinh,
                  maPhieuIn: item.ma,
                }),
                ...(item.ma === "P878" && {
                  hoaDonId: bienBanDieuChinhHoaDonId,
                }),
              });

              let flatList = dsPhieu;
              const getDsPdf = (dsPhieu) => {
                return flatten(dsPhieu.map((item) => item.filePdf));
              };

              if (flatList.length == 0) {
                message.error(t("khamBenh.khongTonTaiPhieuChiDinh"));
                return;
              }
              let dsFilePdf = [];
              const isInNhanh = flatList.some((item) => {
                let valid = false;
                if (isArray(item.data, true)) {
                  valid = item.data.some((el) =>
                    [LOAI_IN.IN_NHANH, LOAI_IN.IN_NHANH_PREVIEW].includes(
                      el.loaiIn
                    )
                  );
                } else {
                  valid = [LOAI_IN.IN_NHANH, LOAI_IN.IN_NHANH_PREVIEW].includes(
                    item.loaiIn
                  );
                }
                return valid;
              });
              if (isInNhanh) {
                let res2 = await printProvider.printPdf(flatList, {
                  onlyInNhanh: true,
                });
                if (res2.code == 0) {
                  resolve();
                  return;
                }

                if (res2.code == 1) {
                  flatList = flatList.filter((item, index) => {
                    return (
                      res2.data.find(
                        (item2) => item2.code != 0 && item2.index == index
                      ) != null
                    );
                  });
                }
              }
              dsFilePdf = getDsPdf(flatList);
              if (dsFilePdf.length) {
                const s = await centralizedErrorHandling(
                  printProvider.getMergePdf(flatten(dsFilePdf))
                );
                if (s) {
                  if (flatList.every((item) => item.loaiIn == LOAI_IN.MO_TAB)) {
                    openInNewTab(s);
                  } else {
                    printJS({
                      printable: s,
                      type: "pdf",
                      onPrintDialogClose: () => {
                        resolve();
                      },
                      onError: () => {
                        resolve();
                      },
                    });
                  }
                }
              }
            } catch (error) {
              console.log("error", error);
            } finally {
              hideLoading();
            }
          }
        }
      }
    });
  };

  const onPrintDsDichVu = async (e) => {
    e.preventDefault();
    try {
      showLoading();
      const res = await inDanhSachChiTietDichVuNb(nbDotDieuTriId);
      fileUtils.downloadFile(res?.file?.doc, `${res?.tenBaoCao}.xlsx`);
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  // nhieuPhieu=true: sinh phiếu chi cho các phiếu đã thanh toán
  const onClickSinhPhieuChi =
    (nhieuPhieu) =>
    ({ defaultLyDo = "" }) => {
      refNhapLyDo.current &&
        refNhapLyDo.current.show(
          {
            lyDo: defaultLyDo,
            showWarningMsg:
              trangThaiHoaDon === TRANG_THAI_HOA_DON.HD_DA_PHAT_HANH,
          },
          async (data) => {
            try {
              showLoading();
              if (thongTinCoBan?.phieuThuNhoHonMucCungChiTra && nhieuPhieu) {
                const res = await sinhPhieuChi({
                  lyDo: data.lyDo,
                  nhaThuNganId: nhaTamUngId,
                  phuongThucTtId: data.phuongThucTtId,
                  maChuanChi: data.maChuanChi,
                  nganHangId: data.nganHangId,
                  dieuChinhHoaDon: !data.dieuChinhHoaDon,
                  quayId: quayId,
                });
                if (res?.id) {
                  const s = await phieuChi(res?.id);
                  printProvider.printPdf(s);
                  history.push(
                    `/thu-ngan/chi-tiet-phieu-thu/${thongTinCoBan.maHoSo}/${res?.dsPhieuThuId?.[0]}/${nbDotDieuTriId}`
                  );
                  handleAfterSubmit({ newIdPhieuThu: res?.dsPhieuThuId?.[0] });
                }
              } else {
                const res = await chiPhieuThu({
                  dsPhieuThuId: [phieuThuId],
                  nbDotDieuTriId,
                  nhaThuNganId: nhaTamUngId,
                  caLamViecId,
                  phuongThucTtId: data.phuongThucTtId,
                  maChuanChi: data.maChuanChi,
                  nganHangId: data.nganHangId,
                  lyDo: data.lyDo,
                  dieuChinhHoaDon: !data.dieuChinhHoaDon,
                  quayId: quayId,
                });
                if (res?.dsHoaDonId?.length) {
                  await inHoaDon({
                    hoaDonId: res?.dsHoaDonId?.[0],
                    dinhDang: dataDinhDangXemHoaDon,
                  });
                }
                if (res?.id) {
                  const s = await phieuChi(res?.id);
                  printProvider.printPdf(s);
                  history.push(
                    `/thu-ngan/chi-tiet-phieu-thu/${thongTinCoBan.maHoSo}/${res?.dsPhieuThuId?.[0]}/${nbDotDieuTriId}`
                  );
                  handleAfterSubmit({ newIdPhieuThu: res?.dsPhieuThuId?.[0] });
                }
              }
            } catch (error) {
              console.error(error);
            } finally {
              hideLoading();
            }
          }
        );
    };

  refSinhPhieuChi.current = onClickSinhPhieuChi(true);

  const showBtnXuatHDDD = () => {
    if (hideBtnXuatHDDT) return false;
    const xuatHddt0Dong = isXuatHoaDonDayDv0Dong;
    const checkXuatHDDT = (value) => {
      return !(value?.trangThaiHoan === 0 && xuatHddt0Dong
        ? value?.thanhTien >= 0
        : value?.thanhTien > 0);
    };

    const checkPhatHanhHoaDon = (value) => {
      return value?.phatHanhHoaDon;
    };
    if (
      !listAllService.every(checkXuatHDDT) &&
      !listAllService.some(checkPhatHanhHoaDon) &&
      thanhToan === TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN
    ) {
      return true;
    } else {
      return false;
    }
  };

  const onDeleteVoucher = (item) => {
    let payload = {
      dsMaGiamGiaId: dsMaGiamGia
        .filter((x) => x.id !== item?.id)
        .map((x1) => x1?.id),
    };
    nbPhieuThuProvider.themMienGiamVoucher(payload, phieuThuId).then((res) => {
      handleAfterSubmit();
    });
  };

  const onThanhToanQrThanhCong = async () => {
    try {
      showLoading();
      handleAfterSubmit();
      const isHoanUng =
        tienTamUng > 0 &&
        ![LOAI_PHIEU_THU.NGOAI_DIEU_TRI, LOAI_PHIEU_THU.NHA_THUOC].includes(
          loaiPhieuThu
        ) &&
        tienTamUng >= thanhTien;
      let isNoiTru = [2, 3, 4, 6, 9].includes(doiTuongKcb);
      let isNoiTruPrint = isNoiTru ? !isHoanUng : true;
      await inListPhieuKhiThanhToan({
        id: phieuThuId,
        nbDotDieuTriId: nbDotDieuTriId,
        nhaThuNganId: nhaTamUngId,
        quayId: quayId,
        caLamViecId: caLamViecId,
        tongTien: thanhTien,
        inPhieuThuKhiThanhToan: isInPhieuThuKhiThanhToan && isNoiTruPrint,
        sinhPhieuThuTamUng: state.sinhPhieuThuTamUng,
        isPhatHanhHoaDonKhiThanhToanNgoaiTru:
          isPhatHanhHoaDonKhiThanhToanNgoaiTru, //bỏ điều kiện hoàn ứng
        // isPhatHanhHoaDonKhiThanhToanNgoaiTru:
        //   isPhatHanhHoaDonKhiThanhToanNgoaiTru && isNoiTruPrint,
        tienNbDua: thanhTien,
        onPrintDialogClose: () => {
          document.getElementById("input-search").focus();
        },
        dataDinhDangXemHoaDon,
        callback: () => {
          if (!dataIN_BANG_KE_TRUOC_THANH_TOAN?.eval()) onTuDongInBangKe6556();
        },
      });
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  function onSaveThoiGianTT() {
    updateThoiGianTT({
      id: thongTinPhieuThu?.id,
      thoiGianThanhToan: updateTime,
    }).then(() => {
      setEditTime(false);
      getThongTinPhieuThu(phieuThuId);
    });
  }

  function onChangeThoiGianTT(e) {
    setUpdateTime(e?._d);
  }

  const onChangePtTtId = (value) => {
    setPhuongThucTtId(value);
    refPtttChanged.current = true;
  };

  const onCollapsed = (value) => {
    setActiveKey(value);
    setExpandTongTien(value?.includes("1"));
  };

  const _dsHoaDon = isArray(dsHoaDon, 1)
    ? dsHoaDon.reduce((acc, cur) => (acc.soHoaDon > cur.soHoaDon ? acc : cur))
    : {};

  const onClickTongKetThanhToan = async () => {
    try {
      showLoading();
      await hoanThanhTongKetThanhToan(nbDotDieuTriId);

      getThongTinCoBan(nbDotDieuTriId);
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onClickHuyTongKetThanhToan = async () => {
    const onOk = async () => {
      try {
        showLoading();
        await huyTongKetThanhToan(nbDotDieuTriId);

        getThongTinCoBan(nbDotDieuTriId);
      } catch (error) {
        console.error(error);
      } finally {
        hideLoading();
      }
    };

    showConfirm(
      {
        title: t("common.canhBao"),
        content: `${t("thuNgan.banCoChacChanMuonHuyTongKetThanhToan")}?`,
        okText: t("common.xacNhan"),
        cancelText: t("common.quayLai"),
        showBtnOk: true,
        typeModal: "warning",
        classNameOkText: "button-warning",
      },
      onOk
    );
  };

  async function onClickXacNhanBhyt(isXacNhan) {
    const handleAfterXacNhan = async () => {
      showLoading();
      try {
        if (isArray(listMaPhieuInKhiXacNhanBHYT, true)) {
          const res = await Promise.all([
            await getPhieuInTheoDsMa({
              nbDotDieuTriId,
              chiDinhTuDichVuId: phieuThuId,
              ...maViTriManHinh,
              dsMaPhieu: listMaPhieuInKhiXacNhanBHYT,
            }),
          ]);
          const listDichVu = await getAllListServices({
            nbDotDieuTriId: nbDotDieuTriId,
            page: "",
            size: "",
            notUpdateData: true,
          });
          const _flattenRes = flatten(res) || [];
          for (let i = 0; i < _flattenRes.length; i++) {
            let item = _flattenRes[i];
            let _maBaoCao = MA_BIEU_MAU_EDITOR[item.ma]
              ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
              : "";

            let mhParams = {};
            //kiểm tra phiếu ký số
            if (checkIsPhieuKySo(item)) {
              //nếu là ký số thì lấy các thông tin về lịch sử ký + tên chân ký + báo cáo id
              mhParams = {
                ...maViTriManHinh,
                kySo: true,
                maPhieuKy: item.ma,
                nbDotDieuTriId,
                chiDinhTuDichVuId: phieuThuId,
              };
            }
            const lichSuKyId = item?.dsSoPhieu?.length
              ? item?.dsSoPhieu[0].lichSuKyId
              : "";

            if (
              ["P032", "P178"].includes(item.ma) &&
              dataTHU_NGAN_TU_DONG_IN_KHONG_HIEN_THI_BANG_KE?.eval()
            ) {
              showLoading();
              if (
                listDichVu.every((dv) => dv.mucHuongBhyt) &&
                item.ma == "P178"
              ) {
                message.error(t("thuNgan.nguoiBenhKhongCoDichVuTuTra"));
              } else {
                window.refModalViewPhieu.current &&
                  (await window.refModalViewPhieu.current.show({
                    id: nbDotDieuTriId,
                    nbDotDieuTriId,
                    ma: item.ma,
                    maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                      ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                      : "",
                    loai:
                      item.ma == "P178" ? 60 : LIST_FILE_SHOW_MODAL[item.ma],
                    loaiIn: item.loaiIn,
                    hinhThucIn: item.hinhThucIn,
                    lichSuKyId,
                    allowHideLoading: false,
                    ...mhParams,
                  }));
              }
            } else {
              if (
                listDichVu.every((dv) => dv.mucHuongBhyt) &&
                item.ma == "P178"
              ) {
                message.error(t("thuNgan.nguoiBenhKhongCoDichVuTuTra"));
              } else {
                showFileEditor({
                  phieu: item,
                  nbDotDieuTriId,
                  nbThongTinId: thongTinCoBan.nbThongTinId,
                  id: nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: _maBaoCao,
                  lichSuKyId,
                  khongDongTab: isKhongTuDongDongTab ? true : undefined,
                  mhParams,
                  ...(isKhongTuDongDongTab && mhParams?.kySo
                    ? { notPrint: true }
                    : {}), //setting kySo = true -> Vẫn bật tab nhưng không tự động in phiếu
                });
              }
            }
          }
        }
      } finally {
        hideLoading();
      }
    };

    if (isXacNhan && !thoiGianHuyThanhToan) {
      showLoading();
      const khoThuNgan = getKho();
      const [err, res] = await toSafePromise(
        nbXacNhanBaoHiemProvider.xacNhanBhyt({
          id: nbDotDieuTriId,
          khoId: khoThuNgan?.id,
        })
      );
      if (err) {
        message.error(err?.message || t("common.xayRaLoiVuiLongThuLaiSau"));

        if (err.code === 1003) {
          //CANH_BAO_DUYET_DLS_KHI_PHAT_THUOC/TAO_PHIEU_LINH = 2
          //kiểm tra API dưới nếu NB có tồn tại thuốc có trangThaiDuocLamSang = 25 => thì không tự động in bảng kê
          if (
            isKiemTraThuocDuocLamSang &&
            listAllService.some((item) => item?.trangThaiDuocLamSang === 25)
          ) {
            hideLoading();
            message.error(
              t("thuNgan.coThuocTrongPhieuBiTuChoiDuyetDuocLamSang")
            );
          } else {
            handleAfterXacNhan();
          }
        } else {
          hideLoading();
        }
      } else {
        message.success(t("thuNgan.xacNhanBhytThanhCong"));
        if (res?.data && typeof res.data === "string") {
          message.error(res.data);
        }
        handleAfterSubmit(isXacNhan);
        handleAfterXacNhan();
      }
      return;
    }

    refModalXacNhanBhyt.current?.show(
      {
        isXacNhan,
        nbDotDieuTriId,
        thongTinPhieuThu,
      },
      handleAfterXacNhan
    );
  }

  const onCapNhatHoanNganHang = () => {
    hoanTienNganHang({
      id: ttNbHoanUng?.id,
      maGiaoDich: ttNbHoanUng?.maGiaoDich,
    });
  };

  return (
    <Main className="thong-tin-phieu-thu">
      <Spin
        spinning={
          phieuThuId && isLoadingThongTinPhieuThu === undefined
            ? true
            : isLoadingThongTinPhieuThu || false
        }
      >
        <div className="top-header">
          <span className="top-header__title">
            {t("thuNgan.thongTinPhieuThu")}
          </span>
          {showActions?.showBtnThanhToan && (
            <AuthWrapper accessRoles={[ROLES["THU_NGAN"].THANH_TOAN]}>
              <Button
                type={"success"}
                rightIcon={<SVG.IcThanhToan />}
                onClick={onClickThanhToan}
                className="btn-thanhToan"
              >
                {t("common.thanhToan")} [F12]
              </Button>
            </AuthWrapper>
          )}
          {showActions?.showBtnDuyet && (
            <Button
              type={"success"}
              // rightIcon={<SVG.IcThanhToan />}
              onClick={() => onClickThanhToan(null, { isDuyetPhieuThu: true })}
              className="btn-thanhToan"
            >
              {t("thuNgan.duyetPhieuThu")}
            </Button>
          )}
          {showBtnXuatHDDD() && (
            <AuthWrapper accessRoles={[ROLES["THU_NGAN"].XUAT_HDDT]}>
              <Button
                rightIcon={<SVG.IcPrint />}
                onClick={onXuatHDDT}
                type="primary"
                borderColor="#FFFFFF20"
                ref={refBtnXuatHDDT}
              >
                {t("thuNgan.xuatHDDT")} [F8]
              </Button>
            </AuthWrapper>
          )}
        </div>
        <div className="content-box">
          <Collapse
            activeKey={activeKey}
            bordered={false}
            expandIcon={customIcon}
            onChange={onCollapsed}
          >
            <Panel
              header={
                <div className="info-payment info-payment--pd0-header">
                  <div className="info-payment__title info-payment__title--bs">
                    {t("common.tongTien")}
                  </div>
                  <div className="info-payment__price info-payment__price--bs">
                    {formatDecimal(tongTienMemo)} đ
                  </div>
                </div>
              }
              key="1"
            >
              {thongTinCoBan.doiTuong === DOI_TUONG.BAO_HIEM && (
                <>
                  <div className="info-payment">
                    <div className="info-payment__title">
                      {t("thuNgan.bhChiTra")}
                    </div>
                    <div className="info-payment__price">
                      {formatDecimal(tienBhThanhToan)} đ
                    </div>
                  </div>
                  <div className="info-payment">
                    <div className="info-payment__title">
                      {t("thuNgan.nbCungChiTra")}
                    </div>
                    <div className="info-payment__price">
                      {formatDecimal(tienNbCungChiTra)} đ
                    </div>
                  </div>
                  <div className="info-payment">
                    <div className="info-payment__title">
                      {t("thuNgan.nbTuTra")}
                    </div>
                    <div className="info-payment__price">
                      {formatDecimal(
                        isKhongHienThiTienPhuThu
                          ? (tienNbPhuThu || 0) + (tienNbTuTraMemo || 0)
                          : tienNbTuTraMemo
                      )}{" "}
                      đ
                    </div>
                  </div>
                  {isKhongHienThiTienPhuThu ? (
                    <></>
                  ) : (
                    <div className="info-payment">
                      <div className="info-payment__title">
                        {t("thuNgan.phuThu")}
                      </div>
                      <div className="info-payment__price">
                        {formatDecimal(tienNbPhuThu)} đ
                      </div>
                    </div>
                  )}
                </>
              )}
              <div className="info-payment">
                <div className="info-payment__title">
                  {t("thuNgan.nguonKhac")}
                </div>
                <div className="info-payment__price">
                  {formatDecimal(tienNguonKhac)} đ
                </div>
              </div>
            </Panel>

            {isHienThiTongTienTheoDoiTuong &&
              doiTuongKcb != DOI_TUONG_KCB.NGOAI_TRU && (
                <Panel
                  header={
                    <div className="info-payment info-payment--pd0-header">
                      <div
                        style={{ flex: 1 }}
                        className="info-payment__title info-payment__title--bs"
                      >
                        {t("thuNgan.doiTuong")}
                      </div>
                      <div
                        style={{ flex: 1, textAlign: "center" }}
                        className="info-payment__price info-payment__price--bs"
                      >
                        {t("thuNgan.soTien")}
                      </div>
                      <div
                        style={{ flex: 1 }}
                        className="info-payment__price info-payment__price--bs"
                      >
                        {t("thuNgan.NBTra")}
                      </div>
                    </div>
                  }
                  key="3"
                >
                  {/* lọc ko hiển thị loại bảng kê Hao phí (4) */}
                  {(dsBangKe || [])
                    .filter(
                      (x) =>
                        x.loaiBangKe &&
                        x.loaiBangKe != 4 &&
                        x.loaiBangKe <= 5 &&
                        x.loaiBangKe >= 1
                    )
                    .map((item) => (
                      <div key={item.id} className="info-payment">
                        <div
                          style={{ flex: 1 }}
                          className="info-payment__title"
                        >
                          {LOAI_BANG_KE[item.loaiBangKe - 1] || ""}
                        </div>
                        <div
                          style={{ flex: 1, textAlign: "center" }}
                          className="info-payment__price"
                        >
                          {formatDecimalNoComma(item.tongTien)} đ
                        </div>
                        <div
                          style={{ flex: 1 }}
                          className="info-payment__price"
                        >
                          {formatDecimalNoComma(item.thanhTien)} đ
                        </div>
                      </div>
                    ))}
                </Panel>
              )}

            {(tienMienGiam > 0 || dsMaGiamGia?.length) && (
              <Panel
                header={
                  <div className="info-payment info-payment--pd0-header">
                    <div className="info-payment__title info-payment__title--bs">
                      {t("thuNgan.tongTienMienGiam")}
                    </div>
                    <div className="info-payment__price info-payment__price--bs">
                      {formatDecimal(tienMienGiam + tienGiamGia)} đ
                    </div>
                  </div>
                }
                key="2"
              >
                <div className="info-payment">
                  <div className="info-payment__title">
                    {t("thuNgan.theoDv")}
                  </div>
                  <div className="info-payment__price">
                    {formatDecimal(tienMienGiamDichVu)} đ
                  </div>
                </div>
                <div className="info-payment">
                  <div className="info-payment__title">
                    {t("thuNgan.theoPhieuThu")}
                  </div>
                  <div className="info-payment__price">
                    <SVG.IcArrowBack
                      color="black"
                      rotate={270}
                      style={{ verticalAlign: "middle" }}
                    />
                    {phanTramMienGiam || 0} %
                  </div>
                </div>
                {phanTramMienGiam <= 0 && tienMienGiamPhieuThu > 0 && (
                  <div className="info-payment">
                    <div className="info-payment__title"></div>
                    <div className="info-payment__price">
                      <SVG.IcArrowBack
                        rotate={270}
                        color="black"
                        style={{ verticalAlign: "middle" }}
                      />
                      {formatDecimal(tienMienGiamPhieuThu)} đ
                    </div>
                  </div>
                )}
                {!!dsMaGiamGia?.length && (
                  <div className="info-payment">
                    <div className="info-payment__title">Voucher</div>{" "}
                    <div
                      className="info-payment__price"
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(2, 1fr)",
                      }}
                    >
                      {(dsMaGiamGia || []).map((item) => {
                        return (
                          <Tooltip title={item?.moTa} color="#108ee9">
                            <div
                              className="info-payment__voucher"
                              style={{
                                marginBottom: "5px",
                                marginLeft: "5px",
                              }}
                            >
                              {item?.maVoucher}
                              <span
                                className="close"
                                onClick={() => onDeleteVoucher(item)}
                              >
                                X
                              </span>
                            </div>
                          </Tooltip>
                        );
                      })}
                    </div>
                  </div>
                )}
                <div style={{ display: "flex", alignItems: "center" }}>
                  <b style={{ fontSize: "16px" }}>
                    {t("thuNgan.quanLyTamUng.ghiChu")}:
                  </b>
                  <div style={{ paddingLeft: "4px" }}>
                    {thongTinPhieuThu?.ghiChu}
                  </div>
                </div>
              </Panel>
            )}

            {(tienTaiTroKhongBaoHiem > 0 || tienTaiTroBaoHiem > 0) && (
              <Panel
                header={
                  <div className="info-payment info-payment--pd0-header">
                    <div className="info-payment__title info-payment__title--bs">
                      {t("thuNgan.tongTienTaiTroBhbl")}
                    </div>
                    <div className="info-payment__price info-payment__price--bs">
                      {formatDecimal(
                        tienTaiTroKhongBaoHiem + tienTaiTroBaoHiem
                      )}{" "}
                      đ
                    </div>
                  </div>
                }
                key="2"
              >
                {(dsHoaDonTaiTro || []).map((item) => {
                  return (
                    <div className="info-payment">
                      <Tooltip title={item?.tenDonViTaiTro}>
                        <div
                          className="info-payment__title"
                          style={{
                            maxWidth: "200px",
                            whiteSpace: "nowrap",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                          }}
                        >
                          {item?.tenDonViTaiTro}
                        </div>{" "}
                      </Tooltip>

                      <div className="info-payment__price">
                        {" "}
                        {formatDecimal(
                          item?.tienBaoHiem + item?.tienKhongBaoHiem
                        )}{" "}
                        đ
                      </div>
                    </div>
                  );
                })}
              </Panel>
            )}
          </Collapse>
          <div className="info-payment info-payment--pd0">
            <div className="info-payment__title info-payment__title--fixed">
              {t("thuNgan.soTienNbPhaiTra")}
            </div>
            <div className="info-payment__price info-payment__price--fixed">
              {formatDecimal(thanhTien)} đ
            </div>
          </div>

          {/* Tổng Biên lai, Tổng Hóa đơn */}
          {isShowThanhTienBienLaiHoaDon && (
            <>
              <div className="info-payment">
                <div className="info-payment__title">
                  {t("thuNgan.tongBienLai")}
                </div>
                <div className="info-payment__price">
                  {tinhTongTienBienLai(listAllService)} đ
                </div>
              </div>

              <div className="info-payment">
                <div className="info-payment__title">
                  {t("thuNgan.tongHoaDon")}
                </div>
                <div className="info-payment__price">
                  {tinhTongTienHoaDon(listAllService)} đ
                </div>
              </div>
            </>
          )}

          {thanhToan !== TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN &&
            isTuDongThanhToanPhieuThuTienMat && (
              <>
                <div className="info-payment info-payment--pdt">
                  <div className="info-payment__title info-payment__title--bs">
                    {t("thuNgan.chiTietPhuongThucTt")} [F7]
                  </div>
                  <div className="info-payment__price"></div>
                </div>
                <div className="info-payment">
                  <div className="info-payment__title">
                    <div className="info-payment__title__left">
                      <Select
                        open={openSelectPhuongThucTt}
                        value={phuongThucTtId}
                        onChange={onChangePtTtId}
                        data={listAllPhuongThucThanhToan}
                        placeholder={t("thuNgan.chonPTTT")}
                        refSelect={refSelectPhuongThucTt}
                        dropdownStyle={{ minWidth: "300px" }}
                        onDropdownVisibleChange={(open) =>
                          setOpenSelectPhuongThucTt(open)
                        }
                      />
                    </div>
                  </div>
                  <div className="info-payment__price">
                    <div> {formatDecimal(thanhTien)} đ</div>
                  </div>
                </div>
              </>
            )}

          <div className="info-payment info-payment--pdt">
            <div className="info-payment__title info-payment__title--bs">
              {t("thuNgan.trangThaiPhieuThu")}
            </div>
            <div
              className={`info-payment__price ${
                thanhToan === TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN
                  ? "info-payment__price--green"
                  : "info-payment__price--orange"
              }`}
            >
              {listtrangThaiPhieuThu.find((x) => x.id === thanhToan)?.ten}
            </div>
          </div>
          <TenThuNgan
            phieuThuId={phieuThuId}
            tenThuNgan={tenThuNgan}
            thuNganId={thongTinPhieuThu?.thuNganId}
            t={t}
            handleAfterSubmit={handleAfterSubmit}
          />
          {tenNguoiYeuCau ? (
            <div className="info-payment">
              <div className="info-payment__title">
                {t("thuNgan.nguoiSinhPhieuChi")}
              </div>
              <div className="info-payment__price">{tenNguoiYeuCau}</div>
            </div>
          ) : null}
          {thanhToan === TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN && (
            <div className="info-payment">
              <div className="info-payment__title">
                {t("thuNgan.tgThanhToan")}
              </div>
              <div className="info-payment__price flex flex-a-center">
                {editTime ? (
                  <div style={{ display: "flex", alignItems: "center" }}>
                    <DatePicker
                      showTime
                      placeholder="Chọn ngày"
                      format="DD/MM/YYYY HH:mm:ss"
                      value={moment(updateTime)}
                      // disabledDate={(currentDate) =>
                      //   currentDate.isBefore(
                      //     moment(thongTinCoBan?.thoiGianVaoVien)
                      //   )
                      // }
                      onChange={onChangeThoiGianTT}
                    />
                    &emsp;
                    <SVG.IcSave
                      color={"var(--color-blue-primary)"}
                      onClick={onSaveThoiGianTT}
                    />
                  </div>
                ) : (
                  thoiGianThanhToan &&
                  moment(thoiGianThanhToan).format("DD/MM/YYYY HH:mm")
                )}

                {!editTime && (
                  <AuthWrapper
                    accessRoles={[ROLES["THU_NGAN"].SUA_THOI_GIAN_THANH_TOAN]}
                  >
                    &emsp;
                    <SVG.IcEdit
                      onClick={() => {
                        setEditTime(true);
                        setUpdateTime(thoiGianThanhToan);
                      }}
                    />
                  </AuthWrapper>
                )}
              </div>
            </div>
          )}
          {!!soPhieu && (
            <div className="info-payment">
              <div className="info-payment__title">
                {t("thuNgan.soPhieuThu")}
              </div>
              <div className="info-payment__price">{soPhieu}</div>
            </div>
          )}

          {!!kyHieu && (
            <div className="info-payment">
              <div className="info-payment__title">
                {t("thuNgan.quanLyTamUng.kyHieu")}
              </div>
              <div className="info-payment__price">{kyHieu}</div>
            </div>
          )}

          {thanhToan === TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN && (
            <>
              <div className="info-payment">
                <div className="info-payment__title">
                  {t("thuNgan.soHoaDon")}
                </div>
                <div className="info-payment__price">{_dsHoaDon?.soHoaDon}</div>
              </div>
              <div className="info-payment">
                <div className="info-payment__title">
                  {t("thuNgan.kyHieuHoaDon")}
                </div>
                <div className="info-payment__price">
                  {_dsHoaDon?.kyHieuHoaDon}
                </div>
              </div>
            </>
          )}

          <DsPtTt
            dsPhuongThucTt={dsPhuongThucTt}
            t={t}
            phieuThuId={phieuThuId}
            getThongTinPhieuThu={getThongTinPhieuThu}
            thuNganId={thongTinPhieuThu?.thuNganId}
            tenQuay={thongTinPhieuThu?.tenQuay}
            quayId={thongTinPhieuThu?.quayId}
            tenCaLamViec={thongTinPhieuThu?.tenCaLamViec}
            caLamViecId={thongTinPhieuThu?.caLamViecId}
            showTenQuay={true}
            handleAfterSubmit={handleAfterSubmit}
          />
          {tienTamUng > thanhTien && ttNbHoanUng && (
            <ThongTinHoanUng nbHoanUng={ttNbHoanUng} />
          )}
          {thanhToan !== TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN &&
            isHoanUngTTVaSinhPhieuTamUng && (
              <Checkbox
                style={{ marginLeft: 0 }}
                onChange={(e) =>
                  setState({ sinhPhieuThuTamUng: e?.target?.checked })
                }
              >
                {t("thuNgan.sinhPhieuThuTamUng")}
              </Checkbox>
            )}

          {dataTU_DONG_CHECK_CO_HOAN_TAM_UNG?.eval() &&
            thanhToan !== TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN && (
              <Checkbox
                style={{ marginLeft: 0 }}
                checked={state.isTamUng}
                onChange={(e) => setState({ isTamUng: e?.target?.checked })}
              >
                {t("thuNgan.coHoanUng")}
              </Checkbox>
            )}

          <div className="bottom-group">
            {phieuThuId && (
              <AuthWrapper accessRoles={[ROLES["THU_NGAN"].IN_PHIEU_THU]}>
                <Dropdown
                  open={openPrint}
                  overlayClassName="thuNgan__button-print"
                  overlay={
                    <MenuPhieuIn
                      listPhieu={listPhieu}
                      curBaocao={curBaocao}
                      setCurBaocao={setCurBaocao}
                      onPrintPhieu={onPrintPhieu}
                      onPrintDsDichVu={onPrintDsDichVu}
                      refDropDownPrint={refDropDownPrint}
                      t={t}
                      openPrint={openPrint}
                      setOpenPrint={setOpenPrint}
                      layerId={layerId}
                      onRegisterHotkey={onRegisterHotkey}
                    />
                  }
                  trigger={["click"]}
                  placement="topCenter"
                  onOpenChange={onOpenChange(setOpenPrint)}
                >
                  <Button
                    // onClick={onPrintPhieuThu}
                    // ref={refBtnInPhieuThu}
                    className="custom-btn"
                    type="default"
                    rightIcon={<SVG.IcPrint />}
                    iconHeight={15}
                    minWidth={120}
                  >
                    {t("thuNgan.inGiayTo")} [F3]
                  </Button>
                </Dropdown>
              </AuthWrapper>
            )}
            {thanhToan === TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN && (
              <AuthWrapper accessRoles={[ROLES["THU_NGAN"].PHIEU_CHI]}>
                <Button
                  onClick={onClickSinhPhieuChi(false)}
                  ref={refBtnSinhPhieuChi}
                  type="default"
                  rightIcon={<SVG.IcHDHD />}
                  minWidth={120}
                >
                  {t("thuNgan.sinhPhieuChi")}
                </Button>
              </AuthWrapper>
            )}

            {checkRole([ROLES["TIEP_DON"].HUY_THE_RFID]) &&
              thongTinCoBan.maThe && (
                <Button
                  onClick={() => {
                    showConfirm(
                      {
                        title: t("tiepDon.huyTheRFID"),
                        content: t("tiepDon.xacNhanHuyTheRFID").replace(
                          "{0}",
                          thongTinCoBan.tenNb
                        ),
                        cancelText: t("common.quayLai"),
                        okText: t("common.dongY"),
                        classNameOkText: "button-error",
                        showImg: true,
                        showBtnOk: true,
                      },
                      () => {
                        postRecordHuy(
                          {
                            nbDotDieuTriId,
                            maThe: thongTinCoBan.maThe,
                          },
                          { ignoreMessage: true }
                        )
                          .then((res) => {
                            message.success(t("tiepDon.huyTheRFIDThanhCong"));
                            if (nbDotDieuTriId)
                              getThongTinCoBan(nbDotDieuTriId);
                          })
                          .catch((err) => {
                            message.error(t("tiepDon.huyTheRFIDThatBai"));
                          });
                      }
                    );
                  }}
                  rightIcon={<SVG.IcPhatHanhThe />}
                >
                  {t("tiepDon.huyTheRFID")}
                </Button>
              )}
          </div>

          {(!!showDuyetChiPhi || !!showHuyDuyetChiPhi) && (
            <div className="bottom-group">
              {!!showDuyetChiPhi && (
                <>
                  <Button
                    type="primary"
                    onClick={onClickDuyetChiPhi}
                    minWidth={105}
                  >
                    {t("thuNgan.duyetChiPhi")}
                  </Button>

                  <Button
                    type="default"
                    onClick={onClickTuChoiDuyetChiPhi}
                    minWidth={105}
                  >
                    {t("thuNgan.tuChoiDuyetChiPhi")}
                  </Button>
                </>
              )}

              {!!showHuyDuyetChiPhi && (
                <Button
                  type="default"
                  onClick={onClickHuyDuyetChiPhi}
                  minWidth={105}
                >
                  {t("thuNgan.huyDuyetChiPhi")}
                </Button>
              )}
            </div>
          )}
          {showDuyetBh && (
            <div className="bottom-group">
              {trangThaiDuyetBh === TRANG_THAI_DUYET_BH.DA_DUYET_BH && (
                <AuthWrapper
                  accessRoles={[ROLES["KE_HOACH_TONG_HOP"].HUY_DUYET_BAO_HIEM]}
                >
                  <Button
                    name="huyDuyetBH"
                    minWidth={105}
                    onClick={onActionBaoHiem(false)}
                  >
                    {t("khth.huyDuyetBH")}
                  </Button>
                </AuthWrapper>
              )}
              {trangThaiDuyetBh === TRANG_THAI_DUYET_BH.CHUA_DUYET_BH && (
                <AuthWrapper
                  accessRoles={[ROLES["KE_HOACH_TONG_HOP"].DUYET_BAO_HIEM]}
                >
                  <Button
                    name="duyetBH"
                    minWidth={105}
                    onClick={onActionBaoHiem(true)}
                    ref={refBtnDuyetBh}
                  >
                    {t("khth.duyetBH")} [F11]
                  </Button>
                </AuthWrapper>
              )}
            </div>
          )}

          {thanhToan === TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN && (
            <>
              <div className="bottom-group">
                <AuthWrapper accessRoles={[ROLES["THU_NGAN"].HUY_THANH_TOAN]}>
                  <Button
                    type="default"
                    onClick={() => onClickHuyThanhToan(1)}
                    minWidth={105}
                  >
                    {t("thuNgan.huyThanhToan")}
                  </Button>
                </AuthWrapper>

                {trangThaiHoaDon === TRANG_THAI_HOA_DON.HD_TAO_MOI &&
                  showBtnXuatHDDD() && (
                    <AuthWrapper
                      accessRoles={[ROLES["THU_NGAN"].XUAT_HOA_DON_NHAP]}
                    >
                      <Button
                        type="default"
                        onClick={onClickXuatHoaDonNhap}
                        minWidth={105}
                      >
                        {t("thuNgan.xuatHoaDonNhap")}
                      </Button>
                    </AuthWrapper>
                  )}
              </div>
              <div className="bottom-group">
                <AuthWrapper
                  accessRoles={[ROLES["THU_NGAN"].HUY_THANH_TOAN_GIU_CHUNG_TU]}
                >
                  <Button
                    type="default"
                    onClick={() => onClickHuyThanhToan(2)}
                    minWidth={105}
                  >
                    {t("thuNgan.huyTtGiuChungTu")}
                  </Button>
                </AuthWrapper>
              </div>

              <div className="bottom-group">
                {showBtnQuyetToan && (
                  <>
                    {thongTinCoBan.doiTuong === DOI_TUONG.BAO_HIEM &&
                      checkRole([ROLES["THU_NGAN"].QUYET_TOAN_BH]) && (
                        <Button
                          ref={refClickQuyetToan}
                          type="default"
                          onClick={onClickQuyetToan(false)}
                          minWidth={105}
                        >
                          {t("thuNgan.quyetToanBh")}
                        </Button>
                      )}

                    {checkRole([ROLES["THU_NGAN"].QUYET_TOAN_130]) && (
                      <Button
                        ref={refClickQuyetToan130}
                        type="default"
                        onClick={onClickQuyetToan(true)}
                        minWidth={105}
                      >
                        {t("thuNgan.quyetToan130")} [F10]
                      </Button>
                    )}
                  </>
                )}
              </div>
            </>
          )}
          {!!showBtnQuyetToanChuaThanhToan && (
            <div className="bottom-group">
              {thongTinCoBan.doiTuong === DOI_TUONG.BAO_HIEM &&
                checkRole([ROLES["THU_NGAN"].QUYET_TOAN_BH]) && (
                  <Button
                    ref={refClickQuyetToan}
                    type="default"
                    onClick={onClickQuyetToan(false)}
                    minWidth={105}
                  >
                    {t("thuNgan.quyetToanBh")}
                  </Button>
                )}

              {checkRole([ROLES["THU_NGAN"].QUYET_TOAN_130]) && (
                <Button
                  ref={refClickQuyetToan130}
                  type="default"
                  onClick={onClickQuyetToan(true)}
                  minWidth={105}
                >
                  {t("thuNgan.quyetToan130")} [F10]
                </Button>
              )}
            </div>
          )}
          {qrThanhToan && (
            <div className="bottom-group">
              {[
                TRANG_THAI_THANH_TOAN_QR.MOI,
                TRANG_THAI_THANH_TOAN_QR.TAO_QR,
                TRANG_THAI_THANH_TOAN_QR.THANH_TOAN_LOI,
                TRANG_THAI_THANH_TOAN_QR.THANH_TOAN,
                TRANG_THAI_THANH_TOAN_QR.TAO_QR_LOI,
              ].includes(qrThanhToan.trangThaiThanhToan) && (
                <Button type="default" onClick={onXemQrCode} minWidth={105}>
                  {t("thuNgan.xemQrCode")}
                </Button>
              )}
              {[
                TRANG_THAI_THANH_TOAN_QR.MOI,
                TRANG_THAI_THANH_TOAN_QR.TAO_QR,
                TRANG_THAI_THANH_TOAN_QR.TAO_QR_LOI,
              ].includes(qrThanhToan.trangThaiThanhToan) &&
                qrThanhToan.thanhToanId && (
                  <Button type="default" onClick={onHuyQrCode} minWidth={105}>
                    {t("thuNgan.huyQrcode")}
                  </Button>
                )}
            </div>
          )}
          {isShowTongKetThanhToan && (
            <div className="bottom-group">
              {thongTinCoBan.trangThaiNb >= TRANG_THAI_NB.DA_RA_VIEN &&
                isDaThanhToanTatCaPhieuThu &&
                thongTinCoBan?.trangThaiTktt != TRANG_THAI_TKTT.HOAN_THANH &&
                checkRole([ROLES["THU_NGAN"].TONG_KET_THANH_TOAN]) && (
                  <Button
                    type="default"
                    onClick={onClickTongKetThanhToan}
                    minWidth={105}
                  >
                    {t("thuNgan.tongKetThanhToan")}
                  </Button>
                )}

              {checkRoleOr([
                ROLES["THU_NGAN"].HUY_TONG_KET_THANH_TOAN,
                ROLES["THU_NGAN"]
                  .HUY_TONG_KET_THANH_TOAN_TRONG_NGAY_CUA_BAN_THAN_THU_NGAN,
                ROLES["THU_NGAN"].HUY_TONG_KET_THANH_TOAN_CUA_THU_NGAN_KHAC,
              ]) &&
                thongTinCoBan?.trangThaiTktt == TRANG_THAI_TKTT.HOAN_THANH && (
                  <Button
                    type="default"
                    onClick={onClickHuyTongKetThanhToan}
                    minWidth={105}
                  >
                    {t("thuNgan.huyTongKetThanhToan")}
                  </Button>
                )}
            </div>
          )}

          {(showXacNhanBhyt ||
            (thongTinCoBan.trangThaiXacNhanBaoHiem ===
              TRANG_THAI_XAC_NHAN_BHYT.DA_XAC_NHAN &&
              thongTinCoBan.doiTuong === DOI_TUONG.BAO_HIEM &&
              checkRole([ROLES["THU_NGAN"].HUY_XAC_NHAN_BHYT]))) && (
            <div className="bottom-group">
              {showXacNhanBhyt && (
                <Button
                  type="default"
                  onClick={() => onClickXacNhanBhyt(true)}
                  minWidth={105}
                  ref={refBtnXacNhanBhyt}
                >
                  {t("thuNgan.xacNhanBhyt")} [F9]
                </Button>
              )}
              {thongTinCoBan.trangThaiXacNhanBaoHiem ===
                TRANG_THAI_XAC_NHAN_BHYT.DA_XAC_NHAN &&
                thongTinCoBan.doiTuong === DOI_TUONG.BAO_HIEM &&
                checkRole([ROLES["THU_NGAN"].HUY_XAC_NHAN_BHYT]) && (
                  <Button
                    type="default"
                    onClick={() => onClickXacNhanBhyt(false)}
                    minWidth={105}
                  >
                    {t("thuNgan.huyXacNhanBhyt")}
                  </Button>
                )}
            </div>
          )}

          {(showBtnCapNhatGiaoDichQr ||
            ([50, 60, 70, 80].includes(ttNbHoanUng.trangThaiHoanThanhToan) &&
              thongTinCoBan?.maNganHang == dataMA_DOI_TAC_HOAN_TIEN &&
              !!thongTinCoBan?.soTaiKhoan)) && (
            <div className="bottom-group">
              {[50, 60, 70, 80].includes(ttNbHoanUng.trangThaiHoanThanhToan) &&
                thongTinCoBan?.maNganHang == dataMA_DOI_TAC_HOAN_TIEN &&
                !!thongTinCoBan?.soTaiKhoan && (
                  <Button
                    type="default"
                    onClick={onCapNhatHoanNganHang}
                    minWidth={105}
                  >
                    {t("thuNgan.capNhatHoanNganHang")}
                  </Button>
                )}
              {showBtnCapNhatGiaoDichQr && (
                <Button
                  type="default"
                  minWidth={105}
                  onClick={onCapNhatTrangThaiGiaoDichQr}
                >
                  {t("thuNgan.capNhatQR")}
                </Button>
              )}
            </div>
          )}
          {bienBanDieuChinhHoaDonId &&
            checkRole([ROLES["THU_NGAN"].HOA_DON_CHUYEN_DOI]) && (
              <div className="bottom-group">
                <Button
                  type="default"
                  onClick={onClickHoaDonChuyenDoi}
                  minWidth={105}
                >
                  {t("thuNgan.hoaDonChuyenDoi")}
                </Button>
              </div>
            )}
        </div>

        <ModalPhuongThucThanhToan ref={refModalPhuongThucThanhToan} />
        <ModalDichVuThanhToan ref={refModalDichVuThanhToan} />
        <ModalSignPrint ref={refModalSignPrint} />
        <ModalSinhPhieuChi ref={refNhapLyDo} />
        <ModalChonTieuChiBangKe ref={refModalChonTieuChiBangKe} />
        <ModalThongTinHoaDon
          ref={refModalThongTinHoaDon}
          listPhieu={listPhieu}
          listDsPhieuThu={listDsPhieuThu}
          onPrintPhieu={onPrintPhieu}
        />
        <ModalDayQuyetToanBYHT ref={refModalDayQuyetToanBYHT} />
        <ModalCheckBaoHiem ref={refModalCheckBaoHiem} isShowButtonOk={true} />
        <ModalNhapLyDoHuyThanhToan ref={refNhapLyDoHuyThanhToan} />
        <ModalNhapLyDoTuChoiDuyetChiPhi
          ref={refModalNhapLyDoTuChoiDuyetChiPhi}
        />
        <ModalTaoQrCode
          nbDotDieuTriId={nbDotDieuTriId}
          ref={refModalTaoQrCode}
        />
        <ModalTaoQrCodeLoi ref={refModalTaoQrCodeLoi} />
        <ModalThongBaoThanhToanQrCode ref={refModalThongBaoThanhToanQrCode} />
        <ModalXacNhanHuyThanhToan
          layerId={layerId}
          isHuyHoaDonKhiHuyThanhToanPT={isHuyHoaDonKhiHuyThanhToanPT}
          ref={refModalXacNhanHuyThanhToan}
        />
        <ModalXacNhanBhyt
          ref={refModalXacNhanBhyt}
          handleAfterSubmit={handleAfterSubmit}
        />
        <ModalInPhieuTomTatBenhAn ref={refModalInPhieuTomTatBA} />
      </Spin>
    </Main>
  );
}

export default ThongTinPhieuThu;
