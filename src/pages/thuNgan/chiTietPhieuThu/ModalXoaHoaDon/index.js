import React, { forwardRef, useImperativeHandle, useRef } from "react";
import { Main } from "./styled";
import { useState } from "react";
import TextArea from "antd/lib/input/TextArea";
import { Button } from "antd";
import { useTranslation } from "react-i18next";

const ModalXoaHoaDon = (props, ref) => {
  const [state, _setState] = useState({
    show: false,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const { t } = useTranslation();
  const refCallback = useRef(null);
  useImperativeHandle(ref, () => ({
    show: (data = {}, callback) => {
      setState({
        show: true,
        thongTinHoaDon: data,
      });
      refCallback.current = callback;
    },
  }));
  const handleOk = () => {
    refCallback.current(state.thongTinHoaDon?.id, state.lyDo);
    setState({ show: false });
  };

  const handleChangeLiDo = (e) => {
    setState({
      lyDo: e.target.value,
    });
  };
  const handleCancel = () => {
    setState({
      show: false,
    });
  };
  return (
    <Main
      title={"Xác nhận xóa hóa đơn"}
      visible={state.show}
      onOk={handleOk}
      onCancel={handleCancel}
      footer={null}
      closable={false}
    >
      <div className="body">
        <div>
          <div className="f-w-600">{t("thuNgan.liDoXoaBo")}</div>
          <TextArea
            onChange={handleChangeLiDo}
            placeholder={t("thuNgan.nhapLiDoXoaBo")}
          ></TextArea>
        </div>
        <div className="footer">
          <Button className="btn btn-cancel" onClick={handleCancel}>
            {t("common.huy")}
          </Button>
          <Button className="btn btn-ok" onClick={handleOk}>
            {t("common.xoa")}
          </Button>
        </div>
      </div>
    </Main>
  );
};

export default forwardRef(ModalXoaHoaDon);
