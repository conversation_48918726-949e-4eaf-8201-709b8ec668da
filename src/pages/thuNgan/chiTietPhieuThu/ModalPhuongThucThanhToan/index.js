import React, {
  useState,
  useEffect,
  useMemo,
  forwardRef,
  useImperativeHandle,
  useRef,
} from "react";
import { Row, Col, Input, message } from "antd";
import { Main } from "./styled";
import { useSelector, useDispatch } from "react-redux";
import { firstLetterWordUpperCase, isArray, sleep } from "utils";
import { useTranslation } from "react-i18next";
import { useConfirm, useEnum, useLoading, useStore, useThietLap } from "hooks";
import {
  ENUM,
  HOTKEY,
  THIET_LAP_CHUNG,
  TRANG_THAI_NB_GOI_DV,
  LOAI_PHIEU_THU,
  LOAI_PHUONG_THUC_TT,
  LOAI_DOI_TAC,
  DINH_DANG_HOA_DON,
  TRANG_THAI_NB,
  ROLES,
} from "constants/index";
import {
  Checkbox,
  ModalTemplate,
  Button,
  Select,
  InputTimeout,
  Tooltip,
} from "components";
import PhuongThucThanhToan from "pages/goiDichVu/ChiTietNguoiBenhSuDungGoi/ModalThemMoiPhieuThuThanhToan/PhuongThucThanhToan";
import { SVG } from "assets";
import { isEqual } from "lodash";
import { addDefaultNganHang } from "./utils";
import { checkRole } from "lib-utils/role-utils";

const ModalPhuongThucThanhToan = (props, ref) => {
  const { t } = useTranslation();
  const MESS_WARN = t("common.nbCoPhieuThuChuaTTOrPhieuHoanChuaDuyetHoan");
  const { showConfirm } = useConfirm();
  const { showLoading, hideLoading } = useLoading();
  const refModal = useRef(null);
  const refCallback = useRef(null);
  const refBeforeProcessing = useRef(null);
  const refAfterProcessing = useRef(null);
  const refF4Func = useRef(null);
  const refPhuongThucThanhToan = useRef();
  const refEnterFunc = useRef();
  const refTaoQr = useRef(null);
  const refPreviousVanTinValue = useRef(null);
  const refIsWaitConfirmHoanTamUng = useRef(false);

  const [dataDINH_DANG_XEM_HOA_DON] = useThietLap(
    THIET_LAP_CHUNG.DINH_DANG_XEM_HOA_DON
  );
  const [MA_PTTT_DUYET_CHI_PHI] = useThietLap(
    THIET_LAP_CHUNG.MA_PTTT_DUYET_CHI_PHI
  );

  const [dataMA_PTTT_CHUYEN_KHOAN] = useThietLap(
    THIET_LAP_CHUNG.MA_PTTT_CHUYEN_KHOAN
  );
  const [dataTU_DONG_CHECK_CO_HOAN_TAM_UNG, isLoadFinish] = useThietLap(
    THIET_LAP_CHUNG.TU_DONG_CHECK_CO_HOAN_TAM_UNG
  );

  const { listAllPhuongThucThanhToan } = useSelector(
    (state) => state.phuongThucTT
  );
  const { nbDangThucHien } = useSelector((state) => state.danhSachPhieuThu);
  const { listNbGoiDv } = useSelector((state) => state.nbGoiDv);
  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan", {});
  const thongTinPhieuThu = useStore("thuNgan.thongTinPhieuThu", {});
  const { tienTamUng } = thongTinPhieuThu;
  const listAllDoiTacThanhToan = useStore("doiTac.listAllDoiTacThanhToan", []);
  const [state, _setState] = useState({
    isTamUng: false,
    dsPhuongThucTt: {},
    orgDsPhuongThucTt: [],
    showErrPtTtHoanUng: false,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const [thongTinHoanTamUng, setThongTinHoanTamUng] = useState({});

  const {
    dsHoaDonDienTu: { inHoaDon },
    thuNgan: {
      thanhToanPhieuThu,
      getThongTinPhieuThu,
      getThongTinHoanUng,
      huyQrThanhToan,
    },
    phuongThucTT: { getListAllPhuongThucThanhToan },
    nbDvHoan: { vanTinTenTK },
    nbDotDieuTri: { onUpdate: updateThongTinTK },
    phuongThucTT: { getListPhuongThucTTQRHoaDon },
  } = useDispatch();

  const onConfirmHoanTamUng = () => {
    refIsWaitConfirmHoanTamUng.current = true;

    showConfirm(
      {
        title: t("common.canhBao"),
        content: `${t(
          "thuNgan.nbCoTonTaiPhieuTamUngChuaHoanBanCoMuonHoanUngKhong"
        )}`,
        cancelText: t("common.quayLai"),
        okText: t("common.xacNhan"),
        typeModal: "warning",
        showBtnOk: true,
      },
      () => {
        setState({
          isTamUng: true,
        });
        refIsWaitConfirmHoanTamUng.current = false;
      },
      () => {
        refIsWaitConfirmHoanTamUng.current = false;
      }
    );
  };

  useEffect(() => {
    if (state?.show && isLoadFinish) {
      if (state.isDuyetPhieuThu && typeof state?.dataHoanUng === "boolean") {
        setState({
          isTamUng: state?.dataHoanUng,
        });
      }

      if (
        tienTamUng > 0 &&
        ![LOAI_PHIEU_THU.NGOAI_DIEU_TRI, LOAI_PHIEU_THU.NHA_THUOC].includes(
          state.loaiPhieuThu
        )
      ) {
        if (dataTU_DONG_CHECK_CO_HOAN_TAM_UNG?.eval()) {
          //check thêm điều kiện trạng thái NB ==100
          if (thongTinCoBan?.trangThaiNb == TRANG_THAI_NB.DA_RA_VIEN) {
            setState({
              isTamUng: true,
            });
          } else if (
            !checkRole([ROLES["THU_NGAN"].XAC_NHAN_BHYT]) &&
            !thongTinCoBan?.maThe
          ) {
            onConfirmHoanTamUng();
          }
        } else {
          setState({
            isTamUng: true,
          });
        }
      }
    }
  }, [
    tienTamUng,
    state.loaiPhieuThu,
    state.doiTuongKcb,
    state?.show,
    state.dataHoanUng,
    state.isDuyetPhieuThu,
    thongTinCoBan?.trangThaiNb,
    thongTinCoBan?.maThe,
    isLoadFinish,
  ]);

  const tienMatObj = useMemo(
    () => listAllPhuongThucThanhToan.find((item) => item?.tienMat),
    [listAllPhuongThucThanhToan]
  );

  const dsNbGoiDv = useMemo(() => {
    return listNbGoiDv.filter(
      (item) =>
        ![
          TRANG_THAI_NB_GOI_DV.DUNG_SU_DUNG,
          TRANG_THAI_NB_GOI_DV.KET_THUC,
        ].includes(item.trangThai)
    );
  }, [listNbGoiDv]);

  useEffect(() => {
    setState({ nbGoiDvId: dsNbGoiDv[0]?.id });
  }, [dsNbGoiDv]);

  const [dataInPhieuHoanKhiThanhToan] = useThietLap(
    THIET_LAP_CHUNG.IN_PHIEU_HOAN_KHI_THANH_TOAN
  );

  const [dataInPhieuThuKhiThanhToan] = useThietLap(
    THIET_LAP_CHUNG.IN_PHIEU_THU_KHI_THANH_TOAN
  );
  const [dataPHAT_HANH_HOA_DON_KHI_THANH_TOAN_NGOAI_TRU] = useThietLap(
    THIET_LAP_CHUNG.PHAT_HANH_HOA_DON_KHI_THANH_TOAN_NGOAI_TRU
  );

  const [dataIN_HOA_DON_KHI_XUAT_HD] = useThietLap(
    THIET_LAP_CHUNG.IN_HOA_DON_KHI_XUAT_HD
  );

  const [dataMA_DOI_TAC_QRCODE_MAC_DINH] = useThietLap(
    THIET_LAP_CHUNG.MA_DOI_TAC_QRCODE_MAC_DINH
  );

  const [dataMA_DOI_TAC_HOAN_TIEN] = useThietLap(
    THIET_LAP_CHUNG.MA_DOI_TAC_HOAN_TIEN
  );

  const [dataMAC_DINH_SO_TIEN_NBPT_KHI_DUNG_MOT_PTTT] = useThietLap(
    THIET_LAP_CHUNG.MAC_DINH_SO_TIEN_NBPT_KHI_DUNG_MOT_PTTT
  );

  const [listGioiTinh] = useEnum(ENUM.GIOI_TINH);

  const isInPhieuThuKhiThanhToan = useMemo(() => {
    if (dataInPhieuThuKhiThanhToan) {
      if (!dataInPhieuThuKhiThanhToan.eval()) return false;
      if (
        dataInPhieuThuKhiThanhToan.eval() == true ||
        dataInPhieuThuKhiThanhToan.eval() == 1
      )
        return true;
      if (dataInPhieuThuKhiThanhToan.eval() == 2)
        return [1, 5, 7, 8, 10].includes(state.doiTuongKcb);
      if (dataInPhieuThuKhiThanhToan.eval() == 3)
        return [2, 3, 4, 6, 9].includes(state.doiTuongKcb);
    }
    return false;
  }, [dataInPhieuThuKhiThanhToan, state.doiTuongKcb]);

  const gioiTinh = useMemo(() => {
    return (
      listGioiTinh.find((item) => item.id === thongTinCoBan?.gioiTinh) || {}
    );
  }, [listGioiTinh, thongTinCoBan]);

  useEffect(() => {
    if (state.show) {
      getListAllPhuongThucThanhToan({ page: "", size: "" });
      getListPhuongThucTTQRHoaDon({ active: true });
    }
  }, [state.show]);

  useEffect(() => {
    if (thongTinCoBan.id && state.show) {
      setThongTinHoanTamUng((prev) => ({
        ...prev,
        nganHangNbId: thongTinCoBan.nganHangId,
        tenTaiKhoan: thongTinCoBan.tenTaiKhoan,
        soTaiKhoan: thongTinCoBan.soTaiKhoan,
      }));
    }
  }, [thongTinCoBan, state.show]);

  useImperativeHandle(ref, () => ({
    show: (
      {
        tongTien,
        phieuThuId,
        nbDotDieuTriId,
        maChuanChi,
        nganHangId,
        nhaTamUngId,
        quayId,
        caLamViecId,
        loaiPhieuThu,
        doiTuongKcb,
        phuongThucMacDinhId,
        boQuaChuaKetLuanKham,
        boQuaTheLoi,
        sinhPhieuThuTamUng,
        isDuyetChiPhi,
        isDuyetPhieuThu,
        dsPhuongThucTt,
        hoanUng,
        beforeProcessing,
        afterProcessing,
        phuongThucTtIdTamUng,
        nganHangIdTamUng,
        isHuyQrCode,
        qrThanhToan,
      },
      callback,
      onTaoQr
    ) => {
      setState({
        phieuThuId,
        nbDotDieuTriId,
        maChuanChi,
        nganHangId,
        tongTien,
        isTamUng: false,
        show: true,
        tamUngLieuTrinh: false,
        phuongThucTtGoi: null,
        nhaTamUngId,
        quayId,
        caLamViecId,
        loaiPhieuThu,
        doiTuongKcb,
        phuongThucMacDinhId,
        boQuaChuaKetLuanKham,
        boQuaTheLoi,
        sinhPhieuThuTamUng,
        isDuyetChiPhi,
        isDuyetPhieuThu,
        orgDsPhuongThucTt: dsPhuongThucTt,
        dataHoanUng: hoanUng,
        phuongThucTtIdTamUng: phuongThucTtIdTamUng || tienMatObj?.id,
        isHuyQrCode,
        qrThanhToan,
      });

      setThongTinHoanTamUng((prev) => ({
        ...prev,
        nganHangId: nganHangIdTamUng,
      }));

      refBeforeProcessing.current = beforeProcessing;
      refAfterProcessing.current = afterProcessing;
      refCallback.current = callback;
      refTaoQr.current = onTaoQr;
    },
  }));
  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  const onUpdateThongTinTK = async () => {
    try {
      showLoading();

      await updateThongTinTK({
        id: thongTinCoBan.id,
        nganHangId: thongTinHoanTamUng.nganHangNbId,
        tenTaiKhoan: thongTinHoanTamUng.tenTaiKhoan,
        soTaiKhoan: thongTinHoanTamUng.soTaiKhoan,
      });
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onOk = (isOk) => async () => {
    if (isOk) {
      let tongTienPTTT = tienPTTT; // Tổng tiền các PTTT
      let isQrCode = false;
      if (tongTienPTTT >= state.tongTien) {
        let tongTien2 = 0,
          pttQrId = "",
          bodyThanhToan;
        try {
          if (
            state.isTamUng &&
            tienTamUng >= state.tongTien &&
            !state.phuongThucTtIdTamUng
          ) {
            setState({ showErrPtTtHoanUng: true });
            message.error(t("thuNgan.vuiLongNhapPhuongThucThanhToan"));
            return;
          }

          const payload = Object.keys(state.dsPhuongThucTt).map((key) => {
            let tienPhuongThucTT = 0;
            if (key == tienMatObj?.id) {
              let tongTienPTTKhac =
                tongTienPTTT - state.dsPhuongThucTt[key].tongTien;
              tienPhuongThucTT =
                state.tongTien < tongTienPTTKhac
                  ? 0
                  : state.tongTien - tongTienPTTKhac || 0;
            } else {
              tienPhuongThucTT = state.dsPhuongThucTt[key].tongTien || 0;
            }
            if (state.tamUngLieuTrinh) {
              if (tongTien2 + tienPhuongThucTT > state.tongTien) {
                tienPhuongThucTT = state.tongTien - tongTien2;
                tongTien2 = state.tongTien;
              } else {
                tongTien2 += tienPhuongThucTT;
              }
            }
            return {
              phuongThucTtId: key,
              tongTien: tienPhuongThucTT,
              maChuanChi: state.dsPhuongThucTt[key].maChuanChi,
              nganHangId: state.dsPhuongThucTt[key].nganHangId,
            };
          });
          if (isArray(payload, true)) {
            const listAllPttQr = [];
            for (const item of payload) {
              const pttt = (listAllPhuongThucThanhToan || []).find(
                (i) => i.id === +item.phuongThucTtId
              );
              if (pttt?.loaiPhuongThucTt === LOAI_PHUONG_THUC_TT.QR_CODE) {
                listAllPttQr.push(item);
              }
            }
            const batBuocNganHang = listAllPttQr.some(
              (i) => i.tongTien > 0 && !i.nganHangId
            );
            if (batBuocNganHang) {
              message.error(t("thuNgan.vuiLongChonTenNganHang"));
              return;
            }
          }
          showLoading();

          if (state.isTamUng && tienTamUng >= state.tongTien) {
            onUpdateThongTinTK();
          }
          if (typeof refBeforeProcessing.current == "function")
            await refBeforeProcessing.current();

          if (state.isHuyQrCode && state.qrThanhToan?.thanhToanId) {
            await huyQrThanhToan({
              thanhToanId: state.qrThanhToan.thanhToanId,
            });
            await sleep(300);
          }

          const isHoanUng = state.isTamUng && tienTamUng >= state.tongTien;
          const isNoiTru = [2, 3, 4, 6, 9].includes(state.doiTuongKcb);
          const isNoiTruPrint = isNoiTru ? !isHoanUng : true;

          const res = await thanhToanPhieuThu({
            id: state.phieuThuId,
            dsPhuongThucTt: payload,
            nbLaySoId:
              state.nbDotDieuTriId == nbDangThucHien?.nbDotDieuTriId
                ? nbDangThucHien?.id
                : null,
            nbDotDieuTriId: state.nbDotDieuTriId,
            nhaThuNganId: state.nhaTamUngId,
            quayId: state.quayId,
            caLamViecId: state.caLamViecId,
            hoanUng: state.isTamUng ? true : null,
            tongTien: state.tongTien,
            tongTienPTTT,
            nbGoiDvId: state.nbGoiDvId,
            tamUngLieuTrinh: state.tamUngLieuTrinh,
            phuongThucTtGoi: state.phuongThucTtGoi,
            inPhieuHoanKhiThanhToan:
              (dataInPhieuHoanKhiThanhToan || "").toLowerCase() === "true",
            inPhieuThuKhiThanhToan: isInPhieuThuKhiThanhToan, //bỏ điều kiện hoàn ứng
            // inPhieuThuKhiThanhToan: isInPhieuThuKhiThanhToan && isNoiTruPrint,
            ghiChu: state?.tamUngLieuTrinh ? "Thanh toán mới" : "",
            boQuaChuaKetLuanKham: state?.boQuaChuaKetLuanKham,
            boQuaTheLoi: state?.boQuaTheLoi,
            sinhPhieuThuTamUng: state.sinhPhieuThuTamUng,
            isDuyetPhieuThu: state.isDuyetPhieuThu,
            isPhatHanhHoaDonKhiThanhToanNgoaiTru:
              isNoiTruPrint &&
              dataPHAT_HANH_HOA_DON_KHI_THANH_TOAN_NGOAI_TRU &&
              parseInt(dataPHAT_HANH_HOA_DON_KHI_THANH_TOAN_NGOAI_TRU) > 0 &&
              dataIN_HOA_DON_KHI_XUAT_HD.eval(),
            ...(isHoanUng
              ? {
                  nbHoanUng: {
                    maChuanChi: state.maChuanChiTamUng,
                    phuongThucTtId: state.phuongThucTtIdTamUng,
                    nganHangNbId: thongTinHoanTamUng.nganHangNbId,
                    soTaiKhoan: thongTinHoanTamUng.soTaiKhoan,
                    tenTaiKhoan: thongTinHoanTamUng.tenTaiKhoan,
                    nganHangId: thongTinHoanTamUng.nganHangId,
                  },
                }
              : {}),
            tienNbDua,
            dataDinhDangXemHoaDon: dataDINH_DANG_XEM_HOA_DON,
            callback: () => {
              if (typeof refAfterProcessing.current == "function") {
                refAfterProcessing.current();
              }
            },
          });

          if ((res?.data?.[0]?.message || "").indexOf(MESS_WARN) >= 0) {
            showConfirm(
              {
                title: t("common.thongBao"),
                content: `${res?.data?.[0]?.message || ""}`,
                cancelText: t("common.dong"),
                typeModal: "warning",
                showImg: true,
                classNameOkText: "button-warning",
              },
              () => {}
            );
          } else if (res?.data?.[0]?.message) {
            message.error(res?.data?.[0]?.message);
          }

          if (state.isTamUng && tienTamUng >= state.tongTien) {
            getThongTinHoanUng({
              page: 0,
              size: 10,
              nbDotDieuTriId: state.nbDotDieuTriId,
              phieuThuId: state.phieuThuId,
              dsTrangThai: 60,
            });
          }

          if (isArray(res._dsPtt, true)) {
            const dsPtt = res._dsPtt.find(
              (i) =>
                i.phuongThucTt.loaiPhuongThucTt === LOAI_PHUONG_THUC_TT.QR_CODE
            );
            if (dsPtt) {
              isQrCode = dsPtt.tongTien > 0 && dsPtt.nganHangId;
              pttQrId = dsPtt.id;
              bodyThanhToan = res.bodyThanhToan;
            }
          }
          hideLoading();
        } catch (error) {
          hideLoading();
          return;
        }
        if (isQrCode) {
          refTaoQr.current &&
            refTaoQr.current({
              nbDotDieuTriId: state.nbDotDieuTriId,
              loai: 20,
              tuBanGhiId: pttQrId,
              bodyThanhToan,
            });
        }
      }
      refCallback.current &&
        refCallback.current({
          tienDaNhan: isTamUngVaThieuTamUng
            ? tongTienPTTT + tienTamUng
            : tongTienPTTT,
          tongTien: state.tongTien,
          dsPhuongThucTt: state.dsPhuongThucTt,
          hoanUng: state.isTamUng ? true : null,
          isQrCode,
        });
      onOk(false)();
    } else {
      setState({ show: false, showErrPtTtHoanUng: false });
    }
  };
  refF4Func.current = onOk;

  const tienPTTT = useMemo(() => {
    let total = 0;
    Object.keys(state.dsPhuongThucTt).forEach((ifp) => {
      total += state.dsPhuongThucTt[ifp].tongTien || 0;
    });
    return Math.round(total * 100) / 100;
  }, [state.dsPhuongThucTt]);

  const tienNbDua = useMemo(() => {
    if (state.isTamUng && tienTamUng - state.tongTien > 0) {
      return 0;
    } else {
      return tienPTTT;
    }
  }, [state.isTamUng, tienTamUng, state.tongTien, tienPTTT]);

  // const tongTienTamUng = useMemo(() => {
  //   return tienNbDua + tienTamUng - state.tongTien;
  // }, [tienNbDua, tienTamUng, state.tongTien]);

  const hotKeys = [
    {
      keyCode: HOTKEY.ESC,
      onEvent: () => {
        onOk(false)();
      },
    },
    {
      keyCode: HOTKEY.F4,
      onEvent: () => {
        //nếu đang chờ confirm hoàn tạm ứng thì ko cho ấn F4 để thanh toán
        if (refIsWaitConfirmHoanTamUng.current) {
          return;
        }
        // onOk(true)();
        refF4Func.current && refF4Func.current(true)();
      },
    },
    {
      keyCode: HOTKEY.F2,
      onEvent: () => {
        refPhuongThucThanhToan.current &&
          refPhuongThucThanhToan.current.handleFocus();
      },
    },
    {
      keyCode: HOTKEY.ENTER,
      onEvent: () => {
        refEnterFunc.current && refEnterFunc.current();
        return true;
      },
    },
  ];

  const nbGoiDv = useMemo(() => {
    return dsNbGoiDv.find((item) => item.id == state.nbGoiDvId);
  }, [state.tamUngLieuTrinh, state.nbGoiDvId, dsNbGoiDv]);

  const tienSauGiamGia = useMemo(() => {
    return (
      (nbGoiDv?.tongTien || 0) -
      (nbGoiDv?.tienMienGiamDichVu || 0) -
      (nbGoiDv?.tienMienGiamGoiDv || 0) -
      (nbGoiDv?.tienGiamGia || 0)
    );
  }, [nbGoiDv]);

  const soTienPhaiGoiThanhToan = useMemo(() => {
    return tienSauGiamGia - (nbGoiDv?.tienDaThanhToan || 0);
  }, [tienSauGiamGia, nbGoiDv]);

  const onChangePhuongThucThanhToan = (dsPhuongThucTt) => {
    setState({ dsPhuongThucTt: { ...dsPhuongThucTt } });
  };
  const soTienTTGoi = useMemo(() => {
    return tienPTTT - state.tongTien;
  }, [tienPTTT, state.tongTien]);

  const tienThuThemNb = useMemo(() => {
    return (state.tongTien || 0) - (tienTamUng || 0);
  }, [state.tongTien, tienTamUng]);

  const isTamUngVaThieuTamUng = useMemo(() => {
    return state.isTamUng && (state.tongTien || 0) - (tienTamUng || 0) >= 0;
  }, [state.isTamUng, state.tongTien, tienTamUng]);

  const tienTraLaiNb = useMemo(() => {
    if (state.tamUngLieuTrinh) return 0;
    if (state.isTamUng && tienTamUng > state.tongTien)
      return tienTamUng - state.tongTien;
    let amountReturn = 0;
    let tongTienPhuongThucTT = tienPTTT;
    amountReturn =
      state.tongTien < tongTienPhuongThucTT
        ? tongTienPhuongThucTT - state.tongTien
        : 0;

    return Math.round(amountReturn * 100) / 100;
  }, [
    tienPTTT,
    state.tamUngLieuTrinh,
    state.isTamUng,
    tienTamUng,
    state.tongTien,
  ]);

  useEffect(() => {
    if (state.tongTien && state.show) {
      const ptTtQrCode = listAllPhuongThucThanhToan.find(
        (x) => x?.loaiPhuongThucTt === LOAI_PHUONG_THUC_TT.QR_CODE
      );
      const macDinhQrNganHang = (ptTtQrCode?.dsNhaCungCap || []).find(
        (x) => x.ma === dataMA_DOI_TAC_QRCODE_MAC_DINH
      );
      let addQrPtTt = {};

      if (macDinhQrNganHang) {
        addQrPtTt = {
          [ptTtQrCode.id]: {
            nganHangId: macDinhQrNganHang.id,
          },
        };

        refPhuongThucThanhToan.current &&
          refPhuongThucThanhToan.current.onGetListTaiKhoan(
            macDinhQrNganHang.id
          );
      }

      if (state.isDuyetChiPhi) {
        const dsPhuongThucTt = listAllPhuongThucThanhToan.filter((item) => {
          return MA_PTTT_DUYET_CHI_PHI.split(",").includes(item.ma);
        });
        if (
          dsPhuongThucTt.length === 2 &&
          dsPhuongThucTt.find((item) => item.tienMat)
        ) {
          const pttt = dsPhuongThucTt.find((item) => !item.tienMat);
          const ptttTienMat = dsPhuongThucTt.find((item) => item.tienMat);
          setState({
            dsPhuongThucTt: addDefaultNganHang(
              {
                [pttt.id]: {
                  tongTien: state.tongTien - tienTamUng,
                  nganHangId:
                    pttt.dsNhaCungCapId?.length === 1
                      ? pttt.dsNhaCungCapId[0]
                      : null,
                },
                [ptttTienMat.id]: {
                  tongTien: tienTamUng,
                },
              },
              addQrPtTt
            ),
          });
        } else {
          let id = (dsPhuongThucTt || []).find((item) => item.tienMat)?.id;
          if (!id) {
            id = (listAllPhuongThucThanhToan || []).find(
              (item) => item.tienMat
            )?.id;
          }
          if (id) {
            setState({
              dsPhuongThucTt: addDefaultNganHang(
                { [id]: { tongTien: tienTamUng } },
                addQrPtTt
              ),
            });
          } else {
            setState({
              dsPhuongThucTt: addDefaultNganHang({}, addQrPtTt),
            });
          }
        }
      } else if (state.isDuyetPhieuThu) {
        let dsPtt = {};
        for (const item of listAllPhuongThucThanhToan) {
          let _dsPtt = state.orgDsPhuongThucTt.find(
            (i) => i.phuongThucTtId === item.id
          );
          if (_dsPtt) {
            dsPtt = {
              ...dsPtt,
              [item.id]: {
                tongTien: _dsPtt.tongTien,
                nganHangId: _dsPtt.nganHangId,
                trangThaiThanhToan: _dsPtt.trangThaiThanhToan,
                maChuanChi:
                  _dsPtt.loaiPhuongThucTt === LOAI_PHUONG_THUC_TT.QR_CODE
                    ? _dsPtt.soPhieuThanhToan || _dsPtt.maChuanChi
                    : _dsPtt.maChuanChi,
              },
            };
          }
        }
        setState({
          dsPhuongThucTt: addDefaultNganHang(dsPtt, addQrPtTt),
        });
      } else {
        let _ptttTienMatId = listAllPhuongThucThanhToan.find(
          (item) => item.tienMat
        )?.id;
        const loaiNganHang = listAllPhuongThucThanhToan.find(
          (x) => x.loaiDoiTac == LOAI_DOI_TAC.THANH_TOAN
        );
        const id = state.phuongThucMacDinhId || _ptttTienMatId;

        //TU_DONG_THANH_TOAN_TIEN_MAT = true
        //Tiền nb phải trả của phiếu thu > Tiền đã tạm ứng ( Hoặc là trên UI là Tiền thu thêm # 0)
        //Chọn phương thức thanh toán # Tiền mặt
        //Có hoàn ứng <trên popup thanh toán> = true
        if (
          state.phuongThucMacDinhId &&
          state.phuongThucMacDinhId !== _ptttTienMatId &&
          state.isTamUng &&
          tienThuThemNb > 0 &&
          !dataMAC_DINH_SO_TIEN_NBPT_KHI_DUNG_MOT_PTTT.eval()
        ) {
          setState({
            dsPhuongThucTt: addDefaultNganHang(
              {
                [id]: { tongTien: tienThuThemNb },
                [_ptttTienMatId]: { tongTien: tienTamUng },
              },
              addQrPtTt
            ),
          });
        } else {
          setState({
            dsPhuongThucTt: addDefaultNganHang(
              {
                [id]: {
                  tongTien: state.tongTien,
                  ...(listAllDoiTacThanhToan?.length === 1 && loaiNganHang
                    ? { nganHangId: listAllDoiTacThanhToan[0]?.id }
                    : {}),
                },
              },
              addQrPtTt
            ),
          });
        }
      }
    }
  }, [
    state.tongTien,
    listAllPhuongThucThanhToan,
    state.show,
    state.isDuyetChiPhi,
    state.isDuyetPhieuThu,
    tienTamUng,
    state.orgDsPhuongThucTt,
    state.isTamUng,
    tienThuThemNb,
    listAllDoiTacThanhToan,
  ]);

  const onChangeGoi = (value) => {
    setState({ nbGoiDvId: value });
  };

  const onChangePhuongThucTTGoi = (value) => {
    setState({ phuongThucTtGoi: value });
  };

  const handleChangeCheckbox = () => {
    if (
      document.getElementById("checkBoxPhuongThucTt") === document.activeElement
    ) {
      setState({ isTamUng: !state.isTamUng });
    }
  };

  refEnterFunc.current = handleChangeCheckbox;

  const tuoi =
    thongTinCoBan?.thangTuoi > 36 || thongTinCoBan?.tuoi
      ? `${thongTinCoBan?.tuoi} ${t("common.tuoi")}`
      : thongTinCoBan?.thangTuoi
      ? `${thongTinCoBan?.thangTuoi} ${t("common.thang")}`
      : "";

  const dsPtTtGoi = useMemo(() => {
    const ptttActive = Object.keys(state?.dsPhuongThucTt || {}).filter(
      (x) => !!state.dsPhuongThucTt[x].tongTien
    );

    if (ptttActive && ptttActive.length == 1) {
      setState({
        phuongThucTtGoi: ptttActive[0],
      });
    }
    return listAllPhuongThucThanhToan.filter((x) =>
      ptttActive.includes(x.id.toString())
    );
  }, [state.dsPhuongThucTt]);

  const soTienConLai = useMemo(() => {
    if (state.isTamUng) {
      let tienPhuongThucTT = 0;
      Object.values(state.dsPhuongThucTt).forEach((item) => {
        tienPhuongThucTT += item.tongTien;
      });
      return state.tongTien - tienTamUng - tienPhuongThucTT;
    }
    return state.tongTien - tienNbDua;
  }, [
    state.tongTien,
    state.dsPhuongThucTt,
    tienNbDua,
    tienTamUng,
    state.isTamUng,
  ]);

  const onChangeValueTamUng = (key) => (e) => {
    const _selectedPtTt = listAllPhuongThucThanhToan.find((x) => x.id === e);
    const _isPtTtChuyenKhoan = _selectedPtTt?.ma == dataMA_PTTT_CHUYEN_KHOAN;
    if (_isPtTtChuyenKhoan) {
      const _listSelectNganHang = _selectedPtTt?.dsNhaCungCap || [];
      const _selectedNganHang = _listSelectNganHang.find(
        (x) => x.ma == dataMA_DOI_TAC_HOAN_TIEN
      );

      setThongTinHoanTamUng((prev) => ({
        ...prev,
        nganHangId: _selectedNganHang?.id,
      }));
    }

    setState({
      [key]: e,
      ...(key === "phuongThucTtIdTamUng" && !!e
        ? { showErrPtTtHoanUng: false }
        : {}),
    });
  };

  const onVanTinTenTK = async ({ soTaiKhoan, nganHangNbId }) => {
    try {
      showLoading();
      const res = await vanTinTenTK({
        soTaiKhoan,
        nganHangId: nganHangNbId,
      });

      setThongTinHoanTamUng((prev) => ({
        ...prev,
        tenTaiKhoan: res?.data?.tenTaiKhoan || "",
      }));
    } catch (error) {
      console.error(error);
      setThongTinHoanTamUng((prev) => ({
        ...prev,
        tenTaiKhoan: "",
      }));
    } finally {
      hideLoading();
    }
  };

  const watchAutoVanTin = ({ nganHangNbId }) => {
    const data = {
      nganHangNbId: nganHangNbId ?? thongTinHoanTamUng.nganHangNbId,
      soTaiKhoan: thongTinHoanTamUng.soTaiKhoan,
    };

    if (
      !isEqual(refPreviousVanTinValue.current, data) &&
      Object.values(data).every((x) => x)
    ) {
      onVanTinTenTK(data);
      refPreviousVanTinValue.current = data;
    }
  };

  const renderHoanTamUng = () => {
    const _phuongThucTt = listAllPhuongThucThanhToan.find(
      (item) => item.id === state.phuongThucTtIdTamUng
    );
    const isPtTtChuyenKhoan =
      _phuongThucTt?.ma && _phuongThucTt.ma == dataMA_PTTT_CHUYEN_KHOAN;

    return (
      <Row className="row-box" align={"top"} gutter={[10, 10]}>
        <Col span={12}>
          <div className="row-label">{t("thuNgan.soTienTraLai")}</div>
          <InputTimeout
            disabled
            value={(tienTamUng - state.tongTien).formatPrice()}
            style={{ textAlign: "right" }}
          />
        </Col>
        <Col span={12}>
          <div className="row-label">
            {t("thuNgan.phuongThucTt")} <span className="error">*</span>
          </div>
          <div className="row-group">
            <Select
              placeholder={t("thuNgan.chonPhuongThucThanhToan")}
              data={listAllPhuongThucThanhToan}
              value={state.phuongThucTtIdTamUng}
              onChange={onChangeValueTamUng("phuongThucTtIdTamUng")}
            />
          </div>
        </Col>

        {state.showErrPtTtHoanUng && (
          <Col span={12} offset={12}>
            <div className="error err-mess">
              {t("thuNgan.vuiLongNhapPhuongThucThanhToan")}!
            </div>
          </Col>
        )}

        {_phuongThucTt && !_phuongThucTt?.tienMat && (
          <>
            <Col span={12}>
              <div className="row-label">{t("thuNgan.nganHangChuyen")}</div>
              <div className="row-group">
                <Select
                  placeholder={t("thuNgan.chonNganHang")}
                  data={listAllDoiTacThanhToan}
                  value={thongTinHoanTamUng.nganHangId}
                  onChange={(e) => {
                    setThongTinHoanTamUng((prev) => ({
                      ...prev,
                      nganHangId: e,
                    }));
                  }}
                />
              </div>
            </Col>
            <Col span={12}>
              <div className="row-label">{t("thuNgan.nganHangNbNhan")}</div>
              <div className="row-group">
                <Select
                  placeholder={t("thuNgan.chonNganHang")}
                  data={listAllDoiTacThanhToan}
                  value={thongTinHoanTamUng.nganHangNbId}
                  onChange={(e) => {
                    setThongTinHoanTamUng((prev) => ({
                      ...prev,
                      nganHangNbId: e,
                    }));
                    watchAutoVanTin({ nganHangNbId: e });
                  }}
                />
              </div>
            </Col>
            {isPtTtChuyenKhoan && (
              <>
                <Col span={12}>
                  <div className="row-label">{t("thuNgan.soTK")}</div>
                  <InputTimeout
                    value={thongTinHoanTamUng.soTaiKhoan}
                    style={{ textAlign: "right" }}
                    onBlur={watchAutoVanTin}
                    onChange={(e) => {
                      setThongTinHoanTamUng((prev) => ({
                        ...prev,
                        soTaiKhoan: e,
                      }));
                    }}
                    timeDelay={0}
                  />
                </Col>
                <Col span={12}>
                  <div className="row-label">
                    {t("thuNgan.tenTK")}
                    {thongTinHoanTamUng.soTaiKhoan && (
                      <Tooltip title={t("khamBenh.capNhatThongTinTK")}>
                        <SVG.IcReload
                          className="cursor-pointer"
                          onClick={() =>
                            onVanTinTenTK({
                              soTaiKhoan: thongTinHoanTamUng.soTaiKhoan,
                              nganHangNbId: thongTinHoanTamUng.nganHangNbId,
                            })
                          }
                        />
                      </Tooltip>
                    )}
                  </div>
                  <InputTimeout
                    placeholder={t("thuNgan.tenTK")}
                    value={thongTinHoanTamUng.tenTaiKhoan}
                    disabled
                  />
                </Col>
              </>
            )}
            <Col span={12}>
              <div className="row-label">
                {t("goiDichVu.maChuanChi/maGiaoDich")}
              </div>
              <InputTimeout
                placeholder={t("goiDichVu.nhapMaChuanChi")}
                value={state.maChuanChiTamUng}
                onChange={onChangeValueTamUng("maChuanChiTamUng")}
                isTextArea
                autoSize
              />
            </Col>
          </>
        )}
      </Row>
    );
  };
  const renderContent = () => {
    if (state.isDuyetPhieuThu) {
      return (
        <>
          <PhuongThucThanhToan
            onChange={onChangePhuongThucThanhToan}
            dsPhuongThucTt={state.dsPhuongThucTt}
            ref={refPhuongThucThanhToan}
            isDuyetChiPhi={false}
            isDuyetPhieuThu={state.isDuyetPhieuThu}
            toaNhaId={state.nhaTamUngId}
            thongTinPhieuThu={thongTinPhieuThu}
          />
          <Checkbox
            id={"checkBoxPhuongThucTt"}
            style={{ paddingTop: "15px" }}
            onChange={() => {
              setState({ isTamUng: !state.isTamUng });
            }}
            checked={state.isTamUng}
          >
            {t("thuNgan.coHoanTamUng")}
          </Checkbox>
          {state.isTamUng && tienTamUng >= state.tongTien && renderHoanTamUng()}
        </>
      );
    }
    return (
      <>
        <div className="thong-tin-phieu-thu">
          <div className="box-left">
            {isTamUngVaThieuTamUng ? (
              <>
                <div className="info-price">
                  <div className="info-price__title">
                    {"Số tiền cần thu thêm"}
                  </div>
                  <div className="info-price__detail">
                    {(tienThuThemNb || 0).formatPrice()}đ
                  </div>
                </div>
              </>
            ) : (
              <>
                <div className="info-price">
                  <div className="info-price__title">
                    {t("thuNgan.tienNbDua")}
                  </div>
                  <div className="info-price__detail">
                    {tienNbDua.formatPrice()} đ
                  </div>
                </div>
                <div className="info-price">
                  <div className="info-price__title">
                    {t("thuNgan.tienTraLai")}
                  </div>
                  <div className="info-price__detail">
                    <span className="info-price__detail_tienTraLai">
                      {tienTraLaiNb.formatPrice()}đ
                    </span>
                  </div>
                </div>
                {soTienConLai > 0 && (
                  <div className="info-price">
                    <div className="info-price__title">
                      {t("thuNgan.soTienConLai")}
                    </div>
                    <div className="info-price__detail">
                      <span className="info-price__detail_tienTraLai">
                        {soTienConLai.formatPrice()}đ
                      </span>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
          <div className="box-right">
            <div className="info-price">
              <div className="info-price__title">
                {t("thuNgan.soTienPhieuThu")}
              </div>
              <div className="info-price__detail">
                {(state.tongTien || 0).formatPrice()} đ
              </div>
            </div>
            <div className="info-price">
              <div className="info-price__title">
                {t("thuNgan.tienTamUngConLai")}
              </div>
              <div className="info-price__detail">
                {(tienTamUng || 0).formatPrice()}đ
              </div>
            </div>
            {!isTamUngVaThieuTamUng && (
              <div className="info-price">
                <div className="info-price__title">
                  {t("thuNgan.tienHoanTamUng")}
                </div>
                <div className="info-price__detail">
                  {(tienTamUng || 0).formatPrice()}đ
                </div>
              </div>
            )}
          </div>
        </div>
        <div>
          {!!dsNbGoiDv?.length && (
            <div>
              <Checkbox
                style={{ paddingTop: "15px" }}
                onChange={() =>
                  setState({ tamUngLieuTrinh: !state.tamUngLieuTrinh })
                }
                checked={state.tamUngLieuTrinh}
              >
                {t("thuNgan.tamUngGoiLieuTrinh")}
              </Checkbox>
              {state.tamUngLieuTrinh && (
                <Row className="row-box" gutter={[8, 8]}>
                  <Col span={12}>
                    <div className="row-label">{`${t("thuNgan.chonGoi")}`}</div>

                    <Select
                      placeholder={t("thuNgan.chonGoi")}
                      data={dsNbGoiDv}
                      ten={"tenGoiDv"}
                      value={state.nbGoiDvId}
                      onChange={onChangeGoi}
                    ></Select>
                  </Col>
                  <Col span={12}>
                    {t("thuNgan.tongTienGoiPhaiNop")}:{" "}
                    {soTienPhaiGoiThanhToan?.formatPrice()}đ
                  </Col>
                  <Col span={12}>
                    <div className="row-label">{`${t(
                      "thuNgan.soTienTTGoi"
                    )}`}</div>

                    <Input
                      disabled={true}
                      className="soTienThanhToanGoi"
                      value={soTienTTGoi?.formatPrice()}
                    />
                  </Col>
                  <Col span={12}>
                    {t("thuNgan.tongTienPhaiNop")}:{" "}
                    {(soTienPhaiGoiThanhToan + state.tongTien)?.formatPrice()}đ
                  </Col>

                  {dsPtTtGoi && dsPtTtGoi.length > 1 && (
                    <Col span={12}>
                      <div className="row-label">{`${t(
                        "thuNgan.chonPTTTGoi"
                      )}`}</div>

                      <Select
                        placeholder={t("thuNgan.chonPTTTGoi")}
                        data={dsPtTtGoi}
                        valueNumber={true}
                        value={state.phuongThucTtGoi}
                        onChange={onChangePhuongThucTTGoi}
                      ></Select>
                    </Col>
                  )}
                </Row>
              )}
            </div>
          )}
        </div>
        <div className="text-note">
          <b>{t("common.luuY")}:</b> {t("thuNgan.qrPayMoMoKhongTheThanhToan")}
        </div>
        <PhuongThucThanhToan
          onChange={onChangePhuongThucThanhToan}
          dsPhuongThucTt={state.dsPhuongThucTt}
          ref={refPhuongThucThanhToan}
          isDuyetChiPhi={state.isDuyetChiPhi}
          tongTien={state.tongTien}
          toaNhaId={state.nhaTamUngId}
          thongTinPhieuThu={thongTinPhieuThu}
        />
        <Checkbox
          id={"checkBoxPhuongThucTt"}
          style={{ paddingTop: "15px" }}
          onChange={() => {
            setState({ isTamUng: !state.isTamUng });
          }}
          checked={state.isTamUng}
        >
          {t("thuNgan.coHoanTamUng")}
        </Checkbox>
        {state.isTamUng && tienTamUng >= state.tongTien && renderHoanTamUng()}
      </>
    );
  };

  return (
    <ModalTemplate
      width={1000}
      ref={refModal}
      title={t(
        state.isDuyetPhieuThu
          ? "thuNgan.duyetPhieuThu"
          : "thuNgan.phuongThucThanhToan"
      )}
      onCancel={onOk(false)}
      closable={false}
      rightTitle={
        <>
          <span className="font-color">
            {firstLetterWordUpperCase(thongTinCoBan?.tenNb)}
          </span>
          {gioiTinh.ten && (
            <span className="normal-weight">&nbsp;{`- ${gioiTinh.ten}`}</span>
          )}
          {tuoi && <span className="normal-weight">&nbsp;{`- ${tuoi}`}</span>}
        </>
      }
      actionLeft={
        <Button.QuayLai onClick={onOk(false)}>
          {t("common.quayLai")} [ESC]
        </Button.QuayLai>
      }
      actionRight={
        <Button
          type="primary"
          minWidth={100}
          rightIcon={<SVG.IcThanhToan />}
          iconHeight={15}
          onClick={onOk(true)}
        >
          {t("common.xacNhan")} [F4]
        </Button>
      }
      hotKeys={hotKeys}
    >
      <Main>{renderContent()}</Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalPhuongThucThanhToan);
