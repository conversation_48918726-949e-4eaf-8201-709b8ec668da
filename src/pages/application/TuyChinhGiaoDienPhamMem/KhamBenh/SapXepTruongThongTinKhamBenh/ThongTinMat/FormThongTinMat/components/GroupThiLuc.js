import React from "react";
import { Row, Col, Checkbox } from "antd";
import styled from "styled-components";
import { get } from "lodash";
import { Select, TextField } from "components";
import { SelectGroup } from "pages/khamBenh/KhamCoBan/styled";
import { useThongTinMat } from "../context";
import { generateTabIndex, reponsiveRow } from "../helpers";
import { useTranslation } from "react-i18next";

const Wrapper = styled.div`
  .row-thi-luc {
    min-height: 40px;
    .main-title {
      flex-direction: row !important;
      justify-content: unset !important;
    }

    .main-title,
    .sub-title {
      line-height: 40px;
    }
    .ant-col {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      min-height: 40px;
    }
  }
`;

const GroupTitle = styled.div`
  font-weight: bold;
`;

const TextFieldItem = ({
  value,
  onChange,
  tabIndex,
  maxLength,
  autoSize,
  autoHeight,
}) => {
  return (
    <TextField
      html={value ?? "" }
      onChange={onChange}
      maxLength={maxLength}
      allowClear={true}
      tabIndex={tabIndex}
      autoSize={autoSize}
      autoHeight={autoHeight}
    />
  );
};

const CheckboxItem = ({ value, onChange, tabIndex }) => {
  return (
    <Checkbox
      checked={value}
      onChange={(e) => onChange(e.target.checked)}
      tabIndex={tabIndex}
    />
  );
};

const DropListItem = ({ getLabel, value, data, onChange, tabIndex }) => {
  return (
    <SelectGroup marginTop={0}>
      <div className="select-box-chan-doan">
        <Select
          data={data}
          onChange={onChange}
          value={value}
          tabIndex={tabIndex}
          suffixIcon={null}
          style={{
            width: "100%",
          }}
          dropdownMatchSelectWidth={200}
          getLabel={getLabel}
          showAction={["focus"]}
        />
      </div>
    </SelectGroup>
  );
};

export const GroupThiLuc = ({
  title,
  subTitle,
  data,
  sectionIndex,
  rowIndex,
  isFirstInGroup,
}) => {
  const { t } = useTranslation();
  const { thongTinMat, onChange, containerRect } = useThongTinMat();

  // Format the title - if it's a React element, use it as is
  const formattedTitle = React.isValidElement(title) ? (
    title
  ) : isFirstInGroup ? (
    <GroupTitle>{t(title)}</GroupTitle>
  ) : title ? (
    t(title)
  ) : (
    ""
  );

  return (
    <Wrapper>
      <Row
        className="row-thi-luc"
        style={{ width: reponsiveRow(containerRect?.width) }}
        gutter={[16, 16]}
      >
        <Col className="main-title flex" flex="0 0 250px">
          {formattedTitle}
          {formattedTitle ? ":" : ""}
        </Col>
        <Col className="sub-title" flex="0 0 50px">
          {subTitle ? t(subTitle) + ":" : ""}
        </Col>
        {data?.map(({ key, type, colStyle, ...props }, index) => {
          const itemProps = {
            ...props,
            thongTinMat,
            onChange: onChange(key),
            value: get(thongTinMat, key),
            tabIndex: generateTabIndex(sectionIndex, rowIndex, index),
          };

          let Component;
          if (type === "dropList") {
            Component = DropListItem;
          } else if (type === "checkbox") {
            Component = CheckboxItem;
          } else {
            Component = TextFieldItem;
          }

          return (
            <Col
              key={key}
              flex="1"
              style={{
                borderRight:
                  index === data.length - 1 ? "none" : "1px solid #000",
                ...colStyle,
              }}
            >
              <Component dataIndex={key} {...itemProps} />
            </Col>
          );
        })}
      </Row>
    </Wrapper>
  );
};
