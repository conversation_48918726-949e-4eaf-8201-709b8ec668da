import React from "react";
import { useTranslation } from "react-i18next";
import { Col, Row, Form, Input } from "antd";
import { useEnum } from "hooks";
import { Checkbox } from "components";
import { isNumber } from "utils";
import { SVG } from "assets";
import { useRenderer } from "./hooks/useRenderer";
import { ENUM } from "constants/index";

const DefaultFieldRenderer = (props) => {
  const {
    fieldId,
    fieldConfig,
    tinhTrangTreSoSinhSauDe,
    parentState,
    fromSetting,
    isReadonly,
    getFieldDefinitionById,
    layout,
    form,
  } = props;

  const { renderComponent } = useRenderer({
    tinhTrangTreSoSinhSauDe,
  });

  const baseFieldDef = getFieldDefinitionById(fieldId);

  if (!baseFieldDef) {
    // console.warn(`DefaultFieldRenderer: No base definition found for fieldId: ${fieldId}`);
    return (
      <Col
        key={fieldId}
        span={fieldConfig.span || 24}
        offset={fieldConfig.offset || 0}
      >
        Missing Base Definition for: {fieldId}
      </Col>
    );
  }

  const additionalProp = getAdditionalPropByFieldId({
    fieldId,
    tinhTrangTreSoSinhSauDe,
  });

  const mergedData = {
    ...baseFieldDef,
    ...fieldConfig,
    id: fieldId,
    isReadonly,
    ...additionalProp,
  };
  const componentToRender = renderComponent(mergedData);

  if (fieldRenderers[fieldId]) {
    return fieldRenderers[fieldId]({
      baseProps: mergedData,
      baseComponent: componentToRender,
      renderComponent,
      parentState,
      fromSetting,
      isReadonly,
      tinhTrangTreSoSinhSauDe,
      layout,
      getFieldDefinitionById,
      form,
    });
  }

  if (isNumber(mergedData.width)) {
    return (
      <Row gutter={[16, 8]}>
        <Col span={mergedData.width ?? 24}>{componentToRender}</Col>
      </Row>
    );
  }

  return componentToRender;
};

const getAdditionalPropByFieldId = ({}) => {
  return {};
};

const ApgarSectionRenderer = ({
  baseComponent,
  renderComponent,
  fromSetting,
  layout,
  getFieldDefinitionById,
  form,
}) => {
  const sectionConfig =
    layout.sections?.tinhTrangTreSoSinhSauDe?.fieldLayout?.apgarSection;

  const fieldOrder = sectionConfig?.fieldOrder || [];
  const fieldLayout = sectionConfig?.fieldLayout || {};

  const [listLoaiApgar] = useEnum(ENUM.LOAI_APGAR, []);

  if (fromSetting) {
    return baseComponent;
  }

  return (
    <div>
      <Form.List name="dsApgar">
        {(fields, { add, remove }) => {
          const onAddApgar = () => {
            if (fields.length < listLoaiApgar.length) {
              const addIdx = fields.length;
              add({ loai: listLoaiApgar[addIdx]?.id });
            }
          };

          const onDeleteApgar = (index) => () => {
            if (index > 0) {
              remove(index);
            }
          };

          return (
            <>
              <div style={{ marginBottom: "16px" }}>
                <div
                  className="apgar-header"
                  style={{
                    display: "flex",
                    alignItems: "center",
                    marginBottom: "16px",
                    fontWeight: "bold",
                  }}
                >
                  <div style={{ marginRight: "8px" }}>Apgar:</div>
                  {fields.length < listLoaiApgar.length && (
                    <SVG.IcAdd
                      color={"var(--color-green-primary)"}
                      onClick={onAddApgar}
                      style={{ cursor: "pointer" }}
                    />
                  )}
                </div>
              </div>

              {fields.map((field, index) => {
                const fieldValue = form.getFieldValue([
                  "dsApgar",
                  field.name,
                  "loai",
                ]);
                const apgarType = listLoaiApgar.find(
                  (x) => x.id === fieldValue
                );

                return (
                  <Row gutter={[16, 16]} key={field.key}>
                    <Col span={2}>
                      <div style={{ paddingTop: "5px", fontWeight: "500" }}>
                        {apgarType?.ten || ""}:
                      </div>
                      <Form.Item {...field} name={[field.name, "loai"]} hidden>
                        <Input hidden />
                      </Form.Item>
                    </Col>
                    {fieldOrder
                      ?.filter((item) => fieldLayout[item]?.active !== false)
                      .map((item) => {
                        const fieldDef = getFieldDefinitionById(item);
                        return (
                          <Col
                            key={item}
                            span={fieldLayout[item]?.span || 2}
                            offset={fieldLayout[item]?.offset || 0}
                          >
                            {renderComponent({
                              ...fieldDef,
                              arrayName: [field.name, item],
                            })}
                          </Col>
                        );
                      })}
                    {index > 0 && (
                      <Col span={1}>
                        <SVG.IcDelete
                          onClick={onDeleteApgar(index)}
                          style={{ cursor: "pointer", marginTop: "8px" }}
                        />
                      </Col>
                    )}
                  </Row>
                );
              })}
            </>
          );
        }}
      </Form.List>
    </div>
  );
};

const DsPhuongPhapHoiSinhRenderer = ({
  baseProps,
  baseComponent,
  fromSetting,
}) => {
  const { t } = useTranslation();
  const [listPhuongPhapHoiSinhSauDe] = useEnum(
    ENUM.PHUONG_PHAP_HOI_SINH_SAU_DE,
    []
  );

  if (fromSetting) {
    return baseComponent;
  }

  return (
    <div>
      <div className="txt-upper">
        {t("quanLyNoiTru.tongKetKhoaDe.phuongPhapHoiSinhNgaySauDe")}
      </div>
      <Form.Item label="" name="dsPhuongPhapHoiSinh">
        <Checkbox.Group
          className="checkbox-pp-hoi-sinh"
          disabled={baseProps.disabled}
        >
          <Row gutter={[16, 16]}>
            {listPhuongPhapHoiSinhSauDe.map((item) => (
              <Col key={item.id} span={4}>
                <Checkbox value={item.id}>{item.ten}</Checkbox>
              </Col>
            ))}
          </Row>
        </Checkbox.Group>
      </Form.Item>
    </div>
  );
};

// Các field renderer đặc biệt
const fieldRenderers = {
  apgarSection: ApgarSectionRenderer,
  dsPhuongPhapHoiSinh: DsPhuongPhapHoiSinhRenderer,
};

export { DefaultFieldRenderer };
