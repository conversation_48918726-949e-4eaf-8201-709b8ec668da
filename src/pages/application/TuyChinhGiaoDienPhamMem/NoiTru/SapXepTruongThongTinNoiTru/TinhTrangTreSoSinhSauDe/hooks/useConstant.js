import { useMemo, useRef, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { merge } from "lodash";
import { useEnum, useQueryAll } from "hooks";
import { safeConvertToArray } from "utils";
import { fieldDefinitions } from "../config";
import { query } from "redux-store/stores";
import { selectMaTen } from "redux-store/selectors";
import { ENUM, YES_NO } from "constants/index";

export const listTinhTrang = [
  { id: "true", ten: "Sống" },
  { id: "false", ten: "Chết" },
];

export const useConstant = () => {
  const { t } = useTranslation();
  const listTinhTrangTreSoSinhSauDe = useRef([
    {
      id: "hoTen",
      i18n: "common.hoVaTen",
      type: "textField",
      required: true,
      maxLength: 255,
      timeDelay: 0,
    },
    {
      id: "gioiTinh",
      i18n: "common.gioiTinh",
      type: "select",
      required: true,
    },
    {
      id: "canNang",
      i18n: "common.canNang",
      type: "numberFormat",
      required: true,
      suffix: "g",
      inputNumberFormatProps: {
        decimalScale: 0,
        timeDelay: 0,
        isAllowed: (values) => {
          const { floatValue } = values;
          return (floatValue || 0) >= 0 && (floatValue || 0) < 10000;
        },
      },
    },
    {
      id: "chieuCao",
      i18n: "editor.chieuCao",
      type: "numberFormat",
      required: true,
      suffix: "cm",
      inputNumberFormatProps: {
        timeDelay: 0,
        isAllowed: (values) => {
          const { floatValue } = values;
          return (floatValue || 0) >= 0;
        },
      },
    },
    {
      id: "diTatHauMon",
      i18n: "quanLyNoiTru.tongKetKhoaDe.diTatHauMon",
      type: "checkbox",
      required: false,
    },
    {
      id: "diTatBamSinhId",
      i18n: "quanLyNoiTru.tongKetKhoaDe.diTatBamSinhKhac",
      type: "select",
      required: true,
    },
    {
      id: "diTatBamSinhChiTiet",
      i18n: "quanLyNoiTru.tongKetKhoaDe.diTatBamSinhKhacChiTiet",
      type: "textField",
      required: false,
    },
    {
      id: "luongDinh",
      i18n: "quanLyNoiTru.tongKetKhoaDe.luongDinh",
      type: "numberFormat",
      required: false,
    },
    {
      id: "apgarSection",
      i18n: "quanLyNoiTru.tongKetKhoaDe.apgar",
      type: "section",
      required: false,
    },
    // Apgar scores with special handling
    {
      id: "tim",
      i18n: "quanLyNoiTru.tongKetKhoaDe.tim",
      type: "numberFormat",
      required: false,
      inputNumberFormatProps: {
        decimalScale: 0,
        timeDelay: 0,
        isAllowed: (values) => {
          const { floatValue } = values;
          return (floatValue || 0) <= 2 && (floatValue || 0) >= 0;
        },
      },
    },
    {
      id: "tho",
      i18n: "quanLyNoiTru.tongKetKhoaDe.tho",
      type: "numberFormat",
      required: false,
      inputNumberFormatProps: {
        decimalScale: 0,
        timeDelay: 0,
        isAllowed: (values) => {
          const { floatValue } = values;
          return (floatValue || 0) <= 2 && (floatValue || 0) >= 0;
        },
      },
    },
    {
      id: "mauSac",
      i18n: "quanLyNoiTru.tongKetKhoaDe.mauSac",
      type: "numberFormat",
      required: false,
      inputNumberFormatProps: {
        decimalScale: 0,
        timeDelay: 0,
        isAllowed: (values) => {
          const { floatValue } = values;
          return (floatValue || 0) <= 2 && (floatValue || 0) >= 0;
        },
      },
    },
    {
      id: "truongLuc",
      i18n: "quanLyNoiTru.tongKetKhoaDe.truongLuc",
      type: "numberFormat",
      required: false,
      inputNumberFormatProps: {
        decimalScale: 0,
        timeDelay: 0,
        isAllowed: (values) => {
          const { floatValue } = values;
          return (floatValue || 0) <= 2 && (floatValue || 0) >= 0;
        },
      },
    },
    {
      id: "phanXa",
      i18n: "quanLyNoiTru.tongKetKhoaDe.phanXa",
      type: "numberFormat",
      required: false,
      inputNumberFormatProps: {
        decimalScale: 0,
        timeDelay: 0,
        isAllowed: (values) => {
          const { floatValue } = values;
          return (floatValue || 0) <= 2 && (floatValue || 0) >= 0;
        },
      },
    },
    {
      id: "tongCong",
      i18n: "quanLyNoiTru.tongKetKhoaDe.tongCong",
      type: "number",
      required: false,
      disabled: true,
    },
    {
      id: "dsPhuongPhapHoiSinh",
      i18n: "quanLyNoiTru.tongKetKhoaDe.phuongPhapHoiSinhNgaySauDe",
      type: "checkbox-group",
      required: false,
    },
    {
      id: "nguoiHoiSucId",
      i18n: "quanLyNoiTru.tongKetKhoaDe.nguoiHoiSuc",
      type: "select",
      required: false,
      getLabel: selectMaTen,
      dropdownMatchSelectWidth: 300,
    },
    {
      id: "tinhTrangSauHoiSuc",
      i18n: "quanLyNoiTru.tongKetKhoaDe.tinhTrangTreSauHoiSuc",
      type: "textField",
      required: false,
    },
  ]).current;

  const [listGioiTinh] = useEnum(ENUM.GIOI_TINH);
  const [listPhuongPhapHoiSinhSauDe] = useEnum(
    ENUM.PHUONG_PHAP_HOI_SINH_SAU_DE,
    []
  );
  const { data: listAllDiTatBamSinh } = useQueryAll(
    query.diTatBamSinh.queryAllDiTatBamSinh
  );
  const { data: listAllNhanVien } = useQueryAll(
    query.nhanVien.queryAllNhanVien
  );

  const fieldEnhancementConfig = useMemo(
    () => ({
      // Fields with list selects
      listSelectFields: {
        gioiTinh: listGioiTinh,
        diTatBamSinhId: listAllDiTatBamSinh,
        dsPhuongPhapHoiSinh: listPhuongPhapHoiSinhSauDe,
        nguoiHoiSucId: listAllNhanVien,
      },

      specificFields: {
        chieuCao: {
          ten: `${t("editor.chieuCao")} (cm)`,
        },
        canNang: {
          ten: `${t("common.canNang")} (g)`,
        },
      },

      conditionalRequiredFields: {
        basicRequired: new Set([
          "hoTen",
          "gioiTinh",
          "canNang",
          "chieuCao",
          "diTatBamSinhId",
        ]),
      },
    }),
    [
      listGioiTinh,
      listAllDiTatBamSinh,
      listPhuongPhapHoiSinhSauDe,
      listAllNhanVien,
      t,
    ]
  );

  const fieldEnhancers = useMemo(
    () => ({
      addListSelect: (field, fieldId, config) => {
        const listSelect = config.listSelectFields[fieldId];
        return listSelect ? merge({}, field, { listSelect }) : field;
      },

      addSpecificProperties: (field, fieldId, config) => {
        const specificProps = config.specificFields[fieldId];
        return specificProps ? merge({}, field, specificProps) : field;
      },

      addConditionalRequired: (field, fieldId, config) => {
        const { basicRequired } = config.conditionalRequiredFields;

        if (!basicRequired.has(fieldId)) {
          return field;
        }

        const conditions = [basicRequired.has(fieldId)];

        const isRequired = conditions.some((condition) => condition);

        return isRequired ? merge({}, field, { required: true }) : field;
      },
    }),
    []
  );

  const allFieldDefinitions = useMemo(() => {
    const map = new Map();
    const allLists = [listTinhTrangTreSoSinhSauDe];

    allLists.forEach((list) => {
      safeConvertToArray(list).forEach((field) => {
        if (field?.id) {
          if (map.has(field.id)) {
            console.warn(`Duplicate field ID found: ${field.id}. Overwriting.`);
          }

          const enhancedField = [
            fieldEnhancers.addListSelect,
            fieldEnhancers.addSpecificProperties,
            fieldEnhancers.addConditionalRequired,
          ].reduce(
            (currentField, enhancer) =>
              enhancer(currentField, field.id, fieldEnhancementConfig),
            { ...field }
          );

          map.set(field.id, enhancedField);
        }
      });
    });

    return map;
  }, [listTinhTrangTreSoSinhSauDe, fieldEnhancementConfig, fieldEnhancers]);

  const getFieldDefinitionById = useCallback(
    (fieldId) => {
      return (
        allFieldDefinitions.get(fieldId) || fieldDefinitions[fieldId] || null
      );
    },
    [allFieldDefinitions]
  );

  return {
    // Field definitions
    listTinhTrangTreSoSinhSauDe,
    getFieldDefinitionById,
    allFieldDefinitions,

    // Enum data
    listGioiTinh,
    listPhuongPhapHoiSinhSauDe,
    listAllDiTatBamSinh,
    listAllNhanVien,

    // Constants
    listTinhTrang,
    YES_NO,
  };
};
