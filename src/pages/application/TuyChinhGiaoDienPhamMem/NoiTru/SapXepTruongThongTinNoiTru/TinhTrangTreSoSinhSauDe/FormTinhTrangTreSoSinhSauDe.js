import React, {
  useState,
  useCallback,
  useRef,
  useImperativeHandle,
  forwardRef,
  useEffect,
} from "react";
import { useTranslation } from "react-i18next";
import { Form, Row, Col } from "antd";
import { Button } from "components";
import { SVG } from "assets";
import { safeConvertToArray } from "utils";
import { DefaultFieldRenderer } from "./fieldRenderer";
import { initialLayout } from "./config";
import { useFormTinhTrangTreSoSinhSauDe } from "./hooks/useFormTinhTrangTreSoSinhSauDe";
import { useConstant } from "./hooks/useConstant";
import { cloneDeep, get, merge, set } from "lodash";
import {
  CollapseWrapper,
  FormTinhTrangTreSoSinhSauDeContainer,
} from "./styled";

const ModalLayoutCustomize = React.lazy(() =>
  import(
    "pages/application/TuyChinhGiaoDienPhamMem/SangLocDinhDuong/ModalLayoutCustomize"
  )
);

// function để convert span thành flex nếu nhập span giá trị là số thập phân
const spanToFlex = (span) => {
  if (typeof span === "number") {
    if (Number.isInteger(span)) {
      return {
        isSpan: true,
        span: span,
      };
    } else {
      const percentage = (span / 24) * 100;
      return {
        isSpan: false,
        span: percentage,
      };
    }
  }
  return `${span}%`;
};

const FormTinhTrangTreSoSinhSauDe = forwardRef((props, ref) => {
  const { t } = useTranslation();
  const {
    fromSetting = false,
    activeKey: controlledActiveKey,
    setActiveKey: setControlledActiveKey,
    tinhTrangTreSoSinhSauDe,
    activeSections,
    parentState,
    form: formParent,
    isReadonly = false,
    onValuesChange,
    extraHeaderRight,
    tinhTrangTreSoSinhSauDeIndex,
  } = props;

  const constantUtils = useConstant();

  const {
    listGioiTinh,
    listTinhTrangThai,
    listAllDiTatBamSinh,
    getFieldDefinitionById,
  } = constantUtils;
  const [_activeKey, _setActiveKey] = useState(activeSections);
  const setActiveKey = useCallback(
    (key) => {
      if (controlledActiveKey) {
        setControlledActiveKey(key);
      } else {
        _setActiveKey(key);
      }
    },
    [controlledActiveKey, setControlledActiveKey]
  );

  const activeKey = controlledActiveKey || _activeKey;

  const refLayoutModal = useRef(null);

  const { getFieldRenderProps, setLayout, layout } =
    useFormTinhTrangTreSoSinhSauDe();

  useImperativeHandle(ref, () => ({
    getLayout: () => layout,
    setLayout,
  }));

  const handleLayoutSaveForPath = useCallback(
    (pathArray, newFieldOrder, newFieldLayoutUpdate) => {
      setLayout((prevLayout) => {
        const currentSectionOrField = get(prevLayout, pathArray);
        if (!currentSectionOrField) {
          console.error("Target for layout save not found at path:", pathArray);
          return prevLayout;
        }
        const updatedSectionOrField = cloneDeep({
          ...currentSectionOrField,
          fieldOrder: newFieldOrder,
          fieldLayout: merge(
            currentSectionOrField.fieldLayout || {},
            newFieldLayoutUpdate
          ),
        });
        return { ...set(prevLayout, pathArray, updatedSectionOrField) };
      });
    },
    [setLayout]
  );

  useEffect(() => {
    setActiveKey(activeSections);
  }, [JSON.stringify(activeSections)]);

  const handleShowLayoutModal = (pathArrayToSection) => {
    // pathArrayToSection ví dụ ['sections', 'tinhTrangTreSoSinhSauDe'] or ['sections', 'tinhTrangTreSoSinhSauDe', 'fieldLayout', 'group1']

    const currentSectionConfig = get(layout, pathArrayToSection);
    const initialSectionConfigForModal = get(initialLayout, pathArrayToSection);

    refLayoutModal.current?.show(
      {
        currentSectionConfig: currentSectionConfig,
        initialSectionConfig: initialSectionConfigForModal,
        getFieldDefinitionById: getFieldDefinitionById,
      },
      {
        onSave: (_, newFieldOrder, newFieldLayout) => {
          handleLayoutSaveForPath(
            pathArrayToSection,
            newFieldOrder,
            newFieldLayout
          );
        },
      }
    );
  };

  const onCollapsed = (key) => {
    setActiveKey(safeConvertToArray(key));
  };

  const fieldRenderProps = {
    ...getFieldRenderProps(),
    listGioiTinh,
    listTinhTrangThai,
    listAllDiTatBamSinh,
    fromSetting,
  };

  const renderPanelHeader = (pathToArray, title, type) => {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          width: "100%",
        }}
      >
        <span>
          {type === "header" && !fromSetting
            ? t("tuyChinhGiaoDien.tinhTrangTreSoSinhNgaySauDeNumber", {
                number: tinhTrangTreSoSinhSauDeIndex + 1,
              })
            : title}
        </span>
        {fromSetting && (
          <Button
            type="text"
            icon={<SVG.IcSetting />}
            onClick={(e) => {
              e.stopPropagation();
              handleShowLayoutModal(pathToArray);
            }}
            style={{ border: "none", background: "none", padding: "0 4px" }}
          />
        )}
        {extraHeaderRight}
      </div>
    );
  };

  const renderPanelContent = (currentPathArray) => {
    // currentPathArray ví dụ ['sections', 'tinhTrangTreSoSinhSauDe'] or ['sections', 'tinhTrangTreSoSinhSauDe', 'fieldLayout', 'group1']
    const sectionData = get(layout, currentPathArray);

    if (!sectionData || !sectionData.fieldOrder) {
      return null;
    }

    return (
      <Row gutter={[16, 8]} className="noi-dung-danh-gia">
        {safeConvertToArray(sectionData.fieldOrder).map((fieldId) => {
          const fieldLayoutConfig = get(sectionData, ["fieldLayout", fieldId], {
            span: 24,
            offset: 0,
            active: true,
          });

          if (fieldLayoutConfig.active === false) {
            return null;
          }

          // Get field definition to check conditional display
          const fieldDef = getFieldDefinitionById(fieldId);
          if (fieldDef && !fromSetting) {
            // Add any conditional display logic here if needed
          }

          if (fieldLayoutConfig.isLayoutSection) {
            const subSectionPathArray = [
              ...currentPathArray,
              "fieldLayout",
              fieldId,
            ];
            const subSectionData = get(layout, subSectionPathArray);

            let title = subSectionData?.i18n || fieldLayoutConfig.i18n;

            title = title ? t(title) : `Sub-section: ${fieldId}`;

            const { isSpan, span } = spanToFlex(fieldLayoutConfig.span);

            if (fieldId === "apgarSection" && !fromSetting) {
              return (
                <Col
                  key={fieldId}
                  span={isSpan && span}
                  flex={!isSpan && `${span}%`}
                  offset={fieldLayoutConfig.offset ?? 0}
                >
                  <div
                    className="sub-section-wrapper"
                    style={{
                      padding: "10px",
                      border: "1px dashed #eee",
                      margin: "5px 0",
                    }}
                  >
                    <DefaultFieldRenderer
                      fieldId={fieldId}
                      fieldConfig={fieldLayoutConfig}
                      parentPath={currentPathArray}
                      tinhTrangTreSoSinhSauDe={tinhTrangTreSoSinhSauDe}
                      parentState={parentState}
                      fromSetting={fromSetting}
                      isReadonly={isReadonly}
                      getFieldDefinitionById={getFieldDefinitionById}
                      constantUtils={constantUtils}
                      form={formParent}
                      {...fieldRenderProps}
                    />
                  </div>
                </Col>
              );
            }

            return (
              <Col
                key={fieldId}
                span={isSpan && span}
                flex={!isSpan && `${span}%`}
                offset={fieldLayoutConfig.offset ?? 0}
              >
                <div
                  className="sub-section-wrapper"
                  style={{
                    padding: "10px",
                    border: "1px dashed #eee",
                    margin: "5px 0",
                  }}
                >
                  {renderPanelHeader(subSectionPathArray, title, "sub-header")}
                  {renderPanelContent(subSectionPathArray)}
                </div>
              </Col>
            );
          }

          const { isSpan, span } = spanToFlex(fieldLayoutConfig.span);

          return (
            <Col
              key={fieldId}
              span={isSpan && span}
              flex={!isSpan && `${span}%`}
              offset={fieldLayoutConfig.offset ?? 0}
            >
              <div
                className={`field-wrapper ${
                  fieldDef?.required ? "required-field" : ""
                }`}
                style={{
                  position: "relative",
                }}
              >
                <DefaultFieldRenderer
                  fieldId={fieldId}
                  fieldConfig={fieldLayoutConfig}
                  parentPath={currentPathArray}
                  tinhTrangTreSoSinhSauDe={tinhTrangTreSoSinhSauDe}
                  parentState={parentState}
                  fromSetting={fromSetting}
                  isReadonly={isReadonly}
                  getFieldDefinitionById={getFieldDefinitionById}
                  constantUtils={constantUtils}
                  form={formParent}
                  {...fieldRenderProps}
                />
              </div>
            </Col>
          );
        })}
      </Row>
    );
  };
  return (
    <FormTinhTrangTreSoSinhSauDeContainer className="collapse-content">
      <Form
        form={formParent}
        labelAlign="left"
        disabled={isReadonly}
        onValuesChange={onValuesChange}
        className="form-custom"
      >
        {(layout.sectionOrder || [])
          .filter((topLevelSectionId) =>
            activeSections.includes(topLevelSectionId)
          )
          .map((topLevelSectionId) => {
            const sectionPathArray = ["sections", topLevelSectionId];
            const section = get(layout, sectionPathArray);

            if (!section) {
              return null;
            }

            return (
              <div key={topLevelSectionId}>
                {section.isCollapsible ? (
                  <CollapseWrapper
                    bordered={false}
                    expandIcon={({ isActive }) =>
                      isActive ? <SVG.IcDown /> : <SVG.IcUp />
                    }
                    className="site-collapse-custom-collapse"
                    activeKey={activeKey}
                    onChange={onCollapsed}
                  >
                    <CollapseWrapper.Panel
                      key={topLevelSectionId}
                      header={renderPanelHeader(
                        sectionPathArray,
                        t(section.i18n),
                        "header"
                      )}
                    >
                      {renderPanelContent(sectionPathArray)}
                    </CollapseWrapper.Panel>
                  </CollapseWrapper>
                ) : (
                  <div
                    className="top-info-section"
                    style={{
                      padding: "16px",
                      backgroundColor: "#fff",
                    }}
                  >
                    {/* For non-collapsible top sections, header is rendered differently */}
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        borderBottom: "1px solid #eee",
                        paddingBottom: "10px",
                        marginBottom: "15px",
                      }}
                    >
                      <h3
                        style={{
                          fontSize: "16px",
                          fontWeight: "600",
                          margin: 0,
                        }}
                      >
                        {t(section.i18n)},
                      </h3>
                      {fromSetting && (
                        <Button
                          type="text"
                          icon={<SVG.IcSetting />}
                          onClick={() =>
                            handleShowLayoutModal(sectionPathArray)
                          }
                          style={{
                            border: "none",
                            background: "none",
                            padding: "0 4px",
                          }}
                        />
                      )}
                    </div>
                    {renderPanelContent(sectionPathArray)}
                  </div>
                )}
              </div>
            );
          })}
      </Form>

      {fromSetting && (
        <>
          <ModalLayoutCustomize ref={refLayoutModal} />
        </>
      )}
    </FormTinhTrangTreSoSinhSauDeContainer>
  );
});

export default FormTinhTrangTreSoSinhSauDe;
