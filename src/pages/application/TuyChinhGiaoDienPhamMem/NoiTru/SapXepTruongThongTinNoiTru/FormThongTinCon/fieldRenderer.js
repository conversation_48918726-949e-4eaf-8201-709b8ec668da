import React from "react";
import { useTranslation } from "react-i18next";
import { useRenderer } from "./hooks/useRenderer";
import { Col, Form, Row, Input } from "antd";
import { isNumber } from "utils";
import { useStore } from "hooks";

const DefaultFieldRenderer = (props) => {
  const {
    fieldId,
    fieldConfig,
    index,
    thongTinCon,
    parentState,
    fromSetting,
    isReadonly,
    getFieldDefinitionById,
  } = props;

  const { renderComponent } = useRenderer({
    thongTinCon,
  });

  const baseFieldDef = getFieldDefinitionById(fieldId);

  if (!baseFieldDef) {
    // console.warn(`DefaultFieldRenderer: No base definition found for fieldId: ${fieldId}`);
    return (
      <Col
        key={fieldId}
        span={fieldConfig.span || 24}
        offset={fieldConfig.offset || 0}
      >
        Missing Base Definition for: {fieldId}
      </Col>
    );
  }

  const additionalProp = getAdditionalPropByFieldId({
    fieldId,
    thongTinCon,
  });

  const mergedData = {
    ...baseFieldDef,
    ...fieldConfig,
    id: fieldId,
    isReadonly,
    ...additionalProp,
  };
  const componentToRender = renderComponent(mergedData, index);

  if (fieldRenderers[fieldId]) {
    return fieldRenderers[fieldId]({
      baseProps: mergedData,
      baseComponent: componentToRender,
      renderComponent,
      parentState,
      fromSetting,
      isReadonly,
      thongTinCon,
    });
  }

  if (isNumber(mergedData.width)) {
    return (
      <Row gutter={[8, 8]}>
        <Col span={mergedData.width ?? 24}>{componentToRender}</Col>
      </Row>
    );
  }

  return componentToRender;
};

const getAdditionalPropByFieldId = ({ fieldId, thongTinCon }) => {
  switch (fieldId) {
    case "maTheBhyt":
      return {
        conditionalDisplay: !!thongTinCon?.maBenhAn,
        disabled: true,
      };
    case "maThe":
      return {
        conditionalDisplay: !thongTinCon?.maBenhAn,
      };
    default:
      return {};
  }
};

const GhiChuRenderer = ({
  baseProps,
  baseComponent,
  parentState,
  fromSetting,
}) => {
  if (isNumber(baseProps.width)) {
    return (
      <Row gutter={[8, 8]}>
        <Col span={baseProps.width ?? 24}>{baseComponent}</Col>
        {parentState?.ghiChuNote && !fromSetting && (
          <span className="note">&emsp;{parentState?.ghiChuNote}</span>
        )}
      </Row>
    );
  }

  return (
    <>
      {baseComponent}
      {parentState?.ghiChuNote && !fromSetting && (
        <span className="note">&emsp;{parentState?.ghiChuNote}</span>
      )}
    </>
  );
};

const MaTheRenderer = ({ baseProps, fromSetting }) => {
  const { t } = useTranslation();
  // thông tin sản phụ cơ bản
  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan");

  const conditionalNote = () => {
    if (!fromSetting) return null;

    return <span className="note">&emsp;Hiển thị khi có mã bệnh án</span>;
  };

  const renderComponent = (
    <>
      <Col span={24} key={baseProps.id}>
        <Form.Item
          label={t(baseProps.i18n)}
          name={baseProps.id}
          rules={[
            {
              required: !!baseProps.required,
              message: t(`danhMuc.vuiLongNhapTitle`, {
                title: t("quanLyNoiTru.giaHanThe.soBaoHiem"),
              }),
            },
            {
              validator(_, value) {
                if (!value || /^[0-9]{10}$/.test(value)) {
                  return Promise.resolve();
                }
                return Promise.reject(
                  t("quanLyNoiTru.thongTinCon.vuiLongNhap10so")
                );
              },
            },
          ]}
        >
          <Input
            addonBefore={`TE1${thongTinCoBan?.maTinhThanhPho || ""}`}
            maxLength={10}
          />
        </Form.Item>
        {conditionalNote()}
      </Col>
    </>
  );

  if (isNumber(baseProps.width)) {
    return (
      <Row gutter={[8, 8]}>
        <Col span={baseProps.width ?? 24}>{renderComponent}</Col>
      </Row>
    );
  }

  return renderComponent;
};
const MaTheBhytRenderer = ({ baseProps, fromSetting, baseComponent }) => {
  const conditionalNote = () => {
    if (!fromSetting) return null;

    return <span className="note">&emsp;Hiển thị khi không có mã bệnh án</span>;
  };

  const renderComponent = () => {
    return (
      <>
        {baseComponent}
        {conditionalNote()}
      </>
    );
  };

  if (isNumber(baseProps.width)) {
    return (
      <Row gutter={[8, 8]}>
        <Col span={baseProps.width ?? 24}>{renderComponent()}</Col>
      </Row>
    );
  }

  return <>{renderComponent()}</>;
};

export const fieldRenderers = {
  ghiChu: GhiChuRenderer,
  maTheBhyt: MaTheBhytRenderer,
  maThe: MaTheRenderer,
};

export { DefaultFieldRenderer };
