import React, { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Input, Form, InputNumber, Upload, message } from "antd";
import { getBackendUrl } from "client/request";
import { Auth<PERSON>rapper, Button, Checkbox, Select } from "components";
import { ENUM, ROLES, NULL_VALUE } from "constants/index";
import baoCaoProvider from "data-access/categories/dm-bao-cao-provider";
import { BaoCaoChiTietStyle, Wrapper } from "./styled";
import { checkRole } from "lib-utils/role-utils";
import FormWraper from "components/FormWraper";
import { useHistory } from "react-router-dom";
import ModalPhienBanBieuMau from "./ModalPhienBanBieuMau";
import { t } from "i18next";
import ModalTaoPhienBanBieuMau from "./ModalTaoPhienBanBieuMau";
import { useEnum, useListAll, useLoading } from "hooks";
import { SVG } from "assets";

const { TextArea } = Input;

const BaoCaoChiTiet = ({
  stateParent,
  setStateParent,

  refCallbackSave = {},
}) => {
  const [form] = Form.useForm();
  const history = useHistory();
  const { showLoading, hideLoading } = useLoading();

  const [state, _setState] = useState({
    mauBaoCao: null,
    editStatus: false,
    defaultFileList: [],
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const refAutoFocus = useRef(null);
  const refPhienBanBieuMau = useRef(null);
  const refTaoPhienBanBieuMau = useRef(null);
  const refData = useRef({});

  const [listKhuVucKy] = useEnum(ENUM.KHU_VUC_KY, []);
  const [listLoaiIn] = useEnum(ENUM.LOAI_IN, []);
  const [listDuLieuKy] = useEnum(ENUM.DU_LIEU_KY, []);
  const [listHinhThucIn] = useEnum(ENUM.HINH_THUC_IN);
  const [listKetNoiBenhAn] = useEnum(ENUM.KET_NOI_BENH_AN);
  const {
    baoCao: { onSizeChange, updateData, createOrEdit },
  } = useDispatch();
  const { dataEditDefault } = useSelector((state) => state.baoCao);
  const [listHuongGiay] = useEnum(ENUM.HUONG_GIAY, []);
  const [listKhoGiay] = useEnum(ENUM.KHO_GIAY, []);
  const [listDinhDangBaoCao] = useEnum(ENUM.DINH_DANG_BAO_CAO, []);
  const [listLoaiBieuMau] = useEnum(ENUM.LOAI_BIEU_MAU, []);
  const [listNgonNgu] = useEnum(ENUM.NGON_NGU, []);
  const [listAllLoaiPhieu] = useListAll("loaiPhieu", {}, true);
  const [listAllLoaiBenhAn] = useListAll(
    "loaiBenhAn",
    { dsCoSoKcbId: NULL_VALUE },
    true
  );
  useEffect(() => {
    if (refAutoFocus.current) {
      refAutoFocus.current.focus();
    }
  }, [stateParent]);
  useEffect(() => {
    if (dataEditDefault) {
      setState({
        ...stateParent,
        loai: dataEditDefault?.loai,
        invalidMauBaoCao: false,
      });
      if (
        !stateParent.editStatus &&
        stateParent.mauBaoCao === null &&
        stateParent.defaultFileList?.length === 0 &&
        !stateParent.invalidMauBaoCao
      ) {
        form.resetFields();
        refData.current = {
          chieuDoc: null,
          chieuNgang: null,
          mauBaoCao: state.mauBaoCao,
          dinhDang: null,
          cauHinh: {},
          components: [],
          layout: [],
          khoGiay: null,
          huongGiay: null,
        };
      } else {
        if (
          dataEditDefault?.dsLoaiBaoCaoId?.length < 1 ||
          !dataEditDefault?.dsLoaiBaoCaoId
        ) {
          dataEditDefault.dsLoaiBaoCaoId = [];
        }
        refData.current = dataEditDefault;
        form.setFieldsValue({
          ...dataEditDefault,
        });
      }
    }
  }, [dataEditDefault, stateParent]);

  useEffect(() => {
    onSizeChange({ size: 10 });
  }, []);
  const onShowModal = () => {
    if (dataEditDefault?.id) {
      refPhienBanBieuMau.current &&
        refPhienBanBieuMau.current.show(dataEditDefault.id);
    } else {
      message.error(t("danhMuc.vuiLongChonBieuMau"));
    }
  };
  const handleAdded = (e) => {
    e.preventDefault();
    form
      .validateFields()
      .then((values) => {
        Object.keys(values).forEach((item) => {
          values[item] = values[item] === undefined ? null : values[item];
        });

        let editStatus = false;
        if (!state.mauBaoCao && ![50, 20, 60].includes(state?.loai)) {
          setState({
            invalidMauBaoCao: true,
          });
          return;
        }
        let formattedData = {
          ...refData.current,
          ...values,
          ma: values?.ma?.trim(),
          ten: values?.ten?.trim(),
          mauBaoCao: state.mauBaoCao,
        };
        if (state.editStatus) {
          formattedData = { ...formattedData, id: dataEditDefault.id };
          editStatus = true;
        }

        showLoading();

        createOrEdit(formattedData)
          .then(() => {
            setStateParent({ mauBaoCao: null, defaultFileList: [] });
            updateData({ dataEditDefault: {} });
            form.resetFields();
            if (!editStatus) {
              setStateParent({
                isSelected: false,
              });
            }
          })
          .catch((err) => {})
          .finally(() => {
            hideLoading();
          });
      })
      .catch((error) => {
        if (!state.mauBaoCao) {
          setState({
            invalidMauBaoCao: true,
          });
          return;
        }
      });
  };

  refCallbackSave.current = handleAdded;

  const onChangeKhoGiay = (val) => {
    setState({
      isRequiredKichThuoc: val === 200,
    });
    form.validateFields();
  };
  const handleCancel = () => {
    if (state.editStatus) {
      form.setFieldsValue(dataEditDefault);
    } else {
      form.resetFields();
    }
    setStateParent({
      isSelected: true,
    });
  };
  const handleHiddenCancel = () => {
    let roleSave = [ROLES["DANH_MUC"].BAO_CAO_THEM];
    let roleEdit = [ROLES["DANH_MUC"].BAO_CAO_THEM];
    if (roleEdit || roleSave) {
      if (state.editStatus) {
        return !checkRole(roleEdit);
      } else {
        return !checkRole(roleSave);
      }
    } else {
      return false;
    }
  };
  const handleHiddenSave = () => {
    let roleSave = [ROLES["DANH_MUC"].BAO_CAO_THEM];
    let roleEdit = [ROLES["DANH_MUC"].BAO_CAO_THEM];
    if (roleEdit || roleSave) {
      if (state.editStatus) {
        return !checkRole(roleEdit);
      } else {
        return !checkRole(roleSave);
      }
    } else {
      return false;
    }
  };
  const onViewEditor = () => {
    history.push(`/editor/config/${dataEditDefault?.id}`);
  };
  const createPhienBan = () => {
    refTaoPhienBanBieuMau.current &&
      refTaoPhienBanBieuMau.current.show(dataEditDefault);
  };

  return (
    <BaoCaoChiTietStyle>
      <Wrapper>
        <FormWraper
          disabled={
            state.editStatus
              ? !checkRole([ROLES["DANH_MUC"].BAO_CAO_SUA])
              : !checkRole([ROLES["DANH_MUC"].BAO_CAO_THEM])
          }
          form={form}
          layout="vertical"
          style={{ width: "100%" }}
          className="form-custom"
          initialValues={{ loai: 10 }}
        >
          <Form.Item
            label={t("danhMuc.maBaoCao")}
            name="ma"
            rules={[
              {
                required: true,
                message: t("danhMuc.vuiLongChonMaBaoCao"),
              },
              {
                max: 30,
                message: t("danhMuc.vuiLongChonMaBaoCaoKhongQuaNumKyTu", {
                  num: 30,
                }),
              },
              {
                whitespace: true,
                message: t("danhMuc.vuiLongChonMaBaoCao"),
              },
            ]}
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.vuiLongChonMaBaoCao")}
              ref={refAutoFocus}
              autoFocus
            />
          </Form.Item>
          <Form.Item label="" className="form-custom">
            {[50, 20].includes(state?.loai) ? (
              dataEditDefault?.id ? (
                <AuthWrapper accessRoles={[ROLES["EDITOR"].CONFIG_PHIEU]}>
                  <Button.Text
                    type="primary"
                    leftIcon={<SVG.IcEdit />}
                    onClick={onViewEditor}
                  >
                    {t("danhMuc.xemSuaBieuMau")}
                  </Button.Text>
                </AuthWrapper>
              ) : null
            ) : (
              <>
                <div className="image">
                  <Upload
                    fileList={state.defaultFileList}
                    customRequest={({ onSuccess, onError, file }) => {
                      baoCaoProvider
                        .upload(file)
                        .then((response) => {
                          onSuccess(null, {});
                          setState({
                            invalidMauBaoCao: false,
                            mauBaoCao: response.data,
                            defaultFileList: [
                              {
                                uid: file.uid,
                                name: file.name,
                                url: `${getBackendUrl()}/api/his/v1/files/${
                                  response?.data
                                }`,
                              },
                            ],
                          });
                        })
                        .catch((e) => {
                          onError(e);
                          setState({
                            mauBaoCao: null,
                          });
                        });
                    }}
                    accept=".doc,.docx,.xls,.xlsx"
                    onRemove={(file) => {
                      setState({
                        mauBaoCao: null,
                        defaultFileList: [],
                      });
                    }}
                  >
                    <Button.Text leftIcon={<SVG.IcUpload />} type="primary">
                      {t("danhMuc.taiLenMauBaoCao")}
                      {state?.loai !== 60 && (
                        <span className="icon-required">*</span>
                      )}
                    </Button.Text>
                  </Upload>
                  {state.mauBaoCao && (
                    <SVG.IcTaoPhienBanMau
                      className="ic-tao-phien-ban"
                      onClick={createPhienBan}
                    ></SVG.IcTaoPhienBanMau>
                  )}
                </div>
                {state?.loai !== 60 && state.invalidMauBaoCao && (
                  <div className="err-msg">
                    {t("danhMuc.vuiLongTaiLenMauBaoCaoExcel")}
                  </div>
                )}
              </>
            )}
          </Form.Item>
          <Form.Item
            label={t("danhMuc.tenBaoCao")}
            name="ten"
            rules={[
              {
                required: true,
                message: t("danhMuc.vuiLongNhapTenBaoCao") + "!",
              },
              {
                max: 1000,
                message: t("danhMuc.vuiLongNhapTenBaoCaoKhongQuaNumKyTu", {
                  num: 1000,
                }),
              },
              {
                whitespace: true,
                message: t("danhMuc.vuiLongNhapTenBaoCao") + "!",
              },
            ]}
          >
            <Input
              style={{ width: "100%" }}
              className="input-option"
              placeholder={t("danhMuc.vuiLongNhapTenBaoCao")}
            />
          </Form.Item>
          <Form.Item label={t("danhMuc.loaiBieuMau")} name="loai">
            <Select
              data={listLoaiBieuMau || []}
              placeholder={t("danhMuc.vuiLongChonLoaiBieuMau")}
              onChange={(e) => setState({ loai: e })}
            />
          </Form.Item>

          {![20].includes(state?.loai) && (
            <>
              <Form.Item label={t("danhMuc.khoGiay")} name="khoGiay">
                <Select
                  data={listKhoGiay}
                  placeholder={t("danhMuc.chonKhoGiay")}
                  onChange={onChangeKhoGiay}
                />
              </Form.Item>
            </>
          )}
          {![20, 50].includes(state?.loai) && (
            <>
              <Form.Item
                label={t("danhMuc.kichThuocChieuDoc") + "(mm)"}
                name="chieuDoc"
                rules={[
                  {
                    required: state.isRequiredKichThuoc,
                    message: t("danhMuc.vuiLongNhapKichThuocChieuDoc") + "!",
                  },
                  {
                    pattern: /^[\d]{0,4}$/,
                    message: t(
                      "danhMuc.vuiLongNhapKichThuocChieuDocKhongQuaNumKyTu",
                      { num: 4 }
                    ),
                  },
                ]}
              >
                <InputNumber
                  style={{ width: "100%" }}
                  placeholder={t("danhMuc.vuiLongNhapKichThuocChieuDoc")}
                  type="number"
                />
              </Form.Item>
              <Form.Item
                label={t("danhMuc.kichThuocChieuNgang") + "(mm)"}
                name="chieuNgang"
                rules={[
                  {
                    required: state.isRequiredKichThuoc,
                    message: t("danhMuc.vuiLongNhapKichThuocNgang") + "!",
                  },
                  {
                    pattern: /^[\d]{0,4}$/,
                    message: t(
                      "danhMuc.vuiLongNhapKichThuocNgangKhongQuaNumKyTu",
                      { num: 4 }
                    ),
                  },
                ]}
              >
                <InputNumber
                  className="input-option"
                  placeholder={t("danhMuc.vuiLongNhapKichThuocNgang")}
                  type="number"
                />
              </Form.Item>
            </>
          )}
          {![20].includes(state?.loai) && (
            <>
              <Form.Item label={t("danhMuc.huongGiay")} name="huongGiay">
                <Select
                  data={listHuongGiay}
                  placeholder={t("danhMuc.chonHuongGiay")}
                />
              </Form.Item>
            </>
          )}
          {![20, 50].includes(state?.loai) && (
            <Form.Item label={t("danhMuc.dinhDangXuatFile")} name="dinhDang">
              <Select
                data={listDinhDangBaoCao || []}
                placeholder={t("danhMuc.choDinhDangXuatFile")}
              />
            </Form.Item>
          )}
          <Form.Item label={t("danhMuc.loaiPhieu")} name="dsLoaiBaoCaoId">
            <Select
              mode="multiple"
              data={listAllLoaiPhieu || []}
              placeholder={t("danhMuc.vuiLongNhapTenLoaiPhieu")}
            />
          </Form.Item>
          <Form.Item label={t("danhMuc.loaiBenhAn")} name="dsLoaiBenhAnId">
            <Select
              data={listAllLoaiBenhAn || []}
              placeholder={t("danhMuc.vuiLongChonLoaiBenhAn")}
              mode="multiple"
              showArrow
            />
          </Form.Item>
          <Form.Item label={t("danhMuc.loaiIn")} name="loaiIn">
            <Select
              data={listLoaiIn}
              placeholder={t("danhMuc.vuiLongChonLoaiIn")}
            />
          </Form.Item>
          <Form.Item label={t("danhMuc.kieuKy")} name="duLieuKy">
            <Select
              data={listDuLieuKy}
              placeholder={t("danhMuc.vuiLongChonLoaiKieuKy")}
            />
          </Form.Item>
          <Form.Item label={t("danhMuc.hinhThucIn")} name="hinhThucIn">
            <Select
              data={listHinhThucIn}
              placeholder={t("danhMuc.vuiLongChonHinhThucIn")}
            />
          </Form.Item>
          <Form.Item label={t("account.ngonNgu")} name="ngonNgu">
            <Select data={listNgonNgu} placeholder={t("danhMuc.chonNgonNgu")} />
          </Form.Item>
          <Form.Item label={t("danhMuc.ketNoiBenhAn")} name="dsKetNoiBenhAn">
            <Select data={listKetNoiBenhAn} hasAllOption={true} showArrow mode="multiple" placeholder={t("danhMuc.chonKetNoiBenhAn")} />
          </Form.Item>
          <Form.Item label={t("danhMuc.maKySo")} name="maKySo">
            <Input
              className="input-option"
              placeholder={t("danhMuc.nhapMaKySo")}
            />
          </Form.Item>

          <Form.Item label=" " name="kySo" valuePropName="checked">
            <Checkbox>{t("common.KY_SO")}</Checkbox>
          </Form.Item>
          <Form.Item
            label={t("common.ghiChu")}
            name="ghiChu"
            rules={[
              {
                max: 255,
                message: t("danhMuc.vuiLongNhapGhiChuKhongQuaNumKyTu", {
                  num: 255,
                }),
              },
            ]}
          >
            <TextArea
              className="input-option"
              placeholder={t("danhMuc.nhapGhiChu")}
            />
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.kySo !== currentValues.kySo
            }
          >
            {({ getFieldValue }) =>
              getFieldValue("kySo") === true ? (
                <Form.Item label={t("danhMuc.khuVucKy")} name="khuVucKy">
                  <Select
                    data={listKhuVucKy}
                    placeholder={t("danhMuc.vuiLongChonKhuVucKy")}
                  />
                </Form.Item>
              ) : null
            }
          </Form.Item>
          {state.editStatus && (
            <Form.Item label=" " name="active" valuePropName="checked">
              <Checkbox>{t("danhMuc.coHieuLuc")}</Checkbox>
            </Form.Item>
          )}
        </FormWraper>

        <div className="button-bottom-modal">
          <div className="action-left">
            <Button
              type="default"
              onClick={onShowModal}
              rightIcon={<SVG.IcEdit />}
              iconHeight={20}
              minWidth={100}
            >
              {t("danhMuc.phienBanBieuMau")}
            </Button>
          </div>
          <div className="action-right">
            <Button
              type="default"
              onClick={handleCancel}
              hidden={handleHiddenCancel()}
              rightIcon={<SVG.IcCancel />}
              iconHeight={15}
              minWidth={100}
            >
              {t("common.huy")}
            </Button>
            <Button
              type="primary"
              onClick={handleAdded}
              hidden={handleHiddenSave()}
              rightIcon={<SVG.IcSave />}
              iconHeight={15}
              minWidth={100}
            >
              {t("common.luu")} [F4]
            </Button>
          </div>
        </div>
      </Wrapper>
      <ModalPhienBanBieuMau ref={refPhienBanBieuMau}></ModalPhienBanBieuMau>
      <ModalTaoPhienBanBieuMau
        ref={refTaoPhienBanBieuMau}
      ></ModalTaoPhienBanBieuMau>
    </BaoCaoChiTietStyle>
  );
};

export default BaoCaoChiTiet;
