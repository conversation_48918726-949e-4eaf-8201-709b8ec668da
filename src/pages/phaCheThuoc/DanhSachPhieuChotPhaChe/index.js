import React, { useEffect, useRef, useState } from "react";
import { MainPage } from "./styled";
import { Button } from "components";
import { useTranslation } from "react-i18next";
import BangDanhSach from "./BangDanhSach";
import BoLocTimKiem from "./BoLocTimKiem";
import { SVG } from "assets";
import { useHistory } from "react-router-dom";
import { ROLES, TRANG_THAI_PHIEU_XUAT_PHA_CHE } from "constants/index";
import { useStore } from "hooks";
import { useDispatch } from "react-redux";
import ModalNhapLyDo from "pages/kho/components/ModalNhapLyDo";
import { message } from "antd";
import { groupBy } from "lodash";
import { checkRole } from "lib-utils/role-utils";
import ModalChotDonPhaChe from "./ModalChotDonPhaChe";

const DsPhieuChotPhaChe = () => {
  const { t } = useTranslation();
  const history = useHistory();
  const refModalNhapLyDo = useRef(null);
  const refModalChotDonPhaChe = useRef(null);
  const refBangDanhSach = useRef(null);

  const [state, _setState] = useState({});
  const listData = useStore("nbChotPhaCheThuoc.listData", []);
  const {
    nbChotPhaCheThuoc: {
      onSearchTongHop,
      hoanThanhPhieuXuatPhaChe,
      huyHoanThanhPhieuXuatPhaChe,
    },
  } = useDispatch();
  const setState = (data) => {
    _setState((pre) => ({ ...pre, ...data }));
  };

  useEffect(() => {
    const isVisible = (trangThai) =>
      !!state.dsSelected?.length &&
      state.dsSelected.every((o) => o.trangThai === trangThai);

    const visibleHoanThanh = isVisible(TRANG_THAI_PHIEU_XUAT_PHA_CHE.TAO_MOI);
    const visibleHuyHoanThanh = isVisible(
      TRANG_THAI_PHIEU_XUAT_PHA_CHE.HOAN_THANH
    );
    setState({ visibleHoanThanh, visibleHuyHoanThanh });
  }, [state.dsSelected]);

  const onSelect = (data) => {
    const dsSelected = listData.filter((x) => data.includes(`${x.id}`));
    setState({ dsSelected });
  };

  const processErrorMsg = (errors) => {
    const obj = groupBy(errors, "code");
    return Object.keys(obj).map(
      (key) =>
        `${t("common.phieu")} ${obj[key].map((o) => o.id).join(", ")} - ${
          obj[key][0].message
        }`
    );
  };

  const handleAction = async (data, func = () => {}) => {
    const promises = data.map(func);
    const errors = (await Promise.allSettled(promises)).reduce(
      (acc, rs) => (rs.status === "rejected" ? [...acc, rs.reason] : acc),
      []
    );
    if (errors?.length) {
      const messages = processErrorMsg(errors);
      message.error(messages);
    } else {
      message.success(t("common.capNhatThanhCong"));
    }
    if (errors?.length !== data.length) onSearchTongHop({ page: 0 });
  };

  const onHuyHoanThanh = async (data) => {
    refModalNhapLyDo.current &&
      refModalNhapLyDo.current.show(
        {
          title: t("phaCheThuoc.lyDoHuyDuyet"),
          message: t("phaCheThuoc.nhapLyDoHuyDuyet"),
        },
        async (lyDo) => {
          const _data = data.map((id) => ({ id, lyDo }));
          await handleAction(_data, huyHoanThanhPhieuXuatPhaChe);
        }
      );
  };

  const onChotPhaChe = () => {
    refModalChotDonPhaChe.current && refModalChotDonPhaChe.current.show();
  };

  const renderActionButton = () => {
    return (
      <div className="header-action">
        <Button type="success" iconHeight={20} onClick={onChotPhaChe}>
          {t("phaCheThuoc.chotPhaChe")}
        </Button>
      </div>
    );
  };

  return (
    <MainPage
      breadcrumb={[
        { title: t("phaCheThuoc.phaCheThuoc"), link: "/pha-che-thuoc" },
        {
          title: t("phaCheThuoc.danhSachPhieuChotPhaChe"),
          link: "/pha-che-thuoc/danh-sach-phieu-chot-pha-che",
        },
      ]}
      title={t("phaCheThuoc.danhSachPhieuChotPhaChe")}
      titleRight={renderActionButton()}
    >
      <div className="wrapper-container">
        <BoLocTimKiem />
        <BangDanhSach ref={refBangDanhSach} onSelect={onSelect} />
      </div>
      <ModalNhapLyDo ref={refModalNhapLyDo} />
      <ModalChotDonPhaChe ref={refModalChotDonPhaChe} />
    </MainPage>
  );
};

export default DsPhieuChotPhaChe;
