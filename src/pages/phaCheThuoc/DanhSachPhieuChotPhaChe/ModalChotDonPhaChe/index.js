import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { ModalTemplate, Button, Select, DatePicker } from "components";
import { useStore } from "hooks";
import moment from "moment";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { useHistory } from "react-router-dom";
import { Main } from "./styled";
import { DS_TINH_CHAT_KHOA } from "constants/index";
import { Form, message } from "antd";

const ModalChotDonPhaChe = ({}, ref) => {
  const { t } = useTranslation();
  const history = useHistory();
  const [form] = Form.useForm();

  const refModal = useRef(null);
  const refSettings = useRef(null);
  const {
    dataSortColumn,
    page,
    size,
    totalElements,
    listNbLapBenhAn: listData,
  } = useSelector((state) => state.danhSachNguoiBenhNoiTru);
  const listAllKhoaNoiTru = useStore("khoa.listDataTongHop", []);

  const {
    danhSachNguoiBenhNoiTru: {
      onSearch,
      onSizeChange,
      onSortChange,
      onChangeInputSearch,
      clearData,
    },
    khoa: { getListKhoaTongHop },
  } = useDispatch();

  const [state, _setState] = useState({ show: false, slTra: 1 });
  const setState = (data) => {
    _setState((pre) => ({ ...pre, ...data }));
  };

  useImperativeHandle(ref, () => ({
    show: () => {
      setState({ show: true });
      onChangeInputSearch({
        dsTrangThai: [30, 40, 50],
      });
    },
    close: ({} = {}) => {
      setState({ show: false });
    },
  }));

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
      clearData();
    }
  }, [state.show]);

  useEffect(() => {
    getListKhoaTongHop({
      page: "",
      size: "",
      active: true,
      dsTinhChatKhoa: DS_TINH_CHAT_KHOA.NOI_TRU,
    });
    return () => {
      clearData();
    };
  }, []);

  const onHandleSubmit = async () => {
    let values = await centralizedErrorHandling(
      form.validateFields().catch(() => null)
    );
    if (!values) return;
  };

  const onOk = (isOk) => () => {
    if (isOk) {
      onHandleSubmit();
    } else {
      setState({ show: false });
    }
  };

  return (
    <ModalTemplate
      ref={refModal}
      width={800}
      title={t("phaCheThuoc.chonNbPhaChe")}
      onCancel={onOk(false)}
      actionLeft={<Button.QuayLai onClick={onOk(false)} />}
    >
      <Main>
        <Form
          form={form}
          layout="vertical"
          style={{ width: "100%" }}
          className="form-custom"
          initialValues={{
            loaiXuat: "0",
          }}
          actionRight={
            <Button
              type="primary"
              minWidth={100}
              iconHeight={15}
              onClick={onOk(true)}
            >
              {t("common.xuatFile")}
            </Button>
          }
        >
          <Form.Item label={t("quyetToanBhyt.mauSoSanh")} name="mauSoSanh">
            <Select />
          </Form.Item>
          <Form.Item label={t("quyetToanBhyt.loaiSoSanh")} name="loaiSoSanh">
            <Select />
          </Form.Item>

          <Form.Item
            name="duLieuSoSanh1"
            label={t("quyetToanBhyt.duLieuSoSanh1")}
            rules={[
              {
                required: true,
                message: t("danhMuc.vuiLongChonTitle"),
              },
            ]}
          >
            <DatePicker />
          </Form.Item>

          <Form.Item
            name="duLieuSoSanh2"
            label={t("quyetToanBhyt.duLieuSoSanh2")}
            rules={[
              {
                required: true,
                message: t("danhMuc.vuiLongChonTitle"),
              },
            ]}
          >
            <DatePicker />
          </Form.Item>
        </Form>
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalChotDonPhaChe);
