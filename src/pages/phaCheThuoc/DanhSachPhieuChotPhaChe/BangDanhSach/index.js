import React, {
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import {
  Checkbox,
  TableWrapper,
  Pagination,
  Tooltip,
  HeaderSearch,
  Dropdown,
} from "components";
import { useDispatch, useSelector } from "react-redux";
import moment from "moment";
import { useConfirm, useEnum } from "hooks";
import { ENUM, ROLES, TRANG_THAI_PHIEU_XUAT_PHA_CHE } from "constants/index";
import { Main } from "./styled";
import { useTranslation } from "react-i18next";
import { SVG } from "assets";
import { useHistory } from "react-router-dom";
import { Menu } from "antd";
import { checkRole } from "lib-utils/role-utils";

const { Column, Setting } = TableWrapper;

const BangDanhSach = ({ onSelect, ...props }, ref) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const history = useHistory();
  const refSettings = useRef(null);

  const [listTrangThaiPhieuXuatPhaChe] = useEnum(
    ENUM.TRANG_THAI_PHIEU_XUAT_PHA_CHE
  );
  const [listTrangThaiPhieuXuatKho] = useEnum(ENUM.TRANG_THAI_PHIEU_NHAP_XUAT);

  const { page, size, totalElements, listData, dataSortColumn } = useSelector(
    (state) => state.nbChotPhaCheThuoc
  );

  const [state, _setState] = useState({
    isCheckedAll: false,
  });
  const setState = (data = {}) => _setState((pre) => ({ ...pre, ...data }));

  const {
    nbChotPhaCheThuoc: {
      onSearchTongHop,
      onSizeChange,
      onSortChange,
      inPhieuXuatPhaChe,
      inPhieuXuatKho,
      deletePhieuXuatPhaChe,
    },
  } = useDispatch();

  useImperativeHandle(ref, () => ({
    resetSelected: () => {
      setState({ selectedRowKeys: [] });
    },
  }));

  const onChangePage = (page) => {
    onSearchTongHop({ page: page - 1 });
  };

  const handleSizeChange = (size) => {
    onSizeChange({ size });
  };

  const onClickSort = (key, value) => {
    onSortChange({ [key]: value });
  };

  const setRowClassName = (record) => {
    let idDiff;
    return record.id === idDiff ? "row-actived" : "";
  };

  const onView = (record) => () => {
    history.push(
      `/pha-che-thuoc/phieu-xuat-pha-che-thuoc/chi-tiet/${record.id}`
    );
  };

  const onEdit = (record) => () => {
    history.push(
      `/pha-che-thuoc/phieu-xuat-pha-che-thuoc/chinh-sua/${record.id}`
    );
  };

  const onDelete = (record) => (e) => {
    e.stopPropagation();
    showConfirm(
      {
        title: t("phaCheThuoc.xoaHangHoa"),
        content: t(
          "phaCheThuoc.heThongXoaToanBoDuLieuPhieuPhaCheSoPhieuKhoiHeThong"
        ),
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        classNameOkText: "button-error",
        showBtnOk: true,
        typeModal: "error",
      },
      () => {
        deletePhieuXuatPhaChe(record?.id).then((s) => {
          onSearchTongHop({ page: 0 });
        });
      }
    );
  };

  const onCheckAll = (e) => {
    const selectedRowKeys = e.target?.checked
      ? listData.map((x) => String(x.id))
      : [];

    onSelect && onSelect(selectedRowKeys);
    setState({
      selectedRowKeys,
      isCheckedAll: e.target?.checked,
    });
  };

  const rowSelection = {
    columnTitle: (
      <HeaderSearch
        title={
          <Checkbox
            onChange={onCheckAll}
            checked={state.isCheckedAll}
          ></Checkbox>
        }
      />
    ),
    columnWidth: 40,
    onChange: (selectedRowKeys) => {
      onSelect && onSelect(selectedRowKeys);
      setState({
        selectedRowKeys,
        isCheckedAll:
          selectedRowKeys.length > 0 &&
          selectedRowKeys.length == listData.length,
      });
    },
    selectedRowKeys: state.selectedRowKeys,
  };

  const renderMenu = (record) => {
    const listPhieu = [
      {
        key: 1,
        label: (
          <a
            href={() => false}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              inPhieuXuatPhaChe(record.id);
            }}
          >
            {t("phaCheThuoc.phieuXuatPhaChe")}
          </a>
        ),
        accessRole: [ROLES["PHA_CHE_THUOC"].IN_PHIEU_XUAT_PHA_CHE],
      },
      {
        key: 2,
        label: (
          <a
            href={() => false}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              inPhieuXuatKho(record.id);
            }}
          >
            {t("phaCheThuoc.phieuXuatKho")}
          </a>
        ),
        accessRole: [ROLES["PHA_CHE_THUOC"].IN_PHIEU_XUAT_KHO],
      },
    ];
    return (
      <Menu items={listPhieu.filter((item) => checkRole(item.accessRole))} />
    );
  };

  const columns = [
    Column({
      title: t("common.stt"),
      width: "30px",
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
      show: true,
      render: (_, __, index) => size * page + index + 1,
    }),
    Column({
      title: t("phaCheThuoc.ngayLapPhieu"),
      sort_key: "thoiGianTaoPhieu",
      dataSort: dataSortColumn["thoiGianTaoPhieu"] || "",
      onClickSort: onClickSort,
      width: "80px",
      dataIndex: "thoiGianTaoPhieu",
      key: "thoiGianTaoPhieu",
      i18Name: "phaCheThuoc.ngayLapPhieu",
      show: true,
      render: (value) => {
        return value ? moment(value).format("DD/MM/YYYY HH:mm:ss") : null;
      },
    }),
    Column({
      title: t("phaCheThuoc.soPhieuXuatPhaChe"),
      sort_key: "soPhieu",
      dataSort: dataSortColumn["soPhieu"] || "",
      onClickSort: onClickSort,
      width: "100px",
      dataIndex: "soPhieu",
      key: "soPhieu",
      i18Name: "phaCheThuoc.soPhieuXuatPhaChe",
      show: true,
    }),
    Column({
      title: t("phaCheThuoc.tenPhieuXuatPhaChe"),
      sort_key: "ten",
      dataSort: dataSortColumn["ten"] || "",
      onClickSort: onClickSort,
      width: "100px",
      dataIndex: "ten",
      key: "ten",
      i18Name: "phaCheThuoc.tenPhieuXuatPhaChe",
      show: true,
    }),
    Column({
      title: t("phaCheThuoc.trangThaiPhieuXuatPhaChe"),
      sort_key: "trangThai",
      dataSort: dataSortColumn["trangThai"] || "",
      onClickSort: onClickSort,
      width: "100px",
      dataIndex: "trangThai",
      key: "trangThai",
      i18Name: "phaCheThuoc.trangThaiPhieuXuatPhaChe",
      show: true,
      render: (value) =>
        listTrangThaiPhieuXuatPhaChe?.find((item) => item.id === value)?.ten,
    }),
    Column({
      title: t("phaCheThuoc.trangThaiPhieuXuatKho"),
      sort_key: "trangThaiPhieuXuatKho",
      dataSort: dataSortColumn["trangThaiPhieuXuatKho"] || "",
      onClickSort: onClickSort,
      width: "100px",
      dataIndex: "trangThaiPhieuXuatKho",
      key: "trangThaiPhieuXuatKho",
      i18Name: "phaCheThuoc.trangThaiPhieuXuatKho",
      show: true,
      render: (value) =>
        listTrangThaiPhieuXuatKho?.find((item) => item.id === value)?.ten,
    }),

    Column({
      title: t("phaCheThuoc.nguoiLapPhieuXuatPhaChe"),
      sort_key: "tenNguoiTaoPhieu",
      dataSort: dataSortColumn["tenNguoiTaoPhieu"] || "",
      onClickSort: onClickSort,
      width: "100px",
      dataIndex: "tenNguoiTaoPhieu",
      key: "tenNguoiTaoPhieu",
      i18Name: "phaCheThuoc.nguoiLapPhieuXuatPhaChe",
      show: true,
    }),
    Column({
      title: t("phaCheThuoc.soPhieuXuatKho"),
      sort_key: "soPhieuXuatKho",
      dataSort: dataSortColumn["soPhieuXuatKho"] || "",
      onClickSort: onClickSort,
      width: "80px",
      dataIndex: "soPhieuXuatKho",
      key: "soPhieuXuatKho",
      i18Name: "phaCheThuoc.soPhieuXuatKho",
      show: true,
    }),
    Column({
      title: t("phaCheThuoc.khoXuat"),
      sort_key: "tenKho",
      dataSort: dataSortColumn["tenKho"] || "",
      onClickSort: onClickSort,
      width: "100px",
      dataIndex: "tenKho",
      key: "tenKho",
      i18Name: "phaCheThuoc.khoXuat",
      show: true,
    }),
    Column({
      title: (
        <div className="col-tien-ich">
          {t("common.tienIch")} <Setting refTable={refSettings} />
        </div>
      ),
      width: "80px",
      dataIndex: "action",
      key: "action",
      fixed: "right",
      ignore: true,
      align: "center",
      render: (_, record) => {
        return (
          <>
            {checkRole([
              ROLES["PHA_CHE_THUOC"].XEM_CHI_TIET_PHIEU_XUAT_PHA_CHE_THUOC,
            ]) && (
              <Tooltip
                title={t("phaCheThuoc.chiTietPhieuPhaChe")}
                placement="bottomLeft"
              >
                <SVG.IcEye className="ic-action" onClick={onView(record)} />
              </Tooltip>
            )}
            {checkRole([ROLES["PHA_CHE_THUOC"].SUA_PHIEU_XUAT_PHA_CHE_THUOC]) &&
              record?.trangThai !== TRANG_THAI_PHIEU_XUAT_PHA_CHE.HOAN_THANH &&
              record?.trangThaiPhieuXuatKho !==
                TRANG_THAI_PHIEU_XUAT_PHA_CHE.CHO_DUYET && (
                <Tooltip
                  title={t("phaCheThuoc.suaPhieuPhaChe")}
                  placement="bottomLeft"
                >
                  <SVG.IcEdit onClick={onEdit(record)} className="icon" />
                </Tooltip>
              )}
            {checkRole([
              ROLES["PHA_CHE_THUOC"].XOA_PHIEU_XUAT_PHA_CHE_THUOC,
            ]) && (
              <Tooltip
                title={t("phaCheThuoc.xoaPhieuPhaChe")}
                placement="bottomLeft"
              >
                <SVG.IcDelete onClick={onDelete(record)} className="icon" />
              </Tooltip>
            )}
            <Dropdown
              overlay={renderMenu(record)}
              trigger={["click"]}
              onClick={(e) => e.stopPropagation()}
            >
              <SVG.IcPrint className="icon" />
            </Dropdown>
          </>
        );
      },
    }),
  ];
  const onRow = (record, index) => {
    return {
      onClick: (event) =>
        checkRole([
          ROLES["PHA_CHE_THUOC"].XEM_CHI_TIET_PHIEU_XUAT_PHA_CHE_THUOC,
        ]) && onView(record)(),
    };
  };
  return (
    <Main noPadding={true}>
      <TableWrapper
        columns={columns}
        dataSource={listData || []}
        onRow={onRow}
        rowKey={(record) => `${record.id}`}
        rowClassName={setRowClassName}
        scroll={{ x: 1366 }}
        tableName="table_PhaChe_DsPhaCheThuoc"
        ref={refSettings}
        rowSelection={rowSelection}
      />
      {!!totalElements && (
        <Pagination
          onChange={onChangePage}
          current={page + 1}
          pageSize={size}
          total={totalElements}
          listData={listData || []}
          onShowSizeChange={handleSizeChange}
        />
      )}
    </Main>
  );
};

export default forwardRef(BangDanhSach);
