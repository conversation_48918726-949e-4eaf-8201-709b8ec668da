import React, { useEffect, useState } from "react";
import { Main } from "./styled";
import { useDispatch } from "react-redux";
import { BaseSearch } from "components";
import { ENUM } from "constants/index";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { useEnum, useStore } from "hooks";

const BoLocTimKiem = () => {
  const { t } = useTranslation();
  const [listLoaiPhieuXuatPhaChe] = useEnum(ENUM.TRANG_THAI_PHIEU_XUAT_PHA_CHE);
  const listKhoUser = useStore("kho.listKhoUser", []);

  const {
    nbChotPhaCheThuoc: { onChangeInputSearch },
    kho: { getTheoTaiKhoan: getKhoTheoTaiKhoan },
  } = useDispatch();

  const [state, _setState] = useState({
    tuThoiGianTaoPhieu: moment()
      .set("hour", 0)
      .set("minute", 0)
      .set("second", 0),
    denThoiGianTaoPhieu: moment()
      .set("hour", 23)
      .set("minute", 59)
      .set("second", 59),
    tenKho: "",
    dsTrangThai: [10, 30],
    trangThaiMacDinh: [10, 30],
  });

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    onChangeInputSearch({
      tuThoiGianTaoPhieu: state.tuThoiGianTaoPhieu?.format(
        "YYYY-MM-DD 00:00:00"
      ),
      denThoiGianTaoPhieu: state.denThoiGianTaoPhieu?.format(
        "YYYY-MM-DD 23:59:59"
      ),
      tenKho: state.tenKho,
    });

    getKhoTheoTaiKhoan({ page: "", page: "", active: true });
  }, []);

  const onSearchInput = (type) => (data) => {
    if (type == "thoiGianTaoPhieu") {
      onChangeInputSearch({
        tuThoiGianTaoPhieu: data.tuThoiGianTaoPhieu?.format(
          "YYYY-MM-DD 00:00:00"
        ),
        denThoiGianTaoPhieu: data.denThoiGianTaoPhieu?.format(
          "YYYY-MM-DD 23:59:59"
        ),
      });
    }
    if (type === "dsTrangThai") {
      setState({ dsTrangThai: data.dsTrangThai });
      onChangeInputSearch({ dsTrangThai: data.dsTrangThai });
    }
    if (["soPhieu", "tenKho", "soPhieuXuatKho"].includes(type)) {
      onChangeInputSearch({ [type]: data[type] });
    }
  };

  return (
    <Main>
      <BaseSearch
        cacheData={{
          tuThoiGianTaoPhieu: state.tuThoiGianTaoPhieu,
          denThoiGianTaoPhieu: state.denThoiGianTaoPhieu,
          tenKho: state.tenKho,
          dsTrangThai: state.dsTrangThai,
        }}
        dataInput={[
          {
            widthInput: "232px",
            type: "dateOptions",
            state: state,
            setState: setState,
            keyValueInput: ["tuThoiGianTaoPhieu", "denThoiGianTaoPhieu"],
            functionChangeInput: onSearchInput("thoiGianTaoPhieu"),
            title: t("phaCheThuoc.thoiGianLapPhieu"),
            placeholder: t("phaCheThuoc.thoiGianLapPhieu"),
            format: "DD/MM/YYYY",
          },
          {
            widthInput: "200px",
            placeholder: t("phaCheThuoc.soPhieuXuatPhaChe"),
            keyValueInput: "soPhieu",
            functionChangeInput: onSearchInput("soPhieu"),
          },
          {
            widthInput: "200px",
            placeholder: t("phaCheThuoc.soPhieuXuatKho"),
            keyValueInput: "soPhieuXuatKho",
            functionChangeInput: onSearchInput("soPhieuXuatKho"),
          },
          {
            widthInput: "200px",
            placeholder: t("phaCheThuoc.khoXuat"),
            keyValueInput: "tenKho",
            functionChangeInput: onSearchInput("tenKho"),
            type: "select",
            value: state.tenKho,
            defaultValue: "",
            listSelect: listKhoUser,
            hasAllOption: true,
          },
          {
            widthInput: "200px",
            title: t("phaCheThuoc.trangThai"),
            keyValueInput: "dsTrangThai",
            functionChangeInput: onSearchInput("dsTrangThai"),
            type: "selectCheckbox",
            defaultValue: state.trangThaiMacDinh,
            hasCheckAll: true,
            virtual: true,
            listSelect: listLoaiPhieuXuatPhaChe,
          },
        ]}
      />
    </Main>
  );
};
export default BoLocTimKiem;
