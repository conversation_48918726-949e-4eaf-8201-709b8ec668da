import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { useDispatch, useSelector } from "react-redux";
import { useConfirm, useEnum, useListAll, useLoading } from "hooks";
import {
  setQueryStringValues,
  setQueryStringValue,
} from "hooks/useQueryString/queryString";
import {
  TableWrapper,
  Pagination,
  Tooltip,
  HeaderSearch,
  Checkbox,
  Dropdown,
  ModalSignPrint,
} from "components";
import { ENUM, LOAI_BIEU_MAU, TRANG_THAI_NB } from "constants/index";
import { SVG } from "assets";
import ModalChiTietHoiChan from "../../ModalChiTietHoiChan";
import { isArray, openInNewTab } from "utils/index";
import { Menu } from "antd";
import printProvider from "data-access/print-provider";
import { checkIsPhieuKySo } from "utils/phieu-utils";
import ModalSaoChep from "../../ModalSaoChep";
import ModalTomTat from "../../ModalTomTat";
import { sanitize } from "dompurify";
import { DanhSachHoiChanWrap } from "../styled";

const { Column, Setting } = TableWrapper;

const TableHoiChan = ({ setParentState }) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const { showLoading, hideLoading } = useLoading();

  const refSettings = useRef(null);
  const [listTienLuong] = useEnum(ENUM.TIEN_LUONG);
  const [listLoaiHoiChan] = useEnum(ENUM.LOAI_HOI_CHAN);
  const [listAllKhoa] = useListAll("khoa", {}, true);
  const [listAllNhanVien] = useListAll("nhanVien", {}, true);
  const refModalChiTietHoiChan = useRef(null);
  const refModalSignPrint = useRef(null);
  const mounted = useRef();
  const refModalSaoChep = useRef(null);
  const refModalTomTat = useRef(null);
  const currentItem =
    useSelector((state) => state.nbBienBanHoiChan.currentItem) || {};
  const {
    listData,
    page,
    size,
    totalElements,
    dataSortColumn,
    isLoading,
    isBatBuocHoiChanThuong,
  } = useSelector((state) => state.nbBienBanHoiChan);
  const {
    nbBienBanHoiChan: {
      onSizeChange,
      onSearchAll,
      onSortChange,
      searchDsHoiChanByParams,
      onDelete,
      updateData,
    },
    phieuIn: { getListPhieu, getFilePhieuIn, showFileEditor },
  } = useDispatch();

  const [state, _setState] = useState({ selectedRowKeys: [], listPhieu: [] });
  const setState = (data) => {
    _setState({ ...state, ...data });
  };

  const onGetListPhieu = (record) => (e) => {
    e.stopPropagation();
    getListPhieu({
      maManHinh: "023",
      maViTri: "02301",
      nbDotDieuTriId: record?.nbDotDieuTriId,
      chiDinhTuDichVuId: record?.id,
    }).then((res) => {
      setState({
        listPhieu: res || [],
      });
    });
  };

  useEffect(() => {
    if (!mounted.current) {
      mounted.current = true;
    } else if (!isLoading && isArray(listData, true)) {
      setParentState({ listSelected: [] });
      setState({ selectedRowKeys: [] });
    }
  }, [listData, isLoading]);

  const onClickSort = (key, value) => {
    onSortChange({ [key]: value, all: true });
  };

  const onChangePage = (page) => {
    setQueryStringValue("page", page - 1);
    onSearchAll({ page: page - 1 });
  };

  const handleSizeChange = (size) => {
    setQueryStringValues({ size: size, page: 0 });
    onSizeChange({ size, all: true });
  };

  const onEditItem = (data) => (e) => {
    e.stopPropagation();
    refModalChiTietHoiChan.current?.show(data);
  };

  const onPrintPhieuWordExcel = async (id, item) => {
    try {
      showLoading();
      const { finalFile, dsPhieu } = await getFilePhieuIn({
        listPhieus: [item],
        showError: true,
        bienBanHoiChanId: id,
      });
      if ((dsPhieu || []).every((x) => x?.loaiIn == 20)) {
        openInNewTab(finalFile);
      } else {
        printProvider.printPdf(dsPhieu);
      }
    } catch (error) {
      console.log("error", error);
    } finally {
      hideLoading();
    }
  };

  const onPrintPhieu = (record, item) => async (e) => {
    e.stopPropagation();
    if (item.loaiBieuMau == LOAI_BIEU_MAU.BIEU_MAU_CHINH_SUA) {
      let mhParams = {};
      //kiểm tra phiếu ký số
      if (checkIsPhieuKySo(item)) {
        //nếu là ký số thì lấy các thông tin về lịch sử ký + tên chân ký + báo cáo id
        mhParams = {
          maManHinh: "023",
          maViTri: "02301",
          chiDinhTuDichVuId: record?.id,
          kySo: true,
          maPhieuKy: item.ma,
          nbDotDieuTriId: record?.nbDotDieuTriId,
        };
      }

      showFileEditor({
        phieu: item,
        nbDotDieuTriId: record?.nbDotDieuTriId,
        chiDinhTuDichVuId: record?.id,
        ma: item.ma,
        mhParams,
      });
    } else {
      if (checkIsPhieuKySo(item)) {
        refModalSignPrint.current &&
          refModalSignPrint.current.showToSign({
            phieuKy: item,
            payload: {
              nbDotDieuTriId: record?.nbDotDieuTriId,
              chiDinhTuDichVuId: record?.id,
              bienBanHoiChanId: record?.id,
              maManHinh: "023",
              maViTri: "02301",
            },
          });
      } else {
        onPrintPhieuWordExcel(record?.id, item);
      }
    }
  };

  const contentPrint = (record) => {
    return (
      <Menu
        items={(state?.listPhieu || []).map((item, index) => ({
          key: index,
          label: (
            <a href={() => false} onClick={onPrintPhieu(record, item)}>
              {item.ten || item.tenBaoCao}
            </a>
          ),
        }))}
      />
    );
  };

  const columns = [
    Column({
      title: t("common.stt"),
      width: 50,
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
    }),
    Column({
      title: t("hoiChan.khoaDangDieuTri"),
      sort_key: "tenKhoa",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["tenKhoa"] || "",
      width: 200,
      dataIndex: "tenKhoa",
      key: "tenKhoa",
      i18Name: "hoiChan.khoaDangDieuTri",
    }),
    Column({
      title: t("khth.hoTenNb"),
      sort_key: "tenNb",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["tenNb"] || "",
      width: 180,
      dataIndex: "tenNb",
      key: "tenNb",
      i18Name: "khth.hoTenNb",
    }),
    Column({
      title: t("common.ngaySinh"),
      sort_key: "ngaySinh",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["ngaySinh"] || "",
      width: 120,
      dataIndex: "ngaySinh",
      key: "ngaySinh",
      i18Name: "common.ngaySinh",
      render: (value) => value && moment(value).format("DD/MM/YYYY"),
    }),
    Column({
      title: t("common.maBenhAn"),
      sort_key: "maBenhAn",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["maBenhAn"] || "",
      width: 120,
      dataIndex: "maBenhAn",
      key: "maBenhAn",
      i18Name: "common.maBenhAn",
    }),
    Column({
      title: t("common.maHoSo"),
      sort_key: "maHoSo",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["maHoSo"] || "",
      width: 120,
      dataIndex: "maHoSo",
      key: "maHoSo",
      i18Name: "common.maHoSo",
    }),
    Column({
      title: t("hoiChan.khoaHoiChan"),
      sort_key: "tenKhoaHoiChan",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["tenKhoaHoiChan"] || "",
      width: 180,
      dataIndex: "tenKhoaHoiChan",
      key: "tenKhoaHoiChan",
      i18Name: "hoiChan.khoaHoiChan",
    }),
    Column({
      title: t("hoiChan.ngayHoiChan"),
      sort_key: "thoiGianThucHien",
      width: 150,
      dataIndex: "thoiGianThucHien",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["thoiGianThucHien"] || "",
      key: "thoiGianThucHien",
      i18Name: "hoiChan.ngayHoiChan",
      render: (item) => {
        return item && moment(item).format("DD/MM/YYYY HH:mm:ss");
      },
    }),
    Column({
      title: t("hoiChan.ngayChiaMo"),
      sort_key: "thoiGianChiaMo",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["thoiGianChiaMo"] || "",
      width: 150,
      dataIndex: "thoiGianChiaMo",
      i18Name: "hoiChan.ngayChiaMo",
      render: (item) => {
        return item && moment(item).format("DD/MM/YYYY HH:mm:ss");
      },
    }),
    Column({
      title: t("common.ghiChu"),
      sort_key: "ghiChu",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["ghiChu"] || "",
      width: 150,
      dataIndex: "ghiChu",
      i18Name: "common.ghiChu",
      hidden: !isBatBuocHoiChanThuong,
      render: (item) => {
        return (
          <div
            dangerouslySetInnerHTML={{
              __html: sanitize(item?.replaceAll("\n", "<br/>")),
            }}
          />
        );
      },
    }),
    Column({
      title: t("hoiChan.chuTri"),
      sort_key: "chuTriId",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["chuTriId"] || "",
      width: 180,
      dataIndex: "chuTriId",
      i18Name: "hoiChan.chuTri",
      key: "chuTriId",
      render: (item) => {
        return listAllNhanVien.find((i) => i.id === item)?.ten;
      },
    }),
    Column({
      title: t("pttt.phauThuatVienChinh"),
      sort_key: "ptvId",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["ptvId"] || "",
      width: 180,
      dataIndex: "ptvId",
      i18Name: "pttt.phauThuatVienChinh",
      key: "ptvId",
      render: (item) => {
        return listAllNhanVien.find((i) => i.id === item)?.ten;
      },
      hidden: !isBatBuocHoiChanThuong,
    }),
    Column({
      title: t("hsba.huongDieuTri"),
      sort_key: "huongDieuTri",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["huongDieuTri"] || "",
      width: 120,
      dataIndex: "huongDieuTri",
      key: "huongDieuTri",
      i18Name: "hsba.huongDieuTri",
      hidden: !isBatBuocHoiChanThuong,
      render: (item) => {
        return (
          <div
            dangerouslySetInnerHTML={{
              __html: sanitize(item?.replaceAll("\n", "<br/>")),
            }}
          />
        );
      },
    }),
    Column({
      title: t("hoiChan.ketLuan"),
      sort_key: "ketLuan",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["ketLuan"] || "",
      width: 120,
      dataIndex: "ketLuan",
      key: "ketLuan",
      i18Name: "hoiChan.ketLuan",
      hidden: !isBatBuocHoiChanThuong,
      render: (item) => {
        return (
          <div
            dangerouslySetInnerHTML={{
              __html: sanitize(item?.replaceAll("\n", "<br/>")),
            }}
          />
        );
      },
    }),
    Column({
      title: t("hoiChan.tienLuong"),
      sort_key: "tienLuong",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["tienLuong"] || "",
      width: 180,
      dataIndex: "tienLuong",
      i18Name: "hoiChan.tienLuong",
      key: "tienLuong",
      render: (item) => {
        return listTienLuong.find((i) => i.id === item)?.ten;
      },
    }),
    Column({
      title: t("hoiChan.loaiHoiChan"),
      sort_key: "loaiHoiChan",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["loaiHoiChan"] || "",
      width: 150,
      dataIndex: "loaiHoiChan",
      i18Name: "hoiChan.loaiHoiChan",
      key: "loaiHoiChan",
      render: (item) => {
        return listLoaiHoiChan.find((i) => i.id === item)?.ten;
      },
    }),
    Column({
      title: t("hoiChan.khoaThamGiaHoiChan"),
      sort_key: "dsKhoaHoiChan",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["dsKhoaHoiChan"] || "",
      width: 150,
      dataIndex: "dsKhoaHoiChan",
      i18Name: "hoiChan.khoaThamGiaHoiChan",
      key: "dsKhoaHoiChan",
      render: (item) => item?.map((i) => i?.ten).join(", "),
    }),
    Column({
      title: (
        <>
          {t("common.khac")}
          <Setting refTable={refSettings} />
        </>
      ),
      width: 160,
      align: "center",
      fixed: "right",
      ignore: true,
      render: (_, item) => {
        return (
          <div className="action-wrapper">
            {!isBatBuocHoiChanThuong && (
              <Tooltip title={t("common.xemChiTiet")}>
                <SVG.IcEye onClick={onViewDetail(item)} className="ic-action" />
              </Tooltip>
            )}
            <Tooltip title={t("common.chinhSua")}>
              <SVG.IcEdit onClick={onEditItem(item)} className="ic-action" />
            </Tooltip>
            <Dropdown
              placement="bottomRight"
              overlay={contentPrint(item)}
              trigger={["click"]}
            >
              <SVG.IcPrint
                onClick={onGetListPhieu(item)}
                className="ic-action"
              />
            </Dropdown>
            <Tooltip title={t("common.xoa")}>
              <SVG.IcDelete
                onClick={showModalConfirmDelete(item)}
                className="ic-action"
              />
            </Tooltip>
            {[
              TRANG_THAI_NB.CHO_TIEP_NHAN_VAO_KHOA,
              TRANG_THAI_NB.DANG_DIEU_TRI,
              TRANG_THAI_NB.DANG_CHUYEN_KHOA,
            ].includes(item.trangThai) && (
              <Tooltip title={t("khamBenh.saoChep")}>
                <SVG.IcSaoChep
                  onClick={() => {
                    refModalSaoChep.current &&
                      refModalSaoChep.current.show(item);
                  }}
                  className="ic-action"
                />
              </Tooltip>
            )}
          </div>
        );
      },
    }),
  ];

  const onDeleteItem = (item) => {
    onDelete(item.id).then(() => {
      searchDsHoiChanByParams({ page: 0, size: 10 });
    });
  };

  const showModalConfirmDelete = (item) => (e) => {
    e.stopPropagation();
    showConfirm(
      {
        title: t("common.canhBao"),
        content: `${t("quanLyNoiTru.banCoChacChanXoaPhieuKhoiHeThong")}?`,
        cancelText: t("common.huy"),
        okText: t("common.xacNhan"),
        showBtnOk: true,
        typeModal: "error",
        classNameOkText: "button-error",
      },
      () => {
        onDeleteItem(item);
      },
      () => {}
    );
  };

  const onSelectChange = (selectedRowKeys, data) => {
    setParentState({ listSelected: [...new Set(selectedRowKeys)] });
    setState({
      selectedRowKeys: [...new Set(selectedRowKeys)],
    });
  };

  const rowSelection = {
    columnTitle: (
      <HeaderSearch title={<Checkbox style={{ display: "none" }} />} />
    ),
    columnWidth: 40,
    onChange: onSelectChange,
    selectedRowKeys: state.selectedRowKeys,
  };

  const onRow = (record) => ({
    onClick: () => {
      updateData({ currentItem: record });
    },
  });

  const onViewDetail = (data) => (e) => {
    e.stopPropagation();
    refModalTomTat.current?.show({
      hoiChan: data,
      listTienLuong,
      listAllKhoa,
      listAllNhanVien,
    });
  };

  const setRowClassName = (record) => {
    return record.id === currentItem?.id
      ? "row-actived row-id-" + record.id
      : "row-id-" + record.id;
  };

  return (
    <DanhSachHoiChanWrap noPadding={true}>
      <TableWrapper
        columns={columns}
        dataSource={listData || []}
        onRow={onRow}
        rowKey={(record) => `${record.id}`}
        tableName="table_hoiChan_DsNbHoiChan"
        loading={!!isLoading}
        ref={refSettings}
        rowSelection={rowSelection}
        rowClassName={setRowClassName}
      />
      {!!totalElements && (
        <Pagination
          listData={listData || []}
          onChange={onChangePage}
          current={page + 1}
          pageSize={size}
          total={totalElements}
          onShowSizeChange={handleSizeChange}
        />
      )}
      {!isBatBuocHoiChanThuong && <ModalTomTat ref={refModalTomTat} />}
      <ModalChiTietHoiChan ref={refModalChiTietHoiChan} />
      <ModalSignPrint ref={refModalSignPrint} />
      <ModalSaoChep ref={refModalSaoChep} />
    </DanhSachHoiChanWrap>
  );
};

export default TableHoiChan;
