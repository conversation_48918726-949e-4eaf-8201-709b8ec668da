import React, { forwardRef, useEffect, useState } from "react";
import { Row, Col } from "antd";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import moment from "moment";
import { useEnum, useListAll } from "hooks";
import { sanitize } from "dompurify";
import { ENUM } from "constants";
import EditWrapper from "components/MultiLevelTab/EditWrapper";
import { Select, TextField } from "components";
import { ThongTinHoiChanWrap } from "../styled";
import { TextFieldStyled } from "../../ModalChiTietHoiChan/styled";

function ThongTinHoiChan(props) {
  const { t } = useTranslation();
  const { currentItem, updateData } = props;
  const [listTienLuong] = useEnum(ENUM.TIEN_LUONG);
  const [listAllNhanVien] = useListAll("nhanVien", {}, true);
  const [listgioiTinh] = useEnum(ENUM.GIOI_TINH);
  const [listCapHoi<PERSON>han] = useEnum(ENUM.CAP_HOI_CHAN);

  const [state, _setState] = useState({});
  const setState = (data) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const {
    nbBienBanHoiChan: { createOrEdit },
  } = useDispatch();

  useEffect(() => {
    if (currentItem?.id) {
      const _gioiTinh =
        (listgioiTinh || []).find((item) => item.id === currentItem.gioiTinh) ||
        {};
      const findEl = (key, data) => (data || []).find((i) => i.id === key)?.ten;
      setState({
        ...currentItem,
        thuKi: currentItem.tenThuKy,
        gioiTinh: _gioiTinh,
        chuTri: findEl(currentItem.chuTriId, listAllNhanVien),
        tienLuong: findEl(currentItem.tienLuong, listTienLuong),
        capHoiChan: findEl(currentItem.capHoiChan, listCapHoiChan),
      });
    } else {
    }
  }, [currentItem]);

  const handleCancel = () => {
    updateData({ currentItem: {} });
  };

  const handleSave = () => {
    createOrEdit({
      id: state.id,
      ptvId: state.ptvId,
      ghiChu: state.ghiChu,
    });
  };

  return (
    <ThongTinHoiChanWrap>
      <EditWrapper
        onCancel={handleCancel}
        onSave={handleSave}
        isShowCancelButton={true}
        isShowSaveButton={true}
      >
        <Row gutter={[16, 16]}>
          <Col xs={24} xxl={12}>
            <b>{t("hoiChan.ngayHoiChan")}: </b>
            <span>
              {state.thoiGianThucHien
                ? moment(state.thoiGianThucHien).format("DD/MM/YYYY hh:mm:ss")
                : ""}
            </span>
          </Col>
          <Col span={12}>
            <b>{t("hoiChan.capHoiChan")}: </b>
            <span>{state.capHoiChan}</span>
          </Col>
          <Col span={12}>
            <b>{t("hoiChan.tienLuong")}: </b>
            <span>{state.tienLuong}</span>
          </Col>
          <Col span={12}>
            <b>{t("hoiChan.chuTri")}: </b>
            <span>{state.chuTri}</span>
          </Col>
          <Col span={12}>
            <b>{t("hoiChan.thuKy")}: </b>
            <span>{state.thuKi}</span>
          </Col>
          <Col xs={16} xxl={12} className="select-hoi-chan">
            <b>{t("pttt.phauThuatVienChinh")}: </b>
            <Select
              placeholder={t("pttt.phauThuatVienChinh")}
              data={listAllNhanVien}
              ten="ten"
              onChange={(value, option) => {
                setState({ ptvId: value });
              }}
              value={state.ptvId}
            />
          </Col>
          <Col span={12}>
            <b>{t("hoiChan.nhanXetBenhTrang")}: </b>
            <div
              dangerouslySetInnerHTML={{
                __html: sanitize(state.nhanXetBenh?.replaceAll("\n", "<br/>")),
              }}
            />
          </Col>
          <Col span={12}>
            <b>{t("common.ghiChu")}: </b>
            <TextFieldStyled>
              <TextField
                className="input_custom"
                marginTop={5}
                onChange={(e) => setState({ ghiChu: e })}
                html={state.ghiChu ?? ""}
                type="html"
              />
            </TextFieldStyled>
          </Col>
          <Col span={24}>
            <b>{t("hoiChan.tomTatQuaTrinhBienBienDieuTriBenh")}: </b>
            <div
              dangerouslySetInnerHTML={{
                __html: sanitize(state.dienBienBenh?.replaceAll("\n", "<br/>")),
              }}
            />
          </Col>
          <Col span={24}>
            <b>{t("hoiChan.ketLuan")}: </b>
            <div
              dangerouslySetInnerHTML={{
                __html: sanitize(state.ketLuan?.replaceAll("\n", "<br/>")),
              }}
            />
          </Col>
          <Col span={24}>
            <b>{t("hoiChan.huongDieuTri")}: </b>
            <div
              dangerouslySetInnerHTML={{
                __html: sanitize(state.huongDieuTri),
              }}
            />
          </Col>
        </Row>
      </EditWrapper>
    </ThongTinHoiChanWrap>
  );
}

export default forwardRef(ThongTinHoiChan);
