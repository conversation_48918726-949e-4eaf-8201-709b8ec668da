import styled, { createGlobalStyle } from "styled-components";

export const MainBienBanHoiChan = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;

  fieldset {
    display: flex;
    flex-direction: column;

    overflow: hidden;
  }

  .item {
    width: 100%;
    display: flex;
    overflow: hidden;
  }

  .container {
    padding: 0 8px !important;
  }
  .icon-tab {
    margin-right: 10px;
  }
  .ant-row {
    width: 100%;
  }
  .content {
    flex: 1;
    max-height: 60vh;
    min-height: 60vh;
    overflow-y: auto;
    margin-top: 0px;
    margin-bottom: 0px;
    .date {
      display: flex;
      .left {
        display: flex;
        align-items: center;
        .title {
          padding-right: 7px;
        }
        .col {
          display: flex;
          flex-wrap: wrap;
          flex-direction: row;
          align-items: center;
          padding-right: 10px;
        }
        .col-checkbox {
          line-height: 38px;
        }
        @media (max-width: 767px) {
          & {
            flex-direction: column;
            align-items: flex-start;
            .col {
              padding-right: 0;
              padding-bottom: 5px;
            }
          }
        }
      }
      .right {
        position: absolute;
        right: 0;
        top: 0;
      }
    }
    .info {
      height: calc(100vh - 360px);
      overflow: auto;
    }
    .table-content {
      display: flex;
      flex-direction: column;
      .header-table {
        display: flex;
        margin: 10px 0 5px;
        .left {
          border-radius: 4px;
          border: 3px solid #cdddfe;
          width: 200px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 10px;
          cursor: pointer;
          img {
            object-fit: none;
          }
        }
        .right {
          position: relative;
          width: 275px;
          margin-left: auto;
          > svg {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            left: 0;
            z-index: 1;
            padding: 0 8px;
            width: 30px;
          }
          input {
            padding-left: 25px;
          }
        }
      }
    }
    .label {
      padding-top: 3px;
      padding-right: 7px;
    }
    .lanDieuTri .label {
      padding-top: 3px;
      @media (max-width: 1536px) {
        & {
          padding-top: 2px;
        }
      }
    }
    .select-hoi-chan {
      border-radius: 3px;
      .ant-select-selector {
        border-radius: 3px;
      }
    }
    .ant-checkbox + span {
      color: rgba(0, 0, 0, 0.85);
    }
    .nhom-mau {
      background-color: #fff !important;
      background: none;
      margin-top: 1px;
    }
  }
  h1 {
    font-style: normal;
    font-weight: 700;
    font-size: 20px;
    line-height: 24px;
    margin: 1em 0 0.5em;
  }

  .table-button {
    margin-top: 10px;
  }

  .action-bottom {
    padding: 0 1rem 1rem;
    display: flex;
    .button-left {
      display: flex;
    }
    .button-right {
      margin-left: auto;
      display: flex;
    }
  }

  .action {
    display: flex;
    justify-content: center;
    gap: 1rem;
    svg {
      width: 20px;
      height: 20px;
    }
  }

  .footer {
    margin-top: 16px;
    display: flex;
    justify-content: end;
  }

  ul > li {
    list-style-type: disc;
    margin-left: 20px;
  }
  .text-field {
    width: 100%;
  }
`;

export const SelectGroup = styled.div`
  line-height: 25px;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 6 6"><circle cx="3" cy="3" r="0.4" fill="black" /></svg>')
    20px;
  background-position-y: 12px;
  background-size: 5px 28px;
  margin-top: ${({ $marginTop }) => ($marginTop ? $marginTop : "10px")};
  display: flex;
  > span {
    padding-top: 8px;
    padding-bottom: ${({ $paddingBottom }) =>
      $paddingBottom ? $paddingBottom : "0"};
    display: inline-block;
    padding-right: 5px;
    background: #ffffff;
    vertical-align: sub;
    flex: 1 0 auto;
    /* height: ${(props) =>
      props.dataHeight ? props.dataHeight + "px" : "auto"}; */
  }
  .red-text {
    color: #ef4066;
  }
  .select-box {
    display: inline-block;
    width: 100%;
    &
      .ant-select
      .ant-select-multiple
      .ant-select-allow-clear
      .ant-select-show-search {
      width: auto;
    }
    & .ant-select {
      width: 100%;
      &.ant-select-show-search {
        width: auto;
      }
    }
    & .ant-select-selector {
      background: none;
      border: 0;
    }
  }
  & .ant-select-single {
    & .ant-select-selection-item {
      line-height: 38px;
    }
  }
`;

export const GlobalStyle = createGlobalStyle`
.popover-list-giay-in{
  .ant-popover-inner-content{
    padding: 0;
    .item-file{
      padding: 10px;
      :hover{
        cursor: pointer;
        background: #dedede;
      }
    }
  }
  }
`;

export const TextFieldStyled = styled.div`
  margin-top: ${({ $marginTop }) => ($marginTop ? $marginTop : "0")};
  div[contenteditable] {
    border: 1px solid transparent;
    &:hover {
      border: 1px solid #0762f7 !important;
      box-shadow: 0 0 0 3px #0062ff47 !important;
      transition: all 0.3s ease;
    }
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 6 6"><circle cx="3" cy="3" r="0.4" fill="black" /></svg>')
      20px;
    background-position-y: 7px;
    background-size: 5px 25px;
    line-height: 25px;
    &:focus-visible {
      outline: none;
      border: none;
    }
  }
`;

export const MainModalChiDinhDichVu = styled.div`
  .table-row-group {
    td {
      background-color: #f0f0f0 !important;
    }
  }
  .ant-table-row-expand-icon {
    display: none;
  }
`;
