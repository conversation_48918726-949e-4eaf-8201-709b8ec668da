import React, {
  forwardRef,
  useState,
  useImperativeHandle,
  useRef,
  useEffect,
  useMemo,
  useCallback,
} from "react";
import { Row, Input, message } from "antd";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { PrinterOutlined } from "@ant-design/icons";
import moment from "moment";
import { cloneDeep, uniq, set } from "lodash";

import {
  useEnum,
  useStore,
  useIsMounted,
  useConfirm,
  useLoading,
  useListAll,
  useThietLap,
  useFillMaHoSo,
} from "hooks";

import {
  Card,
  Button,
  ModalTemplate,
  TableWrapper,
  HeaderSearch,
  Popover,
  SelectLoadMore,
  Tooltip,
  ModalSignPrint,
  ThongTinBenhNhan,
  InputTimeout,
} from "components";
import { MainBienBanHoiChan, GlobalStyle } from "./styled";
import {
  ENUM,
  LOAI_DICH_VU,
  LOAI_IN,
  TRANG_THAI_HOI_CHAN,
  THIET_LAP_CHUNG,
} from "constants/index";
import { SVG } from "assets";
import { LOAI_DICH_VU_CHI_DINH } from "pages/khamBenh/configs";
import ChiDinhDichVuHoiChan from "pages/khamBenh/components/StepWrapper/ModalHoiChan/ChiDinhDichVuHoiChan";
import ChiDinhDichVuThuoc from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/BienBanHoiChan/ChiTietBienBanHoiChan/ChiDinhDichVuThuoc";
import ChiDinhDichVuVatTu from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/BienBanHoiChan/ChiTietBienBanHoiChan/ChiDinhDichVuVatTu";
import ModalChonToDieuTri from "pages/khamBenh/components/StepWrapper/ModalHoiChan/ModalChonToDieuTri";
import nbDotDieuTriProvider from "data-access/nb-dot-dieu-tri-provider";
import { getDsMoTa, getMoTaChanDoan, isArray, openInNewTab } from "utils/index";
import { checkIsPhieuKySo } from "utils/phieu-utils";
import { printJS } from "data-access/print-provider";
import FormHoiChan from "./FormHoiChan";
import { LOAI_HOI_CHAN } from "./config";
import ModalChiDinhDichVu from "./components/ModalChiDinhDichVu";
import { useHoiChanUngBuuMoiNhatWithCallback } from "pages/hoiChan/hooks/useHoiChanUngBuuMoiNhat";
import ModalBoSungThongTinDichVu from "pages/khamBenh/ChiDinhDichVu/ModalBoSungThongTinDichVu";
import ModalThongTinThuoc from "pages/khamBenh/DonThuoc/ModalThongTinThuoc";

const sleep = (ms) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

const ModalChiTietHoiChan = (props, ref) => {
  const { showConfirm } = useConfirm();
  const refModal = useRef(null);
  const { isReadonly, showTimKiemNguoiBenh = true, macDinhLoaiHoiChan } = props;
  const refChiDinhDichVu = useRef(null);
  const refChiDinhThuoc = useRef(null);
  const refChiDinhVatTu = useRef(null);
  const refModalChiDinhDichVu = useRef(null);
  const refTimeoutId = useRef(null);
  const refModalSignPrint = useRef(null);
  const refOkCallback = useRef(null);
  const refCancelCallback = useRef(null);
  const refModalChonToDieuTri = useRef(null);
  const refModalBoSungThongTinDichVu = useRef(null);
  const refModalThongTinThuoc = useRef(null);
  const refInput = useRef(null);
  const isMounted = useIsMounted();
  const { showLoading, hideLoading } = useLoading();
  const width = useStore("application.width", 0);
  const listDsBienBanHoiChanTuVan = useStore(
    "nbBienBanHoiChan.listDsBienBanHoiChanTuVan",
    []
  );
  const thongTinBenhNhanTongHop = useStore(
    "nbDotDieuTri.thongTinBenhNhanTongHop",
    {}
  );
  const listToDieuTri = useStore("toDieuTri.listToDieuTri", []);

  const [BAT_BUOC_CAC_TRUONG_HOI_CHAN_THUOC] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_CAC_TRUONG_HOI_CHAN_THUOC,
    "false"
  );
  const [BAT_BUOC_CAC_TRUONG_HOI_CHAN_THUONG] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_CAC_TRUONG_HOI_CHAN_THUONG,
    "false"
  );
  const [BAT_BUOC_CAC_TRUONG_HOI_CHAN_THONG_QUA_MO] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_CAC_TRUONG_HOI_CHAN_THONG_QUA_MO,
    "false"
  );
  const [dataBAT_BUOC_THANH_PHAN_THAM_GIA_HOI_CHAN] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_THANH_PHAN_THAM_GIA_HOI_CHAN
  );
  const [dataTU_DONG_DIEN_CD_MO_TA_THEO_CAC_CD] = useThietLap(
    THIET_LAP_CHUNG.TU_DONG_DIEN_CD_MO_TA_THEO_CAC_CD,
    "FALSE"
  );

  const { formatMaHoSo, testMaHoSo } = useFillMaHoSo();

  const {
    nbBienBanHoiChan: {
      createOrEdit,
      onDeleteTuVan,
      getById,
      getBienBanHoiChanTuVan,
      updateBienBanHoiChanTuVan,
      tuVanDichVu,
      getBienBanHoiChanLaoKhangThuoc,
      hoanThanh,
      huyHoanThanh,
      chiDinhDichVu,
    },
    phieuIn: { getListPhieu, getFilePhieuIn, showFileEditor },
    nbDotDieuTri: { getByTongHopId, updateData },
    danhSachNguoiBenhNoiTru: { getNbNoiTruById },
  } = useDispatch();

  const [listLoaidichVu] = useEnum(ENUM.LOAI_DICH_VU);
  const [listGioiTinh] = useEnum(ENUM.GIOI_TINH);

  const { t } = useTranslation();

  const [state, _setState] = useState({
    show: false,
    nbBienBanHoiChan: { dsDichVuId: [] },
    nbThongTin: {},
    listPhieu: [],
    phuongPhapPTTTAdded: [],
    orgDsDichVuId: [],
    isForceUpdate: false,
    currentIndex1: -1,
    currentIndex2: -1,
    currentIndex3: -1,
    changeGhiChu: [],
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const [listAllNhanVien] = useListAll("nhanVien", {}, state.show);
  const [listAllKhoa] = useListAll("khoa", {}, true);
  const [listAllThamGiaHoiChan] = useListAll("thamGiaHoiChan", {}, true);

  const dsNhanVien = useMemo(
    () =>
      listAllNhanVien.map((item) => ({
        ...item,
        ten: `${item.ma} - ${
          item.tenHocHamHocVi ? `${item.tenHocHamHocVi} ` : ""
        }${item.ten}`,
      })),
    [listAllNhanVien]
  );
  useImperativeHandle(ref, () => ({
    show: (data = {}, okCallback, cancelCallback) => {
      const _gioiTinh =
        (listGioiTinh || []).find((item) => item.id === data?.gioiTinh) || {};
      setState({
        show: true,
        nbThongTin: {
          ...data,
          gioiTinh: _gioiTinh,
        },
      });
      refOkCallback.current = okCallback;
      refCancelCallback.current = cancelCallback;
    },
    hide: () => {
      onBack();
    },
  }));

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
      updateData({ thongTinBenhNhanTongHop: {} });
    }
  }, [state.show]);

  useEffect(() => {
    if (state.nbThongTin.id && !state.nbThongTin.isAdd) {
      getById(state.nbThongTin.id).then((res) => {
        if (isMounted()) {
          const { ...rest } = res.data || {};
          const data = {
            nbBienBanHoiChan: {
              ...rest,
              thoiGianThucHien: res.data.thoiGianThucHien
                ? moment(res.data.thoiGianThucHien)
                : null,
              thoiGianDieuTri: res.data.thoiGianDieuTri
                ? moment(res.data.thoiGianDieuTri)
                : null,
              thoiGianPt: res.data.thoiGianPt
                ? moment(res.data.thoiGianPt)
                : null,
              thoiGianChiaMo: res.data.thoiGianChiaMo
                ? moment(res.data.thoiGianChiaMo)
                : null,
              chiaMo: res.data.chiaMo ?? null,
              phuPtv1Id: res.data.phuPtv1Id ?? null,
              phuPtv2Id: res.data.phuPtv2Id ?? null,
              ngayDuSinh: res.data.ngayDuSinh
                ? moment(res.data.ngayDuSinh)
                : null,
              ngaySieuAm1: res.data.ngaySieuAm1
                ? moment(res.data.ngaySieuAm1)
                : null,
              ngaySieuAm2: res.data.ngaySieuAm2
                ? moment(res.data.ngaySieuAm2)
                : null,
              ngaySieuAmHoiChan: res.data.ngaySieuAmHoiChan
                ? moment(res.data.ngaySieuAmHoiChan)
                : null,
              ngayHen: res.data.ngayHen ? moment(res.data.ngayHen) : null,
              giaiPhauBenh: res.data.giaiPhauBenh ?? "",
              hinhAnhHoc: res.data.hinhAnhHoc ?? "",
              orgTuoiThai: res.data.tuoiThai,
              xetNghiemKhac: res.data.xetNghiemKhac ?? "",
              chiSoUngThu: res.data.chiSoUngThu ?? "",
              moTaChanDoan: {
                dsCdChinh: getDsMoTa(rest, "dsCdChinh"),
                dsCdKemTheo: getDsMoTa(rest, "dsCdKemTheo"),
              },
            },
            phuongPhapPTTTAdded: [],
          };
          if (isArray(listDsBienBanHoiChanTuVan, true)) {
            const loaitPttt = listDsBienBanHoiChanTuVan.filter(
              (i) => i.loaiDichVu === LOAI_DICH_VU.PHAU_THUAT_THU_THUAT
            );
            if (isArray(loaitPttt, true) && !data.nbBienBanHoiChan.dsDichVuId) {
              data.nbBienBanHoiChan.dsDichVuId = uniq(
                loaitPttt.map((i) => i.dichVuId)
              );
              data.orgDsDichVuId = uniq(loaitPttt.map((i) => i.dichVuId));
            }
          } else {
            data.orgDsDichVuId = [];
          }
          setState(data);
        }
      });
    } else {
      setState({
        nbBienBanHoiChan: {
          thoiGianThucHien: moment(),
          loaiHoiChan: macDinhLoaiHoiChan || 10,
        },
      });
    }
  }, [
    state.nbThongTin,
    isMounted,
    listDsBienBanHoiChanTuVan,
    state.isForceUpdate,
    macDinhLoaiHoiChan,
  ]);

  const updateBienBanHoiChan = (data) => {
    return updateBienBanHoiChanTuVan(data, {
      ignoreMessage: true,
    });
  };

  useHoiChanUngBuuMoiNhatWithCallback({
    maNb: state.nbThongTin.maNb,
    enabled: state.show,
    hasExistingRecord: !!state.nbBienBanHoiChan.id,
    isModalVisible: state.show,
    isUngBuouType:
      state.nbBienBanHoiChan?.loaiHoiChan === LOAI_HOI_CHAN.UNG_BUOU,
    onDataReceived: (dataReceived) => {
      setState({
        nbBienBanHoiChan: {
          ...state.nbBienBanHoiChan,
          ...dataReceived,
        },
      });
    },
  });

  useEffect(() => {
    if (state.nbBienBanHoiChan.id) {
      handleGetBienBanHoiChanTuVan();
    }
  }, [state.nbBienBanHoiChan.id]);

  useEffect(() => {
    if (state.nbThongTin.nbDotDieuTriId) {
      getByTongHopId(state.nbThongTin.nbDotDieuTriId);
    }
  }, [state.nbThongTin.nbDotDieuTriId]);

  const handleGetBienBanHoiChanTuVan = (payload) => {
    getBienBanHoiChanTuVan({
      nbDotDieuTriId: state.nbThongTin.nbDotDieuTriId,
      bienBanHoiChanId: state.nbBienBanHoiChan.id,
      page: 0,
      size: 500,
      ...payload,
    });
  };

  const listDsBienBanHoiChanTuVanPttt = useMemo(() => {
    return listDsBienBanHoiChanTuVan.filter(
      (item) => LOAI_DICH_VU.PHAU_THUAT_THU_THUAT === item.loaiDichVu
    );
  }, [listDsBienBanHoiChanTuVan]);

  const onGetDsPhieu = () => {
    getListPhieu({
      nbDotDieuTriId: state.nbThongTin.nbDotDieuTriId,
      maManHinh: "023",
      maViTri: "02301",
      chiDinhTuDichVuId: state.nbBienBanHoiChan.id,
    }).then((res) => {
      setState({
        listPhieu: res || [],
      });
    });
  };

  useEffect(() => {
    if (state.nbThongTin.nbDotDieuTriId && state.nbBienBanHoiChan.id) {
      onGetDsPhieu();
    }
  }, [state.nbThongTin.nbDotDieuTriId, state.nbBienBanHoiChan.id]);

  useEffect(() => {
    if (
      state.nbThongTin.nbDotDieuTriId &&
      state.nbBienBanHoiChan.loaiHoiChan === LOAI_HOI_CHAN.KHAM_CHUYEN_KHOA
    ) {
      getNbNoiTruById(state.nbThongTin.nbDotDieuTriId);
    }
  }, [state.nbThongTin.nbDotDieuTriId, state.nbBienBanHoiChan.loaiHoiChan]);

  const renderLaoKhangThuoc = (data) => {
    return data
      ?.map((item) =>
        item?.tenChiSoCon && item?.ketQuaChiSoCon
          ? `${item?.tenChiSoCon}: ${item?.ketQuaChiSoCon}`
          : `${item?.tenDichVu}: ${item?.ketQua}`
      )
      ?.join("; ");
  };

  const onChange = (key) => (e, record) => {
    let value = "";
    let data = {};

    if (e?.target) {
      if (e.target.hasOwnProperty("checked")) value = e.target.checked;
      else value = e.target.value;
    } else if (e?._d) value = e;
    else value = e;
    if (key === "dsDichVuId") {
      setState({
        nbBienBanHoiChan: { ...state.nbBienBanHoiChan, [key]: value },
        phuongPhapPTTTAdded: isArray(record, true)
          ? record.map((item) => ({
              loaiDichVu: item.loaiDichVu,
              dichVu: {
                id: item.id,
                ten: item.ten,
                ma: item.ma,
                loaiDichVu: item.loaiDichVu,
              },
              dichVuId: item.id,
              nguoiTuVan: null,
              boSung: null,
              nguoiTuVanId: null,
              soLuong: 1,
              donViYTeId: item.donViYTeId,
              coSoYTeId: item.coSoYTeId,
              bienBanHoiChanId: state.nbBienBanHoiChan.id,
              nbDotDieuTriId: state.nbThongTin.nbDotDieuTriId,
            }))
          : [null],
      });
    } else {
      if (key === "dsCdChinhId" || key === "dsCdKemTheoId") {
        if (key == "dsCdChinhId") {
          state.nbBienBanHoiChan.dsCdChinh =
            record.length >= 1 ? [record?.[record.length - 1]] : [];
        } else {
          state.nbBienBanHoiChan.dsCdKemTheo = record;
        }

        const moTaChanDoan = {
          dsCdChinh: getDsMoTa(state.nbBienBanHoiChan, "dsCdChinh"),
          dsCdKemTheo: getDsMoTa(state.nbBienBanHoiChan, "dsCdKemTheo"),
        };
        const moTa = getMoTaChanDoan([
          moTaChanDoan.dsCdChinh,
          moTaChanDoan.dsCdKemTheo,
        ]);
        setState({
          nbBienBanHoiChan: {
            ...state.nbBienBanHoiChan,
            [key]: value,
            moTaChanDoan,
            moTa: dataTU_DONG_DIEN_CD_MO_TA_THEO_CAC_CD?.eval()
              ? moTa
              : state.nbBienBanHoiChan?.moTa,
          },
        });
      } else {
        setState({
          nbBienBanHoiChan: { ...state.nbBienBanHoiChan, [key]: value },
        });
      }
    }
    if (
      key === "loaiHoiChan" &&
      value === 20 &&
      state.nbThongTin.isAdd &&
      state.nbThongTin.nbDotDieuTriId &&
      !state.nbBienBanHoiChan.id &&
      !state.nbBienBanHoiChan.ketQuaXpert &&
      !state.nbBienBanHoiChan.ketQuaNhuomSoiTrucTiep &&
      !state.nbBienBanHoiChan.ketQuaLpaHang1 &&
      !state.nbBienBanHoiChan.ketQuaLpaHang1 &&
      !state.nbBienBanHoiChan.ketQuaNuoiCuoi &&
      !state.nbBienBanHoiChan.ketQuaKhangSinhDo &&
      !state.nbBienBanHoiChan.ketQuaHuyetHoc &&
      !state.nbBienBanHoiChan.ketQuaSinhHoa &&
      !state.nbBienBanHoiChan.ketQuaDichCacMang &&
      !state.nbBienBanHoiChan.ketQuaXqClvtNguc &&
      !state.nbBienBanHoiChan.ketQuaEcg
    ) {
      getBienBanHoiChanLaoKhangThuoc(state.nbThongTin.nbDotDieuTriId).then(
        (s) => {
          const {
            xpert,
            nhuomSoiTrucTiep,
            lpa1,
            lpa2,
            nuoiCay,
            khangSinhDo,
            huyetHoc,
            sinhHoa,
            dichCacMang,
            xqClvt,
            ecg,
          } = s;
          data = {
            ketQuaXpert: renderLaoKhangThuoc(xpert),
            ketQuaNhuomSoiTrucTiep: renderLaoKhangThuoc(nhuomSoiTrucTiep),
            ketQuaLpaHang1: renderLaoKhangThuoc(lpa1),
            ketQuaLpaHang2: renderLaoKhangThuoc(lpa2),
            ketQuaNuoiCuoi: renderLaoKhangThuoc(nuoiCay),
            ketQuaKhangSinhDo: renderLaoKhangThuoc(khangSinhDo),
            ketQuaHuyetHoc: renderLaoKhangThuoc(huyetHoc),
            ketQuaSinhHoa: renderLaoKhangThuoc(sinhHoa),
            ketQuaDichCacMang: renderLaoKhangThuoc(dichCacMang),
            ketQuaXqClvtNguc: renderLaoKhangThuoc(xqClvt),
            ketQuaEcg: renderLaoKhangThuoc(ecg),
          };
          setState({
            nbBienBanHoiChan: {
              ...state.nbBienBanHoiChan,
              ...data,
              [key]: value,
            },
          });
        }
      );
    }
    if (key === "thamGiaHoiChan") {
      const selected = listAllThamGiaHoiChan.find((x) => x.id === value);

      let newState = {
        [key]: value,
        chuTriId: null,
        thuKyId: null,
        dsThanhPhanId: null,
      };

      if (!selected?.dsKhoaChiDinhId) {
        newState = {
          ...newState,
          chuTriId: selected?.chuTriId || null,
          thuKyId: selected?.thuKyId || null,
          dsThanhPhanId: selected?.dsThanhPhanId || null,
        };
      } else if (
        Array.isArray(selected.dsKhoaChiDinhId) &&
        selected.dsKhoaChiDinhId.length > 0
      ) {
        const hasMatch = selected.dsKhoaChiDinhId.includes(
          !state.nbThongTin.isAdd
            ? state.nbThongTin?.khoaHoiChanId
            : state.nbThongTin?.khoaNbId
        );

        if (hasMatch) {
          newState = {
            ...newState,
            chuTriId: selected?.chuTriId || null,
            thuKyId: selected?.thuKyId || null,
            dsThanhPhanId: selected?.dsThanhPhanId || null,
          };
        }
      }

      setState({
        nbBienBanHoiChan: {
          ...state.nbBienBanHoiChan,
          ...newState,
        },
      });
    }
  };

  const onChangeTextFiled = useCallback(
    (key) => (value) => {
      state.nbBienBanHoiChan[key] = value;
    },
    [state.nbBienBanHoiChan]
  );

  const onHuyHoanThanh = () => {
    huyHoanThanh(state.nbBienBanHoiChan.id).then(() => {
      setState({
        isForceUpdate: !state.isForceUpdate,
      });
    });
  };

  const onHoanThanh = () => {
    hoanThanh(state.nbBienBanHoiChan.id).then(() => {
      setState({
        isForceUpdate: !state.isForceUpdate,
      });
    });
  };

  const onDeleteItem = (item) => {
    showConfirm(
      {
        title: t("common.xoaDuLieu"),
        content: `${t("common.banChacChanMuonXoa")} ${item?.dichVu?.ten}?`,
        cancelText: t("common.quayLai"),
        okText: t("common.dongY"),
        classNameOkText: "button-error",
        showBtnOk: true,
        typeModal: "error",
      },
      () => {
        const idxPTTT = state.phuongPhapPTTTAdded.findIndex(
          (itm) => itm.dichVuId === item.dichVuId
        );
        if (idxPTTT !== -1) {
          setState({
            phuongPhapPTTTAdded: cloneDeep(state.phuongPhapPTTTAdded).splice(
              idxPTTT,
              1
            ),
            nbBienBanHoiChan: {
              ...state.nbBienBanHoiChan,
              dsDichVuId: cloneDeep(state.nbBienBanHoiChan.dsDichVuId).splice(
                idxPTTT,
                1
              ),
            },
          });
        } else {
          onDeleteTuVan({ id: item?.id }).then(() => {
            handleGetBienBanHoiChanTuVan();
          });
        }
      }
    );
  };

  const onBoSungThongTin = (res, item) => {
    const newTable = (res || [])
      .map((x) => ({
        ...x,
        nbDichVu: { ...x.nbDichVu, loaiDichVu: item.loaiDichVu },
        dsMucDich: item?.boSung?.dsMucDich || [],
        dsPhongThucHien: item?.boSung?.dsPhongThucHien || [],
      }))
      .filter((x) => [7624, 8501].includes(x.code));

    //Show modal bổ sung thông tin
    if (newTable.length > 0) {
      if (item?.loaiDichVu === LOAI_DICH_VU.THUOC) {
        refModalThongTinThuoc.current &&
          refModalThongTinThuoc.current.show({
            newTable,
            nbDotDieuTriId: item.nbDotDieuTriId,
            alwaysShowMess: true,
          });
      } else if (
        [
          LOAI_DICH_VU.KHAM,
          LOAI_DICH_VU.XET_NGHIEM,
          LOAI_DICH_VU.CDHA,
          LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
        ].includes(item.loaiDichVu)
      ) {
        refModalBoSungThongTinDichVu.current &&
          refModalBoSungThongTinDichVu.current.show({
            dataSource: newTable,
          });
      }
    }
  };

  const onChiDinhDichVuToDieuTri = async (item, options) => {
    if (!item) return;
    let data = listToDieuTri.filter(
      (x) =>
        moment(x.thoiGianYLenh).format("YYYY-MM-DD") ===
        moment(new Date()).format("YYYY-MM-DD")
    );
    if (!data?.length) {
      message.error(t("quanLyNoiTru.khongCoToDieuTriThoaManDieuKien"));
      return;
    }

    if (data.length > 1) {
      refModalChonToDieuTri.current &&
        refModalChonToDieuTri.current.show(data, async (toDieuTri) => {
          let payload = {
            ...(item.boSung || {}),
            nbDichVu: {
              chiDinhTuDichVuId: toDieuTri.id,
              chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
              dichVuId: item?.dichVuId,
              dsDichVuId: item?.dsDichVuId,
              soLuong: item.soLuong || 1,
              loaiDichVu: item?.loaiDichVu,
              khoaChiDinhId: toDieuTri.khoaChiDinhId,
              loaiHinhThanhToanId:
                item?.boSung?.nbDvKyThuat?.loaiHinhThanhToanId,
              ghiChu: item?.boSung?.nbDichVu?.ghiChu,
            },
            nbDotDieuTriId: toDieuTri.nbDotDieuTriId,
            nbDvKyThuat: {
              tuVanVienId: item.nguoiTuVanId,
            },
          };
          if (options?.updateGhiChu) {
            await updateBienBanHoiChan(item);
          }
          const res = await chiDinhDichVu(payload);
          onBoSungThongTin(res, item);
        });
    } else {
      let payload = {
        ...(item.boSung || {}),
        nbDichVu: {
          chiDinhTuDichVuId: data[0].id,
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
          dichVuId: item?.dichVuId,
          dsDichVuId: item?.dsDichVuId,
          soLuong: item.soLuong || 1,
          loaiDichVu: item?.loaiDichVu,
          khoaChiDinhId: data[0].khoaChiDinhId,
          loaiHinhThanhToanId: item?.boSung?.nbDvKyThuat?.loaiHinhThanhToanId,
          ghiChu: item?.boSung?.nbDichVu?.ghiChu,
        },
        nbDotDieuTriId: data[0].nbDotDieuTriId,
        nbDvKyThuat: {
          tuVanVienId: item.nguoiTuVanId,
        },
      };

      if (options?.updateGhiChu) {
        await updateBienBanHoiChan(item);
      }

      const res = await chiDinhDichVu(payload);
      onBoSungThongTin(res, item);
    }
  };

  const onRow =
    (indexTable) =>
    (record = {}, index) => {
      return {
        onClick: (row) => {
          if (state[`currentIndex${indexTable}`] !== record.id) {
            setState({
              [`currentIndex${indexTable}`]: record.id,
            });

            setTimeout(() => {
              refInput.current?.focus();
            }, 100);
          }
        },
      };
    };

  const onChangeGhiChu = (record, value) => {
    const newRecord = cloneDeep(record);
    set(newRecord, "boSung.nbDichVu.ghiChu", value);
    const index = listDsBienBanHoiChanTuVan.findIndex(
      (i) => i.id === record.id
    );
    if (index !== -1) {
      set(
        listDsBienBanHoiChanTuVan,
        `[${index}].boSung.nbDichVu.ghiChu`,
        value
      );
    }
    setState({
      changeGhiChu: [...state.changeGhiChu, newRecord],
    });
  };

  const columns1 = [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: "50px",
      dataIndex: "index",
      key: "index",
      align: "center",
      render: (item, data, index) => index + 1,
    },
    {
      title: <HeaderSearch title={t("common.loaiDichVu")} />,
      width: "100px",
      dataIndex: "loaiDichVu",
      key: "loaiDichVu",
      align: "center",
      render: (item) => (
        <div className="item">
          {(listLoaidichVu || []).find((x) => x.id === item)?.ten}
        </div>
      ),
    },
    {
      title: <HeaderSearch title={t("common.tenDichVu")} />,
      width: "400px",
      dataIndex: "dichVu",
      key: "dichVu",
      align: "center",
      render: (item) => <div className="item">{item?.ten}</div>,
    },
    {
      title: <HeaderSearch title={t("common.soLuong")} />,
      width: "60px",
      dataIndex: "soLuong",
      key: "soLuong",
      align: "center",
      align: "center",
      render: (item) => <div className="item">{item}</div>,
    },
    {
      title: <HeaderSearch title={t("hoiChan.nguoiTuVan")} />,
      width: "150px",
      dataIndex: "nguoiTuVan",
      key: "nguoiTuVan",
      align: "center",
      render: (item) => <div className="item">{item?.ten}</div>,
    },
    {
      title: <HeaderSearch title={t("common.ghiChu")} />,
      width: "150px",
      dataIndex: "boSung",
      key: "boSung",
      render: (item, record) => {
        if (state[`currentIndex1`] === record.id) {
          return (
            <InputTimeout
              value={item?.nbDichVu?.ghiChu || ""}
              onChange={(value) => {
                onChangeGhiChu(record, value);
              }}
              ref={refInput}
            />
          );
        }
        return <div className="item">{item?.nbDichVu?.ghiChu}</div>;
      },
    },
    {
      title: <HeaderSearch title={t("common.tienIch")} />,
      width: "80px",
      dataIndex: "action",
      key: "action",
      align: "center",
      render: (item, data) => {
        return (
          <div className="action">
            <Tooltip title={t("quanLyNoiTru.chiDinhSangToDieuTri")}>
              <SVG.IcLogout
                size={20}
                onClick={(e) => {
                  e.stopPropagation();
                  onChiDinhDichVuToDieuTri(data);
                }}
                className="ic-action"
              />
            </Tooltip>
            <SVG.IcDelete
              className="ic-action"
              onClick={(e) => {
                e.stopPropagation();
                onDeleteItem(data);
              }}
            />
          </div>
        );
      },
    },
  ];

  const columns2 = [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: "50px",
      dataIndex: "index",
      key: "index",
      align: "center",
      render: (item, data, index) => index + 1,
    },
    {
      title: <HeaderSearch title={t("danhMuc.phanLoaiThuoc")} />,
      width: "100px",
      dataIndex: "loaiDichVu",
      key: "loaiDichVu",
      render: (item) => (
        <div className="item">
          {(listLoaidichVu || []).find((x) => x.id === item)?.ten}
        </div>
      ),
    },
    {
      title: <HeaderSearch title={t("common.tenDichVu")} />,
      width: "400px",
      dataIndex: "dichVu",
      key: "dichVu",
      render: (item) => <div className="item">{item?.ten}</div>,
    },
    {
      title: <HeaderSearch title={t("common.soLuong")} />,
      width: "60px",
      dataIndex: "soLuong",
      key: "soLuong",
      align: "center",
      render: (item) => <div className="item">{item}</div>,
    },
    {
      title: <HeaderSearch title={t("hoiChan.nguoiTuVan")} />,
      width: "150px",
      dataIndex: "nguoiTuVan",
      key: "nguoiTuVan",
      render: (item) => <div className="item">{item?.ten}</div>,
    },
    {
      title: <HeaderSearch title={t("common.ghiChu")} />,
      width: "150px",
      dataIndex: "boSung",
      key: "boSung",
      render: (item, record) => {
        if (state[`currentIndex2`] === record.id) {
          return (
            <InputTimeout
              value={item?.nbDichVu?.ghiChu || ""}
              onChange={(value) => {
                onChangeGhiChu(record, value);
              }}
              ref={refInput}
            />
          );
        }
        return <div className="item">{item?.nbDichVu?.ghiChu}</div>;
      },
    },
    {
      title: <HeaderSearch title={t("common.tienIch")} />,
      width: "80px",
      dataIndex: "action",
      key: "action",
      align: "center",
      render: (item, data) => {
        return (
          <div className="action">
            <Tooltip title={t("quanLyNoiTru.chiDinhSangToDieuTri")}>
              <SVG.IcLogout
                size={20}
                onClick={(e) => {
                  e.stopPropagation();
                  onChiDinhDichVuToDieuTri(data);
                }}
                className="ic-action"
              />
            </Tooltip>
            <SVG.IcDelete
              className="ic-action"
              onClick={(e) => {
                e.stopPropagation();
                onDeleteItem(data);
              }}
            />
          </div>
        );
      },
    },
  ];

  const columns3 = [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: "50px",
      dataIndex: "index",
      key: "index",
      align: "center",
      render: (item, data, index) => index + 1,
    },
    {
      title: <HeaderSearch title={t("danhMuc.maVatTu")} />,
      width: "100px",
      dataIndex: "dichVu",
      key: "dichVu",
      render: (item) => <div className="item">{item.ma}</div>,
    },
    {
      title: <HeaderSearch title={t("common.tenDichVu")} />,
      width: "400px",
      dataIndex: "dichVu",
      key: "dichVu",
      render: (item) => <div className="item">{item?.ten}</div>,
    },
    {
      title: <HeaderSearch title={t("common.soLuong")} />,
      width: "60px",
      dataIndex: "soLuong",
      key: "soLuong",
      align: "center",
      render: (item) => <div className="item">{item}</div>,
    },
    {
      title: <HeaderSearch title={t("hoiChan.nguoiTuVan")} />,
      width: "150px",
      dataIndex: "nguoiTuVan",
      key: "nguoiTuVan",
      render: (item) => <div className="item">{item?.ten}</div>,
    },
    {
      title: <HeaderSearch title={t("common.ghiChu")} />,
      width: "150px",
      dataIndex: "boSung",
      key: "boSung",
      render: (item, record) => {
        if (state[`currentIndex3`] === record.id) {
          return (
            <InputTimeout
              value={item?.nbDichVu?.ghiChu || ""}
              onChange={(value) => {
                onChangeGhiChu(record, value);
              }}
              ref={refInput}
            />
          );
        }
        return <div className="item">{item?.nbDichVu?.ghiChu}</div>;
      },
    },
    {
      title: <HeaderSearch title={t("common.tienIch")} />,
      width: "80px",
      dataIndex: "action",
      key: "action",
      align: "center",
      render: (item, data) => {
        return (
          <div className="action">
            <Tooltip title={t("quanLyNoiTru.chiDinhSangToDieuTri")}>
              <SVG.IcLogout
                size={20}
                onClick={(e) => {
                  e.stopPropagation();
                  onChiDinhDichVuToDieuTri(data);
                }}
                className="ic-action"
              />
            </Tooltip>
            <SVG.IcDelete
              className="ic-action"
              onClick={(e) => {
                e.stopPropagation();
                onDeleteItem(data);
              }}
            />
          </div>
        );
      },
    },
  ];

  const onSaveGhiChu = async () => {
    Promise.all(
      state.changeGhiChu.map((item) => updateBienBanHoiChan(item))
    ).then(() => {
      setState({
        changeGhiChu: [],
        currentIndex1: -1,
        currentIndex2: -1,
        currentIndex3: -1,
      });
    });
  };

  const onCreateOrEdit = async () => {
    if (!state.nbThongTin.nbDotDieuTriId) {
      return message.error(t("hoiChan.vuiLongChonNb"));
    }

    try {
      showLoading();

      onSaveGhiChu();

      const clearHtml = (html) => {
        if (html) {
          const div = document.createElement("div");
          div.innerHTML = html;
          return div.textContent || div.innerText || "";
        }
        return html;
      };

      const { phuongPhapPTTTAdded, nbBienBanHoiChan, orgDsDichVuId } = state;
      const {
        thoiGianThucHien,
        capHoiChan,
        tienLuong,
        loaiHoiChan,
        dsKhoaHoiChanId,
        dsBacSiHoiChanId,
        bacSiDieuTriId,
        chuTriId,
        thuKyId,
        dsThanhPhanId,
        dsThanhPhanNgoaiVien,
        lyDo,
        diaDiem,
        dsCdChinhId,
        dsCdKemTheoId,
        dsCdVaoVienId,
        dsCdPhanBietId,
        huongDieuTri,
        ketLuan,
        tienSuBanThan,
        tienSuGiaDinh,
        toanThan,
        cacBoPhan,
        ketQuaLamSang,
        dienBienBenh,
        kinhGui,
        moTa,
        dsDichVuId,
        nhanXetBenh,
        ptvId,
        giaiDoanBenh,
        chanDoanBanDau,
        dieuTriThuocLao,
        lanDieuTri,
        thoiGianDieuTri,
        phacDoDieuTri,
        ketQuaDieuTri,
        dieuTriThuocLaoHang2,
        tenThuoc,
        benhLyKhac,
        ketQuaXpert,
        ketQuaNhuomSoiTrucTiep,
        ketQuaLpaHang1,
        ketQuaLpaHang2,
        ketQuaNuoiCuoi,
        ketQuaKhangSinhDo,
        ketQuaHuyetHoc,
        ketQuaSinhHoa,
        ketQuaDichCacMang,
        ketQuaXqClvtNguc,
        ketQuaEcg,
        thoiGianPt,
        thoiGianChiaMo,
        chiaMo,
        phuPtv1Id,
        phuPtv2Id,
        truocPt,
        xetNghiem,
        loaiHinhPt,
        chanDoanTruocPt,
        phuongPhapPt,
        phuongPhapVoCam,
        coCauPt,
        duTruVtth,
        duTruMau,
        duKienKhoKhan,
        ykienThongQuaMo,
        quaTrinhBenhLy,
        tienCan,
        huongXuTri,
        phacDoXaTri,
        ngayHen,
        ghiChu,
        dsCdHoiChanId,
        hinhAnhHoc,
        giaiPhauBenh,
        loaiPtTt,
        baoLuuYKien,
        para,
        benhSu,
        batThuong,
        theoDoiTuTuanThai,
        theoDoiDenTuanThai,
        chocHutDichOi,
        sinhThietGaiRau,
        tuoiThai,
        ngayDuSinh,
        timThai,
        ngaySieuAm1,
        ketQuaSieuAm1,
        ngaySieuAm2,
        ketQuaSieuAm2,
        ngaySieuAmHoiChan,
        ketQuaSieuAmHoiChan,
        tienLuongThai,
        tienLuongSanKhoa,
        tienLuongSoSinh,
        xetNghiemKhac,
        chiSoUngThu,
        moTaChanDoan,
      } = nbBienBanHoiChan;

      if (isArray(listDsBienBanHoiChanTuVanPttt, true)) {
        let deleteIds = [];
        if (isArray(phuongPhapPTTTAdded, true)) {
          const diffArr = listDsBienBanHoiChanTuVanPttt.filter(
            (item) =>
              !phuongPhapPTTTAdded.some((i) => i?.dichVuId === item.dichVuId)
          );
          deleteIds = diffArr.map((i) => i.id);
        }
        if (isArray(deleteIds, true)) {
          await onDeleteTuVan({ id: null, deleteIds });
          await sleep(300);
        }
      }

      if (baoLuuYKien && baoLuuYKien.length > 5000) {
        message.error(
          t("hoiChan.truongTitleKhongDuocVuotQuaNumKyTu", {
            title: t("hoiChan.baoLuuYKien"),
            num: 5000,
          })
        );
        return;
      }

      let requiredFields = [];

      if (
        loaiHoiChan === LOAI_HOI_CHAN.THUOC &&
        BAT_BUOC_CAC_TRUONG_HOI_CHAN_THUOC?.eval()
      ) {
        requiredFields = [
          { value: thoiGianThucHien, label: t("hoiChan.ngayHoiChan") },
          { value: lyDo, label: t("hoiChan.lyDoHoiChan") },
          { value: diaDiem, label: t("hoiChan.diaDiem") },
          { value: moTa, label: t("hoiChan.chanDoanMoTaChiTiet") },
          { value: ketLuan, label: t("hoiChan.ketLuan") },
        ];
      }

      if (
        loaiHoiChan === LOAI_HOI_CHAN.THUONG &&
        BAT_BUOC_CAC_TRUONG_HOI_CHAN_THUONG?.eval()
      ) {
        requiredFields = [
          { value: thoiGianThucHien, label: t("hoiChan.ngayHoiChan") },
          {
            value: dsKhoaHoiChanId?.length,
            label: t("hoiChan.moiKhoaHoiChan"),
          },
          { value: chuTriId, label: t("hoiChan.chuTri") },
          { value: thuKyId, label: t("hoiChan.thuKy") },
          { value: lyDo, label: t("hoiChan.lyDoHoiChan") },
          { value: diaDiem, label: t("hoiChan.diaDiem") },
          { value: moTa, label: t("hoiChan.chanDoanMoTaChiTiet") },
          { value: ketLuan, label: t("hoiChan.ketLuan") },
          {
            value: dienBienBenh,
            label: t("hoiChan.tomTatDienBenhQuaTrinhDieuTri"),
          },
          { value: huongDieuTri, label: t("hoiChan.huongDieuTri") },
        ];
        if (chiaMo) {
          requiredFields.push({
            value: thoiGianChiaMo,
            label: t("hoiChan.ngayChiaMo"),
          });
        }
      }

      if (
        loaiHoiChan === LOAI_HOI_CHAN.THONG_QUA_MO &&
        BAT_BUOC_CAC_TRUONG_HOI_CHAN_THONG_QUA_MO?.eval()
      ) {
        requiredFields = [
          { value: thoiGianThucHien, label: t("hoiChan.ngayHoiChan") },
          {
            value: dsKhoaHoiChanId?.length,
            label: t("hoiChan.moiKhoaHoiChan"),
          },
          { value: chuTriId, label: t("hoiChan.chuTri") },
          { value: thuKyId, label: t("hoiChan.thuKy") },
          {
            value: ketQuaXqClvtNguc,
            label: t("hoiChan.hinhAnhXQuangCTScanner"),
          },
          { value: truocPt, label: t("hoiChan.tomTatTinhTrangTruocMo") },
          { value: xetNghiem, label: t("xetNghiem.xetNghiem") },
          { value: loaiPtTt, label: t("pttt.loaiPttt") },
          { value: ptvId, label: t("pttt.phauThuatVienChinh") },
        ];
      }

      for (const field of requiredFields) {
        if (!field.value || field.value === "<br>") {
          message.error(
            t("danhMuc.vuiLongNhapTitle", {
              title: field.label.toLowerCase(),
            })
          );
          return;
        }
      }

      if (
        dataBAT_BUOC_THANH_PHAN_THAM_GIA_HOI_CHAN?.eval() &&
        !isArray(dsThanhPhanId, true) &&
        loaiHoiChan !== LOAI_HOI_CHAN.KHAM_CHUYEN_KHOA
      ) {
        message.error(t("hoiChan.vuiLongNhapThanhPhanThamGiaHoiChan"));
        return;
      }

      const dataCrud = {
        id: state.nbBienBanHoiChan.id,
        nbDotDieuTriId: state.nbThongTin.nbDotDieuTriId,
        thoiGianThucHien: thoiGianThucHien
          ? moment(thoiGianThucHien).format("YYYY-MM-DD HH:mm:ss")
          : null,
        capHoiChan: capHoiChan || null,
        tienLuong: tienLuong || null,
        dsKhoaHoiChanId,
        bacSiDieuTriId,
        dsBacSiHoiChanId,
        chuTriId: chuTriId || null,
        thuKyId: thuKyId || null,
        ptvId: ptvId || null,
        loaiHoiChan,
        dsThanhPhanId,
        dsThanhPhanNgoaiVien,
        lyDo,
        diaDiem,
        dsCdChinhId,
        dsCdKemTheoId,
        dsCdVaoVienId,
        dsCdPhanBietId,
        huongDieuTri,
        ketLuan,
        tienSuBanThan,
        tienSuGiaDinh,
        toanThan,
        cacBoPhan,
        ketQuaLamSang,
        dienBienBenh,
        kinhGui,
        moTa: clearHtml(moTa),
        dsDichVuId,
        nhanXetBenh,
        giaiDoanBenh,
        chanDoanBanDau: clearHtml(chanDoanBanDau),
        dieuTriThuocLao,
        lanDieuTri,
        thoiGianDieuTri: thoiGianDieuTri
          ? moment(thoiGianDieuTri).format("YYYY-MM-DD HH:mm:ss")
          : null,
        phacDoDieuTri,
        ketQuaDieuTri,
        dieuTriThuocLaoHang2,
        tenThuoc,
        benhLyKhac,
        ketQuaXpert,
        ketQuaNhuomSoiTrucTiep,
        ketQuaLpaHang1,
        ketQuaLpaHang2,
        ketQuaNuoiCuoi,
        ketQuaKhangSinhDo,
        ketQuaHuyetHoc,
        ketQuaSinhHoa,
        ketQuaDichCacMang,
        ketQuaXqClvtNguc: clearHtml(ketQuaXqClvtNguc),
        ketQuaEcg,
        ...(!state.nbBienBanHoiChan.id && {
          khoaHoiChanId: state.nbThongTin.khoaNbId,
        }),
        thoiGianPt,
        thoiGianChiaMo,
        chiaMo,
        phuPtv1Id,
        phuPtv2Id,
        truocPt: clearHtml(truocPt),
        xetNghiem: clearHtml(xetNghiem),
        loaiHinhPt: clearHtml(loaiHinhPt),
        chanDoanTruocPt: clearHtml(chanDoanTruocPt),
        phuongPhapPt: clearHtml(phuongPhapPt),
        phuongPhapVoCam: clearHtml(phuongPhapVoCam),
        coCauPt,
        duTruVtth: clearHtml(duTruVtth),
        duTruMau: clearHtml(duTruMau),
        duKienKhoKhan: clearHtml(duKienKhoKhan),
        ykienThongQuaMo: clearHtml(ykienThongQuaMo),
        quaTrinhBenhLy,
        tienCan,
        huongXuTri,
        phacDoXaTri,
        ngayHen: ngayHen ? moment(ngayHen).format("YYYY-MM-DD") : null,
        ghiChu,
        dsCdHoiChanId,
        hinhAnhHoc,
        giaiPhauBenh,
        loaiPtTt,
        baoLuuYKien: clearHtml(baoLuuYKien),
        para,
        benhSu,
        batThuong,
        theoDoiTuTuanThai,
        theoDoiDenTuanThai,
        chocHutDichOi,
        sinhThietGaiRau,
        tuoiThai,
        ngayDuSinh,
        timThai,
        ngaySieuAm1: ngaySieuAm1
          ? moment(ngaySieuAm1).format("YYYY-MM-DD")
          : null,
        ketQuaSieuAm1,
        ngaySieuAm2: ngaySieuAm2
          ? moment(ngaySieuAm2).format("YYYY-MM-DD")
          : null,
        ketQuaSieuAm2,
        ngaySieuAmHoiChan: ngaySieuAmHoiChan
          ? moment(ngaySieuAmHoiChan).format("YYYY-MM-DD")
          : null,
        ketQuaSieuAmHoiChan,
        tienLuongThai,
        tienLuongSanKhoa,
        tienLuongSoSinh,
        xetNghiemKhac,
        chiSoUngThu,
        moTaChanDoan,
      };

      const s = await createOrEdit(dataCrud);

      if (
        isArray(phuongPhapPTTTAdded, true) &&
        !phuongPhapPTTTAdded.some((i) => i === null)
      ) {
        let dataAdded = [];
        if (isArray(orgDsDichVuId, true)) {
          dataAdded = phuongPhapPTTTAdded.filter(
            (i) => !orgDsDichVuId.includes(i.dichVuId)
          );
        } else {
          dataAdded = phuongPhapPTTTAdded;
        }
        if (isArray(dataAdded, true)) {
          await sleep(300);
          dataAdded = dataAdded.map((item) => ({
            nbDotDieuTriId: item.nbDotDieuTriId,
            bienBanHoiChanId: item.bienBanHoiChanId || s?.id,
            loaiDichVu: item.loaiDichVu,
            dichVuId: item.dichVuId,
            nguoiTuVanId: item.nguoiTuVanId,
            soLuong: item.soLuong,
          }));
          await tuVanDichVu(dataAdded);
        }
      }
      handleGetBienBanHoiChanTuVan({
        bienBanHoiChanId: state.nbBienBanHoiChan.id || s?.id,
      });
      refOkCallback.current && refOkCallback.current();
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onBack = () => {
    setState({
      nbBienBanHoiChan: {
        thoiGianThucHien: null,
        capHoiChan: null,
        tienLuong: null,
        loaiHoiChan: null,
        dsKhoaHoiChanId: null,
        bacSiDieuTriId: null,
        dsBacSiHoiChanId: null,
        chuTriId: null,
        thuKyId: null,
        dsThanhPhanId: null,
        dsThanhPhanNgoaiVien: null,
        lyDo: null,
        diaDiem: null,
        dsCdChinhId: null,
        dsCdKemTheoId: null,
        dsCdVaoVienId: null,
        dsCdPhanBietId: null,
        huongDieuTri: null,
        ketLuan: null,
        tienSuBanThan: null,
        tienSuGiaDinh: null,
        toanThan: null,
        cacBoPhan: null,
        ketQuaLamSang: null,
        dienBienBenh: null,
        kinhGui: null,
        moTa: null,
        nhanXetBenh: null,
        ptvId: null,
        giaiDoanBenh: null,
        chanDoanBanDau: null,
        dieuTriThuocLao: null,
        lanDieuTri: null,
        thoiGianDieuTri: null,
        phacDoDieuTri: null,
        ketQuaDieuTri: null,
        dieuTriThuocLaoHang2: null,
        tenThuoc: null,
        benhLyKhac: null,
        ketQuaXpert: null,
        ketQuaNhuomSoiTrucTiep: null,
        ketQuaLpaHang1: null,
        ketQuaLpaHang2: null,
        ketQuaNuoiCuoi: null,
        ketQuaKhangSinhDo: null,
        ketQuaHuyetHoc: null,
        ketQuaSinhHoa: null,
        ketQuaDichCacMang: null,
        ketQuaXqClvtNguc: null,
        ketQuaEcg: null,
        thoiGianPt: null,
        thoiGianChiaMo: null,
        chiaMo: null,
        phuPtv1Id: null,
        phuPtv2Id: null,
        truocPt: null,
        xetNghiem: null,
        loaiHinhPt: null,
        chanDoanTruocPt: null,
        phuongPhapPt: null,
        phuongPhapVoCam: null,
        coCauPt: null,
        duTruVtth: null,
        duTruMau: null,
        duKienKhoKhan: null,
        ykienThongQuaMo: null,
        quaTrinhBenhLy: null,
        tienCan: null,
        huongXuTri: null,
        phacDoXaTri: null,
        ngayHen: null,
        ghiChu: null,
        giaiPhauBenh: null,
        hinhAnhHoc: null,
        dsCdHoiChanId: null,
        thamGiaHoiChan: null,
        loaiPtTt: null,
        baoLuuYKien: null,
        para: null,
        benhSu: null,
        batThuong: null,
        theoDoiTuTuanThai: null,
        theoDoiDenTuanThai: null,
        chocHutDichOi: null,
        sinhThietGaiRau: null,
        tuoiThai: null,
        ngayDuSinh: null,
        timThai: null,
        ngaySieuAm1: null,
        ketQuaSieuAm1: null,
        ngaySieuAm2: null,
        ketQuaSieuAm2: null,
        ngaySieuAmHoiChan: null,
        ketQuaSieuAmHoiChan: null,
        tienLuongThai: null,
        tienLuongSanKhoa: null,
        tienLuongSoSinh: null,
        xetNghiemKhac: null,
        chiSoUngThu: null,
      },
      show: false,
      nbThongTin: {},
    });
    refCancelCallback.current && refCancelCallback.current();
  };

  const handlePrintWordExcel = async (item) => {
    try {
      showLoading();
      const { finalFile, dsPhieu } = await getFilePhieuIn({
        listPhieus: [item],
        nbDotDieuTriId: state.nbThongTin.nbDotDieuTriId,
        chiDinhTuDichVuId: state.nbBienBanHoiChan.id,
        showError: true,
        bienBanHoiChanId: state.nbBienBanHoiChan.id,
      });
      if ((dsPhieu || []).every((x) => x?.loaiIn == LOAI_IN.MO_TAB)) {
        openInNewTab(finalFile);
      } else {
        printJS({
          printable: finalFile,
          type: "pdf",
        });
      }
    } catch (error) {
      console.log("error", error);
    } finally {
      hideLoading();
    }
  };

  const handlePrint = async (item) => {
    if (item.type == "editor") {
      let mhParams = {};
      //kiểm tra phiếu ký số
      if (checkIsPhieuKySo(item)) {
        //nếu là ký số thì lấy các thông tin về lịch sử ký + tên chân ký + báo cáo id
        mhParams = {
          maManHinh: "023",
          maViTri: "02301",
          chiDinhTuDichVuId: state.nbBienBanHoiChan.id,
          kySo: true,
          maPhieuKy: item.ma,
          nbDotDieuTriId: state.nbThongTin.nbDotDieuTriId,
        };
      }

      showFileEditor({
        phieu: item,
        nbDotDieuTriId: state.nbThongTin.nbDotDieuTriId,
        chiDinhTuDichVuId: state.nbBienBanHoiChan.id,
        ma: item.ma,
        mhParams,
      });
    } else {
      if (checkIsPhieuKySo(item)) {
        refModalSignPrint.current &&
          refModalSignPrint.current.showToSign({
            phieuKy: item,
            payload: {
              nbDotDieuTriId: state.nbThongTin.nbDotDieuTriId,
              chiDinhTuDichVuId: state.nbBienBanHoiChan.id,
              maManHinh: "023",
              maViTri: "02301",
            },
          });
      } else {
        handlePrintWordExcel(item);
      }
    }
  };

  const contentPopover = () => {
    return (
      <div>
        {state.listPhieu.map((item, index) => (
          <div
            className="item-file"
            key={index}
            onClick={() => handlePrint(item)}
          >
            {item.ten}
          </div>
        ))}
      </div>
    );
  };

  const onChiDinhDichVu = () => {
    onSaveGhiChu();
    let { chuTriId, dsThanhPhanId } = state.nbBienBanHoiChan;
    let listNguoiTuVan = listAllNhanVien.filter(
      (x) =>
        (dsThanhPhanId || []).map((item) => Number(item)).includes(x.id) ||
        x.id === chuTriId
    );
    refChiDinhDichVu.current &&
      refChiDinhDichVu.current.show(listNguoiTuVan, () => {
        handleGetBienBanHoiChanTuVan();
        setState({ expanDown: true });
      });
  };

  const onChiDinhThuoc = () => {
    onSaveGhiChu();
    let { chuTriId, dsThanhPhanId } = state.nbBienBanHoiChan;
    let listNguoiTuVan = listAllNhanVien.filter(
      (x) =>
        (dsThanhPhanId || []).map((item) => Number(item)).includes(x.id) ||
        x.id === chuTriId
    );
    refChiDinhThuoc.current &&
      refChiDinhThuoc.current.show(listNguoiTuVan, () => {
        handleGetBienBanHoiChanTuVan();
        setState({ expanDown2: true });
      });
  };
  const onChiDinhVatTu = () => {
    onSaveGhiChu();
    let { chuTriId, dsThanhPhanId } = state.nbBienBanHoiChan;
    let listNguoiTuVan = listAllNhanVien.filter(
      (x) =>
        (dsThanhPhanId || []).map((item) => Number(item)).includes(x.id) ||
        x.id === chuTriId
    );
    refChiDinhVatTu.current &&
      refChiDinhVatTu.current.show(listNguoiTuVan, () => {
        handleGetBienBanHoiChanTuVan();
        setState({ expanDown3: true });
      });
  };

  const listLoaiChiDinhDV = useMemo(() => {
    const list = LOAI_DICH_VU_CHI_DINH.filter(
      (x) => x.id && x.id !== LOAI_DICH_VU.BO_CHI_DINH
    ).map((item) => {
      item.ten = t(item.i18n);
      return item;
    });
    return list;
  }, [t]);

  const onSearch = (loadData, value) => {
    if (!value) return;
    if (refTimeoutId.current) clearTimeout(refTimeoutId.current);
    refTimeoutId.current = setTimeout(() => {
      let _value = value.trim();
      switch (true) {
        case testMaHoSo(_value):
          loadData({ maHoSo: formatMaHoSo(_value) }, true);
          break;
        case !/^[0-9]+$/.test(_value) &&
          !/^[0-9A-F]{8}$/.test(_value) &&
          !/^[a-zA-Z]+[0-9]+$/.test(_value):
          loadData({ tenNb: _value }, true);
          break;
        default:
          break;
      }
    }, 500);
  };
  const onPaste = (callback, text) => {
    onSearch(callback, text);
  };

  const onChangeSearch = (value, data) => {
    const { otherData } = data || {};
    if (value) {
      const _gioiTinh =
        (listGioiTinh || []).find((item) => item.id === otherData?.gioiTinh) ||
        {};
      setState({
        nbThongTin: {
          ...otherData,
          nbDotDieuTriId: otherData.id,
          isAdd: true,
          gioiTinh: _gioiTinh,
          nhomMau: otherData?.nhomMau,
        },
      });
    }
  };

  const renderTitle = () => {
    if (
      state.nbBienBanHoiChan.loaiHoiChan === LOAI_HOI_CHAN.KHAM_CHUYEN_KHOA &&
      state.nbBienBanHoiChan.id
    ) {
      return t("hoiChan.chiTietKhamChuyenKhoa");
    }
    if (state.nbThongTin.id) {
      return t(
        `hoiChan.${
          state.nbBienBanHoiChan.id ? "chiTietToHoiChan" : "themMoiToHoiChan"
        }`
      );
    } else {
      return (
        <div className="flex-center gap-8">
          <span>
            {t(
              `hoiChan.${
                state.nbBienBanHoiChan.id
                  ? "chiTietToHoiChan"
                  : "themMoiToHoiChan"
              }`
            )}
          </span>
          {showTimKiemNguoiBenh && (
            <div>
              <SelectLoadMore
                suffixIcon={<SVG.IcSearch />}
                api={nbDotDieuTriProvider.searchNBDotDieuTriTongHop}
                placeholder={t("hoiChan.nhapMaBaMaHsCuaNb")}
                onSearch={onSearch}
                mapData={(item) => ({
                  value: item.id,
                  label: `${item.maHoSo} - ${item.tenNb}`,
                  ten: `${item.maHoSo} - ${item.tenNb}`,
                  otherData: item,
                })}
                onPaste={onPaste}
                firstLoadData={false}
                onChange={onChangeSearch}
                haveLoading
              />
            </div>
          )}
        </div>
      );
    }
  };

  const onChangeMoTa = (type) => (id, value) => {
    if (!state.nbBienBanHoiChan.moTaChanDoan)
      state.nbBienBanHoiChan.moTaChanDoan = {};
    const dsMoTa = state.nbBienBanHoiChan.moTaChanDoan?.[type] || [];
    const item = dsMoTa.find((item) => item?.id == id);
    if (!item) {
      dsMoTa.push({ id, moTa: value });
    } else {
      item.moTa = value;
    }
    state.nbBienBanHoiChan.moTaChanDoan[type] = [...dsMoTa];
    const moTa = getMoTaChanDoan([
      state.nbBienBanHoiChan.moTaChanDoan.dsCdChinh,
      state.nbBienBanHoiChan.moTaChanDoan.dsCdKemTheo,
    ]);
    setState({ nbBienBanHoiChan: { ...state.nbBienBanHoiChan, moTa } });
  };

  return (
    <ModalTemplate
      width={"100vw"}
      onCancel={onBack}
      ref={refModal}
      title={renderTitle()}
      actionLeft={
        <Button.QuayLai
          onClick={() => {
            onBack();
          }}
        />
      }
      actionRight={
        <>
          {state.nbBienBanHoiChan.id && (
            <>
              <Button
                type="success"
                onClick={() =>
                  refModalChiDinhDichVu.current?.show({
                    dataSource: listDsBienBanHoiChanTuVan,
                    nbDotDieuTriId: state.nbThongTin.nbDotDieuTriId,
                    parentName: "hoiChan",
                  })
                }
              >
                {t("hoiChan.chiDinhNhieuDvVaoToDieuTri")}
              </Button>
              <Popover
                overlayClassName="popover-list-giay-in"
                placement="topLeft"
                content={contentPopover()}
                trigger="click"
              >
                <Button
                  onClick={onGetDsPhieu}
                  rightIcon={<PrinterOutlined />}
                  iconHeight={15}
                >
                  {t("common.inPhieu")}
                </Button>
              </Popover>
            </>
          )}
          {state.nbBienBanHoiChan.trangThaiHoiChan ===
            TRANG_THAI_HOI_CHAN.CHUA_DUYET && (
            <Button iconHeight={15} onClick={onHoanThanh}>
              {t("quanLyNoiTru.hoanThanh")}
            </Button>
          )}
          {state.nbBienBanHoiChan.trangThaiHoiChan ===
            TRANG_THAI_HOI_CHAN.HOAN_THANH && (
            <Button iconHeight={15} onClick={onHuyHoanThanh}>
              {t("quanLyNoiTru.huyHoanThanh")}
            </Button>
          )}
          {!isReadonly && (
            <Button
              type="primary"
              minWidth={100}
              onClick={() => onCreateOrEdit()}
            >
              {t("common.luu")} [F4]
            </Button>
          )}
        </>
      }
    >
      <MainBienBanHoiChan>
        <GlobalStyle />
        <fieldset disabled={isReadonly} style={{ width: "100%" }}>
          <ThongTinBenhNhan
            nbDotDieuTriId={state.nbThongTin.nbDotDieuTriId}
            isShowGiaHanThe={false}
            isShowHsba={true}
            isShowSuaChiTiet={false}
            isShowDongBoGia={false}
            isShowSecondaryPatient={true}
          />
          <Card className="content">
            <FormHoiChan
              data={state.nbBienBanHoiChan}
              onChange={onChange}
              onChangeTextFiled={onChangeTextFiled}
              isReadonly={isReadonly}
              width={width}
              t={t}
              dsNhanVien={dsNhanVien}
              listAllKhoa={listAllKhoa}
              listAllThamGiaHoiChan={listAllThamGiaHoiChan}
              thongTinBenhNhanTongHop={thongTinBenhNhanTongHop}
              nbThongTin={state.nbThongTin}
              onBack={onBack}
              BAT_BUOC_CAC_TRUONG_HOI_CHAN_THUOC={
                BAT_BUOC_CAC_TRUONG_HOI_CHAN_THUOC
              }
              BAT_BUOC_CAC_TRUONG_HOI_CHAN_THUONG={
                BAT_BUOC_CAC_TRUONG_HOI_CHAN_THUONG
              }
              BAT_BUOC_CAC_TRUONG_HOI_CHAN_THONG_QUA_MO={
                BAT_BUOC_CAC_TRUONG_HOI_CHAN_THONG_QUA_MO
              }
              dataBAT_BUOC_THANH_PHAN_THAM_GIA_HOI_CHAN={
                dataBAT_BUOC_THANH_PHAN_THAM_GIA_HOI_CHAN
              }
              onChangeMoTa={onChangeMoTa}
            />
            {state.nbBienBanHoiChan.id &&
              !isReadonly &&
              ![20, 30, 50].includes(state.nbBienBanHoiChan.loaiHoiChan) && (
                <Row className="table-content">
                  <div className="header-table">
                    <div
                      className="left"
                      onClick={() => setState({ expanDown: !state?.expanDown })}
                    >
                      {state?.expanDown ? (
                        <SVG.IcExpandDown />
                      ) : (
                        <SVG.IcExpandRight />
                      )}
                      {t("hoiChan.tuVanThucHienDichVu")}
                    </div>
                    <div className="right">
                      <SVG.IcSearch />
                      <Input
                        placeholder={t("common.timKiem")}
                        onClick={onChiDinhDichVu}
                      />
                    </div>
                  </div>
                  {state?.expanDown && (
                    <TableWrapper
                      columns={columns1}
                      dataSource={listDsBienBanHoiChanTuVan.filter((item) =>
                        [
                          LOAI_DICH_VU.KHAM,
                          LOAI_DICH_VU.XET_NGHIEM,
                          LOAI_DICH_VU.CDHA,
                          LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
                        ].includes(item.loaiDichVu)
                      )}
                      locale={{
                        emptyText: (
                          <div style={{ height: 60 }}>
                            <div
                              style={{ color: "#c3c3c3", lineHeight: "60px" }}
                            >
                              {t("common.khongCoDuLieu")}
                            </div>
                          </div>
                        ),
                      }}
                      onRow={onRow(1)}
                    />
                  )}
                </Row>
              )}
            {state.nbBienBanHoiChan.id &&
              !isReadonly &&
              ![20, 30, 50].includes(state.nbBienBanHoiChan.loaiHoiChan) && (
                <Row className="table-content">
                  <div className="header-table">
                    <div
                      className="left"
                      onClick={() =>
                        setState({ expanDown2: !state?.expanDown2 })
                      }
                    >
                      {state?.expanDown2 ? (
                        <SVG.IcExpandDown />
                      ) : (
                        <SVG.IcExpandRight />
                      )}
                      {t("hoiChan.tuVanThucHienThuoc")}
                    </div>
                    <div className="right">
                      <SVG.IcSearch />
                      <Input
                        placeholder={t("common.timKiem")}
                        onClick={onChiDinhThuoc}
                      />
                    </div>
                  </div>
                  {state?.expanDown2 && (
                    <TableWrapper
                      columns={columns2}
                      dataSource={listDsBienBanHoiChanTuVan.filter((item) =>
                        [LOAI_DICH_VU.THUOC].includes(item.loaiDichVu)
                      )}
                      locale={{
                        emptyText: (
                          <div style={{ height: 60 }}>
                            <div
                              style={{ color: "#c3c3c3", lineHeight: "60px" }}
                            >
                              {t("common.khongCoDuLieu")}
                            </div>
                          </div>
                        ),
                      }}
                      onRow={onRow(2)}
                    />
                  )}
                </Row>
              )}
            {state.nbBienBanHoiChan.id &&
              !isReadonly &&
              ![20, 30, 50].includes(state.nbBienBanHoiChan.loaiHoiChan) && (
                <Row className="table-content">
                  <div className="header-table">
                    <div
                      className="left"
                      onClick={() =>
                        setState({ expanDown3: !state?.expanDown3 })
                      }
                    >
                      {state?.expanDown3 ? (
                        <SVG.IcExpandDown />
                      ) : (
                        <SVG.IcExpandRight />
                      )}
                      {t("hoiChan.tuVanThucHienVatTu")}
                    </div>
                    <div className="right">
                      <SVG.IcSearch />
                      <Input
                        placeholder={t("common.timKiem")}
                        onClick={onChiDinhVatTu}
                      />
                    </div>
                  </div>
                  {state?.expanDown3 && (
                    <TableWrapper
                      columns={columns3}
                      dataSource={listDsBienBanHoiChanTuVan.filter((item) =>
                        [LOAI_DICH_VU.VAT_TU].includes(item.loaiDichVu)
                      )}
                      locale={{
                        emptyText: (
                          <div style={{ height: 60 }}>
                            <div
                              style={{ color: "#c3c3c3", lineHeight: "60px" }}
                            >
                              {t("common.khongCoDuLieu")}
                            </div>
                          </div>
                        ),
                      }}
                      onRow={onRow(3)}
                    />
                  )}
                </Row>
              )}
          </Card>
          <ChiDinhDichVuHoiChan
            ref={refChiDinhDichVu}
            dataNb={state.nbBienBanHoiChan}
            listLoaiChiDinh={listLoaiChiDinhDV}
            dsDoiTuongSuDung={30}
            dsLoaiDichVu={[
              LOAI_DICH_VU.KHAM,
              LOAI_DICH_VU.XET_NGHIEM,
              LOAI_DICH_VU.CDHA,
              LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
            ].join(",")}
          />
          <ChiDinhDichVuThuoc
            ref={refChiDinhThuoc}
            dataNb={state.nbBienBanHoiChan}
          />
          <ChiDinhDichVuVatTu
            ref={refChiDinhVatTu}
            dataNb={state.nbBienBanHoiChan}
          />
        </fieldset>
        <ModalChiDinhDichVu
          ref={refModalChiDinhDichVu}
          onChiDinhDichVuToDieuTri={onChiDinhDichVuToDieuTri}
        />
        <ModalChonToDieuTri ref={refModalChonToDieuTri} />
        <ModalSignPrint ref={refModalSignPrint} />
        <ModalBoSungThongTinDichVu ref={refModalBoSungThongTinDichVu} />
        <ModalThongTinThuoc ref={refModalThongTinThuoc} />
      </MainBienBanHoiChan>
    </ModalTemplate>
  );
};

export default forwardRef(ModalChiTietHoiChan);
