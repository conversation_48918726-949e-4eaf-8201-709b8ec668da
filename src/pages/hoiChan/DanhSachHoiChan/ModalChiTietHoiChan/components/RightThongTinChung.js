import React from "react";
import { Checkbox, DateTimePicker } from "components";
import { SVG } from "assets";
import { LOAI_HOI_CHAN } from "../config";
import { Col, Row } from "antd";

function RightThongTinChung({
  data,
  onChange,
  isReadonly,
  onBack,
  t,
  BAT_BUOC_CAC_TRUONG_HOI_CHAN_THUOC,
  BAT_BUOC_CAC_TRUONG_HOI_CHAN_THUONG,
  BAT_BUOC_CAC_TRUONG_HOI_CHAN_THONG_QUA_MO,
}) {
  return (
    <div className="date">
      <Row className="left" gutter={[8, 8]}>
        <Col md={24} xl={12} xxl={8} className="col">
          <span className="title">
            {data.loaiHoiChan === LOAI_HOI_CHAN.KHAM_CHUYEN_KHOA
              ? t("quanLyNoiTru.toDieuTri.ngayYLenh")
              : t("hoiChan.ngayHoi<PERSON>han")}
            <span style={{ color: "red" }}>
              {(BAT_BUOC_CAC_TRUONG_HOI_CHAN_THUOC?.eval() &&
                data.loaiHoiChan === LOAI_HOI_CHAN.THUOC) ||
              (BAT_BUOC_CAC_TRUONG_HOI_CHAN_THUONG?.eval() &&
                data.loaiHoiChan === LOAI_HOI_CHAN.THUONG) ||
              (BAT_BUOC_CAC_TRUONG_HOI_CHAN_THONG_QUA_MO?.eval() &&
                data.loaiHoiChan === LOAI_HOI_CHAN.THONG_QUA_MO)
                ? "*"
                : ""}
            </span>
            :
          </span>
          <DateTimePicker
            className="select-hoi-chan"
            showTime={false}
            showIcon={false}
            format={"DD/MM/YYYY HH:mm:ss"}
            value={data.thoiGianThucHien}
            onChange={onChange("thoiGianThucHien")}
            placeholder={t("common.chonThoiGian")}
            disabled={isReadonly}
          />
        </Col>
        {data.loaiHoiChan === LOAI_HOI_CHAN.THUONG &&
          BAT_BUOC_CAC_TRUONG_HOI_CHAN_THUONG?.eval() && (
            <>
              <Col md={24} xl={12} xxl={8} className="col">
                <span className="title">{t("hoiChan.ngayMo")}:</span>
                <br />
                <DateTimePicker
                  className="select-hoi-chan"
                  showTime={false}
                  showIcon={false}
                  format={"DD/MM/YYYY HH:mm:ss"}
                  value={data.thoiGianPt}
                  onChange={onChange("thoiGianPt")}
                  placeholder={t("common.chonThoiGian")}
                  disabled={isReadonly}
                />
              </Col>
              <Col md={24} xl={18} xxl={12}>
                <Row gutter={[8, 8]}>
                  <Col span={6} className="col col-checkbox">
                    <Checkbox
                      checked={data.chiaMo}
                      onChange={onChange("chiaMo")}
                      disabled={isReadonly}
                    >
                      <span className="title">{t("hoiChan.chiaMo")}</span>
                    </Checkbox>
                  </Col>
                  {data.chiaMo && (
                    <Col span={18} className="col">
                      <span className="title">
                        {t("hoiChan.ngayChiaMo")}
                        <span style={{ color: "red" }}>*</span>:
                      </span>
                      <DateTimePicker
                        className="select-hoi-chan"
                        showTime={false}
                        showIcon={false}
                        format={"DD/MM/YYYY HH:mm:ss"}
                        value={data.thoiGianChiaMo}
                        onChange={onChange("thoiGianChiaMo")}
                        placeholder={t("common.chonThoiGian")}
                        disabled={isReadonly}
                      />
                    </Col>
                  )}
                </Row>
              </Col>
            </>
          )}
        {data.loaiHoiChan === LOAI_HOI_CHAN.THONG_QUA_MO && (
          <Col md={12} xl={12} xxl={8}>
            <span className="title">{t("hoiChan.ngayMo")}:</span>
            <DateTimePicker
              className="select-hoi-chan"
              showTime={false}
              showIcon={false}
              format={"DD/MM/YYYY HH:mm:ss"}
              value={data.thoiGianPt}
              onChange={onChange("thoiGianPt")}
              placeholder={t("quanLyNoiTru.chonThoiGian")}
              disabled={isReadonly}
            />
          </Col>
        )}
      </Row>
      <div className="right">
        <SVG.IcCancel
          className="pointer"
          onClick={() => {
            onBack();
          }}
        />
      </div>
    </div>
  );
}

export default RightThongTinChung;
