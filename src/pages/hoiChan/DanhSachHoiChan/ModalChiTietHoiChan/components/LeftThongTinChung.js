import React from "react";
import { Col, Row, Select as SelectAntd } from "antd";

import { Select, TextField } from "components";
import { SelectGroup } from "../styled";
import { LOAI_HOI_CHAN } from "../config";

function LeftThongTinChung({
  data,
  anLoai<PERSON><PERSON><PERSON>han,
  width,
  isR<PERSON>only,
  list<PERSON>ap<PERSON><PERSON><PERSON><PERSON>,
  listLoaiHoi<PERSON><PERSON>,
  listAllThamGiaHoiChan,
  listAllKhoa,
  dsNhanVien,
  onChange,
  onChangeTextFiled,
  t,
  listTienLuong,
  BAT_BUOC_CAC_TRUONG_HOI_CHAN_THUONG,
  BAT_BUOC_CAC_TRUONG_HOI_CHAN_THONG_QUA_MO,
  dataBAT_BUOC_THANH_PHAN_THAM_GIA_HOI_CHAN,
}) {
  if (data.loaiHoiChan === LOAI_HOI_CHAN.KHAM_CHUYEN_KHOA) {
    return (
      <>
        <Col md={12} xl={12} xxl={11}>
          <SelectGroup $marginTop={"0"} style={{ overflow: "hidden" }}>
            <span className="label">{t("common.loai")}: </span>
            <Select
              className="select-box"
              style={{ minWidth: "250px", marginBottom: "5px" }}
              onChange={onChange("loaiHoiChan")}
              data={listLoaiHoiChan}
              value={data.loaiHoiChan}
              disabled={isReadonly}
            />
          </SelectGroup>
        </Col>
        <Col md={12} xl={12} xxl={11}>
          <SelectGroup $marginTop={"0"} style={{ overflow: "hidden" }}>
            <span>{t("quanLyNoiTru.bacSiDieuTri")}:</span>
            <Select
              className="select-box"
              onChange={onChange("bacSiDieuTriId")}
              data={dsNhanVien}
              showArrow={false}
              value={data.bacSiDieuTriId}
              disabled={isReadonly}
            />
          </SelectGroup>
        </Col>
        <Col md={12} xl={12} xxl={11}>
          <SelectGroup $marginTop={"0"} style={{ overflow: "hidden" }}>
            <span>
              {t("hoiChan.moiKhoaHoiChan")}: {"  "}
            </span>
            <Select
              className="select-box"
              onChange={onChange("dsKhoaHoiChanId")}
              data={listAllKhoa}
              showArrow={false}
              value={data.dsKhoaHoiChanId}
              disabled={isReadonly}
              mode="multiple"
            />
          </SelectGroup>
        </Col>
        <Col md={12} xl={12} xxl={11}>
          <SelectGroup $marginTop={"0"} style={{ overflow: "hidden" }}>
            <span>
              {t("quanLyNoiTru.bacSiKCK")}: {"  "}
            </span>
            <Select
              className="select-box"
              onChange={onChange("dsBacSiHoiChanId")}
              data={dsNhanVien}
              showArrow={false}
              value={data.dsBacSiHoiChanId}
              disabled={isReadonly}
              mode="multiple"
            />
          </SelectGroup>
        </Col>
      </>
    );
  }
  return (
    <>
      <Col md={12} xl={12} xxl={anLoaiHoiChan ? 7 : 12}>
        <span className="label">
          {t("hoiChan.capHoiChan")}: {"  "}
        </span>
        <Select
          className="select-hoi-chan"
          style={{
            minWidth: anLoaiHoiChan && width > 1536 ? "140px" : "200px",
            marginBottom: "5px",
          }}
          onChange={onChange("capHoiChan")}
          data={listCapHoiChan}
          value={data.capHoiChan}
          disabled={isReadonly}
        />
      </Col>
      <Col md={12} xl={12} xxl={anLoaiHoiChan ? 6 : 12}>
        <span className="label">
          {t("hoiChan.tienLuong")}: {"  "}
        </span>
        <Select
          className="select-hoi-chan"
          style={{
            minWidth: anLoaiHoiChan && width > 1536 ? "100px" : "200px",
            marginBottom: "5px",
          }}
          onChange={onChange("tienLuong")}
          data={listTienLuong}
          value={data.tienLuong}
          disabled={isReadonly}
        />
      </Col>
      {anLoaiHoiChan && (
        <Col md={12} xl={12} xxl={11}>
          <span className="label">
            {t("hoiChan.loaiHoiChan")}: {"  "}
          </span>
          <Select
            className="select-hoi-chan"
            style={{ minWidth: "250px", marginBottom: "5px" }}
            onChange={onChange("loaiHoiChan")}
            data={listLoaiHoiChan}
            value={data.loaiHoiChan}
            disabled={isReadonly}
          />
        </Col>
      )}
      <Col md={12} xl={12} xxl={12}>
        <span className="label">{t("danhMuc.thanhPhanThamGiaHoiChan")}: </span>
        <Select
          style={{ minWidth: "250px", marginBottom: "5px" }}
          className="select-hoi-chan"
          onChange={onChange("thamGiaHoiChan")}
          data={listAllThamGiaHoiChan}
          value={data.thamGiaHoiChan}
        />
      </Col>
      {data.loaiHoiChan !== LOAI_HOI_CHAN.THONG_QUA_MO ? (
        <Col span={24}>
          <TextField
            label={t("hoiChan.kinhGui")}
            className="input_custom"
            marginTop={5}
            onChange={onChangeTextFiled("kinhGui")}
            html={data.kinhGui}
          />
        </Col>
      ) : (
        <Col span={24}></Col>
      )}
      <Row gutter={12}>
        <Col span={12}>
          <SelectGroup $marginTop={"0"} style={{ overflow: "hidden" }}>
            <span>
              {t("hoiChan.moiKhoaHoiChan")}
              <span style={{ color: "red" }}>
                {(BAT_BUOC_CAC_TRUONG_HOI_CHAN_THUONG?.eval() &&
                  data.loaiHoiChan === LOAI_HOI_CHAN.THUONG) ||
                (BAT_BUOC_CAC_TRUONG_HOI_CHAN_THONG_QUA_MO?.eval() &&
                  data.loaiHoiChan === LOAI_HOI_CHAN.THONG_QUA_MO)
                  ? "*"
                  : ""}
              </span>
              : {"  "}
            </span>
            <Select
              className="select-box"
              onChange={onChange("dsKhoaHoiChanId")}
              data={listAllKhoa}
              showArrow={false}
              value={data.dsKhoaHoiChanId}
              disabled={isReadonly}
              mode="multiple"
            />
          </SelectGroup>
        </Col>
        <Col span={12}>
          <SelectGroup $marginTop={"0"} style={{ overflow: "hidden" }}>
            <span>
              {t("hoiChan.moiBacSiHoiChan")}: {"  "}
            </span>
            <Select
              className="select-box"
              onChange={onChange("dsBacSiHoiChanId")}
              data={dsNhanVien}
              showArrow={false}
              value={data.dsBacSiHoiChanId}
              disabled={isReadonly}
              mode="multiple"
            />
          </SelectGroup>
        </Col>
      </Row>
      <Col span={12}>
        <SelectGroup>
          <span>
            {t("hoiChan.chuTri")}
            <span style={{ color: "red" }}>
              {(BAT_BUOC_CAC_TRUONG_HOI_CHAN_THUONG?.eval() &&
                data.loaiHoiChan === LOAI_HOI_CHAN.THUONG) ||
              (BAT_BUOC_CAC_TRUONG_HOI_CHAN_THONG_QUA_MO?.eval() &&
                data.loaiHoiChan === LOAI_HOI_CHAN.THONG_QUA_MO)
                ? "*"
                : ""}
            </span>
            : {"  "}
          </span>
          <Select
            className="select-box"
            onChange={onChange("chuTriId")}
            data={dsNhanVien}
            showArrow={false}
            value={data.chuTriId}
            disabled={isReadonly}
          />
        </SelectGroup>
      </Col>

      <Col span={12}>
        <SelectGroup>
          <span>
            {t("hoiChan.thuKy")}
            <span style={{ color: "red" }}>
              {(BAT_BUOC_CAC_TRUONG_HOI_CHAN_THUONG?.eval() &&
                data.loaiHoiChan === LOAI_HOI_CHAN.THUONG) ||
              (BAT_BUOC_CAC_TRUONG_HOI_CHAN_THONG_QUA_MO?.eval() &&
                data.loaiHoiChan === LOAI_HOI_CHAN.THONG_QUA_MO)
                ? "*"
                : ""}
            </span>
            : {"  "}
          </span>
          <Select
            className="select-box"
            onChange={onChange("thuKyId")}
            data={dsNhanVien}
            showArrow={false}
            value={data.thuKyId}
            disabled={isReadonly}
          />
        </SelectGroup>
      </Col>
      <Col span={24}>
        <SelectGroup>
          <span>
            {t("hoiChan.thanhPhanThamGia")}{" "}
            <span style={{ color: "red" }}>
              {dataBAT_BUOC_THANH_PHAN_THAM_GIA_HOI_CHAN?.eval() ? "*" : ""}
            </span>
            : {"  "}
          </span>
          <div className="select-box">
            <Select
              mode="multiple"
              onChange={onChange("dsThanhPhanId")}
              style={{
                width: "100%",
              }}
              data={dsNhanVien}
              value={data.dsThanhPhanId}
              disabled={isReadonly}
            />
          </div>
        </SelectGroup>
      </Col>
      <Col span={24}>
        <SelectGroup>
          <span>
            {t("hoiChan.thanhPhanThamGiaNgoaiVien")}: {"  "}
          </span>
          <div className="select-box">
            <SelectAntd
              mode="tags"
              onChange={onChange("dsThanhPhanNgoaiVien")}
              style={{
                width: "100%",
              }}
              value={data.dsThanhPhanNgoaiVien || []}
              disabled={isReadonly}
            />
          </div>
        </SelectGroup>
      </Col>
    </>
  );
}

export default LeftThongTinChung;
