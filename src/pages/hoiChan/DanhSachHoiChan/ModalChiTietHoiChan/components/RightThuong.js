import React from "react";
import { TextField, SelectLargeData, Select } from "components";
import { SelectGroup, TextFieldStyled } from "../styled";
import { LOAI_HOI_CHAN } from "../config";
import { Row, Col } from "antd";
import CustomTag from "pages/khamBenh/KhamCoBan/ChanDoan/ChanDoanBenh/CustomTag";
import { useThietLap } from "hooks";
import { THIET_LAP_CHUNG } from "constants/index";

const { SelectChanDoan, SelectDvPTTT } = SelectLargeData;

function RightThuong({
  data,
  onChange,
  isReadonly,
  t,
  onChangeTextFiled,
  dsNhanVien,
  BAT_BUOC_CAC_TRUONG_HOI_CHAN_THUOC,
  BAT_BUOC_CAC_TRUONG_HOI_CHAN_THUONG,
  onChangeMoTa,
}) {
  const [dataCHO_PHEP_NHAP_MO_TA_CHAN_DOAN] = useThietLap(
    THIET_LAP_CHUNG.CHO_PHEP_NHAP_MO_TA_CHAN_DOAN,
    "TRUE"
  );
  return (
    <>
      <Col span={24}>
        <TextFieldStyled>
          <TextField
            label={t("common.ghiChu")}
            className="input_custom"
            marginTop={5}
            onChange={onChangeTextFiled("ghiChu")}
            html={data.ghiChu ?? ""}
            type="html"
          />
        </TextFieldStyled>
      </Col>
      <h1>{t("hoiChan.nhanXetBenhTrang").toUpperCase()}</h1>
      <ul>
        <li>
          <TextFieldStyled>
            <TextField
              className="input_custom"
              marginTop={5}
              onChange={onChangeTextFiled("nhanXetBenh")}
              html={data.nhanXetBenh ?? ""}
              type="html"
            />
          </TextFieldStyled>
        </li>
      </ul>
      <h1>{t("hoiChan.ketQuaHoiChan")}</h1>
      <ul>
        <span>
          {" "}
          {"1. "}
          {t("hoiChan.chanDoan")}
          {":"}
        </span>
        <li>
          <SelectGroup>
            <span>{t("hoiChan.chanDoanBenh")}:</span>
            <div className="select-box">
              <SelectChanDoan
                mode="multiple"
                value={(data.dsCdChinhId || []).map((item) => item + "")}
                style={{
                  width: "100%",
                }}
                maxItem={1}
                onChange={onChange("dsCdChinhId")}
                disabled={isReadonly}
                tagRender={CustomTag(
                  onChangeMoTa("dsCdChinh"),
                  data.moTaChanDoan?.dsCdChinh
                )}
              />
            </div>
          </SelectGroup>
        </li>
        <li>
          <SelectGroup>
            <span>{t("hoiChan.chanDoanKemTheo")}:</span>
            <div className="select-box">
              <SelectChanDoan
                mode="multiple"
                value={(data.dsCdKemTheoId || []).map((item) => item + "")}
                style={{
                  width: "100%",
                }}
                onChange={onChange("dsCdKemTheoId")}
                disabled={isReadonly}
                tagRender={CustomTag(
                  onChangeMoTa("dsCdKemTheo"),
                  data.moTaChanDoan?.dsCdKemTheo
                )}
              />
            </div>
          </SelectGroup>
        </li>
        <li>
          <TextFieldStyled>
            <TextField
              label={
                <span>
                  {t("hoiChan.chanDoanMoTaChiTiet")}
                  <span style={{ color: "red" }}>
                    {(BAT_BUOC_CAC_TRUONG_HOI_CHAN_THUOC?.eval() &&
                      data.loaiHoiChan === LOAI_HOI_CHAN.THUOC) ||
                    (BAT_BUOC_CAC_TRUONG_HOI_CHAN_THUONG?.eval() &&
                      data.loaiHoiChan === LOAI_HOI_CHAN.THUONG)
                      ? "*"
                      : ""}
                  </span>
                </span>
              }
              className="input_custom"
              marginTop={10}
              onChange={onChangeTextFiled("moTa")}
              html={data.moTa ?? ""}
              type="html"
              disabled={
                isReadonly || !dataCHO_PHEP_NHAP_MO_TA_CHAN_DOAN?.eval()
              }
            />
          </TextFieldStyled>
        </li>
      </ul>
      <ul>
        <span>
          {" "}
          {"2. "}
          {t("hoiChan.phuongPhapPTTT")}
          {":"}
        </span>
        <li>
          <SelectGroup>
            <div className="select-box">
              <SelectDvPTTT
                value={data.dsDichVuId || []}
                style={{
                  width: "100%",
                }}
                onChange={onChange("dsDichVuId")}
                disabled={isReadonly}
                mode="multiple"
              />
            </div>
          </SelectGroup>
        </li>
        <li>
          <Row gutter={[8, 8]}>
            {[
              { label: t("hoiChan.ptvChinh"), value: "ptvId" },
              { label: t("hoiChan.ptvPhu1"), value: "phuPtv1Id" },
              { label: t("hoiChan.ptvPhu2"), value: "phuPtv2Id" },
            ]
              .filter((item) =>
                BAT_BUOC_CAC_TRUONG_HOI_CHAN_THUONG?.eval() &&
                data.loaiHoiChan === LOAI_HOI_CHAN.THUONG
                  ? item.value === "phuPtv1Id" ||
                    item.value === "phuPtv2Id" ||
                    item.value === "ptvId"
                  : item.value === "ptvId"
              )
              .map((item, index, array) => (
                <Col span={24 / array.length} key={index}>
                  <SelectGroup>
                    <span>{item.label}:</span>
                    <div className="select-box">
                      <Select
                        onChange={onChange(item.value)}
                        style={{
                          width: "100%",
                        }}
                        showArrow={false}
                        data={dsNhanVien}
                        value={data[item.value]}
                        disabled={isReadonly}
                      />
                    </div>
                  </SelectGroup>
                </Col>
              ))}
          </Row>
        </li>
      </ul>
      <ul>
        <span>
          {" "}
          {"3. "}
          {t("hoiChan.giaiDoanBenh")}
          {":"}
        </span>
        <li>
          <TextFieldStyled>
            <TextField
              className="input_custom"
              marginTop={5}
              onChange={onChangeTextFiled("giaiDoanBenh")}
              html={data.giaiDoanBenh ?? ""}
              type="html"
            />
          </TextFieldStyled>
        </li>
      </ul>
      <ul>
        <span>
          {" "}
          {"4. "}
          {t("hoiChan.ketLuan")}
          <span style={{ color: "red" }}>
            {(BAT_BUOC_CAC_TRUONG_HOI_CHAN_THUOC?.eval() &&
              data.loaiHoiChan === LOAI_HOI_CHAN.THUOC) ||
            (BAT_BUOC_CAC_TRUONG_HOI_CHAN_THUONG?.eval() &&
              data.loaiHoiChan === LOAI_HOI_CHAN.THUONG)
              ? "*"
              : ""}
          </span>
          {":"}
        </span>
        <li>
          <TextFieldStyled>
            <TextField
              className="input_custom"
              marginTop={5}
              onChange={onChangeTextFiled("ketLuan")}
              html={data.ketLuan ?? ""}
              type="html"
            />
          </TextFieldStyled>
        </li>
      </ul>
    </>
  );
}

export default RightThuong;
