import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
  useMemo,
} from "react";
import { Form, Row, Col } from "antd";
import {
  Select,
  Button,
  ModalTemplate,
  Checkbox,
  DateTimePicker,
} from "components";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import FormWraper from "components/FormWraper";
import moment from "moment";
import { useStore, useLoading, useListAll, useThietLap } from "hooks";
import { DOI_TUONG_KCB, THIET_LAP_CHUNG, ROLES } from "constants/index";
import { isArray } from "utils/index";
import { lowerFirst } from "lodash";
import { checkRole } from "lib-utils/role-utils";
import { selectMaTen } from "redux-store/selectors";
import { SVG } from "assets";
import { Main, SHGLabel, GiuongNguoiNhaDiv } from "./styled";

const ModalThemMoiPhongGiuong = (props, ref) => {
  const refCallback = useRef(null);
  const refModal = useRef(null);
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const giuongTuChonChoNguoiNha = Form.useWatch(
    "giuongTuChonChoNguoiNha",
    form
  );
  const datCaPhong = Form.useWatch("datCaPhong", form);
  const { showLoading, hideLoading } = useLoading();
  const { doiTuongKcb, maKhoaNb } = useStore(
    "danhSachNguoiBenhNoiTru.chiTietNguoiBenhNoiTru",
    {}
  );
  const [dataKHONG_BAT_BUOC_NHAP_THONG_TIN_GIUONG, isLoadFinish] = useThietLap(
    THIET_LAP_CHUNG.KHONG_BAT_BUOC_NHAP_THONG_TIN_GIUONG,
    ""
  );
  const [dataKHONG_BAT_BUOC_PHAN_GIUONG_TU_CHON, isLoadFinish2] = useThietLap(
    THIET_LAP_CHUNG.KHONG_BAT_BUOC_PHAN_GIUONG_TU_CHON
  );
  const [dataCHI_HIEN_THI_LOAI_GIUONG_THUONG, isLoadFinish3] = useThietLap(
    THIET_LAP_CHUNG.CHI_HIEN_THI_LOAI_GIUONG_THUONG,
    "false"
  );
  //state
  const [state, _setState] = useState({
    show: false,
    currentItem: null,
    bnDaNghiDieuTri: false,
    hoiSucTichCuc: null,
    renderReady: false,
    disabledDvGiuongTuChon: false,
  });

  const setState = (data) => {
    _setState((prevState) => ({
      ...prevState,
      ...data,
    }));
  };

  const [listAllNguonKhacChiTra] = useListAll("nguonKhac", {}, state.show);

  //redux
  const {
    dsPhong,
    dsGiuong,
    dsDVGiuong, //giường thường
    dsNb,
    dsLoaiGiuong,
    dsDVGiuongTuChon, //giường tự chọn
  } = useStore(
    "phanPhongGiuong",
    {},
    {
      fields:
        "dsPhong, dsGiuong, dsDVGiuong, dsNb, dsLoaiGiuong, dsDVGiuongTuChon",
    }
  );

  const dataSearch = useStore("soDoPhongGiuong.dataSearch", {});
  const listKhoaTheoTaiKhoan = useStore("khoa.listKhoaTheoTaiKhoan", []);
  const {
    phanPhongGiuong: {
      getDsPhongTheoKhoa,
      getSoHieuGiuongByPhong,
      getDsDVGiuong,
      getDsDVGiuongTuChon,
      phanGiuong,
      phanGiuongTuChon,
      getDsMucDichSuDungByDVGiuong,
      getListAllLoaiGiuong,
      getDvGiuongTheoThietLap,
    },
    noiTruPhongGiuong: { onSearch: getDsPhongGiuong },
  } = useDispatch();

  useImperativeHandle(ref, () => ({
    show: async (data = {}, callback) => {
      const {
        khoaId,
        phongId,
        id: giuongId,
        dsLoaiPhong,
        isGiuongTuChon,
      } = data;
      setState({
        show: true,
        currentItem: data,
        readOnlyTuThoiGian: false,
        minTuThoiGian: null,
        dsLoaiPhong,
        renderReady: false,
        isGiuongTuChon,
      });

      refCallback.current = callback;

      await getDsPhongTheoKhoa({ khoaId, dsLoaiPhong: [50, 60] });
      const res = await getSoHieuGiuongByPhong({ phongId, page: 0, size: "" });
      kiemTraHoiSucTichCuc({ listGiuong: res, giuongId, phongId });

      await getDsDVGiuongTuChon({
        page: "",
        size: "",
        khoaChiDinhId: dataSearch?.khoaId,
        phanLoai: 10,
      });

      setState({ renderReady: true });
    },
  }));

  const dsLoaiGiuongMemo = useMemo(() => {
    if (isLoadFinish3 && dsLoaiGiuong.length > 0) {
      //Khai báo true/Mã loại giường thường cách nhau bởi dấu phẩy
      const [chiHienThiGiuongThuongStr, maLoaiGiuongStr] =
        dataCHI_HIEN_THI_LOAI_GIUONG_THUONG.split("/");
      const chiHienThiGiuongThuong = chiHienThiGiuongThuongStr === "true"; // chuyển thành boolean
      const dsMaLoaiGiuong = maLoaiGiuongStr ? maLoaiGiuongStr.split(",") : [];

      if (chiHienThiGiuongThuong) {
        return (dsLoaiGiuong || []).filter((x) =>
          dsMaLoaiGiuong.includes(x.ma)
        );
      }
    }

    return dsLoaiGiuong || [];
  }, [dsLoaiGiuong, isLoadFinish3]);
  const dsLoaiGiuongMemoIds = (dsLoaiGiuongMemo || []).map((x) => x.id);

  useEffect(() => {
    getListAllLoaiGiuong({ page: "", size: "", active: true });
  }, []);

  useEffect(() => {
    if (state?.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state?.show]);

  function layDsDvGiuong({ isHoiSucTichCuc, phongId }) {
    const hoiSucTichCuc =
      isHoiSucTichCuc !== undefined ? isHoiSucTichCuc : state.hoiSucTichCuc;

    getDsDVGiuong({
      page: "",
      size: "",
      khoaChiDinhId: dataSearch?.khoaId,
      hoiSucTichCuc,
      phanLoai: 20,
      phongId,
    });
  }

  function kiemTraThietLapDvGiuong({ nbDotDieuTriId, giuongId, tuThoiGian }) {
    if (!nbDotDieuTriId || !giuongId || !tuThoiGian) {
      return;
    }
    getDvGiuongTheoThietLap({
      nbDotDieuTriId,
      khoaChiDinhId: dataSearch?.khoaId,
      thoiGianThucHien: moment(tuThoiGian).format("DD-MM-YYYY HH:mm:ss"),
      giuongId,
    }).then((res) => {
      if (res && (dsDVGiuong || []).findIndex((x) => x.id == res.id) > -1) {
        form.setFieldsValue({
          dvGiuongId: res.id,
        });
        onUpdateDvGiuongId(res.id);
      }
    });
  }

  useEffect(() => {
    if (state.show && state.currentItem && !state.renderReady) {
      const { khoaId, phongId, id } = state.currentItem || {};
      const _selectedGiuong = (dsGiuong || []).find((x) => x.id == id);

      form.setFieldsValue({
        khoaId,
        phongId,
        giuongId: id,
        nbDotDieuTriId: dataSearch?.nbDotDieuTriId || null,
        loaiGiuongId:
          isArray(dsGiuong, true) &&
          dsLoaiGiuongMemoIds.includes(dsGiuong[0]?.loaiGiuongId)
            ? dsGiuong[0]?.loaiGiuongId
            : dsLoaiGiuongMemoIds.length === 1
            ? dsLoaiGiuongMemoIds[0]
            : null,
        dvGiuongTuChonId:
          state.isGiuongTuChon && _selectedGiuong?.dvGiuongTuChonId
            ? _selectedGiuong?.dvGiuongTuChonId
            : null,
      });

      if (state.isGiuongTuChon && _selectedGiuong?.dvGiuongTuChonId) {
        setState({ disabledDvGiuongTuChon: true });
      } else {
        setState({ disabledDvGiuongTuChon: false });
      }
    }
  }, [
    state.show,
    state.currentItem,
    dataSearch?.nbDotDieuTriId,
    dsGiuong,
    dsLoaiGiuongMemo,
    state.renderReady,
    state.isGiuongTuChon,
  ]);

  useEffect(() => {
    if (state.currentItem) {
      if (dsNb && dsNb.length > 0 && dataSearch.nbDotDieuTriId) {
        updateValidKetThucKham(dataSearch.nbDotDieuTriId, (params) => {
          kiemTraThietLapDvGiuong({
            nbDotDieuTriId: dataSearch.nbDotDieuTriId,
            giuongId: state.currentItem.id,
            ...params,
          });
        });
      }
    }
  }, [state.currentItem, dsNb, dataSearch.nbDotDieuTriId]);

  function kiemTraNghiDieuTri(_nbDotDieuTriId) {
    getDsPhongGiuong({
      nbDotDieuTriId: _nbDotDieuTriId,
      sort: "tuThoiGian,asc",
    }).then((res) => {
      if (res?.data && res.data.length > 0) {
        const newestPG = res?.data[res?.data.length - 1];

        if (newestPG.loai == 30) {
          setState({
            bnDaNghiDieuTri: true,
            minTuThoiGian: newestPG.tuThoiGian,
          });

          //Bỏ mặc định trường Nằm từ ngày với trường hợp NB quay lại điều trị sau khi Nghỉ điều trị
          form.setFieldsValue({ tuThoiGian: null });
        } else {
          setState({
            bnDaNghiDieuTri: false,
          });
        }
      } else {
        setState({
          bnDaNghiDieuTri: false,
        });
      }
    });
  }

  function kiemTraHoiSucTichCuc({ listGiuong, giuongId, phongId }) {
    let listData = isArray(listGiuong, true) ? listGiuong : dsGiuong;
    const hoiSucTichCuc = (listData || []).find(
      (x) => x.id == giuongId
    )?.hoiSucTichCuc;
    setState({ hoiSucTichCuc });
    layDsDvGiuong({
      isHoiSucTichCuc: hoiSucTichCuc,
      phongId,
    });
  }

  useEffect(() => {
    if (state.show && dataSearch.nbDotDieuTriId) {
      kiemTraNghiDieuTri(dataSearch.nbDotDieuTriId);
    }
  }, [dataSearch.nbDotDieuTriId, state.show]);

  const onFinish = (values) => {
    console.log("Success:", values);
  };

  const onFinishFailed = (errorInfo) => {
    console.log("Failed:", errorInfo);
  };

  //function
  const onClose = () => {
    form.resetFields();
    setState({ show: false, currentItem: null, disabledDvGiuongTuChon: false });
  };

  const onSave = () => {
    showLoading();

    form
      .validateFields()
      .then((values) => {
        const {
          dvGiuongId,
          dvGiuongTuChonId,
          nbDotDieuTriId,
          giuongId,
          phongId,
          tuThoiGian,
          denThoiGian,
          loaiGiuongId,
          tuTra,
          mucDichId,
          nguonKhacId,
          giuongTuChonChoNguoiNha,
          datCaPhong,
          dsGiuongNguoiNha,
        } = values;

        if (state.isGiuongTuChon) {
          phanGiuongTuChon({
            nbDotDieuTriId: dataSearch.nbDotDieuTriId,
            giuongId,
            dvGiuongId: dvGiuongTuChonId,
            tuThoiGian: moment(tuThoiGian).format("DD-MM-YYYY HH:mm:ss"),
            denThoiGian: denThoiGian
              ? moment(denThoiGian).format("DD-MM-YYYY HH:mm:ss")
              : null,
            ...(giuongTuChonChoNguoiNha || datCaPhong
              ? { dsGiuongNguoiNha }
              : {}),
          })
            .then(() => {
              hideLoading();
              refCallback.current && refCallback.current();
              onClose();
            })
            .catch(() => {
              hideLoading();
            });
        } else {
          phanGiuong({
            dvGiuongId,
            dvGiuongTuChonId,
            nbDotDieuTriId,
            giuongId,
            phongId,
            loaiGiuongId,
            tuThoiGian: moment(tuThoiGian).format("DD-MM-YYYY HH:mm:ss"),
            tuTra,
            mucDichId,
            nguonKhacId,
            ...(giuongTuChonChoNguoiNha || datCaPhong
              ? { dsGiuongNguoiNha }
              : {}),
          })
            .then(() => {
              hideLoading();
              refCallback.current && refCallback.current();
              onClose();
            })
            .catch(() => {
              hideLoading();
            });
        }
      })
      .catch(() => {
        hideLoading();
      });
  };

  const onUpdateDvGiuongId = (_dvGiuongId) => {
    getDsMucDichSuDungByDVGiuong({
      page: 0,
      size: 500,
      active: true,
      dichVuId: _dvGiuongId,
    }).then((res) => {
      if (res && res.length == 1) {
        form.setFieldsValue({ mucDichId: res[0].id });
      } else {
        form.setFieldsValue({ mucDichId: null });
      }
    });
  };

  function onValuesChange(changedValues, allValues) {
    if (changedValues["khoaId"]) {
      getDsPhongTheoKhoa({
        khoaId: changedValues["khoaId"],
        dsLoaiPhong: [50, 60],
      });
      getDsDVGiuongTuChon({
        page: "",
        size: "",
        khoaChiDinhId: changedValues["khoaId"],
        phanLoai: 10,
      }).finally(() => {
        setState({ renderReady: true });
      });
      form.setFieldsValue({
        phongId: null,
        giuongId: null,
        loaiGiuongId: null,
      });
    }

    if (changedValues["phongId"]) {
      kiemTraHoiSucTichCuc({
        giuongId: allValues["giuongId"],
        phongId: changedValues["phongId"],
      });
      getSoHieuGiuongByPhong({ phongId: changedValues["phongId"] }).then(
        (res) => {
          if (res && res.length === 1) {
            form.setFieldsValue({
              giuongId: res[0].id,
              loaiGiuongId: dsLoaiGiuongMemoIds.includes(res[0].loaiGiuongId)
                ? res[0].loaiGiuongId
                : dsLoaiGiuongMemoIds.length === 1
                ? dsLoaiGiuongMemoIds[0]
                : null,
            });
          } else {
            form.setFieldsValue({
              giuongId: null,
              loaiGiuongId: null,
            });
          }
        }
      );
    }

    if (changedValues["tuThoiGian"]) {
      kiemTraThietLapDvGiuong(allValues);
    }

    if (changedValues["nbDotDieuTriId"]) {
      updateValidKetThucKham(changedValues["nbDotDieuTriId"]);

      kiemTraNghiDieuTri(changedValues["nbDotDieuTriId"]);
      kiemTraThietLapDvGiuong(allValues);
    }

    if (changedValues["dvGiuongId"]) {
      onUpdateDvGiuongId(changedValues["dvGiuongId"]);
      let dsDvId = (dsDVGiuong || []).filter((i) => i.dichVuId);
      dsDvId = dsDvId.reduce((acc, cur) => {
        if (cur.nguonKhacId) {
          acc.push(cur.nguonKhacId);
        }
        return acc;
      }, []);
      if (isArray(dsDvId, true)) {
        form.setFieldsValue({ nguonKhacId: dsDvId[0] });
      }
    }

    if (changedValues["giuongId"]) {
      const _selectedGiuong = (dsGiuong || []).find(
        (i) => i.id === changedValues["giuongId"]
      );
      let _loaiGiuongId = dsLoaiGiuongMemoIds.includes(
        _selectedGiuong?.loaiGiuongId
      )
        ? _selectedGiuong?.loaiGiuongId
        : dsLoaiGiuongMemoIds.length === 1
        ? dsLoaiGiuongMemoIds[0]
        : null;

      if (_loaiGiuongId) {
        form.setFieldsValue({ loaiGiuongId: _loaiGiuongId });
      }
      if (state.isGiuongTuChon) {
        form.setFieldsValue({
          dvGiuongTuChonId: _selectedGiuong?.dvGiuongTuChonId,
        });

        if (_selectedGiuong?.dvGiuongTuChonId) {
          setState({ disabledDvGiuongTuChon: true });
        } else {
          setState({ disabledDvGiuongTuChon: false });
        }
      }
      kiemTraHoiSucTichCuc({
        giuongId: changedValues["giuongId"],
        phongId: allValues.phongId,
      });
      kiemTraThietLapDvGiuong({
        ...allValues,
        ...(_loaiGiuongId && { loaiGiuongId: _loaiGiuongId }),
      });

      if (allValues["datCaPhong"]) {
        //khi check đặt cả phòng thì mặc định fill đủ số lượng giường
        const _allDsGiuongNguoiNha = (dsGiuong || [])
          .filter((x) => x.id !== changedValues["giuongId"])
          .map((x) => ({
            giuongId: x.id,
            dvGiuongId: null,
          }));
        form.setFieldsValue({ dsGiuongNguoiNha: _allDsGiuongNguoiNha });
      }
    }

    if (changedValues.hasOwnProperty("datCaPhong")) {
      if (changedValues["datCaPhong"]) {
        let _addState = {};
        //khi check đặt cả phòng thì mặc định fill đủ số lượng giường
        const _allDsGiuongNguoiNha = (dsGiuong || [])
          .filter((x) => x.id !== allValues.giuongId)
          .map((x, idx) => {
            if (x?.dvGiuongTuChonId) {
              _addState[`disabledDsGiuongNguoiNha${idx}`] = true;
            } else {
              _addState[`disabledDsGiuongNguoiNha${idx}`] = false;
            }

            return {
              giuongId: x.id,
              dvGiuongId: x?.dvGiuongTuChonId,
            };
          });

        setState(_addState);
        form.setFieldsValue({ dsGiuongNguoiNha: _allDsGiuongNguoiNha });
      } else {
        form.setFieldsValue({ dsGiuongNguoiNha: [] });
      }
    }

    if (changedValues["dsGiuongNguoiNha"]) {
      Object.entries(changedValues["dsGiuongNguoiNha"]).forEach(
        ([index, value]) => {
          if (value?.giuongId) {
            const _selectedGiuong = (dsGiuong || []).find(
              (i) => i.id === value.giuongId
            );

            const currentGiuongNguoiNha =
              allValues.dsGiuongNguoiNha[index] || {};

            form.setFieldsValue({
              dsGiuongNguoiNha: {
                [index]: {
                  ...currentGiuongNguoiNha,
                  dvGiuongId: _selectedGiuong?.dvGiuongTuChonId,
                },
              },
            });

            if (_selectedGiuong?.dvGiuongTuChonId) {
              setState({ [`disabledDsGiuongNguoiNha${index}`]: true });
            } else {
              setState({ [`disabledDsGiuongNguoiNha${index}`]: false });
            }
          }
        }
      );
    }
  }

  const updateValidKetThucKham = async (_nbDotDieuTriId, cbFunc = null) => {
    const infoNb = (dsNb || []).find((x) => x.id == _nbDotDieuTriId);

    if (!infoNb) return;
    const {
      tuThoiGianSdGiuong,
      giuongId,
      denThoiGianSdGiuong,
      thoiGianVaoKhoa,
      thoiGianVaoVien,
    } = infoNb;

    // giuongId = null -> từ ngày lấy trường tuThoiGianSdGiuong, k cho người dùng sửa
    // giuongId khác null, denThoiGianSdGiuong khác null -> từ ngày lấy trường denThoiGianSdGiuong, k cho người dùng sửa
    // giuongId khác null, denThoiGianSdGiuong = null -> từ ngày để null, người dùng phải chọn th.g lớn hơn tuThoiGianSdGiuong
    if (!giuongId) {
      setState({ readOnlyTuThoiGian: true });
      form.setFieldsValue({ tuThoiGian: moment() });
      if (cbFunc) {
        cbFunc({ tuThoiGian: moment() });
      }
    } else {
      if (denThoiGianSdGiuong) {
        setState({ readOnlyTuThoiGian: true });
        form.setFieldsValue({ tuThoiGian: moment() });
        if (cbFunc) {
          cbFunc({ tuThoiGian: moment() });
        }
      } else {
        setState({
          readOnlyTuThoiGian: false,
          minTuThoiGian: tuThoiGianSdGiuong,
        });

        const res = await getDsPhongGiuong({
          nbDotDieuTriId: _nbDotDieuTriId,
          sort: "tuThoiGian,asc",
        });

        let _data = (res?.data || []).filter((item) =>
          state.isGiuongTuChon ? item.loai == 50 : item.loai != 50
        );
        //nếu chưa có line giường tự chọn thì lấy dữ liệu của line giường thường để set mặc định giá trị
        if (state.isGiuongTuChon && _data.length == 0) {
          _data = (res?.data || []).filter((item) => item.loai === 10);
        }

        let _tuThoiGian;
        if (_data.length > 0) {
          const newestPG = _data[_data.length - 1];
          if (newestPG.loai == 10) {
            //vào viện
            _tuThoiGian = moment(thoiGianVaoVien);
          } else if (newestPG.loai == 20) {
            //chuyển khoa
            _tuThoiGian = moment(thoiGianVaoKhoa);
          } else if (newestPG.loai == 50) {
            //tự chọn
            _tuThoiGian = newestPG.denThoiGian
              ? moment(newestPG.denThoiGian)
              : null;
          } else {
            _tuThoiGian = moment();
          }
          form.setFieldsValue({ tuThoiGian: _tuThoiGian });
          if (cbFunc) {
            cbFunc({ tuThoiGian: _tuThoiGian });
          }
        }
      }
    }
  };

  function onDisabledDate(time) {
    if (state.minTuThoiGian) {
      return moment(time) < moment(state.minTuThoiGian);
    }

    return false;
  }

  const renderTenDvGiuong = (item) =>
    `${item.ma} - ${item.ten} (${(
      item.giaKhongBaoHiem || 0
    ).formatPrice()} | BH: ${(item.giaBaoHiem || 0).formatPrice()})`;

  const isBatBuocChonGiuong = useMemo(() => {
    if (!isLoadFinish) return false;

    const configValue = dataKHONG_BAT_BUOC_NHAP_THONG_TIN_GIUONG?.trim();
    const maKhoa = maKhoaNb?.trim()?.toUpperCase();

    if (!configValue || configValue.toLowerCase() === "false") {
      return true;
    }

    if (configValue.toLowerCase() === "true") {
      return false;
    }

    const [flag, dsKhoa] = configValue.split("/");
    if (flag?.trim()?.toLowerCase() === "true" && dsKhoa && maKhoa) {
      const danhSachMaKhoaNb = dsKhoa
        .split(",")
        .map((k) => k.trim().toUpperCase())
        .filter(Boolean);

      return !danhSachMaKhoaNb.includes(maKhoa);
    }

    return true;
  }, [dataKHONG_BAT_BUOC_NHAP_THONG_TIN_GIUONG, maKhoaNb, isLoadFinish]);

  const isBatBuocPhanGiuongTuChon = useMemo(() => {
    if (!isLoadFinish2) return false;

    return (
      dataKHONG_BAT_BUOC_PHAN_GIUONG_TU_CHON?.trim()?.toLowerCase() !== "true"
    );
  }, [dataKHONG_BAT_BUOC_PHAN_GIUONG_TU_CHON, isLoadFinish2]);

  return (
    <ModalTemplate
      width={"65%"}
      ref={refModal}
      title={t("quanLyNoiTru.phongGiuong.themMoiPhongGiuong")}
      onCancel={onClose}
      actionLeft={<Button.QuayLai onClick={onClose} />}
      actionRight={
        <Button
          type="primary"
          minWidth={100}
          onClick={onSave}
          rightIcon={<SVG.IcSave />}
        >
          {t("common.luu")}
        </Button>
      }
    >
      <Main>
        <FormWraper
          name="basic"
          initialValues={{ remember: true, tuThoiGian: moment() }}
          onFinish={onFinish}
          layout={"vertical"}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          form={form}
          onValuesChange={onValuesChange}
        >
          <Row>
            {state.isGiuongTuChon && (
              <>
                <Col span={12}>
                  <Form.Item
                    label={t("quanLyNoiTru.khoaChiDinh")}
                    name="khoaId"
                    rules={[
                      {
                        required: true,
                        message: `${t("quanLyNoiTru.vuiLongChonKhoa")}!`,
                      },
                    ]}
                  >
                    <Select
                      data={listKhoaTheoTaiKhoan || []}
                      placeholder={t("common.chonKhoa")}
                      allowClear={false}
                    />
                  </Form.Item>
                </Col>

                <Col span={12}></Col>
              </>
            )}

            <Col span={12}>
              <Form.Item
                label={t("common.phong")}
                name="phongId"
                rules={[
                  {
                    required: true,
                    message: `${t("khamBenh.chiDinh.vuiLongChonPhong")}!`,
                  },
                ]}
              >
                <Select
                  data={dsPhong || []}
                  placeholder={t("common.chonPhong")}
                />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                label={t("quanLyNoiTru.phongGiuong.soHieuGiuong")}
                name="giuongId"
                rules={[
                  {
                    required: true,
                    message: `${t("quanLyNoiTru.vuiLongChonSoHieuGiuong")}!`,
                  },
                ]}
              >
                <Select
                  data={dsGiuong || []}
                  placeholder={t("danhMuc.chonSoHieuGiuong")}
                />
              </Form.Item>
            </Col>

            {!state.isGiuongTuChon && (
              <>
                <Col span={12}>
                  <Form.Item
                    label={t("quanLyNoiTru.phongGiuong.maHsMaBa")}
                    name="nbDotDieuTriId"
                    rules={[
                      {
                        required: true,
                        message: `${t("quanLyNoiTru.vuiLongChonMaHsMaBa")}!`,
                      },
                    ]}
                  >
                    <Select
                      data={(dsNb || []).map((x) => ({
                        id: x.id,
                        ten: `${x.maHoSo || ""}/${x.maBenhAn || ""}`,
                      }))}
                      placeholder={t("quanLyNoiTru.chonMaHsMaBa")}
                    />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    label={t("quanLyNoiTru.hoVaTenNb")}
                    name="nbDotDieuTriId"
                    rules={[
                      {
                        required: true,
                        message: `${t("quanLyNoiTru.vuiLongChonHoVaTenNB")}!`,
                      },
                    ]}
                  >
                    <Select
                      data={(dsNb || []).map((x) => ({
                        id: x.id,
                        ten: x.tenNb,
                      }))}
                      placeholder={t("quanLyNoiTru.chonHoVaTenNB")}
                    />
                  </Form.Item>
                </Col>
              </>
            )}

            <Col span={12}>
              <Form.Item
                label={t("quanLyNoiTru.phongGiuong.namTuNgay")}
                name="tuThoiGian"
                rules={[
                  {
                    required: true,
                    message: `${t("quanLyNoiTru.vuiLongChonThoiGian")}!`,
                  },
                ]}
              >
                <DateTimePicker
                  showTime={false}
                  // disabled={
                  //   state.readOnlyTuThoiGian && !state.bnDaNghiDieuTri
                  // }
                  // disabledDate={onDisabledDate}
                  placeholder={t("common.chonNgay")}
                  format="DD/MM/YYYY HH:mm:ss"
                />
              </Form.Item>
            </Col>

            {state.isGiuongTuChon && (
              <Col span={12}>
                <Form.Item
                  label={t("quanLyNoiTru.phongGiuong.namDenNgay")}
                  name="denThoiGian"
                >
                  <DateTimePicker
                    showTime={false}
                    // disabled={
                    //   state.readOnlyTuThoiGian && !state.bnDaNghiDieuTri
                    // }
                    // disabledDate={onDisabledDate}
                    placeholder={t("common.chonNgay")}
                    format="DD/MM/YYYY HH:mm:ss"
                  />
                </Form.Item>
              </Col>
            )}

            {!state.isGiuongTuChon && (
              <Col span={12}>
                <Form.Item
                  label={t("quanLyNoiTru.phongGiuong.loaiGiuong")}
                  name="loaiGiuongId"
                  rules={[
                    {
                      required: isBatBuocChonGiuong,
                      message: `${t("quanLyNoiTru.vuiLongChonLoaiGiuong")}!`,
                    },
                  ]}
                >
                  <Select
                    data={dsLoaiGiuongMemo}
                    placeholder={t("danhMuc.chonLoaiGiuong")}
                  />
                </Form.Item>
              </Col>
            )}

            {!state.isGiuongTuChon && (
              <Col span={24}>
                <Form.Item
                  label={t("quanLyNoiTru.phongGiuong.tenDichVuGiuongThuong")}
                  name="dvGiuongId"
                  rules={[
                    {
                      required:
                        doiTuongKcb !== DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU &&
                        isBatBuocChonGiuong,
                      message: `${t("danhMuc.vuiLongChonTitle", {
                        title: lowerFirst(
                          t("quanLyNoiTru.phongGiuong.tenDichVuGiuongThuong")
                        ),
                      })}!`,
                    },
                  ]}
                >
                  <Select
                    data={dsDVGiuong || []}
                    placeholder={t("danhMuc.chonTitle", {
                      title: lowerFirst(
                        t("quanLyNoiTru.phongGiuong.tenDichVuGiuongThuong")
                      ),
                    })}
                    disabled={
                      !checkRole([ROLES["QUAN_LY_NOI_TRU"].PHAN_GIUONG_THUONG])
                    }
                    getLabel={renderTenDvGiuong}
                    getValue={(item) => item.dichVuId}
                  />
                </Form.Item>
              </Col>
            )}

            <Col span={24}>
              <Form.Item
                label={t(
                  "quanLyNoiTru.phongGiuong.tenDvGiuongTuChonNbDangKyThem"
                )}
                name="dvGiuongTuChonId"
                rules={
                  state.dsLoaiPhong?.includes(60) &&
                  isBatBuocPhanGiuongTuChon && [
                    {
                      required: true,
                      message: `${t("danhMuc.vuiLongChonTitle", {
                        title: lowerFirst(
                          t(
                            "quanLyNoiTru.phongGiuong.tenDvGiuongTuChonNbDangKyThem"
                          )
                        ),
                      })}!`,
                    },
                  ]
                }
              >
                <Select
                  data={dsDVGiuongTuChon || []}
                  placeholder={t("danhMuc.chonTitle", {
                    title: lowerFirst(
                      t(
                        "quanLyNoiTru.phongGiuong.tenDvGiuongTuChonNbDangKyThem"
                      )
                    ),
                  })}
                  disabled={
                    !checkRole([
                      ROLES["QUAN_LY_NOI_TRU"].PHAN_GIUONG_TU_CHON,
                    ]) || state.disabledDvGiuongTuChon
                  }
                  getLabel={renderTenDvGiuong}
                  getValue={(item) => item.dichVuId}
                />
              </Form.Item>
            </Col>

            {!state.isGiuongTuChon && (
              <>
                <Col span={10}>
                  <Form.Item label={t("danhMuc.nguonKhac")} name="nguonKhacId">
                    <Select
                      data={listAllNguonKhacChiTra}
                      placeholder={t("danhMuc.nguonKhac")}
                    />
                  </Form.Item>
                </Col>

                {/* <Col span={12}>
                <Form.Item label="Mục đích sử dụng giường" name="mucDichId">
                  <Select
                    data={dsMucDichSuDung || []}
                    placeholder="Chọn phòng"
                  />
                </Form.Item>
              </Col> */}

                <Col span={3}>
                  <Form.Item label=" " name="tuTra" valuePropName="checked">
                    <Checkbox>{t("quanLyNoiTru.dvNoiTru.tuTra")}</Checkbox>
                  </Form.Item>
                </Col>
              </>
            )}
            {checkRole([
              ROLES["QUAN_LY_NOI_TRU"].HIEN_THI_CHECKBOX_MUA_THEM_GIUONG,
            ]) && (
              <>
                <Col span={7}>
                  <Form.Item
                    label=" "
                    name="giuongTuChonChoNguoiNha"
                    valuePropName="checked"
                  >
                    <Checkbox>
                      {t("quanLyNoiTru.dvNoiTru.giuongTuChonChoNguoiNha")}
                    </Checkbox>
                  </Form.Item>
                </Col>
                <Col span={4}>
                  <Form.Item
                    label=" "
                    name="datCaPhong"
                    valuePropName="checked"
                  >
                    <Checkbox>{t("quanLyNoiTru.dvNoiTru.datCaPhong")}</Checkbox>
                  </Form.Item>
                </Col>
              </>
            )}
          </Row>

          {(datCaPhong || giuongTuChonChoNguoiNha) && (
            <>
              <h2>{t("quanLyNoiTru.dvNoiTru.giuongChoNguoiNha")}</h2>
              <GiuongNguoiNhaDiv>
                <Row>
                  <Form.List name={"dsGiuongNguoiNha"}>
                    {(fields, { add, remove }) => {
                      return (
                        <>
                          {fields.map((field, index) => {
                            return (
                              <React.Fragment key={index}>
                                <Col span={12} className="form-item">
                                  <Form.Item
                                    label={
                                      <SHGLabel>
                                        <span>
                                          {t(
                                            "quanLyNoiTru.dvNoiTru.soHieuGiuongTuChonChoNguoiNhaGiuong",
                                            {
                                              num: index + 1,
                                            }
                                          )}
                                        </span>

                                        {!datCaPhong && (
                                          <SVG.IcDelete
                                            onClick={() => remove(field.name)}
                                          />
                                        )}
                                      </SHGLabel>
                                    }
                                    name={[field.name, "giuongId"]}
                                    rules={[
                                      {
                                        required: true,
                                        message: `${t(
                                          "quanLyNoiTru.vuiLongChonSoHieuGiuong"
                                        )}!`,
                                      },
                                    ]}
                                  >
                                    <Select
                                      data={dsGiuong || []}
                                      placeholder={t(
                                        "danhMuc.chonSoHieuGiuong"
                                      )}
                                    />
                                  </Form.Item>
                                </Col>

                                <Col span={24}>
                                  <Form.Item
                                    label={t(
                                      "quanLyNoiTru.dvNoiTru.tenDichVuGiuongTuChonChoNguoiNhaGiuong",
                                      { num: index + 1 }
                                    )}
                                    name={[field.name, "dvGiuongId"]}
                                    rules={[
                                      {
                                        required: true,
                                        message: `${t(
                                          "quanLyNoiTru.phongGiuong.vuiLongChonTenDichVuGiuong"
                                        )}!`,
                                      },
                                    ]}
                                  >
                                    <Select
                                      data={dsDVGiuongTuChon || []}
                                      placeholder={t(
                                        "quanLyNoiTru.chonDvGiuong"
                                      )}
                                      disabled={
                                        state[
                                          `disabledDsGiuongNguoiNha${index}`
                                        ]
                                      }
                                      getLabel={renderTenDvGiuong}
                                      getValue={(item) => item.dichVuId}
                                    />
                                  </Form.Item>
                                </Col>
                              </React.Fragment>
                            );
                          })}

                          {!datCaPhong && (
                            <Col span={5} offset={1}>
                              <Button
                                minWidth={200}
                                rightIcon={<SVG.IcAdd />}
                                type="success"
                                onClick={() => add()}
                                iconHeight={20}
                              >
                                {t(
                                  "quanLyNoiTru.dvNoiTru.themGiuongChoNguoiNha"
                                )}
                              </Button>
                            </Col>
                          )}
                        </>
                      );
                    }}
                  </Form.List>
                </Row>
              </GiuongNguoiNhaDiv>
            </>
          )}
        </FormWraper>
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalThemMoiPhongGiuong);
