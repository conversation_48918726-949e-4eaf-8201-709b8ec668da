import styled from "styled-components";

export const Main = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.45);
  z-index: 1000;
  .nb-dvkt-base-search__addition {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    margin-left: 8px;
    font-size: 14px;
    .ant-checkbox + span {
      font-weight: 600;
    }
  }
  .wrapper {
    position: relative;
    width: 100%;
    height: calc(100% - 15px);
    top: 15px;
    background-color: white;
    z-index: 80;
    display: flex;
    flex-direction: column;

    .header {
      h1 {
        font-size: 18px;
        font-weight: 600;
        margin: 0;
        line-height: normal;
      }
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid rgba(23, 43, 77, 0.1);
      padding: 10px;
      .icon-close {
        cursor: pointer;
      }
    }
    .content {
      flex: 1;
      padding: 0 5px;
      overflow: auto;
      display: flex;
      flex-direction: column;
      .danh-sach {
        padding-top: 10px;
        flex: 1;
        position: relative;
      }
      .group-header {
        display: flex;
        background: #0549b9;
        color: white;
        padding: 6px 10px 6px 10px;
        border-radius: 4px 4px 0 0;
        align-items: center;
        &__item {
          display: flex;
          padding: 0 5px;
        }
      }
      .group-header {
        &-level2 {
          display: flex;
          background: rgba(233, 233, 233, 1);
          &__content {
            background: #f3f4f6;
            padding: 8px;
            width: 100%;
          }
          .title {
            padding: 8px;
            font-weight: 600;
          }
        }
        &-level3 {
          background: rgba(233, 233, 233, 1);
          &__content {
            background: #c1d8fd;
            padding: 8px;
          }
        }
        &-level4 {
          background: rgba(233, 233, 233, 1);
          &__content {
            display: flex;
            overflow: auto;
            box-shadow: -1px 0px 0px 0px #c1d8fd, 1px 0px 0px 0px #c1d8fd;
          }
          .group-header__item {
            flex-basis: var(--basis, 50px);
            flex-grow: var(--grow, 0);
            flex-shrink: 0;
            padding: 8px 4px;
            box-shadow: 1px 0px 0px 0px rgba(15, 23, 42, 0.1);
            border-bottom: 1px solid rgba(15, 23, 42, 0.1);
            background: #f6f6f6;
            border-right: 1px solid #f0f0f0;
            display: flex;
            justify-content: center;
            &:last-child {
              border-right: none;
            }
          }
        }
        &-level5 {
          background: rgba(233, 233, 233, 1);

          &__content {
            box-shadow: -1px 0px 0px 0px #c1d8fd, 1px 0px 0px 0px #c1d8fd;

            &:hover {
              .group-header__item,
              .row-detail {
                background: #c1f0db !important;
              }
            }
            .group-header__item,
            .row-detail {
              &.odd-row {
                background: #e6effe;
              }
              &.even-row {
                background: #ffffff;
              }
            }
            .row {
              display: flex;
              cursor: pointer;
              .group-header__item {
                flex-basis: var(--basis, 50px);
                flex-grow: var(--grow, 0);
                flex-shrink: 0;
                padding: 6px 8px;
                border-right: 1px solid #f0f0f0;
                &:last-child {
                  border-right: none;
                }
                &--checkbox,
                &--stt,
                &--so-luong,
                &--cap-cuu {
                  display: flex;
                  justify-content: center;
                }
                &.text-color-red {
                  color: red;
                }
              }
            }
            .row-detail {
              border-top: 1px solid #f0f0f0;
            }
          }
        }
      }
    }
    .uppercase {
      text-transform: uppercase;
    }
    .fw-600 {
      font-weight: 600;
    }
    .wrap {
      &-level1 {
        background: rgba(233, 233, 233, 1);
        width: 100%;
        padding: 0 8px;
      }
      &-level2 {
        background: #f3f4f6;
        width: 100%;
        padding: 0 8px;
      }
    }
    .endline {
      height: 8px;
      border-bottom: 1px solid rgba(15, 23, 42, 0.1);
    }
    .border-bottom-main {
      border-bottom: 1px solid #c1d8fd;
      overflow: hidden;
    }
    .group-border-lr {
      border-left: 1px solid #0549b9;
      border-right: 1px solid #0549b9;
    }
    .group-border-bottom {
      border-bottom: 1px solid #0549b9;
    }
    .loading-container {
      width: 100%;
      height: 100%;
      position: absolute;
      .loading {
        position: fixed;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        z-index: 10;
        .ant-spin-text {
          color: red;
          font-weight: bold;
          font-size: 16px;
        }
      }
      .loading-mask {
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 9;
        opacity: 0.3;
        transition: opacity 0.3s;
        background: #f5f5f5;
      }
    }
  }
  .table-expand-icon {
    display: inline-flex;
    float: none;
    vertical-align: sub;
    color: #1890ff;
    outline: none;
    cursor: pointer;
    transition: color 0.3s;
    position: relative;
    float: left;
    box-sizing: border-box;
    width: 17px;
    height: 17px;
    padding: 0;
    color: inherit;
    line-height: 17px;
    background: #fff;
    border: 1px solid #f0f0f0;
    border-radius: 2px;
    transform: scale(0.94117647);
    transition: all 0.3s;
    user-select: none;

    &::before {
      top: 7px;
      right: 3px;
      left: 3px;
      height: 1px;
      position: absolute;
      background: currentcolor;
      transition: transform 0.3s ease-out;
      content: "";
    }
    &::after {
      top: 3px;
      bottom: 3px;
      left: 7px;
      width: 1px;
      transform: rotate(90deg);
      position: absolute;
      background: currentcolor;
      transition: transform 0.3s ease-out;
      content: "";
    }
    &-collapsed {
      &::before {
        transform: rotate(-180deg);
      }
      &::after {
        transform: rotate(0deg);
      }
    }
    &:hover {
      color: #40a9ff;
      border-color: currentColor;
    }
  }
`;
