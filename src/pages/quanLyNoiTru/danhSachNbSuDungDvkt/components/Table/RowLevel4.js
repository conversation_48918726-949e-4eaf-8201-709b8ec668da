import React, { useContext, useEffect, useRef } from "react";
import classNames from "classnames";
import { t } from "i18next";
import { LOAI_DICH_VU } from "constants";
import { flexConfig } from "../../constant";
import { Checkbox } from "components";
import { DanhSachContext } from "../../context";

const className = "group-header__item fw-600 text-center";

const RowLevel4 = ({ record }) => {
  const ref = useRef(null);
  const {
    getGroupKey,
    onSelectAllGroup,
    isSelectedAllGroup,
    isIndeterminateGroup,
    showCheckBox,
  } = useContext(DanhSachContext);
  //clear eventlistener gây lỗi trong Th này, chrome có thể bỏ qua
  useEffect(() => {
    const handleScroll = (e) => {
      if ((e.shiftKey && e.deltaY !== 0) || e.deltaX !== 0) e.preventDefault();
    };
    ref.current.addEventListener("wheel", handleScroll, { passive: false });
  }, []);

  const columnXetNghiem = (
    <>
      <div
        style={flexConfig.stt}
        className={classNames(className, "sticky sticky-left")}
      >
        STT
      </div>
      <div
        style={flexConfig.tenDichVu}
        className={classNames(className, "sticky sticky-left sticky-left-last")}
      >
        {t("quanLyNoiTru.tenPhieuDinhLuong")}
      </div>
      <div style={flexConfig.soLuong} className={className}>
        {t("common.soLuong")}
      </div>
      <div style={flexConfig.ketQua} className={className}>
        {t("common.ketQua")}
      </div>
      <div style={flexConfig.donViTinh} className={className}>
        {t("quanLyNoiTru.toDieuTri.dvt")}
      </div>
      <div style={flexConfig.ketQuaThamChieu} className={className}>
        {t("xetNghiem.giaTriThamChieu")}
      </div>
      <div style={flexConfig.ketLuan} className={className}>
        {t("common.ketLuan")}
      </div>
      <div style={flexConfig.tenPhongThucHien} className={className}>
        {t("thuNgan.phongThucHien")}
      </div>
      <div style={flexConfig.bacSiChiDinh} className={className}>
        {t("xetNghiem.bsChiDinh")}
      </div>
      <div style={flexConfig.trangThai} className={className}>
        {t("common.trangThai")}
      </div>
      <div style={flexConfig.capCuu} className={className}>
        {t("pttt.capCuu")}
      </div>
      <div style={flexConfig.thoiGian} className={className}>
        {t("cdha.thoiGianChiDinh")}
      </div>
      <div style={flexConfig.thoiGian} className={className}>
        {t("common.thoiGianThucHien")}
      </div>
      <div style={flexConfig.thoiGian} className={className}>
        {t("xetNghiem.thoiGianCoKetQua")}
      </div>
      <div style={flexConfig.canhBao} className={className}>
        {t("quanLyNoiTru.canhBaoBatThuong")}
      </div>
      <div style={flexConfig.thoiGianXemKetQua} className={className}>
        {t("quanLyNoiTru.thoiGianXemKq")}
      </div>
      <div style={flexConfig.tenNguoiXemKetQua} className={className}>
        {t("quanLyNoiTru.nguoiXemKq")}
      </div>
      <div style={flexConfig.tenBenhPham} className={className}>
        {t("quanLyNoiTru.mauBenhPham")}
      </div>
      <div
        style={flexConfig.tienIch}
        className={classNames(className, "sticky sticky-right")}
      >
        {t("common.tienIch")}
      </div>
    </>
  );

  const columnCdha = (
    <>
      <div
        style={flexConfig.stt}
        className={classNames(className, "sticky sticky-left")}
      >
        {t("common.stt")}
      </div>
      <div
        style={flexConfig.tenDichVu}
        className={classNames(className, "sticky sticky-left sticky-left-last")}
      >
        {t("common.tenDichVu")}
      </div>
      <div style={flexConfig.soLuong} className={className}>
        {t("common.soLuong")}
      </div>
      <div style={flexConfig.ketQua} className={className}>
        {t("common.ketQua")}
      </div>
      <div style={flexConfig.tenKhoaChiDinh} className={className}>
        {t("baoCao.khoaChiDinh")}
      </div>
      <div style={flexConfig.tenKhoaThucHien} className={className}>
        {t("baoCao.khoaThucHien")}
      </div>
      <div style={flexConfig.tenPhongThucHien} className={className}>
        {t("baoCao.phongThucHien")}
      </div>
      <div style={flexConfig.bacSiChiDinh} className={className}>
        {t("xetNghiem.bsChiDinh")}
      </div>
      <div style={flexConfig.trangThai} className={className}>
        {t("common.trangThai")}
      </div>
      <div style={flexConfig.capCuu} className={className}>
        {t("pttt.capCuu")}
      </div>
      <div style={flexConfig.thoiGian} className={className}>
        {t("cdha.thoiGianChiDinh")}
      </div>
      <div style={flexConfig.thoiGian} className={className}>
        {t("common.thoiGianThucHien")}
      </div>
      <div style={flexConfig.thoiGian} className={className}>
        {t("cdha.thoiGianCoKetQua")}
      </div>
      <div style={flexConfig.canhBao} className={className}>
        {t("quanLyNoiTru.canhBaoBatThuong")}
      </div>
      <div style={flexConfig.thoiGianXemKetQua} className={className}>
        {t("quanLyNoiTru.thoiGianXemKq")}
      </div>
      <div style={flexConfig.tenNguoiXemKetQua} className={className}>
        {t("quanLyNoiTru.nguoiXemKq")}
      </div>
      <div
        style={flexConfig.tienIch}
        className={classNames(className, "sticky sticky-right")}
      >
        {t("common.tienIch")}
      </div>
    </>
  );

  const columnPttt = (
    <>
      <div
        style={flexConfig.stt}
        className={classNames(className, "sticky sticky-left")}
      >
        {t("common.stt")}
      </div>
      <div
        style={flexConfig.tenDichVu}
        className={classNames(className, "sticky sticky-left sticky-left-last")}
      >
        {t("common.tenDichVu")}
      </div>
      <div style={flexConfig.soLuong} className={className}>
        {t("common.soLuong")}
      </div>
      <div style={flexConfig.loaiPttt} className={className}>
        {t("quanLyNoiTru.loaiHinhPt")}
      </div>
      <div style={flexConfig.phauThuatVien} className={className}>
        {t("quanLyNoiTru.PtVien")}
      </div>
      <div style={flexConfig.cdSauPt} className={className}>
        {t("quanLyNoiTru.chanDoanTruocPt")}
      </div>
      <div style={flexConfig.cdSauPt} className={className}>
        {t("quanLyNoiTru.chanDoanSauPt")}
      </div>
      <div style={flexConfig.cachThuc} className={className}>
        {t("quanLyNoiTru.cachThuc")}
      </div>
      <div style={flexConfig.phuongPhapPhauThuat} className={className}>
        {t("quanLyNoiTru.chiSoSong.phuongPhapPhauThuat")}
      </div>
      <div style={flexConfig.tenPhongThucHien} className={className}>
        {t("quanLyNoiTru.phongThucHien")}
      </div>
      <div style={flexConfig.bacSiChiDinh} className={className}>
        {t("xetNghiem.bsChiDinh")}
      </div>
      <div style={flexConfig.trangThai} className={className}>
        {t("common.trangThai")}
      </div>
      <div style={flexConfig.thoiGian} className={className}>
        {t("cdha.thoiGianChiDinh")}
      </div>
      <div style={flexConfig.thoiGian} className={className}>
        {t("common.thoiGianThucHien")}
      </div>
      <div style={flexConfig.thoiGian} className={className}>
        {t("cdha.thoiGianCoKetQua")}
      </div>
      <div style={flexConfig.canhBao} className={className}>
        {t("quanLyNoiTru.canhBaoBatThuong")}
      </div>
      <div style={flexConfig.thoiGianXemKetQua} className={className}>
        {t("quanLyNoiTru.thoiGianXemKq")}
      </div>
      <div style={flexConfig.tenNguoiXemKetQua} className={className}>
        {t("quanLyNoiTru.nguoiXemKq")}
      </div>
      <div
        style={flexConfig.tienIch}
        className={classNames(className, "sticky sticky-right")}
      >
        {t("common.tienIch")}
      </div>
    </>
  );

  const columns =
    record.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM
      ? columnXetNghiem
      : record.loaiDichVu === LOAI_DICH_VU.CDHA
      ? columnCdha
      : columnPttt;

  return (
    <div className={classNames("group-header-level4 group-border-lr")}>
      <div className="wrap-level1">
        <div className="wrap-level2">
          <div
            ref={(el) => {
              ref.current = el;
              if (el && el.clientWidth < el.scrollWidth) {
                el.classList.add("scrollable");
              }
            }}
            className="group-header-level4__content inner-scroll"
          >
            {showCheckBox(record) && (
              <div
                style={flexConfig.checkbox}
                className={classNames(className, "sticky sticky-left")}
              >
                <Checkbox
                  checked={isSelectedAllGroup(getGroupKey(record))}
                  indeterminate={isIndeterminateGroup(getGroupKey(record))}
                  onChange={(e) =>
                    onSelectAllGroup(getGroupKey(record), e.target.checked)
                  }
                />
              </div>
            )}
            {columns}
          </div>
        </div>
      </div>
    </div>
  );
};
export default RowLevel4;
