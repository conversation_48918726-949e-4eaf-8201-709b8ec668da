import React, { useContext } from "react";
import moment from "moment";
import { t } from "i18next";
import classNames from "classnames";
import { checkOpenFc } from "../../helpers";
import { DanhSachContext } from "../../context";
import { RowLevel1Styled } from "./styled";
import { Tooltip } from "components";
import { SVG } from "assets";
import { LOAI_DICH_VU } from "constants/index";

const RowLevel1 = ({ record }) => {
  const key = `${record.rootId}-${record.nhomDichVuCap1Id}`;
  const {
    treeStatus,
    setTreeStatus,
    rowDetailExpand,
    setRowDetailExpand,
    showCheckBox,
    onXemKetQua,
    onHuyXemKetQua,
    listAllDsDichVu,
    getGroupPrefixKey,
  } = useContext(DanhSachContext);

  // Hàm toggle expand/collapse tất cả chỉ số con của các dịch vụ xét nghiệm trong nhóm này
  const handleToggleAllChiSoCon = () => {
    // Lấy prefix key cho nhóm dịch vụ cấp 1 này
    const prefixKey = getGroupPrefixKey(record);

    // Lọc các dịch vụ xét nghiệm có dsChiSoCon thuộc nhóm này
    const dichVuXetNghiemCoChiSoCon = (listAllDsDichVu || []).filter((dv) => {
      const dvPrefixKey = getGroupPrefixKey(dv);
      return (
        dvPrefixKey === prefixKey &&
        dv.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM &&
        dv.dsChiSoCon?.length > 0
      );
    });

    if (dichVuXetNghiemCoChiSoCon.length === 0) return;

    // Kiểm tra xem có bất kỳ chỉ số con nào đang được expand không
    const hasAnyExpanded = dichVuXetNghiemCoChiSoCon.some((dv) => {
      const rowKey = `${dv.rootId}-${dv.nhomDichVuCap1Id}-${dv.nhomDichVuCap2Id}-${dv.soPhieu}-${dv.index}`;
      return rowDetailExpand[rowKey]?.showChiSoCon;
    });

    // Tạo object để update trạng thái expand cho tất cả chỉ số con
    const newRowDetailExpand = { ...rowDetailExpand };

    dichVuXetNghiemCoChiSoCon.forEach((dv) => {
      const rowKey = `${dv.rootId}-${dv.nhomDichVuCap1Id}-${dv.nhomDichVuCap2Id}-${dv.soPhieu}-${dv.index}`;
      newRowDetailExpand[rowKey] = {
        ...newRowDetailExpand[rowKey],
        showChiSoCon: !hasAnyExpanded, // Toggle: nếu có cái nào expand thì collapse tất cả, ngược lại expand tất cả
      };
    });

    setRowDetailExpand(newRowDetailExpand);
  };

  // Kiểm tra xem có chỉ số con nào đang được expand không để hiển thị icon phù hợp
  const hasExpandedChiSoCon = (() => {
    const prefixKey = getGroupPrefixKey(record);

    return (listAllDsDichVu || []).some((dv) => {
      const dvPrefixKey = getGroupPrefixKey(dv);
      if (
        dvPrefixKey !== prefixKey ||
        dv.loaiDichVu !== LOAI_DICH_VU.XET_NGHIEM ||
        !dv.dsChiSoCon?.length
      ) {
        return false;
      }
      const rowKey = `${dv.rootId}-${dv.nhomDichVuCap1Id}-${dv.nhomDichVuCap2Id}-${dv.soPhieu}-${dv.index}`;
      return rowDetailExpand[rowKey]?.showChiSoCon;
    });
  })();

  return (
    <RowLevel1Styled className="group-header-level2 group-border-lr flex align-items-center">
      <button
        className={classNames("table-expand-icon", {
          "table-expand-icon-collapsed": checkOpenFc(treeStatus[key]?.open),
        })}
        onClick={() => {
          setTreeStatus((prev) => ({
            ...prev,
            [key]: {
              id: record.rootId,
              children: {
                id: record.nhomDichVuCap1Id,
              },
              open: checkOpenFc(prev[key]?.open),
            },
          }));
        }}
      />
      <div className="title uppercase">{record.tenNhomDichVuCap1}</div>
      <div className="title">
        [
        {record.thoiGianThucHien &&
          moment(record.thoiGianThucHien).format("DD/MM/YYYY")}{" "}
        - {record.tenKhoaChiDinh}]
      </div>
      {/* Nút toggle expand/collapse tất cả chỉ số con cho dịch vụ xét nghiệm */}
      {(() => {
        const prefixKey = getGroupPrefixKey(record);
        return (listAllDsDichVu || []).some((dv) => {
          const dvPrefixKey = getGroupPrefixKey(dv);
          return (
            dvPrefixKey === prefixKey &&
            dv.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM &&
            dv.dsChiSoCon?.length > 0
          );
        });
      })() && (
        <Tooltip
          title={
            hasExpandedChiSoCon
              ? "Thu gọn tất cả chỉ số con xét nghiệm"
              : "Mở rộng tất cả chỉ số con xét nghiệm"
          }
          placement="bottom"
        >
          <div
            className="cursor-pointer flex align-items-center gap-4 ml-8"
            onClick={handleToggleAllChiSoCon}
            style={{
              padding: "4px 8px",
              borderRadius: "4px",
              backgroundColor: hasExpandedChiSoCon
                ? "var(--color-warning-light-1)"
                : "var(--color-primary-light-1)",
              color: hasExpandedChiSoCon
                ? "var(--color-warning)"
                : "var(--color-primary)",
              fontSize: "12px",
              fontWeight: 500,
            }}
          >
            {hasExpandedChiSoCon ? (
              <SVG.IcArrowUp style={{ width: 12, height: 12 }} />
            ) : (
              <SVG.IcArrowDown style={{ width: 12, height: 12 }} />
            )}
            <span>{hasExpandedChiSoCon ? "Thu gọn XN" : "Mở rộng XN"}</span>
          </div>
        </Tooltip>
      )}
      {showCheckBox(record) && (
        <div className="flex gap-8">
          <Tooltip title={t("khamBenh.xacNhanXemKetQua")} placement="bottom">
            <SVG.IcTick
              style={{
                width: 20,
                height: 20,
              }}
              className="cursor-pointer"
              onClick={onXemKetQua(record)}
            />
          </Tooltip>
          <Tooltip title={t("common.huyXacNhanXemKetQua")} placement="bottom">
            <SVG.IcCloseCircle
              style={{
                width: 20,
                height: 20,
              }}
              className="cursor-pointer"
              color={"var(--color-red-primary)"}
              onClick={onHuyXemKetQua(record)}
            />
          </Tooltip>
        </div>
      )}
    </RowLevel1Styled>
  );
};

export default RowLevel1;
