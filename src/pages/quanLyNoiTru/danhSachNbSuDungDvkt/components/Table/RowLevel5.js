import React, { useContext, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import classNames from "classnames";
import moment from "moment";
import { Checkbox, EllipsisText, Tooltip } from "components";
import {
  LOAI_DICH_VU,
  LOAI_IN,
  ROLES,
  TRANG_THAI_DICH_VU,
} from "constants/index";
import printProvider from "data-access/print-provider";
import { isArray, locPhieuLisPacs, openInNewTab } from "utils";
import { useLoading } from "hooks";
import { DanhSachContext } from "../../context";
import { SVG } from "assets";
import { checkOpenFc } from "../../helpers";
import { flexConfig } from "../../constant";
import { parseTextToHtml } from "components/editor/cores/ToDieuTri/DanhSachToDieuTri/ultils";
import { RowLevel5Styled } from "./styled";
import { checkRole } from "lib-utils/role-utils";

/*
   Nếu có dsChiSoCon thì là chỉ số cha và là dịch vụ xét nghiệm
   Nếu có chiSoConId thì là chỉ số con 
*/

const RowLevel5 = ({ record: data }) => {
  const { t } = useTranslation();
  const {
    rowDetailExpand,
    setRowDetailExpand,
    listTrangThaiDichVuMemo,
    loaiPtttMap,
    refInnerScroll,
    onSelectRow,
    selectedRowKeysByGroup,
    getGroupKey,
    showCheckBox,
  } = useContext(DanhSachContext);
  const { showLoading, hideLoading } = useLoading();

  const {
    pacs: { getUrl },
    choTiepDonDV: { getPhieuKetQua },
    nbXetNghiem: { getKetQuaXNPdf },
    chiDinhKhamBenh: { xemKetQua, huyXemKetQua },
    nbDichVuKyThuat: { onChangeInputSearch },
  } = useDispatch();

  const refScroll = useRef(null);
  const rowKey = `${data.rootId}-${data.nhomDichVuCap1Id}-${data.nhomDichVuCap2Id}-${data.soPhieu}-${data.index}`;

  const showDetailChiSoCon = checkOpenFc(rowDetailExpand[rowKey]?.showChiSoCon);

  const handleShowChiSoCon = () => {
    setRowDetailExpand((prev) => ({
      ...prev,
      [rowKey]: {
        ...prev[rowKey],
        showChiSoCon: checkOpenFc(prev[rowKey]?.showChiSoCon),
      },
    }));
  };

  //clear eventlistener gây lỗi trong Th này, chrome có thể bỏ qua
  useEffect(() => {
    const handleScroll = (e) => {
      if ((e.shiftKey && e.deltaY !== 0) || e.deltaX !== 0) e.preventDefault();
    };
    refScroll.current.addEventListener("wheel", handleScroll, {
      passive: false,
    });
  }, []);

  const onPrintPdf = async (s) => {
    const dsPhieu = locPhieuLisPacs(s, {
      allData: false,
      isLocPhieu: true,
      isCdha: true,
    });
    const dsPhieuFull = locPhieuLisPacs(s, {
      allData: true,
      isLocPhieu: true,
      isCdha: true,
    });
    if (
      isArray(dsPhieuFull, true) &&
      dsPhieuFull.every((x) => x?.loaiIn == LOAI_IN.MO_TAB)
    ) {
      // [].every luôn luôn true
      const finalFile = await printProvider.getMergePdf(dsPhieu);
      openInNewTab(finalFile);
    } else {
      printProvider.printPdf(dsPhieuFull);
    }
  };

  const onViewPdf = (data) => async () => {
    if (
      [
        TRANG_THAI_DICH_VU.DA_CO_KET_QUA,
        TRANG_THAI_DICH_VU.DA_HOAN_TAT_DICH_VU,
      ].includes(data?.trangThai)
    ) {
      try {
        showLoading();
        const getResult =
          data?.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM
            ? getKetQuaXNPdf
            : getPhieuKetQua;

        const s = await getResult({
          nbDotDieuTriId: data?.nbDotDieuTriId,
          dsSoKetNoi: [data?.soKetNoi],
          dsSoPhieu: data?.soPhieu,
        });
        onPrintPdf(s);
      } catch (error) {
      } finally {
        hideLoading();
      }
    }
  };

  const onViewPacs = (data) => async () => {
    if (data?.guiPacs) {
      try {
        showLoading();
        const s = await getUrl({ id: data?.id });
        if (s) window.open(s, "_blank").focus();
      } catch (error) {
      } finally {
        hideLoading();
      }
    }
  };

  const renderActionButton = (type, data) => {
    switch (type) {
      case "viewPdf":
        return (
          <Tooltip title={t("cdha.xemKetQuaPdf")}>
            <SVG.IcPdf
              className={classNames("ic-action", {
                disabled: ![
                  TRANG_THAI_DICH_VU.DA_CO_KET_QUA,
                  TRANG_THAI_DICH_VU.DA_HOAN_TAT_DICH_VU,
                ].includes(data?.trangThai),
              })}
              onClick={onViewPdf(data)}
            />
          </Tooltip>
        );
      case "viewPacs":
        return (
          <Tooltip title={t("cdha.xemKetQuaPacs")}>
            <SVG.IcViewImagePacs
              className={classNames("ic-action", {
                disabled: !data.guiPacs,
              })}
              onClick={onViewPacs(data)}
            />
          </Tooltip>
        );
    }
  };

  const onXemKetQua = (record) => async () => {
    try {
      showLoading();
      let s = await xemKetQua({
        payload: [record.id],
        loaiDichVu: record.loaiDichVu,
      });

      if (s.code === 0) {
        onChangeInputSearch({});
      }
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onHuyXemKetQua = (record) => async () => {
    try {
      showLoading();

      const s = await huyXemKetQua({
        payload: [record.id],
        loaiDichVu: record.loaiDichVu,
      });

      if (s.code === 0) {
        onChangeInputSearch({});
      }
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const columnXetNghiem = [
    {
      className: "group-header__item--stt sticky sticky-left",
      style: flexConfig.stt,
      key: "index",
    },
    {
      className:
        "group-header__item--ten-phieu sticky sticky-left sticky-left-last",
      style: flexConfig.tenDichVu,
      key: "tenDichVu",
      render: (item, record) => (
        <div className="flex gap-8 row-detail__header flex1">
          <EllipsisText.Tooltip
            content={record?.tenChiSoCon ? record.tenChiSoCon : item}
            limitLine={3}
          />
          {/* arrow show chỉ số con */}
          {!!record?.dsChiSoCon?.length && (
            <div
              className={classNames("icon-expand", {
                expand: showDetailChiSoCon,
              })}
              onClick={handleShowChiSoCon}
            >
              <SVG.IcArrowDown />
            </div>
          )}
        </div>
      ),
    },
    {
      className: "group-header__item--so-luong",
      style: flexConfig.soLuong,
      key: "soLuong",
    },
    {
      className: "group-header__item--ket-qua",
      style: flexConfig.ketQua,
      key: "ketQua",
      render: (item) => <EllipsisText.Tooltip content={item} limitLine={3} />,
    },
    {
      className: "group-header__item--don-vi-tinh",
      style: flexConfig.donViTinh,
      key: "tenDonViTinh",
    },
    {
      className: "group-header__item--gia-tri-tham-chieu",
      style: flexConfig.ketQuaThamChieu,
      key: "ketQuaThamChieu",
    },
    {
      className: "group-header__item--ket-luan",
      style: flexConfig.ketLuan,
      key: "ketLuan",
      render: (item) => (
        <EllipsisText.Tooltip content={parseTextToHtml(item)} limitLine={3} />
      ),
    },
    {
      className: "group-header__item--phong-thuc-hien",
      style: flexConfig.tenPhongThucHien,
      key: "tenPhongThucHien",
    },
    {
      className: "group-header__item--bac-si-chi-dinh",
      style: flexConfig.bacSiChiDinh,
      key: "vietTatHhhvBsChiDinh",
      render: (_, record) =>
        !record?.chiSoConId && (
          <>
            {record.vietTatHhhvBsChiDinh}
            {record.tenBacSiChiDinh}
          </>
        ),
    },
    {
      className: "group-header__item--trang-thai",
      style: flexConfig.trangThai,
      key: "trangThai",
      render: (item, record) =>
        !record?.chiSoConId && (
          <EllipsisText.Tooltip
            content={listTrangThaiDichVuMemo[item]}
            limitLine={3}
          />
        ),
    },
    {
      className: "group-header__item--cap-cuu",
      style: flexConfig.capCuu,
      key: "capCuu",
      render: (item, record) =>
        !record?.chiSoConId && <Checkbox checked={item} />,
    },
    {
      className: "group-header__item--thoi-gian-chi-dinh",
      style: flexConfig.thoiGian,
      key: "thoiGianChiDinh",
      render: (item) => {
        return item && moment(item).format("DD/MM/YYYY HH:mm:ss");
      },
    },
    {
      className: "group-header__item--thoi-gian-chi-dinh",
      style: flexConfig.thoiGian,
      key: "thoiGianThucHien",
      render: (item) => {
        return item && moment(item).format("DD/MM/YYYY HH:mm:ss");
      },
    },
    {
      className: "group-header__item--thoi-gian-co-ket-qua",
      style: flexConfig.thoiGian,
      key: "thoiGianCoKetQua",
      render: (item) => {
        return item && moment(item).format("DD/MM/YYYY HH:mm:ss");
      },
    },
    {
      className: "group-header__item--canh-bao text-color-red",
      style: flexConfig.canhBao,
      key: "canhBao",
      render: (item) => {
        return <EllipsisText.Tooltip content={item} limitLine={3} />;
      },
    },
    {
      className: "group-header__item--thoi-gian-xem-kq",
      style: flexConfig.thoiGianXemKetQua,
      key: "thoiGianXemKetQua",
      render: (item) => {
        return item && moment(item).format("DD/MM/YYYY HH:mm:ss");
      },
    },
    {
      className: "group-header__item--nguoi-xem-kq",
      style: flexConfig.tenNguoiXemKetQua,
      key: "tenNguoiXemKetQua",
    },
    {
      className: "group-header__item--ten-benh-pham",
      style: flexConfig.tenBenhPham,
      key: "tenBenhPham",
    },
    {
      className: "group-header__item--tien-ich sticky sticky-right",
      style: flexConfig.tienIch,
      key: "tienIch",
      render: (_, record) => {
        return (
          !record?.chiSoConId && (
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                width: "100%",
                gap: "3px",
              }}
            >
              {renderActionButton("viewPdf", record)}
              {[
                TRANG_THAI_DICH_VU.DA_CO_KET_QUA,
                TRANG_THAI_DICH_VU.DA_DUYET,
              ]?.includes(record.trangThai) &&
                record.loaiDichVu == LOAI_DICH_VU.XET_NGHIEM &&
                checkRole([ROLES["KHAM_BENH"].XEM_HUY_KET_QUA_XN]) && (
                  <Tooltip
                    title={t("common.xacNhanXemKetQua")}
                    placement="bottom"
                  >
                    <SVG.IcTick
                      color={"var(--color-green-primary)"}
                      className="ic-action"
                      onClick={onXemKetQua(record)}
                    />
                  </Tooltip>
                )}
              {[TRANG_THAI_DICH_VU.DA_HOAN_TAT_DICH_VU]?.includes(
                record.trangThai
              ) &&
                checkRole([ROLES["KHAM_BENH"].XEM_HUY_KET_QUA_XN]) && (
                  <Tooltip
                    title={t("common.huyXacNhanXemKetQua")}
                    placement="bottom"
                  >
                    <SVG.IcCloseCircle
                      color={"var(--color-red-primary)"}
                      className="ic-action"
                      onClick={onHuyXemKetQua(record)}
                    />
                  </Tooltip>
                )}
            </div>
          )
        );
      },
    },
  ];

  const columnCDHA = [
    {
      className: "group-header__item--stt sticky sticky-left",
      style: flexConfig.stt,
      key: "index",
    },
    {
      className:
        "group-header__item--ten-phieu sticky sticky-left sticky-left-last",
      key: "tenDichVu",
      style: flexConfig.tenDichVu,
      render: (item, record) => (
        <EllipsisText.Tooltip
          content={record?.chiSoConId ? record.tenChiSoCon : item}
          limitLine={3}
        />
      ),
    },
    {
      className: "group-header__item--so-luong",
      style: flexConfig.soLuong,
      key: "soLuong",
    },
    {
      className: "group-header__item--ket-qua",
      style: flexConfig.ketQua,
      key: "ketQua",
      render: (item) => (
        <EllipsisText.Tooltip content={parseTextToHtml(item)} limitLine={3} />
      ),
    },
    {
      className: "group-header__item--khoa-chi-dinh",
      style: flexConfig.tenKhoaChiDinh,
      key: "tenKhoaChiDinh",
    },
    {
      className: "group-header__item--khoa-thuc-hien",
      style: flexConfig.tenKhoaThucHien,
      key: "tenKhoaThucHien",
    },
    {
      className: "group-header__item--phong-thuc-hien",
      style: flexConfig.tenPhongThucHien,
      key: "tenPhongThucHien",
    },
    {
      className: "group-header__item--bac-si-chi-dinh",
      style: flexConfig.bacSiChiDinh,
      key: "vietTatHhhvBsChiDinh",
      render: (_, record) =>
        !record?.chiSoConId && (
          <>
            {record.vietTatHhhvBsChiDinh}
            {record.tenBacSiChiDinh}
          </>
        ),
    },

    {
      className: "group-header__item--trang-thai",
      style: flexConfig.trangThai,
      key: "trangThai",
      render: (item, record) =>
        !record?.chiSoConId && (
          <EllipsisText.Tooltip
            content={listTrangThaiDichVuMemo[item]}
            limitLine={3}
          />
        ),
    },
    {
      className: "group-header__item--cap-cuu",
      style: flexConfig.capCuu,
      key: "capCuu",
      render: (item, record) =>
        !record?.chiSoConId && <Checkbox checked={item} />,
    },
    {
      className: "group-header__item--thoi-gian-chi-dinh",
      style: flexConfig.thoiGian,
      key: "thoiGianChiDinh",
      render: (item) => {
        return item && moment(item).format("DD/MM/YYYY HH:mm:ss");
      },
    },
    {
      className: "group-header__item--thoi-gian-chi-dinh",
      style: flexConfig.thoiGian,
      key: "thoiGianThucHien",
      render: (item) => {
        return item && moment(item).format("DD/MM/YYYY HH:mm:ss");
      },
    },
    {
      className: "group-header__item--thoi-gian-co-ket-qua",
      style: flexConfig.thoiGian,
      key: "thoiGianCoKetQua",
      render: (item) => {
        return item && moment(item).format("DD/MM/YYYY HH:mm:ss");
      },
    },
    {
      className: "group-header__item--canh-bao text-color-red",
      style: flexConfig.canhBao,
      key: "canhBao",
      render: (item) => {
        return <EllipsisText.Tooltip content={item} limitLine={3} />;
      },
    },
    {
      className: "group-header__item--thoi-gian-xem-kq",
      style: flexConfig.thoiGianXemKetQua,
      key: "thoiGianXemKetQua",
      render: (item) => {
        return item && moment(item).format("DD/MM/YYYY HH:mm:ss");
      },
    },
    {
      className: "group-header__item--nguoi-xem-kq",
      style: flexConfig.tenNguoiXemKetQua,
      key: "tenNguoiXemKetQua",
    },
    {
      className: "group-header__item--tien-ich sticky sticky-right",
      style: flexConfig.tienIch,
      key: "tienIch",
      render: (_, record) => {
        return (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              width: "100%",
            }}
          >
            {renderActionButton("viewPacs", record)}
            {renderActionButton("viewPdf", record)}
            {[
              TRANG_THAI_DICH_VU.DA_CO_KET_QUA,
              TRANG_THAI_DICH_VU.DA_DUYET,
            ]?.includes(record.trangThai) &&
              checkRole([ROLES["KHAM_BENH"].XEM_HUY_KET_QUA_CDHA]) && (
                <Tooltip
                  title={t("common.xacNhanXemKetQua")}
                  placement="bottom"
                >
                  <SVG.IcTick
                    color={"var(--color-green-primary)"}
                    className="ic-action"
                    onClick={onXemKetQua(record)}
                  />
                </Tooltip>
              )}
            {[TRANG_THAI_DICH_VU.DA_HOAN_TAT_DICH_VU]?.includes(
              record.trangThai
            ) &&
              checkRole([ROLES["KHAM_BENH"].XEM_HUY_KET_QUA_CDHA]) && (
                <Tooltip
                  title={t("common.huyXacNhanXemKetQua")}
                  placement="bottom"
                >
                  <SVG.IcCloseCircle
                    color={"var(--color-red-primary)"}
                    className="ic-action"
                    onClick={onHuyXemKetQua(record)}
                  />
                </Tooltip>
              )}
          </div>
        );
      },
    },
  ];

  const columnPTTT = [
    {
      className: "group-header__item--stt sticky sticky-left",
      style: flexConfig.stt,
      key: "index",
    },
    {
      className:
        "group-header__item--ten-phieu sticky sticky-left sticky-left-last",
      style: flexConfig.tenDichVu,
      key: "tenDichVu",
      render: (item) => (
        <div className="flex gap-8 row-detail__header flex1">
          <EllipsisText.Tooltip content={item} limitLine={3} />
        </div>
      ),
    },
    {
      className: "group-header__item--so-luong",
      style: flexConfig.soLuong,
      key: "soLuong",
    },
    {
      className: "group-header__item--loai-hinh-pttt",
      style: flexConfig.loaiPttt,
      key: "loaiPtTt",
      render: (item) => loaiPtttMap?.get(item) || "",
    },
    {
      className: "group-header__item--phau-thuat-vien",
      style: flexConfig.phauThuatVien,
      key: "tenPtv1",
      render: (item, record) => {
        return [record.tenPtv1, record.tenPtv2].filter(Boolean).join(", ");
      },
    },
    {
      className: "group-header__item--cd-sau-pt",
      style: flexConfig.cdSauPt,
      key: "dsCdChinh",
      render: (item) => {
        return (
          isArray(item, 1) && (
            <EllipsisText.Tooltip
              content={item.map((i) => i.ten).join("; ")}
              limitLine={3}
            />
          )
        );
      },
    },
    {
      className: "group-header__item--cd-sau-pt",
      style: flexConfig.cdSauPt,
      key: "cdSauPt",
      render: (item) => <EllipsisText.Tooltip content={item} limitLine={3} />,
    },
    {
      className: "group-header__item--cach-thuc",
      style: flexConfig.cachThuc,
      key: "cachThuc",
      render: (item) => <EllipsisText.Tooltip content={item} limitLine={3} />,
    },
    {
      className: "group-header__item--cach-thuc",
      style: flexConfig.phuongPhapPhauThuat,
      key: "phuongPhap",
      render: (item) => <EllipsisText.Tooltip content={item} limitLine={3} />,
    },
    {
      className: "group-header__item--phong-thuc-hien",
      style: flexConfig.tenPhongThucHien,
      key: "tenPhongThucHien",
    },
    {
      className: "group-header__item--bac-si-chi-dinh",
      style: flexConfig.bacSiChiDinh,
      key: "vietTatHhhvBsChiDinh",
      render: (_, record) =>
        !record?.chiSoConId && (
          <>
            {record.vietTatHhhvBsChiDinh}
            {record.tenBacSiChiDinh}
          </>
        ),
    },
    {
      className: "group-header__item--trang-thai",
      style: flexConfig.trangThai,
      key: "trangThai",
      render: (item, record) =>
        !record?.chiSoConId && (
          <EllipsisText.Tooltip
            content={listTrangThaiDichVuMemo[item]}
            limitLine={3}
          />
        ),
    },
    {
      className: "group-header__item--thoi-gian-chi-dinh",
      style: flexConfig.thoiGian,
      key: "thoiGianChiDinh",
      render: (item) => {
        return item && moment(item).format("DD/MM/YYYY HH:mm:ss");
      },
    },
    {
      className: "group-header__item--thoi-gian-chi-dinh",
      style: flexConfig.thoiGian,
      key: "thoiGianThucHien",
      render: (item) => {
        return item && moment(item).format("DD/MM/YYYY HH:mm:ss");
      },
    },
    {
      className: "group-header__item--thoi-gian-co-ket-qua",
      style: flexConfig.thoiGian,
      key: "thoiGianCoKetQua",
      render: (item) => {
        return item && moment(item).format("DD/MM/YYYY HH:mm:ss");
      },
    },
    {
      className: "group-header__item--canh-bao text-color-red",
      style: flexConfig.canhBao,
      key: "canhBao",
      render: (item) => {
        return <EllipsisText.Tooltip content={item} limitLine={3} />;
      },
    },
    {
      className: "group-header__item--thoi-gian-xem-kq",
      style: flexConfig.thoiGianXemKetQua,
      key: "thoiGianXemKetQua",
      render: (item) => {
        return item && moment(item).format("DD/MM/YYYY HH:mm:ss");
      },
    },
    {
      className: "group-header__item--nguoi-xem-kq",
      style: flexConfig.tenNguoiXemKetQua,
      key: "tenNguoiXemKetQua",
    },
    {
      className: "group-header__item--tien-ich sticky sticky-right",
      style: flexConfig.tienIch,
      key: "tienIch",
      render: (_, record) => {
        return (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              width: "100%",
            }}
          >
            {[
              TRANG_THAI_DICH_VU.DA_CO_KET_QUA,
              TRANG_THAI_DICH_VU.DA_DUYET,
            ]?.includes(record.trangThai) &&
              checkRole([ROLES["KHAM_BENH"].XEM_HUY_KET_QUA_PTTT]) && (
                <Tooltip
                  title={t("common.xacNhanXemKetQua")}
                  placement="bottom"
                >
                  <SVG.IcTick
                    color={"var(--color-green-primary)"}
                    className="ic-action"
                    onClick={onXemKetQua(record)}
                  />
                </Tooltip>
              )}
            {[TRANG_THAI_DICH_VU.DA_HOAN_TAT_DICH_VU]?.includes(
              record.trangThai
            ) &&
              checkRole([ROLES["KHAM_BENH"].XEM_HUY_KET_QUA_PTTT]) && (
                <Tooltip
                  title={t("common.huyXacNhanXemKetQua")}
                  placement="bottom"
                >
                  <SVG.IcCloseCircle
                    color={"var(--color-red-primary)"}
                    className="ic-action"
                    onClick={onHuyXemKetQua(record)}
                  />
                </Tooltip>
              )}
          </div>
        );
      },
    },
  ];

  const renderColumn =
    data.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM
      ? columnXetNghiem
      : data.loaiDichVu === LOAI_DICH_VU.CDHA
      ? columnCDHA
      : columnPTTT;

  return (
    <RowLevel5Styled
      className={classNames("group-header-level5 group-border-lr")}
    >
      <div className="wrap-level1">
        <div className="wrap-level2">
          <div
            className={classNames("group-header-level5__content", {
              "rounded-b-4": data.className?.["rounded-b-4"],
              "border-bottom-main": data.className?.["border-bottom-main"],
            })}
          >
            <div
              ref={(ref) => {
                refInnerScroll(ref, data.loaiDichVu);
                refScroll.current = ref;
                if (ref && ref.clientWidth < ref.scrollWidth) {
                  ref.classList.add("scrollable");
                }
              }}
              className="row inner-scroll"
            >
              {showCheckBox(data) && (
                <div
                  className={classNames(
                    "group-header__item group-header__item--checkbox sticky sticky-left",
                    {
                      "odd-row": data.className?.["odd-row"],
                      "even-row": data.className?.["even-row"],
                    }
                  )}
                  style={flexConfig.checkbox}
                >
                  <Checkbox
                    checked={(
                      selectedRowKeysByGroup[getGroupKey(data)] || []
                    ).includes(data?.id)}
                    onChange={onSelectRow(getGroupKey(data), data?.id)}
                  />
                </div>
              )}
              {renderColumn.map((column, index) => {
                return (
                  <div
                    key={column.key ?? index}
                    className={classNames(
                      "group-header__item",
                      column.className,
                      {
                        "odd-row": data.className?.["odd-row"],
                        "even-row": data.className?.["even-row"],
                      }
                    )}
                    style={column.style}
                  >
                    {column.render
                      ? column.render(data[column.key], data)
                      : data[column.key]}
                  </div>
                );
              })}
            </div>
            {data.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM &&
              showDetailChiSoCon &&
              data.dsChiSoCon?.map((item) => {
                return (
                  <div
                    className="row inner-scroll"
                    ref={(ref) => {
                      if (ref && ref.clientWidth < ref.scrollWidth) {
                        ref.classList.add("scrollable");
                      }
                    }}
                  >
                    <div
                      className={classNames(
                        "group-header__item group-header__item--checkbox sticky sticky-left",
                        {
                          "odd-row": data.className?.["odd-row"],
                          "even-row": data.className?.["even-row"],
                        }
                      )}
                      style={flexConfig.checkbox}
                    />
                    {renderColumn.map((column, index) => {
                      return (
                        <div
                          key={column.key ?? index}
                          className={classNames(
                            "group-header__item border-t-main",
                            column.className,
                            {
                              "odd-row": data.className?.["odd-row"],
                              "even-row": data.className?.["even-row"],
                              italic: column.key === "tenDichVu",
                            }
                          )}
                          style={column.style}
                        >
                          {column.render
                            ? column.render(item[column.key], item)
                            : item[column.key]}
                        </div>
                      );
                    })}
                  </div>
                );
              })}
          </div>
        </div>
      </div>
    </RowLevel5Styled>
  );
};

export default RowLevel5;
