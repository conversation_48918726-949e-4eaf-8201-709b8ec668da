import { LOAI_DICH_VU } from "constants/index";

const TRANG_THAI_DICH_VU_XET_NGHIEM = [
  {
    value: 25,
    label: "cdha.choTiepNhan",
  },
  {
    value: [38, 46, 62],
    label: "xetNghiem.choLayMau",
  },
  {
    value: 66,
    label: "xetNghiem.daLayMau",
  },
  {
    value: 90,
    label: "xetNghiem.daTiepNhanMau",
  },
  {
    value: 155,
    label: "xetNghiem.daCoKetQua",
  },
  {
    value: 160,
    label: "xetNghiem.daDuyetKetQua",
  },
  {
    value: 80,
    label: "xetNghiem.huyMau",
  },
  {
    value: 50,
    label: "common.boQua",
  },
  {
    value: 170,
    label: "common.daXemKetQua",
  },
];

const TRANG_THAI_DICH_VU_CDHA_TDCN = [
  {
    value: 15,
    label: "xetNghiem.choTiepDonCLS",
  },
  {
    value: [25, 35, 43],
    label: "cdha.choTiep<PERSON>han",
  },
  {
    value: 63,
    label: "cdha.daTiep<PERSON>han",
  },
  {
    value: 155,
    label: "xetNghiem.daCoKetQua",
  },
  {
    value: 50,
    label: "common.boQua",
  },
  {
    value: 170,
    label: "common.daXemKetQua",
  },
];

const TRANG_THAI_DICH_VU_PTTT = [
  {
    value: 25,
    label: "cdha.choTiepNhan",
  },
  {
    value: 63,
    label: "cdha.daTiepNhan",
  },
  {
    value: 95,
    label: "pttt.daChuyenHoiTinh",
  },
  {
    value: 155,
    label: "xetNghiem.daCoKetQua",
  },
  {
    value: 170,
    label: "common.daXemKetQua",
  },
];

export const TRANG_THAI_MAP = {
  [LOAI_DICH_VU.KHAM]: [], // KHÁM
  [LOAI_DICH_VU.XET_NGHIEM]: TRANG_THAI_DICH_VU_XET_NGHIEM, // XET_NGHIEM
  [LOAI_DICH_VU.CDHA]: TRANG_THAI_DICH_VU_CDHA_TDCN, // CDHA_TDCN
  [LOAI_DICH_VU.PHAU_THUAT_THU_THUAT]: TRANG_THAI_DICH_VU_PTTT, // PTTT
};

const getUniqueStatusItems = (statusArrays) => {
  const uniqueMap = new Map();

  statusArrays.forEach((statusArray) => {
    statusArray.forEach((item) => {
      const key = item.label;
      if (!uniqueMap.has(key)) {
        uniqueMap.set(key, item);
      }
    });
  });

  return Array.from(uniqueMap.values());
};

export const DANH_SACH_TRANG_THAI = {
  KHAM: [],
  XET_NGHIEM: TRANG_THAI_DICH_VU_XET_NGHIEM,
  CDHA_TDCN: TRANG_THAI_DICH_VU_CDHA_TDCN,
  PTTT: TRANG_THAI_DICH_VU_PTTT,
  XN_CDHA: getUniqueStatusItems([
    TRANG_THAI_DICH_VU_XET_NGHIEM,
    TRANG_THAI_DICH_VU_CDHA_TDCN,
  ]),
  XN_PTTT: getUniqueStatusItems([
    TRANG_THAI_DICH_VU_XET_NGHIEM,
    TRANG_THAI_DICH_VU_PTTT,
  ]),
  CDHA_PTTT: getUniqueStatusItems([
    TRANG_THAI_DICH_VU_CDHA_TDCN,
    TRANG_THAI_DICH_VU_PTTT,
  ]),
  XN_CDHA_PTTT: getUniqueStatusItems([
    TRANG_THAI_DICH_VU_XET_NGHIEM,
    TRANG_THAI_DICH_VU_CDHA_TDCN,
    TRANG_THAI_DICH_VU_PTTT,
  ]),
};

// Get status items based on service types
export const getTrangThaiByLoaiDichVu = (dsLoaiDichVu = []) => {
  if (!Array.isArray(dsLoaiDichVu) || dsLoaiDichVu.length === 0) {
    return DANH_SACH_TRANG_THAI.XN_CDHA_PTTT;
  }

  // Get dsTrangThai dựa vào dsLoaiDichVu
  const statusArrays = dsLoaiDichVu
    .map((type) => TRANG_THAI_MAP[type])
    .filter(Boolean);

  if (statusArrays.length === 0) {
    return DANH_SACH_TRANG_THAI.XN_CDHA_PTTT;
  }

  // Trả về dsTrangThai unique
  return getUniqueStatusItems(statusArrays);
};

export const flexConfig = {
  checkbox: {
    "--basis": "50px",
    "--sticky-left": 0,
  },
  stt: {
    "--basis": "50px",
    "--sticky-left": "50px",
  },
  tenDichVu: {
    "--basis": "200px",
    "--grow": 1,
    "--sticky-left": "100px",
  },
  soLuong: {
    "--basis": "80px",
  },
  ketQua: {
    "--basis": "65px",
    "--grow": 1,
  },
  donViTinh: {
    "--basis": "100px",
  },
  loaiPttt: {
    "--basis": "100px",
  },
  ketQuaThamChieu: {
    "--basis": "150px",
    "--grow": 1,
  },
  phauThuatVien: {
    "--basis": "150px",
    "--grow": 1,
  },
  cdSauPt: {
    "--basis": "150px",
    "--grow": 1,
  },
  ketLuan: {
    "--basis": "80px",
    "--grow": 1,
  },
  tenPhongThucHien: {
    "--basis": "200px",
    "--grow": 1,
  },
  tenKhoaThucHien: {
    "--basis": "200px",
    "--grow": 1,
  },
  tenKhoaChiDinh: {
    "--basis": "200px",
    "--grow": 1,
  },
  bacSiChiDinh: {
    "--basis": "200px",
  },
  capCuu: {
    "--basis": "80px",
  },
  trangThai: {
    "--basis": "100px",
    "--grow": 1,
  },
  tienIch: {
    "--basis": "100px",
  },
  thoiGian: {
    "--basis": "150px",
  },
  cachThuc: {
    "--basis": "200px",
    "--grow": 1,
  },
  phuongPhapPhauThuat: {
    "--basis": "200px",
    "--grow": 1,
  },
  canhBao: {
    "--basis": "200px",
    "--grow": 1,
  },
  thoiGianXemKetQua: {
    "--basis": "150px",
  },
  tenNguoiXemKetQua: {
    "--basis": "200px",
    "--grow": 1,
  },
  tenBenhPham: {
    "--basis": "200px",
    "--grow": 1,
  },
};
