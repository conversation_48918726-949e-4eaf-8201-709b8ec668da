import React, { useMemo, useRef, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { dispatch, query } from "redux-store/stores";
import { debounce, isEqual } from "lodash";
import {
  BaseSearch,
  InputTimeout,
  SelectLoadMore,
  Tooltip,
  Virtuoso,
  Checkbox,
} from "components";
import { useQueryAll, useEnum } from "hooks";
import { LIST_TRANG_THAI_THUOC } from "../config";
import { SVG } from "assets";
import dmDichVuKhoProvider from "data-access/categories/dm-dich-vu-kho-provider";
import { combineSort } from "utils";
import ModalChonKhoa from "./ModalChonKhoa";
import { useCapPhatThuoc } from "../hooks/useCapPhatThuoc";
import { Form, Input } from "antd";
import styled from "styled-components";
import {
  CoffeeOutlined,
  CloudOutlined,
  MoonOutlined,
  StarOutlined,
} from "@ant-design/icons";
import { THIET_LAP_CHUNG, LOAI_DICH_VU, ENUM } from "constants/index";
import { Main, SelectStyled, GlobalStyle } from "./styled";

const TimeOptionsContainer = styled.div`
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  gap: 8px;
  transition: all 0.3s ease;
  padding-left: 5px;
  height: 40px;
`;

const CheckboxStyled = styled(Checkbox)`
  margin: 0;
  font-weight: 500;
  white-space: nowrap;
`;

const TimeOptionsWrapper = styled.div`
  display: flex;
  flex-wrap: nowrap;
  gap: 8px;
  transition: all 0.3s ease;
  opacity: ${(props) => (props.show ? "1" : "0")};
  max-width: ${(props) => (props.show ? "500px" : "0")};
`;

const TimeOption = styled.div`
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 2px 8px;
  border-radius: 18px;
  transition: all 0.25s ease-in-out;
  margin: 0;
  background: ${({ $selected }) => ($selected ? "#1890ff" : "#f0f2f5")};
  color: ${({ $selected }) => ($selected ? "white" : "#595959")};
  box-shadow: ${({ $selected }) =>
    $selected ? "0 2px 8px rgba(24, 144, 255, 0.2)" : "none"};

  .anticon {
    margin-right: 5px;
    font-size: 14px;
    vertical-align: middle;
  }

  .label {
    font-weight: 500;
    font-size: 13px;
    white-space: nowrap;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
  }
`;

const ThuocChuaSuDungComponent = ({ t, onSearch, initialState }) => {
  const [isChecked, setIsChecked] = useState(initialState?.length > 0);
  const [selectedTimes, setSelectedTimes] = useState(initialState || []);
  const lastSelectedTime = useRef(null);
  const refTimeout = useRef(null);

  useEffect(() => {
    setSelectedTimes(initialState || []);
  }, [initialState]);

  const loaiThoiGianOptions = [
    {
      id: 0,
      ten: t("quanLyNoiTru.sang"),
      icon: <CoffeeOutlined />,
    },
    {
      id: 10,
      ten: t("quanLyNoiTru.chieu"),
      icon: <CloudOutlined />,
    },
    {
      id: 20,
      ten: t("quanLyNoiTru.toi"),
      icon: <MoonOutlined />,
    },
    {
      id: 30,
      ten: t("quanLyNoiTru.dem"),
      icon: <StarOutlined />,
    },
  ];

  const onChange = (e) => {
    setIsChecked(e.target.checked);
    if (!e.target.checked) {
      lastSelectedTime.current = selectedTimes;
    }
    onSearch("dsLoaiThoiGianChuaSuDung")({
      dsLoaiThoiGianChuaSuDung: e.target.checked
        ? lastSelectedTime.current
        : null,
    });
  };

  const toggleTimeOption = (id) => {
    let newSelectedTimes;
    if (selectedTimes.includes(id)) {
      newSelectedTimes = selectedTimes.filter((item) => item !== id);
    } else {
      newSelectedTimes = [...selectedTimes, id];
    }

    setSelectedTimes(newSelectedTimes);
    if (refTimeout.current) {
      clearTimeout(refTimeout.current);
    }
    refTimeout.current = setTimeout(() => {
      onSearch("dsLoaiThoiGianChuaSuDung")({
        dsLoaiThoiGianChuaSuDung: newSelectedTimes,
      });
    }, 500);
  };

  return (
    <TimeOptionsContainer>
      <CheckboxStyled onChange={onChange} checked={isChecked}>
        {t("quanLyNoiTru.thuocChuaSuDung")}
      </CheckboxStyled>

      <TimeOptionsWrapper show={isChecked}>
        {loaiThoiGianOptions.map((option) => (
          <TimeOption
            key={option.id}
            $selected={selectedTimes.includes(option.id)}
            onClick={() => toggleTimeOption(option.id)}
          >
            {option.icon}
            <span className="label">{option.ten}</span>
          </TimeOption>
        ))}
      </TimeOptionsWrapper>
    </TimeOptionsContainer>
  );
};

const TimKiem = ({ onChangeInputSearch }) => {
  const {
    listKhoTheoTaiKhoan,
    listKhoaTheoTaiKhoan,
    getTrangThaiThuoc,
    state,
    setState,
  } = useCapPhatThuoc();

  const { t } = useTranslation();
  const refModalChonKhoa = useRef(null);

  const [dataLOAI_CHI_DINH] = useEnum(ENUM.LOAI_CHI_DINH);

  const { data: listDieuDuong } = useQueryAll(
    query.nhanVien.queryAllNhanVien({
      params: {
        dsMaThietLapVanBang: [THIET_LAP_CHUNG.DIEU_DUONG, THIET_LAP_CHUNG.Y_TA],
        khoaId: state.dsKhoaChiDinhId,
      },
    })
  );

  const { data: listAllPhong } = useQueryAll(query.phong.queryAllPhong);

  const { data: listAllBacSi } = useQueryAll(
    query.nhanVien.queryAllNhanVien({
      params: {
        dsMaThietLapVanBang: [THIET_LAP_CHUNG.BAC_SI],
      },
    })
  );

  const listPhongTheoTaiKhoanModify = useMemo(() => {
    if (!state.dsKhoaChiDinhId) return listAllPhong;
    return listAllPhong.filter((item) => state.dsKhoaChiDinhId === item.khoaId);
  }, [listAllPhong, state.dsKhoaChiDinhId]);

  const cacheData = useMemo(() => {
    return {
      maNb: state.maNb,
      tenNb: state.tenNb,
      maHoSo: state.maHoSo,
      maThe: state.maThe,
      maBenhAn: state.maBenhAn,
      dsKhoaChiDinhId: state.dsKhoaChiDinhId,
      dsTrangThai: state.dsTrangThai.flatMap(
        (item) => getTrangThaiThuoc(item)?.refIds
      ),
      dsKhoId: state.dsKhoId,
      loaiDuongDung: state.loaiDuongDung,
      dsDichVuId: state.dsDichVuId,
      tuThoiGianThucHien: moment.isMoment(state.tuThoiGianThucHien)
        ? moment(state.tuThoiGianThucHien)
            .startOf("date")
            .format("DD/MM/YYYY HH:mm:ss")
        : state.tuThoiGianThucHien,
      denThoiGianThucHien: moment.isMoment(state.denThoiGianThucHien)
        ? moment(state.denThoiGianThucHien)
            .endOf("date")
            .format("DD/MM/YYYY HH:mm:ss")
        : state.denThoiGianThucHien,
      dsPhongId: state.dsPhongId,
      dsBacSiDieuTriId: state.dsBacSiDieuTriId,
      dsTrangThaiHoan: state.dsTrangThaiHoan,
      chiDinhTuToDieuTri: state.chiDinhTuToDieuTri,
      dsLoaiChiDinh: state.dsLoaiChiDinh,
    };
  }, [state]);

  const isDisableSelectAll = !(
    state.tenNb ||
    state.maHoSo ||
    state.maThe ||
    state.maBenhAn ||
    state.maNb
  );
  const onSearch = (type) => (data, pureValue) => {
    if (!type) {
      const hasPatientIdentifier =
        data.tenNb || data.maHoSo || data.maThe || data.maBenhAn || data.maNb;

      if (!hasPatientIdentifier && pureValue?.trim()?.length) return;

      if (
        !isDisableSelectAll &&
        !hasPatientIdentifier &&
        !state.dsKhoaChiDinhId
      ) {
        refModalChonKhoa.current?.show({
          onChange: (value) => {
            const updatedSearch = { ...data, dsKhoaChiDinhId: value };
            onChangeInputSearch(updatedSearch);
            setState(updatedSearch);
          },
        });
        return;
      }
      let transformParams = {};

      if (type === "dsTrangThai") {
        transformParams = {
          dsTrangThai: data.dsTrangThai.flatMap(
            (i) => getTrangThaiThuoc(i)?.refIds
          ),
        };
      }

      if (type === "dsKhoaChiDinhId") {
        data.dsPhongId = [];
      }
      onChangeInputSearch({
        ...data,
        ...transformParams,
      });
      setState(data);
      return;
    }

    let search = data;
    let newState = data;

    switch (type) {
      case "thoiGianThucHien":
        search = {
          tuThoiGianThucHien: data.tuThoiGianThucHien?.format(
            "YYYY-MM-DD 00:00:00"
          ),
          denThoiGianThucHien: data.denThoiGianThucHien?.format(
            "YYYY-MM-DD 23:59:59"
          ),
        };
        newState = data;
        break;

      case "dsTrangThai":
        search = {
          dsTrangThai: data.dsTrangThai.flatMap(
            (i) => getTrangThaiThuoc(i)?.refIds
          ),
        };
        newState = data;
        break;

      case "dsKhoaChiDinhId":
        search = {
          dsKhoaChiDinhId: data.dsKhoaChiDinhId,
          dsPhongId: [],
        };
        newState = search;
        break;
    }

    onChangeInputSearch(search);
    setState(newState);
  };

  const onSearchThuoc = (e) => {
    setState({ inputSearchThuoc: e });
  };

  // This is the correct approach for BaseSearch "addition" type
  const ThuocChuaSuDungFilter = (
    <ThuocChuaSuDungComponent
      t={t}
      setState={setState}
      onSearch={onSearch}
      initialState={state.dsLoaiThoiGianChuaSuDung}
    />
  );

  const dataInput = [
    {
      widthInput: "200px",
      placeholder: t("quanLyNoiTru.timTenQrMaHs"),
      functionChangeInput: onSearch(),
      isScanQR: true,
      qrGetValue: "maHoSo",
      putToQuerry: false,
      trimStr: true,
      keysFlexible: [
        {
          key: "tenNb",
          type: "string",
        },
        {
          key: "maHoSo",
          type: "maHoSo",
        },
        {
          key: "maThe",
          type: "hex",
        },
        {
          key: "maBenhAn",
          type: "number7",
        },
        {
          key: "maNb",
          type: "startLettersEndNumbers",
        },
      ],
    },
    {
      widthInput: "200px",
      placeholder: t("quanLyNoiTru.chonKhoa"),
      keyValueInput: "dsKhoaChiDinhId",
      functionChangeInput: onSearch("dsKhoaChiDinhId"),
      type: "select",
      putToQuerry: false,
      allowClear: true,
      hasAllOption: true,
      optionAllValue: null,
      getDisabled: [
        (item) => item.id === null && isDisableSelectAll,
        isDisableSelectAll,
      ],
      listSelect: listKhoaTheoTaiKhoan,
      dropdownWidth: 400,
    },
    {
      widthInput: "200px",
      placeholder: t("common.chonPhong"),
      keyValueInput: "dsPhongId",
      functionChangeInput: onSearch("dsPhongId"),
      type: "select",
      putToQuerry: false,
      mode: "multiple",
      listSelect: listPhongTheoTaiKhoanModify,
      dropdownWidth: 400,
    },
    {
      widthInput: "200px",
      placeholder: t("baoCao.chonBacSi"),
      keyValueInput: "dsBacSiDieuTriId",
      functionChangeInput: onSearch("dsBacSiDieuTriId"),
      type: "select",
      putToQuerry: false,
      mode: "multiple",
      listSelect: listAllBacSi,
      dropdownWidth: 400,
    },
    {
      widthInput: "220px",
      type: "dateOptions",
      state: state,
      setState: setState,
      keyValueInput: ["tuThoiGianThucHien", "denThoiGianThucHien"],
      functionChangeInput: onSearch("thoiGianThucHien"),
      title: t("quanLyNoiTru.thoiGianYLenh"),
      placeholder: t("quanLyNoiTru.thoiGianYLenh"),
      putToQuerry: false,
      format: "DD/MM/YYYY",
      customWidth: "300px",
    },
    {
      widthInput: "150px",
      title: t("quanLyNoiTru.suatAn.trangThai"),
      placeholder: t("quanLyNoiTru.suatAn.trangThai"),
      keyValueInput: "dsTrangThai",
      type: "selectCheckbox",
      defaultValue: state.dsTrangThai,
      listSelect: LIST_TRANG_THAI_THUOC,
      functionChangeInput: debounce(onSearch("dsTrangThai"), 400),
      putToQuerry: false,
      hasCheckAll: true,
      virtual: true,
      popoverMinWidth: "250px",
    },
    {
      placeholder: t("quanLyNoiTru.chiDinhTuToDieuTri"),
      keyValueInput: "chiDinhTuToDieuTri",
      type: "addition",
      putToQuerry: false,
      component: (
        <div
          className="flex-center h-full"
          style={{
            paddingLeft: 5,
          }}
        >
          <Checkbox
            checked={state.chiDinhTuToDieuTri}
            onChange={(e) => {
              setState({ chiDinhTuToDieuTri: e.target.checked });
              onChangeInputSearch({
                dsChiDinhTuLoaiDichVu: e.target.checked
                  ? [LOAI_DICH_VU.TO_DIEU_TRI, LOAI_DICH_VU.NHA_THUOC]
                  : null,
              });
            }}
          >
            {t("quanLyNoiTru.chiDinhTuToDieuTri")}
          </Checkbox>
        </div>
      ),
    },
    {
      placeholder: t("quanLyNoiTru.thuocChuaSuDung"),
      keyValueInput: "dsLoaiThoiGianChuaSuDung",
      functionChangeInput: onSearch("dsLoaiThoiGianChuaSuDung"),
      type: "addition",
      putToQuerry: false,
      component: ThuocChuaSuDungFilter,
    },
  ];
  const filter = {
    open: true,
    width: "110px",
    funcSearchData: (data) => {
      onChangeInputSearch(data);
      setState(data);
    },
    data: [
      {
        title: t("quanLyNoiTru.dvNoiTru.kho"),
        placeholder: t("quanLyNoiTru.dvNoiTru.kho"),
        key: "dsKhoId",
        type: "select",
        mode: "multiple",
        dataSelect: listKhoTheoTaiKhoan,
        value: state.dsKhoId || [],
      },
      {
        title: t("quanLyNoiTru.phanLoaiThuoc"),
        placeholder: t("quanLyNoiTru.phanLoaiThuoc"),
        key: "loaiDuongDung",
        type: "select",
        value: state.loaiDuongDung,
        dataSelect: [
          {
            id: "1",
            ten: t("quanLyNoiTru.capPhatThuoc.dichTruyen"),
          },
          { id: "2", ten: t("quanLyNoiTru.capPhatThuoc.thuocThuong") },
        ],
        hasAllOption: true,
      },
      {
        title: t("common.loaiChiDinh"),
        placeholder: t("common.loaiChiDinh"),
        key: "dsLoaiChiDinh",
        type: "select",
        mode: "multiple",
        dataSelect: dataLOAI_CHI_DINH,
        dropdownWidth: 400,
        value: state.dsLoaiChiDinh || [],
      },
      {
        type: "addition",
        component: ({ form }) => (
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => {
              return !isEqual(prevValues.dsDichVuId, currentValues.dsDichVuId);
            }}
          >
            {() => {
              const dataThuoc = (state.dsThuoc || []).filter((item) =>
                form.getFieldValue("dsDichVuId")?.includes(item.value)
              );
              return (
                <>
                  <Form.Item name="dsDichVuId" hidden>
                    <Input hidden />
                  </Form.Item>
                  <SelectStyled>
                    <div className="label">{t("common.thuoc")}</div>
                    <SelectLoadMore
                      placeholder={t("khamBenh.donThuoc.chonThuoc")}
                      api={dmDichVuKhoProvider.search}
                      mapData={(i) => ({
                        value: `${i.id}`,
                        label: `${i.dichVu.ma} - ${i.dichVu.ten}`,
                      })}
                      onChange={(e, item) => {
                        form.setFieldsValue({ dsDichVuId: e });
                        setState({ dsThuoc: item });
                      }}
                      value={form.getFieldValue("dsDichVuId") || []}
                      mode="multiple"
                      addParam={{
                        "dichVu.loaiDichVu": 90,
                        size: 10,
                        "dichVu.ten": state.inputSearchThuoc || "",
                        sort: combineSort({
                          active: "desc",
                          "dichVu.ma": "asc",
                        }),
                      }}
                      keySearch={"dv.ten"}
                      style={{ width: "300px" }}
                      className={"button-search-thuoc"}
                      dropdownMatchSelectWidth={1000}
                      maxTagCount="responsive"
                      showSearch={false}
                      tagRender={(props) => {
                        return dataThuoc.map((i) => {
                          return <span key={i.value}>{i.label}</span>;
                        });
                      }}
                      maxTagPlaceholder={(data) => {
                        const label = dataThuoc
                          .map((item) => item.label)
                          .join(", ");
                        return (
                          <Tooltip
                            title={label}
                            overlayInnerStyle={{ width: "60vw" }}
                          >
                            <div style={{ cursor: "pointer" }}>
                              + {dataThuoc?.length}...
                            </div>
                          </Tooltip>
                        );
                      }}
                      popupClassName="select-loadmore-thuoc"
                      dropdownRender={(node) => {
                        return (
                          <div className="flex">
                            <div className="left">
                              <InputTimeout
                                onKeyDown={(e) => {
                                  if (e.key === "Backspace") {
                                    return e.stopPropagation();
                                  }
                                }}
                                onChange={onSearchThuoc}
                                value={state.inputSearchThuoc}
                                placeholder={t(
                                  "quanLyNoiTru.capPhatThuoc.timThuoc"
                                )}
                                size="large"
                                suffix={<SVG.IcSearch />}
                              />
                              {node}
                            </div>
                            <div className="right">
                              <div className="title">
                                <span>
                                  {" "}
                                  {t(
                                    "quanLyNoiTru.capPhatThuoc.thuocDaChon"
                                  )}:{" "}
                                </span>
                                <span className="number">
                                  {dataThuoc?.length || 0}
                                </span>{" "}
                              </div>
                              <Virtuoso
                                data={dataThuoc}
                                className="virtuoso"
                                style={{ height: "100%" }}
                                itemContent={(index, item) => {
                                  return (
                                    <div
                                      onClick={() => {
                                        dataThuoc.splice(index, 1);
                                        setState({
                                          dsThuoc: [...dataThuoc],
                                        });
                                        form.setFieldsValue({
                                          dsDichVuId: [
                                            ...dataThuoc.map((x) => x.value),
                                          ],
                                        });
                                      }}
                                      className="item"
                                      title={item?.label}
                                      key={item?.value}
                                    >
                                      <div className="label">{item.label}</div>
                                      <SVG.IcDone />
                                    </div>
                                  );
                                }}
                              />
                            </div>
                          </div>
                        );
                      }}
                    />
                  </SelectStyled>
                </>
              );
            }}
          </Form.Item>
        ),
        key: "dsDichVuId",
      },
      {
        title: t("quanLyNoiTru.dieuDuongPhuTrach"),
        placeholder: t("quanLyNoiTru.dieuDuongPhuTrach"),
        key: "dsDieuDuongId",
        functionChangeInput: onChangeInputSearch,
        mode: "multiple",
        type: "select",
        dataSelect: listDieuDuong,
        value: state.dsDieuDuongId || [],
      },
    ],
  };

  return (
    <Main>
      <GlobalStyle />
      <div className="search-based">
        <BaseSearch
          cacheData={cacheData}
          dataInput={dataInput}
          filter={filter}
        />
      </div>

      <div className="array-store">
        {state.dsTrangThai.map((item) => {
          return (
            <div className="item" key={item}>
              <span>{t(getTrangThaiThuoc(item)?.i18n)}</span>
              <SVG.IcCancel
                style={{ cursor: "pointer" }}
                onClick={(e) => {
                  const _dsTrangThai = state.dsTrangThai.filter(
                    (x) => x !== item
                  );
                  onSearch("dsTrangThai")({ dsTrangThai: _dsTrangThai });
                }}
              />
            </div>
          );
        })}
      </div>
      <ModalChonKhoa data={listKhoaTheoTaiKhoan} ref={refModalChonKhoa} />
    </Main>
  );
};

export default TimKiem;
