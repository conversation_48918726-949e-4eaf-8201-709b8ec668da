import React, { useEffect, useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector, useDispatch } from "react-redux";
import { Button, Dropdown } from "components";
import { SVG } from "assets";
import { Menu } from "antd";
import {
  LIST_PHIEU_IN_EDITOR_ALL,
  MA_BIEU_MAU_EDITOR,
  VI_TRI_PHIEU_IN,
  MAN_HINH_PHIEU_IN,
} from "constants/index";
import { POPUP_TYPE } from "../../config";
import printProvider from "data-access/print-provider";
import { openInNewTab } from "utils";
import { useConfirm, useLoading, useRefFunc } from "hooks";
import moment from "moment";
import { useCheckbox } from "../../hooks/useCheckbox";
import { useCapPhatThuoc } from "../../hooks/useCapPhatThuoc";
import { checkIsPhieuKySo } from "utils/phieu-utils";
//Modal chọn tiêu chí phiếu
import ModalChonKhoaPhieuTruyenDich from "../ModalChonKhoaPhieuTruyenDich";
import ModalChonPhieuCongKhaiThuoc from "../ModalChonPhieuCongKhaiThuoc";
import ModalChonPhieuBanGiaoThuoc from "../ModalChonPhieuBanGiaoThuoc";
import ModalTraThuocNhieuNgay from "../ModalTraThuocNhieuNgay";
import ModalChonPhieuCongKhaiVtyt from "../ModalChonPhieuCongKhaiVtyt";
import { LOAI_DICH_VU } from "constants/index";
import { GlobalStyle, Main } from "./styled";

const Header = ({ onChangeInputSearch }) => {
  const { t } = useTranslation();

  const { getCheckedList, removeAllChecked } = useCheckbox();
  const { showConfirm } = useConfirm();
  const {
    state,
    setState,
    dataFilter,
    getTrangThaiThuoc,
    modalState,
    setModalState,
  } = useCapPhatThuoc();

  const { showLoading, hideLoading } = useLoading();

  const refModalChonPhieuCongKhaiThuoc = useRef(null);
  const refModalChonPhieuBanGiaoThuoc = useRef(null);
  const refModalTraThuocNhieuNgay = useRef(null);
  const refModalChonKhoaPhieuTruyenDich = useRef(null);
  const refModalChonPhieuCongKhaiVtyt = useRef(null);
  const refPagesData = useRef({});
  const refFlag = useRef(false);
  const refTimeout = useRef(null);

  const [localState, _setLocalState] = useState({
    listPhieu: [],
  });
  const setLocalState = (data = {}) => {
    _setLocalState((state) => {
      return { ...state, ...data };
    });
  };

  const { isLoading, listThongKe, pagesData } = useSelector(
    (state) => state.nbCapPhatThuoc
  );
  const {
    phieuIn: { getFilePhieuIn, showFileEditor },
    nbCapPhatThuoc: {
      getListPhieu,
      postBanGiaoThuoc,
      postSuDungThuoc,
      updateData,
    },
  } = useDispatch();

  useEffect(() => {
    getListPhieu({
      maManHinh: MAN_HINH_PHIEU_IN.POPUP_DSNB_SU_DUNG_THUOC,
      maViTri: VI_TRI_PHIEU_IN.POPUP_DSNB_SU_DUNG_THUOC.IN_GIAY_TO,
    }).then((res) => {
      const arr = res || [];

      arr.sort((a, b) => {
        if (a.stt == null && b.stt == null) return 0;
        if (a.stt == null) return 1;
        if (b.stt == null) return -1;
        return a.stt - b.stt;
      });

      setLocalState({
        listPhieu: arr,
      });
    });
  }, []);

  const onPrintPhieu = useRefFunc(async (item) => {
    if (LIST_PHIEU_IN_EDITOR_ALL.includes(item.ma)) {
      let mhParams = {};
      //kiểm tra phiếu ký số
      if (checkIsPhieuKySo(item)) {
        //nếu là ký số thì lấy các thông tin về lịch sử ký + tên chân ký + báo cáo id
        mhParams = {
          maManHinh: item.maManHinh,
          maViTri: item.maViTri,
          kySo: true,
          maPhieuKy: item.ma,
        };
      }

      const lichSuKyId = item?.dsSoPhieu?.length
        ? item?.dsSoPhieu[0].lichSuKyId
        : "";

      if (item.ma == "P183") {
        refModalChonPhieuCongKhaiThuoc.current &&
          refModalChonPhieuCongKhaiThuoc.current.show(
            {
              khoaChiDinhId: state?.dsKhoaChiDinhId,
              ma: item.ma,
            },
            (filterData) => {
              showFileEditor({
                phieu: item,
                id: filterData?.id,
                nbDotDieuTriId: filterData?.nbDotDieuTriId,
                ma: item.ma,
                maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                  ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                  : "",
                khoaChiDinhId: filterData?.khoaChiDinhId,
                tuThoiGian: filterData?.tuThoiGian,
                denThoiGian: filterData?.denThoiGian,
                dsLoaiChiDinh: filterData?.dsLoaiChiDinh,
                dsChiDinhTuLoaiDichVu: [
                  LOAI_DICH_VU.TO_DIEU_TRI,
                  LOAI_DICH_VU.NHA_THUOC,
                ],
                lichSuKyId,
              });
            }
          );
      } else if (item.ma == "P181" || item.ma == "P191") {
        refModalChonPhieuBanGiaoThuoc.current &&
          refModalChonPhieuBanGiaoThuoc.current.show(
            {
              khoaChiDinhId: state?.dsKhoaChiDinhId,
            },
            (filterData) => {
              showFileEditor({
                phieu: item,
                id: filterData?.nbDotDieuTriId,
                nbDotDieuTriId: filterData?.nbDotDieuTriId,
                ma: item.ma,
                maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                  ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                  : "",
                khoaChiDinhId: filterData?.khoaChiDinhId,
                thoiGianYLenh: filterData?.thoiGianYLenh,
                lichSuKyId,
              });
            }
          );
      } else if (item.ma == "P086") {
        refModalChonKhoaPhieuTruyenDich.current &&
          refModalChonKhoaPhieuTruyenDich.current.show(
            {
              khoaChiDinhId: state?.dsKhoaChiDinhId,
            },
            (filterData) => {
              showFileEditor({
                phieu: item,
                id: filterData?.id,
                nbDotDieuTriId: filterData?.nbDotDieuTriId,
                ma: item.ma,
                maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                  ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                  : "",
                khoaChiDinhId: filterData?.khoaChiDinhId,
                mhParams,
              });
            }
          );
      } else if (item.ma == "P197") {
        //Phiếu thực hiện và công khai vật tư y tế tiêu hao
        refModalChonPhieuCongKhaiVtyt.current &&
          refModalChonPhieuCongKhaiVtyt.current.show(
            {
              khoaChiDinhId: state?.dsKhoaChiDinhId,
              ma: item.ma,
            },
            (filterData) => {
              const payload = {
                phieu: item,
                id: filterData?.id,
                nbDotDieuTriId: filterData?.nbDotDieuTriId,
                ma: item.ma,
                maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                  ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                  : "",
                khoaChiDinhId: filterData?.khoaChiDinhId,
                tuThoiGian: filterData?.tuThoiGian,
                denThoiGian: filterData?.denThoiGian,
                mhParams,
              };
              showFileEditor(payload);
            }
          );
      } else {
        showFileEditor({
          phieu: item,
          ma: item.ma,
          maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
            ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
            : "",
          mhParams,
        });
      }
    } else {
      try {
        showLoading();
        const { finalFile, dsPhieu } = await getFilePhieuIn({
          listPhieus: [item],
          showError: true,
        });
        if ((dsPhieu || []).every((x) => x?.loaiIn == 20)) {
          openInNewTab(finalFile);
        } else {
          printProvider.printPdf(dsPhieu);
        }
      } catch (error) {
        console.log("error", error);
      } finally {
        hideLoading();
      }
    }
  });

  const contentPrint = useMemo(() => {
    return (
      <Menu
        items={(localState?.listPhieu || []).map((item, index) => ({
          key: index,
          label: (
            <a href={() => false} onClick={() => onPrintPhieu(item)}>
              {item.ten || item.tenBaoCao}
            </a>
          ),
        }))}
      />
    );
  }, [localState.listPhieu]);

  const listRightContent = [
    {
      key: "TAO_MOI",
      label: t("common.taoMoi"),
      background: "#C1F3F7",
      value: listThongKe.taoMoi || 0,
    },
    {
      key: "DA_TAO_PHIEU_LINH",
      label: t("quanLyNoiTru.capPhatThuoc.daTaoPL"),
      background: "#C1D8FD",
      value: listThongKe.daTaoPhieuLinh || 0,
    },
    {
      key: "CHO_DUYET_PHIEU_LINH",
      label: t("quanLyNoiTru.capPhatThuoc.choDuyetPL"),
      background: "#D9C0F2",
      value: listThongKe.choDuyetPhieuLinh || 0,
    },
    {
      key: "DA_DUYET_DUOC_LAM_SANG",
      label: t("kho.daDuyetDLS"),
      background: "#FECECE",
      value: listThongKe.daDuyetDuocLamSang || 0,
    },
    {
      key: "DA_PHAT",
      label: t("common.daPhat"),
      background: "#FEF9C3",
      value: listThongKe.daPhat || 0,
    },
    {
      key: "DA_BAN_GIAO",
      label: t("quanLyNoiTru.capPhatThuoc.daBanGiao"),
      background: "#B3E5FC",
      value: listThongKe.daBanGiao || 0,
    },
    {
      key: "DA_SU_DUNG",
      label: t("quanLyNoiTru.capPhatThuoc.daSuDung"),
      background: "#FFE0B2",
      value: listThongKe.daSuDung || 0,
    },
  ];

  const onBanGiaoThuoc = () => {
    let payload = null;
    const selectedKeys = getCheckedList();
    if (!selectedKeys.length) {
      payload = [dataFilter];
    } else {
      payload = selectedKeys.map((x) => ({
        nbDotDieuTriId: x,
      }));
    }

    showConfirm(
      {
        title: t("quanLyNoiTru.xacNhanBanGiaoThuoc"),
        content: t("quanLyNoiTru.xacNhanBanGiaoThuocChoNguoiBenhDaChon"),
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        showBtnOk: true,
        typeModal: "warning",
      },
      () => {
        showLoading();

        postBanGiaoThuoc({ page: 0, payload, isDetail: false })
          .then(() => {
            removeAllChecked();
          })
          .finally(() => {
            hideLoading();
          });
      },
      () => {}
    );
  };

  const onXacNhanSdThuoc = () => {
    let payload = null;
    const selectedKeys = getCheckedList();
    if (!selectedKeys.length) {
      payload = [dataFilter];
    } else {
      payload = selectedKeys.map((x) => ({
        nbDotDieuTriId: x,
      }));
    }

    showConfirm(
      {
        title: t("quanLyNoiTru.xacNhanCapPhatSuDungThuoc"),
        content: t("quanLyNoiTru.xacNhanCapPhatSuDungThuocChoNguoiBenhDaChon"),
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        showBtnOk: true,
        typeModal: "warning",
      },
      () => {
        showLoading();

        postSuDungThuoc({
          page: 0,
          payload,
          isDetail: false,
        })
          .then(() => {
            removeAllChecked();
          })
          .finally(() => {
            hideLoading();
          });
      },
      () => {}
    );
  };

  const onDetailList = (popupType) => {
    refPagesData.current = pagesData;
    updateData({ pagesData: {}, isLoading: true });
    refFlag.current = true;
    setModalState({
      popupType,
    });
  };

  const onClick = (key) => {
    setState({ dsTrangThai: [key] });
    onChangeInputSearch({ dsTrangThai: getTrangThaiThuoc(key)?.refIds });
  };

  const onTraThuocNhieuNgay = () => {
    let tuThoiGianThucHien = null;
    let denThoiGianThucHien = null;

    if (dataFilter?.tuThoiGianThucHien instanceof moment) {
      tuThoiGianThucHien = dataFilter?.tuThoiGianThucHien;
    } else if (
      moment(dataFilter?.tuThoiGianThucHien, "DD/MM/YYYY HH:mm:ss").isValid()
    ) {
      tuThoiGianThucHien = moment(
        dataFilter?.tuThoiGianThucHien,
        "DD/MM/YYYY HH:mm:ss"
      );
    }

    if (dataFilter?.denThoiGianThucHien instanceof moment) {
      denThoiGianThucHien = dataFilter?.denThoiGianThucHien;
    } else if (
      moment(dataFilter?.tuThoiGianThucHien, "DD/MM/YYYY HH:mm:ss").isValid()
    ) {
      denThoiGianThucHien = moment(
        dataFilter?.denThoiGianThucHien,
        "DD/MM/YYYY HH:mm:ss"
      );
    }

    refModalTraThuocNhieuNgay.current &&
      refModalTraThuocNhieuNgay.current.show({
        tuThoiGianThucHien,
        denThoiGianThucHien,
      });
  };

  useEffect(() => {
    if (refFlag.current) {
      refTimeout.current = setTimeout(() => {
        updateData({ pagesData: refPagesData.current, isLoading: false });
      }, 300);
      refFlag.current = false;
    }
    return () => refTimeout.current && clearTimeout(refTimeout.current);
  }, [modalState.popupType, refFlag.current]);

  return (
    <Main>
      <GlobalStyle />
      <div className="left flex">
        {modalState.popupType === POPUP_TYPE.DS_NB_SD_THUOC && (
          <Button
            onClick={() =>
              onDetailList(POPUP_TYPE.DS_CHI_TIET_BAN_GIAO_VA_SD_THUOC)
            }
          >
            {t("quanLyNoiTru.xemDanhSachChiTiet")}
          </Button>
        )}
        {modalState.popupType ===
          POPUP_TYPE.DS_CHI_TIET_BAN_GIAO_VA_SD_THUOC && (
          <Button
            onClick={() => {
              onDetailList(POPUP_TYPE.DS_NB_SD_THUOC);
            }}
          >
            {t("quanLyNoiTru.xemDanhSachNb")}
          </Button>
        )}

        <Dropdown
          trigger={["click"]}
          overlayClassName="dropdown-toggle-cap-phat-thuoc"
          menu={{
            items: [
              {
                label: (
                  <div className="flex align-items-center gap-8">
                    <SVG.IcBanGiaoThuoc width={20} height={20} />
                    <span>{t("quanLyNoiTru.capPhatThuoc.banGiaoThuoc")}</span>
                  </div>
                ),
                key: "banGiaoThuoc",
              },
              {
                label: (
                  <div className="flex align-items-center gap-8">
                    <SVG.IcSuccess width={20} height={20} />
                    <span>{t("quanLyNoiTru.capPhatThuoc.xacNhanSDThuoc")}</span>
                  </div>
                ),
                key: "xacNhanSDThuoc",
              },
              {
                label: (
                  <div className="flex align-items-center gap-8">
                    <SVG.IcRectangular width={20} height={20} />
                    <span>
                      {t("quanLyNoiTru.capPhatThuoc.traThuocNhieuNgay")}
                    </span>
                  </div>
                ),
                key: "traThuocNhieuNgay",
              },
            ],
            onClick: ({ key }) => {
              if (key === "banGiaoThuoc") onBanGiaoThuoc();
              else if (key === "xacNhanSDThuoc") onXacNhanSdThuoc();
              else onTraThuocNhieuNgay();
            },
          }}
        >
          <Button rightIcon={<SVG.IcArrowDown />}>{t("common.tienIch")}</Button>
        </Dropdown>

        <Dropdown
          overlayClassName="dropdown-toggle-cap-phat-thuoc"
          overlay={contentPrint}
          trigger={["click"]}
        >
          <Button className="ic-print" rightIcon={<SVG.IcPrint />}>
            {t("common.inGiayTo")}
          </Button>
        </Dropdown>
      </div>

      <div className="right-container">
        <div className="right flex">
          {listRightContent.map((item) => {
            const { key, background, label, value } = item;
            return (
              <div
                className="right-item"
                key={key}
                style={{
                  display: "inline-flex",
                  background,
                  cursor: "pointer",
                  ...(isLoading && {
                    cursor: "not-allowed",
                  }),
                }}
              >
                <div
                  style={{
                    ...(isLoading && {
                      pointerEvents: "none",
                    }),
                  }}
                  onClick={() => onClick(key)}
                >
                  {label}
                  <span>{value}</span>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      <ModalChonPhieuCongKhaiThuoc ref={refModalChonPhieuCongKhaiThuoc} />
      <ModalChonPhieuBanGiaoThuoc ref={refModalChonPhieuBanGiaoThuoc} />
      <ModalTraThuocNhieuNgay ref={refModalTraThuocNhieuNgay} />
      <ModalChonKhoaPhieuTruyenDich ref={refModalChonKhoaPhieuTruyenDich} />
      <ModalChonPhieuCongKhaiVtyt ref={refModalChonPhieuCongKhaiVtyt} />
    </Main>
  );
};

export default Header;
