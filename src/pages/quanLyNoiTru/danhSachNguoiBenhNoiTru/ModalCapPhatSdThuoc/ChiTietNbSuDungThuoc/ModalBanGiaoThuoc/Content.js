import React, { memo, useEffect, useMemo, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { Input, Radio, message } from "antd";
import moment from "moment";
import { cloneDeep, size } from "lodash";
import { useLoading } from "hooks";
import {
  Button,
  HeaderSearch,
  ModalTemplate,
  TableWrapper,
  Checkbox,
  Tooltip,
} from "components";
import { EVENT } from "../../config";
import { InputNumberFormat } from "components/common";
import classNames from "classnames";
import { roundToDigits } from "utils";
import { LOAI_DICH_VU, TRANG_THAI_THUOC } from "constants/index";
import dmBaoCaoProvider from "data-access/categories/dm-bao-cao-provider";
import { useCapPhatThuoc } from "../../hooks/useCapPhatThuoc";
import { Main } from "./styled";

const { TextArea } = Input;

const Content = () => {
  const refRadioCheck = useRef(null);
  const { t } = useTranslation();

  const { refModalPatientSign, dataKY_CAP_PHAT_THUOC, checkIsDisabled } =
    useCapPhatThuoc();

  const { data: dataModal, onCloseModal } = ModalTemplate.useModal();
  const { data, event, page } = dataModal;
  const [kyNguoiBenh, setKyNguoiBenh] = useState(false);

  const [state, _setState] = useState({
    data: [],
    selectedRowKeys: [],
    dataThuocKho: [],
    errors: {},
    isCheckedAll: true,
    isTuTruc: true,
  });

  const setState = (data = {}) => _setState((state) => ({ ...state, ...data }));

  const { showLoading, hideLoading } = useLoading();
  const {
    nbCapPhatThuoc: { postBanGiaoThuoc, postSuDungThuoc },
    traHangHoa: { postDsDvThuocTraKho },
    signer: { getImageSignPadPatient },
    files: { getFormData, onSaveForm },
    phieuIn: { kyPhieu },
  } = useDispatch();

  useEffect(() => {
    let _data = [];

    function getData(arr, isChild = false) {
      arr.forEach((item) => {
        item.isChild = isChild;
        let condition =
          (event === EVENT.BAN_GIAO
            ? TRANG_THAI_THUOC.DA_PHAT.id == item.trangThai
            : TRANG_THAI_THUOC.DA_BAN_GIAO.id == item.trangThai) &&
          !checkIsDisabled(item);
        if (condition) _data.push(item);
        if (item.children) {
          getData(item.children, true);
        }
      });
    }
    getData(cloneDeep(data));

    _data = _data.map((item) => {
      let slSuDung = (item.dsThoiGianSuDung || []).reduce((total, current) => {
        return total + current?.soLuong || 0;
      }, 0);
      let defaultValue = item.soLuong - item.soLuongHuy - slSuDung;
      defaultValue = defaultValue > 0 ? defaultValue : null;
      if (event === EVENT.SU_DUNG) {
        defaultValue = null;
      }

      return {
        ...item,
        _soLuongTra: defaultValue,
        _ghiChu: item.ghiChuCapPhatThuoc ?? item.ghiChu,
      };
    });

    let dataThuocKho = _data.filter((x) => x.loai !== 20);
    setState({
      data: _data,
      dataThuocKho,
      selectedRowKeys: _data.map((i) => i.id),
    });
  }, []);

  const onCancel = () => {
    setKyNguoiBenh(null);
    onCloseModal();
  };

  const isBanGiao = event === EVENT.BAN_GIAO;

  let listKeys = useMemo(() => {
    if (state.isTuTruc) return state.data.map((i) => i.id);
    return state.dataThuocKho.map((i) => i.id);
  }, [state.isTuTruc, state.data, state.dataThuocKho]);

  const api = EVENT.BAN_GIAO === event ? postBanGiaoThuoc : postSuDungThuoc;

  const _postDsDvThuocTraKho = (data) => {
    const payload = data
      .map((item) => ({
        loaiHangHoa: LOAI_DICH_VU.THUOC,
        nbDichVuId: item.id,
        soLuong: item._soLuongTra ? roundToDigits(item._soLuongTra, 3) : null,
      }))
      .filter((item) => item.soLuong);

    if (payload.length) {
      postDsDvThuocTraKho(payload).catch((e) => {
        message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
      });
    }
  };

  const onSubmit = async () => {
    const data = (state.isTuTruc ? state.data : state.dataThuocKho).filter(
      (i) => (state.selectedRowKeys || []).includes(i.id)
    );
    if (data.length) {
      if (event === EVENT.SU_DUNG) {
        let error = null;
        for (let item of data) {
          let soLuongKe = item[item?.loai === 60 ? "soLuong" : "soLuongYeuCau"];
          if (item._soLuongTra > soLuongKe - item.soLuongTra) {
            error = t("quanLyNoiTru.capPhatThuoc.slTraKhongDuocNhapQuaSL");
            break;
          }
          if (item._soLuongTra === 0) {
            error = t("quanLyNoiTru.capPhatThuoc.soLuongTraPhaiLonHon0");
            break;
          }
        }
        if (error) {
          message.error(error);
          return;
        }
      }
      const baoCaos = await dmBaoCaoProvider.getMauBaoCao({
        ma: isBanGiao ? "EMR_HSDD015.1" : "EMR_HSDD015.2",
      });
      const banGiaoOrHoanThanh = (dataForm) => {
        showLoading();
        if (event === EVENT.SU_DUNG) {
          _postDsDvThuocTraKho(data);
        }

        api({
          page: page,
          payload: data
            .flatMap((x) =>
              size(x.dsThuocGop)
                ? x.dsThuocGop.map((item) => ({
                    ...item,
                    _ghiChu: x._ghiChu,
                  }))
                : [x]
            )
            .map((x) => ({
              loai: x.loai,
              id: x.id,
              quaTrinhSuDungThuoc: refRadioCheck.current || 10,
              ghiChu: x._ghiChu,
            })),
        })
          .then(() => {
            onCancel();
            if (dataKY_CAP_PHAT_THUOC.eval()) {
              window.open(
                `/editor/bao-cao/${baoCaos.data.ma}/${dataForm.soPhieu}`
              );
            }

            hideLoading();
          })
          .finally(() => {
            hideLoading();
          });
      };
      if (dataKY_CAP_PHAT_THUOC.eval()) {
        if (!kyNguoiBenh) {
          const params = {
            file: baoCaos.data,
            updateState: false,
            queries: {
              thoiGianThucHien: moment().format(),
              ngayThucHien: moment(state.data?.[0]?.thoiGianThucHien).format(
                "YYYY-MM-DD"
              ),
              dsThuocId: data.map((el) => el.id),
              nbDotDieuTriId: state.data?.[0]?.nbDotDieuTriId,
              khoaChiDinhId: state.data?.[0].khoaChiDinhId,
              baoCaoId: baoCaos.data.id,
            },
          };
          let dataForm = await getFormData(null, params);

          if (!dataForm?.id) {
            dataForm.baoCaoId = baoCaos.data?.id;
            dataForm.dsThuocId = data.map((el) => el.id);
            dataForm.ngayThucHien = moment(
              params.queries.ngayThucHien
            ).format();
            dataForm.dsThuocId = data.map((el) => el.id);
            dataForm.khoaChiDinhId = params.queries.khoaChiDinhId;
            const res = await onSaveForm(null, {
              file: baoCaos.data,
              data: dataForm,
            });
            dataForm = res;
          }

          const kyNguoiBenh = async (anhKy) => {
            const thongTinKy = await kyPhieu({
              anhKy,
              id: null,
              chuKySo: 1,
              baoCaoId: baoCaos.data.id,
              soPhieu: dataForm.soPhieu,
              nbDotDieuTriId: dataForm.nbDotDieuTriId,
              duLieu: dataForm,
              viTri: 1,
            });
            if (thongTinKy.code == 0) {
              setKyNguoiBenh({ dataForm, thongTinKy: thongTinKy.data });
            }
          };
          try {
            const image = await getImageSignPadPatient();
            const anhKy = image.replace("data:image/png;base64,", "");
            await kyNguoiBenh(anhKy);
          } catch (error) {
            if (refModalPatientSign.current) {
              refModalPatientSign.current.show(
                {
                  isGetImage: true,
                },
                async (image) => {
                  const anhKy = image.replace("data:image/png;base64,", "");
                  kyNguoiBenh(anhKy);
                }
              );
            }
          }
        } else {
          const thongTinChanKy = baoCaos.data.components.find(
            (el) => el.name === "ImageSign" && el.props.capKy == 2
          );

          const res = await kyPhieu({
            baoCaoId: kyNguoiBenh.dataForm.baoCaoId,
            soPhieu: kyNguoiBenh.dataForm.soPhieu,
            nbDotDieuTriId: kyNguoiBenh.dataForm.nbDotDieuTriId,
            khoaChiDinhId: kyNguoiBenh.dataForm.khoaChiDinhId,
            khoaThucHienId: null,
            duLieu: kyNguoiBenh.dataForm,
            chuKySo: 2,
            loaiKy: thongTinChanKy?.props.loaiKy,
            api: baoCaos.data.api,
            idPhieu: kyNguoiBenh.dataForm.id,
            viTri: 1,
            maBaoCao: baoCaos.data.ma,
            id: kyNguoiBenh.thongTinKy.id,
          });
          if (res.code === 0 && res.data.trangThai === 60) {
            banGiaoOrHoanThanh(res.data);
          }
        }
      } else {
        banGiaoOrHoanThanh();
      }
    } else {
      message.error(t("quanLyNoiTru.capPhatThuoc.chuaChonThuoc"));
    }
  };

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: 30,
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
      render: (field, record, index) => {
        return record.dungKemId ? (
          <Tooltip title={t("pttt.thuocDungKem")}>
            <div
              className={classNames({
                "stt-dung-kem": record.dungKemId,
              })}
            >
              {index + 1}
            </div>
          </Tooltip>
        ) : (
          index + 1
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.toDieuTri.tenThuocLieuDungGhiChu")}
        />
      ),
      width: 250,
      dataIndex: "tenDichVu",
      key: "tenDichVu",
      i18Name: "quanLyNoiTru.tenThuocLieuDungGhiChu",
      align: "left",
      show: true,
      render: (_, record) => {
        const {
          hamLuong,
          soLuongHuy,
          soLuongTra,
          tenDvtSuDung,
          lyDoHuy,
          tenHoatChat,
          loai,
          tenDonViTinh,
          tenDichVu,
          sttNgaySuDung,
          dungKemId,
          ghiChu,
          cachDung,
          tenLieuDung,
          tenDvtSoCap,
        } = record;

        const donVi =
          tenDonViTinh === tenDvtSoCap && tenDonViTinh !== tenDvtSuDung
            ? tenDvtSoCap
            : (loai === 60 ? tenDonViTinh : tenDvtSuDung) || "";

        const _slHuy =
          !!soLuongHuy &&
          ` (${t("common.huy")} ${soLuongHuy} ${donVi}${
            lyDoHuy ? ` ${lyDoHuy}` : ""
          }${!!soLuongTra ? "" : `)`}`;
        const _slTra = !!soLuongTra
          ? `${!!soLuongHuy ? `, ` : ` (`}${t(
              "quanLyNoiTru.tra"
            )} ${soLuongTra} ${donVi})`
          : "";

        return (
          <div>
            <div className="thuoc-info__container">
              <div
                style={{
                  fontWeight: "bold",
                  marginLeft: 5,
                }}
              >
                {!!sttNgaySuDung && (
                  <div className="circle">{sttNgaySuDung}</div>
                )}{" "}
                {dungKemId ? "-" : ""} {tenDichVu}
                {tenHoatChat || hamLuong
                  ? tenHoatChat
                    ? hamLuong
                      ? ` (${tenHoatChat} - ${hamLuong})`
                      : ` (${tenHoatChat})`
                    : `(-${hamLuong})`
                  : ""}
                <span className="text-huy">
                  {_slHuy}
                  {_slTra}
                </span>
              </div>

              {(cachDung || tenLieuDung || ghiChu) && (
                <ul
                  style={{
                    listStyle: "disc",
                    paddingLeft: 20,
                    marginLeft: 0,
                    marginBottom: 0,
                    width: "calc(100% - 5px)",
                  }}
                >
                  <li>
                    {tenLieuDung} {cachDung}
                    {(tenLieuDung || cachDung) && <>. </>}
                    {ghiChu && (
                      <span style={{ fontStyle: "italic" }}>
                        {t("common.ghiChu")}: {ghiChu}
                      </span>
                    )}
                  </li>
                </ul>
              )}
            </div>
          </div>
        );
      },
    },
    {
      title: <HeaderSearch title={t("quanLyNoiTru.suatAn.sl")} />,
      width: 100,
      dataIndex: "soLuongYeuCau",
      key: "soLuongYeuCau",
      align: "center",
      i18Name: "quanLyNoiTru.sl",
      show: true,
      render: (_, record) => {
        const soLuongHuyTra = roundToDigits(
          record.soLuongHuy * 1 + record.soLuongTra * 1,
          3
        );

        const { tenDvtSoCap, tenDonViTinh, tenDvtSuDung } = record;

        const donVi =
          tenDonViTinh === tenDvtSoCap && tenDonViTinh !== tenDvtSuDung
            ? tenDvtSoCap
            : record?.loai === 60
            ? record.tenDonViTinh
            : record.tenDvtSuDung;

        return (
          <div className="sl-info">
            {record?.loai === 60 ? record.soLuong : record.soLuongYeuCau}{" "}
            {soLuongHuyTra > 0 && (
              <span className="text-danger">`(-${soLuongHuyTra})`</span>
            )}
            <span>&nbsp;{donVi}</span>
          </div>
        );
      },
    },
    {
      title: <HeaderSearch title={t("quanLyNoiTru.capPhatThuoc.slSuDung")} />,
      width: 100,
      dataIndex: "dsThoiGianSuDung",
      key: "slSuDung",
      align: "center",
      i18Name: "quanLyNoiTru.capPhatThuoc.slSuDung",
      hidden: event === EVENT.BAN_GIAO,
      show: true,
      render: (field, record) => {
        let totalSl = (field || []).reduce((total, current) => {
          return total + current?.soLuong || 0;
        }, 0);
        return totalSl ? `${totalSl} ${record.tenDvtSuDung}` : null;
      },
    },
    {
      title: <HeaderSearch title={t("quanLyNoiTru.capPhatThuoc.slTra")} />,
      width: 120,
      dataIndex: "soLuongTra",
      key: "soLuongTra",
      align: "center",
      i18Name: "quanLyNoiTru.capPhatThuoc.slTra",
      show: true,
      hidden: event === EVENT.BAN_GIAO,
      render: (_, record, index) => {
        return (
          <div className="flex-center gap-4">
            <InputNumberFormat
              allowNegative={false}
              allowEmptyFormatting={false}
              decimalScale={3}
              onValueChange={(values) => {
                state.data[index]._soLuongTra = values?.floatValue;
                setState({ data: [...state.data] });
              }}
              value={state.data[index]._soLuongTra}
            />
            <div style={{ width: 80 }}>{record.tenDvtSuDung}</div>
          </div>
        );
      },
    },

    {
      title: <HeaderSearch title={t("common.ghiChu")} />,
      width: 250,
      dataIndex: "ghiChu",
      key: "ghiChu",
      align: "left",
      i18Name: "common.ghiChu",
      show: true,
      render: (_, __, index) => {
        return (
          <>
            <TextArea
              bordered={false}
              onChange={(e) => {
                state.data[index]._ghiChu = e.target.value;
                setState({ data: [...state.data] });
              }}
              placeholder=""
              rows={4}
              style={{
                borderBottom: "1px solid #7A869A",
                height: 40,
              }}
              value={state.data[index]._ghiChu}
            />
          </>
        );
      },
    },
  ];

  const onSelectChange = (selectedRowKeys, data) => {
    //Ẩn checkbox của thuốc con. Khi thay đổi thuốc cha thì thay đổi cả con
    const updatedSelectedKeys = [
      ...new Set(
        data
          .filter((i) => !i.isChild)
          .flatMap((i) => [i.id, ...(i.children?.map((j) => j.id) || [])])
      ),
    ];

    if (listKeys.length === updatedSelectedKeys.length) {
      setState({
        isCheckedAll: true,
        selectedRowKeys: updatedSelectedKeys,
      });
    } else {
      setState({
        isCheckedAll: false,
        selectedRowKeys: updatedSelectedKeys,
      });
    }
  };

  const oncheckAll = (e) => {
    setState({
      selectedRowKeys: e.target?.checked ? listKeys : [],
      isCheckedAll: e.target?.checked,
    });
  };

  const rowSelection = {
    columnTitle: (
      <HeaderSearch
        title={
          <Checkbox
            style={{ color: "#03317c" }}
            onChange={oncheckAll}
            checked={state.isCheckedAll}
          ></Checkbox>
        }
      />
    ),
    columnWidth: 40,
    onChange: onSelectChange,
    selectedRowKeys: state.selectedRowKeys,
    getCheckboxProps: (record) => ({
      disabled: record.isChild,
      style: {
        display: record.isChild && "none",
      },
    }),
  };

  const onChangeRadio = (e) => {
    refRadioCheck.current = e.target.value;
  };

  const renderTextButon = useMemo(() => {
    if (dataKY_CAP_PHAT_THUOC.eval()) {
      if (!kyNguoiBenh) {
        return "Ký người bệnh";
      } else {
        return (
          "Điều dưỡng xác nhận ký và " + (isBanGiao ? "bàn giao" : "hoàn thành")
        );
      }
    } else {
      return isBanGiao ? "Bàn giao" : "Hoàn thành sử dụng";
    }
  }, [kyNguoiBenh, isBanGiao, dataKY_CAP_PHAT_THUOC]);
  return (
    <ModalTemplate.Container>
      <ModalTemplate.Content>
        <Main>
          {!!state.data.length && (
            <div className="header-ban-giao">
              <h2>
                {state.data?.[0]?.thoiGianThucHien &&
                  moment(state.data?.[0]?.thoiGianThucHien).format(
                    "DD/MM/YYYY"
                  )}
                {state.data?.[0]?.tenKhoaChiDinh &&
                  ` - ${state.data?.[0]?.tenKhoaChiDinh}`}
              </h2>

              <div className="checkbox">
                <Checkbox
                  checked={state.isTuTruc}
                  onChange={(e) =>
                    setState({
                      isTuTruc: e.target.checked,
                      isCheckedAll: false,
                      selectedRowKeys: [],
                      ...(state.isCheckedAll && {
                        isCheckedAll: true,
                        selectedRowKeys: e.target.checked
                          ? state.data.map((i) => i.id)
                          : state.dataThuocKho.map((i) => i.id),
                      }),
                    })
                  }
                >
                  {t("quanLyNoiTru.capPhatThuoc.thuocTuTruc")}
                </Checkbox>
              </div>
            </div>
          )}
          <TableWrapper
            dataSource={state.isTuTruc ? state.data : state.dataThuocKho}
            columns={columns}
            rowKey={(record) => record.id}
            rowSelection={rowSelection}
            childrenColumnName="none"
          />
          <div className="footer-ban-giao">
            <span className="total">
              {`${t("common.tong")}: ${state.selectedRowKeys.length} ${t(
                "quanLyNoiTru.khoan"
              )}`}
            </span>
            <span>{t("quanLyNoiTru.capPhatThuoc.quaTrinhSuDungThuoc")}</span>
            <Radio.Group onChange={onChangeRadio} defaultValue={"10"}>
              <div className="inline-flex text">
                <Radio value="10">
                  {t("quanLyNoiTru.capPhatThuoc.anToan")}
                </Radio>
              </div>
              <div className="inline-flex text">
                <Radio value="20">{t("common.khac")}</Radio>
              </div>
            </Radio.Group>
          </div>
        </Main>
      </ModalTemplate.Content>
      <ModalTemplate.Footer>
        <ModalTemplate.FooterLeft />
        <ModalTemplate.FooterRight>
          <Button onClick={onCancel}>{t("common.huy")}</Button>
          <Button onClick={onSubmit} type="primary">
            {renderTextButon}
          </Button>
        </ModalTemplate.FooterRight>
      </ModalTemplate.Footer>
    </ModalTemplate.Container>
  );
};

export default memo(Content);
