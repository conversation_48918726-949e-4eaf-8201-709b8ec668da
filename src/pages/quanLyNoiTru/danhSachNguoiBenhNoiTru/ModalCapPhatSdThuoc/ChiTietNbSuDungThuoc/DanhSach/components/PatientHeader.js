import React from "react";
import moment from "moment";
import classNames from "classnames";
import { ENUM } from "constants/index";
import { useEnum } from "hooks";
import { useCheckbox } from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ModalCapPhatSdThuoc/hooks/useCheckbox";
import { Checkbox } from "components";
import { SVG } from "assets";
import { t } from "i18next";

const PatientHeader = React.memo(
  ({ patient, onToggle, isExpandedPatient, listGioiTinh }) => {
    const {
      tenNb,
      ngaySinh,
      diaChi,
      tuoi2,
      maBenhAn,
      id,
      soHieuGiuong,
      tenPhong,
      tenBacSiDieuTri,
      maHoSo,
    } = patient || {};

    const gioiTinh = listGioiTinh?.find(
      (item) => item.id === patient?.dsThuoc?.[0]?.data?.[0]?.gioiTinh
    )?.ten;

    const _ngaySinh = moment(ngaySinh).format("DD/MM/YYYY");

    const renderInfoNb = (
      <div className="virt-patient-info">
        <CustomCheckbox id={id} onClick={(e) => e.stopPropagation()} />
        <div className="virt-patient-info__group">
          <div className="virt-patient-info__row">
            <span className="virt-patient-info__value virt-text--bold">
              {tenNb}
            </span>
            <div className="virt-patient-info__value">&nbsp;-&nbsp;</div>
            <div className="virt-flex virt-flex--gap-xs">
              <div>
                <span className="virt-patient-info__label">
                  {t("common.maHs")}:
                </span>
                <span className="virt-patient-info__value">&nbsp;{maHoSo}</span>
              </div>
              <div className="virt-patient-info__label">&nbsp;-&nbsp;</div>
              <div>
                <span className="virt-patient-info__label">
                  {t("common.maBa")}:
                </span>
                <span className="virt-patient-info__value">
                  &nbsp;{maBenhAn}
                </span>
              </div>
            </div>
            <div className="virt-patient-info__label">&nbsp;-&nbsp;</div>
            <span className="virt-patient-info__label">
              {gioiTinh}, {tuoi2}, {_ngaySinh}, {diaChi}
            </span>
          </div>
          <div className="virt-patient-info__row">
            <span className="virt-patient-info__label">
              {t("danhMuc.soHieuGiuong")}: {soHieuGiuong}
            </span>
            <span className="virt-patient-info__label">&nbsp;-&nbsp;</span>
            <span className="virt-patient-info__label">
              {t("tiepDon.tenPhong")}: {tenPhong}
            </span>
            <span className="virt-patient-info__label">&nbsp;-&nbsp;</span>
            <span className="virt-patient-info__label">
              {t("cdha.bacSiDieuTri")}: {tenBacSiDieuTri}
            </span>
          </div>
        </div>
      </div>
    );

    return (
      <div className="virt-patient-header" onClick={onToggle}>
        <div className="virt-patient-header__wrapper">
          <h1 className="virt-patient-header__content">{renderInfoNb}</h1>
          <SVG.IcArrowDown
            className={classNames("virt-patient-header__toggle", {
              "virt-patient-header__toggle--collapsed": !isExpandedPatient,
            })}
          />
        </div>
      </div>
    );
  }
);

const CustomCheckbox = ({ id, ...restProps }) => {
  const { checked, handleChange } = useCheckbox();

  return (
    <Checkbox
      onChange={(e) => {
        handleChange(e, id);
      }}
      checked={checked}
      {...restProps}
    />
  );
};

PatientHeader.displayName = "PatientHeader";

export default PatientHeader;
