import React, {
  useCallback,
  useEffect,
  useState,
  useRef,
  useMemo,
} from "react";
import { useDispatch } from "react-redux";
import { t } from "i18next";
import { Empty, Menu, message } from "antd";
import moment from "moment";
import { cloneDeep, groupBy, isNil, sumBy, orderBy, flatten } from "lodash";
import { useVirtualizer } from "@tanstack/react-virtual";
import { Button, Fallback } from "components";
import printProvider from "data-access/print-provider";
import { isArray, openInNewTab } from "utils/index";
import {
  LIST_PHIEU_IN_EDITOR,
  LOAI_DICH_VU,
  MA_BIEU_MAU_EDITOR,
} from "constants/index";
import ModalChonPhieuCongKhaiThuoc from "../ModalChonPhieuCongKhaiThuoc";
import { useLazyMemo, useLoading, useRefFunc, useStore } from "hooks";
import { isSameDay, POPUP_TYPE } from "../../config";
import ModalChonPhieuBanGiaoThuoc from "../ModalChonPhieuBanGiaoThuoc";
import { useCapPhatThuoc } from "../../hooks/useCapPhatThuoc";
import ModalBanGiaoThuoc from "../ModalBanGiaoThuoc";
import ModalChiTietCapPhat from "../ModalChiTietCapPhat";
import ModalThoiGianDungThuoc from "../TableThuoc/ModalThoiGianDungThuoc";
import ModalSuaThoiGian from "../TableThuoc/ModalSuaThoiGian";
import ModalNgungSDThuoc from "../ModalNgungSDThuoc";
import ModalChonKhoaPhieuTruyenDich from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ToDieuTri/modals/ModalChonKhoaPhieuTruyenDich";
import { checkIsPhieuKySo } from "utils/phieu-utils";
import ModalPatientSign from "pages/editor/report/components/ModalPatientSign";
import { PatientHeader, GroupHeader, MedicationRow } from "./components";
import { gopThuocKhacLo } from "utils/chi-dinh-thuoc-utils";
import { Main } from "./styled";

// Row types for virtual list
const ROW_TYPES = {
  PATIENT_HEADER: "patient-header",
  GROUP_HEADER: "group-header",
  MEDICATION_ROW: "medication-row",
  LOADING: "loading",
};

const DanhSach = () => {
  const {
    state: parentState,
    dataFilter,
    modalState,
    refScrollContainer,
    refModalBanGiao,
    refModalChiTietCapPhat,
    refModalThoiGianDungThuoc,
    refModalSuaThoiGian,
    refModalNgungSDThuoc,
    refModalChonPhieuTruyenDich,
    calculateTime,
    configTime,
    refModalPatientSign,
    isTachDongCapPhatThuoc,
    getNhanVienById,
    dataMA_DUONG_DUNG_DICH_TRUYEN,
    listGioiTinh,
  } = useCapPhatThuoc();

  const { showLoading, hideLoading } = useLoading();

  const [state, _setState] = useState({
    fullLoad: false,
    listPhieu: [],
    expandedPatients: new Map(),
    expandedGroups: new Map(),
  });

  const setState = (data = {}) => _setState((state) => ({ ...state, ...data }));

  const {
    isLoading: firstLoading,
    totalElements,
    page,
    pagesData,
  } = useStore(
    "nbCapPhatThuoc",
    {},
    { fields: "isLoading,totalElements,page,pagesData" }
  );

  const {
    phieuIn: { getFilePhieuIn, showFileEditor },
    nbCapPhatThuoc: { onChangeInputSearch, getDsThuoc, getListPhieu },
  } = useDispatch();

  const refModalChonPhieuCongKhaiThuoc = useRef(null);
  const refModalChonPhieuBanGiaoThuoc = useRef(null);

  const togglePatientExpand = useCallback((patientId, expand) => {
    _setState((prev) => ({
      ...prev,
      expandedPatients: new Map(prev.expandedPatients).set(patientId, expand),
    }));
  }, []);

  const toggleGroupExpand = useCallback((groupId) => {
    _setState((prev) => ({
      ...prev,
      expandedGroups: new Map(prev.expandedGroups).set(
        groupId,
        !(prev.expandedGroups.get(groupId) ?? true)
      ),
    }));
  }, []);

  useEffect(() => {
    setState({
      expandedPatients: new Map(),
      expandedGroups: new Map(),
    });
  }, [modalState.popupType]);

  const renderTxtBuoi = useCallback(
    (key) => {
      let _dataThietLap = "";
      let _defaultTxt = "";

      switch (key) {
        case "sang":
          _dataThietLap = configTime.SANG;
          _defaultTxt = t("quanLyNoiTru.sang");
          break;
        case "chieu":
          _dataThietLap = configTime.CHIEU;
          _defaultTxt = t("quanLyNoiTru.chieu");
          break;
        case "toi":
          _dataThietLap = configTime.TOI;
          _defaultTxt = t("quanLyNoiTru.toi");
          break;
        case "dem":
          _dataThietLap = configTime.DEM;
          _defaultTxt = t("quanLyNoiTru.dem");
          break;
        default:
          break;
      }
      if (!_dataThietLap) return _defaultTxt;
      const _strArr = _dataThietLap.split("/");
      if (_strArr.length === 3) {
        return _strArr[2].upperCaseFirstLetter();
      }
      return _defaultTxt;
    },
    [configTime]
  );

  const sortMedications = useCallback((medications) => {
    if (!medications || !medications.length) return medications;

    // Ưu  tiên 1: sttHienThi (BS chỉnh sửa vị trí) - null sắp cuối
    // Ưu tiên 2: sttDuongDung (0,1,2..., null) - null sắp cuối
    // Ưu tiên 3: thoiGianChiDinh (tăng dần theo thời gian chỉ định)
    return orderBy(
      medications,
      [
        (item) =>
          item.sttHienThi != null ? item.sttHienThi : Number.MAX_SAFE_INTEGER,
        (item) =>
          item.sttDuongDung != null
            ? item.sttDuongDung
            : Number.MAX_SAFE_INTEGER,
        (item) => moment(item.thoiGianChiDinh).valueOf(),
      ],
      ["asc", "asc", "asc"]
    );
  }, []);

  useEffect(() => {
    getListPhieu({
      maManHinh: "020",
      maViTri: "02001",
    }).then((res) => {
      const arr = res || [];

      arr.sort((a, b) => {
        if (a.stt == null && b.stt == null) return 0;
        if (a.stt == null) return 1;
        if (b.stt == null) return -1;
        return a.stt - b.stt;
      });

      setState({
        listPhieu: arr,
      });
    });
  }, []);

  const parseThoiGianSuDung = useCallback(
    (data) => {
      const isThuocDichTruyen = dataMA_DUONG_DUNG_DICH_TRUYEN.includes(
        data.maDuongDung
      );

      if (!data.dsThoiGianSuDung?.length) return {};

      const result = new Map();

      for (const [index, next] of data.dsThoiGianSuDung.entries()) {
        const {
          timeLine,
          time: tuThoiGian,
          numCol: colTuThoiGian,
        } = calculateTime(next.tuThoiGian);
        if (!timeLine) continue;

        const isBuoiDem = timeLine === "Đêm";

        let formattedTime = tuThoiGian;
        if (
          next.tuThoiGian &&
          !isSameDay(moment(next.tuThoiGian), moment(data.thoiGianThucHien))
        ) {
          const needsDateSuffix =
            !isSameDay(moment(next.tuThoiGian), moment(next.denThoiGian)) ||
            isBuoiDem;

          if (needsDateSuffix) {
            formattedTime += " " + moment(next.tuThoiGian).format("DD/MM");
          }
        }

        let denThoiGianData = null;
        let isMerge = false;

        if (isThuocDichTruyen) {
          const denThoiGian = next.denThoiGian ? moment(next.denThoiGian) : "";

          if (!denThoiGian) {
            denThoiGianData = { type: "arrow" };
          } else {
            const { numCol: colDenThoiGian } = calculateTime(next.denThoiGian);
            isMerge = colDenThoiGian - colTuThoiGian === 1;

            const isWithinSameDay =
              isSameDay(denThoiGian, moment(next.tuThoiGian)) &&
              isSameDay(denThoiGian, moment(data.thoiGianThucHien));

            const timeFormat =
              isWithinSameDay && !isBuoiDem
                ? denThoiGian.format("HH:mm")
                : denThoiGian.format("HH:mm DD/MM");

            denThoiGianData = { type: "arrowWithTime", time: timeFormat };
          }
        }

        const timeLineKey = timeLine.unsignText();
        const existingEntry = result.get(timeLineKey);

        result.set(timeLineKey, {
          data: [
            ...(existingEntry?.data || []),
            {
              timeLine,
              renderTime: {
                tuThoiGian: formattedTime,
                denThoiGian: denThoiGianData,
              },
              dieuDuongId: next.dieuDuongId,
              tuThoiGian: next.tuThoiGian,
              denThoiGian: next.denThoiGian,
              soLuong: next.soLuong,
              index: index,
            },
          ],
          isMerge: existingEntry?.isMerge || isMerge,
        });
      }

      return Object.fromEntries(result);
    },
    [calculateTime, dataMA_DUONG_DUNG_DICH_TRUYEN]
  );

  const convertData = useCallback(
    (inputData) => {
      if (!inputData || !inputData.length) return [];

      const dataMap = new Map();

      inputData.forEach((thuoc) => {
        if (thuoc.dsThoiGianSuDung?.length) {
          thuoc.dsThoiGianSuDungConfig = parseThoiGianSuDung(thuoc);
        }

        if (thuoc.children?.length) {
          thuoc.children = thuoc.children.map((child) => {
            if (child.dsThoiGianSuDung?.length) {
              child.dsThoiGianSuDungConfig = parseThoiGianSuDung(child);
            }

            return child;
          });
        }

        const key =
          thuoc.tachDong && isTachDongCapPhatThuoc
            ? `${thuoc.id}_${thuoc.stt}`
            : thuoc.id;

        dataMap.set(key, thuoc);
      });

      const groupedData = groupBy(
        [...dataMap.values()],
        (x) =>
          `${moment(x?.thoiGianThucHien).format("DD-MM-YYYY")} -  ${
            x?.tenKhoaChiDinh || ""
          }`
      );

      return Object.entries(groupedData).map((item, index) => {
        const sortedMedications = sortMedications(item[1]);

        return {
          key: String(index),
          data: sortedMedications.map((thuoc, index) => {
            thuoc.index = index + 1;
            return thuoc;
          }),
          title: item[0].trim(),
          index: index + 1,
          id: index,
        };
      });
    },
    [parseThoiGianSuDung, isTachDongCapPhatThuoc, sortMedications]
  );

  const generatePageData = useCallback(
    (data) => {
      if (!data || !data.length) return [];
      const _data = gopThuocKhacLo(data);

      if (isTachDongCapPhatThuoc) {
        const dataThuoc = [];

        const processTachDong = (dsThuocs, isChild = false) => {
          dsThuocs.forEach((thuoc) => {
            if (thuoc.tachDong) {
              const soLuongThuocTachDong = thuoc.soLan1Ngay || 1;

              for (let index = 0; index < soLuongThuocTachDong; index++) {
                const gioSuDung = (thuoc.dsThoiGianSuDung || []).find(
                  (x) => x.stt === index
                );
                const thuocClone = cloneDeep(thuoc);

                thuocClone.stt = index;
                thuocClone.dsThoiGianSuDungAll = thuoc.dsThoiGianSuDung;
                thuocClone.dsThoiGianSuDung = gioSuDung ? [gioSuDung] : [];

                const dieuDuongXacNhan = (thuoc.dsXacNhanDieuDuong || []).find(
                  (el) => el.stt === index
                );

                if (dieuDuongXacNhan) {
                  thuocClone.dieuDuongXacNhan = {
                    ...dieuDuongXacNhan,
                    taiKhoan: getNhanVienById(dieuDuongXacNhan?.dieuDuongId)
                      ?.taiKhoan,
                  };
                }

                if (index === 0) {
                  thuocClone.numberMergeRow = soLuongThuocTachDong;
                }

                if (!isChild) {
                  dataThuoc.push(thuocClone);
                }
              }

              if (thuoc.children?.length && !isChild) {
                processTachDong(thuoc.children, true);
              }
            } else {
              if (!isChild) {
                dataThuoc.push(thuoc);
              }
            }
          });
        };

        processTachDong(_data);
        return convertData(dataThuoc);
      } else {
        return convertData(cloneDeep(_data));
      }
    },
    [convertData, isTachDongCapPhatThuoc, getNhanVienById]
  );

  const { listDsThuoc } = useLazyMemo(
    (prev) => {
      let result = {};
      for (let key in pagesData) {
        if (prev?.pagesData?.[key] === pagesData[key]) {
          result[key] = prev?.result?.[key];
        } else {
          result[key] = pagesData[key].map((item) => ({
            ...item,
            dsThuoc: generatePageData(item.dsThuoc),
          }));
        }
      }

      return {
        pagesData,
        result,
        listDsThuoc: Object.values(result).reduce((acc, next) => {
          return [...acc, ...next];
        }, []),
      };
    },
    [pagesData, generatePageData]
  );

  const isExpandedPatient = useCallback(
    (patientId) => {
      const isShow = state.expandedPatients.get(patientId);
      if (!isNil(isShow)) return isShow;

      if (
        modalState.popupType === POPUP_TYPE.DS_CHI_TIET_BAN_GIAO_VA_SD_THUOC
      ) {
        return true;
      }
      return false;
    },
    [state.expandedPatients, modalState.popupType]
  );

  const isExpandedGroup = useCallback(
    (groupId) => {
      return state.expandedGroups.get(groupId) ?? true;
    },
    [state.expandedGroups]
  );

  const allPossibleItems = useMemo(() => {
    const items = [];

    listDsThuoc.forEach((patient) => {
      items.push({
        type: ROW_TYPES.PATIENT_HEADER,
        id: `patient-${patient.id}`,
        data: patient,
        patientId: patient.id,
      });

      if (patient.dsThuoc) {
        patient.dsThuoc.forEach((group) => {
          const groupId = `${patient.id}-${group.id}`;

          items.push({
            type: ROW_TYPES.GROUP_HEADER,
            id: groupId,
            data: { ...group, patient },
            patientId: patient.id,
            groupId,
          });

          if (group.data) {
            group.data.forEach((medication, index) => {
              medication.children = Object.entries(
                groupBy(medication.children, "chiDinhId")
              ).map(([chiDinhId, items]) => {
                return {
                  ...items[0],
                  soLuong: sumBy(items, "soLuong"),
                  soLuongHuy: sumBy(items, "soLuongHuy"),
                  soLuongYeuCau: sumBy(items, "soLuongYeuCau"),
                  soLuongYeuCauTra: sumBy(items, "soLuongYeuCauTra"),
                  soLuongTra: sumBy(items, "soLuongTra"),
                  soLuongSoCap: sumBy(items, "soLuongSoCap"),
                };
              });
              const childrenCount = medication.children?.length || 0;
              const isParent = childrenCount > 0;

              items.push({
                type: ROW_TYPES.MEDICATION_ROW,
                id: `${groupId}-med-${index}`,
                groupId,
                patientId: patient.id,
                data: {
                  medication,
                  group,
                  patient,
                  medicationIndex: index,
                  isParent,
                  childrenCount,
                },
              });

              if (medication.children?.length) {
                medication.children.forEach((child, childIndex) => {
                  items.push({
                    type: ROW_TYPES.MEDICATION_ROW,
                    id: `${groupId}-med-${index}-child-${childIndex}`,
                    groupId,
                    patientId: patient.id,
                    data: {
                      medication: child,
                      group,
                      patient,
                      medicationIndex: index,
                      isChild: true,
                      parentMedicationId: medication.id,
                    },
                  });
                });
              }
            });
          }
        });
      }
    });

    return items;
  }, [listDsThuoc]);

  const flattenedItems = useMemo(() => {
    const filteredItems = allPossibleItems.filter((item) => {
      if (item.type === ROW_TYPES.PATIENT_HEADER) {
        return true;
      }

      if (item.type === ROW_TYPES.GROUP_HEADER) {
        const isPatientExpanded = isExpandedPatient(item.patientId);
        return isPatientExpanded;
      }

      if (item.type === ROW_TYPES.MEDICATION_ROW) {
        const isPatientExpanded = isExpandedPatient(item.patientId);
        const isGroupExpanded = isExpandedGroup(item.groupId);
        return isPatientExpanded && isGroupExpanded;
      }

      return true;
    });

    if (!state.fullLoad && listDsThuoc.length < totalElements) {
      filteredItems.push({
        type: ROW_TYPES.LOADING,
        id: "loading",
        fixedHeight: 60,
      });
    }

    return filteredItems;
  }, [
    allPossibleItems,
    isExpandedPatient,
    isExpandedGroup,
    state.fullLoad,
    listDsThuoc.length,
    totalElements,
  ]);

  const virtualizer = useVirtualizer({
    count: flattenedItems.length,
    getScrollElement: () => refScrollContainer.current,

    estimateSize: (index) => {
      const item = flattenedItems[index];
      if (!item) return 50;

      if (item.fixedHeight) {
        return item.fixedHeight;
      }

      switch (item.type) {
        case ROW_TYPES.PATIENT_HEADER:
          return 65;
        case ROW_TYPES.GROUP_HEADER:
          return 50;
        case ROW_TYPES.MEDICATION_ROW:
          return item.data?.isChild ? 45 : 60;
        default:
          return 50;
      }
    },
    overscan: 5,
    measureElement: (element) => {
      if (!element) return 0;
      return element.getBoundingClientRect().height;
    },
  });

  const loadMore = useCallback(() => {
    if (!state.fullLoad) {
      let fullLoad = false;
      setState({ loading: true });
      getDsThuoc({ page: page + 1 })
        .then((s) => {
          fullLoad = s.pageNumber >= s.totalPages - 1;
        })
        .finally(() => setState({ fullLoad, loading: false }));
    }
  }, [page]);

  useEffect(() => {
    if (parentState.initStatus === "done") {
      onChangeInputSearch({
        page: 0,
        size: 10,
        ...dataFilter,
        maBenhAn: parentState.maBenhAn,
      });
    }
  }, [parentState.initStatus]);

  const onPrintPhieu = useRefFunc((item, data) => async () => {
    const { id, thoiGianThucHien, allThuoc, selectedIds, listData } =
      data || {};

    if (
      (item?.ma === "P1214" || item?.ma === "P1215") &&
      !isArray(selectedIds, true)
    ) {
      message.error(
        t("quanLyNoiTru.capPhatThuoc.vuiLongChonThuocDeInPhieu", {
          phieu: item.ten,
        })
      );
      return;
    }

    let dsThuoc = selectedIds.reduce((acc, cur) => {
      let currentItem = allThuoc?.find((item) => item.id === cur);
      if (currentItem) {
        acc.push({
          id: currentItem?.id,
          loai: currentItem?.loai,
        });
      }
      return acc;
    }, []);

    let idxChildSelected = flatten(listData.map((x) => x.children)).findIndex(
      (item) => selectedIds.includes(item.id)
    );

    if (LIST_PHIEU_IN_EDITOR.includes(item?.ma)) {
      let mhParams = {};
      //kiểm tra phiếu ký số
      if (checkIsPhieuKySo(item)) {
        //nếu là ký số thì lấy các thông tin về lịch sử ký + tên chân ký + báo cáo id
        mhParams = {
          nbDotDieuTriId: id,
          maManHinh: "020",
          maViTri: "02001",
          // chiDinhTuDichVuId: id,
          dsChiDinhTuLoaiDichVu: [
            LOAI_DICH_VU.TO_DIEU_TRI,
            LOAI_DICH_VU.NHA_THUOC,
          ],
          kySo: true,
          maPhieuKy: item.ma,
          baoCaoId: item.baoCaoId,
        };
      }
      const lichSuKyId = item?.dsSoPhieu?.length
        ? item?.dsSoPhieu[0].lichSuKyId
        : "";
      if (item.ma == "P183") {
        refModalChonPhieuCongKhaiThuoc.current &&
          refModalChonPhieuCongKhaiThuoc.current.show(
            {
              nbDotDieuTriId: id,
              khoaChiDinhId: parentState.dsKhoaChiDinhId,
              readOnlyNb: true,
            },
            (filterData) => {
              showFileEditor({
                phieu: item,
                id: filterData?.id,
                nbDotDieuTriId: filterData?.nbDotDieuTriId,
                ma: item.ma,
                khoaChiDinhId: filterData?.khoaChiDinhId,
                tuThoiGian: filterData?.tuThoiGian,
                denThoiGian: filterData?.denThoiGian,
                dsLoaiChiDinh: filterData?.dsLoaiChiDinh,
                lichSuKyId,
                dsChiDinhTuLoaiDichVu: [
                  LOAI_DICH_VU.TO_DIEU_TRI,
                  LOAI_DICH_VU.NHA_THUOC,
                ],
              });
            }
          );
      } else if (item.ma == "P181" || item.ma == "P191") {
        refModalChonPhieuBanGiaoThuoc.current &&
          refModalChonPhieuBanGiaoThuoc.current.show(
            {
              nbDotDieuTriId: id,
              khoaChiDinhId: parentState.dsKhoaChiDinhId,
              readOnlyNb: true,
              thoiGianThucHien,
              dsSoPhieu: item.dsSoPhieu,
            },
            (filterData) => {
              showFileEditor({
                phieu: item,
                id: filterData?.nbDotDieuTriId,
                nbDotDieuTriId: filterData?.nbDotDieuTriId,
                ma: item.ma,
                khoaChiDinhId: filterData?.khoaChiDinhId,
                thoiGianYLenh: filterData?.thoiGianYLenh,
                thoiGianThucHien: moment(filterData?.thoiGianYLenh)
                  .set({
                    hours: 0,
                    minutes: 0,
                    seconds: 0,
                    milliseconds: 0,
                  })
                  .format(),
              });
            }
          );
      } else if (item.ma === "P086") {
        refModalChonPhieuTruyenDich.current &&
          refModalChonPhieuTruyenDich.current.show(
            {
              khoaChiDinhId: parentState.dsKhoaChiDinhId,
            },
            (filterData) => {
              showFileEditor({
                phieu: item,
                id: filterData?.id,
                nbDotDieuTriId: id,
                ma: item.ma,
                maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                  ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                  : "",
                // chiDinhTuDichVuId: currentToDieuTri.id,
                chiDinhTuLoaiDichVu: [
                  LOAI_DICH_VU.TO_DIEU_TRI,
                  LOAI_DICH_VU.NHA_THUOC,
                ],
                khoaChiDinhId: filterData?.khoaChiDinhId,
                lichSuKyId,
                ...mhParams,
              });
            }
          );
      } else {
        showFileEditor({
          phieu: item,
          nbDotDieuTriId: id,
          khoaChiDinhId: parentState.dsKhoaChiDinhId,
          tuThoiGian: null,
          denThoiGian: null,
          lichSuKyId,
        });
      }
    } else {
      try {
        showLoading();
        const { finalFile, dsPhieu } = await getFilePhieuIn({
          listPhieus: [item],
          showError: true,
          ...(["P1214", "P1215"].includes(item?.ma) && {
            nbDotDieuTriId: id,
            dsThuoc,
            maViTri: "02001",
            maPhieuIn: item.ma,
            dungKem: idxChildSelected > -1,
          }),
        });
        if ((dsPhieu || []).every((x) => x?.loaiIn == 20)) {
          openInNewTab(finalFile);
        } else {
          printProvider.printPdf(dsPhieu);
        }
      } catch (error) {
      } finally {
        hideLoading();
      }
    }
  });

  const contentPrint = useCallback(
    (data) => (
      <Menu
        items={(state?.listPhieu || []).map((item, index) => ({
          key: index,
          label: (
            <a href={() => false} onClick={onPrintPhieu(item, data)}>
              {item.ten || item.tenBaoCao}
            </a>
          ),
        }))}
      />
    ),
    [state.listPhieu]
  );

  const renderVirtualItem = useCallback(
    (virtualItem) => {
      const item = flattenedItems[virtualItem.index];
      if (!item) return null;

      const patientId = item.patientId || (item.data && item.data.id);
      switch (item.type) {
        case ROW_TYPES.PATIENT_HEADER:
          const isExpanded = isExpandedPatient(patientId);
          return (
            <PatientHeader
              key={item.id}
              patient={item.data}
              onToggle={() => togglePatientExpand(patientId, !isExpanded)}
              isExpandedPatient={isExpanded}
              listGioiTinh={listGioiTinh}
            />
          );

        case ROW_TYPES.GROUP_HEADER:
          return (
            <GroupHeader
              key={item.id}
              group={item.data}
              patientId={patientId}
              onToggle={() => toggleGroupExpand(item.id)}
              contentPrint={contentPrint}
              renderTxtBuoi={renderTxtBuoi}
              isExpandedGroup={isExpandedGroup(item.id)}
            />
          );

        case ROW_TYPES.MEDICATION_ROW:
          return (
            <MedicationRow
              key={item.id}
              data={item.data}
              patientId={patientId}
              renderTxtBuoi={renderTxtBuoi}
              calculateTime={calculateTime}
              rowIndex={virtualItem.index}
              childrenCount={item.data.childrenCount || 0}
              isParent={item.data.isParent || false}
            />
          );

        case ROW_TYPES.LOADING:
          return (
            <div key={item.id} className="virt-loading">
              <Button
                className="virt-loading__button"
                disabled={state.loading}
                onClick={loadMore}
              >
                {state.loading
                  ? t("quanLyNoiTru.capPhatThuoc.dangTaiThemDuLieu")
                  : t("quanLyNoiTru.capPhatThuoc.taiThem")}
              </Button>
            </div>
          );

        default:
          return null;
      }
    },
    [
      flattenedItems,
      togglePatientExpand,
      toggleGroupExpand,
      contentPrint,
      renderTxtBuoi,
      calculateTime,
      loadMore,
      state.loading,
      isExpandedPatient,
      isExpandedGroup,
    ]
  );

  return (
    <Main noPadding={true}>
      {firstLoading && <Fallback />}
      {!listDsThuoc?.length ? (
        <Empty
          style={{
            padding: "100px 0",
          }}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      ) : (
        <div
          style={{
            height: "100%",
          }}
        >
          <div
            className="virtualizer-container"
            style={{
              height: `${virtualizer.getTotalSize()}px`,
              width: "100%",
              position: "relative",
            }}
          >
            {virtualizer.getVirtualItems().map((virtualItem) => {
              const item = flattenedItems[virtualItem.index];
              if (!item) return null;

              const itemClassName = `virtual-item ${item.type}${
                item.data?.isChild ? " child-item" : ""
              }`;

              return (
                <div
                  key={virtualItem.key}
                  data-index={virtualItem.index}
                  data-type={item.type}
                  className={itemClassName}
                  ref={virtualizer.measureElement}
                  style={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    width: "100%",
                    transform: `translateY(${virtualItem.start}px)`,
                    ...(item?.fixedHeight
                      ? { height: `${virtualItem.size}px` }
                      : {}),
                  }}
                >
                  {renderVirtualItem(virtualItem)}
                </div>
              );
            })}
          </div>
        </div>
      )}

      <ModalChonPhieuCongKhaiThuoc ref={refModalChonPhieuCongKhaiThuoc} />
      <ModalChonKhoaPhieuTruyenDich ref={refModalChonPhieuTruyenDich} />
      <ModalChonPhieuBanGiaoThuoc ref={refModalChonPhieuBanGiaoThuoc} />
      <ModalBanGiaoThuoc ref={refModalBanGiao} />
      <ModalChiTietCapPhat ref={refModalChiTietCapPhat} />
      <ModalThoiGianDungThuoc ref={refModalThoiGianDungThuoc} />
      <ModalSuaThoiGian ref={refModalSuaThoiGian} />
      <ModalNgungSDThuoc ref={refModalNgungSDThuoc} />
      <ModalPatientSign ref={refModalPatientSign} />
    </Main>
  );
};

export default DanhSach;
