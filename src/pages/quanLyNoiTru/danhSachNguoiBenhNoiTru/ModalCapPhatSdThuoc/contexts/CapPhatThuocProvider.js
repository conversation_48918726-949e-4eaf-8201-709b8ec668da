import React, {
  createContext,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { query } from "redux-store/stores";
import {
  CACHE_KEY,
  DS_TINH_CHAT_KHOA,
  ENUM,
  THIET_LAP_CHUNG,
} from "constants/index";
import {
  useConfirm,
  useEnum,
  useLazyKVMap,
  useLoading,
  useQueryAll,
  useScale,
  useStore,
  useThietLap,
} from "hooks";
import moment from "moment";
import { message } from "antd";
import { LIST_TRANG_THAI_THUOC } from "../config";
import { ModalTemplate } from "components";
import cacheUtils from "lib-utils/cache-utils";
import { cleanEmptyData, isArray, safeConvertToArray } from "utils/index";
import { cloneDeep, size } from "lodash";
import { useConfigTime } from "../hooks/useConfigTime";
import { LOAI_DICH_VU } from "constants/index";
import { toSafePromise } from "lib-utils";
import { calculateInfusionTime } from "../utils";

export const CapPhatThuocContext = createContext(null);

const dsTrangThaiDefault = [
  "DA_PHAT",
  "DA_BAN_GIAO",
  "DA_SU_DUNG",
  "NGUNG_SU_DUNG",
];

const initialState = {
  tuThoiGianThucHien: moment().startOf("date"),
  denThoiGianThucHien: moment().endOf("date"),
  dsTrangThai: [...dsTrangThaiDefault],
  isCheckedAll: false,
  dsTrangThaiHoan: [0, 10, 20],
  selectedRowKeysByGroup: {},
};

export const CapPhatThuocProvider = ({ children }) => {
  const { t } = useTranslation();
  const { onScale, onUnscale } = useScale();

  const {
    data,
    onCloseModal,
    setState: setModalState,
    ...modalState
  } = ModalTemplate.useModal();

  const [state, _setState] = useState(initialState);

  const setState = (data = {}) => _setState((state) => ({ ...state, ...data }));

  const { showAsyncConfirm, showConfirm } = useConfirm();
  const { showLoading, hideLoading } = useLoading();
  const nhanVienId = useStore("auth.auth.nhanVienId");
  const authId = useStore("auth.auth.id");

  const {
    nbCapPhatThuoc: {
      putThoiGianSuDungThuoc,
      postHuyBanGiaoThuoc,
      postHuySuDungThuoc,
      getDsThuoc,
      putdieuDuongXacNhan,
      postHuyNgungSDThuoc,
    },
  } = useDispatch();

  //refs
  const refScrollContainer = useRef(null);
  const refModalBanGiao = useRef(null);
  const refModalChiTietCapPhat = useRef(null);
  const refModalThoiGianDungThuoc = useRef(null);
  const refModalSuaThoiGian = useRef(null);
  const refModalNgungSDThuoc = useRef(null);
  const refModalChonPhieuTruyenDich = useRef(null);
  const refModalPatientSign = useRef(null);

  const [getTrangThaiThuoc] = useLazyKVMap(LIST_TRANG_THAI_THUOC);
  const [listGioiTinh] = useEnum(ENUM.GIOI_TINH);

  const getTenThuocById = useMemo(() => {
    const kvMap = new Map();
    LIST_TRANG_THAI_THUOC.forEach((item) => {
      item.refIds.forEach((id) => {
        kvMap.set(id, t(item.i18n));
      });
    });
    return (id) => kvMap.get(id);
  }, []);

  const [dataMA_DUONG_DUNG_DICH_TRUYEN] = useThietLap(
    THIET_LAP_CHUNG.MA_DUONG_DUNG_DICH_TRUYEN
  );

  const [dataTHOI_GIAN_VA_TEN_BUOI_SANG] = useThietLap(
    THIET_LAP_CHUNG.THOI_GIAN_VA_TEN_BUOI_SANG
  );
  const [dataTHOI_GIAN_VA_TEN_BUOI_CHIEU] = useThietLap(
    THIET_LAP_CHUNG.THOI_GIAN_VA_TEN_BUOI_CHIEU
  );
  const [dataTHOI_GIAN_VA_TEN_BUOI_TOI] = useThietLap(
    THIET_LAP_CHUNG.THOI_GIAN_VA_TEN_BUOI_TOI
  );
  const [dataTHOI_GIAN_VA_TEN_BUOI_DEM] = useThietLap(
    THIET_LAP_CHUNG.THOI_GIAN_VA_TEN_BUOI_DEM
  );
  const [dataTACH_DONG_CAP_PHAT_THUOC] = useThietLap(
    THIET_LAP_CHUNG.TACH_DONG_CAP_PHAT_THUOC
  );
  // const [dataKY_CAP_PHAT_THUOC] = useThietLap(
  //   THIET_LAP_CHUNG.KY_CAP_PHAT_THUOC
  // );

  const dataKY_CAP_PHAT_THUOC = "False";

  const { data: listAllNhanVien } = useQueryAll(
    query.nhanVien.queryAllNhanVien
  );

  const { data: listKhoTheoTaiKhoan } = useQueryAll(
    query.kho.queryKhoTheoTaiKhoan
  );

  const { data: listKhoaTheoTaiKhoan } = useQueryAll(
    query.khoa.queryKhoaTheoTaiKhoan({
      params: {
        dsTinhChatKhoa: DS_TINH_CHAT_KHOA.NOI_TRU,
      },
    })
  );

  const [getNhanVienById] = useLazyKVMap(listAllNhanVien);

  const { calculateTime, configTime } = useConfigTime({
    dataTHOI_GIAN_VA_TEN_BUOI_SANG,
    dataTHOI_GIAN_VA_TEN_BUOI_CHIEU,
    dataTHOI_GIAN_VA_TEN_BUOI_TOI,
    dataTHOI_GIAN_VA_TEN_BUOI_DEM,
  });

  const isTachDongCapPhatThuoc = dataTACH_DONG_CAP_PHAT_THUOC.eval();

  useEffect(() => {
    onScale();
    return () => {
      onUnscale();
    };
  }, []);

  useEffect(() => {
    const initData = async () => {
      const { dsTinhChatKhoa, maHoSo, maBenhAn } = data;

      const khoaLamViec = await cacheUtils.read(
        authId + dsTinhChatKhoa,
        CACHE_KEY.DATA_KHOA_LAM_VIEC,
        null,
        false,
        false
      );
      setState({
        khoaLamViec,
        chiDinhTuToDieuTri: true,
        dsKhoaChiDinhId: khoaLamViec?.id,
        ...(window.location.pathname.includes(
          "/quan-ly-noi-tru/chi-tiet-nguoi-benh-noi-tru"
        ) && { maHoSo, maBenhAn }),
        initStatus: "done",
      });
    };
    initData();
  }, []);

  // use to parse state to params
  const dataFilter = useMemo(() => {
    return cleanEmptyData({
      maNb: state.maNb,
      tenNb: state.tenNb,
      maHoSo: state.maHoSo,
      maThe: state.maThe,
      maBenhAn: state.maBenhAn,
      dsKhoaChiDinhId: state.dsKhoaChiDinhId
        ? safeConvertToArray(state.dsKhoaChiDinhId)
        : null,
      dsTrangThai: state.dsTrangThai.flatMap(
        (item) => getTrangThaiThuoc(item)?.refIds
      ),
      dsKhoId: state.dsKhoId,
      loaiDuongDung: state.loaiDuongDung,
      dsDichVuId: state.dsDichVuId,
      tuThoiGianThucHien: moment.isMoment(state.tuThoiGianThucHien)
        ? moment(state.tuThoiGianThucHien)
            .startOf("date")
            .format("YYYY-MM-DD HH:mm:ss")
        : state.tuThoiGianThucHien,
      denThoiGianThucHien: moment.isMoment(state.denThoiGianThucHien)
        ? moment(state.denThoiGianThucHien)
            .endOf("date")
            .format("YYYY-MM-DD HH:mm:ss")
        : state.denThoiGianThucHien,
      dsPhongId: state.dsPhongId,
      dsBacSiDieuTriId: state.dsBacSiDieuTriId,
      dsTrangThaiHoan: state.dsTrangThaiHoan,
      dsChiDinhTuLoaiDichVu: state.chiDinhTuToDieuTri
        ? [LOAI_DICH_VU.TO_DIEU_TRI, LOAI_DICH_VU.NHA_THUOC]
        : null,
      dsLoaiChiDinh: state.dsLoaiChiDinh,
    });
  }, [state]);

  const onBanGiaoThuoc = useCallback(
    ({ data, event, page }) =>
      () => {
        refModalBanGiao.current &&
          refModalBanGiao.current.show({ data, event, page });
      },
    []
  );

  const onHuyBanGiao = useCallback(
    ({ data, page }) =>
      async () => {
        const res = await showAsyncConfirm({
          title: t("quanLyNoiTru.huyBanGiaoThuoc"),
          content: t("quanLyNoiTru.xacNhanHuyBanGiaoThuoc"),
          cancelText: t("common.huy"),
          okText: t("common.dongY"),
          showBtnOk: true,
          typeModal: "warning",
        });
        if (res.action === "ok") {
          showLoading();

          const payload = size(data.dsThuocGop)
            ? data.dsThuocGop.map(({ loai, id }) => ({ loai, id }))
            : [{ loai: data.loai, id: data.id }];

          if (data.children?.length) {
            const thuocDungKemPayload = data.children.map(({ loai, id }) => ({
              loai,
              id,
            }));
            payload.push(...thuocDungKemPayload);
          }

          await toSafePromise(postHuyBanGiaoThuoc({ page, payload }));
          hideLoading();
          return true;
        }
        return false;
      },
    []
  );

  // function return true nếu hủy thành công, false nếu không xác nhận huỷ
  const onHuySuDungThuoc = useCallback(
    ({ data, page }) =>
      async () => {
        const res = await showAsyncConfirm({
          title: t("quanLyNoiTru.huyHoanThanh"),
          content: t("quanLyNoiTru.xacNhanHuyHoanThanhSuDungThuoc"),
          cancelText: t("common.huy"),
          okText: t("common.dongY"),
          showBtnOk: true,
          typeModal: "warning",
        });
        if (res.action === "ok") {
          showLoading();
          const payload = size(data.dsThuocGop)
            ? data.dsThuocGop.map(({ loai, id }) => ({ loai, id }))
            : [{ loai: data.loai, id: data.id }];

          if (data.children?.length) {
            const thuocDungKemPayload = data.children.map(({ loai, id }) => ({
              loai,
              id,
            }));
            payload.push(...thuocDungKemPayload);
          }

          await toSafePromise(postHuySuDungThuoc({ page, payload }));
          hideLoading();
          return true;
        }
        return false;
      },
    []
  );

  const onNgungSDThuoc = useCallback(
    ({ data, page }) =>
      () => {
        refModalNgungSDThuoc.current &&
          refModalNgungSDThuoc.current.show({ data }, () => {
            getDsThuoc({ page });
          });
      },
    []
  );

  const onHuyNgungSDThuoc = useCallback(
    ({ data, page }) =>
      async () => {
        const res = await showAsyncConfirm({
          title: t("quanLyNoiTru.huyNgungSuDungThuoc"),
          content: t("quanLyNoiTru.xacNhanHuyNgungSDThuoc"),
          cancelText: t("common.huy"),
          okText: t("common.dongY"),
          showBtnOk: true,
          typeModal: "warning",
        });
        if (res.action === "ok") {
          showLoading();

          const payload = size(data.dsThuocGop)
            ? data.dsThuocGop.map(({ loai, id }) => ({ loai, id }))
            : [{ loai: data.loai, id: data.id }];

          await toSafePromise(postHuyNgungSDThuoc({ page, payload }));
          hideLoading();
          return true;
        }
        return false;
      },
    []
  );

  const onEditThoiGian = useCallback(
    ({ list, page }) =>
      () => {
        const {
          dsThoiGianSuDung,
          id,
          loai,
          thoiGianYLenh,
          thoiGianThucHien,
          maDuongDung,
          soLan1Ngay,
          tenDonViTinh,
          tenDvtSuDung,
          children = [],
          soLuong1Lan,
          tenDvtSoCap,
          // Additional infusion parameters
          tocDoTruyen,
          donViTocDoTruyen,
          soGiot,
          cachGio,
          dungTich,
          thoiGianBatDau,
        } = list || {};
        let donVi = loai === 60 ? tenDonViTinh : tenDvtSuDung;

        refModalSuaThoiGian.current &&
          refModalSuaThoiGian.current.show(
            {
              data: dsThoiGianSuDung,
              thoiGianYLenh,
              isThuocDichTruyen:
                dataMA_DUONG_DUNG_DICH_TRUYEN.includes(maDuongDung),
              soLan1Ngay,
              donVi,
              thoiGianThucHien,
              soLuong1Lan,
              tenDonViTinh,
              tenDvtSoCap,
              tenDvtSuDung,
              // Additional infusion parameters
              tocDoTruyen,
              donViTocDoTruyen,
              soGiot,
              cachGio,
              dungTich,
              thoiGianBatDau,
            },
            (data) => {
              let dsThoiGianSuDung = data;
              if (list.tachDong && isTachDongCapPhatThuoc) {
                if (dsThoiGianSuDung?.length) {
                  dsThoiGianSuDung = list.dsThoiGianSuDungAll.map((el) => {
                    const thuoc = data.find((x) => x.stt === el.stt);
                    return thuoc?.stt === el.stt ? thuoc : el;
                  });
                } else {
                  dsThoiGianSuDung = list.dsThoiGianSuDungAll.filter(
                    (el) => el.stt !== list.stt
                  );
                }
              }

              showLoading();
              putThoiGianSuDungThuoc({
                payload: [
                  {
                    id,
                    loai,
                    dsThoiGianSuDung: dsThoiGianSuDung.map((item) => {
                      const { loaiThoiGianSuDung } = calculateTime(
                        item.tuThoiGian
                      );
                      return {
                        ...item,
                        loaiThoiGianSuDung:
                          loaiThoiGianSuDung ?? item.loaiThoiGianSuDung,
                      };
                    }),
                  },
                ],
                page,
              }).finally(() => {
                hideLoading();
              });
            }
          );
      },
    [dataMA_DUONG_DUNG_DICH_TRUYEN, calculateTime, isTachDongCapPhatThuoc]
  );

  const validateInputTime = useCallback(
    (inputTime) => {
      const { timeLine: inputTimeLine } = calculateTime(inputTime);
      if (!inputTimeLine) {
        message.error(t("quanLyNoiTru.thoiGianNhapKhongHopLe"));
        return null;
      }
      return inputTimeLine;
    },
    [calculateTime, t]
  );

  const calculateTimeEnd = useCallback(
    (
      inputTime,
      isThuocDichTruyen,
      soLuong1Lan,
      tocDoTruyen,
      donViTocDoTruyen,
      soGiot
    ) => {
      if (!isThuocDichTruyen) return null;
      const duration = calculateInfusionTime(
        soLuong1Lan,
        tocDoTruyen,
        donViTocDoTruyen,
        soGiot
      );
      return moment(inputTime)
        .add(duration, "minutes")
        .format("YYYY-MM-DD HH:mm:ss");
    },
    []
  );

  const checkUsageLimit = useCallback(
    (dsThoiGianSuDung, soLan1Ngay, isUpdate = false) => {
      if (isUpdate) return false;
      const soLanDieuDuongNhap = dsThoiGianSuDung.filter(
        (item) => item.dieuDuongId
      ).length;
      if (soLan1Ngay && soLanDieuDuongNhap >= soLan1Ngay) {
        message.error(
          t(
            "quanLyNoiTru.capPhatThuoc.thuocChiDuocNhapSuDungToiDaTitleLanNgay",
            { title: soLan1Ngay }
          )
        );
        return true;
      }
      return false;
    },
    [t]
  );

  const calculateSoLuongNhap = useCallback((data, isParent, keySoLuong) => {
    const tongThoiGianSd = (data.dsThoiGianSuDung || []).reduce(
      (acc, cur) => acc + (cur?.soLuong || 0),
      0
    );
    const soLuong = isParent
      ? data[keySoLuong] || data.soLuong1Lan
      : data.soLuong1Lan;
    const soLuongNhapConLai = (data.soLuong || 0) - tongThoiGianSd;

    // Trường hợp tenDonViTinh = tenDvtSoCap và khác tenDvtSuDung thì sử dụng slSang slChieu slToi slDem
    if (
      data.tenDonViTinh === data.tenDvtSoCap &&
      data.tenDonViTinh !== data.tenDvtSuDung
    ) {
      return data[keySoLuong];
    }

    return soLuongNhapConLai <= 0 ? null : Math.min(soLuongNhapConLai, soLuong);
  }, []);

  const processDsThoiGianSuDung = useCallback(
    ({
      record,
      checkInRange,
      item,
      isInRange,
      inputTimeLine,
      thoiGian,
      isThuocDichTruyen,
      soLuongNhap,
      tocDoTruyen,
      donViTocDoTruyen,
      soGiot,
      nhanVienId,
      stt,
      isTachDongCapPhatThuoc,
    }) => {
      const cloneDeepRecord = cloneDeep(record);
      if (cloneDeepRecord.tachDong && isTachDongCapPhatThuoc) {
        cloneDeepRecord.dsThoiGianSuDung = cloneDeepRecord.dsThoiGianSuDungAll;
      }

      const shouldAddNewItem =
        !checkInRange ||
        (item && !isInRange && item.timeLine !== inputTimeLine);

      const duration = calculateInfusionTime(
        soLuongNhap,
        tocDoTruyen,
        donViTocDoTruyen,
        soGiot
      );

      const newItem = shouldAddNewItem
        ? [
            {
              tuThoiGian: thoiGian,
              denThoiGian: isThuocDichTruyen
                ? duration
                  ? moment(thoiGian)
                      .add(duration, "minutes")
                      .format("YYYY-MM-DD HH:mm:ss")
                  : null
                : null,
              dieuDuongId: nhanVienId,
              soLuong: soLuongNhap,
              stt,
            },
          ]
        : [];

      return [...(cloneDeepRecord.dsThoiGianSuDung || []), ...newItem].map(
        (item) => {
          const { loaiThoiGianSuDung } = calculateTime(item.tuThoiGian);
          return {
            ...item,
            loaiThoiGianSuDung: loaiThoiGianSuDung ?? item.loaiThoiGianSuDung,
          };
        }
      );
    },
    [calculateTime, cloneDeep]
  );

  // item là item trong dsThoiGianSuDung,data là rowData
  const onChangeThoiGianSdThuoc = useCallback(
    ({ item, data, page, defaultTime }) =>
      (e) => {
        e.stopPropagation();

        const isThuocDichTruyen = dataMA_DUONG_DUNG_DICH_TRUYEN.includes(
          data.maDuongDung
        );

        const {
          soLan1Ngay,
          dsThoiGianSuDung: _dsThoiGianSuDung,
          children = [],
          stt,
          soLuong1Lan,
          tocDoTruyen,
          donViTocDoTruyen,
          soGiot,
          ...list
        } = data;

        const dsThoiGianSuDung = _dsThoiGianSuDung || [];
        const { timeLine, time } = calculateTime(list?.thoiGianYLenh);

        // defaultTime là thoiGianBatDau bác sĩ kê
        const inputTime = defaultTime
          ? moment(defaultTime).format("YYYY-MM-DD HH:mm:ss")
          : moment().format("YYYY-MM-DD HH:mm:ss");

        const inputTimeLine = validateInputTime(inputTime);
        if (!inputTimeLine) return;

        const timeEnd = calculateTimeEnd(
          inputTime,
          isThuocDichTruyen,
          soLuong1Lan,
          tocDoTruyen,
          donViTocDoTruyen,
          soGiot
        );
        const keySoLuong = `sl${inputTimeLine.unsignText()}`;

        if (!item && checkUsageLimit(dsThoiGianSuDung, soLan1Ngay)) return;

        let checkInRange = false;
        let isInRange = false;

        // Handle existing item updates
        if (item) {
          checkInRange = true;
          isInRange = moment().isBetween(
            moment(item.tuThoiGian),
            moment(item.denThoiGian)
          );
          if (checkUsageLimit(dsThoiGianSuDung, soLan1Ngay, isInRange)) return;

          if (item.timeLine === inputTimeLine) {
            dsThoiGianSuDung[item.index] = {
              ...dsThoiGianSuDung[item.index],
              dieuDuongId: nhanVienId,
              tuThoiGian: inputTime,
              // Thuốc thường thì đến thời gian null
              // Neếu thuốc dịch truyền và thời gian nhập nằm trong khoảng thời gian đã nhập thì giữ nguyên giá trị đến thời gian
              denThoiGian: isThuocDichTruyen
                ? isInRange
                  ? dsThoiGianSuDung[item.index].denThoiGian
                  : timeEnd
                : null,
              stt: dsThoiGianSuDung[item.index].stt || stt,
            };
          }
        }

        const callback = (thoiGian) => {
          const payload = [data, ...children].map((record, index) => {
            const soLuongNhap = calculateSoLuongNhap(
              record,
              index === 0,
              keySoLuong
            );
            const dsThoiGianSuDungProcessed = processDsThoiGianSuDung({
              record,
              isParent: index === 0,
              keySoLuong,
              checkInRange,
              item,
              isInRange,
              inputTimeLine,
              thoiGian,
              isThuocDichTruyen,
              soLuongNhap,
              tocDoTruyen,
              donViTocDoTruyen,
              soGiot,
              nhanVienId,
              stt,
              isTachDongCapPhatThuoc,
            });

            return {
              id: record.id,
              loai: record.loai,
              dsThoiGianSuDung: dsThoiGianSuDungProcessed,
            };
          });

          showLoading();
          putThoiGianSuDungThuoc({
            payload: payload,
            page,
          }).finally(() => {
            hideLoading();
          });
        };

        // khi nhập tại line nhập có thời gian bác sĩ kê
        // thì lấy theo thời gian bắt đầu
        if (timeLine === inputTimeLine || defaultTime) {
          callback(inputTime);
        } else {
          refModalThoiGianDungThuoc.current?.show(
            {
              thoiGianYLenh: list?.thoiGianYLenh,
              timeLine: `${timeLine} ${time}`,
            },
            callback
          );
        }
      },
    [
      calculateTime,
      isTachDongCapPhatThuoc,
      dataMA_DUONG_DUNG_DICH_TRUYEN,
      validateInputTime,
      calculateTimeEnd,
      checkUsageLimit,
      calculateSoLuongNhap,
      processDsThoiGianSuDung,
    ]
  );

  const onClickTenThuoc = useCallback(
    ({ record, page }) => {
      refModalChiTietCapPhat.current &&
        refModalChiTietCapPhat.current.show({
          data: record,
          trangThaiThuoc: LIST_TRANG_THAI_THUOC,
          onHuyBanGiao: onHuyBanGiao({ data: record, page }),
          onHuySuDungThuoc: onHuySuDungThuoc({ data: record, page }),
          onHuyNgungSDThuoc: onHuyNgungSDThuoc({ data: record, page }),
          page,
        });
    },
    [onHuyBanGiao, onHuySuDungThuoc, onHuyNgungSDThuoc]
  );

  const onPauseThoiGianSuDungThuoc = useCallback(
    ({ item, list, page }) =>
      (e) => {
        let nowTime = moment();
        let startTime = moment(item.tuThoiGian);

        const {
          loai,
          id,
          dsThoiGianSuDung = [],
          dsThoiGianSuDungAll = [],
        } = list;

        let dsThoiGianSuDungClone = cloneDeep(dsThoiGianSuDung);
        if (list.tachDong && isTachDongCapPhatThuoc && !list.dungKemId) {
          dsThoiGianSuDungClone = dsThoiGianSuDungAll.map((item) => {
            if (item.stt === list.stt) {
              if (item.dieuDuongId == nhanVienId) {
                if (nowTime.isBefore(startTime)) {
                  item.tuThoiGian = moment().format("YYYY-MM-DD HH:mm:ss");
                } else {
                  item.denThoiGian = moment().format("YYYY-MM-DD HH:mm:ss");
                }
              }
              return item;
            } else {
              return item;
            }
          });
        } else {
          dsThoiGianSuDungClone[item.index].dieuDuongId = nhanVienId;
          if (nowTime.isBefore(startTime)) {
            dsThoiGianSuDungClone[item.index].tuThoiGian = moment().format(
              "YYYY-MM-DD HH:mm:ss"
            );
          } else {
            dsThoiGianSuDungClone[item.index].denThoiGian = moment().format(
              "YYYY-MM-DD HH:mm:ss"
            );
          }
        }

        showLoading();
        putThoiGianSuDungThuoc({
          payload: [
            {
              id,
              loai,
              dsThoiGianSuDung: dsThoiGianSuDungClone.map((item) => {
                const { loaiThoiGianSuDung } = calculateTime(item.tuThoiGian);
                return {
                  ...item,
                  loaiThoiGianSuDung:
                    loaiThoiGianSuDung ?? item.loaiThoiGianSuDung,
                };
              }),
            },
          ],
          page,
        }).finally(() => {
          hideLoading();
        });
      },
    [isTachDongCapPhatThuoc, calculateTime]
  );

  const onDieuDuongXacNhan = useCallback(
    ({ list, page }) =>
      () => {
        const { id, loai, ...payload } = list;
        let dsXacNhanDieuDuong = list?.dsXacNhanDieuDuong || [];
        if (list.dieuDuongXacNhan) {
          showConfirm(
            {
              title: t("common.thongBao"),
              content: `${t(
                "quanLyNoiTru.capPhatThuoc.banCoChacMuonHuyXacNhan"
              )}`,
              cancelText: t("common.huy"),
              okText: t("common.dongY"),
              classNameOkText: "button-warning",
              showBtnOk: true,
              typeModal: "warning",
            },
            () => {
              dsXacNhanDieuDuong = dsXacNhanDieuDuong.filter((el) => {
                return el.stt !== list.stt;
              });
              putdieuDuongXacNhan({
                payload: [
                  {
                    id,
                    loai,
                    dsXacNhanDieuDuong,
                  },
                ],
                page,
              });
            }
          );
        } else {
          dsXacNhanDieuDuong.push({
            stt: list.stt,
            dieuDuongId: nhanVienId,
            thoiGianThucHien: moment().format("YYYY-MM-DD HH:mm:ss"),
          });
          putdieuDuongXacNhan({
            payload: [
              {
                id,
                loai,
                dsXacNhanDieuDuong,
              },
            ],
            page,
          });
        }
      },
    [isTachDongCapPhatThuoc, nhanVienId]
  );

  const checkIsDisabled = useCallback((medication) => {
    const { soLuong } = medication || {};
    // Loại thuốc nhà thuốc đã tạo đơn thì được sửa thời gian
    if (medication.loai === 55) return false;
    return soLuong === 0;
  }, []);

  const checkDisabledList = useCallback((list) => {
    if (!isArray(list, true)) return false;
    return list.every(checkIsDisabled);
  }, []);

  const onSelectRow = useCallback(
    (groupId, id) => (e) => {
      const checked = e.target.checked;
      const prevGroupKeys = state.selectedRowKeysByGroup[groupId] || [];
      const selectedRowKeys = new Set(prevGroupKeys);

      if (checked) {
        selectedRowKeys.add(id);
      } else {
        selectedRowKeys.delete(id);
      }

      setState({
        selectedRowKeysByGroup: {
          ...state.selectedRowKeysByGroup,
          [groupId]: [...selectedRowKeys],
        },
      });
    },
    [state.selectedRowKeysByGroup]
  );

  const flattenData = useCallback((list = []) => {
    return list.reduce((acc, item) => {
      acc.push(item);
      if (item.children?.length) {
        acc.push(...flattenData(item.children));
      }
      return acc;
    }, []);
  }, []);

  const onSelectAllGroup = useCallback(
    (groupId, groupData, checked) => {
      const allData = flattenData(groupData);
      const allIds = allData.map((item) => item.id);

      setState({
        selectedRowKeysByGroup: {
          ...state.selectedRowKeysByGroup,
          [groupId]: checked ? allIds : [],
        },
      });
    },
    [state, setState]
  );

  return (
    <CapPhatThuocContext.Provider
      value={{
        //state
        state,
        setState,
        modalState,
        setModalState,
        //data
        dataMA_DUONG_DUNG_DICH_TRUYEN,
        dataTHOI_GIAN_VA_TEN_BUOI_SANG,
        dataTHOI_GIAN_VA_TEN_BUOI_CHIEU,
        dataTHOI_GIAN_VA_TEN_BUOI_TOI,
        dataTHOI_GIAN_VA_TEN_BUOI_DEM,
        isTachDongCapPhatThuoc,
        dataKY_CAP_PHAT_THUOC,

        listAllNhanVien,
        listGioiTinh,
        getNhanVienById,
        dataFilter,
        listKhoTheoTaiKhoan,
        listKhoaTheoTaiKhoan,
        getTrangThaiThuoc,
        getTenThuocById,
        //refs
        refScrollContainer,
        refModalBanGiao,
        refModalChiTietCapPhat,
        refModalThoiGianDungThuoc,
        refModalSuaThoiGian,
        refModalNgungSDThuoc,
        refModalChonPhieuTruyenDich,
        refModalPatientSign,
        //actions
        onBanGiaoThuoc,
        onHuyBanGiao,
        onHuySuDungThuoc,
        onNgungSDThuoc,
        onHuyNgungSDThuoc,
        onChangeThoiGianSdThuoc,
        onEditThoiGian,
        onClickTenThuoc,
        onPauseThoiGianSuDungThuoc,
        onCloseModal,
        onDieuDuongXacNhan,
        calculateTime,
        configTime,
        checkIsDisabled,
        checkDisabledList,
        onSelectRow,
        onSelectAllGroup,
        flattenData,
      }}
    >
      {children}
    </CapPhatThuocContext.Provider>
  );
};

export const CapPhatThuocConsumer = CapPhatThuocContext.Consumer;
