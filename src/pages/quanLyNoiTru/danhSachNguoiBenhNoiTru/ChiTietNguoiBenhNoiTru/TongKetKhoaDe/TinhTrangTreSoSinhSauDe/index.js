import React, {
  useEffect,
  useRef,
  useState,
  forwardRef,
  useImperativeHandle,
  useMemo,
} from "react";
import { useDispatch } from "react-redux";
import { orderBy } from "lodash";
import { Form } from "antd";
import { useStore, useEnum } from "hooks";
import FormTinhTrangTreSoSinhSauDe from "pages/application/TuyChinhGiaoDienPhamMem/NoiTru/SapXepTruongThongTinNoiTru/TinhTrangTreSoSinhSauDe/FormTinhTrangTreSoSinhSauDe";
import { ENUM } from "constants/index";
import { Main } from "./styled";

const FormTinhTrangTreWrapper = ({
  item,
  index,
  onValuesChange,
  formsRef,
  state,
}) => {
  const [form] = Form.useForm();
  const [listLoaiApgar] = useEnum(ENUM.LOAI_APGAR, []);

  useEffect(() => {
    formsRef.current.set(index, form);
    form.setFieldsValue({
      ...item,
      dsApgar: item.dsApgar || [{ loai: listLoaiApgar[0]?.id }],
    });
  }, [form, index, item, formsRef, listLoaiApgar]);

  return (
    <FormTinhTrangTreSoSinhSauDe
      tinhTrangTreSoSinhSauDe={item}
      tinhTrangTreSoSinhSauDeIndex={index}
      activeSections={["tinhTrangTreSoSinhSauDe"]}
      form={form}
      fromSetting={false}
      isReadonly={false}
      onValuesChange={onValuesChange}
      parentState={state}
    />
  );
};

const TinhTrangTreSoSinhSauDe = (props, ref) => {
  const [state, _setState] = useState({});
  const formsRef = useRef(new Map());

  const setState = (data) => {
    _setState((pre) => ({ ...pre, ...data }));
  };

  const tongKetSanPhu = useStore("nbThongTinSanPhu.tongKetSanPhu", {});
  const { putTongKetTreSoSinh } = useDispatch().nbThongTinSanPhu;

  const onSave = async () => {
    try {
      const validationPromises = Array.from(formsRef.current.values()).map(
        (form) => form.validateFields()
      );

      const formValues = await Promise.all(validationPromises);

      const _data = formValues.map((values, index) => ({
        ...dsThongTinCon[index],
        ...values,
      }));

      putTongKetTreSoSinh({
        id: tongKetSanPhu.id,
        payload: _data.map((item) => ({
          ...item,
          canNang: item.canNang / 1000,
        })),
      });
    } catch (error) {
      console.error("Form validation failed:", error);
    }
  };

  useImperativeHandle(ref, () => ({
    onSave,
  }));

  const dsThongTinCon = useMemo(() => {
    let data = [];
    if (tongKetSanPhu?.id) {
      data = orderBy(
        tongKetSanPhu?.dsThongTinCon.map((item) => ({
          ...item,
          canNang: item.canNang * 1000,
        })) || [],
        "conThu",
        "asc"
      );
    }

    return data;
  }, [tongKetSanPhu?.id]);

  useEffect(() => {
    if (dsThongTinCon.length > 0) {
      setState({
        activeKey: dsThongTinCon.map(
          (_, index) => `thong-tin-tre-so-sinh-sau-de-${index}`
        ),
      });
    }
  }, [dsThongTinCon]);

  const onValuesChange = (formIndex) => (changedValues, allValues) => {
    if (changedValues.hasOwnProperty("dsApgar")) {
      for (const index in allValues.dsApgar) {
        const item = allValues.dsApgar[index];
        item.tongCong = ["tim", "tho", "mauSac", "truongLuc", "phanXa"].reduce(
          (prev, cur) => prev + (parseInt(item[cur]) || 0),
          0
        );
        allValues.dsApgar[index] = item;
      }

      formsRef.current.get(formIndex).setFieldsValue({
        dsApgar: allValues.dsApgar,
      });
    }
  };

  return (
    <Main>
      {dsThongTinCon.map((item, index) => (
        <FormTinhTrangTreWrapper
          key={`thong-tin-tre-so-sinh-sau-de-${index}`}
          item={item}
          index={index}
          onValuesChange={onValuesChange(index)}
          formsRef={formsRef}
          state={state}
        />
      ))}
    </Main>
  );
};

export default forwardRef(TinhTrangTreSoSinhSauDe);
