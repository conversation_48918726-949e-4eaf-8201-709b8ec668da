import React, { useRef, useState, useEffect } from "react";
import { Main } from "./styled";
import { useDispatch, useSelector } from "react-redux";
import ChiDinhDichVuMau from "pages/chiDinhDichVu/DichVuMau";
import { useStore } from "hooks";
import MauDaChiDinh from "./MauDaChiDinh";
import { Input, message } from "antd";
import imgSearch from "assets/images/template/icSearch.png";
import { useTranslation } from "react-i18next";
import { HotKeyTrigger, Select } from "components";
import moment from "moment";
import { checkRole } from "lib-utils/role-utils";
import { LOAI_DICH_VU, ROLES } from "constants/index";
import ThemChiDinhSpan from "pages/chiDinhDichVu/components/ThemChiDinhTxtSpan";

const Mau = (props) => {
  const { t } = useTranslation();
  const refChiDinhDichVuMau = useRef(null);
  const {
    isReadonly,
    layerId,
    checkChongChiDinh,
    chiDinhTuDichVuId,
    chiDinhTuLoaiDichVu,
    khoaLamViec,
  } = props;
  const {
    chiDinhMau: { getListChePhamMau },
  } = useDispatch();
  const { currentToDieuTri } = useSelector((state) => state.toDieuTri);
  const configData = useStore("chiDinhKhamBenh.configData", {});
  const { denNgayTheBhyt, tuNgayTheBhyt } = configData?.thongTinNguoiBenh || {};
  const listThietLapChonKho = useSelector(
    (state) => state.thietLapChonKho.listThietLapChonKhoMau
  );

  const [state, _setState] = useState({
    khoId: null,
  });

  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };

  useEffect(() => {
    if (listThietLapChonKho.length === 1) {
      setState({ khoId: listThietLapChonKho[0]?.id });
    } else {
      setState({ khoId: null });
    }
  }, [listThietLapChonKho]);

  const onChiDinhMau = (e) => {
    const chongChiDinhObject = checkChongChiDinh?.();
    if (chongChiDinhObject?.chongChiDinh) {
      chongChiDinhObject?.showConfirm();
      return;
    }
    if (!state.khoId) {
      return;
    }

    refChiDinhDichVuMau.current &&
      refChiDinhDichVuMau.current.show(
        {
          khoId: state.khoId,
          khoaChiDinhId: configData.khoaChiDinhId,
        },
        () => {
          if (
            configData.chiDinhTuLoaiDichVu === LOAI_DICH_VU.TO_DIEU_TRI &&
            denNgayTheBhyt &&
            tuNgayTheBhyt
          ) {
            const tuNgay = moment(tuNgayTheBhyt);
            const denNgay = moment(denNgayTheBhyt);
            if (!moment().isBetween(tuNgay, denNgay)) {
              message.error(
                `Giá trị thẻ hiện tại của NB đã hết hạn. Thời gian sử dụng từ ${tuNgay.format(
                  "DD/MM/YYYY"
                )} đến ${denNgay.format("DD/MM/YYYY")}`
              );
            }
          }
        }
      );
  };

  const onSelectKho = (value) => {
    setState({
      khoId: value,
    });
  };

  const refreshList = () => {
    if (configData.nbDotDieuTriId) {
      getListChePhamMau({
        nbDotDieuTriId: configData.nbDotDieuTriId,
        chiDinhTuDichVuId: configData.chiDinhTuDichVuId,
        chiDinhTuLoaiDichVu: configData.chiDinhTuLoaiDichVu,
        dsTrangThaiHoan: [0, 10, 20],
      });
    }
  };

  return (
    <Main>
      {checkRole([ROLES["QUAN_LY_NOI_TRU"].THEM_CHI_DINH_MAU]) &&
        !isReadonly && (
          <div className="search-area">
            <ThemChiDinhSpan />
            <Select
              data={listThietLapChonKho}
              style={{ width: "200px" }}
              placeholder={t("khamBenh.donThuoc.vuiLongChonKho")}
              onChange={onSelectKho}
              value={state.khoId}
            />

            <HotKeyTrigger
              layerIds={[layerId]}
              hotKey="F2"
              triggerEvent={onChiDinhMau}
            >
              <div className="search-text">
                <img src={imgSearch} alt="imgSearch" />
                <Input
                  placeholder={`${t("common.timKiem")} [F2]`}
                  onClick={onChiDinhMau}
                />
              </div>
            </HotKeyTrigger>
          </div>
        )}
      {!state.khoId && !isReadonly ? (
        <div className="require-kho">
          {t("khamBenh.donThuoc.vuiLongChonKho")}
        </div>
      ) : null}

      <MauDaChiDinh isReadonly={isReadonly} />

      <ChiDinhDichVuMau
        ref={refChiDinhDichVuMau}
        chiDinhTuLoaiDichVu={chiDinhTuLoaiDichVu || 210}
        chiDinhTuDichVuId={chiDinhTuDichVuId || currentToDieuTri?.id}
        refreshList={refreshList}
        khoaLamViec={khoaLamViec}
      />
    </Main>
  );
};

export default Mau;
