import React, {
  useEffect,
  forwardRef,
  useImperativeHandle,
  useState,
  memo,
  useRef,
} from "react";
import { Col, message, Row } from "antd";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import moment from "moment";
import { cloneDeep } from "lodash";

import { useConfirm, useEnum, useLoading, useStore, useThietLap } from "hooks";
import { checkRole } from "lib-utils/role-utils";

import { InputTimeout, TableWrapper, Tooltip } from "components";
import {
  ENUM,
  LOAI_DICH_VU,
  ROLES,
  THIET_LAP_CHUNG,
  TRANG_THAI_PHA_CHE_THUOC,
} from "constants/index";
import { SVG } from "assets";
import { isArray, roundToDigits } from "utils/index";

const { Column } = TableWrapper;

const flattenData = (list) => {
  let result = [];

  list.forEach((item) => {
    const { children, ...rest } = item;
    result.push(rest);

    if (Array.isArray(children)) {
      result = result.concat(flattenData(children));
    }
  });

  return result;
};

const ThuocDungMoiTachDong = forwardRef(
  (
    {
      isReadOnly = false,
      visible = false,
      data,
      dataVatTu,
      onChangeDsThuocEdit,
    },
    ref
  ) => {
    const { t } = useTranslation();
    const [listTrangThaiPhaCheThuoc] = useEnum(ENUM.TRANG_THAI_PHA_CHE_THUOC);
    const { showConfirm } = useConfirm();
    const { showLoading, hideLoading } = useLoading();
    const [dataLAM_TRON_SL_THUOC_PHA_CHE] = useThietLap(
      THIET_LAP_CHUNG.LAM_TRON_SL_THUOC_PHA_CHE
    );

    const {
      soPhieu = "",
      trangThai = null,
      dsPhaCheThuocChiTiet = [],
    } = data || {};

    const [state, _setState] = useState({
      editingKey: null,
      dsThuocEdit: [],
      expandedRowKeys: [],
    });
    const setState = (data = {}) => {
      _setState((state) => {
        return { ...state, ...data };
      });
    };

    const configData = useStore("chiDinhKhamBenh.configData", {});
    const currentToDieuTri = useStore("toDieuTri.currentToDieuTri", {});
    const dsThuocChuaTaoPhieu = useStore(
      "nbPhaCheThuoc.dsThuocChuaTaoPhieu",
      []
    );
    const {
      nbPhaCheThuoc: {
        getDsThuocChuaTaoPhieu,
        themPhaCheThuocChiTiet,
        updatePhaCheThuocChiTiet,
        deletePhaCheThuocChiTiet,
        onChangeInputSearch,
      },
      chiDinhVatTu: { getListDichVuVatTu, onDeleteDichVu },
    } = useDispatch();

    useImperativeHandle(ref, () => ({
      saveThuocChiTiet: async (phaCheThuocId) => {
        const api = data?.id
          ? updatePhaCheThuocChiTiet
          : themPhaCheThuocChiTiet;

        let payload = [];
        const flatList = flattenData(state.dsThuocEdit);

        flatList.forEach((item) => {
          payload.push(
            data?.id
              ? {
                  ...item,
                  id: item.id,
                }
              : {
                  phaCheThuocId,
                  nbDvKhoId: item?.id,
                  nbDotDieuTriId: configData?.nbDotDieuTriId,
                  dungKemId: item?.dungKemId,
                  soLuongHuy: item?.soLuongHuy,
                  ghiChu: item?.ghiChu,
                }
          );
        });

        if (payload.length > 0) {
          await api(payload);
        }
      },
      validateThuocChiTiet: () => {
        return true;
      },
    }));

    useEffect(() => {
      let _data = [],
        dsThuocEdit = [];

      if (data?.id) {
        _data = dsPhaCheThuocChiTiet || [];
      } else {
        _data = dsThuocChuaTaoPhieu || [];
      }
      dsThuocEdit = cloneDeep(_data).filter((item) => !item.dungKemId);
      dsThuocEdit = dsThuocEdit.map((item) => {
        let _dsDungKem = _data.filter(
          (x) => x.dungKemId == (data?.id ? item.nbDvKhoId : item.id)
        );
        if (_dsDungKem.length > 0) {
          item.children = _dsDungKem.map((i) => ({
            ...i,
            noStt: true,
          }));
          item.dungMoi = _dsDungKem.map((x) => x.tenDichVu).join(", ");
        }

        return item;
      });
      if (dataVatTu.length > 0 && data?.id) {
        dsThuocEdit = [...dsThuocEdit, ...dataVatTu];
      }

      setState({
        dsThuocEdit,
        expandedRowKeys: dsThuocEdit.map((item) => item.id),
      });
      onChangeDsThuocEdit?.(dsThuocEdit); // dùng để set ngược ra ngoài để enable/disabled button Lưu
    }, [dsThuocChuaTaoPhieu, data, dataVatTu]);

    useEffect(() => {
      if (!data?.id && visible) {
        const ngayYLenh = moment(currentToDieuTri.thoiGianYLenh);

        getDsThuocChuaTaoPhieu({
          chiDinhTuDichVuId: configData?.chiDinhTuDichVuId,
          chiDinhTuLoaiDichVu: configData?.chiDinhTuLoaiDichVu,
          nbDotDieuTriId: configData?.nbDotDieuTriId,
          ...(configData?.chiDinhTuLoaiDichVu === LOAI_DICH_VU.TO_DIEU_TRI
            ? {
                tuThoiGianThucHien: ngayYLenh.format("YYYY-MM-DD 00:00:00"),
                denThoiGianThucHien: ngayYLenh.format("YYYY-MM-DD 23:59:59"),
                tuThoiGianYLenh: ngayYLenh.format("YYYY-MM-DD 00:00:00"),
                denThoiGianYLenh: ngayYLenh.format("YYYY-MM-DD 23:59:59"),
              }
            : {}),
        });
      }
    }, [visible, data?.id]);

    const onDeleteVatTu = (record) => {
      const ten = record.ten || record.tenDichVu || "";
      showConfirm(
        {
          title: t("common.xoaDuLieu"),
          content: t("common.banCoChacMuonXoa") + " " + ten + "?",
          cancelText: t("common.quayLai"),
          okText: t("common.dongY"),
          classNameOkText: "button-error",
          showImg: true,
          showBtnOk: true,
        },
        () => {
          onDeleteDichVu(record.id).then((s) =>
            getListDichVuVatTu({
              nbDotDieuTriId: configData?.nbDotDieuTriId,
              chiDinhTuDichVuId: data?.id,
              dsTrangThaiHoan: [0, 10, 20],
              chiDinhTuLoaiDichVu: LOAI_DICH_VU.PHIEU_PHA_THUOC,
            })
          );
        },
        () => {}
      );
    };

    const onDeleteThuoc = (record) => {
      const ten = record.ten || record.tenDichVu || "";
      if (!record.dungKemId && isArray(record.children, true)) {
        message.error(t("phaCheThuoc.vuiLongXoaHetThuocDungKemTruoc"));
        return;
      }
      showConfirm(
        {
          title: t("common.xoaDuLieu"),
          content: t("common.banCoChacMuonXoa") + " " + ten + "?",
          cancelText: t("common.quayLai"),
          okText: t("common.dongY"),
          classNameOkText: "button-error",
          showImg: true,
          showBtnOk: true,
        },
        () => {
          showLoading();
          deletePhaCheThuocChiTiet(record.id)
            .then((s) => {
              let updateSource = cloneDeep(state.dsThuocEdit);
              updateSource = updateSource.filter((x) => {
                if (isArray(x.children, true)) {
                  x.children = x.children.filter((y) => y.id !== record.id);
                  return true;
                }
                return x.id !== record.id;
              });

              setState({ dsThuocEdit: updateSource });
            })
            .finally(() => {
              hideLoading();
            });
        },
        () => {}
      );
    };

    const onChange = (data, key, index) => (value) => {
      let dsThuocEdit = [...state.dsThuocEdit];
      dsThuocEdit[index][key] = value;
      setState({ dsThuocEdit });
    };

    const columns = [
      Column({
        title: t("common.stt"),
        width: 50,
        dataIndex: "index",
        key: "index",
        align: "center",
        ignore: true,
        render: (item, data, index) => {
          if (data.noStt) return "";
          return index + 1;
        },
      }),
      Column({
        title: t("danhMuc.maThuoc"),
        width: 120,
        dataIndex: "maDichVu",
        key: "maDichVu",
        i18Name: "danhMuc.maThuoc",
      }),
      Column({
        title: t("common.tenThuoc"),
        width: 140,
        dataIndex: "tenDichVu",
        key: "tenDichVu",
        i18Name: "common.tenThuoc",
        render: (item, data, index) => {
          return `${item} ${data.hamLuong || ""}`;
        },
      }),
      Column({
        title: t("phaCheThuoc.dvt"),
        width: 120,
        dataIndex: "tenDonViTinh",
        key: "tenDonViTinh",
        i18Name: "phaCheThuoc.dvt",
      }),
      Column({
        title: t("phaCheThuoc.dungMoiPha"),
        width: 80,
        dataIndex: "dungMoi",
        key: "dungMoi",
        i18Name: "phaCheThuoc.dungMoiPha",
      }),
      Column({
        title: t("phaCheThuoc.lieuYeuCauPha"),
        width: 80,
        dataIndex: "soLuongYeuCau",
        key: "soLuongYeuCau",
        i18Name: "phaCheThuoc.lieuYeuCauPha",
        align: "right",
      }),
      Column({
        title: t("kho.tenKho"),
        width: 120,
        dataIndex: "tenKho",
        key: "tenKho",
        i18Name: "kho.tenKho",
      }),
      Column({
        title: t("common.slHuy"),
        width: 150,
        dataIndex: "soLuongHuy",
        key: "soLuongHuy",
        i18Name: "common.slHuy",
        align: "right",
      }),
      Column({
        title: t("phaCheThuoc.lieuPhaCheThucDung"),
        width: 150,
        dataIndex: "soLuongThucDung",
        key: "soLuongThucDung",
        i18Name: "phaCheThuoc.lieuPhaCheThucDung",
        align: "right",
        hidden: !dataLAM_TRON_SL_THUOC_PHA_CHE?.eval(),
        render: (item, data, index) => {
          if (data.isVatTu) return "";
          return roundToDigits(data.soLuongYeuCau - data.soLuongHuy, 3);
        },
      }),
      Column({
        title: <>{t("common.ghiChu")}</>,
        width: 150,
        dataIndex: "ghiChu",
        key: "ghiChu",
        i18Name: "common.ghiChu",
        render: (item, data, index) => {
          if (!data.isVatTu) {
            return (
              <InputTimeout
                value={item}
                onChange={onChange(data, "ghiChu", index)}
              />
            );
          } else {
            return item;
          }
        },
      }),
      Column({
        title: t("common.thaoTac"),
        width: 50,
        ignore: true,
        fixed: "right",
        align: "center",
        render: (item, list, index) => {
          return (
            <>
              {list.isVatTu &&
                checkRole([ROLES["PHA_CHE_THUOC"].THEM_VAT_TU]) &&
                data?.id &&
                isReadOnly &&
                [
                  TRANG_THAI_PHA_CHE_THUOC.TAO_MOI,
                  TRANG_THAI_PHA_CHE_THUOC.DUYET_DUOC,
                  TRANG_THAI_PHA_CHE_THUOC.XAC_NHAN_NB,
                ].includes(list.trangThai) && (
                  <Tooltip title={t("common.xoa")}>
                    <SVG.IcDelete
                      onClick={() => onDeleteVatTu(list)}
                      className="ic-action cursor-pointer"
                    />
                  </Tooltip>
                )}
              {!list.isVatTu &&
                checkRole([
                  ROLES["PHA_CHE_THUOC"].XOA_THUOC_PHA_CHE_THEO_DON,
                ]) &&
                data?.id &&
                !isReadOnly &&
                [
                  TRANG_THAI_PHA_CHE_THUOC.TAO_MOI,
                  TRANG_THAI_PHA_CHE_THUOC.DUYET_DUOC,
                  TRANG_THAI_PHA_CHE_THUOC.XAC_NHAN_NB,
                  TRANG_THAI_PHA_CHE_THUOC.HUY_PHA_CHE,
                ].includes(data.trangThai) && (
                  <Tooltip title={t("common.xoa")}>
                    <SVG.IcDelete
                      onClick={() => onDeleteThuoc(list)}
                      className="ic-action cursor-pointer"
                    />
                  </Tooltip>
                )}
            </>
          );
        },
      }),
    ];

    return (
      <>
        <Row className="y-lenh-pha-che">
          <Col span={12} className="y-lenh-pha-che-title">
            {t("phaCheThuoc.thuocVaDungMoiTachDong")}
          </Col>

          <Col span={6}>
            <div className="y-lenh-pha-che-item">
              <div className="item-label">{t("common.soPhieu")}:</div>
              <div className="item-content">{soPhieu || ""}</div>
            </div>
          </Col>

          <Col span={6}>
            <div className="y-lenh-pha-che-item">
              <div className="item-label">{t("common.trangThai")}:</div>
              <div className="item-content">
                {(listTrangThaiPhaCheThuoc || []).find(
                  (x) => x.id === trangThai
                )?.ten || ""}
              </div>
            </div>
          </Col>
        </Row>

        <TableWrapper
          dataSource={state.dsThuocEdit}
          columns={columns}
          tableName="table_ToDieuTri_PhaCheThuocTheoDon_ThuocVaDungMoiTachDong"
          styleWrap={{ height: "100%" }}
          rowKey={(record) => record?.id}
          expandedRowKeys={state.expandedRowKeys}
          onExpand={(expanded, record) => {
            if (expanded) {
              setState({
                expandedRowKeys: [...state.expandedRowKeys, record?.id],
              });
            } else {
              setState({
                expandedRowKeys: state.expandedRowKeys.filter(
                  (key) => key !== record?.id
                ),
              });
            }
          }}
        />
      </>
    );
  }
);

export default memo(ThuocDungMoiTachDong);
