import React, {
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
  useEffect,
} from "react";
import { useTranslation } from "react-i18next";
import {
  Checkbox,
  Button,
  DateTimePicker,
  ModalTemplate,
  Select,
} from "components";
import { HOTKEY, MA_BIEU_MAU_EDITOR, LOAI_DICH_VU } from "constants/index";
import { Main } from "./styled";
import { Form, message } from "antd";
import moment from "moment";
import { useSelector } from "react-redux";
import { SVG } from "assets";
import { useStore } from "hooks";
import { combineUrlParams } from "utils";
import { useDispatch } from "react-redux";

const ModalInToDieuTriNhieuNgay = (props, ref) => {
  const { t } = useTranslation();
  const [state, _setState] = useState({ show: false });
  const setState = (data = {}) => {
    _setState((state) => ({ ...state, ...data }));
  };
  const { listKhoaTheoTaiKhoan } = useSelector((state) => state.khoa);
  const thongTinChiTiet = useStore("khamBenh.thongTinChiTiet", {});
  const dsMaBADaiHan = useStore("nbMaBenhAn.dsMaBADaiHan", []);
  const thongTinBenhNhan = useStore("nbDotDieuTri.thongTinBenhNhan");
  const { getDsMaBADaiHan } = useDispatch().nbMaBenhAn;

  const [form] = Form.useForm();
  const refModal = useRef(null);
  useImperativeHandle(ref, () => ({
    show: async ({
      khoaId,
      nbDotDieuTriId,
      maBaoCao,
      chiDinhTuLoaiDichVu,
      mhParams,
      isInDieuTriDaiHan = false,
      dichVuCDHAId,
      baoCaoId,
    }) => {
      setState({
        nbDotDieuTriId,
        show: true,
        maBaoCao,
        tuThoiGianYLenh: moment(new Date())
          .set("hour", "00")
          .set("minute", "00")
          .set("second", "00"),
        denThoiGianYLenh: moment(new Date())
          .set("hour", "23")
          .set("minute", "59")
          .set("second", "59"),
        chiDinhTuLoaiDichVu,
        mhParams,
        isInDieuTriDaiHan,
        inNhieuNgay: !!isInDieuTriDaiHan,
        dichVuCDHAId,
        baoCaoId,
      });

      let maBenhAnField = {};
      if (chiDinhTuLoaiDichVu === LOAI_DICH_VU.KHAM) {
        const res = await getDsMaBADaiHan(thongTinBenhNhan?.nbThongTinId);
        maBenhAnField = {
          maBenhAn:
            res.findIndex((item) => item.id === nbDotDieuTriId) > -1
              ? nbDotDieuTriId
              : null,
        };
      }

      form.setFieldsValue({
        ...maBenhAnField,
        khoaChiDinhId: khoaId,
        tuThoiGianYLenh: moment(new Date())
          .set("hour", "00")
          .set("minute", "00")
          .set("second", "00"),
        denThoiGianYLenh: moment(new Date())
          .set("hour", "23")
          .set("minute", "59")
          .set("second", "59"),
        inNhieuNgay: !!isInDieuTriDaiHan,
      });
    },
  }));
  const onOk = (isOk) => () => {
    if (isOk) {
      form.submit();
    } else {
      setState({ show: false });
    }
  };

  const onHandleSubmit = (values) => {
    if (moment(values.tuThoiGianYLenh) > moment(values.denThoiGianYLenh)) {
      message.error(
        t("quanLyNoiTru.toDieuTri.thoiGianTuNgayPhaiLonHonHoacBangDenNgay")
      );
      return;
    }
    let _maBenhAn = null;
    if (state.chiDinhTuLoaiDichVu === LOAI_DICH_VU.KHAM && values.inNhieuNgay) {
      _maBenhAn =
        dsMaBADaiHan.find((item) => item.id == values.maBenhAn)?.ten || null;
    }

    const params = {
      ...state.mhParams,
      nbDotDieuTriId: state.nbDotDieuTriId,
      khoaChiDinhId: values.khoaChiDinhId,
      // nếu là ở màn khám bệnh thì truyền thoiGianThucHien
      // nếu là ở nội trú thì truyền thoiGianYLenh
      ...(state.chiDinhTuLoaiDichVu === LOAI_DICH_VU.KHAM ||
      state.maBaoCao === "P975"
        ? {
            tuThoiGianThucHien: moment(values.tuThoiGianYLenh).format(
              "YYYY-MM-DD HH:mm:ss"
            ),
            denThoiGianThucHien: moment(values.denThoiGianYLenh).format(
              "YYYY-MM-DD HH:mm:ss"
            ),
            ...(values.inNhieuNgay
              ? {
                  nbThongTinId: thongTinBenhNhan?.nbThongTinId,
                  maBenhAn: _maBenhAn,
                }
              : {}),
          }
        : {
            tuThoiGianYLenh: moment(values.tuThoiGianYLenh).format(),
            denThoiGianYLenh: moment(values.denThoiGianYLenh).format(),
            // in tờ điều trị nội trú nhiều ngày truyền thêm loai = 10
            ...(state.maBaoCao === "P088" ? { loai: 10 } : {}),
          }),
      chiDinhTuLoaiDichVu: state.chiDinhTuLoaiDichVu,
      baoCaoId: state.baoCaoId,
    };
    let url = "";

    Object.keys(params).forEach((item, index) => {
      if (params[item]) {
        url += `${index === 0 ? "" : "&"}${item}=${params[item]}`;
      }
    });
    if (
      (state.maBaoCao === "P184" && !values.inNhieuNgay) ||
      state.maBaoCao === "P975"
    ) {
      window.open(
        combineUrlParams(
          `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[state.maBaoCao].maBaoCao}/${
            state.chiDinhTuLoaiDichVu === LOAI_DICH_VU.CDHA
              ? state.dichVuCDHAId
              : thongTinChiTiet.id
          }`,
          {
            ...state.mhParams,
            nbDotDieuTriId: state.nbDotDieuTriId,
            chiDinhTuLoaiDichVu: state.chiDinhTuLoaiDichVu,
            baoCaoId: state.baoCaoId,
          }
        )
      );
    } else {
      // nếu là ở màn khám bệnh thì truyền thêm id bản ghi khám
      let _idPath = "";
      if (state.chiDinhTuLoaiDichVu === LOAI_DICH_VU.CDHA) {
        _idPath = `/${state.dichVuCDHAId}`;
      } else if (state.chiDinhTuLoaiDichVu === LOAI_DICH_VU.KHAM) {
        //nếu ở MH điều trị dài hạn => truyền nbDotDieuTriId
        if (state.isInDieuTriDaiHan) {
          _idPath = `/${state.nbDotDieuTriId}`;
        } else {
          _idPath = `/${thongTinChiTiet.id}`;
        }
      }
      window.open(
        `/editor/bao-cao/${
          MA_BIEU_MAU_EDITOR[state.maBaoCao].maBaoCao
        }${_idPath}?${url}`
      );
    }
  };
  const hotKeys = [
    {
      keyCode: HOTKEY.ESC,
      onEvent: () => {
        onOk(false)();
      },
    },
    {
      keyCode: HOTKEY.F4,
      onEvent: () => {
        onOk(true)();
      },
    },
  ];

  useEffect(() => {
    if (state.show) {
      refModal.current.show();
    } else {
      refModal.current.hide();
    }
  }, [state.show]);
  const onChangeDate = (key) => (value) => {
    setState({
      [key]: value,
    });
  };

  return (
    <ModalTemplate
      ref={refModal}
      hotKeys={hotKeys}
      onCancel={onOk(false)}
      title={"Tờ điều trị nhiều ngày"}
      width={600}
      actionLeft={
        <Button.QuayLai onClick={onOk(false)}>
          {t("common.quayLai")} [ESC]
        </Button.QuayLai>
      }
      actionRight={
        <Button
          type="primary"
          minWidth={100}
          onClick={onOk(true)}
          rightIcon={<SVG.IcSave />}
        >
          <span> {t("common.in")}</span>{" "}
        </Button>
      }
    >
      <Main>
        <Form
          form={form}
          style={{ width: "100%" }}
          layout="vertical"
          onFinish={onHandleSubmit}
        >
          <Form.Item label={t("common.khoa")} name="khoaChiDinhId">
            <Select
              data={listKhoaTheoTaiKhoan}
              placeholder={t("quanLyNoiTru.vuiLongChonKhoa")}
            />
          </Form.Item>
          <Form.Item
            name="inNhieuNgay"
            hidden={state.maBaoCao !== "P184" || state.isInDieuTriDaiHan}
            valuePropName="checked"
          >
            <Checkbox
              onChange={() => setState({ inNhieuNgay: !state?.inNhieuNgay })}
            >
              {t("quanLyNoiTru.inNhieuNgay")}
            </Checkbox>
          </Form.Item>
          <Form.Item
            label={t("common.tuNgay")}
            name="tuThoiGianYLenh"
            rules={[
              {
                required: true,
                message: t("quanLyNoiTru.vuiLongChonThoiGianTuNgay"),
              },
            ]}
            hidden={state.maBaoCao === "P184" && !state.inNhieuNgay}
          >
            <DateTimePicker
              showTime={false}
              format={"DD/MM/YYYY HH:mm:ss"}
              value={state.tuThoiGianYLenh}
              onChange={onChangeDate("tuThoiGianYLenh")}
            />
          </Form.Item>
          <Form.Item
            label={t("common.denNgay")}
            name="denThoiGianYLenh"
            rules={[
              {
                required: true,
                message: t("quanLyNoiTru.vuiLongChonThoiGianDenNgay"),
              },
            ]}
            hidden={state.maBaoCao === "P184" && !state.inNhieuNgay}
          >
            <DateTimePicker
              showTime={false}
              format={"DD/MM/YYYY HH:mm:ss"}
              value={state.denThoiGianYLenh}
              onChange={onChangeDate("denThoiGianYLenh")}
            />
          </Form.Item>
          <Form.Item
            label={t("dieuTriDaiHan.maBADaiHan")}
            name="maBenhAn"
            hidden={state.maBaoCao === "P184" && !state.inNhieuNgay}
          >
            <Select
              valueNumber={true}
              data={dsMaBADaiHan}
              placeholder={t("khamBenh.chonMaBa")}
            />
          </Form.Item>
        </Form>
      </Main>
    </ModalTemplate>
  );
};
export default forwardRef(ModalInToDieuTriNhieuNgay);
