import styled, { createGlobalStyle } from "styled-components";

export const NoBox = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  & .label {
    padding: 10px 0;
    font-weight: 600;
  }
`;

export const Main = styled.div`
  h1 {
    padding-top: 5px;
  }
  .noi-tru__text-field-loi-dan {
    overflow: auto;
    resize: vertical;
  }
  .selection {
    display: flex;
    .ant-select {
      min-width: 275px;
    }
    .input-box {
      position: relative;
      width: 275px;
      > img {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        left: 0;
        z-index: 1;
        padding: 0 8px;
        width: 30px;
      }
      input {
        padding-left: 25px;
      }
    }
  }
  .title {
    display: flex;
    .right {
      margin-left: auto;
    }
    &.validate {
      color: red;
    }
  }

  .don-thuoc-container {
    .item {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      gap: 5px;
    }
  }
  & .item:has(.select-box-chan-doan) > div {
      width: 100%;
  }
`;
export const DivInfo = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  margin-right: 50px;

  & .ant-select {
    flex: 1;
  }
`;
export const GlobalStyle = createGlobalStyle`
  .donRaVien__button-print > .ant-dropdown-menu {
    min-width: 190px;
    min-height: 90px;
  }

  .step-wrapper-in-options{
    margin-top: -15px;
    z-index: 100000;
    &.right{
      padding-left: 15px;
      .ant-space{
        gap: 0px !important;
      }
      top: 10px !important;
      .ant-popover-inner-content{
        width: fit-content;
      }
    }
    .ant-popover-inner-content{
      padding: 0px;
      .ant-space-item{
        padding: 10px;
        margin: 0;
        &:hover {
          background: linear-gradient(0deg,rgba(255,255,255,0.75),rgba(255,255,255,0.75)),#0762F7;
        }
        white-space : nowrap;
      }
    }
  }

  .drop-down-select {
    min-width: 200px !important;
  }
  .date-custom-kham-benh-don-thuoc {
    .ant-picker-header-super-prev-btn{
        display: none;
    }
    .ant-picker-header-super-next-btn{
        display: none;
    }
  }
  .ant-select {
    & .ant-select-arrow {
      margin-top: initial !important;
      transform: translateY(-50%);
    }
  }
  .dropdown-loi-dan {
    width: 350px !important;
    @media (min-width: 1369px) {
      width: 500px !important;
    }
  }
`;
