import { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useQueryAll, useThietLap, useConfirm } from "hooks";
import moment from "moment";
import { isNumber } from "utils";
import { THIET_LAP_CHUNG, DOI_TUONG_KCB } from "constants/index";
import { query } from "redux-store/stores";

export const useKiemTraChongChiDinh = (chiTietNguoiBenhNoiTru) => {
  const { t } = useTranslation();
  const { showConfirm } = useConfirm();
  const [
    dataKHONG_CHI_DINH_DV_NEU_KHONG_SANG_LOC_DINH_DUONG,
    finishKHONG_CHI_DINH_DV_NEU_KHONG_SANG_LOC_DINH_DUONG,
  ] = useThietLap(
    THIET_LAP_CHUNG.KHONG_CHI_DINH_DV_NEU_KHONG_SANG_LOC_DINH_DUONG
  );

  const [
    dataKHONG_CHI_DINH_DV_NEU_KHONG_SANG_LOC_DINH_DUONG_SAU_7NGAY,
    finishKHONG_CHI_DINH_DV_NEU_KHONG_SANG_LOC_DINH_DUONG_SAU_7NGAY,
  ] = useThietLap(
    THIET_LAP_CHUNG.KHONG_CHI_DINH_DV_NEU_KHONG_SANG_LOC_DINH_DUONG_SAU_7NGAY
  );

  const queryDsSangLoc = useQueryAll(
    query.sangLocSuyDd.queryDsSangLocDinhDuong({
      params: {
        nbDotDieuTriId: chiTietNguoiBenhNoiTru?.id,
        sort: "thoiGianThucHien,desc",
      },
      persist: false,
      cacheTime: 0,
      staleTime: 0,
      enabled: !!chiTietNguoiBenhNoiTru?.id,
    })
  );

  const checkChongChiDinh = useMemo(() => {
    const {
      doiTuongKcb,
      thoiGianVaoKhoaNhapVien,
      trangThaiSangLocDd,
      maKhoaNb,
    } = chiTietNguoiBenhNoiTru || {};
    const thoiGianVaoVien =
      thoiGianVaoKhoaNhapVien && moment(thoiGianVaoKhoaNhapVien);

    const [gioGioiHan, dsMaKhoaDuocChiDinh] =
      dataKHONG_CHI_DINH_DV_NEU_KHONG_SANG_LOC_DINH_DUONG.split("/");

    const gioiHanGio = parseInt(gioGioiHan, 10);

    const gioiHanGio7Ngay = parseInt(
      dataKHONG_CHI_DINH_DV_NEU_KHONG_SANG_LOC_DINH_DUONG_SAU_7NGAY,
      10
    );

    const doiTuongKcbList = [
      DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU,
      DOI_TUONG_KCB.DIEU_TRI_NOI_TRU,
      DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_BAN_NGAY,
    ];

    const khongChongChiDinh =
      chiTietNguoiBenhNoiTru?.id &&
      (dsMaKhoaDuocChiDinh
        ?.split(",")
        ?.map((item) => item.trim())
        ?.includes(maKhoaNb) ||
        !thoiGianVaoVien ||
        !doiTuongKcbList.includes(doiTuongKcb) ||
        (!isNumber(gioiHanGio) && !isNumber(gioiHanGio7Ngay)));

    const isDataLoading =
      queryDsSangLoc.isLoading ||
      !finishKHONG_CHI_DINH_DV_NEU_KHONG_SANG_LOC_DINH_DUONG ||
      !finishKHONG_CHI_DINH_DV_NEU_KHONG_SANG_LOC_DINH_DUONG_SAU_7NGAY;

    return () => {
      if (isDataLoading || !chiTietNguoiBenhNoiTru?.id) {
        return {
          chongChiDinh: true,
          showConfirm: () =>
            showConfirm({
              title: t("common.thongBao"),
              content: t("quanLyNoiTru.dangChuanBiDuLieu"),
              cancelText: t("common.dong"),
              classNameOkText: "button-warning",
              showImg: true,
              typeModal: "warning",
            }),
        };
      }

      if (khongChongChiDinh) return { chongChiDinh: false };

      const now = moment();

      if (
        isNumber(gioiHanGio) &&
        now.isAfter(thoiGianVaoVien.clone().add(gioiHanGio, "hours")) &&
        trangThaiSangLocDd === 10
      ) {
        return {
          chongChiDinh: true,
          showConfirm: () =>
            showConfirm({
              title: t("common.thongBao"),
              content: t("quanLyNoiTru.canhBaoKhongTheChiDinh"),
              cancelText: t("common.dong"),
              classNameOkText: "button-warning",
              showImg: true,
              typeModal: "warning",
            }),
        };
      }

      const sangLocDinhDuongGanNhat = queryDsSangLoc?.data?.[0];
      const isAfter7DaysLimit =
        isNumber(gioiHanGio7Ngay) &&
        chiTietNguoiBenhNoiTru?.trangThaiSangLocDd !== 10 &&
        sangLocDinhDuongGanNhat?.thoiGianThucHien &&
        now.isAfter(
          moment(sangLocDinhDuongGanNhat.thoiGianThucHien)
            .add(7, "days")
            .add(gioiHanGio7Ngay, "hours")
        );
      if (isAfter7DaysLimit) {
        return {
          chongChiDinh: true,
          showConfirm: () =>
            showConfirm({
              title: t("common.thongBao"),
              content: t("quanLyNoiTru.canhBaoKhongTheChiDinhDichVuSau7Ngay"),
              cancelText: t("common.dong"),
              classNameOkText: "button-warning",
              showImg: true,
              typeModal: "warning",
            }),
        };
      }

      return { chongChiDinh: false };
    };
  }, [
    chiTietNguoiBenhNoiTru?.doiTuongKcb,
    chiTietNguoiBenhNoiTru?.thoiGianVaoKhoaNhapVien,
    chiTietNguoiBenhNoiTru?.trangThaiSangLocDd,
    chiTietNguoiBenhNoiTru?.maKhoaNb,
    dataKHONG_CHI_DINH_DV_NEU_KHONG_SANG_LOC_DINH_DUONG,
    dataKHONG_CHI_DINH_DV_NEU_KHONG_SANG_LOC_DINH_DUONG_SAU_7NGAY,
    finishKHONG_CHI_DINH_DV_NEU_KHONG_SANG_LOC_DINH_DUONG,
    finishKHONG_CHI_DINH_DV_NEU_KHONG_SANG_LOC_DINH_DUONG_SAU_7NGAY,
    queryDsSangLoc?.data,
    queryDsSangLoc?.isLoading,
    t,
  ]);

  return { checkChongChiDinh };
};
