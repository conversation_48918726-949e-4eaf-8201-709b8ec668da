import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
  useRef,
} from "react";
import { Main } from "./styled";
import { DatePicker, Button, ModalTemplate } from "components";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { isEmpty } from "lodash";
import { SVG } from "assets";
import TableChonPhieu from "../../ToDieuTri/modals/TableChonPhieu";
import ModalConfirmChonPhieu from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalConfirmChonPhieu";

const ModalInPhieuPC_CAMKET_PTTT_TYCNT = (props, ref) => {
  const { t } = useTranslation();
  const refModal = useRef(null);
  const refCallback = useRef(null);
  const refModalConfirmChonPhieu = useRef(null);

  //state
  const [state, _setState] = useState({
    show: false,
    checkValidate: false,
    thoiGianThucHien: moment(),
    selectedPhieu: null,
    dsSoPhieu: [],
  });
  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };

  useImperativeHandle(ref, () => ({
    show: (data = {}, onOk) => {
      const _dsSoPhieu = (data.dsSoPhieu || [])
        .filter((item) => !isEmpty(item.soPhieu))
        .map((item) => ({
          ...item,
          id: item.soPhieu,
        }));

      setState({
        show: true,
        dsSoPhieu: _dsSoPhieu,
        tenPhieu: data?.tenPhieu,
        thoiGianThucHien: moment(),
      });

      refCallback.current = onOk;
    },
  }));

  //function
  const onClose = () => {
    setState({
      show: false,
      checkValidate: false,
      thoiGianThucHien: moment(),
    });
  };

  const onCheckValidate = () => {
    setState({ checkValidate: true });
    if (!state.thoiGianThucHien) {
      return false;
    }

    return true;
  };

  const onSelectPhieuDaIn = (payload = {}) => {
    const data = {
      id: payload.id,
      thoiGianThucHien: payload.thoiGianThucHien
        ? moment(payload.thoiGianThucHien).format("YYYY-MM-DD HH:mm:00")
        : null,
    };

    refCallback.current && refCallback.current(data);
    setState({
      show: false,
      selectedPhieu: null,
      dsSoPhieu: [],
      selectedRowKeys: null,
    });
  };

  const onSave = () => {
    if (state.selectedPhieu) {
      onSelectPhieuDaIn(state.selectedPhieu);
    } else {
      if (!onCheckValidate()) {
        return;
      }

      const { thoiGianThucHien } = state;

      //kiểm tra trùng từ ngày trong ds số phiếu
      const _ngayThucHien = moment(thoiGianThucHien).format("DD/MM/YYYY");
      const _checkSoPhieu = (state.dsSoPhieu || []).filter(
        (item) =>
          moment(item.thoiGianThucHien).format("DD/MM/YYYY") == _ngayThucHien
      );

      if (_checkSoPhieu.length > 0) {
        refModalConfirmChonPhieu.current &&
          refModalConfirmChonPhieu.current.show(
            {
              dsSoPhieu: _checkSoPhieu,
              tuNgay: _ngayThucHien,
              showTen2: false,
              showThoiGianIn: true,
            },
            () => {
              refCallback.current({
                thoiGianThucHien: thoiGianThucHien.format(
                  "YYYY-MM-DD HH:mm:00"
                ),
              });
              onClose();
            },
            onSelectPhieuDaIn
          );
      } else {
        refCallback.current({
          thoiGianThucHien: thoiGianThucHien.format("YYYY-MM-DD HH:mm:00"),
        });
        onClose();
      }
    }
  };

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  const onChange = (key) => (e) => {
    setState({
      [key]: e || null,
      selectedRowKeys: null,
      selectedPhieu: null,
    });
  };

  //Set lại giá trị default thời gian và khoa khi chọn phiếu
  useEffect(() => {
    if (state?.selectedRowKeys) {
      setState({
        thoiGianThucHien: moment(),
        khoaChiDinhId: state.defaultKhoaChiDinhId,
      });
    }
  }, [state?.selectedRowKeys]);

  return (
    <ModalTemplate
      ref={refModal}
      width={600}
      onCancel={onClose}
      title={state.tenPhieu}
      actionLeft={<Button.QuayLai onClick={onClose} />}
      actionRight={
        <Button
          type="primary"
          minWidth={100}
          onClick={onSave}
          rightIcon={<SVG.IcPrint />}
        >
          {t("common.in")}
        </Button>
      }
    >
      <Main>
        <div className="form">
          <div className="form-item">
            <div className="label">
              <span>{t("common.thoiGianThucHien")}:</span>
              {!state.selectedPhieu && <span className="error">*</span>}
            </div>
            <div className="content">
              <DatePicker
                showTime
                value={
                  state.thoiGianThucHien ? moment(state.thoiGianThucHien) : null
                }
                format="DD-MM-YYYY HH:mm"
                placeholder={t("common.thoiGianThucHien")}
                onChange={onChange("thoiGianThucHien")}
              />
            </div>
          </div>
          {state?.checkValidate && !state.thoiGianThucHien && (
            <span className="error">
              {t("baoCao.vuiLongChonThoiGianThucHien")}
            </span>
          )}
        </div>

        <div className="ds-phieu-da-in">
          <TableChonPhieu
            dsSoPhieu={state?.dsSoPhieu}
            setParentState={setState}
            selectedRowKeys={state?.selectedRowKeys}
            showTen2={false}
            showThoiGianIn={true}
            showKhoaChiDinh={false}
          />
        </div>

        <ModalConfirmChonPhieu ref={refModalConfirmChonPhieu} />
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalInPhieuPC_CAMKET_PTTT_TYCNT);
