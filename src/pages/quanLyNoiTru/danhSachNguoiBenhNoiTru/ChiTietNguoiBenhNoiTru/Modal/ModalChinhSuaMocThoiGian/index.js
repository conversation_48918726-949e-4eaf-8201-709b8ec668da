import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { Form, Row, Col, Typography, message, Spin } from "antd";
import moment from "moment";
import { Button, DateTimePicker, ModalTemplate } from "components";
import { useStore } from "hooks";
import { DOI_TUONG_KCB, LOAI_DICH_VU } from "constants/index";
import { Main } from "./styled";
import nbDichVuProvider from "data-access/nb-dich-vu-provider";
import nbDotDieuTriProvider from "data-access/nb-dot-dieu-tri-provider";

const { Text } = Typography;

const ModalChinhSuaMocThoiGian = (props, ref) => {
  const { t } = useTranslation();
  const [state, _setState] = useState({ show: false });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const {
    khamBenh: { themThongTinDV },
  } = useDispatch();

  const [form] = Form.useForm();
  const refModal = useRef(null);
  const refCallback = useRef(null);

  const chiTietNguoiBenhNoiTru = useStore(
    "danhSachNguoiBenhNoiTru.chiTietNguoiBenhNoiTru"
  );
  const thongTinBenhNhan = useStore("nbDotDieuTri.thongTinBenhNhan", {});

  useImperativeHandle(ref, () => ({
    show: (data = {}, callback) => {
      setState({ show: true, nbDotDieuTriId: data.nbDotDieuTriId });
      refCallback.current = callback;
    },
  }));

  useEffect(() => {
    if (!refModal.current) return;

    if (state?.show) {
      refModal.current.show();

      form.setFieldsValue({
        thoiGianLapBenhAn: thongTinBenhNhan?.thoiGianLapBenhAn
          ? moment(thongTinBenhNhan.thoiGianLapBenhAn)
          : null,
        thoiGianVaoKhoaNhapVien: chiTietNguoiBenhNoiTru?.thoiGianVaoKhoaNhapVien
          ? moment(chiTietNguoiBenhNoiTru.thoiGianVaoKhoaNhapVien)
          : null,
      });
    } else {
      refModal.current.hide();
    }
  }, [state?.show, thongTinBenhNhan, chiTietNguoiBenhNoiTru]);

  useEffect(() => {
    if (!state?.show || !state.nbDotDieuTriId) return;

    setState({ loadingDichVu: true });

    nbDichVuProvider
      .searchAll({
        nbDotDieuTriId: state.nbDotDieuTriId,
        doiTuongKcb: DOI_TUONG_KCB.NGOAI_TRU,
        loaiDichVu: LOAI_DICH_VU.KHAM,
        page: "",
        size: "",
      })
      .then((s) => {
        const dichVuList =
          s?.data?.map((item) => ({
            id: item?.id,
            tenDichVu: item?.tenDichVu,
            thoiGianTiepNhan: item?.thoiGianTiepNhan
              ? moment(item.thoiGianTiepNhan)
              : null,
            thoiGianThucHien: item?.thoiGianThucHien
              ? moment(item.thoiGianThucHien)
              : null,
            thoiGianKetLuan: item?.thoiGianKetLuan
              ? moment(item.thoiGianKetLuan)
              : null,
          })) || [];

        setState({ dichVuList });

        form.setFieldsValue({
          dichVu: dichVuList.map((dv) => ({
            thoiGianTiepNhan: dv.thoiGianTiepNhan,
            thoiGianThucHien: dv.thoiGianThucHien,
            thoiGianKetLuan: dv.thoiGianKetLuan,
          })),
        });
      })
      .finally(() => {
        setState({ loadingDichVu: false });
      });
  }, [state?.show, state.nbDotDieuTriId]);

  const onHandleSubmit = async (values) => {
    const { thoiGianVaoKhoaNhapVien, thoiGianLapBenhAn, dichVu } = values;

    if (state.dichVuList?.length && dichVu?.length) {
      for (let i = 0; i < state.dichVuList.length; i++) {
        const dvForm = dichVu[i] || {};
        const { thoiGianTiepNhan, thoiGianThucHien, thoiGianKetLuan } = dvForm;

        if (!thoiGianTiepNhan) {
          message.error(
            `${t("quanLyNoiTru.vuiLongChonThoiGianTiepNhanKham")} - ${
              state.dichVuList[i]?.tenDichVu
            }`
          );
          return;
        }
        if (!thoiGianThucHien) {
          message.error(
            `${t("quanLyNoiTru.vuiLongChonThoiGianThucHienKham")} - ${
              state.dichVuList[i]?.tenDichVu
            }`
          );
          return;
        }
        if (!thoiGianKetLuan) {
          message.error(
            `${t("quanLyNoiTru.vuiLongChonThoiGianKetLuanKham")} - ${
              state.dichVuList[i]?.tenDichVu
            }`
          );
          return;
        }
      }
    }

    if (!thoiGianLapBenhAn) {
      message.error(t("quanLyNoiTru.vuiLongChonThoiGianLapBenhAn"));
      return;
    }
    if (!thoiGianVaoKhoaNhapVien) {
      message.error(t("quanLyNoiTru.vuiLongChonThoiGianTiepNhanVaoKhoa"));
      return;
    }

    const formatTime = (time) =>
      time ? moment(time).format("YYYY-MM-DD HH:mm:ss") : null;

    try {
      if (state.dichVuList?.length && dichVu?.length) {
        await Promise.all(
          state.dichVuList.map((dv, idx) => {
            const dvForm = dichVu[idx] || {};
            return themThongTinDV({
              id: dv.id,
              nbDichVu: {
                thoiGianThucHien: formatTime(dvForm.thoiGianThucHien),
              },
              nbDvKyThuat: {
                thoiGianTiepNhan: formatTime(dvForm.thoiGianTiepNhan),
              },
              thoiGianKetLuan: formatTime(dvForm.thoiGianKetLuan),
            });
          })
        );
      }

      await nbDotDieuTriProvider.chinhSuaThoiGianNhapVien({
        id: state.nbDotDieuTriId,
        thoiGianLapBenhAn: formatTime(thoiGianLapBenhAn),
        thoiGianVaoKhoaNhapVien: formatTime(thoiGianVaoKhoaNhapVien),
      });

      refCallback.current && refCallback.current();
      onCancel();
    } catch (err) {
      message.error(err?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
    }
  };

  const onCancel = () => {
    setState({ show: false });
    form.resetFields();
  };

  const onSubmit = () => {
    form.submit();
  };

  return (
    <ModalTemplate
      width={800}
      ref={refModal}
      title={t("quanLyNoiTru.chinhSuaMocThoiGian")}
      onCancel={onCancel}
      actionLeft={<Button.QuayLai onClick={onCancel} />}
      actionRight={
        <Button type="primary" minWidth={100} onClick={onSubmit}>
          {t("common.xacNhan")}
        </Button>
      }
    >
      <Main>
        <Form
          form={form}
          layout="vertical"
          className="form-custom"
          onFinish={onHandleSubmit}
        >
          {state.loadingDichVu ? (
            <div style={{ textAlign: "center", padding: "16px" }}>
              <Spin />
            </div>
          ) : (
            <>
              {!!state.dichVuList?.length && (
                <Row gutter={16} className="row-label">
                  <Col span={6}>{t("sinhHieu.tenDvKham")}</Col>
                  <Col span={6}>{t("quanLyNoiTru.thoiGianTiepNhanKham")}</Col>
                  <Col span={6}>{t("quanLyNoiTru.thoiGianThucHienKham")}</Col>
                  <Col span={6}>{t("baoCao.thoiGianKetLuanKham")}</Col>
                </Row>
              )}

              {state.dichVuList?.map((dv, index) => (
                <Row
                  gutter={16}
                  key={dv.id || index}
                  style={{ marginBottom: 8 }}
                >
                  <Col span={6}>
                    <Text italic>{dv.tenDichVu}</Text>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      name={["dichVu", index, "thoiGianTiepNhan"]}
                      style={{ marginBottom: 0 }}
                    >
                      <DateTimePicker placeholder={t("common.chonThoiGian")} />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      name={["dichVu", index, "thoiGianThucHien"]}
                      style={{ marginBottom: 0 }}
                    >
                      <DateTimePicker placeholder={t("common.chonThoiGian")} />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      name={["dichVu", index, "thoiGianKetLuan"]}
                      style={{ marginBottom: 0 }}
                    >
                      <DateTimePicker placeholder={t("common.chonThoiGian")} />
                    </Form.Item>
                  </Col>
                </Row>
              ))}
            </>
          )}

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label={t("dieuTriDaiHan.thoiGianLapBenhAn")}
                name="thoiGianLapBenhAn"
              >
                <DateTimePicker placeholder={t("common.chonThoiGian")} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label={t("quanLyNoiTru.thoiGianTiepNhanVaoKhoa")}
                name="thoiGianVaoKhoaNhapVien"
              >
                <DateTimePicker placeholder={t("common.chonThoiGian")} />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalChinhSuaMocThoiGian);
