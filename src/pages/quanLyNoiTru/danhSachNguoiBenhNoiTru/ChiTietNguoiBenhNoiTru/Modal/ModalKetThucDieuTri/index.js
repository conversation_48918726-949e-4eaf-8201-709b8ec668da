import React, {
  forwardRef,
  useC<PERSON>back,
  useEffect,
  useImperative<PERSON>andle,
  useMemo,
  useRef,
  useState,
} from "react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { useHistory } from "react-router-dom";
import { Form, Input } from "antd";
import {
  AlertMessage,
  Button,
  DateTimePicker,
  InputNumberField,
  ModalTemplate,
  Select,
  SelectLargeData,
  Checkbox,
  ModalSignPrint,
} from "components";
import moment from "moment";
import {
  useConfirm,
  useDelayedState,
  useEnum,
  useLazyKVMap,
  useListAll,
  useStore,
  useThietLap,
} from "hooks";
import { useKetQuaDieuTri } from "pages/khamBenh/hooks/useKetQuaDieuTri";
import { useHuongDieuTri } from "pages/khamBenh/hooks/useHuongDieuTri";
import {
  ENUM,
  KET_QUA_DIEU_TRI,
  HUONG_DIEU_TRI_NOI_TRU,
  THIET_LAP_CHUNG,
  LOAI_CHUYEN_VIEN,
  MAN_HINH_AP_DUNG,
  ROLES,
  MAN_HINH_PHIEU_IN,
  VI_TRI_PHIEU_IN,
  LOAI_BIEU_MAU,
} from "constants/index";
import { firstLetterWordUpperCase, isNumber } from "utils/index";
import { checkRole } from "lib-utils/role-utils";
import { toSafePromise } from "lib-utils";
import { SVG } from "assets";
import { checkIsPhieuKySo } from "utils/phieu-utils";
import { useParams } from "react-router-dom";
import { Main, GlobalStyle } from "./styled";

const { SelectChanDoan } = SelectLargeData;

const ModalKetThucDieuTri = (props, ref) => {
  const { t } = useTranslation();
  const history = useHistory();
  const refModalSignPrint = useRef(null);
  const { id } = useParams();

  const [state, _setState] = useState({
    show: false,
    tuVong: false,
    saveLoading: false,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const { onShowModalChuyenVien, onKiemTraHoSo } = props;
  const [form] = Form.useForm();
  const huongDieuTri = Form.useWatch("huongDieuTri", form);
  const { showConfirm } = useConfirm();

  const refModal = useRef(null);
  const [listlyDoTuVong] = useEnum(ENUM.LY_DO_TU_VONG);
  const [listdiaDiemTuVong] = useEnum(ENUM.DIA_DIEM_TU_VONG);
  const [listgioiTinh] = useEnum(ENUM.GIOI_TINH);
  const [listHangBenhVien] = useEnum(ENUM.HANG_BENH_VIEN, []);

  const { listHuongDieuTriKham, setValueHuongDieuTri } = useHuongDieuTri({
    callApi: state.show,
    manHinh: MAN_HINH_AP_DUNG.NOI_TRU,
  });

  const { listKetQuaDieuTriKham, setValueKetQuaDieuTri } = useKetQuaDieuTri({
    callApi: state.show,
    huongDieuTri,
    manHinh: MAN_HINH_AP_DUNG.NOI_TRU,
  });

  const [getHangBenhVien] = useLazyKVMap(listHangBenhVien);

  const listBacSi = useStore("nhanVien.listBacSi", []);

  const [refIsSubmit, onChangeStateSubmit] = useDelayedState(3000);

  const chiTietNguoiBenhNoiTru = useStore(
    "danhSachNguoiBenhNoiTru.chiTietNguoiBenhNoiTru"
  );
  const listPhongThucHien = useStore("phong.listPhong", []);
  const listFilter = useStore("pttt.listFilter", []);
  const { nbThongTinRaVien, thongTinCoBan } = useStore("nbDotDieuTri", {});
  const listToDieuTri = useStore("toDieuTri.listToDieuTri", []);
  const [listAllBenhVien] = useListAll("benhVien", {}, true);
  const [listAllNhomDichVuCap1] = useListAll("nhomDichVuCap1", {}, true);
  const listAllPhieuThuChotDot = useStore(
    "dsPhieuThuChotDotDieuTri.listAllPhieuThuChotDot",
    []
  );

  const [dataHIEN_THI_CD_CHINH_RA_VIEN_NT] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_CD_CHINH_RA_VIEN_NT
  );
  const [dataHIEN_THI_CHECK_BOX_KHONG_TU_DONG_TINH_LAI_NGAY_GIUONG] =
    useThietLap(
      THIET_LAP_CHUNG.HIEN_THI_CHECK_BOX_KHONG_TU_DONG_TINH_LAI_NGAY_GIUONG
    );
  const [dataMA_NHOM_DICH_VU_CAP1_PT] = useThietLap(
    THIET_LAP_CHUNG.MA_NHOM_DICH_VU_CAP1_PT
  );
  const [dataMAC_DINH_CHON_TAT_CA_DV_PTTT_KHI_KET_THUC_DT] = useThietLap(
    THIET_LAP_CHUNG.MAC_DINH_CHON_TAT_CA_DV_PTTT_KHI_KET_THUC_DT
  );
  const [dataBAT_BUOC_CHON_PHONG_HEN_KHI_NB_HEN_KHAM] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_CHON_PHONG_HEN_KHI_NB_HEN_KHAM
  );
  const [dataMAC_DINH_KHONG_TINH_LAI_NGAY_GIUONG] = useThietLap(
    THIET_LAP_CHUNG.MAC_DINH_KHONG_TINH_LAI_NGAY_GIUONG
  );
  const [
    dataCHUYEN_KHOA_RA_VIEN_KHONG_TINH_LAI_GIUONG,
    loadFinishThietLapChuyenKhoa,
  ] = useThietLap(THIET_LAP_CHUNG.CHUYEN_KHOA_RA_VIEN_KHONG_TINH_LAI_GIUONG);

  const isChotDotDieuTri = useMemo(() => {
    if (
      !Array.isArray(listAllPhieuThuChotDot) ||
      listAllPhieuThuChotDot.length === 0 ||
      !loadFinishThietLapChuyenKhoa
    ) {
      return false;
    }

    return (
      listAllPhieuThuChotDot.some((item) => !!item?.chotDotDieuTriId) &&
      dataCHUYEN_KHOA_RA_VIEN_KHONG_TINH_LAI_GIUONG?.eval()
    );
  }, [
    listAllPhieuThuChotDot,
    dataCHUYEN_KHOA_RA_VIEN_KHONG_TINH_LAI_GIUONG,
    loadFinishThietLapChuyenKhoa,
  ]);

  const listDsPttt = useMemo(() => {
    return (listFilter || []).map((item) => ({
      ...item,
      id: item?.id,
      ten: `${item?.maDichVu} - ${item?.tenDichVu}`,
    }));
  }, [listFilter]);

  const {
    danhSachNguoiBenhNoiTru: { ketThucDieuTri, getNbNoiTruById },
    nbDotDieuTri: { getThongTinRaVien },
    dsLuuTruBa: { getChiTietLuuTruBA },
    nhanVien: { getListBacSi },
    phieuIn: { getPhieuInTheoMa, showFileEditor },
    noiTruPhongGiuong: { onSearch: getDsPhongGiuong },
    phong: { getListPhongTongHop },
    pttt: { onSearchDanhSachPhauThuatThuThuat },
  } = useDispatch();

  useImperativeHandle(ref, () => ({
    show: () => {
      setState({
        show: true,
        tuVong: false,
        isDisplayThoiGianHenKham: false,
        isDisplayChuyenVien: false,
      });
      onSearchDanhSachPhauThuatThuThuat({
        size: 500,
        dataSearch: {
          nbDotDieuTriId: id,
          dsTrangThaiHoan: [0, 10, 20],
        },
      });
    },
    hide: () => {
      setState({ show: false });
    },
  }));

  const getBenhVienLabel = (item) => {
    const tenHang = getHangBenhVien(item.hangBenhVien)?.ten;
    const textHangBv = tenHang
      ? ` - ${t("tiepDon.hangBenhVien")}: ${tenHang}`
      : "";
    const textTuyen = item.tuyenBenhVien
      ? ` - ${t("tiepDon.tuyen")}: ${item.tuyenBenhVien}`
      : "";
    return `${item.ma} - ${item.ten}${textHangBv}${textTuyen} ${
      item?.tenTinhThanhPho ? `(${item?.tenTinhThanhPho})` : ""
    }`;
  };

  useEffect(() => {
    if (state?.show) {
      refModal.current && refModal.current.show();
      let dsCdChinhId;
      if (!nbThongTinRaVien?.dsCdChinhId?.length) {
        if (dataHIEN_THI_CD_CHINH_RA_VIEN_NT !== "1") {
          if (listToDieuTri.length) {
            dsCdChinhId = listToDieuTri?.[0]?.dsCdChinhId;
          }
        }
      } else {
        dsCdChinhId = nbThongTinRaVien?.dsCdChinhId;
      }
      const thoiGianHenKham =
        nbThongTinRaVien?.thoiGianHenKham &&
        moment(nbThongTinRaVien?.thoiGianHenKham);
      const thoiGianRaVien = nbThongTinRaVien?.thoiGianRaVien
        ? moment(nbThongTinRaVien?.thoiGianRaVien)
        : moment();

      let info = {
        ...nbThongTinRaVien,
        dsCdChinhId: (dsCdChinhId || []).map((item) => item + ""),
        thoiGianHenKham,
        thoiGianRaVien,
        soNgayHenKham:
          thoiGianHenKham && diffDate(thoiGianHenKham, thoiGianRaVien),
        thoiGianTuVong:
          nbThongTinRaVien?.thoiGianTuVong &&
          moment(nbThongTinRaVien?.thoiGianTuVong),
        tenBacSiDieuTri: nbThongTinRaVien?.bacSiDieuTri?.ten,
        tenNguoiBaoLanh:
          thongTinCoBan?.tenNguoiBaoLanh1 || thongTinCoBan?.tenNguoiBaoLanh2,
        sdtNguoiBaoLanh:
          thongTinCoBan?.sdtNguoiBaoLanh1 || thongTinCoBan?.sdtNguoiBaoLanh2,
        soCanCuocNguoiBaoLanh: thongTinCoBan?.soCanCuocNguoiBaoLanh,
        phuongPhapDieuTri: nbThongTinRaVien?.phuongPhapDieuTri,
        khongTinhLaiNgayGiuong:
          !!dataHIEN_THI_CHECK_BOX_KHONG_TU_DONG_TINH_LAI_NGAY_GIUONG?.eval() ||
          !!dataMAC_DINH_KHONG_TINH_LAI_NGAY_GIUONG?.eval() ||
          isChotDotDieuTri,
        dsPtTtGiayRaVienId: nbThongTinRaVien?.dsPtTtGiayRaVien?.map(
          (item) => item.id
        ),
      };
      form.setFieldsValue(info);

      if (nbThongTinRaVien?.ketQuaDieuTri === KET_QUA_DIEU_TRI.TU_VONG) {
        setState({ tuVong: true, isDisplayThoiGianHenKham: true });
      }
      if (
        nbThongTinRaVien?.ketQuaDieuTri === HUONG_DIEU_TRI_NOI_TRU.TRON_VIEN ||
        nbThongTinRaVien?.huongDieuTri === HUONG_DIEU_TRI_NOI_TRU.CHUYEN_VIEN ||
        nbThongTinRaVien?.huongDieuTri ===
          HUONG_DIEU_TRI_NOI_TRU.CHUYEN_VIEN_THEO_YEU_CAU
      ) {
        setState({
          isDisplayThoiGianHenKham: true,
          isDisplayChuyenVien: [
            HUONG_DIEU_TRI_NOI_TRU.CHUYEN_VIEN,
            HUONG_DIEU_TRI_NOI_TRU.CHUYEN_VIEN_THEO_YEU_CAU,
          ].includes(nbThongTinRaVien?.huongDieuTri),
        });
      }
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [
    state?.show,
    nbThongTinRaVien,
    thongTinCoBan,
    dataHIEN_THI_CHECK_BOX_KHONG_TU_DONG_TINH_LAI_NGAY_GIUONG,
    dataMAC_DINH_KHONG_TINH_LAI_NGAY_GIUONG,
    isChotDotDieuTri,
  ]);
  useEffect(() => {
    let param = { active: true, page: "", size: "" };
    getListBacSi({
      dsMaThietLapVanBang: [THIET_LAP_CHUNG.BAC_SI],
      ...param,
    });
    getListPhongTongHop({
      page: 0,
      sort: "active,desc",
      size: 500,
      dsLoaiPhong: 30,
    });
  }, []);

  const nhomDichVuCap1PT = useMemo(() => {
    return (
      listAllNhomDichVuCap1.find(
        (item) => item.ma === dataMA_NHOM_DICH_VU_CAP1_PT
      ) || {}
    );
  }, [listAllNhomDichVuCap1, dataMA_NHOM_DICH_VU_CAP1_PT]);

  const { isBatBuocNhapThoiGian, isBatBuocNhapPhong } = useMemo(() => {
    if (!isNumber(dataBAT_BUOC_CHON_PHONG_HEN_KHI_NB_HEN_KHAM) || !huongDieuTri)
      return {
        isBatBuocNhapThoiGian: false,
        isBatBuocNhapPhong: false,
      };
    const value = Number(dataBAT_BUOC_CHON_PHONG_HEN_KHI_NB_HEN_KHAM);
    return {
      isBatBuocNhapThoiGian: [1, 3].includes(value) && huongDieuTri === 20,
      isBatBuocNhapPhong: [2, 3].includes(value) && huongDieuTri === 20,
    };
  }, [dataBAT_BUOC_CHON_PHONG_HEN_KHI_NB_HEN_KHAM, huongDieuTri]);

  useEffect(() => {
    const isEnableAutoSelect =
      dataMAC_DINH_CHON_TAT_CA_DV_PTTT_KHI_KET_THUC_DT?.eval();

    const currentSelected = form.getFieldValue("dsPtTtGiayRaVienId");

    if (
      state.show &&
      isEnableAutoSelect &&
      (!currentSelected || currentSelected.length === 0)
    ) {
      const defaultSelected = (listDsPttt || [])
        .filter((item) => item?.nhomDichVuCap1Id === nhomDichVuCap1PT?.id)
        .map((item) => item.id);

      if (defaultSelected.length > 0) {
        form.setFieldsValue({
          dsPtTtGiayRaVienId: defaultSelected,
        });
      }
    }
  }, [
    state.show,
    listDsPttt,
    dataMAC_DINH_CHON_TAT_CA_DV_PTTT_KHI_KET_THUC_DT,
    nhomDichVuCap1PT?.id,
  ]);

  const gioiTinh =
    (listgioiTinh || []).find(
      (item) => item.id === chiTietNguoiBenhNoiTru?.gioiTinh
    ) || {};
  const dataPhongHenKham = useMemo(
    () =>
      (listPhongThucHien || []).reduce(
        (a, c) =>
          a.some((item) => item.id === c.id)
            ? [...a]
            : [
                ...a,
                {
                  id: c.id,
                  ten: `${c?.ma} - ${c?.ten}`,
                },
              ],
        []
      ),
    [listPhongThucHien]
  );
  const onHanldeSubmit = (
    { tenBacSiDieuTri, khongTinhLaiNgayGiuong, ...values },
    boQuaKiemTraThoiGianTaiKhoa = false
  ) => {
    if (refIsSubmit.current) return;

    onChangeStateSubmit(true);
    setState({
      saveLoading: true,
    });
    const payload = {
      ...values,
      dsPhongHenKhamId:
        values.dsPhongHenKhamId?.length === dataPhongHenKham?.length
          ? null
          : values.dsPhongHenKhamId,
      phuongPhapDieuTri: values.phuongPhapDieuTri ?? null,
      soNgayHenKham: undefined, //ignore
      id: chiTietNguoiBenhNoiTru.id,
      thoiGianHenKham:
        values.thoiGianHenKham &&
        moment(values.thoiGianHenKham).format("YYYY-MM-DD HH:mm:ss"),
      thoiGianRaVien:
        values.thoiGianRaVien &&
        moment(values.thoiGianRaVien).format("YYYY-MM-DD HH:mm:ss"),
      thoiGianTuVong:
        values.thoiGianTuVong &&
        moment(values.thoiGianTuVong).format("YYYY-MM-DD HH:mm:ss"),
      // bacSiDieuTriId: nbThongTinRaVien.bacSiDieuTriId
      //   ? nbThongTinRaVien.bacSiDieuTriId
      //   : values.bacSiDieuTriId,
      bacSiDieuTriId: values.bacSiDieuTriId,
      tenNguoiBaoLanh: values.tenNguoiBaoLanh,
      sdtNguoiBaoLanh: values.sdtNguoiBaoLanh,
      soCanCuocNguoiBaoLanh: values.soCanCuocNguoiBaoLanh,
      ...(checkRole([
        ROLES["QUAN_LY_NOI_TRU"]
          .HIEN_THI_CHECK_BOX_KHONG_TU_DONG_TINH_LAI_NGAY_GIUONG,
      ]) && khongTinhLaiNgayGiuong
        ? {
            chotGiuong: true,
          }
        : {}),
      ...(boQuaKiemTraThoiGianTaiKhoa && {
        boQuaKiemTraThoiGianTaiKhoa: true,
      }),
    };

    const handleSuccess = () => {
      onChangeStateSubmit(false);
      setState({
        saveLoading: false,
      });

      getDsPhongGiuong({
        nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
        sort: "tuThoiGian,asc",
      });
      getNbNoiTruById(chiTietNguoiBenhNoiTru.id);
      getChiTietLuuTruBA(chiTietNguoiBenhNoiTru.id);
      getThongTinRaVien(chiTietNguoiBenhNoiTru.id);
      onViewGiayRaVien();
      onCancel();
    };

    const handleError = (err) => {
      if (err?.showKiemTraHs) {
        const { thoiGianRaVien } = err || {};

        onCancel();
        onKiemTraHoSo({
          thoiGianRaVien,
          tuKhoaId: chiTietNguoiBenhNoiTru.khoaNbId,
        });
      }
      if (err?.code === 1024) {
        onChangeStateSubmit(false);
        setState({ saveLoading: false });
        showConfirm(
          {
            title: t("common.thongBao"),
            content: err?.message,
            cancelText: t("common.huy"),
            okText: t("common.dongY"),
            showImg: false,
            showBtnOk: true,
            showBtnCancel: true,
            typeModal: "warning",
          },
          () => {
            onHanldeSubmit(values, true); //boQuaKiemTraThoiGianTaiKhoa: true
          },
          () => {
            onCancel();
          }
        );
        return;
      }
      setState({
        saveLoading: false,
      });
      onChangeStateSubmit(false);
    };

    if (
      payload.huongDieuTri === HUONG_DIEU_TRI_NOI_TRU.CHUYEN_VIEN ||
      payload.huongDieuTri === HUONG_DIEU_TRI_NOI_TRU.CHUYEN_VIEN_THEO_YEU_CAU
    ) {
      onChangeStateSubmit(false);
      setState({
        saveLoading: false,
      });

      onShowModalChuyenVien(
        {
          dsCdRaVienId: payload.dsCdChinhId,
          vienChuyenDenId: payload?.vienChuyenDenId,
          loai: LOAI_CHUYEN_VIEN.CHUYEN_VIEN,
          huongDieuTri: payload.huongDieuTri,
        },
        ({ huongDieuTri }) => {
          // Sử dụng hướng điều trị thay đổi bên trong modalChuyenVien chuyển viện
          payload.huongDieuTri = huongDieuTri;
          return new Promise((resolve, reject) => {
            ketThucDieuTri(payload, { showPopupErr: true })
              .then(() => {
                handleSuccess();
                resolve(true);
              })
              .catch((err) => {
                handleError(err);
                resolve(false);
              });
          });
        },
        () => {
          setTimeout(() => {
            history.go();
          }, 500);
        }
      );
    } else {
      ketThucDieuTri(payload, { showPopupErr: true })
        .then(() => {
          handleSuccess();
        })
        .catch((err) => {
          handleError(err);
        });
    }
  };
  const onCancel = () => {
    setState({ show: false, tuVong: false });
    form.resetFields();
  };
  const onSubmit = () => {
    form.submit();
  };

  const diffDate = (date1, date2) => {
    return moment(date1)
      .startOf("day")
      .diff(moment(date2).startOf("day"), "days");
  };

  const handleChangeThoiGianHenKham = (key, value) => {
    const { thoiGianRaVien, soNgayHenKham, thoiGianHenKham } =
      form.getFieldsValue();

    if (!thoiGianRaVien) {
      return;
    }

    if (key === "thoiGianRaVien") {
      if (soNgayHenKham) {
        form.setFieldsValue({
          thoiGianHenKham: moment(thoiGianRaVien).add(soNgayHenKham, "days"),
        });
      } else if (thoiGianHenKham) {
        form.setFieldsValue({
          soNgayHenKham: diffDate(thoiGianHenKham, thoiGianRaVien),
        });
      }
      return;
    }

    if (key === "soNgayHenKham") {
      if (value) {
        form.setFieldsValue({
          thoiGianHenKham: moment(thoiGianRaVien).add(value, "days"),
        });
      }
      return;
    }

    if (key === "thoiGianHenKham" && value) {
      form.setFieldsValue({
        soNgayHenKham: diffDate(value, thoiGianRaVien),
      });
    }
  };

  const onViewGiayRaVien = async () => {
    const { id } = chiTietNguoiBenhNoiTru;

    const [err, phieuIn] = await toSafePromise(
      getPhieuInTheoMa({
        nbDotDieuTriId: id,
        maManHinh: MAN_HINH_PHIEU_IN.NOI_TRU,
        maViTri: VI_TRI_PHIEU_IN.NOI_TRU.IN_BAC_SI,
        maPhieu: "P036", // EMR_BA070 giấy ra viện
      })
    );
    if (err) return;

    if (phieuIn.loaiBieuMau === LOAI_BIEU_MAU.BIEU_MAU_CHINH_SUA) {
      let mhParams = {};
      //kiểm tra phiếu ký số
      if (checkIsPhieuKySo(phieuIn)) {
        //nếu là ký số thì lấy các thông tin về lịch sử ký + tên chân ký + báo cáo id
        mhParams = {
          nbDotDieuTriId: id,
          maManHinh: MAN_HINH_PHIEU_IN.NOI_TRU,
          maViTri: VI_TRI_PHIEU_IN.NOI_TRU.IN_BAC_SI,
          kySo: true,
          maPhieuKy: phieuIn.ma,
        };
      }
      showFileEditor({
        phieu: phieuIn,
        nbDotDieuTriId: id,
        ma: phieuIn.ma,
        mhParams,
      });
    } else {
      if (checkIsPhieuKySo(phieuIn)) {
        refModalSignPrint.current &&
          refModalSignPrint.current.showToSign({
            phieuKy: phieuIn,
            payload: {
              maManHinh: MAN_HINH_PHIEU_IN.NOI_TRU,
              maViTri: VI_TRI_PHIEU_IN.NOI_TRU.IN_BAC_SI,
              nbDotDieuTriId: id,
            },
          });
      }
    }
  };

  const onChangeField = (key) => (e) => {
    let tuVong = false;
    let isDisplayThoiGianHenKham = false;
    let isDisplayChuyenVien = false;
    let ketQuaDieuTri = form.getFieldValue("ketQuaDieuTri");
    let tinhTrangRaVien = form.getFieldValue("huongDieuTri");
    if (key === "ketQuaDieuTri") {
      ketQuaDieuTri = e;
    }
    if (key === "tinhTrangRaVien") {
      tinhTrangRaVien = e;
    }
    if (ketQuaDieuTri === KET_QUA_DIEU_TRI.TU_VONG) {
      isDisplayThoiGianHenKham = true;
      tuVong = true;
    }
    if (
      [
        HUONG_DIEU_TRI_NOI_TRU.TRON_VIEN,
        HUONG_DIEU_TRI_NOI_TRU.CHUYEN_VIEN,
        HUONG_DIEU_TRI_NOI_TRU.CHUYEN_VIEN_THEO_YEU_CAU,
      ].includes(tinhTrangRaVien)
    ) {
      if (
        [
          HUONG_DIEU_TRI_NOI_TRU.CHUYEN_VIEN,
          HUONG_DIEU_TRI_NOI_TRU.CHUYEN_VIEN_THEO_YEU_CAU,
        ].includes(tinhTrangRaVien)
      ) {
        isDisplayChuyenVien = true;
      }
      isDisplayThoiGianHenKham = true;
    }

    setState({
      tuVong: tuVong,
      isDisplayThoiGianHenKham: isDisplayThoiGianHenKham,
      isDisplayChuyenVien,
    });
  };
  let tuoi =
    chiTietNguoiBenhNoiTru?.thangTuoi > 36 || chiTietNguoiBenhNoiTru?.tuoi
      ? `${chiTietNguoiBenhNoiTru?.tuoi} ${t("common.tuoi")}`
      : `${chiTietNguoiBenhNoiTru?.thangTuoi} ${t("common.thang")}`;

  const onValuesChange = (changedFields) => {
    if (changedFields.hasOwnProperty("huongDieuTri")) {
      form.setFieldsValue({ ketQuaDieuTri: null });
      return;
    }
    if (
      changedFields.hasOwnProperty("thoiGianRaVien") ||
      changedFields.hasOwnProperty("soNgayHenKham") ||
      changedFields.hasOwnProperty("thoiGianHenKham")
    ) {
      const [key, value] = Object.entries(changedFields)[0];
      handleChangeThoiGianHenKham(key, value);
    }
  };

  const isEmptyHTML = (htmlString) => {
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = htmlString;
    return (
      tempDiv.innerText.trim() === "" &&
      Array.from(tempDiv.childNodes).every((node) => {
        return (
          node.nodeType === Node.ELEMENT_NODE &&
          (node.tagName === "BR" || node.innerHTML.trim() === "")
        );
      })
    );
  };

  const validatePPDieuTri = (rule, value) => {
    if (isEmptyHTML(value)) {
      return Promise.reject(
        new Error(t("quanLyNoiTru.chuaNhapPhuongPhapDieuTri"))
      );
    }
    return Promise.resolve();
  };
  return (
    <>
      <GlobalStyle />
      <ModalTemplate
        width={800}
        ref={refModal}
        title={t("quanLyNoiTru.ketThucDieuTri")}
        rightTitle={
          <>
            <span className="font-color">
              {firstLetterWordUpperCase(chiTietNguoiBenhNoiTru?.tenNb)}
            </span>
            {gioiTinh.ten && (
              <span className="normal-weight"> - {gioiTinh.ten} </span>
            )}

            {tuoi && <span className="normal-weight">- {tuoi}</span>}
          </>
        }
        onCancel={onCancel}
        closable={false}
        actionLeft={<Button.QuayLai onClick={onCancel} />}
        actionRight={
          <Button
            type="primary"
            minWidth={100}
            onClick={onSubmit}
            rightIcon={<SVG.IcSave />}
            loading={state.saveLoading}
          >
            {t("common.luu")}
          </Button>
        }
      >
        <Main>
          <AlertMessage
            keyThietLap={THIET_LAP_CHUNG.TEN_CANH_BAO_POPUP_KET_THUC_DIEU_TRI}
          />
          <Form
            form={form}
            layout="vertical"
            onFinish={onHanldeSubmit}
            className="form-custom"
            onValuesChange={onValuesChange}
          >
            <Form.Item
              label={t("quanLyNoiTru.chanDoanRaVienChinh")}
              name="dsCdChinhId"
              rules={[
                {
                  required: true,
                  message: t("quanLyNoiTru.chuaNhapChanDoanRaVien"),
                },
              ]}
            >
              <SelectChanDoan
                mode="multiple"
                style={{
                  width: "100%",
                }}
                maxItem={1}
                placeholder={t("quanLyNoiTru.chanDoanRaVienChinh")}
              />
            </Form.Item>

            <Form.Item
              label={t("quanLyNoiTru.phuongPhapDieuTri")}
              rules={[
                {
                  required: true,
                  validator: validatePPDieuTri,
                },
              ]}
              name="phuongPhapDieuTri"
            >
              <InputEditable />
            </Form.Item>

            <Form.Item
              label={t("quanLyNoiTru.tinhTrangRaVien")}
              name="huongDieuTri"
              rules={[
                {
                  required: true,
                  message: t("quanLyNoiTru.vuiLongChonTinhTrangRaVien"),
                },
              ]}
            >
              <Select
                data={listHuongDieuTriKham}
                placeholder={t("quanLyNoiTru.tinhTrangRaVien")}
                onChange={onChangeField("tinhTrangRaVien")}
                setFallbackValue={setValueHuongDieuTri}
                popupClassName="select-huong-dieu-tri"
                className="select-huong-dieu-tri"
              />
            </Form.Item>
            <Form.Item
              label={t("quanLyNoiTru.ketQuaDieuTri")}
              name="ketQuaDieuTri"
              rules={[
                {
                  required: true,
                  message: t("quanLyNoiTru.vuiLongChonKetQuaDieuTri"),
                },
              ]}
            >
              <Select
                data={listKetQuaDieuTriKham}
                onChange={onChangeField("ketQuaDieuTri")}
                placeholder={t("quanLyNoiTru.ketQuaDieuTri")}
                setFallbackValue={setValueKetQuaDieuTri}
              />
            </Form.Item>
            {state?.tuVong && (
              <Form.Item
                label={t("quanLyNoiTru.lyDoTuVong")}
                name="lyDoTuVong"
                rules={[
                  {
                    required: state?.tuVong,
                    message: t("quanLyNoiTru.vuiLongChonLyDoTuVong"),
                  },
                ]}
              >
                <Select
                  data={listlyDoTuVong}
                  placeholder={t("quanLyNoiTru.lyDoTuVong")}
                />
              </Form.Item>
            )}
            {state?.tuVong && (
              <Form.Item
                label={t("quanLyNoiTru.diaDiemTuVong")}
                name="diaDiemTuVong"
                rules={[
                  {
                    required: state?.tuVong,
                    message: t("quanLyNoiTru.vuiLongChonDiaDiemTuVong"),
                  },
                ]}
              >
                <Select
                  data={listdiaDiemTuVong}
                  placeholder={t("quanLyNoiTru.diaDiemTuVong")}
                />
              </Form.Item>
            )}
            {state?.tuVong && (
              <Form.Item
                label={t("quanLyNoiTru.thoiGianTuVong")}
                name="thoiGianTuVong"
                rules={[
                  {
                    required: state?.tuVong,
                    message: t("quanLyNoiTru.vuiLongNhapThoiGianTuVong"),
                  },
                ]}
              >
                <DateTimePicker
                  placeholder={t("quanLyNoiTru.thoiGianTuVong")}
                  disabledDate={(date) =>
                    moment(date).isBefore(
                      chiTietNguoiBenhNoiTru?.thoiGianVaoVien
                    )
                  }
                />
              </Form.Item>
            )}
            {state?.tuVong && (
              <Form.Item
                label={t("quanLyNoiTru.ghiChuTuVong")}
                name="ghiChuTuVong"
              >
                <Input placeholder={t("quanLyNoiTru.ghiChuTuVong")}></Input>
              </Form.Item>
            )}
            <Form.Item
              label={t("common.thoiGianRaVien")}
              name="thoiGianRaVien"
              rules={[
                {
                  required: true,
                  message: t("quanLyNoiTru.vuiLongNhapThoiGianRaVien"),
                },
              ]}
            >
              <DateTimePicker
                showTime
                format={"DD/MM/YYYY HH:mm:ss"}
                className="input-filter"
                placeholder={t("common.thoiGianRaVien")}
                disabledDate={(date) =>
                  moment(date).isBefore(chiTietNguoiBenhNoiTru?.thoiGianVaoVien)
                }
              />
            </Form.Item>
            {state.isDisplayChuyenVien && (
              <Form.Item
                label={t("quanLyNoiTru.giayChuyenVien.vienChuyenDen")}
                name="vienChuyenDenId"
                rules={[
                  {
                    required: true,
                    message: t(
                      "quanLyNoiTru.giayChuyenVien.vuiLongNhapVienChuyenDen"
                    ),
                  },
                ]}
              >
                <Select
                  data={listAllBenhVien}
                  getLabel={getBenhVienLabel}
                  placeholder={t("quanLyNoiTru.giayChuyenVien.vienChuyenDen")}
                  dropdownMatchSelectWidth={600}
                />
              </Form.Item>
            )}
            {!state?.isDisplayThoiGianHenKham && (
              <>
                <Form.Item
                  label={t("quanLyNoiTru.henQuayLaiSauSoNgay")}
                  name="soNgayHenKham"
                >
                  <InputNumberField
                    decimalScale={0}
                    thousandSeparator={false}
                    allowNegative={false}
                    allowLeadingZeros={false}
                    isAllowed={({ floatValue }) => {
                      return floatValue !== undefined
                        ? floatValue > 0 && floatValue < 10000
                        : true;
                    }}
                    placeholder={t("quanLyNoiTru.henQuayLaiSauSoNgay")}
                  />
                </Form.Item>
                <Form.Item
                  noStyle
                  shouldUpdate={(prevValues, currentValues) =>
                    prevValues.thoiGianRaVien !== currentValues.thoiGianRaVien
                  }
                >
                  {({ getFieldValue }) => {
                    const thoiGianRaVien = getFieldValue("thoiGianRaVien");
                    return (
                      <Form.Item
                        label={t("quanLyNoiTru.thoiGianHenKham")}
                        name="thoiGianHenKham"
                        rules={[
                          {
                            required: isBatBuocNhapThoiGian,
                            message: t(
                              "quanLyNoiTru.vuiLongChonThoiGianHenKham"
                            ),
                          },
                        ]}
                      >
                        <DateTimePicker
                          placeholder={t("quanLyNoiTru.thoiGianHenKham")}
                          disabledDate={(date) =>
                            (date &&
                              thoiGianRaVien &&
                              moment(date).isSameOrBefore(
                                thoiGianRaVien,
                                "date"
                              )) ||
                            moment(date).isSameOrBefore(
                              chiTietNguoiBenhNoiTru?.thoiGianVaoVien,
                              "date"
                            )
                          }
                        />
                      </Form.Item>
                    );
                  }}
                </Form.Item>
              </>
            )}
            <Form.Item
              label={t("quanLyNoiTru.bacSiDieuTri")}
              name="bacSiDieuTriId"
              rules={[
                {
                  required: true,
                  message: t("quanLyNoiTru.vuiLongChonBacSiDieuTri"),
                },
              ]}
            >
              <Select
                data={listBacSi}
                placeholder={t("quanLyNoiTru.bacSiDieuTri")}
                getLabel={(item) =>
                  `${item?.ma || ""} ${
                    item?.vietTatHocHamHocVi
                      ? `- ${item.vietTatHocHamHocVi}`
                      : ""
                  } ${item?.ten ? `${item.ten}` : ""}`.trim()
                }
              />
            </Form.Item>
            <Form.Item
              label={t("quanLyNoiTru.phongHenKham")}
              name="dsPhongHenKhamId"
              rules={[
                {
                  required: isBatBuocNhapPhong,
                  message: t("quanLyNoiTru.vuiLongChonPhongHenKham"),
                },
              ]}
            >
              <Select
                data={dataPhongHenKham}
                mode="multiple"
                hasAllOption={false}
                placeholder={t("quanLyNoiTru.vuiLongChonPhongHenKham")}
              />
            </Form.Item>
            <Form.Item
              label={t("quanLyNoiTru.danhSachPTTT")}
              name="dsPtTtGiayRaVienId"
            >
              <Select
                data={listDsPttt}
                placeholder={t("quanLyNoiTru.chonDanhSachPTTT")}
                mode="multiple"
              />
            </Form.Item>
            {checkRole([
              ROLES["QUAN_LY_NOI_TRU"]
                .HIEN_THI_CHECK_BOX_KHONG_TU_DONG_TINH_LAI_NGAY_GIUONG,
            ]) ? (
              <Form.Item
                label=" "
                valuePropName="checked"
                name="khongTinhLaiNgayGiuong"
              >
                <Checkbox softDisabled={isChotDotDieuTri}>
                  {t("quanLyNoiTru.khongTinhLaiNgayGiuong")}
                </Checkbox>
              </Form.Item>
            ) : null}
          </Form>
          <ModalSignPrint ref={refModalSignPrint} />
        </Main>
      </ModalTemplate>
    </>
  );
};

const InputEditable = ({ value, onChange, ...rest }) => {
  return (
    <div
      contentEditable
      onBlur={(e) => onChange(e?.target?.innerHTML)}
      dangerouslySetInnerHTML={{ __html: value }}
      {...rest}
    ></div>
  );
};

export default forwardRef(ModalKetThucDieuTri);
