import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from "react";
import { useTranslation } from "react-i18next";
import { useSelector, useDispatch } from "react-redux";
import { AutoComplete, Col, Collapse, message, Row, Input } from "antd";
import moment, { isMoment } from "moment";
import { debounce } from "lodash";
import {
  useCache,
  useConfirm,
  useEnum,
  useLoading,
  useStore,
  useThietLap,
} from "hooks";
import { useHuongDieuTri } from "pages/khamBenh/hooks/useHuongDieuTri";
import { useKetQuaDieuTri } from "pages/khamBenh/hooks/useKetQuaDieuTri";
import { firstLetterWordUpperCase, getDsMoTa, getMoTaChanDoan } from "utils";
import {
  AuthWrapper,
  Button,
  ModalTemplate,
  Select,
  SelectLargeData,
  DateTimePicker,
  TextField,
  DatePicker,
} from "components";
import {
  DOI_TUONG,
  ENUM,
  HUONG_DIEU_TRI_NOI_TRU,
  MAN_HINH_AP_DUNG,
  ROLES,
  THIET_LAP_CHUNG,
  TRANG_THAI_NB,
  CO_KHONG,
  DS_TINH_CHAT_KHOA,
  CACHE_KEY,
} from "constants/index";
import { checkRole } from "lib-utils/role-utils";
import { SVG } from "assets";
import { SelectGroup } from "../../ToDieuTri/styled";
import { AutoCompleteStyled, Main, TextFieldStyled } from "./styled";
import useListAllNhanVienQuyenKy from "pages/khamBenh/KetLuan/hooks/useListAllNhanVienQuyenKy";
import { selectMaTen } from "redux-store/selectors";
import CustomTag from "pages/khamBenh/KhamCoBan/ChanDoan/ChanDoanBenh/CustomTag";
import InputTuoiThai from "./InputTuoiThai";

const { SelectChanDoan, SelectBenhVien } = SelectLargeData;
const { Panel } = Collapse;

const lisTrangThaiNb = [
  TRANG_THAI_NB.DA_RA_VIEN, //100
  TRANG_THAI_NB.TU_CHOI_DUYET_CHI_PHI, // 102
  TRANG_THAI_NB.CHO_DUYET_CHI_PHI, //103
  TRANG_THAI_NB.DA_DUYET_CHI_PHI, // 106
  TRANG_THAI_NB.HEN_DIEU_TRI, //110
  TRANG_THAI_NB.TU_CHOI_DUYET_CHI_PHI_HEN_DIEU_TRI, // 112
  TRANG_THAI_NB.CHO_DUYET_CHI_PHI_HEN_DIEU_TRI, // 113
  TRANG_THAI_NB.DA_DUYET_CHI_PHI_HEN_DIEU_TRI, // 116
  TRANG_THAI_NB.DA_THANH_TOAN_RA_VIEN, //120
  TRANG_THAI_NB.DA_THANH_TOAN_HEN_DIEU_TRI, //130
];

const ModalThongTinRaVien = forwardRef((props, ref) => {
  const { t } = useTranslation();
  const [state, _setState] = useState({
    openNghiNgoaiTruTuNgay: false,
    soNgayHenKham: null,
    dsMoTaChinh: [],
    dsMoTaKemTheo: [],
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const { showConfirm } = useConfirm();
  const refModal = useRef(null);
  const refDataSave = useRef({});
  const { showLoading, hideLoading } = useLoading();
  const { onShowModalChuyenVien, isRoleSuaThongTinHenKham } = props;
  const chiTietNguoiBenhNoiTru = useStore(
    "danhSachNguoiBenhNoiTru.chiTietNguoiBenhNoiTru",
    {}
  );

  const nbThongTinRaVien = useStore("nbDotDieuTri.nbThongTinRaVien", null);
  const listToDieuTri = useStore("toDieuTri.listToDieuTri");
  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan", {});
  const listPhongThucHien = useStore("phong.listPhong", []);
  const nbNguoiBaoLanh = useStore(
    "nbDotDieuTri.thongTinBenhNhan.nbNguoiBaoLanh",
    {}
  );
  const listAllLoiDan = useStore("loiDan.listAllLoiDan", []);
  const { id, nhanVienId } = useStore("auth.auth", {});

  const { listNhanVien } = useSelector((state) => state.nhanVien);
  const listKhoaTheoTaiKhoan = useStore("khoa.listKhoaTheoTaiKhoan", []);
  const listFilter = useStore("pttt.listFilter", []);

  const [khoaLamViec, _, isLoadFinish] = useCache(
    id + DS_TINH_CHAT_KHOA.NOI_TRU,
    CACHE_KEY.DATA_KHOA_LAM_VIEC,
    null,
    false
  );

  const {
    nbDotDieuTri: { updateThongTinRaVien, getThongTinRaVien, getById },
    danhSachNguoiBenhNoiTru: { getNbNoiTruById },
    phong: { getListPhongTongHop },
    nbRaVien: { dayPhieuRaVienById, huyPhieuRaVienById },
    loiDan: { getListAllLoiDan },
    pttt: { onSearchDanhSachPhauThuatThuThuat },
    toDieuTri: { getToDieuTri },
  } = useDispatch();

  const [listDiaDiemTuVong] = useEnum(ENUM.DIA_DIEM_TU_VONG);
  const [listLyDoTuVong] = useEnum(ENUM.LY_DO_TU_VONG);
  const [listGioiTinh] = useEnum(ENUM.GIOI_TINH);
  const [listTheBenhLao] = useEnum(ENUM.THE_BENH_LAO);

  const [listTreEmKhongThe] = useEnum(ENUM.TRE_EM_KHONG_THE);
  const [listDinhChiThaiNghen] = useEnum(ENUM.DINH_CHI_THAI_NGHEN);
  const [dataHIEN_THI_CD_CHINH_RA_VIEN_NT] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_CD_CHINH_RA_VIEN_NT
  );
  const [MOI_QUAN_HE_CHA, isFinishCha] = useThietLap(
    THIET_LAP_CHUNG.MOI_QUAN_HE_CHA
  );
  const [MOI_QUAN_HE_ME, isFinishMe] = useThietLap(
    THIET_LAP_CHUNG.MOI_QUAN_HE_ME
  );
  const [dataBAT_BUOC_TRUONG_KHOA] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_TRUONG_KHOA
  );
  const [THONG_TIN_BAT_BUOC_THONG_TIN_RA_VIEN] = useThietLap(
    THIET_LAP_CHUNG.THONG_TIN_BAT_BUOC_THONG_TIN_RA_VIEN
  );
  const [MAC_DINH_THU_TRUONG_DON_VI] = useThietLap(
    THIET_LAP_CHUNG.MAC_DINH_THU_TRUONG_DON_VI
  );
  const [dataCHO_PHEP_NHAP_MO_TA_CHAN_DOAN] = useThietLap(
    THIET_LAP_CHUNG.CHO_PHEP_NHAP_MO_TA_CHAN_DOAN,
    "TRUE"
  );
  const [dataTU_DONG_DIEN_CD_MO_TA_THEO_CAC_CD] = useThietLap(
    THIET_LAP_CHUNG.TU_DONG_DIEN_CD_MO_TA_THEO_CAC_CD,
    "FALSE"
  );
  const [dataTU_DONG_CAP_NHAT_CHAN_DOAN_BENH_KEM_THEO] = useThietLap(
    THIET_LAP_CHUNG.TU_DONG_CAP_NHAT_CHAN_DOAN_BENH_KEM_THEO,
    "FALSE"
  );

  const { listHuongDieuTriKham, setValueHuongDieuTri } = useHuongDieuTri({
    callApi: true,
    manHinh: MAN_HINH_AP_DUNG.NOI_TRU,
  });

  const { listKetQuaDieuTriKham, setValueKetQuaDieuTri } = useKetQuaDieuTri({
    callApi: true,
    huongDieuTri: state?.huongDieuTri,
    manHinh: MAN_HINH_AP_DUNG.NOI_TRU,
  });

  const [listAllNhanVienQuyenKyMemo] = useListAllNhanVienQuyenKy({
    viTri: "noiTruThongTinRaVien",
  });

  const isReadonly = useMemo(() => {
    return [
      TRANG_THAI_NB.DA_RA_VIEN,
      TRANG_THAI_NB.HEN_DIEU_TRI,
      TRANG_THAI_NB.DA_THANH_TOAN_RA_VIEN,
      TRANG_THAI_NB.DA_THANH_TOAN_HEN_DIEU_TRI,
    ].includes(chiTietNguoiBenhNoiTru?.trangThai);
  }, [chiTietNguoiBenhNoiTru]);

  const thietLapMoiQhCha = useMemo(() => {
    if (isFinishCha && MOI_QUAN_HE_CHA) {
      if (nbNguoiBaoLanh?.moiQuanHe?.ma === MOI_QUAN_HE_CHA?.toUpperCase()) {
        return {
          tenCha: nbNguoiBaoLanh?.hoTen,
          soBaoHiemXaHoiCha: nbNguoiBaoLanh?.soBaoHiemXaHoi,
        };
      } else if (
        nbNguoiBaoLanh?.moiQuanHe2?.ma === MOI_QUAN_HE_CHA?.toUpperCase()
      ) {
        return {
          tenCha: nbNguoiBaoLanh?.hoTen2,
          soBaoHiemXaHoiCha: nbNguoiBaoLanh?.soBaoHiemXaHoi2,
        };
      }
    }
    return "";
  }, [isFinishCha, MOI_QUAN_HE_CHA, nbNguoiBaoLanh]);

  const thietLapMoiQhMe = useMemo(() => {
    if (isFinishMe && MOI_QUAN_HE_ME) {
      if (nbNguoiBaoLanh?.moiQuanHe?.ma === MOI_QUAN_HE_ME?.toUpperCase()) {
        return {
          tenMe: nbNguoiBaoLanh?.hoTen,
          soBaoHiemXaHoiMe: nbNguoiBaoLanh?.soBaoHiemXaHoi,
        };
      } else if (
        nbNguoiBaoLanh?.moiQuanHe2?.ma === MOI_QUAN_HE_ME?.toUpperCase()
      ) {
        return {
          tenMe: nbNguoiBaoLanh?.hoTen2,
          soBaoHiemXaHoiMe: nbNguoiBaoLanh?.soBaoHiemXaHoi2,
        };
      }
    }
    return "";
  }, [isFinishMe, MOI_QUAN_HE_ME, nbNguoiBaoLanh]);

  const isBatBuocTruongKhoa = useMemo(() => {
    return (
      dataBAT_BUOC_TRUONG_KHOA &&
      dataBAT_BUOC_TRUONG_KHOA.toLowerCase() === "true"
    );
  }, [dataBAT_BUOC_TRUONG_KHOA]);

  const listAllLoiDanOptions = useMemo(() => {
    return listAllLoiDan.map((item) => ({
      label: item.ten,
      value: item.ten,
    }));
  }, [listAllLoiDan]);

  const listNhanVienOptions = useMemo(() => {
    return listNhanVien.map((item) => ({
      ten: `${item.ma} - ${item.ten}`,
      id: item.id,
    }));
  }, [listNhanVien]);

  const listDsPttt = useMemo(() => {
    return (listFilter || []).map((item) => ({
      id: item?.id,
      ten: `${item?.maDichVu} - ${item?.tenDichVu}`,
    }));
  }, [listFilter]);

  useEffect(() => {
    if (chiTietNguoiBenhNoiTru?.id && state.show) {
      getById(chiTietNguoiBenhNoiTru?.id);
      onSearchDanhSachPhauThuatThuThuat({
        size: 500,
        dataSearch: {
          nbDotDieuTriId: chiTietNguoiBenhNoiTru?.id,
          dsTrangThaiHoan: [0, 10, 20],
        },
      });
      getToDieuTri({ nbDotDieuTriId: chiTietNguoiBenhNoiTru?.id });
    }
  }, [chiTietNguoiBenhNoiTru, state.show]);

  useEffect(() => {
    if (isLoadFinish && nhanVienId) {
      getListAllLoiDan(
        {
          dsKhoaChiDinhId: khoaLamViec?.id,
          active: true,
          dsBacSiChiDinhId: nhanVienId,
        },
        {
          reload: true,
        }
      );
    }
  }, [isLoadFinish, nhanVienId]);

  const gioiTinh =
    (listGioiTinh || []).find(
      (item) => item.id === chiTietNguoiBenhNoiTru?.gioiTinh
    ) || {};

  const handleClickBack = () => {
    refModal.current && refModal.current.hide();
    setState({ show: false });
  };

  useEffect(() => {
    if (nbThongTinRaVien && state.show) {
      let dsCdChinhId;
      if (!nbThongTinRaVien?.dsCdChinhId?.length) {
        if (dataHIEN_THI_CD_CHINH_RA_VIEN_NT !== "1") {
          if (listToDieuTri.length) {
            dsCdChinhId = listToDieuTri?.[0]?.dsCdChinhId;
          }
        }
      } else {
        dsCdChinhId = nbThongTinRaVien?.dsCdChinhId;
      }

      const tuoiThai =
        nbThongTinRaVien?.tuoiThai == null || nbThongTinRaVien?.tuoiThai === 0
          ? nbThongTinRaVien?.dsThongTinCon?.[0]?.ngayThai || 0
          : nbThongTinRaVien?.tuoiThai;

      let dsCdKemTheoId = nbThongTinRaVien?.dsCdKemTheoId || [];

      if (
        dataTU_DONG_CAP_NHAT_CHAN_DOAN_BENH_KEM_THEO?.eval() &&
        !nbThongTinRaVien?.hasOwnProperty("dsCdKemTheoId")
      ) {
        dsCdKemTheoId = [
          ...new Set([
            ...(listToDieuTri || []).flatMap((item) =>
              Array.isArray(item.dsCdKemTheoId) && item.dsCdKemTheoId.length > 0
                ? item.dsCdKemTheoId
                : []
            ),
            ...dsCdKemTheoId,
          ]),
        ];
      }

      let data = {
        dsCdChinhId: dsCdChinhId,
        dsCdKemTheoId: dsCdKemTheoId,
        moTa: nbThongTinRaVien?.moTa,
        phuongPhapDieuTri: nbThongTinRaVien?.phuongPhapDieuTri,
        loiDanBacSi: nbThongTinRaVien?.loiDanBacSi
          ? nbThongTinRaVien?.loiDanBacSi
          : listAllLoiDan.length === 1 && !dsCdChinhId?.length // nếu chỉ có 1 lời dặn và không có chẩn đoán chính (dữ liệu ra viện) thì mặc định là lời dặn đầu tiên
            ? listAllLoiDan[0]?.ten
            : null,
        soBaoHiemXaHoi: nbThongTinRaVien?.soBaoHiemXaHoi,
        tinhTrang: nbThongTinRaVien?.tinhTrang,
        quaTrinhBenhLy: nbThongTinRaVien?.quaTrinhBenhLy,
        ketQuaCls: nbThongTinRaVien?.ketQuaCls,
        cheDoTiepTheo: nbThongTinRaVien?.cheDoTiepTheo,
        huongDieuTri: nbThongTinRaVien?.huongDieuTri,
        ketQuaDieuTri: nbThongTinRaVien?.ketQuaDieuTri,
        thoiGianTuVong:
          nbThongTinRaVien?.thoiGianTuVong &&
          moment(nbThongTinRaVien?.thoiGianTuVong),
        diaDiemTuVong: nbThongTinRaVien?.diaDiemTuVong,
        lyDoTuVong: nbThongTinRaVien?.lyDoTuVong,
        ghiChuTuVong: nbThongTinRaVien?.ghiChuTuVong,
        thoiGianRaVien:
          nbThongTinRaVien?.thoiGianRaVien &&
          moment(nbThongTinRaVien?.thoiGianRaVien),
        thoiGianHenKham:
          nbThongTinRaVien?.thoiGianHenKham &&
          moment(nbThongTinRaVien?.thoiGianHenKham),
        soNgayHenKham:
          nbThongTinRaVien?.thoiGianRaVien && nbThongTinRaVien?.thoiGianHenKham
            ? moment(nbThongTinRaVien?.thoiGianRaVien).diff(
              nbThongTinRaVien?.thoiGianHenKham,
              "days"
            )
            : null,
        treEmKhongThe: nbThongTinRaVien?.treEmKhongThe || 0,
        tenCha: thietLapMoiQhCha?.tenCha,
        tenMe: thietLapMoiQhMe?.tenMe,
        thuTruongId:
          nbThongTinRaVien?.thuTruongId ||
          (MAC_DINH_THU_TRUONG_DON_VI &&
            listAllNhanVienQuyenKyMemo.find(
              (item) => item.ma === MAC_DINH_THU_TRUONG_DON_VI
            )?.id),
        dinhChiThaiNghen: nbThongTinRaVien?.dinhChiThaiNghen || 0,
        orgTuoiThai: tuoiThai || null,
        tuoiThai: tuoiThai || null,
        nguyenNhanDinhChiThai: nbThongTinRaVien?.nguyenNhanDinhChiThai,
        thoiGianDinhChiThai:
          nbThongTinRaVien?.thoiGianDinhChiThai &&
          moment(nbThongTinRaVien?.thoiGianDinhChiThai),
        truongKhoaId: nbThongTinRaVien?.truongKhoaId,
        tuThoiGianNghiNgoaiTru: nbThongTinRaVien?.tuThoiGianNghiNgoaiTru
          ? moment(nbThongTinRaVien?.tuThoiGianNghiNgoaiTru)
          : null,
        denThoiGianNghiNgoaiTru:
          nbThongTinRaVien?.denThoiGianNghiNgoaiTru &&
          moment(nbThongTinRaVien?.denThoiGianNghiNgoaiTru),
        thoiGianChungTu: nbThongTinRaVien?.thoiGianChungTu
          ? moment(nbThongTinRaVien?.thoiGianChungTu)
          : null,

        truongKhoa: nbThongTinRaVien?.truongKhoa,
        vienChuyenDenId: nbThongTinRaVien?.vienChuyenDenId,
        dsPhongHenKhamId: nbThongTinRaVien?.dsPhongHenKhamId,
        soBaoHiemXaHoiCha: thietLapMoiQhCha?.soBaoHiemXaHoiCha,
        theBenhLao: nbThongTinRaVien?.theBenhLao,
        viemLoetTiDe: nbThongTinRaVien?.viemLoetTiDe,
        viemPhoiDoUDong: nbThongTinRaVien?.viemPhoiDoUDong,
        hiv: nbThongTinRaVien?.hiv,
        dsCdYhctChinhId: nbThongTinRaVien?.dsCdYhctChinhId,
        dsCdYhctKemTheoId: nbThongTinRaVien?.dsCdYhctKemTheoId,
        dsPtTtGiayRaVienId: nbThongTinRaVien?.dsPtTtGiayRaVien?.map(
          (item) => item.id
        ),
        dsMoTaChinh: getDsMoTa(nbThongTinRaVien, "dsCdChinh"),
        dsMoTaKemTheo: getDsMoTa(nbThongTinRaVien, "dsCdKemTheo"),
      };
      setState(data);

      refDataSave.current = data;
    }
  }, [
    nbThongTinRaVien,
    listToDieuTri,
    thietLapMoiQhCha,
    thietLapMoiQhMe,
    listAllLoiDan,
    listAllNhanVienQuyenKyMemo,
    state.show,
    dataHIEN_THI_CD_CHINH_RA_VIEN_NT,
    dataTU_DONG_CAP_NHAT_CHAN_DOAN_BENH_KEM_THEO,
  ]);
  const dataPhongHenKham = useMemo(
    () =>
      (listPhongThucHien || []).reduce(
        (a, c) =>
          a.some((item) => item.id === c.id)
            ? [...a]
            : [
              ...a,
              {
                id: c.id,
                ten: `${c?.ma} - ${c?.ten}`,
              },
            ],
        []
      ),
    [listPhongThucHien]
  );

  const kiemTraHuongDieuTriChuyenVien = (huongDieuTri) => {
    return [
      HUONG_DIEU_TRI_NOI_TRU.CHUYEN_VIEN,
      HUONG_DIEU_TRI_NOI_TRU.CHUYEN_VIEN_THEO_YEU_CAU,
    ].includes(huongDieuTri);
  };

  const handleClickNext = (callback) =>
    debounce(() => {
      const {
        dsCdChinhId,
        dsCdKemTheoId,
        moTa,
        phuongPhapDieuTri,
        loiDanBacSi,
        tinhTrang,
        quaTrinhBenhLy,
        ketQuaCls,
        cheDoTiepTheo,
        huongDieuTri,
        ketQuaDieuTri,
        thoiGianTuVong,
        diaDiemTuVong,
        lyDoTuVong,
        ghiChuTuVong,
        thoiGianRaVien,
        thoiGianHenKham,
        treEmKhongThe,
        tenCha,
        tenMe,
        thuTruongId,
        dinhChiThaiNghen,
        tuoiThai,
        nguyenNhanDinhChiThai,
        thoiGianDinhChiThai,
        tuThoiGianNghiNgoaiTru,
        denThoiGianNghiNgoaiTru,
        thoiGianChungTu,
        truongKhoaId,
        maTheBhyt,
        truongKhoa,
        soBaoHiemXaHoi,
        vienChuyenDenId,
        dsPhongHenKhamId,
        soBaoHiemXaHoiCha,
        soBaoHiemXaHoiMe,
        theBenhLao,
        viemLoetTiDe,
        viemPhoiDoUDong,
        hiv,
        dsCdYhctChinhId,
        dsCdYhctKemTheoId,
        dsPtTtGiayRaVienId,
      } = refDataSave.current || {};
      const payload = {
        ...chiTietNguoiBenhNoiTru,
        dsCdChinhId,
        dsCdKemTheoId,
        moTa,
        phuongPhapDieuTri,
        loiDanBacSi,
        tinhTrang,
        quaTrinhBenhLy,
        ketQuaCls,
        cheDoTiepTheo,
        huongDieuTri,
        ketQuaDieuTri,
        thoiGianTuVong,
        diaDiemTuVong,
        lyDoTuVong,
        ghiChuTuVong,
        thoiGianRaVien,
        thoiGianHenKham,
        treEmKhongThe,
        tenCha,
        tenMe,
        thuTruongId,
        dinhChiThaiNghen,
        tuoiThai: tuoiThai || null,
        nguyenNhanDinhChiThai,
        thoiGianDinhChiThai,
        tuThoiGianNghiNgoaiTru,
        denThoiGianNghiNgoaiTru,
        thoiGianChungTu,
        truongKhoaId,
        truongKhoa,
        maTheBhyt,
        soBaoHiemXaHoi: soBaoHiemXaHoi ? soBaoHiemXaHoi : null,
        id: chiTietNguoiBenhNoiTru?.id,
        vienChuyenDenId: kiemTraHuongDieuTriChuyenVien(huongDieuTri)
          ? vienChuyenDenId
          : null,
        dsPhongHenKhamId:
          dsPhongHenKhamId?.length === dataPhongHenKham?.length
            ? null
            : dsPhongHenKhamId,
        soBaoHiemXaHoiCha,
        soBaoHiemXaHoiMe,
        theBenhLao,
        viemLoetTiDe,
        viemPhoiDoUDong,
        hiv,
        dsCdYhctKemTheoId,
        dsCdYhctChinhId,
        ...(kiemTraHuongDieuTriChuyenVien(huongDieuTri) && {
          soNgayHenKham: null,
          dsPhongHenKhamId: null,
          thoiGianHenKham: null,
        }),
        dsPtTtGiayRaVienId,
        moTaChanDoan: {
          dsCdChinh: state.dsMoTaChinh,
          dsCdKemTheo: state.dsMoTaKemTheo,
        },
      };
      const validateThoiGianNghiNgoaiTru = () => {
        return (
          (denThoiGianNghiNgoaiTru && !tuThoiGianNghiNgoaiTru) ||
          (tuThoiGianNghiNgoaiTru && !denThoiGianNghiNgoaiTru) ||
          (isMoment(tuThoiGianNghiNgoaiTru) &&
            isMoment(denThoiGianNghiNgoaiTru) &&
            moment(tuThoiGianNghiNgoaiTru).valueOf() >=
            moment(denThoiGianNghiNgoaiTru).valueOf())
        );
      };
      const validateThoiGianRavien = () => {
        return (
          thoiGianRaVien &&
          chiTietNguoiBenhNoiTru?.thoiGianVaoKhoaNhapVien &&
          moment(thoiGianRaVien).valueOf() <
          moment(chiTietNguoiBenhNoiTru?.thoiGianVaoKhoaNhapVien).valueOf()
        );
      };
      if (validateThoiGianRavien()) {
        message.error(
          t("quanLyNoiTru.thoiGianRaVienKhongDuocNhoHonThoiGianVaoVien")
        );
        return;
      }

      const validateThongTinBatBuocRaVien = () => {
        return (
          THONG_TIN_BAT_BUOC_THONG_TIN_RA_VIEN?.eval() &&
          (!theBenhLao ||
            typeof state?.viemLoetTiDe !== "boolean" ||
            typeof state?.viemPhoiDoUDong !== "boolean" ||
            typeof state?.hiv !== "boolean")
        );
      };

      const validateThoiGianHenKham = () => {
        if (!thoiGianHenKham || !thoiGianRaVien) {
          return true; // Không validate nếu thiếu dữ liệu
        }

        // Kiểm tra ngày hẹn khám phải lớn hơn ngày ra viện
        return moment(thoiGianHenKham)
          .startOf("day")
          .isAfter(moment(thoiGianRaVien).startOf("day"));
      };

      if (
        (!lyDoTuVong &&
          (state?.huongDieuTri === 204 ||
            [5, 8]?.includes(state.ketQuaDieuTri))) ||
        (!thoiGianTuVong &&
          (state?.huongDieuTri === 204 ||
            [5, 8]?.includes(state.ketQuaDieuTri))) ||
        !ketQuaDieuTri ||
        !huongDieuTri ||
        !phuongPhapDieuTri ||
        phuongPhapDieuTri === "<br>" ||
        !dsCdChinhId ||
        validateThoiGianNghiNgoaiTru() ||
        (!thoiGianRaVien &&
          lisTrangThaiNb.includes(chiTietNguoiBenhNoiTru?.trangThai)) ||
        (!vienChuyenDenId && kiemTraHuongDieuTriChuyenVien(huongDieuTri)) ||
        (!dsPhongHenKhamId?.length &&
          chiTietNguoiBenhNoiTru?.trangThai === TRANG_THAI_NB.DA_RA_VIEN &&
          state.thoiGianHenKham &&
          !kiemTraHuongDieuTriChuyenVien(huongDieuTri)) ||
        (!tuoiThai && dinhChiThaiNghen === 1) ||
        (!nguyenNhanDinhChiThai && dinhChiThaiNghen === 1) ||
        (!thoiGianDinhChiThai && dinhChiThaiNghen === 1) ||
        (isBatBuocTruongKhoa && (!truongKhoaId || !thuTruongId)) ||
        validateThongTinBatBuocRaVien() ||
        !validateThoiGianHenKham()
      ) {
        setState({ validate: true });

        // Hiển thị thông báo lỗi cụ thể cho thời gian hẹn khám
        if (!validateThoiGianHenKham() && thoiGianHenKham && thoiGianRaVien) {
          message.error(
            t("quanLyNoiTru.thoiGianHenKhamPhaiLonHonThoiGianRaVien")
          );
          return;
        }
      } else {
        setState({ validate: false });
        showLoading();

        updateThongTinRaVien({ ...payload })
          .then((s) => {
            getNbNoiTruById(chiTietNguoiBenhNoiTru?.id);
            getThongTinRaVien(chiTietNguoiBenhNoiTru?.id).then((res) => {
              if (res?.huongDieuTri === 40 || res?.huongDieuTri === 42) {
                onShowModalChuyenVien({
                  dsCdRaVienId: res.dsCdChinhId,
                  dsCdKemTheoId: res.dsCdKemTheoId,
                  moTa: res.moTa,
                  vienChuyenDenId: res?.vienChuyenDenId,
                  huongDieuTri: res?.huongDieuTri,
                });
              }
            });

            if (callback) {
              callback();
            } else {
              setState({ show: false });
            }
          })
          .catch((err) => {
            if (err.code === 8021 || err.code === 8022) {
              refDataSave.current.thoiGianRaVien =
                nbThongTinRaVien?.thoiGianRaVien &&
                moment(nbThongTinRaVien?.thoiGianRaVien);

              setState({
                thoiGianRaVien:
                  nbThongTinRaVien?.thoiGianRaVien &&
                  moment(nbThongTinRaVien?.thoiGianRaVien),
              });
            } else {
              if (err?.code === 8025 || err?.code === 8041) {
                const skipKey =
                  err?.code === 8025
                    ? "boQuaKiemTraNhomNgayGiuong"
                    : "boQuaKiemTraNgayRaXml";
                showConfirm(
                  {
                    title: t("common.thongBao"),
                    content: `${err.message}. ${t(
                      "quanLyNoiTru.tiepTucChinhSua"
                    )}`,
                    cancelText: t("common.huy"),
                    okText: t("common.dongY"),
                    showBtnOk: true,
                  },
                  () => {
                    updateThongTinRaVien({
                      ...payload,
                      [skipKey]: true,
                    }).then((s) => {
                      getNbNoiTruById(chiTietNguoiBenhNoiTru?.id);
                      getThongTinRaVien(chiTietNguoiBenhNoiTru?.id).then(
                        (res) => {
                          if (
                            res?.huongDieuTri === 40 ||
                            res?.huongDieuTri === 42
                          ) {
                            onShowModalChuyenVien({
                              dsCdRaVienId: res.dsCdChinhId,
                              dsCdKemTheoId: res.dsCdKemTheoId,
                              moTa: res.moTa,
                              vienChuyenDenId: res?.vienChuyenDenId,
                              huongDieuTri: res?.huongDieuTri,
                            });
                          }
                        }
                      );

                      callbackFunc();
                    });
                  }
                );
              }
            }
          })
          .finally(() => {
            hideLoading();
          });
      }
    }, [500]);

  const onChangeInput = (key) => (e, dataItems) => {
    let value = "";
    if (e?.target) {
      if (e.target.hasOwnProperty("checked")) value = e.target.checked;
      else value = e.target.value;
    } else value = e;

    refDataSave.current[key] = value;
    if (
      key === "thoiGianRaVien" ||
      key === "soNgayHenKham" ||
      key === "thoiGianHenKham"
    ) {
      handleChangeThoiGianHenKham(key, value);
      if (key === "thoiGianRaVien") {
        updateDataSave({
          tuThoiGianNghiNgoaiTru: null,
        });
      }
    }
    if (key == "truongKhoaId") {
      const selectNv = listNhanVien.find((item) => item.id === e);
      if (selectNv) {
        setState({
          truongKhoa: selectNv,
          [key]: value,
        });

        refDataSave.current.truongKhoa = selectNv;
      } else {
        setState({
          truongKhoa: null,
          [key]: null,
        });

        refDataSave.current.truongKhoa = null;
      }
    } else if (key == "dsCdChinhId" || key == "dsCdKemTheoId") {
      const keyMoTa = key == "dsCdChinhId" ? "dsMoTaChinh" : "dsMoTaKemTheo";
      const dsMoTa = value.map((id, index) => ({
        id: id,
        tenChanDoan: dataItems?.find((item2) => item2.id == id)?.label,
        moTa: (state[keyMoTa] || [])?.find((item) => item.id == id)?.moTa || "",
      }));
      const moTa = getMoTaChanDoan(
        key == "dsCdChinhId"
          ? [dsMoTa, state.dsMoTaKemTheo]
          : [state.dsMoTaChinh, dsMoTa]
      );
      if (dataTU_DONG_DIEN_CD_MO_TA_THEO_CAC_CD?.eval()) {
        refDataSave.current["moTa"] = moTa;
        setState({
          [key]: value,
          [keyMoTa]: dsMoTa,
          moTa: moTa,
        });
      } else {
        setState({
          [key]: value,
          [keyMoTa]: dsMoTa,
        });
      }
    } else {
      let _state = { [key]: value };
      if (key === "huongDieuTri") {
        if (kiemTraHuongDieuTriChuyenVien(e)) {
          _state = {
            ..._state,
            soNgayHenKham: null,
            dsPhongHenKhamId: null,
            thoiGianHenKham: null,
          };
        }
      }
      setState({ ..._state });
    }
  };

  const onChangeTextFiled = useCallback(
    (key) => (input) => {
      let value;
      if (input && typeof input === "object" && input.target) {
        value = input.target.value;
      } else {
        value = input;
      }
      state[key] = value;
      refDataSave.current[key] = value;
    },
    [state]
  );

  useImperativeHandle(ref, () => ({
    show: () => {
      setState({ show: true });
    },
  }));

  useEffect(() => {
    if (state.show) {
      getListPhongTongHop({
        page: 0,
        sort: "active,desc",
        size: 500,
        dsLoaiPhong: 30,
      });
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  const isValidMaBHXH = useMemo(() => {
    if (!state?.soBaoHiemXaHoi) {
      return true;
    }
    const regex = /^[0-9]{10}$/;
    return regex.test(state?.soBaoHiemXaHoi);
  }, [state?.soBaoHiemXaHoi]);

  const onGuiChungTu = async () => {
    try {
      showLoading();

      await dayPhieuRaVienById(chiTietNguoiBenhNoiTru?.id);

      getNbNoiTruById(chiTietNguoiBenhNoiTru?.id);
    } catch (error) {
    } finally {
      hideLoading();
    }
  };

  const onHuyChungNhan = async () => {
    try {
      showLoading();

      await huyPhieuRaVienById(chiTietNguoiBenhNoiTru?.id);

      getNbNoiTruById(chiTietNguoiBenhNoiTru?.id);
    } catch (error) {
    } finally {
      hideLoading();
    }
  };

  const updateDataSave = (object) => {
    Object.keys(object).forEach((key) => {
      refDataSave.current[key] = object[key];
    });
    setState(object);
  };

  const diffDate = (date1, date2) => {
    return moment(date1)
      .startOf("day")
      .diff(moment(date2).startOf("day"), "days");
  };
  const handleChangeThoiGianHenKham = (key, value) => {
    let thoiGianRaVien =
      key === "thoiGianRaVien" ? value : state.thoiGianRaVien;
    let soNgayHenKham = key === "soNgayHenKham" ? value : state.soNgayHenKham;
    let thoiGianHenKham =
      key === "thoiGianHenKham" ? value : state.thoiGianHenKham;

    updateDataSave({
      thoiGianRaVien,
      soNgayHenKham,
      thoiGianHenKham,
    });

    if (!thoiGianRaVien) {
      return;
    }

    if (key === "thoiGianRaVien") {
      if (soNgayHenKham) {
        updateDataSave({
          thoiGianHenKham: moment(thoiGianRaVien).add(soNgayHenKham, "days"),
        });
      } else if (thoiGianHenKham) {
        updateDataSave({
          soNgayHenKham: diffDate(thoiGianHenKham, thoiGianRaVien),
        });
      }
      return;
    }

    if (key === "soNgayHenKham") {
      if (value) {
        updateDataSave({
          thoiGianHenKham: moment(thoiGianRaVien).add(value, "days"),
        });
      }
      return;
    }

    if (key === "thoiGianHenKham" && value) {
      updateDataSave({
        soNgayHenKham: diffDate(value, thoiGianRaVien),
      });
    }
  };

  const onChangeMoTa = (type) => (id, value) => {
    const dsMoTa = state[type] || [];
    const item = dsMoTa.find((item) => item?.id == id);
    if (!item) {
      dsMoTa.push({ id, moTa: value });
    } else {
      item.moTa = value;
    }
    if (dataTU_DONG_DIEN_CD_MO_TA_THEO_CAC_CD.eval()) {
      const moTa = getMoTaChanDoan([state.dsMoTaChinh, state.dsMoTaKemTheo]);
      if (refDataSave.current) refDataSave.current["moTa"] = moTa;
      setState({ [type]: [...dsMoTa], moTa });
    } else {
      setState({ [type]: [...dsMoTa] });
    }
  };
  return (
    <ModalTemplate
      width={"85%"}
      ref={refModal}
      onCancel={handleClickBack}
      title={t("quanLyNoiTru.thongTinRaVien")}
      rightTitle={
        <>
          <span className="font-color">
            {firstLetterWordUpperCase(chiTietNguoiBenhNoiTru?.tenNb)}
          </span>
          {gioiTinh.ten && (
            <span className="normal-weight"> - {gioiTinh.ten} </span>
          )}

          {chiTietNguoiBenhNoiTru?.tuoi2 && (
            <span className="normal-weight">
              - {chiTietNguoiBenhNoiTru?.tuoi2}
            </span>
          )}
        </>
      }
      actionLeft={<Button.QuayLai onClick={handleClickBack} />}
      actionRight={
        <>
          {chiTietNguoiBenhNoiTru?.trangThaiDayCong != 40 && (
            <AuthWrapper
              accessRoles={[ROLES["GIAY_DAY_CONG"].NB_RA_VIEN_DAY_LE]}
            >
              <Button
                type={"primary"}
                minWidth={60}
                onClick={handleClickNext(onGuiChungTu)}
                rightIcon={<SVG.IcGuiCt />}
              >
                {t("giayDayCong.action.guiGiamDinh")}
              </Button>
            </AuthWrapper>
          )}

          {chiTietNguoiBenhNoiTru?.trangThaiDayCong == 40 && (
            <AuthWrapper accessRoles={[ROLES["GIAY_DAY_CONG"].NB_RA_VIEN_HUY]}>
              <Button
                type={"error"}
                minWidth={60}
                onClick={handleClickNext(onHuyChungNhan)}
                rightIcon={<SVG.IcCloseCircle />}
              >
                {t("giayDayCong.action.huyGiamDinh")}
              </Button>
            </AuthWrapper>
          )}

          <Button
            type={"primary"}
            minWidth={60}
            rightIcon={<SVG.IcSave />}
            onClick={handleClickNext()}
          >
            {t("common.luu")}
          </Button>
        </>
      }
    >
      <Main>
        <Row>
          <Col span={15}>
            <SelectGroup>
              <span>
                {t("quanLyNoiTru.chanDoanRaVienChinh")}
                <span className={"title-error"}>*</span>:
              </span>
              <div className="select-box-chan-doan">
                <SelectChanDoan
                  mode="multiple"
                  value={(state?.dsCdChinhId || []).map((item) => item + "")}
                  style={{
                    width: "100%",
                  }}
                  maxItem={1}
                  onChange={onChangeInput("dsCdChinhId")}
                  disabled={isRoleSuaThongTinHenKham}
                  tagRender={CustomTag(
                    onChangeMoTa("dsMoTaChinh"),
                    state.dsMoTaChinh
                  )}
                  allowClear
                />
              </div>
            </SelectGroup>
            {state?.validate && !state?.dsCdChinhId?.length && (
              <span className="title-error">
                {t("quanLyNoiTru.chuaNhapChanDoanRaVien")}
              </span>
            )}
            <SelectGroup>
              <span>{t("quanLyNoiTru.chanDoanRaVienKemTheo")}: </span>
              <div className="select-box-chan-doan">
                <SelectChanDoan
                  mode="multiple"
                  value={(state?.dsCdKemTheoId || []).map((item) => item + "")}
                  style={{
                    width: "100%",
                  }}
                  onChange={onChangeInput("dsCdKemTheoId")}
                  disabled={isRoleSuaThongTinHenKham}
                  allowClear
                  tagRender={CustomTag(
                    onChangeMoTa("dsMoTaKemTheo"),
                    state.dsMoTaKemTheo
                  )}
                />
              </div>
            </SelectGroup>

            <TextField
              label={t("quanLyNoiTru.chanDoanRaVienMoTaChiTiet")}
              className="input_custom"
              marginTop={5}
              maxLine={1}
              onChange={onChangeInput("moTa")}
              html={state?.moTa}
              disabled={
                isRoleSuaThongTinHenKham ||
                !dataCHO_PHEP_NHAP_MO_TA_CHAN_DOAN?.eval()
              }
            />

            <TextFieldStyled>
              <TextField
                label={
                  <div>
                    {t("quanLyNoiTru.phuongPhapDieuTri")}
                    <span className={"title-error"}>*</span>
                  </div>
                }
                classNameLabel="flex"
                className="input_custom"
                marginTop={5}
                maxLine={1}
                onChange={onChangeTextFiled("phuongPhapDieuTri")}
                html={state?.phuongPhapDieuTri}
                type="html"
                timeDelay={50}
                haveOtherProps={true}
                inputHtmlProps={{ acceptPasteTypes: ["text/plain"] }}
                onPaste={onChangeTextFiled("phuongPhapDieuTri")}
                disabled={isRoleSuaThongTinHenKham}
              />
            </TextFieldStyled>
            {state?.validate &&
              (!state?.phuongPhapDieuTri ||
                state?.phuongPhapDieuTri === "<br>") && (
                <span className="title-error">
                  {t("quanLyNoiTru.chuaNhapPhuongPhapDieuTri")}
                </span>
              )}

            <AutoCompleteStyled>
              <span className="label-title">
                {t("quanLyNoiTru.loiDanBacSi")}:
              </span>
              <AutoComplete
                className="autocomplete-custom"
                options={state?.loiDanBacSi ? [] : listAllLoiDanOptions}
                filterOption={(input, option) =>
                  option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
                value={state?.loiDanBacSi}
                onSelect={(value) => {
                  onChangeInput("loiDanBacSi")(value);
                }}
                allowClear={true}
                onClear={() => {
                  onChangeInput("loiDanBacSi")(null);
                }}
              >
                <TextField
                  className="input_custom"
                  marginTop={5}
                  maxLine={1}
                  onChange={onChangeTextFiled("loiDanBacSi")}
                  html={state?.loiDanBacSi}
                  disabled={isRoleSuaThongTinHenKham}
                  useChangeEvent={true}
                />
              </AutoComplete>
            </AutoCompleteStyled>
            <TextField
              label={t("quanLyNoiTru.tinhTrangNguoiBenh")}
              className="input_custom"
              marginTop={5}
              maxLine={1}
              onChange={onChangeInput("tinhTrang")}
              html={state?.tinhTrang}
              disabled={isRoleSuaThongTinHenKham}
            />

            <TextFieldStyled>
              <TextField
                label={t("quanLyNoiTru.quaTrinhBenhLyVaDienBienLamSang")}
                className="input_custom"
                marginTop={5}
                maxLine={1}
                onChange={onChangeTextFiled("quaTrinhBenhLy")}
                html={state?.quaTrinhBenhLy}
                type="html"
                timeDelay={50}
                haveOtherProps={true}
                disabled={isRoleSuaThongTinHenKham}
              />
            </TextFieldStyled>

            <TextFieldStyled>
              <TextField
                label={t("quanLyNoiTru.tomTatKetQuaCls")}
                className="input_custom"
                marginTop={5}
                maxLine={1}
                onChange={onChangeTextFiled("ketQuaCls")}
                html={state?.ketQuaCls}
                type="html"
                timeDelay={50}
                haveOtherProps={true}
                disabled={isRoleSuaThongTinHenKham}
              />
            </TextFieldStyled>
            <TextFieldStyled>
              <TextField
                label={t("quanLyNoiTru.huongDieuTriVaCacCheDoTiepTheo")}
                className="input_custom"
                marginTop={5}
                maxLine={1}
                onChange={onChangeTextFiled("cheDoTiepTheo")}
                html={state?.cheDoTiepTheo}
                type="html"
                timeDelay={50}
                haveOtherProps={true}
                disabled={isRoleSuaThongTinHenKham}
              />
            </TextFieldStyled>
            <div>
              <SelectGroup>
                <span className="title">{t("quanLyNoiTru.danhSachPTTT")}:</span>
                <div className="select-box-chan-doan">
                  <Select
                    style={{
                      width: "100%",
                    }}
                    data={listDsPttt}
                    value={state?.dsPtTtGiayRaVienId}
                    onChange={onChangeInput("dsPtTtGiayRaVienId")}
                    mode="multiple"
                  />
                </div>
              </SelectGroup>
            </div>
            {listKhoaTheoTaiKhoan
              ?.find((x) => x.id == khoaLamViec?.id)
              ?.dsTinhChatKhoa?.includes(DS_TINH_CHAT_KHOA.Y_HOC_CO_TRUYEN) && (
                <Col>
                  <SelectGroup>
                    <span>{t("quanLyNoiTru.chanDoanBenhYhct")}: </span>
                    <div className="select-box-chan-doan">
                      <SelectChanDoan
                        mode="multiple"
                        maxItem={1}
                        value={(state?.dsCdYhctChinhId || []).map(
                          (item) => item + ""
                        )}
                        onChange={onChangeInput("dsCdYhctChinhId")}
                        style={{
                          width: "100%",
                        }}
                        disabled={isReadonly}
                        isYhct={true}
                      />
                    </div>
                  </SelectGroup>
                </Col>
              )}
            {listKhoaTheoTaiKhoan
              ?.find((x) => x.id == khoaLamViec?.id)
              ?.dsTinhChatKhoa?.includes(DS_TINH_CHAT_KHOA.Y_HOC_CO_TRUYEN) && (
                <Col>
                  <SelectGroup>
                    <span>{t("quanLyNoiTru.chanDoanKemTheoYhct")}: </span>
                    <div className="select-box-chan-doan">
                      <SelectChanDoan
                        mode="multiple"
                        value={(state?.dsCdYhctKemTheoId || []).map(
                          (item) => item + ""
                        )}
                        onChange={onChangeInput("dsCdYhctKemTheoId")}
                        style={{
                          width: "100%",
                        }}
                        disabled={isReadonly}
                        isYhct={true}
                      />
                    </div>
                  </SelectGroup>
                </Col>
              )}
          </Col>
          <Col span={8} offset={1}>
            <SelectGroup>
              <span>
                {t("quanLyNoiTru.tinhTrangRaVien")}
                <span className={"title-error"}>*</span>:
              </span>
              <div className="select-box-chan-doan">
                <Select
                  data={listHuongDieuTriKham}
                  value={state?.huongDieuTri}
                  style={{
                    width: "100%",
                  }}
                  onChange={(e) => {
                    onChangeInput("huongDieuTri")(e);
                    setState({
                      ketQuaDieuTri: null,
                    });
                  }}
                  disabled={
                    (isReadonly &&
                      !checkRole([
                        ROLES["QUAN_LY_NOI_TRU"].CHINH_SUA_THONG_TIN_RA_VIEN,
                      ])) ||
                    isRoleSuaThongTinHenKham
                  }
                  setFallbackValue={setValueHuongDieuTri}
                />
              </div>
            </SelectGroup>
            {state?.validate && !state?.huongDieuTri && (
              <span className="title-error">
                {t("quanLyNoiTru.chuaChonTinhTrangRaVien")}
              </span>
            )}

            <SelectGroup>
              <span>
                {t("quanLyNoiTru.ketQuaDieuTri")}
                <span className={"title-error"}>*</span>:
              </span>
              <div className="select-box-chan-doan">
                <Select
                  data={listKetQuaDieuTriKham}
                  value={state?.ketQuaDieuTri}
                  style={{
                    width: "100%",
                  }}
                  onChange={onChangeInput("ketQuaDieuTri")}
                  disabled={
                    (isReadonly &&
                      !checkRole([
                        ROLES["QUAN_LY_NOI_TRU"].CHINH_SUA_THONG_TIN_RA_VIEN,
                      ])) ||
                    isRoleSuaThongTinHenKham
                  }
                  setFallbackValue={setValueKetQuaDieuTri}
                />
              </div>
            </SelectGroup>
            {state?.validate && !state?.ketQuaDieuTri && (
              <span className="title-error">
                {t("quanLyNoiTru.chuaChonKetQuaDieuTri")}
              </span>
            )}
            {kiemTraHuongDieuTriChuyenVien(state.huongDieuTri) && (
              <SelectGroup>
                <span>
                  {t("quanLyNoiTru.giayChuyenVien.vienChuyenDen")}
                  <span className={"title-error"}>*</span>:
                </span>
                <div
                  className="select-box-chan-doan"
                  style={{
                    overflow: "hidden",
                  }}
                >
                  <SelectBenhVien
                    value={state?.vienChuyenDenId}
                    style={{
                      width: "100%",
                    }}
                    onChange={onChangeInput("vienChuyenDenId")}
                    disabled={isReadonly || isRoleSuaThongTinHenKham}
                    showSearch
                    dropdownMatchSelectWidth={600}
                  />
                </div>
              </SelectGroup>
            )}
            {state?.validate &&
              !state?.vienChuyenDenId &&
              kiemTraHuongDieuTriChuyenVien(state.huongDieuTri) && (
                <span className="title-error">
                  {t("quanLyNoiTru.giayChuyenVien.vuiLongNhapVienChuyenDen")}
                </span>
              )}

            {(state?.huongDieuTri === 204 ||
              [5, 8]?.includes(state.ketQuaDieuTri)) && (
                <div className="date">
                  <span className="title">
                    {t("quanLyNoiTru.thoiGianTuVong")}
                    <span className={"title-error"}>* </span>:
                  </span>
                  <DateTimePicker
                    showTime={{ format: "HH:mm:ss" }}
                    value={state?.thoiGianTuVong}
                    onChange={onChangeInput("thoiGianTuVong")}
                    placeholder={t("common.chonThoiGian")}
                    disabled={isRoleSuaThongTinHenKham}
                  />
                </div>
              )}
            {state?.validate &&
              (state?.huongDieuTri === 204 ||
                [5, 8]?.includes(state.ketQuaDieuTri)) &&
              !state?.thoiGianTuVong && (
                <span className="title-error">
                  {t("quanLyNoiTru.chuaNhapThoiGianTuVong")}
                </span>
              )}
            {(state?.huongDieuTri === 204 ||
              [5, 8]?.includes(state.ketQuaDieuTri)) && (
                <SelectGroup>
                  <span>{t("quanLyNoiTru.diaDiemTuVong")}: </span>
                  <div className="select-box-chan-doan">
                    <Select
                      style={{
                        width: "100%",
                      }}
                      data={listDiaDiemTuVong}
                      value={state?.diaDiemTuVong}
                      onChange={onChangeInput("diaDiemTuVong")}
                      disabled={isRoleSuaThongTinHenKham}
                    />
                  </div>
                </SelectGroup>
              )}

            {(state?.huongDieuTri === 204 ||
              [5, 8]?.includes(state.ketQuaDieuTri)) && (
                <SelectGroup>
                  <span>
                    {t("quanLyNoiTru.lyDoTuVong")}
                    <span className={"title-error"}>* </span>:
                  </span>
                  <div className="select-box-chan-doan">
                    <Select
                      data={listLyDoTuVong}
                      style={{
                        width: "100%",
                      }}
                      onChange={onChangeInput("lyDoTuVong")}
                      value={state?.lyDoTuVong}
                      disabled={isRoleSuaThongTinHenKham}
                    />
                  </div>
                </SelectGroup>
              )}
            {state?.validate &&
              (state?.huongDieuTri === 204 ||
                [5, 8]?.includes(state.ketQuaDieuTri)) &&
              !state?.lyDoTuVong && (
                <span className="title-error">
                  {t("quanLyNoiTru.chuaChonLyDoTuVong")}
                </span>
              )}
            {(state?.huongDieuTri === 204 ||
              [5, 8]?.includes(state.ketQuaDieuTri)) && (
                <TextField
                  label={t("quanLyNoiTru.ghiChuTuVong")}
                  className="input_custom"
                  marginTop={5}
                  maxLine={1}
                  onChange={onChangeInput("ghiChuTuVong")}
                  html={state?.ghiChuTuVong}
                  disabled={isRoleSuaThongTinHenKham}
                />
              )}

            <div className="date">
              <span className="title">
                {t("common.thoiGianRaVien")}
                {!state?.thoiGianRaVien &&
                  lisTrangThaiNb.includes(
                    chiTietNguoiBenhNoiTru?.trangThai
                  ) && <span className={"title-error"}>*</span>}
                :
              </span>

              <DatePicker
                showTime={{ format: "HH:mm:ss" }}
                format="DD/MM/YYYY HH:mm:ss"
                value={state?.thoiGianRaVien}
                onChange={onChangeInput("thoiGianRaVien")}
                placeholder={t("common.chonThoiGian")}
                disabled={isRoleSuaThongTinHenKham}
                pointTime={
                  chiTietNguoiBenhNoiTru?.thoiGianVaoKhoaNhapVien ||
                  chiTietNguoiBenhNoiTru?.thoiGianVaoVien
                }
                compareTime={(a, b) => a < b}
                disabledDate={(date) =>
                  moment(date).isBefore(chiTietNguoiBenhNoiTru?.thoiGianVaoVien)
                }
              />
            </div>
            {state?.validate &&
              !state?.thoiGianRaVien &&
              lisTrangThaiNb.includes(chiTietNguoiBenhNoiTru?.trangThai) && (
                <span className="title-error">
                  {t("quanLyNoiTru.vuiLongNhapThoiGianRaVien")}!
                </span>
              )}

            <div className="input-so-ngay-hen-kham">
              <TextField
                label={t("quanLyNoiTru.henKhamLaiSau")}
                onChange={onChangeInput("soNgayHenKham")}
                html={state.soNgayHenKham}
                type="numberFormat"
                inputNumberFormatProps={{
                  decimalScale: 0,
                  thousandSeparator: false,
                  allowNegative: false,
                  allowLeadingZeros: false,
                  disabled: kiemTraHuongDieuTriChuyenVien(state.huongDieuTri),
                  isAllowed: ({ floatValue }) => {
                    return floatValue !== undefined
                      ? floatValue > 0 && floatValue < 10000
                      : true;
                  },
                }}
              />
              <span className="input-suffix">{t("common.ngay")}</span>
            </div>
            <div className="date">
              <span className="title">
                {t("quanLyNoiTru.thoiGianHenKham")}:
              </span>
              <DateTimePicker
                showTime={{ format: "HH:mm:ss" }}
                value={state?.thoiGianHenKham}
                onChange={onChangeInput("thoiGianHenKham")}
                placeholder={t("common.chonThoiGian")}
                disabled={kiemTraHuongDieuTriChuyenVien(state.huongDieuTri)}
                disabledDate={(date) =>
                  (date &&
                    state.thoiGianRaVien &&
                    moment(date).isSameOrBefore(
                      moment(state.thoiGianRaVien),
                      "date"
                    )) ||
                  moment(date).isSameOrBefore(
                    chiTietNguoiBenhNoiTru?.thoiGianVaoVien,
                    "date"
                  )
                }
              />
            </div>
            {state?.thoiGianHenKham &&
              state?.thoiGianRaVien &&
              !moment(state.thoiGianHenKham)
                .startOf("day")
                .isAfter(moment(state.thoiGianRaVien).startOf("day")) && (
                <span className="title-error">
                  {t("quanLyNoiTru.thoiGianHenKhamPhaiLonHonThoiGianRaVien")}
                </span>
              )}
            <div>
              <SelectGroup>
                <span className="title">
                  {t("quanLyNoiTru.phongHenKham")}
                  {chiTietNguoiBenhNoiTru?.trangThai ===
                    TRANG_THAI_NB.DA_RA_VIEN &&
                    state.thoiGianHenKham && (
                      <span className={"title-error"}>* </span>
                    )}
                  :
                </span>
                <div className="select-box-chan-doan">
                  <Select
                    style={{
                      width: "100%",
                    }}
                    data={dataPhongHenKham}
                    value={state?.dsPhongHenKhamId}
                    onChange={onChangeInput("dsPhongHenKhamId")}
                    mode="multiple"
                    hasAllOption={false}
                    disabled={kiemTraHuongDieuTriChuyenVien(state.huongDieuTri)}
                  />
                </div>
              </SelectGroup>
              {state?.validate &&
                !state?.dsPhongHenKhamId?.length &&
                chiTietNguoiBenhNoiTru?.trangThai ===
                TRANG_THAI_NB.DA_RA_VIEN && (
                  <span className="title-error">
                    {t("quanLyNoiTru.chuaChonPhongHenKham")}
                  </span>
                )}
            </div>
            {THONG_TIN_BAT_BUOC_THONG_TIN_RA_VIEN?.eval() && (
              <div>
                <SelectGroup>
                  <span>
                    {t("quanLyNoiTru.theBenh")}
                    <span className="title-error">* </span>:
                  </span>
                  <div className="select-box-chan-doan">
                    <Select
                      style={{
                        width: "100%",
                      }}
                      data={listTheBenhLao}
                      value={state?.theBenhLao}
                      onChange={onChangeInput("theBenhLao")}
                    />
                  </div>
                </SelectGroup>
                {state?.validate && !state?.theBenhLao && (
                  <span className="title-error">
                    {t("quanLyNoiTru.chuaChonThongTinTheBenh")}
                  </span>
                )}
              </div>
            )}
            {THONG_TIN_BAT_BUOC_THONG_TIN_RA_VIEN?.eval() && (
              <div>
                <SelectGroup>
                  <span>
                    {t("quanLyNoiTru.viemLoetTiDe")}
                    <span className="title-error">* </span>:
                  </span>
                  <div className="select-box-chan-doan">
                    <Select
                      style={{
                        width: "100%",
                      }}
                      data={CO_KHONG}
                      value={state?.viemLoetTiDe}
                      onChange={onChangeInput("viemLoetTiDe")}
                    />
                  </div>
                </SelectGroup>
                {state?.validate &&
                  !(typeof state?.viemLoetTiDe === "boolean") && (
                    <span className="title-error">
                      {t("quanLyNoiTru.chuaChonThongTinViemLoetTiDe")}
                    </span>
                  )}
              </div>
            )}
            {THONG_TIN_BAT_BUOC_THONG_TIN_RA_VIEN?.eval() && (
              <div>
                <SelectGroup>
                  <span>
                    {t("quanLyNoiTru.viemPhoiDoUDong")}
                    <span className="title-error">* </span>:
                  </span>
                  <div className="select-box-chan-doan">
                    <Select
                      style={{
                        width: "100%",
                      }}
                      data={CO_KHONG}
                      value={state?.viemPhoiDoUDong}
                      onChange={onChangeInput("viemPhoiDoUDong")}
                    />
                  </div>
                </SelectGroup>
                {state?.validate &&
                  !(typeof state?.viemPhoiDoUDong === "boolean") && (
                    <span className="title-error">
                      {t("quanLyNoiTru.chuaNhapThongTinViemPhoiUDong")}
                    </span>
                  )}
              </div>
            )}
            {THONG_TIN_BAT_BUOC_THONG_TIN_RA_VIEN?.eval() && (
              <div>
                <SelectGroup>
                  <span>
                    {t("quanLyNoiTru.nguoiBenhHIV")}
                    <span className="title-error">* </span>:
                  </span>
                  <div className="select-box-chan-doan">
                    <Select
                      style={{
                        width: "100%",
                      }}
                      data={CO_KHONG}
                      value={state?.hiv}
                      onChange={onChangeInput("hiv")}
                    />
                  </div>
                </SelectGroup>
                {state?.validate && !(typeof state?.hiv === "boolean") && (
                  <span className="title-error">
                    {t("quanLyNoiTru.chuaChonThongTinNguoiBenhHiv")}
                  </span>
                )}
              </div>
            )}
          </Col>
        </Row>
        {/* người bệnh bảo hiểm + trạng thái ra viện = ra viện, trón viện, xin ra viện */}
        {/* {[15, 50, 60].includes(state?.huongDieuTri) && ( */}
        {/* Luôn hiển thị không bắt điều kiện nào */}
        <div className="more-info">
          <Collapse defaultActiveKey={["1", "2"]}>
            <Panel header={t("quanLyNoiTru.thongTinKhac")} key="1">
              <Row>
                <Col span={15}>
                  <Row>
                    <Col span={24}>
                      <TextField
                        label={
                          <span>
                            {t("tiepDon.maSoBHXH2")}
                            {/* <span className={"title-error"}>*</span> */}
                          </span>
                        }
                        className="input_custom"
                        marginTop={5}
                        maxLine={1}
                        // disabled={thongTinCoBan.doiTuong === DOI_TUONG.BAO_HIEM}
                        html={state?.soBaoHiemXaHoi}
                        onChange={onChangeInput("soBaoHiemXaHoi")}
                        disabled={isRoleSuaThongTinHenKham}
                      />
                      {!isValidMaBHXH && (
                        <span className="title-error">
                          {t("quanLyNoiTru.saiDinhDangMaSoBHXH")}
                        </span>
                      )}
                    </Col>

                    <Col span={11}>
                      <SelectGroup>
                        <span>{t("tenTruong.dinhChiThaiNghen")}: </span>
                        <div className="select-box-chan-doan">
                          <Select
                            style={{
                              width: "100%",
                            }}
                            data={listDinhChiThaiNghen}
                            value={state?.dinhChiThaiNghen}
                            onChange={onChangeInput("dinhChiThaiNghen")}
                            disabled={isRoleSuaThongTinHenKham}
                          />
                        </div>
                      </SelectGroup>
                    </Col>

                    <Col span={12} offset={1}>
                      <InputTuoiThai
                        t={t}
                        initialValue={state.orgTuoiThai}
                        value={state.tuoiThai}
                        onChange={onChangeInput("tuoiThai")}
                        disabled={isRoleSuaThongTinHenKham}
                      />
                      {state?.validate &&
                        !state?.tuoiThai &&
                        state?.dinhChiThaiNghen == 1 && (
                          <span className="title-error">
                            {t("giayDayCong.vuiLongNhapTuoiThai")}
                          </span>
                        )}
                    </Col>

                    <Col span={11}>
                      <div className="date">
                        <span className="title">
                          {t("quanLyNoiTru.nghiNgoaiTruTuNgay")}
                          {state?.denThoiGianNghiNgoaiTru && (
                            <span className={"title-error"}>*</span>
                          )}
                          {": "}
                        </span>
                        <DateTimePicker
                          showTime={{ format: "HH:mm:ss" }}
                          value={state?.tuThoiGianNghiNgoaiTru}
                          onChange={onChangeInput("tuThoiGianNghiNgoaiTru")}
                          placeholder={t("common.chonThoiGian")}
                          afterOpenChange={(open) => {
                            setState({ openTuThoiGianNghiNgoaiTru: open });
                            if (open && !state.thoiGianRaVien) {
                              message.error(
                                `${t(
                                  "quanLyNoiTru.vuiLongNhapThoiGianRaVien"
                                )}!`
                              );
                            }
                          }}
                          disabledDate={(current) => {
                            if (!state.thoiGianRaVien) return true;
                            else if (state.thoiGianRaVien) {
                              return (
                                current &&
                                (current.isBefore(
                                  state?.thoiGianRaVien,
                                  "day"
                                ) ||
                                  current.isAfter(
                                    moment(state?.thoiGianRaVien).add(1, "day"),
                                    "day"
                                  ))
                              );
                            }
                          }}
                          defaultPickerValue={
                            state.thoiGianRaVien && moment(state.thoiGianRaVien)
                          }
                          disabled={isRoleSuaThongTinHenKham}
                        />
                      </div>
                      {state.openTuThoiGianNghiNgoaiTru &&
                        !state.thoiGianRaVien && (
                          <span className="title-error">
                            {t("quanLyNoiTru.vuiLongNhapThoiGianRaVien")}!
                          </span>
                        )}
                      {state?.tuThoiGianNghiNgoaiTru &&
                        state?.thoiGianRaVien &&
                        state?.tuThoiGianNghiNgoaiTru instanceof moment &&
                        state?.thoiGianRaVien instanceof moment &&
                        state?.tuThoiGianNghiNgoaiTru <=
                        state?.thoiGianRaVien && (
                          <span className="title-error">
                            {t("quanLyNoiTru.khongPhuHopVoiNgayRaVien")}
                          </span>
                        )}
                      {state?.validate &&
                        state?.denThoiGianNghiNgoaiTru &&
                        !state?.tuThoiGianNghiNgoaiTru && (
                          <span className="title-error">
                            {t("quanLyNoiTru.vuiLongNhapNghiNgoaiTruTuNgay")}
                          </span>
                        )}
                    </Col>

                    <Col span={12} offset={1}>
                      <div className="date">
                        <span className="title">
                          {t("quanLyNoiTru.nghiNgoaiTruDenNgay")}
                          {state?.tuThoiGianNghiNgoaiTru && (
                            <span className={"title-error"}>*</span>
                          )}
                          {": "}
                        </span>
                        <DateTimePicker
                          showTime={{ format: "HH:mm:ss" }}
                          value={state?.denThoiGianNghiNgoaiTru}
                          onChange={onChangeInput("denThoiGianNghiNgoaiTru")}
                          placeholder={t("common.chonThoiGian")}
                          disabled={isRoleSuaThongTinHenKham}
                        />
                      </div>
                      {state?.validate &&
                        state?.tuThoiGianNghiNgoaiTru &&
                        !state?.denThoiGianNghiNgoaiTru && (
                          <span className="title-error">
                            {t("quanLyNoiTru.vuiLongNhapNghiNgoaiTruDenNgay")}
                          </span>
                        )}
                      {state?.validate &&
                        isMoment(state.tuThoiGianNghiNgoaiTru) &&
                        isMoment(state.denThoiGianNghiNgoaiTru) &&
                        moment(state.tuThoiGianNghiNgoaiTru).valueOf() >=
                        moment(state.denThoiGianNghiNgoaiTru).valueOf() && (
                          <span className="title-error">
                            {t(
                              "quanLyNoiTru.nghiNgoaiTruTuNgayPhaiNhoHonDenNgay"
                            )}
                          </span>
                        )}
                    </Col>

                    <Col span={11}>
                      <div className="date">
                        <span className="title">
                          {t("giayDayCong.ngayChungTu")}
                          {/* <span className={"title-error"}>*</span> */}:
                        </span>

                        <DateTimePicker
                          showTime={{ format: "HH:mm:ss" }}
                          value={state?.thoiGianChungTu}
                          onChange={onChangeInput("thoiGianChungTu")}
                          placeholder={t("common.chonThoiGian")}
                          disabled={isRoleSuaThongTinHenKham}
                        />
                      </div>
                      {/* {state?.validate && !state?.thoiGianChungTu && (
                            <span className="title-error">
                              {"Bắt buộc chọn ngày chứng từ"}
                            </span>
                          )} */}
                    </Col>

                    <Col span={12} offset={1}>
                      <SelectGroup>
                        <span>
                          {t("tenTruong.thuTruongDonVi")}
                          {isBatBuocTruongKhoa && (
                            <>
                              <span className={"title-error"}>*</span>:{" "}
                            </>
                          )}
                        </span>
                        <div className="select-box-chan-doan">
                          <Select
                            style={{ width: "100%" }}
                            data={listAllNhanVienQuyenKyMemo}
                            value={state?.thuTruongId}
                            onChange={onChangeInput("thuTruongId")}
                            disabled={isRoleSuaThongTinHenKham}
                            getLabel={selectMaTen}
                          />
                        </div>
                      </SelectGroup>
                      {isBatBuocTruongKhoa &&
                        state?.validate &&
                        !state?.thuTruongId && (
                          <span className="title-error">
                            {t("giayDayCong.batBuocChonThuTruongDonVi")}
                          </span>
                        )}
                    </Col>
                    <Col span={11}>
                      <TextField
                        label={t("quanLyNoiTru.nguyenNhanDinhChiThai")}
                        className="input_custom"
                        marginTop={5}
                        maxLine={1}
                        onChange={onChangeInput("nguyenNhanDinhChiThai")}
                        html={state?.nguyenNhanDinhChiThai}
                        disabled={isRoleSuaThongTinHenKham}
                      />
                      {state?.validate &&
                        !state?.nguyenNhanDinhChiThai &&
                        state?.dinhChiThaiNghen == 1 && (
                          <span className="title-error">
                            {t("quanLyNoiTru.vuiLongNhapNguyenNhanDinhChiThai")}
                          </span>
                        )}
                    </Col>
                    <Col span={12} offset={1}>
                      <div className="date">
                        <span className="title">
                          {t("quanLyNoiTru.thoiGianDinhChiThai")}:
                        </span>
                        <DateTimePicker
                          showTime={{ format: "HH:mm:ss" }}
                          value={state?.thoiGianDinhChiThai}
                          onChange={onChangeInput("thoiGianDinhChiThai")}
                          placeholder={t("common.chonThoiGian")}
                          disabled={isRoleSuaThongTinHenKham}
                        />
                      </div>
                      {state?.validate &&
                        !state?.thoiGianDinhChiThai &&
                        state?.dinhChiThaiNghen == 1 && (
                          <span className="title-error">
                            {t("quanLyNoiTru.vuiLongNhapThoiGianDinhChiThai")}
                          </span>
                        )}
                    </Col>
                  </Row>
                </Col>
                <Col span={8} offset={1}>
                  <TextField
                    label={t("tiepDon.maThe")}
                    className="input_custom"
                    marginTop={5}
                    maxLine={1}
                    disabled={
                      thongTinCoBan.doiTuong === DOI_TUONG.BAO_HIEM ||
                      isRoleSuaThongTinHenKham
                    }
                    html={chiTietNguoiBenhNoiTru?.maTheBhyt}
                    onChange={onChangeInput("maTheBhyt")}
                  />

                  <SelectGroup>
                    <span>{t("tenTruong.treEmKhongThe")}: </span>
                    <div className="select-box-chan-doan">
                      <Select
                        style={{
                          width: "100%",
                        }}
                        data={listTreEmKhongThe}
                        value={state?.treEmKhongThe}
                        onChange={onChangeInput("treEmKhongThe")}
                        disabled={isRoleSuaThongTinHenKham}
                      />
                    </div>
                  </SelectGroup>

                  <SelectGroup>
                    <span>
                      {t("giayDayCong.tenTruongKhoa")}
                      {isBatBuocTruongKhoa && (
                        <>
                          <span className={"title-error"}>*</span>:{" "}
                        </>
                      )}
                    </span>
                    <div className="select-box-chan-doan">
                      <Select
                        style={{
                          width: "100%",
                        }}
                        data={listNhanVienOptions}
                        value={state?.truongKhoaId}
                        onChange={onChangeInput("truongKhoaId")}
                        disabled={isRoleSuaThongTinHenKham}
                      />
                    </div>
                  </SelectGroup>
                  {isBatBuocTruongKhoa &&
                    state?.validate &&
                    !state?.truongKhoaId && (
                      <span className="title-error">
                        {t("giayDayCong.batBuocChonTruongKhoa")}
                      </span>
                    )}

                  <TextField
                    label={
                      <span>
                        {t("giayDayCong.maCchnTruongKhoa")}
                        {/* <span className={"title-error"}>*</span> */}
                      </span>
                    }
                    className="input_custom"
                    marginTop={5}
                    maxLine={1}
                    disabled={true}
                    html={state.truongKhoa?.chungChi}
                  />
                  {/* {state?.validate && !state?.truongKhoa?.chungChi && (
                        <span className="title-error">
                          {"Mã CCHN trưởng khoa là bắt buộc"}
                        </span>
                      )} */}
                </Col>
              </Row>
            </Panel>
            <Panel
              header={t("quanLyNoiTru.thongTinChaMeApDungChoNbDuoi16Tuoi")}
              key="2"
            >
              <Row>
                <Col span={7}>
                  <TextField
                    label={t("giayDayCong.tenCha")}
                    className="input_custom"
                    marginTop={5}
                    maxLine={1}
                    onChange={onChangeInput("tenCha")}
                    html={state?.tenCha}
                    disabled={true}
                  />
                  <TextField
                    label={t("giayDayCong.maBHXHCuaCha")}
                    className="input_custom"
                    marginTop={5}
                    maxLine={1}
                    onChange={onChangeInput("soBaoHiemXaHoiCha")}
                    html={state?.soBaoHiemXaHoiCha}
                    disabled={true}
                  />
                  {/* {state?.validate &&
                    state?.soBaoHiemXaHoiCha &&
                    state?.soBaoHiemXaHoiCha.length !== 10 && (
                      <span className="title-error">
                        {t("quanLyNoiTru.truongMaBhxhSaiDinhDang")}
                      </span>
                    )} */}
                </Col>
                <Col span={7} offset={1}>
                  <TextField
                    label={t("giayDayCong.tenMe")}
                    className="input_custom"
                    marginTop={5}
                    maxLine={1}
                    onChange={onChangeInput("tenMe")}
                    html={state?.tenMe}
                    disabled={true}
                  />
                  <TextField
                    label={t("giayDayCong.maBHXHCuaMe")}
                    className="input_custom"
                    marginTop={5}
                    maxLine={1}
                    onChange={onChangeInput("soBaoHiemXaHoiMe")}
                    html={state?.soBaoHiemXaHoiMe}
                    disabled={true}
                  />
                  {/* {state?.validate &&
                    state?.soBaoHiemXaHoiMe &&
                    state?.soBaoHiemXaHoiMe.length !== 10 && (
                      <span className="title-error">
                        {t("quanLyNoiTru.truongMaBhxhSaiDinhDang")}
                      </span>
                    )} */}
                </Col>
              </Row>
              {state?.validate &&
                !state?.tenCha &&
                !state?.tenMe &&
                state?.treEmKhongThe == 1 && (
                  <span className="title-error">
                    {t("quanLyNoiTru.batBuocNhapTenChaHoacTenMe")}
                  </span>
                )}
            </Panel>
          </Collapse>
        </div>
        {/* )} */}
      </Main>
    </ModalTemplate>
  );
});

export default ModalThongTinRaVien;
