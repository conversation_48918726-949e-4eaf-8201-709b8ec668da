import React, {
  useEffect,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle,
  useState,
} from "react";
import { Main } from "./styled";
import { firstLetterWordUpperCase } from "utils";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import FormChuyenVien from "pages/khamBenh/KetLuan/FormChuyenVien";
import fileUtils from "utils/file-utils";
import { printJS } from "data-access/print-provider";
import { Button, ModalTemplate, ModalSignPrint } from "components";
import { useEnum, useLoading } from "hooks";
import { ENUM, LOAI_BIEU_MAU } from "constants/index";
import { SVG } from "assets";
import { checkIsPhieuKySo } from "utils/phieu-utils";
import { useHuongDieuTri } from "pages/khamBenh/hooks/useHuongDieuTri";

const ModalChuyenVien = (
  { infoNb, thongTinChiTiet, isDisableVienChuyenDen, refreshList = () => {} },
  ref
) => {
  const [state, _setState] = useState({
    isPressOk: false,
    huongDieuTri: null,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const { showLoading, hideLoading } = useLoading();

  const refModal = useRef(null);
  const refData = useRef({});
  const refFormChuyenVien = useRef(null);
  const refModalSignPrint = useRef(null);
  const refCallFirst = useRef(null);
  const refCallBack = useRef(null);
  const {
    benhVien: { getListAllBenhVien },
    quanLyNoiTru: { updateGiayChuyenVien },
    nbChuyenVien: { inPhieuChuyenVienById, taoMoiGiayChuyenTuyen },
    danhSachNguoiBenhNoiTru: { getNbNoiTruById },
    nbDotDieuTri: { getThongTinRaVien },
    phieuIn: { showFileEditor, getPhieuInTheoMa },
  } = useDispatch();
  const [listGioiTinh] = useEnum(ENUM.GIOI_TINH);
  const { listHuongDieuTriKham } = useHuongDieuTri({
    callApi: state.show,
  });
  const { t } = useTranslation();

  useEffect(() => {
    if (state.show) {
      refData.current = thongTinChiTiet;
      setState({ huongDieuTri: thongTinChiTiet?.huongDieuTri });
    }
  }, [thongTinChiTiet, state.show]);

  const gioiTinh =
    (listGioiTinh || []).find((item) => item.id === infoNb?.gioiTinh) || {};
  const inPhieuChuyenVien = async (idPhieu) => {
    const res = await getPhieuInTheoMa({
      nbDotDieuTriId: infoNb.id,
      maManHinh: "006",
      maViTri: "00601",
      maPhieu: "P054",
    });

    if (res.loaiBieuMau === LOAI_BIEU_MAU.BIEU_MAU_CHINH_SUA) {
      let mhParams = {};
      //kiểm tra phiếu ký số
      if (checkIsPhieuKySo(res)) {
        //nếu là ký số thì lấy các thông tin về lịch sử ký + tên chân ký + báo cáo id
        mhParams = {
          nbDotDieuTriId: infoNb.id,
          maManHinh: "006",
          maViTri: "00601",
          kySo: true,
          maPhieuKy: res.ma,
        };
      }
      showFileEditor({
        phieu: res,
        nbDotDieuTriId: infoNb.id,
        ma: res.ma,
        mhParams,
      });
    } else {
      if (checkIsPhieuKySo(res)) {
        refModalSignPrint.current &&
          refModalSignPrint.current.showToSign({
            phieuKy: res,
            payload: {
              maManHinh: "006",
              maViTri: "00601",
              nbDotDieuTriId: infoNb.id,
            },
          });
      } else {
        inPhieuChuyenVienById(idPhieu).then((s) => {
          fileUtils
            .getFromUrl({ url: fileUtils.absoluteFileUrl(s.file.pdf) })
            .then((s) => {
              const blob = new Blob([new Uint8Array(s)], {
                type: "application/pdf",
              });
              const blobUrl = window.URL.createObjectURL(blob);
              printJS({
                printable: blobUrl,
                type: "pdf",
              });
            });
        });
      }
    }
  };

  const onPrint = () => {
    inPhieuChuyenVien(thongTinChiTiet?.nbChuyenVien?.id);
  };
  const handleSetData = (arrKey) => (e) => {
    const value = e?.currentTarget ? e.currentTarget.innerHTML : e;
    const [key1, key2] = arrKey;
    if (key2) {
      refData.current = {
        ...refData.current,
        [key1]: refData.current[key1]
          ? {
              ...refData.current[key1],
              [key2]: value,
            }
          : { [key2]: value },
      };
    } else {
      refData.current = {
        ...refData.current,
        [key1]: value,
      };
    }
  };
  useImperativeHandle(ref, () => ({
    show: (data = {}, callFirst = null, callBack = null) => {
      setState({ show: true });

      refCallFirst.current = callFirst;
      refCallBack.current = callBack;
    },
  }));
  useEffect(() => {
    if (state?.show) {
      refModal.current && refModal.current.show();
      setState({ isPressOk: false });
      getListAllBenhVien({ active: true, page: "", size: "" });
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state?.show]);

  const onOk = (isOk) => async () => {
    if (isOk) {
      let data = {};
      Object.values(refData.current).forEach((item) => {
        data = {
          ...data,
          ...item,
        };
      });

      const {
        active,
        createdAt,
        createdBy,
        updatedBy,
        updatedAt,
        dieuTriTai1,
        dieuTriTai2,
        vienChuyenDen,
        ...payload
      } = data;

      function onReloadNb() {
        getNbNoiTruById(payload.nbDotDieuTriId);
        // update lại thông tin viện chuyển đến
        getThongTinRaVien(payload.nbDotDieuTriId);
        refreshList && refreshList();
        // setTimeout(() => {
        //   onOk(false)();
        // }, 50);
        if (refCallBack.current) {
          refCallBack.current();
        }
      }
      if (!payload.lyDoChuyenTuyen) {
        payload.lyDoChuyenTuyen = 1;
      }
      if (payload.lyDoChuyenTuyen === 1 && !payload.duDieuKienChuyenTuyen) {
        setState({ isPressOk: true });
        return;
      }

      if (refCallFirst.current) {
        const checkValue = await refCallFirst.current({
          huongDieuTri: state.huongDieuTri,
        });
        if (!checkValue) {
          onOk(false)();
          return;
        }
      }

      if (payload.id) {
        showLoading();
        updateGiayChuyenVien(payload)
          .then((s) => {
            onReloadNb();
            setState({ show: false });
          })
          .finally(() => {
            hideLoading();
          });
      } else {
        showLoading();
        taoMoiGiayChuyenTuyen({ ...payload, loai: payload.loai || 10 })
          .then((res) => {
            inPhieuChuyenVien(res?.data?.id);
            onReloadNb();
            setState({ show: false });
          })
          .finally(() => {
            hideLoading();
          });
      }
    } else {
      setState({ show: false, huongDieuTri: null });
    }
  };

  return (
    <ModalTemplate
      width={1140}
      ref={refModal}
      closable={true}
      title={t("quanLyNoiTru.phieuChuyenCoSoKhamBenhChuaBenh").toUpperCase()}
      onCancel={onOk(false)}
      destroyOnClose={true}
      rightTitle={
        <span className="header-right" style={{ marginRight: "20px" }}>
          <span className="font-color">
            {firstLetterWordUpperCase(infoNb?.tenNb)}
          </span>
          {gioiTinh.ten && (
            <span className="normal-weight"> - {gioiTinh.ten} </span>
          )}

          {infoNb?.tuoi && (
            <span className="normal-weight">
              - {infoNb?.tuoi} {t("common.tuoi")}
            </span>
          )}
        </span>
      }
      actionLeft={<Button.QuayLai onClick={onOk(false)} />}
      actionRight={
        <>
          <Button onClick={onPrint} rightIcon={<SVG.IcPrint />}>
            {t("common.in")}
          </Button>
          <Button
            type="primary"
            onClick={onOk(true)}
            rightIcon={<SVG.IcSave />}
          >
            {t("common.luu")}
          </Button>
        </>
      }
    >
      <Main>
        <FormChuyenVien
          ref={refFormChuyenVien}
          handleSetData={handleSetData}
          infoNb={infoNb}
          thongTinChiTiet={thongTinChiTiet}
          isDisableVienChuyenDen={isDisableVienChuyenDen}
          isPressOk={state.isPressOk}
          isInNoiTru={true}
          listHuongDieuTriKham={listHuongDieuTriKham}
          huongDieuTri={state.huongDieuTri}
          onChangeHuongDieuTri={(value) => setState({ huongDieuTri: value })}
        />

        <ModalSignPrint ref={refModalSignPrint} />
      </Main>
    </ModalTemplate>
  );
};

export default memo(forwardRef(ModalChuyenVien));
