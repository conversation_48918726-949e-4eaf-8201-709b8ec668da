import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Table<PERSON>rap<PERSON>, <PERSON><PERSON><PERSON> } from "components";
import moment from "moment";
import { SVG } from "assets";
import {
  DOI_TUONG_KCB,
  ROLES,
  TRANG_THAI_DUYET_BH,
  TRANG_THAI_NB,
  TRANG_THAI_THE_BAO_HIEM,
} from "constants/index";
import { Tag } from "antd";
import { checkRole } from "lib-utils/role-utils";
import { isNil } from "lodash";
import { calculateAgeChildren, isArray } from "utils";
import { InfoMeCon } from "./styled";

export const columns = ({
  t,
  onClickSort,
  onSettings,
  dataSortColumn,
  listTrangThaiNb,
  listTrangThaiTheBhyt,
  listDoiTuong,
  listHuongDieuTri,
  listKetQuaDieuTri,
  listAllPhanLoaiPHCN,
  listTrangThaiThanhToan,
  listTrangThaiTuVanThuoc,
  listPhanLoaiNBCapCuu,
  list<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>BH,
  listGioiTinh,
  isKhoaNbNgatDieuTriNoiTru = false,
  isGopPhieuThuDichVuNhaThuoc = false,
  showGioSinh = false,
  onViewDetail = () => () => {},
  onShowLogModal = () => () => {},
  onTiepDonHenDieuTri = () => () => {},
  onShowLichSuKCB = () => () => {},
  history,
  dataAN_CANH_BAO_QUA_HAN_SLDD_3NGAY,
  dataHIEN_THI_CAC_FIELD_CANH_BAO_NOI_TRU,
}) => {
  const hienThiCacFieldCanhBaoNoiTru =
    !!dataHIEN_THI_CAC_FIELD_CANH_BAO_NOI_TRU?.eval();

  const renderIconOpenHSBA = (nbDotDieuTriId) => {
    if (!nbDotDieuTriId) return null;

    return (
      <SVG.IcShowFull
        style={{ position: "absolute", top: 5, right: 5 }}
        onClick={(event) => {
          event.preventDefault();
          event.stopPropagation();

          history.push(
            `/quan-ly-noi-tru/chi-tiet-nguoi-benh-noi-tru/${nbDotDieuTriId}`
          );
        }}
      />
    );
  };

  const getNguyCoTeNga = (danhGia) => {
    const DANH_GIA = {
      1: t("quanLyNoiTru.khongCoNguyCoNga"),
      2: t("quanLyNoiTru.nguyCoThap"),
      3: t("quanLyNoiTru.nguyCoTrungBinh"),
      4: t("quanLyNoiTru.nguyCoCao"),
    };

    const COLOR = {
      1: "#0747a6",
      2: "#172b4d",
      3: "#ff8b00",
      4: "#de350b",
    };

    const level = danhGia[0];
    return {
      text: DANH_GIA[level] || "",
      color: COLOR[level] || "blue",
    };
  };

  const renderDiUng = (record) => {
    const diUng = record?.diUng;
    if (isNil(diUng)) return "";

    return diUng ? t("common.co") : t("common.khong");
  };

  const renderNguyCoTeNga = (record) => {
    const khungDanhGia = record?.khungDanhGia;
    if (!isArray(khungDanhGia, 1)) return null;

    const latestAssessment = khungDanhGia[0];

    let danhGia = null;
    let latestTime = null;

    const periods = [
      "sauKhiBiTeNga",
      "thayDoiTinhTrangBenhLy",
      "sauThuThuat",
      "sauPhauThuat",
      "vaoKhoa",
    ];

    for (const period of periods) {
      const periodData = latestAssessment[period];
      if (periodData?.danhGia && periodData?.thoiGianDanhGia) {
        const time = new Date(periodData.thoiGianDanhGia);
        if (!latestTime || time > latestTime) {
          latestTime = time;
          danhGia = periodData.danhGia;
        }
      }
    }

    if (!danhGia) return null;

    const { text, color } = getNguyCoTeNga(danhGia);
    return { text, color };
  };

  return [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: "50px",
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("cdha.khoaDangDieuTri")}
          sort_key="khoaNbId"
          dataSort={dataSortColumn["khoaNbId"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "300px",
      dataIndex: "khoaNbId",
      key: "khoaNbId",
      i18Name: "cdha.khoaDangDieuTri",
      show: true,
      render: (field, item, index) => {
        return (
          <EllipsisText content={`${item?.maKhoaNb} - ${item?.tenKhoaNb}`} />
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.khoaChuyenDen")}
          sort_key="tenDenKhoa"
          dataSort={dataSortColumn["tenDenKhoa"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "300px",
      dataIndex: "tenDenKhoa",
      key: "tenDenKhoa",
      i18Name: "quanLyNoiTru.khoaChuyenDen",
      show: true,
      render: (field) => {
        return field && <EllipsisText content={field} />;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.hoTenNb")}
          sort_key="tenNb"
          dataSort={dataSortColumn["tenNb"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "300px",
      dataIndex: "tenNb",
      key: "tenNb",
      align: "left",
      i18Name: "thuNgan.hoTenNb",
      show: true,
      className: "tenNb",
      render: (item, data) => {
        return (
          <div>
            <div style={{ display: "flex", width: "100%" }}>
              <label style={{ flex: 1 }}>{item}</label>
              {!data.tonTaiDvKyThuatTdt && (
                <Tooltip title={t("quanLyNoiTru.chuaCoChiDinhDVKT")}>
                  <div style={{ marginLeft: "auto" }}>
                    <SVG.IcDVKT color={"#FF0000;"} />
                  </div>
                </Tooltip>
              )}
              {data.phieuSoKet && (
                <Tooltip
                  title={t("quanLyNoiTru.chuaCoPhieuSoKet15NgayDieuTri")}
                >
                  <div style={{ marginLeft: "auto" }}>
                    <SVG.IcPhieuSoKet />
                  </div>
                </Tooltip>
              )}

              {data.trangThaiSangLocDd == 25 &&
                !dataAN_CANH_BAO_QUA_HAN_SLDD_3NGAY?.eval() && (
                  <Tooltip
                    title={t("quanLyNoiTru.canhBaoQuaHanSangLocDinhDuongNgay", {
                      soNgay: 3,
                    })}
                  >
                    <div style={{ marginLeft: "auto" }}>
                      <SVG.IcQuaHan3Ngay />
                    </div>
                  </Tooltip>
                )}

              {data.trangThaiSangLocDd == 20 && (
                <Tooltip
                  title={t("quanLyNoiTru.canhBaoQuaHanSangLocDinhDuongNgay", {
                    soNgay: 7,
                  })}
                >
                  <div style={{ marginLeft: "auto" }}>
                    <SVG.IcQuaHan7Ngay />
                  </div>
                </Tooltip>
              )}
              {data.trungTenNb && hienThiCacFieldCanhBaoNoiTru && (
                <Tooltip title={t("quanLyNoiTru.coNguoiBenhTrungTen")}>
                  <SVG.IcNbTrungTen />
                </Tooltip>
              )}
            </div>

            <InfoMeCon>
              {data.tenNbMe && (
                <div className="info-item">
                  <label>{t("common.me")}:</label>
                  <Tooltip
                    color={"#fff"}
                    title={
                      <span style={{ color: "#000" }}>
                        <b>{t("common.tenNb")}: </b>
                        {data.tenNbMe}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        {renderIconOpenHSBA(data.nbDotDieuTriMeId)}
                        <br />
                        <b>{t("common.maNb")}: </b>
                        {data.maNbMe}
                        <br />
                        <b>{t("baoCao.khoaNB")}: </b>
                        {data.tenKhoaNbMe}
                      </span>
                    }
                  >
                    {data.tenNbMe}
                  </Tooltip>
                </div>
              )}

              {data.dsThongTinCon && data.dsThongTinCon.length && (
                <div className="info-item">
                  <label>{t("common.con")}:</label>
                  {data.dsThongTinCon.map((item, index) => (
                    <span>
                      <Tooltip
                        key={index}
                        color={"#fff"}
                        title={
                          <span style={{ color: "#000" }}>
                            <b>{t("common.tenNb")}: </b>
                            {item.tenNb}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            {renderIconOpenHSBA(item.nbDotDieuTriId)}
                            <br />
                            <b>{t("common.maNb")}: </b>
                            {item.maNb}
                            <br />
                            <b>{t("baoCao.khoaNB")}: </b>
                            {item.tenKhoa}
                          </span>
                        }
                      >
                        {item.tenNb}
                      </Tooltip>
                      {index < data.dsThongTinCon.length - 1 && (
                        <span style={{ marginRight: 2 }}>,</span>
                      )}
                    </span>
                  ))}
                </div>
              )}
            </InfoMeCon>
          </div>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.phanLoaiNb")}
          sort_key="tenPhanLoaiNb"
          dataSort={dataSortColumn["tenPhanLoaiNb"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "150px",
      dataIndex: "tenPhanLoaiNb",
      key: "tenPhanLoaiNb",
      align: "center",
      i18Name: "common.phanLoaiNb",
      show: true,
      render: (item, record) => {
        return (
          <div
            style={{
              background: record?.mauNen,
              color: record?.mauChu,
              width: "fit-content",
              padding: "0 3px",
              margin: "0 auto",
              borderRadius: "2px",
            }}
          >
            {item}
          </div>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("hsba.tuoi")}
          sort_key="ngaySinh"
          dataSort={dataSortColumn["ngaySinh"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "80px",
      dataIndex: "tuoi2",
      key: "tuoi2",
      align: "left",
      i18Name: "hsba.tuoi",
      show: true,
      render: (field, item, index) => {
        if (
          item?.nbDotDieuTriMeId &&
          item?.trangThai === TRANG_THAI_NB.DANG_DIEU_TRI
        ) {
          return calculateAgeChildren(item?.ngaySinh);
        }
        return field;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.soDienThoai")}
          sort_key="soDienThoai"
          dataSort={dataSortColumn["soDienThoai"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "120px",
      dataIndex: "soDienThoai",
      key: "soDienThoai",
      i18Name: "common.soDienThoai",
      show: true,
      render: (field) => {
        if (!field || checkRole([ROLES["TIEP_DON"].HIEN_THI_DAY_DU_SDT])) {
          return field;
        }
        return field.replace(/(\d{4})\d{3}(\d{3})/, "$1xxx$2");
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.ngaySinh")}
          sort_key="ngaySinh"
          dataSort={dataSortColumn["ngaySinh"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "100px",
      dataIndex: "ngaySinh",
      key: "ngaySinh",
      align: "left",
      i18Name: "common.ngaySinh",
      show: true,
      render: (field, item, index) => {
        return (
          <div>
            {item?.ngaySinh &&
              moment(item?.ngaySinh).format(
                item?.chiNamSinh ? "YYYY" : "DD/MM/YYYY"
              )}
          </div>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.maHoSo")}
          sort_key="maHoSo"
          dataSort={dataSortColumn["maHoSo"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "110px",
      dataIndex: "maHoSo",
      key: "maHoSo",
      i18Name: "common.maHoSo",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("common.maBa")}
          sort_key="maBenhAn"
          dataSort={dataSortColumn["maBenhAn"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "80px",
      dataIndex: "maBenhAn",
      key: "maBenhAn",
      i18Name: "common.maBa",
      show: true,
    },
    {
      title: <HeaderSearch title={t("common.soNgayDT")} />,
      width: "100px",
      align: "right",
      i18Name: "common.soNgayDT",
      dataIndex: "soNgayDieuTri",
      key: "soNgayDieuTri",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("common.khoaNhapVien")}
          sort_key="tenKhoaNhapVien"
          dataSort={dataSortColumn["tenKhoaNhapVien"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "300px",
      dataIndex: "tenKhoaNhapVien",
      key: "tenKhoaNhapVien",
      i18Name: "common.khoaNhapVien",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("cdha.thoiGianNhapVien")}
          sort_key="thoiGianVaoKhoaNhapVien"
          dataSort={dataSortColumn["thoiGianVaoKhoaNhapVien"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "170px",
      dataIndex: "thoiGianVaoKhoaNhapVien",
      key: "thoiGianVaoKhoaNhapVien",
      i18Name: "cdha.thoiGianNhapVien",
      show: true,
      render: (field, item, index) => {
        return (
          <div>
            {item?.thoiGianVaoKhoaNhapVien &&
              moment(item?.thoiGianVaoKhoaNhapVien).format(
                "DD/MM/YYYY HH:mm:ss"
              )}
          </div>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.tienTamUng")}
          sort_key="tienTamUng"
          dataSort={dataSortColumn["tienTamUng"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "130px",
      dataIndex: "tienTamUng",
      key: "tienTamUng",
      align: "right",
      i18Name: "thuNgan.tienTamUng",
      show: true,
      render: (field, item, index) => {
        return (
          (item.tienTamUng || 0) - (item.tienHoanUng || 0)
        )?.formatPrice();
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("cdha.tienConLai")}
          sort_key="tienConLai"
          dataSort={dataSortColumn["tienConLai"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "130px",
      dataIndex: "tienConLai",
      key: "tienConLai",
      align: "right",
      i18Name: "cdha.tienConLai",
      show: true,
      render: (field, item, index) => {
        let result = field;
        if (isGopPhieuThuDichVuNhaThuoc) {
          result = (field || 0) + (item?.tienNhaThuocChuaThanhToan || 0);
        }
        return Math.round(result)?.formatPrice();
      },
    },
    {
      title: <HeaderSearch title={t("cdha.chanDoanBenhHienTai")} />,
      width: "300px",
      i18Name: "cdha.chanDoanBenhHienTai",
      show: true,
      render: (field, item, index) => {
        return (
          <EllipsisText
            content={
              item?.dsCdChinh?.length &&
              item?.dsCdChinh?.map((itemLoop) => itemLoop.ten)?.join("; ")
            }
          />
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("cdha.thoiGianVaoKhoa")}
          sort_key="thoiGianVaoKhoa"
          dataSort={dataSortColumn["thoiGianVaoKhoa"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "200px",
      dataIndex: "thoiGianVaoKhoa",
      key: "thoiGianVaoKhoa",
      i18Name: "cdha.thoiGianVaoKhoa",
      show: true,
      render: (field, item, index) => {
        return (
          <div>
            {item?.thoiGianVaoKhoa &&
              moment(item?.thoiGianVaoKhoa).format("DD/MM/YYYY HH:mm:ss")}
          </div>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("cdha.trangThaiNb")}
          sort_key="trangThai"
          dataSort={dataSortColumn["trangThai"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "180px",
      dataIndex: "trangThai",
      key: "trangThai",
      i18Name: "cdha.trangThaiNb",
      show: true,
      render: (item) => {
        return (listTrangThaiNb || []).find((x) => x.id === item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.trangThaiTheBhyt")}
          sort_key="trangThaiTheBhyt"
          dataSort={dataSortColumn["trangThaiTheBhyt"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "180px",
      dataIndex: "trangThaiTheBhyt",
      key: "trangThaiTheBhyt",
      i18Name: "common.trangThaiTheBhyt",
      show: true,
      render: (item) =>
        !!item && (
          <Tag
            className="mt-2"
            color={
              [
                TRANG_THAI_THE_BAO_HIEM.THE_DUNG,
                TRANG_THAI_THE_BAO_HIEM.DA_DUYET_THE,
              ].includes(item)
                ? "green"
                : "orange"
            }
          >
            {(listTrangThaiTheBhyt || []).find((x) => x.id === item)?.ten}
          </Tag>
        ),
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.giuongPhong")}
          sort_key="giuongId"
          dataSort={dataSortColumn["giuongId"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "200px",
      key: "phongGiuong",
      i18Name: "quanLyNoiTru.giuongPhong",
      show: true,
      render: (item, data) => {
        const { soHieuGiuong, tenPhong, loaiChuyenKhoa } = data || {};

        if (loaiChuyenKhoa == 30) {
          return <span>Nghỉ điều trị</span>;
        }
        return soHieuGiuong && tenPhong ? `${soHieuGiuong} - ${tenPhong}` : "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("cdha.bacSiDieuTri")}
          sort_key="tenBacSiDieuTri"
          dataSort={dataSortColumn["tenBacSiDieuTri"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "250px",
      dataIndex: "tenBacSiDieuTri",
      key: "tenBacSiDieuTri",
      i18Name: "cdha.bacSiDieuTri",
      show: true,
      render: (_, record) => {
        return `${record.vietTatHocHamHocVi || ""} ${
          record.tenBacSiDieuTri || ""
        }`;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.dieuDuongPhuTrach")}
          sort_key="tenDieuDuongPhuTrach"
          dataSort={dataSortColumn["tenDieuDuongPhuTrach"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "250px",
      dataIndex: "tenDieuDuongPhuTrach",
      key: "tenDieuDuongPhuTrach",
      i18Name: "quanLyNoiTru.dieuDuongPhuTrach",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("common.maNguoiBenh")}
          sort_key="maNb"
          dataSort={dataSortColumn["maNb"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "130px",
      dataIndex: "maNb",
      key: "maNb",
      i18Name: "common.maNguoiBenh",
      show: true,
    },
    {
      title: <HeaderSearch title={t("common.diaChi")} />,
      width: "250px",
      dataIndex: "diaChi",
      key: "diaChi",
      i18Name: "common.diaChi",
      show: true,
      ellipsis: true,
    },
    {
      title: <HeaderSearch title={t("common.doiTuong")} />,
      width: 120,
      dataIndex: "doiTuong",
      key: "doiTuong",
      i18Name: "common.doiTuong",
      show: true,
      render: (item) => listDoiTuong.find((x) => x.id === item)?.ten,
    },
    {
      title: (
        <HeaderSearch title={t("khamBenh.chanDoan.phanLoaiNguoiBenhCapCuu")} />
      ),
      width: 140,
      dataIndex: "phanLoaiNbCapCuu",
      key: "phanLoaiNbCapCuu",
      i18Name: "khamBenh.chanDoan.phanLoaiNguoiBenhCapCuu",
      show: true,
      className: "phanLoaiNbCapCuu",
      render: (item) => listPhanLoaiNBCapCuu.find((x) => x.id === item)?.ten,
    },
    {
      title: <HeaderSearch title={t("quanLyNoiTru.soTheBHYT")} />,
      width: 150,
      dataIndex: "maTheBhyt",
      key: "maTheBhyt",
      i18Name: "quanLyNoiTru.soTheBHYT",
      show: true,
    },
    {
      title: <HeaderSearch title={t("tenTruong.huongDieuTri")} />,
      width: 150,
      dataIndex: "huongDieuTri",
      key: "huongDieuTri",
      i18Name: "tenTruong.huongDieuTri",
      show: true,
      render: (item) => listHuongDieuTri.find((x) => x.id === item)?.ten,
    },
    {
      title: <HeaderSearch title={t("tenTruong.ketQuaDieuTri")} />,
      width: 150,
      dataIndex: "ketQuaDieuTri",
      key: "ketQuaDieuTri",
      i18Name: "tenTruong.ketQuaDieuTri",
      show: true,
      render: (item) => listKetQuaDieuTri.find((x) => x.id === item)?.ten,
    },
    {
      title: (
        <HeaderSearch
          title={t("tenTruong.thoiGianChungTu")}
          sort_key="thoiGianChungTu"
          dataSort={dataSortColumn["thoiGianChungTu"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "200px",
      dataIndex: "thoiGianChungTu",
      key: "thoiGianChungTu",
      i18Name: "tenTruong.thoiGianChungTu",
      show: true,
      render: (field, item, index) => {
        return (
          <div>
            {item?.thoiGianChungTu &&
              moment(item?.thoiGianChungTu).format("DD/MM/YYYY HH:mm:ss")}
          </div>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.phanLoaiPHCN")}
          sort_key="dsPhanLoaiPhcnId"
          dataSort={dataSortColumn["dsPhanLoaiPhcnId"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "200px",
      dataIndex: "dsPhanLoaiPhcnId",
      key: "dsPhanLoaiPhcnId",
      i18Name: "danhMuc.phanLoaiPHCN",
      show: true,
      render: (item, data) => {
        if ([10, 30].includes(data.trangThaiPhcn)) {
          return (item || [])
            .map(
              (x) => (listAllPhanLoaiPHCN || []).find((x1) => x1.id == x)?.ten
            )
            .join(", ");
        } else {
          return "";
        }
      },
    },
    {
      title: <HeaderSearch title={t("kho.tuVanThuoc.trangThaiTuVanThuoc")} />,
      width: "160px",
      dataIndex: "trangThaiTuVanThuoc",
      key: "trangThaiTuVanThuoc",
      i18Name: "kho.tuVanThuoc.trangThaiTuVanThuoc",
      show: true,
      render: (item) =>
        listTrangThaiTuVanThuoc.find((x) => x.id === item)?.ten || "",
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.trangThaiThanhToanTaiNganHang")}
          sort_key="trangThaiThanhToan"
          dataSort={dataSortColumn["trangThaiThanhToan"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "200px",
      dataIndex: "trangThaiThanhToan",
      key: "trangThaiThanhToan",
      i18Name: "quanLyNoiTru.trangThaiThanhToanTaiNganHang",
      show: false,
      render: (item) =>
        (listTrangThaiThanhToan || []).find((i) => i.id === item)?.ten || "",
    },
    {
      title: <HeaderSearch title={t("quanLyNoiTru.lyDoTuChoiDuyet")} />,
      width: 150,
      dataIndex: "duyetChiPhi",
      key: "duyetChiPhi",
      i18Name: "quanLyNoiTru.lyDoTuChoiDuyet",
      show: false,
    },
    {
      title: <HeaderSearch title={t("quanLyNoiTru.toDieuTri.cheDoChamSoc")} />,
      width: 120,
      dataIndex: "tenCheDoChamSoc",
      key: "tenCheDoChamSoc",
      i18Name: "quanLyNoiTru.toDieuTri.cheDoChamSoc",
    },
    {
      title: <HeaderSearch title={t("quanLyNoiTru.diUng")} />,
      width: "80px",
      dataIndex: "diUng",
      key: "diUng",
      i18Name: "quanLyNoiTru.diUng",
      show: true,
      hidden: !hienThiCacFieldCanhBaoNoiTru,
      align: "center",
      render: (_, record) => {
        return renderDiUng(record);
      },
    },
    {
      title: <HeaderSearch title={t("quanLyNoiTru.nguyCoTeNga")} />,
      width: "120px",
      dataIndex: "khungDanhGia",
      key: "nguyCoTeNga",
      i18Name: "quanLyNoiTru.nguyCoTeNga",
      show: true,
      hidden: !hienThiCacFieldCanhBaoNoiTru,
      align: "center",
      render: (_, record) => {
        const result = renderNguyCoTeNga(record);
        if (!result || !result.text) return null;

        return (
          <span style={{ color: result.color, fontWeight: "bold" }}>
            {result.text}
          </span>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.gioiTinh")}
          sort_key="gioiTinh"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.gioiTinh || 0}
        />
      ),
      width: 110,
      dataIndex: "gioiTinh",
      i18Name: "common.gioiTinh",
      key: "gioiTinh",
      show: false,
      render: (item) => {
        return listGioiTinh.find((x) => x.id === item)?.ten || "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.gioSinh")}
          sort_key="ngaySinh"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.ngaySinh || 0}
        />
      ),
      width: 110,
      dataIndex: "ngaySinh",
      i18Name: "common.gioSinh",
      key: "gioSinh",
      show: false,
      hidden: !showGioSinh,
      render: (item) => {
        return item && moment(item).format("HH:mm:ss");
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.mucHuongTheBhyt")}
          sort_key="mucHuongTheBhyt"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.mucHuongTheBhyt || 0}
        />
      ),
      width: 80,
      dataIndex: "mucHuongTheBhyt",
      i18Name: "quanLyNoiTru.mucHuongTheBhyt",
      key: "mucHuongTheBhyt",
      align: "center",
      show: true,
      render: (item) => {
        return item && `${item}%`;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("tiepDon.ngayHenKham")}
          sort_key="thoiGianHenKham"
          dataSort={dataSortColumn["thoiGianHenKham"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "170px",
      dataIndex: "thoiGianHenKham",
      key: "thoiGianHenKham",
      i18Name: "tiepDon.ngayHenKham",
      show: true,
      render: (field, item, index) => {
        return (
          <div>
            {item?.thoiGianHenKham &&
              moment(item?.thoiGianHenKham).format("DD/MM/YYYY HH:mm:ss")}
          </div>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("phacDoDieuTri.ngayRaVien")}
          sort_key="thoiGianRaVien"
          dataSort={dataSortColumn["thoiGianRaVien"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "170px",
      dataIndex: "thoiGianRaVien",
      key: "thoiGianRaVien",
      i18Name: "phacDoDieuTri.ngayRaVien",
      show: true,
      render: (field, item, index) => {
        return (
          <div>
            {item?.thoiGianRaVien &&
              moment(item?.thoiGianRaVien).format("DD/MM/YYYY HH:mm:ss")}
          </div>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("khth.trangThaiDuyetBH")}
          sort_key="trangThaiDuyetBh"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.trangThaiDuyetBh || 0}
        />
      ),
      width: 110,
      dataIndex: "trangThaiDuyetBh",
      i18Name: "khth.trangThaiDuyetBH",
      key: "trangThaiDuyetBh",
      show: true,
      render: (item) =>
        !!item && (
          <Tag
            className="mt-2"
            color={
              [TRANG_THAI_DUYET_BH.DA_DUYET_BH].includes(item)
                ? "green"
                : "orange"
            }
          >
            {(listTrangThaiDuyetBH || []).find((x) => x.id === item)?.ten}
          </Tag>
        ),
    },

    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("common.tienIch")}
              <SVG.IcSetting onClick={onSettings} className="icon" />
            </>
          }
        />
      ),
      width: "120px",
      dataIndex: "",
      key: "tienIch",
      align: "center",
      fixed: "right",
      ignore: true,
      render: (record) => {
        const { trangThai, doiTuongKcb, maSoGiayToTuyThan } = record || {};

        return (
          <div>
            <Tooltip title={t("common.xemChiTiet")}>
              <SVG.IcEye className="ic-action" onClick={onViewDetail(record)} />
            </Tooltip>
            {onShowLogModal && (
              <Tooltip
                placement="topLeft"
                title={t("quanLyNoiTru.kiemTraLichSuThayDoi")}
              >
                <SVG.IcShowLog
                  className="ic-action"
                  onClick={onShowLogModal(record)}
                />
              </Tooltip>
            )}
            {trangThai === TRANG_THAI_NB.DA_THANH_TOAN_HEN_DIEU_TRI &&
              (doiTuongKcb === DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU ||
                (isKhoaNbNgatDieuTriNoiTru &&
                  (doiTuongKcb === DOI_TUONG_KCB.DIEU_TRI_NOI_TRU ||
                    doiTuongKcb ===
                      DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_BAN_NGAY))) && (
                <Tooltip title={t("quanLyNoiTru.tiepDonHenDieuTri")}>
                  <SVG.IcTuyChinhTiepDon
                    color={"var(--color-blue-primary)"}
                    className="ic-action"
                    onClick={onTiepDonHenDieuTri(record)}
                  />
                </Tooltip>
              )}

            {onShowLichSuKCB && (
              <Tooltip
                title={t("common.lichSuKhamChuaBenh")}
                placement="topLeft"
              >
                <SVG.IcLichSuKCB
                  className="ic-action"
                  onClick={onShowLichSuKCB(record)}
                />
              </Tooltip>
            )}
          </div>
        );
      },
    },
  ];
};
