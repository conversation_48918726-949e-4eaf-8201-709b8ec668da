import React, { memo, useEffect } from "react";
import ScreenPhieuLinh from "pages/quanLyNoiTru/components/ScreenPhieuLinh";
import { useDispatch } from "react-redux";
import { useParams } from "react-router-dom";
import { useConfirm, useStore } from "hooks";
import { useTranslation } from "react-i18next";
import { HeaderSearch, Tooltip } from "components";
import { SVG } from "assets";

const LinhTheoDichVu = ({ refSetting }) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const { id } = useParams();
  const listDataChiTiet = useStore("nbPhieuLinhSuatAn.listDataChiTiet", []);
  const chiTietPhieuLinh = useStore("nbPhieuLinhSuatAn.chiTietPhieuLinh", {});

  const {
    nbPhieuLinhSuatAn: { chiTiet, deletePhieuLinhSuatAn, getById },
  } = useDispatch();

  useEffect(() => {
    chiTiet({ phieuLinhId: id, page: "", size: "", phieuTraId: undefined });
  }, [chiTietPhieuLinh]);

  const onDelete = (item, e) => {
    e.stopPropagation();
    showConfirm(
      {
        title: t("common.xoaDuLieu"),
        content: `${t("common.banChacChanMuonXoa")}?`,
        cancelText: t("common.quayLai"),
        okText: t("common.dongY"),
        classNameOkText: "button-error",
        showImg: true,
        showBtnOk: true,
      },
      () => {
        deletePhieuLinhSuatAn({ id, dichVuId: item.dichVuId }).then((s) => {
          getById(id);
        });
      }
    );
  };

  const renderColumns = ({ columns, onSettings }) => {
    refSetting.current["tab1"] = onSettings;
    return [
      columns.stt2({ width: 25 }),
      // column.tenDichVu(),
      columns.inputTimeout({
        title: t("danhMuc.maDichVu"),
        dataIndex: "maDichVu",
        width: 50,
      }),
      columns.inputTimeout({
        title: t("common.tenDichVu"),
        dataIndex: "tenDichVu",
        width: 100,
      }),
      columns.inputTimeout({
        title: t("common.soLuong"),
        dataIndex: "soLuong",
        width: 50,
      }),
      columns.inputTimeout({
        title: t("common.donViTinh"),
        dataIndex: "tenDonViTinh",
        width: 50,
      }),
      columns.date({
        title: t("common.ngayThucHien"),
        dataIndex: "thoiGianThucHien",
        width: 100,
      }),
      {
        title: <HeaderSearch title={t("common.tienIch")} />,
        width: 60,
        fixed: "right",
        key: "",
        dataIndex: "",
        align: "center",
        ignore: true,
        render: (item, data) => {
          return (
            <>
              {chiTietPhieuLinh?.trangThai === 10 && (
                <Tooltip title={t("common.xoa")}>
                  <SVG.IcDelete
                    className="ic-action"
                    onClick={(e) => onDelete(data, e)}
                  />
                </Tooltip>
              )}
            </>
          );
        },
      },
    ];
  };

  return (
    <ScreenPhieuLinh.DanhSach
      renderColumns={renderColumns}
      getDanhSach={chiTiet}
      initParam={{ phieuLinhId: id, page: "", size: "", phieuTraId: undefined }}
      dataSource={listDataChiTiet}
      tableName="table_NOITRU_ChiTietSuatAn_DsLinhTheoDv"
      // totalElements={totalElements}
    ></ScreenPhieuLinh.DanhSach>
  );
};

export default memo(LinhTheoDichVu);
