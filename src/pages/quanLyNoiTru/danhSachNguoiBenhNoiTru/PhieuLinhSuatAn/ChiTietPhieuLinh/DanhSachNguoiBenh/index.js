import React, { useEffect } from "react";
import { useDispatch } from "react-redux";
import { useParams } from "react-router-dom";
import ScreenPhieuLinh from "pages/quanLyNoiTru/components/ScreenPhieuLinh";
import { useConfirm, useEnum, useStore } from "hooks";
import { useTranslation } from "react-i18next";
import { ENUM } from "constants/index";
import { HeaderSearch, Tooltip } from "components";
import { SVG } from "assets";

const DanhSachNguoiBenh = ({ refSetting }) => {
  const { id } = useParams();
  const { t } = useTranslation();
  const { showConfirm } = useConfirm();
  const {
    chiDinhSuatAn: { getDsSuatAn },
    loaiBuaAn: { getListAllLoaiBuaAn },
    nbPhieuLinhSuatAn: { deletePhieuLinhSuatAn, getById },
  } = useDispatch();
  const { listDvSuatAn, totalElements, page } = useStore(
    "chiDinhSuatAn",
    {},
    {
      fields: "listDvSuatAn,totalElements,page",
    }
  );
  const chiTietPhieuLinh = useStore("nbPhieuLinhSuatAn.chiTietPhieuLinh", {});

  const [listGioiTinh] = useEnum(ENUM.GIOI_TINH, []);
  const [listTrangThaiPhieuLinhSuatAn] = useEnum(
    ENUM.TRANG_THAI_PHIEU_LINH_SUAT_AN,
    []
  );
  const listAllLoaiBuaAn = useStore("loaiBuaAn.listAllLoaiBuaAn", []);

  useEffect(() => {
    getListAllLoaiBuaAn({ page: "", size: "", active: true });
  }, []);

  useEffect(() => {
    getDsSuatAn({ phieuLinhId: id, page: 0, size: 50 });
  }, [chiTietPhieuLinh]);

  const onDelete = (item, e) => {
    e.stopPropagation();
    showConfirm(
      {
        title: t("common.xoaDuLieu"),
        content: `${t("common.banChacChanMuonXoa")}?`,
        cancelText: t("common.quayLai"),
        okText: t("common.dongY"),
        classNameOkText: "button-error",
        showImg: true,
        showBtnOk: true,
      },
      () => {
        deletePhieuLinhSuatAn({ id, nbDvSuatAnId: item.id }).then((s) => {
          getById(id);
        });
      }
    );
  };

  const onSearch = (data) => {
    getDsSuatAn({ phieuLinhId: id, ...data });
  };

  const renderColumns = ({ columns, onSettings }) => {
    refSetting.current["tab2"] = onSettings;
    return [
      columns.stt,
      columns.inputTimeout({
        title: t("common.tenNb"),
        dataIndex: "tenNb",
        searchKey: "tenNb",
        showSearch: true,
        width: 200,
      }),
      columns.inputTimeout({
        title: t("common.maBa"),
        dataIndex: "maBenhAn",
        searchKey: "maBenhAn",
        showSearch: true,
        width: 100,
      }),
      columns.enum({
        title: t("common.gioiTinh"),
        dataIndex: "gioiTinh",
        listEnum: listGioiTinh,
        width: 100,
      }),
      columns.inputTimeout({
        title: t("danhMuc.maDichVu"),
        dataIndex: "maDichVu",
        searchKey: "maDichVu",
        showSearch: true,
        width: 100,
      }),
      columns.inputTimeout({
        title: t("common.tenDichVu"),
        dataIndex: "tenDichVu",
        searchKey: "tenDichVu",
        showSearch: true,
        width: 180,
      }),
      columns.inputTimeout({
        title: t("common.ghiChu"),
        dataIndex: "ghiChu",
        width: 180,
      }),
      columns.select({
        title: t("quanLyNoiTru.suatAn.loaiBuaAn"),
        dataIndex: "loaiBuaAnId",
        width: 150,
        dataSearch: listAllLoaiBuaAn,
        showSearch: true,
        hasAllOption: true,
        render: (item) =>
          (listAllLoaiBuaAn || []).find((x) => x.id == item)?.ten || "",
      }),
      columns.inputTimeout({
        title: t("quanLyNoiTru.suatAn.soLuong"),
        dataIndex: "soLuong",
        width: 80,
      }),
      columns.inputTimeout({
        title: t("common.donViTinh"),
        dataIndex: "tenDonViTinh",
        width: 100,
      }),
      columns.enum({
        title: t("common.trangThai"),
        dataIndex: "trangThai",
        listEnum: listTrangThaiPhieuLinhSuatAn,
        width: 150,
      }),
      columns.date({
        title: t("common.ngayThucHien"),
        dataIndex: "thoiGianThucHien",
        searchKey: "tuThoiGianThucHien",
        showSearch: true,
        width: 150,
      }),
      columns.checkbox({
        title: t("common.khongTinhTien"),
        dataIndex: "khongTinhTien",
        width: 100,
        dataSearch: [t("common.khongTinhTien"), t("common.coTinhTien")],
      }),
      columns.checkbox({
        title: t("common.tuTra"),
        dataIndex: "tuTra",
        dataSearch: [t("common.tuTra"), t("common.khongTuTra")],
      }),
      {
        title: <HeaderSearch title={t("common.tienIch")} />,
        width: 60,
        fixed: "right",
        key: "",
        dataIndex: "",
        align: "center",
        ignore: true,
        render: (item, data) => {
          return (
            <>
              {data.trangThai === 10 && (
                <Tooltip title={t("common.xoa")}>
                  <SVG.IcDelete
                    className="ic-action"
                    onClick={(e) => onDelete(data, e)}
                  />
                </Tooltip>
              )}
            </>
          );
        },
      },
    ];
  };

  return (
    <ScreenPhieuLinh.DanhSach
      getDanhSach={onSearch}
      dataSource={listDvSuatAn}
      renderColumns={renderColumns}
      totalElements={totalElements}
      tableName="table_NOITRU_ChiTietSuatAn_DsNb"
      initParam={{ page: page, size: 50 }}
    />
  );
};

export default DanhSachNguoiBenh;
