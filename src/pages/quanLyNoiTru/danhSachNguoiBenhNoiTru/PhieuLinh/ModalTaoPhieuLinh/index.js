import React, { useRef } from "react";
import { Col, message, Row } from "antd";
import {
  Checkbox,
  Select,
  SelectLoadMore,
  DateTimePicker,
  TableWrapper,
  HeaderSearch,
} from "components";
import {
  ENUM,
  LOAI_DICH_VU,
  LOAI_NHAP_XUAT,
  LOAI_PHONG,
  THIET_LAP_CHUNG,
  TRANG_THAI_THUOC,
} from "constants/index";
import nbDotDieuTriProvider from "data-access/nb-dot-dieu-tri-provider";
import {
  useConfirm,
  useEnum,
  useListAll,
  useLoading,
  useStore,
  useThietLap,
} from "hooks";
import moment from "moment";
import ScreenPhieuLinh from "pages/quanLyNoiTru/components/ScreenPhieuLinh";
import { forwardRef, useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { useHistory } from "react-router-dom";
import dmDichVuKhoProvider from "data-access/categories/dm-dich-vu-kho-provider.js";
import nbCapPhatThuocProvider from "data-access/kho/nb-cap-phat-thuoc-provider";
import { sortBy, uniqBy } from "lodash";
import DOMPurify from "dompurify";
import { checkRole } from "lib-utils/role-utils";
import { ROLES } from "constants";

const LIST_TRANG_THAI_DLS = [
  {
    id: 10,
    i18n: "kho.choDuyetDLS",
  },
  {
    id: 15,
    i18n: "kho.choDuyetDLS",
  },
  {
    id: 25,
    i18n: "kho.daDuyetDLS",
  },
];

const ModalTaoPhieuLinh = (
  {
    disabledLoaiNhapXuat,
    refModalTaoPhieuSuatAn,
    khoaLamViecId,
    isPttt = false,
    isCDHA = false,
    initState: _initState,
  },
  ref
) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const { auth } = useSelector((state) => state.auth);
  const refGetSubmit = useRef(() => {});
  const refIgnore = useRef(false);
  const [listLoaiDichVuKho] = useEnum(ENUM.LOAI_DICH_VU_KHO);
  const listLoaiDichVuKhoCustom = useMemo(
    () => [
      ...[90, 95, 100, 110, 125, 120].map((i) =>
        listLoaiDichVuKho?.find((j) => j.id === i)
      ),
      { id: 50, ten: t("common.suatAn") },
    ],
    [listLoaiDichVuKho]
  );
  const [listloaiNhapXuat] = useEnum(ENUM.LOAI_NHAP_XUAT);

  const listKhoCha = useStore("kho.listKhoCha", []);
  // const listAllTuKho = useStore("kho.listAllKho", []);

  const listPhong = useStore("phong.listPhong", []);
  const listThietLapChonKhoTongHop = useStore(
    "thietLapChonKho.listThietLapChonKhoTongHop",
    []
  );

  const listTuKho = useStore("thietLapChonKho.listTuKho", []);
  const listKhoaTheoTaiKhoan = useStore("khoa.listKhoaTheoTaiKhoan", []);
  const listBuongPt = useStore("phong.listBuongPt", []);
  const listAllPhanLoaiThuoc = useStore(
    "phanLoaiThuoc.listAllPhanLoaiThuoc",
    []
  );
  const [listAllMaPhieuLinh] = useListAll("maPhieuLinh");

  const listTuKhoCustom = useMemo(() => {
    const list = listTuKho.map((item) => ({
      id: item?.kho?.id,
      ten: item?.kho?.ten,
    }));
    return sortBy(uniqBy(list, "id"), "ten");
  }, [listTuKho]);

  const [CANH_BAO_DUYET_DLS_KHI_PHAT_THUOC_TAO_PHIEU_LINH] = useThietLap(
    THIET_LAP_CHUNG.CANH_BAO_DUYET_DLS_KHI_PHAT_THUOC_TAO_PHIEU_LINH,
    ""
  );

  const [LINH_BU_TU_TRUC_KB_DA_THANH_TOAN] = useThietLap(
    THIET_LAP_CHUNG.LINH_BU_TU_TRUC_KB_DA_THANH_TOAN,
    ""
  );
  const [dataHIEN_THI_CHECKBOX_TU_TRUC] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_CHECKBOX_TU_TRUC
  );

  const history = useHistory();
  const { showLoading, hideLoading } = useLoading();
  const [state, _setState] = useState({
    paramTuKho: {},
    listKhoTuTruc: [],
  });
  const setState = (data) => {
    _setState((pre) => ({ ...pre, ...data }));
  };
  const {
    phong: { getListPhongTongHop, getListBuongTongHop },
    kho: { getListKhoCha },
    nhapKho: { taoPhieuLinhBu },
    thietLapChonKho: { getListThietLapChonKhoTongHop, getListThietLapChonKho },
    phanLoaiThuoc: { getListAllPhanLoaiThuoc },
  } = useDispatch();

  const initState = useMemo(() => {
    return _initState
      ? {
          ..._initState,
          tuThoiGian: moment().set("hour", 0).set("minute", 0).set("second", 0),
          denThoiGian: moment()
            .set("hour", 23)
            .set("minute", 59)
            .set("second", 59),
          dsKhoaChiDinhId:
            auth?.dsKhoaPhuTrachId.length < 2
              ? auth?.dsKhoaPhuTrachId[0]
              : khoaLamViecId,
          thanhToan: LINH_BU_TU_TRUC_KB_DA_THANH_TOAN?.eval(),
        }
      : {
          loaiDichVu: 90,
          tuThoiGian: moment().set("hour", 0).set("minute", 0).set("second", 0),
          denThoiGian: moment()
            .set("hour", 23)
            .set("minute", 59)
            .set("second", 59),
          loaiNhapXuat: 85,
          dsKhoaChiDinhId:
            auth?.dsKhoaPhuTrachId.length < 2
              ? auth?.dsKhoaPhuTrachId[0]
              : khoaLamViecId,
          thanhToan: LINH_BU_TU_TRUC_KB_DA_THANH_TOAN?.eval(),
        };
  }, [
    auth?.dsKhoaPhuTrach,
    khoaLamViecId,
    _initState,
    LINH_BU_TU_TRUC_KB_DA_THANH_TOAN,
  ]);
  // console.log("initState", initState)
  useEffect(() => {
    if (khoaLamViecId) {
      getListPhongTongHop({
        khoaId: khoaLamViecId,
        active: true,
        page: "",
        size: "",
      });
      getListBuongTongHop({
        khoaId: khoaLamViecId,
        active: true,
        page: "",
        size: "",
        dsLoaiPhong: [LOAI_PHONG.PHONG_GIUONG, LOAI_PHONG.PHONG_GIUONG_TU_CHON],
      });
    }
  }, [khoaLamViecId]);

  useEffect(() => {
    getListAllPhanLoaiThuoc({ page: "", size: "", active: true });
  }, []);

  const getListTuKho = (payload) => {
    const paramTuKho = {
      ...state.paramTuKho,
      ...payload,
      dsCoCheDuyetPhat: 20,
    };
    setState({ paramTuKho });
    getListThietLapChonKho({
      loaiDichVu: payload?.dsLoaiDichVu,
      page: 0,
      size: 500,
      tuTruc: false,
      khoaChiDinhId: khoaLamViecId,
    });
  };

  const getListDenKho = (_state, onChange = () => {}) => {
    if (_state.loaiNhapXuat === 80 && _state.khoDoiUngId) {
      getListKhoCha({
        active: true,
        khoTrucThuocId: _state.khoDoiUngId,
      }).then((response) =>
        onChange(response.length === 1 ? response[0]?.id : null)
      );
    } else if (_state.loaiNhapXuat === 85 && _state.loaiDichVu) {
      getListThietLapChonKhoTongHop({
        khoaChiDinhId: khoaLamViecId,
        loaiDichVu: _state.loaiDichVu,
        noiTru: true,
        tuTruc: false,
        active: true,
        page: "",
        size: "",
        canLamSang: false,
      }).then((response) => {
        onChange(response.length === 1 ? response[0]?.id : null);
      });
    }
  };

  const onCustomChange = (key, onChange, _state) => (value) => {
    if (key === "loaiDichVu") {
      if (value === LOAI_DICH_VU.SUAT_AN) {
        ref.current && ref.current.close();
        refModalTaoPhieuSuatAn.current && refModalTaoPhieuSuatAn.current.show();
        return;
      }
      if (_state.loaiNhapXuat === LOAI_NHAP_XUAT.LINH_BU_TU_TRUC) {
        getListTuKho({ dsLoaiDichVu: value });
      }
      getListDenKho({ ..._state, [key]: value }, onChange("khoId"));

      onChange("khoDoiUngId")();
    }

    if (key === "loaiNhapXuat" || key === "khoDoiUngId") {
      getListDenKho({ ..._state, [key]: value }, onChange("khoId"));
      // onChange("khoId")();
    }

    if (
      key === "loaiNhapXuat" &&
      value === LOAI_NHAP_XUAT.LINH_BU_TU_TRUC &&
      _state.loaiDichVu !== state.paramTuKho.dsLoaiDichVu
    ) {
      getListTuKho({ dsLoaiDichVu: _state.loaiDichVu });
    }

    if (key === "dsKhoaChiDinhId") {
      if (_state.loaiNhapXuat === 80) {
        getListTuKho({ khoaQuanLyId: value });
      }
      getListDenKho({ ..._state, [key]: value }, onChange("khoId"));

      getListPhongTongHop({ khoaId: value, active: true, page: "", size: "" });
      onChange("dsPhongId")([]);
      getListBuongTongHop({
        khoaId: value,
        active: true,
        page: "",
        size: "",
        dsLoaiPhong: [LOAI_PHONG.PHONG_GIUONG, LOAI_PHONG.PHONG_GIUONG_TU_CHON],
      });
    }

    onChange(key)(value);
  };

  const renderFilter = ({ _state, onChange }) => {
    const listDenKho = [
      LOAI_NHAP_XUAT.CHE_PHAM_MAU,
      LOAI_NHAP_XUAT.LINH_NOI_TRU,
    ]?.includes(_state.loaiNhapXuat)
      ? listThietLapChonKhoTongHop
      : _state.loaiNhapXuat === 80
      ? listKhoCha || []
      : [];

    return (
      <Row>
        <Col md={12} xl={12} xxl={12}>
          <div className="item-select">
            <label className="label-filter">
              {t("cdha.loaiHangHoa")}
              <span className="icon-required">*</span>
            </label>
            <Select
              onChange={onCustomChange("loaiDichVu", onChange, _state)}
              value={_state.loaiDichVu}
              className="input-filter"
              placeholder={t("cdha.chonLoaiHangHoa")}
              data={listLoaiDichVuKhoCustom}
            />
          </div>
        </Col>
        <Col md={12} xl={12} xxl={12}>
          <div className="item-select">
            <label className="label-filter">
              {t("cdha.loaiLinh")}
              <span className="icon-required">*</span>
            </label>
            <Select
              disabled={disabledLoaiNhapXuat}
              onChange={onCustomChange("loaiNhapXuat", onChange, _state)}
              value={_state.loaiNhapXuat}
              className="input-filter"
              placeholder={t("cdha.chonLoaiLinh")}
              data={listloaiNhapXuat?.filter((x) =>
                [
                  LOAI_NHAP_XUAT.LINH_BU_TU_TRUC,
                  LOAI_NHAP_XUAT.LINH_NOI_TRU,
                  LOAI_NHAP_XUAT.CHE_PHAM_MAU,
                ]?.includes(x.id)
              )}
            />
          </div>
        </Col>
        <Col md={12} xl={12} xxl={12}>
          {![
            LOAI_NHAP_XUAT.LINH_NOI_TRU,
            LOAI_NHAP_XUAT.CHE_PHAM_MAU,
          ]?.includes(_state.loaiNhapXuat) && (
            <div className="item-select">
              <label className="label-filter">
                {t("cdha.tuKho")}
                <span className="icon-required">*</span>
              </label>
              <Select
                disabled={_state.loaiNhapXuat === LOAI_NHAP_XUAT.LINH_NOI_TRU}
                onChange={onCustomChange("khoDoiUngId", onChange, _state)}
                value={_state.khoDoiUngId}
                className="input-filter"
                placeholder={t("cdha.chonKho")}
                data={listTuKhoCustom}
              />
            </div>
          )}
        </Col>
        <Col md={12} xl={12} xxl={12}>
          <div className="item-select">
            <label className="label-filter">
              {t("cdha.denkho")}
              {!(
                dataHIEN_THI_CHECKBOX_TU_TRUC?.eval() &&
                _state.loaiNhapXuat == 85
              ) && <span className="icon-required">*</span>}
            </label>
            <Select
              onChange={onChange("khoId")}
              value={_state.khoId}
              className="input-filter"
              placeholder={t("cdha.chonKho")}
              data={listDenKho}
            />
          </div>
        </Col>
        <Col md={12} xl={12} xxl={12}>
          <div className="item-date">
            <label className="label-filter">
              {t("cdha.linhTuNgay")}
              <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              showTime={false}
              value={_state.tuThoiGian}
              onChange={onChange("tuThoiGian")}
              placeholder={t("common.chonNgay")}
              format="DD/MM/YYYY HH:mm:ss"
              className="input-filter"
              disabledDate={(date) => date > _state.denThoiGian}
            />
          </div>
        </Col>
        <Col md={12} xl={12} xxl={12}>
          <div className="item-date">
            <label className="label-filter">
              {t("cdha.linhDenNgay")}
              <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              showTime={false}
              value={_state.denThoiGian}
              onChange={onChange("denThoiGian")}
              placeholder={t("common.chonNgay")}
              format="DD/MM/YYYY HH:mm:ss"
              className="input-filter"
              disabledDate={(date) => date < _state.tuThoiGian}
            />
          </div>
        </Col>
        <Col md={12} xl={12} xxl={12}>
          <div className="item-select">
            <label className="label-filter">
              {t("cdha.khoaChiDinh")}
              <span className="icon-required">*</span>
            </label>
            <Select
              onChange={onCustomChange("dsKhoaChiDinhId", onChange, _state)}
              value={_state.dsKhoaChiDinhId}
              className="input-filter"
              placeholder={t("common.chonKhoa")}
              data={listKhoaTheoTaiKhoan}
            />
          </div>
        </Col>
        <Col md={12} xl={12} xxl={12}>
          <div className="item-select">
            <label className="label-filter">{t("common.phong")}</label>
            <Select
              onChange={onChange("dsPhongId")}
              value={_state.dsPhongId}
              className="input-filter"
              mode="multiple"
              placeholder={t("common.chonPhong")}
              data={listPhong}
            />
          </div>
        </Col>
        <Col md={12} xl={12} xxl={12}>
          <div className="item-select">
            <label className="label-filter">{t("common.maBenhAn")}</label>
            <SelectLoadMore
              api={(params) => {
                const { value, ...searchParams } = params || {};
                if (!value) {
                  return nbDotDieuTriProvider.getNbNoiTru({
                    ...searchParams,
                    maBenhAn: "",
                    tenNb: "",
                  });
                }
                let _value = value.trim();
                switch (true) {
                  case /^[0-9]{7}$/.test(_value):
                    Object.assign(searchParams, { maBenhAn: _value });
                    break;
                  case !/^[0-9]+$/.test(_value) &&
                    !/^[0-9A-F]{8}$/.test(_value) &&
                    !/^[a-zA-Z]+[0-9]+$/.test(_value):
                    Object.assign(searchParams, { tenNb: _value });
                    break;
                  default:
                    return Promise.reject("Invalid search value");
                }
                return nbDotDieuTriProvider.getNbNoiTru(searchParams);
              }}
              mapData={(i) => ({
                value: i.id,
                label: `${i.maBenhAn} - ${i.tenNb}`,
              })}
              onChange={onChange("dsNbDotDieuTriId", true)}
              value={_state.dsNbDotDieuTriId}
              keySearch={"value"}
              placeholder={t("cdha.chonMa")}
              className="input-filter"
              blurReset={true}
              mode="multiple"
              haveLoading
            />
          </div>
        </Col>
        {_state.loaiNhapXuat === LOAI_NHAP_XUAT.LINH_BU_TU_TRUC &&
          _state.loaiDichVu === LOAI_DICH_VU.VAT_TU && (
            <Col md={12} xl={12} xxl={12}>
              <div className="item-checkbox">
                <Checkbox
                  style={{ flexDirection: "unset" }}
                  onChange={onChange("chayMay")}
                >
                  {t("cdha.vatTuChayMay")}
                </Checkbox>
              </div>
            </Col>
          )}
        {_state.loaiNhapXuat === LOAI_NHAP_XUAT.LINH_BU_TU_TRUC &&
          [
            LOAI_DICH_VU.VAT_TU,
            LOAI_DICH_VU.THUOC,
            LOAI_DICH_VU.VAC_XIN,
          ].includes(_state.loaiDichVu) && (
            <Col md={12} xl={12} xxl={12}>
              <div className="item-select">
                <label className="label-filter">{t("cdha.buongPt")}</label>
                <Select
                  data={listBuongPt}
                  className="input-filter"
                  onChange={onChange("buongPtTtId")}
                  defaultValue=""
                  hasAllOption={true}
                />
              </div>
            </Col>
          )}
        {[LOAI_DICH_VU.THUOC, LOAI_DICH_VU.VAC_XIN].includes(
          _state.loaiDichVu
        ) && (
          <>
            <Col md={12} xl={12} xxl={12}>
              <div className="item-select">
                <label className="label-filter">
                  {t("quanLyNoiTru.phanLoaiThuoc")}
                </label>
                <Select
                  data={listAllPhanLoaiThuoc}
                  className="input-filter"
                  onChange={onChange("dsPhanLoaiDvKhoId", true)}
                  value={_state.dsPhanLoaiDvKhoId}
                  defaultValue={_state.dsPhanLoaiDvKhoId}
                  placeholder={t("kho.nhapPhanLoaiThuoc")}
                  mode="multiple"
                />
              </div>
            </Col>
            <Col md={12} xl={12} xxl={12}>
              <div className="item-select">
                <label className="label-filter">{t("common.tenThuoc")}</label>
                <SelectLoadMore
                  api={dmDichVuKhoProvider.search}
                  mapData={(i) => ({
                    value: i.dichVu.id,
                    label: `${i.dichVu.ma} - ${i.dichVu.ten}`,
                  })}
                  onChange={onChange("dsDichVuId", true)}
                  value={_state.dsDichVuId}
                  keySearch={"timKiem"}
                  placeholder={t("cdha.nhapTenThuoc")}
                  className="select input-filter"
                  addParam={{
                    "dichVu.loaiDichVu": _state.loaiDichVu,
                    sort: "active,desc",
                    size: 10,
                    sort: "dichVu.ma,asc",
                    dsPhanLoaiDvKhoId: _state.dsPhanLoaiDvKhoId,
                  }}
                  blurReset={true}
                  mode="multiple"
                />
              </div>
            </Col>
          </>
        )}
        <Col md={12} xl={12} xxl={12}>
          <div className="item-select">
            <label className="label-filter">{t("kho.maPhieuLinh")}</label>
            <Select
              data={listAllMaPhieuLinh}
              className="input-filter"
              onChange={onChange("dsMaPhieuLinhId", true)}
              value={_state.dsMaPhieuLinhId}
              placeholder={t("kho.maPhieuLinh")}
              mode="multiple"
            />
          </div>
        </Col>
        {LINH_BU_TU_TRUC_KB_DA_THANH_TOAN?.eval() && (
          <Col md={12} xl={12} xxl={12}>
            <div className="item-date">
              <div className="item-checkbox">
                <Checkbox
                  style={{ flexDirection: "unset" }}
                  onChange={onChange("thanhToan")}
                  disabled={
                    !checkRole([ROLES["KHO"].LINH_BU_TU_TRUC_KB_DA_THANH_TOAN])
                  }
                  checked={_state.thanhToan}
                >
                  {t("kho.daThanhToan")}
                </Checkbox>
              </div>
            </div>
          </Col>
        )}
      </Row>
    );
  };

  const renderCanhBaoThuocDls = (data) => {
    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          padding: "0 16px",
        }}
      >
        <div
          style={{
            marginBottom: 8,
          }}
          dangerouslySetInnerHTML={{
            __html: DOMPurify.sanitize(
              t(
                "quanLyNoiTru.cacThuocKhangSinhDuoiDayChuaDuocDuyetDuocLamSangTiepTucTaoPhieuLinh"
              )
            ),
          }}
        />
        <TableWrapper
          columns={[
            {
              title: (
                <HeaderSearch
                  title={t("common.stt")}
                  className="flex flex-center"
                />
              ),
              width: "50px",
              dataIndex: "index",
              key: "index",
              align: "center",
              render: (_, __, index) => index + 1,
            },
            {
              title: (
                <HeaderSearch
                  title={t("danhMuc.ma")}
                  className="flex flex-center"
                />
              ),
              width: "120px",
              dataIndex: "maDichVu",
              key: "maDichVu",
            },
            {
              title: (
                <HeaderSearch
                  title={t("quanLyNoiTru.tenHamLuong")}
                  className="flex flex-center"
                />
              ),
              width: "300px",
              dataIndex: "tenDichVu",
              key: "tenDichVu",
              render: (_, record) => {
                const { tenDichVu, tenHoatChat, hamLuong } = record;
                return (
                  (tenDichVu || "") +
                  (tenHoatChat ? ` (${tenHoatChat})` : "") +
                  (hamLuong ? ` - ${hamLuong}` : "")
                );
              },
            },
            {
              title: (
                <HeaderSearch
                  title={t("kho.trangThaiDuyetDls")}
                  className="flex flex-center"
                />
              ),
              width: "120px",
              dataIndex: "trangThai",
              key: "trangThai",
              render: (item) => {
                const content = LIST_TRANG_THAI_DLS.find(
                  (trangThai) => trangThai.id == item
                )?.i18n;
                return content ? t(content) : "";
              },
            },
          ]}
          dataSource={data}
        />
      </div>
    );
  };

  const onOk = async (data) => {
    if (!data.loaiDichVu) {
      message.error("cdha.vuiLongChonLoaiHangHoa");
      return;
    }

    //85: lĩnh nội trú, 80: lĩnh bù tủ trực
    showLoading();
    try {
      const body = {
        loaiDichVu: data.loaiDichVu,
        loaiNhapXuat: data.loaiNhapXuat,
        khoId: [
          LOAI_NHAP_XUAT.CHE_PHAM_MAU,
          LOAI_NHAP_XUAT.LINH_NOI_TRU,
        ]?.includes(data.loaiNhapXuat)
          ? data.khoId
          : data.khoDoiUngId,
        ...(!data.khoId &&
        [LOAI_NHAP_XUAT.CHE_PHAM_MAU, LOAI_NHAP_XUAT.LINH_NOI_TRU]?.includes(
          data.loaiNhapXuat
        ) &&
        dataHIEN_THI_CHECKBOX_TU_TRUC?.eval()
          ? {
              dsKhoId: listThietLapChonKhoTongHop.map((x) => x.id),
              khoId: undefined,
            }
          : {}),
        khoDoiUngId: [
          LOAI_NHAP_XUAT.CHE_PHAM_MAU,
          LOAI_NHAP_XUAT.LINH_NOI_TRU,
        ]?.includes(data.loaiNhapXuat)
          ? undefined
          : data.khoId,
        tuThoiGian:
          data.tuThoiGian instanceof moment
            ? data.tuThoiGian.format("YYYY-MM-DD HH:mm:ss")
            : "",
        denThoiGian:
          data.denThoiGian instanceof moment
            ? data.denThoiGian.format("YYYY-MM-DD HH:mm:ss")
            : "",
        khoaChiDinhId: data.dsKhoaChiDinhId ? data.dsKhoaChiDinhId : undefined,
        dsPhongId:
          data.dsPhongId?.length === 0 ||
          data.dsPhongId?.length === listPhong?.length
            ? undefined
            : data.dsPhongId,
        dsNbDotDieuTriId: data.dsNbDotDieuTriId,
        chayMay: data.chayMay,
        buongPtTtId: data.buongPtTtId,
        dsDichVuId: data.dsDichVuId,
        dsPhanLoaiDvKhoId:
          data.dsPhanLoaiDvKhoId?.length > 0
            ? data.dsPhanLoaiDvKhoId
            : undefined,
        dsMaPhieuLinhId:
          data.dsMaPhieuLinhId?.length > 0 ? data.dsMaPhieuLinhId : undefined,
        ...(LINH_BU_TU_TRUC_KB_DA_THANH_TOAN?.eval()
          ? { thanhToan: data.thanhToan ? 50 : null }
          : {}),
        dsTrangThaiHoan: [0, 10, 20],
      };

      if (
        CANH_BAO_DUYET_DLS_KHI_PHAT_THUOC_TAO_PHIEU_LINH.toLowerCase() ===
          "true" &&
        !refIgnore.current
      ) {
        const res = await nbCapPhatThuocProvider.getThuocKhangSinh(body);
        if (res.data.length > 0) {
          showConfirm(
            {
              title: t("common.canhBao"),
              content: renderCanhBaoThuocDls(res.data),
              cancelText: t("common.huy"),
              okText: t("common.dongY"),
              classNameOkText: "button-warning",
              showBtnOk: true,
              typeModal: "warning",
              isContentElement: true,
              showIconContent: false,
              rightCancelButton: true,
              width: 800,
            },
            () => {
              refIgnore.current = true;
              refGetSubmit.current?.();
            }
          );
        } else {
          return taoPhieuLinhBu(body);
        }
      } else {
        return taoPhieuLinhBu(body);
      }
    } finally {
      hideLoading();
      refIgnore.current = false;
    }
  };

  const afterSubmit = (data) => {
    setTimeout(() => {
      if (window.location.pathname.indexOf("danh-sach-phieu-linh") >= 0) {
        window.location.reload();
      } else {
        let chiTiet = "danh-sach-phieu-linh";
        if (data.length == 1) {
          chiTiet = "chi-tiet-phieu-linh/" + data[0].id;
        }
        history.push(
          `/${
            isCDHA
              ? "chan-doan-hinh-anh"
              : isPttt
              ? "phau-thuat-thu-thuat"
              : "quan-ly-noi-tru"
          }/${chiTiet}`
        );
      }
    });
  };

  const onShow = () => {
    getListTuKho({});
    getListDenKho(initState);
    getListBuongTongHop({
      active: true,
      page: "",
      size: "",
      dsLoaiPhong: [LOAI_PHONG.PHONG_GIUONG, LOAI_PHONG.PHONG_GIUONG_TU_CHON],
    });
  };

  return (
    <ScreenPhieuLinh.ModalCreate
      width={600}
      title={t("quanLyNoiTru.taoPhieuLinh")}
      renderFilter={renderFilter}
      initState={initState}
      ref={ref}
      onSubmit={onOk}
      afterSubmit={afterSubmit}
      onShow={onShow}
      refGetSubmit={refGetSubmit}
    ></ScreenPhieuLinh.ModalCreate>
  );
};

export default forwardRef(ModalTaoPhieuLinh);
