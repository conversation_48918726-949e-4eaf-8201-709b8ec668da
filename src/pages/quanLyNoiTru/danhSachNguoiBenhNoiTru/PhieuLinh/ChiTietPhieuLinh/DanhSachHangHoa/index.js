import React, { memo, useRef, useState } from "react";
import { HeaderSearch, InputTimeout, TableWrapper } from "components";
import moment from "moment";
import TableEmpty from "pages/kho/components/TableEmpty";
import { useDispatch, useSelector } from "react-redux";
import { Main } from "./styled";
import ModalDanhSachNb from "../ModalDanhSachNb";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";
import { SVG } from "assets";
import { useMemo } from "react";
import { TRANG_THAI_PHIEU_NHAP_XUAT, THIET_LAP_CHUNG } from "constants/index";
import { roundToDigits } from "utils/index";
import { useConfirm, useStore, useThietLap, useWindowSize } from "hooks";
import { customSortBySttAndName } from "../../utils";
import { cloneDeep } from "lodash";
import { groupByMaThuoc } from "utils/kho-utils";

const DanhSachHangHoa = ({
  onFocusSearchHangHoa,
  isEdit,
  isModal,
  id: parentId,
  ...props
}) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const { id: paramsId } = useParams();
  const size = useWindowSize();
  const refModalDsNb = useRef();
  const refSettings = useRef(null);

  const [state, _setState] = useState({
    dataSortColumn: {},
    listCheckId: [],
    ten: "",
    selectedId: null,
  });
  const { dataSortColumn } = state;
  const setState = (data) => {
    _setState((pre) => ({ ...pre, ...data }));
  };
  const [dataCHAN_XOA_THUOC_LE_PHIEU_LINH_TRA] = useThietLap(
    THIET_LAP_CHUNG.CHAN_XOA_THUOC_LE_PHIEU_LINH_TRA,
    null
  );
  const [dataGOP_DONG_THUOC, finishGOP_DONG_THUOC] = useThietLap(
    THIET_LAP_CHUNG.GOP_DONG_THUOC
  );

  const dsSlLe = useStore("nbDvKho.dsSlLe", []);
  const dsSlLeLinhTruoc = useStore("nbDvKho.dsSlLeLinhTruoc", []);

  const { dsNhapXuatChiTiet = [], thongTinPhieu } = useSelector(
    (state) => state.phieuNhapXuat
  );

  const id = useMemo(() => {
    return parentId || paramsId;
  }, [parentId, paramsId]);

  const dsNhapXuatChiTietFilterMemo = useMemo(() => {
    let filterDs = customSortBySttAndName(dsNhapXuatChiTiet, [
      "sttPhieuLinh",
      "ten",
    ]);

    if (
      [
        TRANG_THAI_PHIEU_NHAP_XUAT.TAO_MOI,
        TRANG_THAI_PHIEU_NHAP_XUAT.TAO_MOI_DA_GIU_CHO,
      ].includes(thongTinPhieu?.trangThai)
    ) {
      filterDs = filterDs.filter((item) => item?.soLuong !== 0);
    }

    if (finishGOP_DONG_THUOC && dataGOP_DONG_THUOC?.eval()) {
      filterDs = groupByMaThuoc(cloneDeep(filterDs), "ma");
    }

    if (state.ten) {
      const searchTxt = state.ten?.trim().toLowerCase().unsignText() || "";
      filterDs = filterDs.filter(
        (x) => x.ten?.toLowerCase().unsignText().indexOf(searchTxt) >= 0
      );
    }

    return filterDs;
  }, [
    dsNhapXuatChiTiet,
    thongTinPhieu,
    state.ten,
    dataGOP_DONG_THUOC,
    finishGOP_DONG_THUOC,
  ]);

  const {
    phieuNhapXuat: { getById },
    danhSachNguoiBenhNoiTru: {
      deleteDsDvThuocPhieuLinh,
      deleteDsDvVatTuPhieuLinh,
    },
    nbDvKho: { getNbDvKho: getDsNb },
  } = useDispatch();

  const isChanXoaThuocLe = useMemo(() => {
    return (
      dataCHAN_XOA_THUOC_LE_PHIEU_LINH_TRA &&
      dataCHAN_XOA_THUOC_LE_PHIEU_LINH_TRA.toLowerCase() == "true"
    );
  }, [dataCHAN_XOA_THUOC_LE_PHIEU_LINH_TRA]);

  const dsMaHangHoaLe = useMemo(() => {
    return [
      ...new Set(
        [...(dsSlLe || []), ...(dsSlLeLinhTruoc || [])].map(
          (item) => item.maDichVu
        )
      ),
    ];
  }, [dsSlLe, dsSlLeLinhTruoc]);

  const onClickSort = (key, value) => {
    const sort = { ...dataSortColumn, [key]: value };
    setState({ dataSortColumn: sort });
  };

  const onDelete = (item, index) => (e) => {
    showConfirm(
      {
        title: t("common.thongBao"),
        content: `${t("common.banCoChacMuonXoa")}`,
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        classNameOkText: "button-warning",
        showImg: true,
        showBtnOk: true,
        typeModal: "warning",
      },
      () => {
        const apiCall =
          item.loaiDichVu === 90
            ? deleteDsDvThuocPhieuLinh
            : deleteDsDvVatTuPhieuLinh;

        apiCall({ phieuNhapXuatChiTietId: item?.id }).then((res) => {
          if (res && res.code === 0) {
            getDsNb({ phieuNhapXuatId: id, dsTrangThaiHoan: [0, 10, 20] });
            getById(id);
          }
        });
      }
    );
  };

  const onDetail = (item) => () => {
    if (refModalDsNb.current) {
      refModalDsNb.current.show({ ma: item.ma });
    }
  };

  const onSearchInput = (key) => (e) => {
    setState({ [key]: e || "" });
  };

  const onSettings = () => {
    refSettings && refSettings.current.settings();
  };

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: size.width <= 1400 ? 64 : 64,
      dataIndex: "index",
      key: "index",
      hideSearch: true,
      align: "center",
      ignore: true,
      render: (_, __, index) => index + 1,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.tenHangHoa")}
          sort_key="ten"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.ten || 0}
          search={
            <InputTimeout
              placeholder={t("quanLyNoiTru.timKiemTheoTenHoacMaHangHoa")}
              onChange={onSearchInput("ten")}
            />
          }
        />
      ),
      width: 230,
      dataIndex: "ten",
      key: "ten",
      type: true,
      hideSearch: false,
      show: true,
      i18Name: "kho.tenHangHoa",
      render: (value, item, index) => {
        return (
          <>
            <span
              className=""
              style={{
                color: "#0762F7",
                fontWeight: "bold",
                display: "inline-block",
              }}
            >
              {item?.ten}
            </span>
            <div>{item?.ghiChu}</div>
          </>
        );
      },
    },
    {
      title: <HeaderSearch title={t("common.hoatChat")} />,
      width: 200,
      dataIndex: "tenHoatChat",
      key: "hoatChat",
      hideSearch: true,
      show: true,
      i18Name: "common.hoatChat",
    },
    {
      title: (
        <HeaderSearch
          title={t("common.hamLuong")}
          sort_key="hamLuong"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.hamLuong || 0}
        />
      ),
      width: size.width <= 1400 ? 150 : 150,
      dataIndex: "hamLuong",
      key: "hamLuong",
      hideSearch: true,
      show: true,
      i18Name: "common.hamLuong",
      // render: (item) => item?.tenDonViTinh,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.sLThuCapYeuCau")}
          sort_key="soLuongYeuCau"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.soLuongYeuCau || 0}
        />
      ),
      key: "soLuongYeuCau",
      width: 90,
      dataIndex: "soLuongYeuCau",
      hideSearch: true,
      align: "right",
      show: true,
      i18Name: "kho.sLThuCapYeuCau",
      render: (item, data) =>
        `${item} ${data.tenDonViTinh ? data.tenDonViTinh : ""}`,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.sLsoCapYeuCau")}
          sort_key="soLuongSoCapYeuCau"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.soLuongSoCapYeuCau || 0}
        />
      ),
      key: "soLuongSoCapYeuCau",
      width: size.width <= 1400 ? 83 : 83,
      dataIndex: "soLuongSoCapYeuCau",
      hideSearch: true,
      align: "right",
      show: true,
      i18Name: "kho.sLsoCapYeuCau",
      render: (item, data) =>
        `${item ? roundToDigits(item, 3) : ""} ${
          data.tenDvtSoCap ? data.tenDvtSoCap : ""
        }`,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.sLSoCapDuyet")}
          sort_key="soLuongSoCap"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.soLuong || 0}
        />
      ),
      key: "soLuongSoCap",
      width: size.width <= 1400 ? 83 : 83,
      dataIndex: "soLuongSoCap",
      hideSearch: true,
      align: "right",
      show: true,
      i18Name: "kho.sLSoCapDuyet",
      render: (item, data) =>
        `${item ? roundToDigits(item, 3) : ""} ${
          data.tenDvtSoCap ? data.tenDvtSoCap : ""
        }`,
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.slHuy")}
          sort_key="soLuongHuy"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.soLuongHuy || 0}
        />
      ),
      key: "soLuongHuy",
      width: size.width <= 1400 ? 83 : 83,
      dataIndex: "soLuongHuy",
      hideSearch: true,
      align: "right",
      show: true,
      i18Name: "quanLyNoiTru.slHuy",
      render: (item, data) =>
        `${parseFloat(item?.toPrecision(12))} ${data.tenDonViTinh ? data.tenDonViTinh : ""}`,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.soLo")}
          sort_key="soLo"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.soLo || 0}
        />
      ),
      width: size.width <= 1400 ? 150 : 150,
      dataIndex: "loNhap",
      key: "soLo",
      hideSearch: true,
      align: "center",
      show: true,
      i18Name: "kho.soLo",
      render: (item) => item?.soLo,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.hanSuDung")}
          sort_key="loNhap.ngayHanSuDung"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["loNhap.ngayHanSuDung"] || 0}
        />
      ),
      width: size.width <= 1400 ? 150 : 150,
      dataIndex: "loNhap",
      key: "ngayHanSuDung",
      hideSearch: true,
      show: true,
      i18Name: "kho.hanSuDung",
      render: (value, _, __) =>
        value?.ngayHanSuDung
          ? moment(value?.ngayHanSuDung)?.format("DD/MM/YYYY")
          : "",
    },
    {
      title: <HeaderSearch title={t("danhMuc.giaBaoHiem")} />,
      width: 100,
      dataIndex: "giaBaoHiem",
      key: "giaBaoHiem",
      i18Name: "danhMuc.giaBaoHiem",
      hideSearch: true,
      show: true,
      align: "right",
      render: (item, record) =>
        record?.loNhap?.giaBaoHiem && record?.loNhap?.giaBaoHiem.formatPrice(),
    },
    {
      title: <HeaderSearch title={t("danhMuc.giaKhongBaoHiem")} />,
      width: 100,
      dataIndex: "giaKhongBaoHiem",
      key: "giaKhongBaoHiem",
      i18Name: "danhMuc.giaKhongBaoHiem",
      hideSearch: true,
      show: true,
      align: "right",
      render: (item, record) =>
        record?.loNhap?.giaKhongBaoHiem &&
        record?.loNhap?.giaKhongBaoHiem.formatPrice(),
    },
    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("common.tienIch")}{" "}
              <SVG.IcSetting
                onClick={onSettings}
                style={{ cursor: "pointer" }}
              />
            </>
          }
        />
      ),
      key: "",
      width: 80,
      dataIndex: "",
      hideSearch: true,
      align: "right",
      fixed: "right",
      ignore: true,
      render: (_, item, index) => {
        const hideDeleteBtn =
          isChanXoaThuocLe && dsMaHangHoaLe.includes(item.ma);

        return (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-around",
            }}
          >
            {/* <img style={{ cursor: "pointer" }} src={IcDelete} /> */}
            <SVG.IcEye onClick={onDetail(item)} />
            {![20, 30].includes(thongTinPhieu?.trangThai) &&
              !isModal &&
              !hideDeleteBtn && (
                <SVG.IcDelete onClick={onDelete(item, index)} />
              )}
          </div>
        );
      },
    },
  ];

  const onRow = (record, index) => {
    return {
      onClick: (event) => {
        setState({ selectedId: record.id });
      },
    };
  };

  return (
    <Main className="main">
      <TableWrapper
        rowKey={(item) => item.id}
        columns={columns}
        onRow={onRow}
        dataSource={dsNhapXuatChiTietFilterMemo}
        locale={{
          emptyText: (
            <TableEmpty
              onClickButton={onFocusSearchHangHoa}
              showButton={isEdit}
            />
          ),
        }}
        styleWrap={{ height: "100%" }}
        ref={refSettings}
        tableName="table_QuanLyNoiTru_HangHoaLinh"
        rowClassName={(record) =>
          record.id == state.selectedId ? "row-selected-detail" : ""
        }
        columnResizable={true}
      />

      <ModalDanhSachNb
        isModal={isModal}
        thongTinPhieu={thongTinPhieu}
        ref={refModalDsNb}
        id={parentId}
      />
    </Main>
  );
};

export default memo(DanhSachHangHoa);
