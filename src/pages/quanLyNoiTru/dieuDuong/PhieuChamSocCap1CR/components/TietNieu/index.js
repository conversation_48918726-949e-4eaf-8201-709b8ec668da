import { Col, Row } from 'antd'
import React from 'react'
import { DateTimePicker, InputTimeout } from 'components'
import { get } from 'lodash'
import MultipleChoice from '../common/MultipleChoice'
import SingleChoice from '../common/SingleChoice'
import FieldItem from '../common/FieldItem'
import { Main } from './styled'
import moment from 'moment'


const DANH_SACH_MAU_SAC_NUOC_TIEU = [
    { id: 1, ten: "Vàng nhạt" },
    { id: 2, ten: "Vàng sậm" },
    { id: 3, ten: "Xá xị" },
    { id: 4, ten: "Xanh" },
    { id: 5, ten: "Đỏ nhạt" },
    { id: 6, ten: "Đỏ tươi" },
]
const DANH_SACH_TINH_CHAT_NUOC_TIEU = [
    { id: 1, ten: "Trong" },
    { id: 2, ten: "Đục" },
    { id: 3, ten: "<PERSON>ợn cợn" },
]
const DANH_SACH_TINH_TRANG_TIEU = [
    { id: 1, ten: "<PERSON><PERSON><PERSON> thường" },
    { id: 2, ten: "Bí tiểu" },
    { id: 3, ten: "Tiểu rát/buốt" },
    { id: 4, ten: "Tiểu lắt nhắt" },
]

const DANH_SACH_BAI_TIET = [
    { id: 1, ten: "Tiểu tự chủ" },
    { id: 2, ten: "Tiểu không tự chủ" },
    { id: 3, ten: "Tiểu qua ống thông niệu đạo" },
]
const TINH_TRANG_ONG_THONG = [
    { id: 1, ten: "Thông tốt" },
    { id: 2, ten: "Bán tắc" },
    { id: 3, ten: "Tắc" },
]


const TietNieu = ({ onChange, data, readOnly, ...props }) => {

    return (
        <Main>
            <Row gutter={[16, 16]} className="toan-than">
                <Col span={24}>
                    <FieldItem title={"Bài tiết"} direction={2} titleMinWidth={150}>
                        <SingleChoice
                            radioWidth={150}
                            type="radio"
                            disabled={readOnly}
                            value={get(data, "baiTiet")}
                            outputTypeArray={true}
                            data={DANH_SACH_BAI_TIET} onChange={e => {
                                onChange(["baiTiet"], e);
                            }}></SingleChoice>
                    </FieldItem>

                    <FieldItem title={"Ống thông"} direction={2} align='flex-start' titleMinWidth={150}>
                        <FieldItem title="Tình trạng" direction={2} top={0}>
                            <SingleChoice
                                type="radio"
                                disabled={readOnly}
                                outputTypeArray={true}
                                value={get(data, "tinhTrangOngThong")}
                                data={TINH_TRANG_ONG_THONG} onChange={e => {
                                    onChange(["tinhTrangOngThong"], e);
                                }}></SingleChoice>
                        </FieldItem>
                        <FieldItem title={"Ngày đặt"} direction={2} width={250}>
                            <DateTimePicker
                                disabled={readOnly}
                                value={get(data, `ngayDatOngThongTietNieu`) ? moment(get(data, `ngayDatOngThongTietNieu`)) : null}
                                onChange={e => { onChange(["ngayDatOngThongTietNieu"], e?.format("YYYY-MM-DD") || "") }}
                                placeholder={"DD/MM/YYYY"}
                                format="DD/MM/YYYY"
                                className="input-filter"
                            />
                        </FieldItem>
                        <FieldItem title={"Ngày rút"} direction={2} width={250}>
                            <DateTimePicker
                                disabled={readOnly}
                                value={get(data, `ngayRutOngThongTietNieu`) ? moment(get(data, `ngayRutOngThongTietNieu`)) : null}
                                onChange={e => { onChange(["ngayRutOngThongTietNieu"], e?.format("YYYY-MM-DD") || "") }}
                                placeholder={"DD/MM/YYYY"}
                                format="DD/MM/YYYY"
                                className="input-filter"
                            />
                        </FieldItem>

                    </FieldItem>
                    <FieldItem title={"Số lượng"} direction={2} width={300} titleMinWidth={150}>
                        <div className='flex flex-a-center gap-5'>
                            <InputTimeout
                                disabled={readOnly}
                                className="flex1"
                                value={get(data, "soLuongNuocTieu")}
                                onChange={e => {
                                    onChange(["soLuongNuocTieu"], e);
                                }}></InputTimeout>
                            <span>(...ml/...giờ)</span>
                        </div>
                    </FieldItem>
                    <FieldItem title={"Màu sắc nước tiểu"} direction={2} titleMinWidth={150} align='flex-start'>
                        <SingleChoice
                            type="radio"
                            radioWidth={110}
                            outputTypeArray={true}
                            disabled={readOnly}
                            className="flex1"
                            value={get(data, "mauSacNuocTieu")}
                            data={DANH_SACH_MAU_SAC_NUOC_TIEU} onChange={e => {
                                onChange(["mauSacNuocTieu"], e);
                            }}></SingleChoice>
                    </FieldItem>
                    <FieldItem title={"Tính chất"} direction={2} titleMinWidth={150}>
                        <MultipleChoice
                            type="checkbox"
                            radioWidth={110}
                            disabled={readOnly}
                            className="flex1"
                            value={get(data, "tinhChatNuocTieu")}
                            data={DANH_SACH_TINH_CHAT_NUOC_TIEU} onChange={e => {
                                onChange(["tinhChatNuocTieu"], e);
                            }}></MultipleChoice>
                    </FieldItem>
                    <FieldItem title={"Tình trạng tiểu"} direction={2} titleMinWidth={150}>
                        <MultipleChoice
                            type="checkbox"
                            checkboxWidth={110}
                            disabled={readOnly}
                            className="flex1"
                            value={get(data, "tinhTrangNuocTieu")}
                            data={DANH_SACH_TINH_TRANG_TIEU} onChange={e => {
                                onChange(["tinhTrangNuocTieu"], e);
                            }}></MultipleChoice>
                    </FieldItem>
                    <FieldItem title={"Khác"} direction={2} titleMinWidth={150}>
                        <InputTimeout
                            disabled={readOnly}
                            className="flex1"
                            value={get(data, "tietNieuKhac")}
                            onChange={e => {
                                onChange(["tietNieuKhac"], e);
                            }}></InputTimeout>
                    </FieldItem>
                </Col>
            </Row>
        </Main >
    )
}
export default TietNieu;