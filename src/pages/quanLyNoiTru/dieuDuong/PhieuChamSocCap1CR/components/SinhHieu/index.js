import { Col, Row } from 'antd'
import React from 'react'
import { InputTimeout } from 'components'
import { get } from 'lodash'
import FieldItem from '../common/FieldItem'
import { Main } from './styled'

const SinhHieu = ({ onChange, data, readOnly, phieuCap1, ...props }) => {
    const fields = [
        {
            key: "huyetApTamThu",
            type: "number",
            label: "Huyết áp tâm thu",
            keyData: "chiSoSong.huyetApTamThu",
            min: get(data, "chiSoSong.huyetApTamTruong"),
            maxLength: 9,
            donVi: "mmHg",
            onChange: (e) => {
                onChange(["chiSoSong", "huyetApTamThu"], e);
            }
        },
        {
            key: "huyetApTamTruong",
            type: "number",
            label: "Huyết áp tâm trương",
            keyData: "chiSoSong.huyetApTamTruong",
            min: 0,
            max: get(data, "chiSoSong.huyetApTamThu"),
            donVi: "mmHg",
            onChange: (e) => {
                onChange(["chiSoSong", "huyetApTamTruong"], e);
            }

        },
        {
            key: "mach",
            type: "number",
            label: "Mạch",
            keyData: "chiSoSong.mach",
            min: 0,
            maxLength: 9,
            controls: false,
            donVi: "lần/phút",
            onChange: (e) => {
                onChange(["chiSoSong", "mach"], e);
            }
        },
        {
            key: "nhietDo",
            type: "number",
            label: "Nhiệt độ",
            keyData: "chiSoSong.nhietDo",
            min: 0,
            maxLength: 9,
            controls: false,
            donVi: "ºC",
            onChange: (e) => {
                onChange(["chiSoSong", "nhietDo"], e);
            }
        },
        {
            key: "spo2",
            type: "number",
            label: "SPO2",
            keyData: "chiSoSong.spo2",
            min: 0,
            maxLength: 9,
            controls: false,
            donVi: "%",
            onChange: (e) => {
                onChange(["chiSoSong", "spo2"], e);
            }

        },
        {
            key: "canNang",
            type: "number",
            label: "Cân nặng",
            keyData: "chiSoSong.canNang",
            donVi: "kg",
            min: 0,
            maxLength: 9,
            controls: false,
            onChange: (e) => {
                onChange?.(["chiSoSong", "canNang"], e);
            }
        },
        ...(phieuCap1 ? [] : [
            {
                key: "chieuCao",
                type: "number",
                label: "Chiều cao",
                keyData: "chiSoSong.chieuCao",
                donVi: "cm",
                min: 0,
                onChange: (e) => {
                    onChange?.(["chiSoSong", "chieuCao"], e);
                }
            }]),
        {
            key: "nhipTho",
            type: "number",
            label: "Nhịp thở",
            keyData: phieuCap1 ? "nhipTho" : "chiSoSong.nhipTho",
            min: 0,
            maxLength: 9,
            controls: false,
            donVi: "lần/phút",
            onChange: (e) => {
                onChange?.(phieuCap1 ? ["nhipTho"] : ["chiSoSong", "nhipTho"], e);
            }

        },
        ...(!phieuCap1 ? [{
            key: "vongEo",
            type: "number",
            label: "Chỉ số vòng eo",
            keyData: "ve",
            min: 0,
            donVi: "cm",
            onChange: (e) => {
                onChange?.(["ve"], e);
            }

        }, {
            key: "vongHong",
            type: "number",
            label: "Chỉ số vòng hông",
            keyData: "vh",
            min: 0,
            donVi: "cm",
            onChange: (e) => {
                onChange?.(["vh"], e);
            }
        }] : [])
    ]
    const renderComponent = item => {
        switch (item.type) {
            case "number":
                return <InputTimeout
                    readOnly={readOnly}
                    disabled={readOnly}
                    type={"number"}
                    className="flex1"
                    min={item.min}
                    maxLength={item.maxLength}
                    max={item.max}
                    controls={item.controls}
                    parser={(value) => value?.replace(',', '.')}
                    value={get(data, item.keyData)} onChange={item.onChange} />
            default: return null;
        }
    }
    return (
        <Main>
            <Row gutter={[8, 8]}>
                {
                    fields?.map(item => {
                        return <Col key={item.key} span={24} className="flex gap-4 flex-a-center">
                            <FieldItem title={item.label}>
                                <div className='flex flex-a-center gap-4'>
                                    {renderComponent(item)}
                                    {item.donVi}
                                </div>
                            </FieldItem>
                        </Col>
                    })
                }
            </Row>
        </Main>
    )
}
export default SinhHieu;