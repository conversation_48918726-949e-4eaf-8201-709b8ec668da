import React from 'react'
import { Radio, Select } from 'components';
import { isArray } from 'lodash';
import { Main } from './styled';
import { SVG } from 'assets';

export default function index({ type, data, value, defaultValue, onChange, className, outputTypeArray = false, disabled, radioWidth }) {
    if (value === undefined) {
        value = defaultValue;
    }
    const newValue = outputTypeArray ? isArray(value) ? value[0] : value : value; // Handle single value or array
    const onChangeHandler = (e) => {
        let value = e;
        if (type == "radio")
            value = e.target.value;
        if (outputTypeArray) {
            onChange((value !== null || value !== undefined) ? [value] : null); // Convert to array if needed
        } else {
            onChange(value); // Use single value directly
        }
    };
    const onRemove = () => {
        onChange(null);
    }
    return <Main className={`${className || ""} single-choice`} radioWidth={radioWidth}>
        {
            type == "radio" ?
                <>
                    <Radio.Group
                        disabled={disabled} onChange={onChangeHandler} value={newValue} className="group-radio">
                        {(data || []).map(item => {
                            return <Radio key={item.id} value={item.id}>{item.ten}</Radio>
                        })}
                    </Radio.Group>
                    {
                        (newValue !== null && newValue !== undefined) && !disabled &&
                        <SVG.IcCloseCircle className="ic-clear" onClick={onRemove} />
                    }
                </> :
                <Select
                    disabled={disabled}
                    value={newValue}
                    data={data} onChange={onChangeHandler}
                    hasAllOption={false}>
                </Select>
        }
    </Main>
}
