import styled from "styled-components";

export const Main = styled.div`
    margin-bottom: 20px;
    & .group-checkbox{
      display: flex;
      flex-direction: column;
      & .ant-radio-group{
        ${props => props.radioWidth ? `> label { min-width: ${props.radioWidth || 100}px; }` : ``}
      }
      & svg.ic-clear{
        cursor: pointer;
        min-width: 50px;
        & path{
          fill: red;
        }
      }
    }
    & .list-checkbox{
      margin-top: 10px;
      & > label{
        min-width: ${props => props.checkboxWidth || 150}px;
      }
    }
  
`;
