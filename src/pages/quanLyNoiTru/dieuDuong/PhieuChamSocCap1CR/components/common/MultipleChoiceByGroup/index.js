import { Checkbox } from 'antd';
import { Main } from './styled';
import { Radio, Select } from 'components';
import { get, isArray } from 'lodash';
import React, { forwardRef, useImperativeHandle } from 'react'
import { useMemo } from 'react';
import { useEffect } from 'react';
import { useState } from 'react';
import { SVG } from 'assets';

const MultipleChoiceByGroup = forwardRef(({ type, label, data, value, onChange, dataGroups,
    className, disabled, checkboxStyle,
    radioWidth,
    checkboxWidth,
    ...props }, ref) => {
    const [state, _setState] = useState({ groupValue: null });
    const setState = data => {
        _setState(state => {
            return {
                ...state,
                ...data
            }
        })
    }

    useImperativeHandle(ref, () => ({
        changeGroup: groupValue => {
            setState({ groupValue: groupValue });
        },
        getGroupValue: () => {
            return state.groupValue;
        }
    }));

    const onChange1 = (e, groupValue) => {
        if (isArray(dataGroups) && dataGroups.length > 1) {
            const groups2 = dataGroups.map(item => isArray(item.values) ? item.values : [item.values]);
            const group = !!e ? groups2.find(group => group.includes(e[e.length - 1])) : null;
            if (type == "groupCheckbox") {
                if (!group)
                    onChange(e, groupValue || group);
                else
                    onChange(e.filter(item => group.includes(item)), groupValue || group)
            }
            else if (type == "groupRadio") {
                onChange(e?.target?.value || null, groupValue || group);
            }
        }
    };
    const onChangeGroup = e => {
        setState({ groupValue: e.target.value })
        const listItems = dataGroups.find(item => item.name == e.target.value)?.values;
        if (listItems.length == 1) {
            if (type == "groupCheckbox")
                onChange1(listItems, listItems);
            else
                onChange1(listItems[0], listItems);
        }
        else {
            if (type == "groupCheckbox")
                onChange1([], listItems);
            else
                onChange1(null, listItems);
        }
    }
    useEffect(() => {
        if (type == "groupCheckbox") {
            const newValue = isArray(value) ? value : value ? [value] : [];
            const group = (dataGroups || []).find(group => group.values?.some(item2 => newValue.includes(item2)))?.name;
            if (group)
                setState({ groupValue: group });
        } else
            if (type == "groupRadio") {
                const newValue = value;
                const group = (dataGroups || []).find(group => group.values?.some(item2 => item2 == newValue))?.name;
                if (group)
                    setState({ groupValue: group });
            }
    }, [value, dataGroups, type])

    const currentGroup = useMemo(() => {
        return dataGroups?.find(item => item.name == state.groupValue);
    }, [state.groupValue])

    const onClear = () => {
        setState({ groupValue: null })
        onChange1(null, null);
    }

    return <Main radioWidth={radioWidth} checkboxWidth={checkboxWidth}>
        {
            type == "groupCheckbox" ?
                <div className='group-checkbox'>
                    <div className='flex flex-a-center'>
                        <Radio.Group disabled={disabled} value={state.groupValue} onChange={onChangeGroup}>
                            {
                                dataGroups.map((item, index) => {
                                    return <Radio key={index} value={item.name} style={{ color: item.color || "initial" }}>
                                        {item.name}
                                    </Radio>
                                })
                            }
                        </ Radio.Group>
                        {
                            state.groupValue && !disabled &&
                            <SVG.IcCloseCircle onClick={onClear} className="ic-clear" />
                        }
                    </div>
                    {
                        (currentGroup?.values?.length > 1) && <Checkbox.Group
                            className='list-checkbox'
                            disabled={disabled}
                            style={checkboxStyle}
                            value={value}
                            options={currentGroup?.values?.map(item2 => ({
                                value: item2, label: data.find(item3 => item3.id == item2)?.ten
                            }))}
                            onChange={onChange1} />
                    }
                </div> :
                type == "groupRadio" ?
                    <div className='group-checkbox'>
                        <Radio.Group disabled={disabled} value={state.groupValue} onChange={onChangeGroup}>
                            {
                                dataGroups.map((item, index) => {
                                    return <Radio key={index} value={item.name} style={{ color: item.color || "initial" }}>
                                        {item.name}
                                    </Radio>
                                })
                            }
                        </ Radio.Group>
                        {
                            (currentGroup?.values?.length > 1) &&
                            <Radio.Group disabled={disabled} value={value} onChange={onChange1}>
                                {
                                    currentGroup?.values.map((item, index) => {
                                        return <Radio key={index} value={item + ""} style={{ color: item.color || "initial" }}>
                                            {data?.find(item2 => item2.id == item)?.ten}
                                        </Radio>
                                    })
                                }
                            </ Radio.Group>
                        }
                    </div>
                    :
                    <Select
                        hasAllOption={false}
                        disabled={disabled}
                        className={`${className || ""}`}
                        value={value}
                        mode={"multiple"} data={data} onChange={onChange1}></Select>
        }
    </Main>
});
export default MultipleChoiceByGroup;
