import { Col, Row } from 'antd'
import React from 'react'
import { But<PERSON>, DateTimePicker, InputTimeout } from 'components'
import { get, isArray } from 'lodash'
import SingleChoice from '../common/SingleChoice'
import MultipleChoice from '../common/MultipleChoice'
import FieldItem from '../common/FieldItem'
import { Main } from './styled'
import { SVG } from 'assets'
import moment from 'moment'
import { detectMob } from 'utils'

const DATA_KHONG_CO = [
    { id: 1, ten: "Không" },
    { id: 2, ten: "Có" },
];

const VI_TRI_ONG_THONG_TM_TRUNG_TAM = [
    { id: 1, ten: "Cảnh trong phải" },
    { id: 2, ten: "Cảnh trong trái" },
    { id: 3, ten: "Dưới đòn phải" },
    { id: 4, ten: "Dưới đòn trái" },
    { id: 5, ten: "Đùi trái" },
    { id: 6, ten: "<PERSON>ùi phải" },
]

const VI_TRI_ONG_THONG_DONG_MACH = [
    { id: 1, ten: "Quay phải" },
    { id: 2, ten: "Quay trái" },
    { id: 3, ten: "Đùi phải" },
    { id: 4, ten: "Đùi trái" },
]
const VI_TRI_ONG_THONG_TM_NGOAI_VI = [
    { id: "1-a", ten: "Cánh tay phải" },
    { id: "1-b", ten: "Cánh tay trái" },
    { id: "2-a", ten: "Cẳng tay phải" },
    { id: "2-b", ten: "Cẳng tay trái" },
    { id: "3-a", ten: "Mu bàn tay phải" },
    { id: "3-b", ten: "Mu bàn tay trái" },
    { id: "4", ten: "Đầu" },
    { id: "5-a", ten: "Cổ tay phải" },
    { id: "5-b", ten: "Cổ tay trái" },
    { id: "6-a", ten: "Cẳng chân phải" },
    { id: "6-b", ten: "Cẳng chân trái" },
    { id: "7-a", ten: "Mu bàn chân phải" },
    { id: "7-b", ten: "Mu bàn chân trái" },
    { id: "8-a", ten: "Cổ phải" },
    { id: "8-b", ten: "Cổ trái" },
]

const TINH_TRANG_CHAN_ONG_THONG = [
    { id: 1, ten: "Khô sạch" },
    { id: 2, ten: "Viêm đỏ" },
    { id: 3, ten: "Tiết dịch" },
]

const VIP_SCORE = [
    { id: 1, ten: "(0) Cấp 0" },
    { id: 2, ten: "(1) Cấp 1" },
    { id: 3, ten: "(2) Cấp 2" },
    { id: 4, ten: "(3) Cấp 3" },
    { id: 5, ten: "(4) Cấp 4" },
    { id: 6, ten: "(5) Cấp 5" },
]

const TINH_TRANG_HOAT_DONG = [
    { id: 1, ten: "Thông tốt" },
    { id: 2, ten: "Bán tắc" },
    { id: 3, ten: "Tắc" },
]

const OngThong = ({ onChange, data, readOnly, ...props }) => {

    const onAdd = (type) => () => {
        onChange([`${type}[${data[type]?.length || 0}]`], {});
    }

    const onDelete = (type, index) => () => {
        const dataOngThong = data[type];
        dataOngThong?.splice(index, 1);
        onChange([type], dataOngThong);
    }

    const isMobile = detectMob();

    return (
        <Main>
            <FieldItem title="Ống thông TM trung tâm" rightTitle={!readOnly &&
                <Button
                    type="primary" rightIcon={<SVG.IcAdd />} onClick={onAdd("ongThongTmTrungTam")}>Thêm mới</Button>
            } maxWidth={isMobile ? 800 : 0}>
                <Row gutter={[8, 8]} className="toan-than">

                    {(data.ongThongTmTrungTam || [{}])?.map((item, index) => (
                        <Col xl={isMobile ? 24 : 12} md={24} xs={24} key={index}>
                            <FieldItem maxWidth={800} title={"Ống thông TM trung tâm số " + (index + 1)} border={true}
                                rightTitle={!readOnly && <SVG.IcDelete onClick={onDelete("ongThongTmTrungTam", index)} />}>
                                <SingleChoice
                                    type="radio"
                                    disabled={readOnly}
                                    className="flex1"
                                    outputTypeArray={true}
                                    value={get(data, `ongThongTmTrungTam[${index}].ongThong`)}
                                    data={DATA_KHONG_CO} onChange={e => {
                                        onChange([`ongThongTmTrungTam[${index}]`, "ongThong"], e);
                                    }}></SingleChoice>
                                {
                                    isArray(get(data, `ongThongTmTrungTam[${index}].ongThong`)) && get(data, `ongThongTmTrungTam[${index}].ongThong`).includes(2) &&
                                    <Row gutter={[8, 8]} className="toan-than" key={index}>
                                        <Col span={12}>
                                            <FieldItem title={"Vị trí"}>
                                                <SingleChoice
                                                    type="radio"
                                                    disabled={readOnly}
                                                    className="vi-tri-tmnv"
                                                    outputTypeArray={true}
                                                    value={get(data, `ongThongTmTrungTam[${index}].viTri`)}
                                                    data={VI_TRI_ONG_THONG_TM_TRUNG_TAM} onChange={e => {
                                                        onChange([`ongThongTmTrungTam[${index}]`, "viTri"], e);
                                                    }}></SingleChoice>
                                            </FieldItem>
                                        </Col>
                                        <Col span={12}>
                                            <FieldItem title={"Vị trí khác"}>
                                                <InputTimeout
                                                    disabled={readOnly}
                                                    className="flex1"
                                                    value={get(data, `ongThongTmTrungTam[${index}].viTriKhac`)}
                                                    onChange={e => {
                                                        onChange([`ongThongTmTrungTam[${index}]`, "viTriKhac"], e);
                                                    }}></InputTimeout>
                                            </FieldItem>
                                        </Col>
                                        <Col span={12}>
                                            <FieldItem title={"Tình trạng hoạt động"}>
                                                <SingleChoice
                                                    type="radio"
                                                    disabled={readOnly}
                                                    className="flex1"
                                                    outputTypeArray={true}
                                                    value={get(data, `ongThongTmTrungTam[${index}].tinhTrangHoatDong`)}
                                                    data={TINH_TRANG_HOAT_DONG} onChange={e => {
                                                        onChange([`ongThongTmTrungTam[${index}]`, "tinhTrangHoatDong"], e);
                                                    }}></SingleChoice>
                                            </FieldItem>
                                        </Col>
                                        <Col span={12}>
                                            <FieldItem title={"Tình trạng chân ống thông"}>
                                                <MultipleChoice
                                                    type="checkbox"
                                                    className="flex1"
                                                    value={get(data, `ongThongTmTrungTam[${index}].tinhTrangHoatDongDm`)}
                                                    data={TINH_TRANG_CHAN_ONG_THONG} onChange={e => {
                                                        onChange([`ongThongTmTrungTam[${index}]`, "tinhTrangHoatDongDm"], e);
                                                    }}></MultipleChoice>
                                            </FieldItem>
                                        </Col>
                                        <Col span={12}>
                                            <FieldItem title={"Ngày đặt"}>
                                                <DateTimePicker
                                                    disabled={readOnly}
                                                    value={get(data, `ongThongTmTrungTam[${index}].ngayDat`) ? moment(get(data, `ongThongTmTrungTam[${index}].ngayDat`)) : null}
                                                    onChange={e => { onChange([`ongThongTmTrungTam[${index}]`, "ngayDat"], e?.format("YYYY-MM-DD") || "") }}
                                                    placeholder={"DD/MM/YYYY"}
                                                    format="DD/MM/YYYY"
                                                    className="input-filter"
                                                />
                                            </FieldItem>
                                        </Col>
                                        <Col span={12}>
                                            <FieldItem title={"Ngày rút"}>
                                                <DateTimePicker
                                                    disabled={readOnly}
                                                    value={get(data, `ongThongTmTrungTam[${index}].ngayRut`) ? moment(get(data, `ongThongTmTrungTam[${index}].ngayRut`)) : null}
                                                    onChange={e => { onChange([`ongThongTmTrungTam[${index}]`, "ngayRut"], e?.format("YYYY-MM-DD") || "") }}
                                                    placeholder={"DD/MM/YYYY"}
                                                    format="DD/MM/YYYY"
                                                    className="input-filter"
                                                />
                                            </FieldItem>
                                        </Col>
                                        <Col span={12}>
                                            <FieldItem title={"Khác"}>
                                                <InputTimeout
                                                    disabled={readOnly}
                                                    className="flex1"
                                                    value={get(data, `ongThongTmTrungTam[${index}].ongThongTmKhac`)}
                                                    onChange={e => {
                                                        onChange([`ongThongTmTrungTam[${index}]`, "ongThongTmKhac"], e);
                                                    }}
                                                >
                                                </InputTimeout>
                                            </FieldItem>
                                        </Col>
                                    </Row>
                                }
                            </FieldItem>
                        </Col>
                    ))}
                </Row>
            </FieldItem>

            <FieldItem title="Ống thông Động mạch" rightTitle={!readOnly &&
                <Button
                    type="primary" rightIcon={<SVG.IcAdd />} onClick={onAdd("ongThongDongMach")}>Thêm mới</Button>
            } maxWidth={isMobile ? 800 : 0}>
                <Row gutter={[8, 8]} className="toan-than">

                    {
                        (data.ongThongDongMach || [{}])?.map((item, index) => (
                            <Col xl={isMobile ? 24 : 12} md={24} xs={24} key={index}>
                                <FieldItem maxWidth={800} title={"Ống thông Động mạch số " + (index + 1)} border={true}
                                    rightTitle={!readOnly && <SVG.IcDelete onClick={onDelete("ongThongDongMach", index)} />}>

                                    <SingleChoice
                                        disabled={readOnly}
                                        className="flex1"
                                        type="radio"
                                        outputTypeArray={true}
                                        value={get(data, `ongThongDongMach[${index}].ongThong`)}
                                        data={DATA_KHONG_CO} onChange={e => {
                                            onChange([`ongThongDongMach[${index}]`, "ongThong"], e);
                                        }}></SingleChoice>
                                    {
                                        isArray(get(data, `ongThongDongMach[${index}].ongThong`)) && get(data, `ongThongDongMach[${index}].ongThong`).includes(2) &&
                                        <Row gutter={[8, 8]} className="toan-than" key={index}>
                                            <Col span={12}>

                                                <FieldItem title={"Vị trí"}>
                                                    <SingleChoice
                                                        type="radio"
                                                        disabled={readOnly}
                                                        className="flex1"
                                                        outputTypeArray={true}
                                                        value={get(data, `ongThongDongMach[${index}].viTri`)}
                                                        data={VI_TRI_ONG_THONG_DONG_MACH} onChange={e => {
                                                            onChange([`ongThongDongMach[${index}]`, "viTri"], e);
                                                        }}></SingleChoice>
                                                </FieldItem>
                                            </Col>
                                            <Col span={12}>
                                                <FieldItem title={"Vị trí khác"}>
                                                    <InputTimeout
                                                        disabled={readOnly}
                                                        className="flex1"
                                                        value={get(data, `ongThongDongMach[${index}].viTriKhac`)}
                                                        onChange={e => {
                                                            onChange([`ongThongDongMach[${index}]`, "viTriKhac"], e);
                                                        }}></InputTimeout>
                                                </FieldItem>
                                            </Col>

                                            <Col span={12}>
                                                <FieldItem title={"Tình trạng hoạt động"}>
                                                    <SingleChoice
                                                        type="radio"
                                                        disabled={readOnly}
                                                        className="flex1"
                                                        outputTypeArray={true}
                                                        value={get(data, `ongThongDongMach[${index}].tinhTrangHoatDong`)}
                                                        data={TINH_TRANG_HOAT_DONG} onChange={e => {
                                                            onChange([`ongThongDongMach[${index}]`, "tinhTrangHoatDong"], e);
                                                        }}></SingleChoice>
                                                </FieldItem>
                                            </Col>
                                            <Col span={12}>

                                                <FieldItem title={"Tình trạng chân ống thông"}>
                                                    <MultipleChoice
                                                        type="checkbox"
                                                        disabled={readOnly}
                                                        className="flex1"
                                                        value={get(data, `ongThongDongMach[${index}].tinhTrangHoatDongDm`)}
                                                        data={TINH_TRANG_CHAN_ONG_THONG} onChange={e => {
                                                            onChange([`ongThongDongMach[${index}]`, "tinhTrangHoatDongDm"], e);
                                                        }}></MultipleChoice>
                                                </FieldItem>
                                            </Col>
                                            <Col span={12}>
                                                <FieldItem title={"Ngày đặt"}>
                                                    <DateTimePicker
                                                        disabled={readOnly}
                                                        value={get(data, `ongThongDongMach[${index}].ngayDat`) ? moment(get(data, `ongThongDongMach[${index}].ngayDat`)) : null}
                                                        onChange={e => { onChange([`ongThongDongMach[${index}]`, "ngayDat"], e?.format("YYYY-MM-DD") || "") }}
                                                        placeholder={"DD/MM/YYYY"}
                                                        format="DD/MM/YYYY"
                                                        className="input-filter"
                                                    />
                                                </FieldItem>
                                            </Col>
                                            <Col span={12}>
                                                <FieldItem title={"Ngày rút"}>
                                                    <DateTimePicker
                                                        disabled={readOnly}
                                                        value={get(data, `ongThongDongMach[${index}].ngayRut`) ? moment(get(data, `ongThongDongMach[${index}].ngayRut`)) : null}
                                                        onChange={e => { onChange([`ongThongDongMach[${index}]`, "ngayRut"], e?.format("YYYY-MM-DD") || "") }}
                                                        placeholder={"DD/MM/YYYY"}
                                                        format="DD/MM/YYYY"
                                                        className="input-filter"
                                                    />
                                                </FieldItem>
                                            </Col>
                                            <Col span={12}>
                                                <FieldItem title={"Khác"}>
                                                    <InputTimeout
                                                        disabled={readOnly}
                                                        className="flex1"
                                                        value={get(data, `ongThongDongMach[${index}].ongThongDmKhac`)}
                                                        onChange={e => {
                                                            onChange([`ongThongDongMach[${index}]`, "ongThongDmKhac"], e);
                                                        }}
                                                    >
                                                    </InputTimeout>
                                                </FieldItem>
                                            </Col>
                                        </Row>
                                    }
                                </FieldItem>
                            </Col>
                        ))
                    }
                </Row>
            </FieldItem>
            <FieldItem title="Ống thông TM ngoại vi" rightTitle={!readOnly &&
                <Button
                    type="primary" rightIcon={<SVG.IcAdd />} onClick={onAdd("ongThongTmNgoaiVi")}>Thêm mới</Button>
            } maxWidth={isMobile ? 800 : 0}>
                <Row gutter={[8, 8]} className="toan-than">


                    {
                        (data.ongThongTmNgoaiVi || [{}])?.map((item, index) => (
                            <Col xl={isMobile ? 24 : 12} md={24} xs={24} key={index}>
                                <FieldItem maxWidth={800} title={"Ống thông TM ngoại vi số " + (index + 1)} border={true}
                                    rightTitle={!readOnly && <SVG.IcDelete onClick={onDelete("ongThongTmNgoaiVi", index)} />}>
                                    <SingleChoice
                                        type="radio"
                                        disabled={readOnly}
                                        className="flex1"
                                        outputTypeArray={true}
                                        value={get(data, `ongThongTmNgoaiVi[${index}].ongThong`)}
                                        data={DATA_KHONG_CO} onChange={e => {
                                            onChange([`ongThongTmNgoaiVi[${index}]`, "ongThong"], e);
                                        }}></SingleChoice>
                                    {
                                        isArray(get(data, `ongThongTmNgoaiVi[${index}].ongThong`)) && get(data, `ongThongTmNgoaiVi[${index}].ongThong`).includes(2) &&
                                        <Row gutter={[8, 8]} className="toan-than" key={index}>
                                            <Col span={12}>

                                                <FieldItem title={"Vị trí"}>
                                                    <SingleChoice
                                                        type="radio"
                                                        disabled={readOnly}
                                                        className="vi-tri-tmnv"
                                                        value={VI_TRI_ONG_THONG_TM_NGOAI_VI.find(item => item.ten == get(data, `ongThongTmNgoaiVi[${index}].viTriOngThongTmNv`))?.id || get(data, `ongThongTmNgoaiVi[${index}].viTriOngThongTmNv`)}
                                                        data={VI_TRI_ONG_THONG_TM_NGOAI_VI} onChange={e => {
                                                            const item = VI_TRI_ONG_THONG_TM_NGOAI_VI.find(item => item.id == e);
                                                            onChange([`ongThongTmNgoaiVi[${index}]`, "viTriOngThongTmNv"], item.ten);
                                                        }}></SingleChoice>
                                                </FieldItem>
                                            </Col>
                                            <Col span={12}>
                                                <FieldItem title={"Tình trạng hoạt động"}>
                                                    <SingleChoice
                                                        type="radio"
                                                        disabled={readOnly}
                                                        className="flex1"
                                                        outputTypeArray={true}
                                                        value={get(data, `ongThongTmNgoaiVi[${index}].tinhTrangHoatDong`)}
                                                        data={TINH_TRANG_HOAT_DONG} onChange={e => {
                                                            onChange([`ongThongTmNgoaiVi[${index}]`, "tinhTrangHoatDong"], e);
                                                        }}></SingleChoice>
                                                </FieldItem>
                                            </Col>
                                            <Col span={12}>

                                                <FieldItem title={"Tình trạng da nơi TMNV"}>
                                                    <MultipleChoice
                                                        type="checkbox"
                                                        className="flex1"
                                                        outputTypeArray={true}
                                                        value={get(data, `ongThongTmNgoaiVi[${index}].tinhTrangHoatDongDm`)}
                                                        data={TINH_TRANG_CHAN_ONG_THONG} onChange={e => {
                                                            onChange([`ongThongTmNgoaiVi[${index}]`, "tinhTrangHoatDongDm"], e);
                                                        }}></MultipleChoice>
                                                </FieldItem>
                                            </Col>
                                            <Col span={12}>
                                                <FieldItem title={"Điểm đánh giá mức độ viêm tĩnh mạch (VIP Score)"}>
                                                    <SingleChoice
                                                        type="radio"
                                                        disabled={readOnly}
                                                        className="flex1"
                                                        outputTypeArray={true}
                                                        value={get(data, `ongThongTmNgoaiVi[${index}].danhGiaMucDoViem`)}
                                                        data={VIP_SCORE} onChange={e => {
                                                            onChange([`ongThongTmNgoaiVi[${index}]`, "danhGiaMucDoViem"], e);
                                                        }}></SingleChoice>
                                                </FieldItem>
                                            </Col>
                                            <Col span={12}>
                                                <FieldItem title={"Ngày đặt"}>
                                                    <DateTimePicker
                                                        disabled={readOnly}
                                                        value={get(data, `ongThongTmNgoaiVi[${index}].ngayDat`) ? moment(get(data, `ongThongTmNgoaiVi[${index}].ngayDat`)) : null}
                                                        onChange={e => { onChange([`ongThongTmNgoaiVi[${index}]`, "ngayDat"], e?.format("YYYY-MM-DD") || "") }}
                                                        placeholder={"DD/MM/YYYY"}
                                                        format="DD/MM/YYYY"
                                                        className="input-filter"
                                                    />
                                                </FieldItem>
                                            </Col>
                                            <Col span={12}>
                                                <FieldItem title={"Ngày rút"}>
                                                    <DateTimePicker
                                                        disabled={readOnly}
                                                        value={get(data, `ongThongTmNgoaiVi[${index}].ngayRut`) ? moment(get(data, `ongThongTmNgoaiVi[${index}].ngayRut`)) : null}
                                                        onChange={e => { onChange([`ongThongTmNgoaiVi[${index}]`, "ngayRut"], e?.format("YYYY-MM-DD") || "") }}
                                                        placeholder={"DD/MM/YYYY"}
                                                        format="DD/MM/YYYY"
                                                        className="input-filter"
                                                    />
                                                </FieldItem>
                                            </Col>
                                            <Col span={12}>
                                                <FieldItem title={"Khác"}>
                                                    <InputTimeout
                                                        disabled={readOnly}
                                                        className="flex1"
                                                        value={get(data, `ongThongTmNgoaiVi[${index}].ongThongKhac`)}
                                                        onChange={e => {
                                                            onChange([`ongThongTmNgoaiVi[${index}]`, "ongThongKhac"], e);
                                                        }}
                                                    >
                                                    </InputTimeout>
                                                </FieldItem>
                                            </Col>
                                        </Row>
                                    }
                                </FieldItem>
                            </Col>
                        ))
                    }
                </Row>
            </FieldItem>

            <FieldItem title={"Ống thông khác"}>
                <InputTimeout
                    disabled={readOnly}
                    className="flex1"
                    value={get(data, "ongThongKhac")}
                    onChange={e => {
                        onChange(["ongThongKhac"], e);
                    }}
                >
                </InputTimeout>
            </FieldItem>
        </Main >
    )
}
export default OngThong;