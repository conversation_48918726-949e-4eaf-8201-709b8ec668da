import { Col, Row } from 'antd'
import React from 'react'
import { But<PERSON>, DateTimePicker, InputTimeout } from 'components'
import { get, isArray } from 'lodash'
import SingleChoice from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/SingleChoice'
import MultipleChoice from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/MultipleChoice'
import FieldItem from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/FieldItem'
import { Main } from './styled'
import { SVG } from 'assets'
import moment from 'moment'
import { detectMob } from 'utils'

const DATA_LOAI_ONG_THONG = [
    { id: 1, ten: "Ống thông tĩnh mạch ngoại biên" },
    { id: 2, ten: "Ống thông tĩnh mạch trung tâm" },
    { id: 3, ten: "Ống thông khác" },
]

const VI_TRI_ONG_THONG_TM_NGOAI_VI = [
    { id: "1", ten: "Cánh tay" },
    { id: "2", ten: "Cẳng tay" },
    { id: "3", ten: "Mu bàn tay" },
    { id: "4", ten: "Dưới đòn" },
]

const TINH_TRANG_DA_NOI_DAT = [
    { id: 1, ten: "Khô sạch" },
    { id: 2, ten: "Viêm đỏ" },
    { id: 3, ten: "Tiết dịch" },
]

const VIP_SCORE = [
    { id: 1, ten: "(0) Cấp 0" },
    { id: 2, ten: "(1) Cấp 1" },
    { id: 3, ten: "(2) Cấp 2" },
    { id: 4, ten: "(3) Cấp 3" },
    { id: 5, ten: "(4) Cấp 4" },
    { id: 6, ten: "(5) Cấp 5" },
]

const TINH_TRANG_HOAT_DONG = [
    { id: 1, ten: "Thông tốt" },
    { id: 2, ten: "Bán tắc" },
    { id: 3, ten: "Tắc" },
]

const OngThong = ({ onChange, data, readOnly, ...props }) => {

    const onAdd = (type) => () => {
        onChange([`${type}[${data[type]?.length || 0}]`], {});
    }

    const onDelete = (type, index) => () => {
        const dataOngThong = data[type];
        dataOngThong?.splice(index, 1);
        onChange([type], dataOngThong);
    }

    const isMobile = detectMob();

    return (
        <Main>
            <FieldItem title="Ống thông" rightTitle={!readOnly &&
                <Button
                    type="primary" rightIcon={<SVG.IcAdd />} onClick={onAdd("khungOngThong")}>Thêm mới</Button>
            } maxWidth={isMobile ? 800 : 0}>
                <Row gutter={[8, 8]} className="toan-than">

                    {(data.khungOngThong || [{}])?.map((item, index) => (
                        <Col xl={isMobile ? 24 : 12} md={24} xs={24} key={index}>
                            <FieldItem maxWidth={800} title={"Ống thông số " + (index + 1)} border={true}
                                rightTitle={!readOnly && <SVG.IcDelete onClick={onDelete("khungOngThong", index)} />}>
                                <FieldItem title={"Tên ống thông"}>
                                    <SingleChoice
                                        type="radio"
                                        disabled={readOnly}
                                        className="flex1"
                                        outputTypeArray={true}
                                        value={get(data, `khungOngThong[${index}].ongThong`)}
                                        data={DATA_LOAI_ONG_THONG} onChange={e => {
                                            onChange([`khungOngThong[${index}]`, "ongThong"], e);
                                        }}></SingleChoice>
                                </FieldItem>
                                <Row gutter={[8, 8]} className="toan-than" key={index}>
                                    <Col span={12}>
                                        <FieldItem title={"Tên khác"}>
                                            <InputTimeout
                                                disabled={readOnly}
                                                className="flex1"
                                                value={get(data, `khungOngThong[${index}].tenKhac`)}
                                                onChange={e => {
                                                    onChange([`khungOngThong[${index}]`, "tenKhac"], e);
                                                }}></InputTimeout>
                                        </FieldItem>
                                    </Col>
                                    <Col span={12}>
                                        <FieldItem title={"Vị trí"}>
                                            <SingleChoice
                                                type="radio"
                                                disabled={readOnly}
                                                className="vi-tri-tmnv"
                                                outputTypeArray={true}
                                                value={get(data, `khungOngThong[${index}].viTri`)}
                                                data={VI_TRI_ONG_THONG_TM_NGOAI_VI} onChange={e => {
                                                    onChange([`khungOngThong[${index}]`, "viTri"], e);
                                                }}></SingleChoice>
                                        </FieldItem>
                                    </Col>
                                    <Col span={12}>
                                        <FieldItem title={"Vị trí khác"}>
                                            <InputTimeout
                                                disabled={readOnly}
                                                className="flex1"
                                                value={get(data, `khungOngThong[${index}].viTriKhac`)}
                                                onChange={e => {
                                                    onChange([`khungOngThong[${index}]`, "viTriKhac"], e);
                                                }}></InputTimeout>
                                        </FieldItem>
                                    </Col>
                                    <Col span={12}>
                                        <FieldItem title={"Tình trạng hoạt động"}>
                                            <SingleChoice
                                                type="radio"
                                                disabled={readOnly}
                                                className="flex1"
                                                outputTypeArray={true}
                                                value={get(data, `khungOngThong[${index}].tinhTrangHoatDong`)}
                                                data={TINH_TRANG_HOAT_DONG} onChange={e => {
                                                    onChange([`khungOngThong[${index}]`, "tinhTrangHoatDong"], e);
                                                }}></SingleChoice>
                                        </FieldItem>
                                    </Col>
                                    <Col span={24}>
                                        <FieldItem title={"Điểm đánh giá mức độ viêm TM (VIP score) đối với ống thông TM ngoại biên"}>
                                            <SingleChoice
                                                type="radio"
                                                className="flex1"
                                                outputTypeArray={true}
                                                disabled={readOnly}
                                                value={get(data, `khungOngThong[${index}].mucDoViem`)}
                                                data={VIP_SCORE} onChange={e => {
                                                    onChange([`khungOngThong[${index}]`, "mucDoViem"], e);
                                                }}></SingleChoice>
                                        </FieldItem>
                                    </Col>
                                    <Col span={24}>
                                        <FieldItem title={"Tình trạng da nơi đặt"}>
                                            <SingleChoice
                                                type="radio"
                                                className="flex1"
                                                outputTypeArray={true}
                                                disabled={readOnly}
                                                value={get(data, `khungOngThong[${index}].tinhTrangDaNoiDat`)}
                                                data={TINH_TRANG_DA_NOI_DAT} onChange={e => {
                                                    onChange([`khungOngThong[${index}]`, "tinhTrangDaNoiDat"], e);
                                                }}></SingleChoice>
                                        </FieldItem>
                                    </Col>
                                    <Col span={12}>
                                        <FieldItem title={"Ngày đặt"}>
                                            <DateTimePicker
                                                disabled={readOnly}
                                                value={get(data, `khungOngThong[${index}].ngayDat`) ? moment(get(data, `khungOngThong[${index}].ngayDat`)) : null}
                                                onChange={e => { onChange([`khungOngThong[${index}]`, "ngayDat"], e?.format("YYYY-MM-DD") || "") }}
                                                placeholder={"DD/MM/YYYY"}
                                                format="DD/MM/YYYY"
                                                className="input-filter"
                                            />
                                        </FieldItem>
                                    </Col>
                                    <Col span={12}>
                                        <FieldItem title={"Ngày rút"}>
                                            <DateTimePicker
                                                disabled={readOnly}
                                                value={get(data, `khungOngThong[${index}].ngayRut`) ? moment(get(data, `khungOngThong[${index}].ngayRut`)) : null}
                                                onChange={e => { onChange([`khungOngThong[${index}]`, "ngayRut"], e?.format("YYYY-MM-DD") || "") }}
                                                placeholder={"DD/MM/YYYY"}
                                                format="DD/MM/YYYY"
                                                className="input-filter"
                                            />
                                        </FieldItem>
                                    </Col>
                                    <Col span={12}>
                                        <FieldItem title={"Khác"}>
                                            <InputTimeout
                                                disabled={readOnly}
                                                className="flex1"
                                                value={get(data, `khungOngThong[${index}].khac`)}
                                                onChange={e => {
                                                    onChange([`khungOngThong[${index}]`, "khac"], e);
                                                }}
                                            >
                                            </InputTimeout>
                                        </FieldItem>
                                    </Col>
                                </Row>
                            </FieldItem>
                        </Col>
                    ))}
                </Row>
            </FieldItem>
        </Main >
    )
}
export default OngThong;