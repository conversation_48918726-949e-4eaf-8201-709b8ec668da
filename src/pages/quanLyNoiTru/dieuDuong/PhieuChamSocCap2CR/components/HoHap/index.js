import { Col, Row } from 'antd'
import React from 'react'
import { InputTimeout } from 'components'
import { get } from 'lodash'
import MultipleChoice from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/MultipleChoice'
import SingleChoice from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/SingleChoice'
import FieldItem from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/FieldItem'
import MultipleChoiceByGroup from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/MultipleChoiceByGroup'
import { Main } from './styled'


const DANH_SACH_KIEU_THO = [
    { id: 1, ten: "Đều êm" },
    { id: 2, ten: "Thở nông" },
    { id: 3, ten: "Khò khè" },
    { id: 4, ten: "Khó thở" },
    { id: 5, ten: "Co kéo cơ hô hấp phụ" },
]

const DATA_HO = [
    { id: 1, ten: "Không" },
    { id: 2, ten: "Ho khan" },
    { id: 3, ten: "Ho đờm" },
    { id: 4, ten: "Ho máu" },
]

const LIEU_PHAP_OXY_DONG_THAP = [
    { id: 1, ten: "Không" },
    { id: 2, ten: "Oxy gọng kính" },
    { id: 3, ten: "Oxy mặt nạ có túi không hít lại" },
];


const DATA_KHONG_CO = [
    { id: 1, ten: "Không" },
    { id: 2, ten: "Có" },
];


const DANH_SACH_MAU_SAC_DOM = [
    { id: 1, ten: "Trắng đục" },
    { id: 2, ten: "Vàng" },
    { id: 3, ten: "Xanh" },
    { id: 4, ten: "Trắng trong" },
    { id: 5, ten: "Đỏ" },
    { id: 6, ten: "Nâu" },
    { id: 7, ten: "Hồng" },
    { id: 8, ten: "Đen" },
]

const DANH_SACH_TINH_CHAT_DOM = [
    { id: 1, ten: "Loãng" },
    { id: 2, ten: "Đặc" },
    { id: 3, ten: "Lẫn máu" },
]

const HoHap = ({ onChange, data, readOnly, ...props }) => {

    return (
        <Main>
            <Row gutter={[16, 16]} className="toan-than">
                <Col span={24}>
                    <FieldItem title={"Kiểu thở"} direction={2} align='flex-start'>
                        <MultipleChoiceByGroup
                            radioWidth={120}
                            disabled={readOnly}
                            type="groupRadio"
                            dataGroups={[{ name: "Đều êm", values: [1] }, { name: "Bất thường", color: "red", values: [2, 3, 4, 5] }]}
                            value={get(data, "kieuTho")}
                            data={DANH_SACH_KIEU_THO} onChange={e => {
                                onChange(["kieuTho"], e);
                            }}></MultipleChoiceByGroup>
                    </FieldItem>
                    <FieldItem title={"Ho"} direction={2} align='flex-start'>
                        <SingleChoice
                            disabled={readOnly}
                            radioWidth={120}
                            type="radio"
                            outputTypeArray={true}
                            value={get(data, "ho")}
                            data={DATA_HO} onChange={e => {
                                onChange(["ho"], e);
                            }}></SingleChoice>
                    </FieldItem>
                    <FieldItem title={"Đờm"} direction={2} align='flex-start'>
                        <SingleChoice
                            type="radio"
                            disabled={readOnly}
                            radioWidth={120}
                            className="flex1"
                            value={Number(get(data, "dom"))}
                            data={DATA_KHONG_CO} onChange={e => {
                                onChange(["dom"], e);
                            }}></SingleChoice>
                        {
                            data.dom == 2 &&
                            <>
                                <FieldItem title="Số lượng" direction={2} titleMinWidth={120} maxWidth={250}>
                                    <div className='flex flex-a-center gap-4'>
                                        <InputTimeout
                                            disabled={readOnly}
                                            type="number"
                                            value={get(data, "soLuongDom")}
                                            onChange={(e) => {
                                                onChange(["soLuongDom"], e);
                                            }}
                                        /> ml
                                    </div>
                                </FieldItem>
                                <FieldItem title="Màu sắc" direction={2} titleMinWidth={120}>
                                    <SingleChoice
                                        type="radio"
                                        radioWidth={105}
                                        disabled={readOnly}
                                        className="flex1"
                                        value={Number(get(data, "mauSacSom"))}
                                        data={DANH_SACH_MAU_SAC_DOM} onChange={e => {
                                            onChange(["mauSacSom"], e);
                                        }}></SingleChoice>
                                </FieldItem>
                                <FieldItem title="Tính chất" direction={2} titleMinWidth={120}>
                                    <MultipleChoice
                                        checkboxWidth={105}
                                        disabled={readOnly}
                                        className="flex1"
                                        type="checkbox"
                                        value={get(data, "tinhChatDom")}
                                        data={DANH_SACH_TINH_CHAT_DOM} onChange={e => {
                                            onChange(["tinhChatDom"], e);
                                        }}></MultipleChoice>
                                </FieldItem>

                            </>
                        }
                    </FieldItem>

                    <FieldItem title={"Liệu pháp oxy"} direction={2} align='flex-start'>
                        <SingleChoice
                            type="radio"
                            radioWidth={120}
                            disabled={readOnly}
                            outputTypeArray={true}
                            value={get(data, "lieuPhapOxy")}
                            data={LIEU_PHAP_OXY_DONG_THAP} onChange={e => {
                                onChange(["lieuPhapOxy"], e);
                            }}></SingleChoice>
                        <FieldItem title="Lưu lượng oxy (lít/phút)" direction={2} titleMinWidth={150} width={450}>
                            <div className='flex flex-a-center gap-5'>
                                <InputTimeout
                                    disabled={readOnly}
                                    type="number"
                                    value={get(data, "luuLuongOxy")}
                                    onChange={(e) => {
                                        onChange(["luuLuongOxy"], e);
                                    }}
                                />
                                lít/phút
                            </div>
                        </FieldItem>

                    </FieldItem>
                    <FieldItem title={"Khác"} direction={2}>
                        <InputTimeout
                            disabled={readOnly}
                            className="flex1"
                            value={get(data, "hoHapKhac")}
                            onChange={e => {
                                onChange(["hoHapKhac"], e);
                            }}></InputTimeout>
                    </FieldItem>
                </Col>
            </Row>
        </Main >
    )
}
export default HoHap;