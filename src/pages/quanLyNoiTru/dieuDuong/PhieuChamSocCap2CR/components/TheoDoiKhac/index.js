import { Col, Row } from 'antd'
import React from 'react'
import { <PERSON><PERSON>, DateTimePicker, InputTimeout } from 'components'
import { get, isArray } from 'lodash'
import MultipleChoice from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/MultipleChoice'
import SingleChoice from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/SingleChoice'
import FieldItem from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/FieldItem'
import { Main } from './styled'
import { SVG } from 'assets'
import { detectMob } from 'utils'

const NGUY_CO_TE_NGA = [
    { id: 1, ten: "Không có nguy cơ" },
    { id: 2, ten: "Thấp" },
    { id: 3, ten: "Trung bình" },
    { id: 4, ten: "Cao" },
]
const NGUY_CO_TI_DE = [
    { id: 1, ten: "<PERSON>hông có nguy cơ" },
    { id: 2, ten: "Thấp" },
    { id: 3, ten: "Trung bình" },
    { id: 4, ten: "Cao" },
    { id: 5, ten: "Rất cao" },
]
const CANH_BAO_SOM = [
    { id: 1, ten: "Thấp" },
    { id: 2, ten: "Trung bình thấp" },
    { id: 3, ten: "Trung bình" },
    { id: 4, ten: "Cao" },
]

const TheoDoiKhac = ({ onChange, data, readOnly, ...props }) => {

    const onAdd = (type) => () => {
        onChange([`${type}[${data[type]?.length || 0}]`], {});
    }
    const onRemove = index => () => {
        const khungDau = [...data.khungDau];
        khungDau.splice(index, 1);
        onChange(["khungDau"], khungDau);
    }
    const isMobile = detectMob();

    return (
        <Main>
            <FieldItem
                title="Đau" rightTitle={!readOnly && <Button
                    type="primary" rightIcon={<SVG.IcAdd />} onClick={onAdd("khungDau")}>Thêm mới</Button>}
                maxWidth={isMobile ? 800 : 0}>
                <Row gutter={[8, 8]}>
                    {(data.khungDau || [{}])?.map((item, index) => (
                        <Col span={isMobile ? 24 : 8} xs={isMobile ? 24 : 12} xl={isMobile ? 24 : 8}>
                            <FieldItem maxWidth={800} title={`Thông tin theo dõi đau thứ ${index + 1}`} border={true} rightTitle={!readOnly && <SVG.IcDelete className="pointer" onClick={onRemove(index)} />}>
                                <FieldItem title={"Vị trí"}>
                                    <InputTimeout
                                        disabled={readOnly}
                                        className="flex1"
                                        value={get(data, `khungDau[${index}].viTri`)}
                                        onChange={e => {
                                            onChange([`khungDau[${index}]`, "viTri"], e);
                                        }}
                                    >
                                    </InputTimeout>
                                </FieldItem>
                                <FieldItem title={"Mức độ đau"}>
                                    <InputTimeout
                                        disabled={readOnly}
                                        className="flex1"
                                        value={get(data, `khungDau[${index}].mucDoDau`)}
                                        onChange={e => {
                                            onChange([`khungDau[${index}]`, "mucDoDau"], e);
                                        }}
                                    >
                                    </InputTimeout>
                                </FieldItem>
                            </FieldItem>
                        </Col>
                    ))}
                </Row>
            </FieldItem>

            <hr />
            <FieldItem title="Nguy cơ té ngã (Morse)">
                <SingleChoice data={NGUY_CO_TE_NGA}
                    disabled={readOnly}
                    type="radio"
                    value={get(data, "nguyCoTeNgaMorse")} outputTypeArray={true} onChange={e => onChange(["nguyCoTeNgaMorse"], e)} />
            </FieldItem>
            <FieldItem title="Nguy cơ loét do tỳ đè (Braden)">
                <SingleChoice data={NGUY_CO_TI_DE}
                    disabled={readOnly}
                    type="radio"
                    value={get(data, "nguyCoLoetBraden")} outputTypeArray={true} onChange={e => onChange(["nguyCoLoetBraden"], e)} />
            </FieldItem>
            <FieldItem title="Cảnh báo sớm (NEWS2)">
                <SingleChoice
                    disabled={readOnly}
                    type="radio"
                    data={CANH_BAO_SOM} value={get(data, "canhBaoSom2")} outputTypeArray={true} onChange={e => onChange(["canhBaoSom2"], e)} />
            </FieldItem>
            {/* <FieldItem title="Khác">
                <InputTimeout
                    className="flex1"
                    disabled={readOnly}
                    value={get(data, `dauKhac`)}
                    onChange={e => {
                        onChange(["dauKhac"], e);
                    }}
                >
                </InputTimeout>
            </FieldItem> */}
        </Main >
    )
}
export default TheoDoiKhac;