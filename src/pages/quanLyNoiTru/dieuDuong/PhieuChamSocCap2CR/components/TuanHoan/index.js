import { Col, Row } from 'antd'
import React from 'react'
import { InputTimeout } from 'components'
import { get, isArray } from 'lodash'
import MultipleChoice from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/MultipleChoice'
import MultipleChoiceByGroup from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/MultipleChoiceByGroup'
import SingleChoice from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/SingleChoice'
import FieldItem from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/FieldItem'
import { Main } from './styled'


const VI_TRI_PHU = [
    { id: 1, ten: "Chi trên" },
    { id: 2, ten: "Chi dưới" },
    { id: 3, ten: "Toàn thân" },
];

const TINH_CHAT_PHU = [
    { id: 1, ten: "<PERSON><PERSON> mềm ấn lõm" },
    { id: 2, ten: "<PERSON>ù cứng" },
];

const DANH_SACH_TINH_CHAT_MACH = [
    { id: 1, ten: "Đều, rõ" },
    { id: 2, ten: "Không đều" },
    { id: 3, ten: "Rời rạc" },
    { id: 4, ten: "Khó bắt" },
    { id: 5, ten: "Không rõ mạch" },
]
const DANH_SACH_DO_PHU = [
    { id: 1, ten: "Độ 1" },
    { id: 2, ten: "Độ 2" },
    { id: 3, ten: "Độ 3" },
    { id: 4, ten: "Độ 4" },
]

const TuanHoan = ({ onChange, data, readOnly, ...props }) => {

    return (
        <Main>
            <Row gutter={[16, 16]} className="toan-than">
                <Col span={24}>
                    <FieldItem title={"Tính chất mạch"} direction={2}>
                        <MultipleChoice
                            checkboxWidth={97}
                            type="checkbox"
                            disabled={readOnly}
                            className="flex1"
                            value={get(data, "tinhChatMach")}
                            data={DANH_SACH_TINH_CHAT_MACH} onChange={e => {
                                onChange(["tinhChatMach"], e);
                            }}></MultipleChoice>
                    </FieldItem>
                    <FieldItem title={"Phù"} direction={2} align='flex-start'>
                        <FieldItem title="Độ phù" top={0} direction={2} align='flex-start'>
                            <MultipleChoiceByGroup
                                radioWidth={97} 
                                type="groupRadio"
                                disabled={readOnly}
                                dataGroups={[{ name: "Không", values: [] }, { name: "Có", color: "red", values: [1, 2, 3, 4] }]}
                                value={get(data, "phu")}
                                data={DANH_SACH_DO_PHU} onChange={e => {
                                    onChange(["phu"], e ? e : null);
                                }}></MultipleChoiceByGroup>
                        </FieldItem>

                        {data.phu &&
                            <>
                                <FieldItem title="Vị trí" direction={2} titleMinWidth={103}>
                                    <MultipleChoice
                                        type="checkbox"
                                        disabled={readOnly}
                                        value={get(data, "viTriPhu2")}
                                        data={VI_TRI_PHU} onChange={e => {
                                            onChange(["viTriPhu2"], e);
                                        }}></MultipleChoice>
                                </FieldItem>
                                <FieldItem title="Tính chất" direction={2} titleMinWidth={103}>
                                    <SingleChoice
                                        type="radio"
                                        disabled={readOnly}
                                        outputTypeArray={true}
                                        value={get(data, "tinhChatPhu")}
                                        data={TINH_CHAT_PHU} onChange={e => {
                                            onChange(["tinhChatPhu"], e);
                                        }}></SingleChoice>
                                </FieldItem>
                            </>
                        }
                    </FieldItem>
                    <FieldItem title={"Khác"} direction={2} width={400}>
                        <InputTimeout
                            disabled={readOnly}
                            className="flex1"
                            value={get(data, "tuanHoanKhac")}
                            onChange={e => {
                                onChange(["tuanHoanKhac"], e);
                            }}></InputTimeout>
                    </FieldItem>
                </Col>
            </Row>
        </Main>
    )
}
export default TuanHoan;