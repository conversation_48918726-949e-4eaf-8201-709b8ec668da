import { Col, Row } from 'antd'
import React from 'react'
import { DateTimePicker, InputTimeout } from 'components'
import { get, isEmpty } from 'lodash'
import MultipleChoice from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/MultipleChoice'
import SingleChoice from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/SingleChoice'
import FieldItem from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/FieldItem'
import { Main } from './styled'


const LOI_NOI = [
    { id: 1, ten: "Bình thường" },
    { id: 2, ten: "Nói khàn" },
    { id: 3, ten: "Mất tiếng" },
]

const TINH_TRANG_YEU_LIET = [
    { id: 1, ten: "Không yếu liệt" },
    { id: 2, ten: "Y<PERSON> toàn thân" },
    { id: 3, ten: "YL ½ người trái" },
    { id: 4, ten: "YL ½ người phải" },
    { id: 5, ten: "YL hai chi dưới" },
];

const ROI_LOAN_CAM_GIAC = [
    { id: 1, ten: "Không" },
    { id: 2, ten: "Tê bì" },
    { id: 3, ten: "Giảm cảm giác" },
    { id: 4, ten: "Mất cảm giác" },
]
const DAU_HIEU_KHAC = [
    { id: 1, ten: "Run" },
    { id: 2, ten: "Co giật" },
    { id: 3, ten: "Múa giật" },
    { id: 4, ten: "Loạn động" },
    { id: 5, ten: "Động kinh" },
    { id: 6, ten: "Chóng mặt" },
]

const THAN_THE = [
    { id: 1, ten: "Sạch" },
    { id: 2, ten: "Không sạch" },
]
const TOC = [
    { id: 1, ten: "Sạch" },
    { id: 2, ten: "Không sạch" },
]


const DATA_KHONG_CO = [
    { id: 1, ten: "Không" },
    { id: 2, ten: "Có" },
];


const ThanKinh = ({ onChange, data, readOnly, ...props }) => {

    return (
        <Main>
            <Row gutter={[16, 16]} className="toan-than">
                <Col span={24}>
                    <FieldItem title={"Lời nói"} direction={2} titleMinWidth={180}>
                        <SingleChoice
                            radioWidth={130}
                            disabled={readOnly}
                            outputTypeArray={true}
                            type="radio"
                            value={Number(get(data, "loiNoi"))}
                            data={LOI_NOI} onChange={e => {
                                onChange(["loiNoi"], e);
                            }}></SingleChoice>

                    </FieldItem>
                    <FieldItem title={"Tình trạng yếu liệt"} direction={2} titleMinWidth={180}>
                        <SingleChoice
                            radioWidth={130}
                            disabled={readOnly}
                            outputTypeArray={true}
                            type="radio"
                            value={Number(get(data, "tinhTrangThanKinh"))}
                            data={TINH_TRANG_YEU_LIET} onChange={e => {
                                onChange(["tinhTrangThanKinh"], e);
                            }}></SingleChoice>

                    </FieldItem>
                    <FieldItem title={"Rối loạn cảm giác (tay chân)"} direction={2} titleMinWidth={180}>
                        <SingleChoice
                            radioWidth={130}
                            disabled={readOnly}
                            outputTypeArray={true}
                            type="radio"
                            value={Number(get(data, "roiLoanCamGiac"))}
                            data={ROI_LOAN_CAM_GIAC} onChange={e => {
                                onChange(["roiLoanCamGiac"], e);
                            }}></SingleChoice>

                    </FieldItem>
                    <FieldItem title={"Dấu hiệu khác"} direction={2} titleMinWidth={180} align='flex-start'>
                        <SingleChoice
                            radioWidth={130}
                            disabled={readOnly}
                            type="radio"
                            value={Number(get(data, "thanKinhKhac"))}
                            data={DAU_HIEU_KHAC} onChange={e => {
                                onChange(["thanKinhKhac"], e);
                            }}></SingleChoice>
                        {get(data, "thanKinhKhac")
                            &&
                            <FieldItem title={"Thời gian kéo dài (giây, phút, giờ)"} top={0} direction={2} titleMinWidth={120} width={400}>
                                <DateTimePicker
                                    disabled={readOnly}
                                    value={get(data, `thoiGianKeoDai`) ? moment(get(data, `thoiGianKeoDai`)) : null}
                                    onChange={e => { onChange(["thoiGianKeoDai"], e?.format("YYYY-MM-DD") || "") }}
                                    placeholder={"DD/MM/YYYY"}
                                    format="DD/MM/YYYY"
                                    className="input-filter"
                                />
                            </FieldItem>
                        }
                    </FieldItem>
                    <FieldItem title={"Khác"} top={0} direction={2} titleMinWidth={180} width={400}>
                        <div className='flex flex-a-center gap-5'>
                            <InputTimeout
                                disabled={readOnly}
                                value={get(data, `ctThanKinh`)}
                                onChange={e => { onChange(["ctThanKinh"], e) }}
                            />
                        </div>
                    </FieldItem>
                </Col>
            </Row>
        </Main >
    )
}
export default ThanKinh;