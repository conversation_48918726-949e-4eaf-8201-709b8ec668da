import { Col, Row } from 'antd'
import React from 'react'
import { InputTimeout } from 'components'
import { get } from 'lodash'
import MultipleChoice from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/MultipleChoice'
import { Main } from './styled'
import FieldItem from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/FieldItem'

const THUC_HIEN_THUOC = [
    { id: 1, ten: "(1) Thuốc uống" },
    { id: 2, ten: "(2) Thuốc tiêm" },
    { id: 3, ten: "(3) Thuốc truyền" },
    { id: 4, ten: "(4) Phun khí dung" },
    { id: 5, ten: "(5) Thuốc đặt" },
    { id: 6, ten: "(6) Truyền máu và chế phẩm máu" },]


const CHAM_SOC_DIEU_DUONG = [
    { ten: "(1) Thở oxy", id: 1 },
    { ten: "(2) Vỗ rung", id: 2 },
    { ten: "(3) Ăn qua sonde", id: 3 },
    { ten: "(4) Đặt sonde dạ dày", id: 4 },
    { ten: "(5) Đặt sonde tiểu", id: 5 },
    { ten: "(6) Đặt catheter TM", id: 6 },
    { ten: "(7) TB cắt chỉ", id: 7 },
    { ten: "(8) TB - RVT", id: 8 },
    { ten: "(9) TB-RVT-CL", id: 9 },
    { ten: "(10) Thụt tháo", id: 10 },
    { ten: "(11) Phòng ngừa loét", id: 11 },
    { ten: "(12) Rút dẫn lưu", id: 12 },
    { ten: "(13) Gội đầu", id: 13 },
    { ten: "(14) Tắm", id: 14 },
    { ten: "(15) Rút meches", id: 15 },
    { ten: "(16) PHCN", id: 16 },
    { ten: "(17) Hạ sốt", id: 17 },
    { ten: "(18) Vệ sinh răng miệng", id: 18 },
    { ten: "(19) Rút sonde dạ dày", id: 19 },
    { ten: "(20) Rút sonde tiểu", id: 20 },
    { ten: "(21) Rút catheter TM", id: 21 },
    { ten: "(22) Hút đờm", id: 22 },
    { ten: "(23) Cho người bệnh nằm đầu cao", id: 23 },
    { ten: "(24) Phòng ngừa té ngã", id: 24 },
]

const THEO_DOI_NGUOI_BENH = [
    { ten: "(1) DHST", id: 1, hint: [] },
    { ten: "(2) Cân nặng", id: 2, hint: [] },
    { ten: "(3) Dinh dưỡng", id: 3, hint: [] },
    { ten: "(4) ĐHMM", id: 4, hint: [] },
    { ten: "(5) Hô hấp", id: 5, hint: [] },
    { ten: "(6) Nước tiểu", id: 6, hint: [] },
    { ten: "(7) Vệ sinh", id: 7, hint: [] },
    { ten: "(8) Vết mổ", id: 8, hint: [] },
    { ten: "(9) Vận động", id: 9, hint: [] },
    { ten: "(10) Lọc máu", id: 10, hint: [] },
    { ten: "(11) Dịch dẫn lưu", id: 11, hint: [] },
    { ten: "(12) Dấu hiệu khản tiếng", id: 12, hint: [] },
    { ten: "(13) Theo dõi tri giác", id: 13, hint: [] },
]

const THUC_HIEN_CHI_DINH_CLS = [
    { id: 1, ten: "(1) Xét nghiệm máu" },
    { id: 2, ten: "(2) Xét nghiệm nước tiểu" },
    { id: 3, ten: "(3) Xét nghiệm đàm" },
    { id: 4, ten: "(4) Xét nghiệm phân" },
    { id: 5, ten: "(5) Xét nghiệm dịch" },
    { id: 6, ten: "(6) Điện tim" },
    { id: 7, ten: "(7) Siêu âm bụng" },
    { id: 8, ten: "(8) Siêu âm tim" },
    { id: 9, ten: "(9) X-Quang" },
    { id: 10, ten: "(10) CT" },
    { id: 11, ten: "(11) MRI" },
    { id: 12, ten: "(12) Nội soi dạ dày" },
    { id: 13, ten: "(13) Nội soi đại tràng" },
    { id: 14, ten: "(14) Nội soi phế quản" }
]


const CanThiepDieuDuong = ({ onChange, data, readOnly, ...props }) => {

    return (
        <Main>
            <Row gutter={[16, 16]} className="toan-than">
                <Col span={24}>
                    <FieldItem title={"(A) Thực hiện thuốc"}>
                        <MultipleChoice
                            checkboxWidth={250}
                            type="checkbox"
                            disabled={readOnly}
                            className="flex1"
                            value={get(data, "thucHienThuoc") ? get(data, "thucHienThuoc").split(",").map(item => Number(item)) : []}
                            data={THUC_HIEN_THUOC} onChange={e => {
                                onChange(["thucHienThuoc"], e?.join(","));
                            }}></MultipleChoice>
                    </FieldItem>
                    <FieldItem title={"Khác"}>
                        <InputTimeout
                            disabled={readOnly}
                            value={get(data, "ctThucHienThuoc")}
                            onChange={e => {
                                onChange(["ctThucHienThuoc"], e);
                            }}></InputTimeout>
                    </FieldItem>
                    <FieldItem title={"(B) Thực hiện cận lâm sàng"}>
                        <MultipleChoice
                            type="checkbox"
                            checkboxWidth={250}
                            disabled={readOnly}
                            value={get(data, "thucHienCls") ? get(data, "thucHienCls").split(",").map(item => Number(item)) : []}
                            data={THUC_HIEN_CHI_DINH_CLS} onChange={e => {
                                onChange(["thucHienCls"], e?.join(","));
                            }}></MultipleChoice>
                    </FieldItem>
                    <FieldItem title={"Khác"}>
                        <InputTimeout
                            disabled={readOnly}
                            value={get(data, "ctThucHienCls")}
                            onChange={e => {
                                onChange(["ctThucHienCls"], e);
                            }}></InputTimeout>
                    </FieldItem>
                    <FieldItem title={"(C) Chăm sóc điều dưỡng"}>
                        <MultipleChoice
                            checkboxWidth={250}
                            type="checkbox"
                            disabled={readOnly}
                            value={get(data, "chamSocDieuDuong") ? get(data, "chamSocDieuDuong").split(",").map(item => Number(item)) : []}
                            data={CHAM_SOC_DIEU_DUONG} onChange={e => {
                                onChange(["chamSocDieuDuong"], e?.join(","));
                            }}></MultipleChoice>
                    </FieldItem>
                    <FieldItem title={"Khác"}>
                        <InputTimeout
                            disabled={readOnly}
                            value={get(data, "ctChamSocDieuDuong")}
                            onChange={e => {
                                onChange(["ctChamSocDieuDuong"], e);
                            }}></InputTimeout>
                    </FieldItem>
                    <FieldItem title={"THEO DÕI NGƯỜI BỆNH"}>
                        <MultipleChoice
                            checkboxWidth={250}
                            type="checkbox"
                            disabled={readOnly}
                            value={get(data, "theoDoiNguoiBenh")}
                            data={THEO_DOI_NGUOI_BENH} onChange={e => {
                                onChange(["theoDoiNguoiBenh"], e);
                            }}></MultipleChoice>
                    </FieldItem>
                    <FieldItem title={"Khác"}>
                        <InputTimeout
                            disabled={readOnly}
                            value={get(data, "ctTheoDoiNguoiBenh")}
                            onChange={e => {
                                onChange(["ctTheoDoiNguoiBenh"], e);
                            }}></InputTimeout>
                    </FieldItem>
                </Col>

            </Row>
        </Main >
    )
}
export default CanThiepDieuDuong;