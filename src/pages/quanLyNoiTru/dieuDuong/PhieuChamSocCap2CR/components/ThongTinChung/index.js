import { Col, Row } from 'antd'
import React from 'react'
import { InputTimeout } from 'components'
import { get, isArray } from 'lodash'
import MultipleChoiceByGroup from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/MultipleChoiceByGroup'
import SingleChoice from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/SingleChoice'
import FieldItem from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/FieldItem'
import { Main } from './styled'

const PHAN_CAP_CHAM_SOC = [
    { id: 2, ten: "Chăm sóc cấp 2" },
    { id: 3, ten: "Chăm sóc cấp 3" },
]
const TIEN_SU_DI_UNG = [
    { id: 1, ten: "Chưa ghi nhân" },
    { id: 2, ten: "Có" },
]



const ThongTinChung = ({ onChange, onChangeState, data, state, readOnly, ...props }) => {

    return (
        <Main>
            <Row gutter={[16, 16]} className="toan-than">
                <Col span={24}>
                    <FieldItem titleMinWidth={150} title={"Phân cấp chăm sóc"} direction={2}>
                        <SingleChoice
                            radioWidth={150}
                            type="radio"
                            disabled={readOnly}
                            className="flex1"
                            value={Number(get(data, "phanCapChamSoc"))}
                            data={PHAN_CAP_CHAM_SOC} onChange={e => {
                                onChange(["phanCapChamSoc"], e);
                            }}>
                        </SingleChoice>
                    </FieldItem>
                    <FieldItem titleMinWidth={150} title={"Tiền sử dị ứng"} direction={2} align='flex-start'>
                        <SingleChoice
                            radioWidth={150}
                            type="radio"
                            disabled={readOnly}
                            className="flex1"
                            value={Number(get(state, "tienSuDiUng"))}
                            outputTypeArray={true}
                            data={TIEN_SU_DI_UNG} onChange={onChangeState("tienSuDiUng")}>
                        </SingleChoice>
                    </FieldItem>
                    {
                        state?.tienSuDiUng == 2 &&
                        <FieldItem title={"Có, ghi rõ"} direction={2} width={400}>
                            <InputTimeout
                                disabled={readOnly}
                                className="flex1"
                                value={get(state, "ghiRo")}
                                onChange={onChangeState("ghiRo")}
                            >
                            </InputTimeout>
                        </FieldItem>
                    }
                </Col>
            </Row>
        </Main >
    )
}
export default ThongTinChung;