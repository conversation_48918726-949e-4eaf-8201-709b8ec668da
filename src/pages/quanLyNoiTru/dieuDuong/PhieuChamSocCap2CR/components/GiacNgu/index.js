import { Col, Row } from 'antd'
import React from 'react'
import { InputTimeout } from 'components'
import { get } from 'lodash'
import MultipleChoice from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/MultipleChoice'
import SingleChoice from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/SingleChoice'
import FieldItem from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/FieldItem'
import { Main } from './styled'


const TINH_TRANG_GIAC_NGU = [
    { id: 1, ten: "<PERSON>ình thường" },
    { id: 2, ten: "<PERSON>h<PERSON> ngủ" },
    { id: 3, ten: "Rối loạn giấc ngủ" },
    { id: 4, ten: "Mất ngủ" },
]

const NGHI_NGOI = [
    { id: 1, ten: "Nghỉ tại giường" },
    { id: 2, ten: "Đi lại nhẹ nhàng quanh giường" },
    { id: 3, ten: "Hạn chế gắng sức" },
];

const KHA_NANG_TU_CHAM_SOC = [
    { id: 1, ten: "Độc lập" },
    { id: 2, ten: "Phụ thuộc 1 phần" },
    { id: 3, ten: "Phụ thuộc hoàn toàn" },
]
const RANG_MIENG = [
    { id: 1, ten: "Sạch" },
    { id: 2, ten: "Không sạch" },
    { id: 3, ten: "Răng giả" },
]

const THAN_THE = [
    { id: 1, ten: "Sạch" },
    { id: 2, ten: "Không sạch" },
]
const TOC = [
    { id: 1, ten: "Sạch" },
    { id: 2, ten: "Không sạch" },
]


const DATA_KHONG_CO = [
    { id: 1, ten: "Không" },
    { id: 2, ten: "Có" },
];


const GiacNgu = ({ onChange, data, readOnly, ...props }) => {

    return (
        <Main>
            <Row gutter={[16, 16]} className="toan-than">
                <Col span={24}>
                    <FieldItem titleMarginTop={5} title={"GIẤC NGỦ, NGHỈ NGƠI:"} direction={2} align='flex-start' titleMinWidth={150}>
                        <FieldItem title={"Số giờ ngủ được"} top={0} direction={2} titleMinWidth={120}>
                            <div className='flex flex-a-center gap-5'>
                                <InputTimeout
                                    type="number"
                                    disabled={readOnly}
                                    min={0}
                                    value={get(data, "soGioNgu")}
                                    onChange={(e) => {
                                        onChange(["soGioNgu"], e);
                                    }}
                                />
                                <span>giờ</span>
                            </div>
                        </FieldItem>
                        <FieldItem title={"Tình trạng giấc ngủ"} direction={2} titleMinWidth={120}>
                            <SingleChoice
                                radioWidth={120}
                                disabled={readOnly}
                                type="radio"
                                value={Number(get(data, "tinhTrangGiacNgu"))}
                                data={TINH_TRANG_GIAC_NGU} onChange={e => {
                                    onChange(["tinhTrangGiacNgu"], e);
                                }}></SingleChoice>

                        </FieldItem>
                        <FieldItem title={"Sử dụng thuốc ngủ"} direction={2} titleMinWidth={120}>
                            <SingleChoice
                                radioWidth={120}
                                disabled={readOnly}
                                className="flex1"
                                type="radio"
                                outputTypeArray={true}
                                value={get(data, "suDungThuocNgu")}
                                data={DATA_KHONG_CO} onChange={e => {
                                    onChange(["suDungThuocNgu"], e);
                                }}></SingleChoice>
                        </FieldItem>
                        <FieldItem title={"Nghỉ ngơi"} direction={2} titleMinWidth={120}>
                            <MultipleChoice
                                type="checkbox"
                                disabled={readOnly}
                                value={get(data, "nghiNgoi")}
                                data={NGHI_NGOI} onChange={e => {
                                    onChange(["nghiNgoi"], e);
                                }}></MultipleChoice>

                        </FieldItem>
                        <FieldItem title={"Khác"} direction={2}>
                            <InputTimeout
                                disabled={readOnly}
                                className="flex1"
                                value={get(data, "giacNguKhac")}
                                onChange={e => {
                                    onChange(["giacNguKhac"], e);
                                }}></InputTimeout>
                        </FieldItem>
                    </FieldItem>
                    <FieldItem title={"VỆ SINH CÁ NHÂN:"} direction={2} align='flex-start' titleMinWidth={150}>
                        <FieldItem title={"Khả năng tự chăm sóc"} direction={2} top={0} titleMinWidth={150}>
                            <SingleChoice
                                type="radio"
                                disabled={readOnly}
                                className="flex1"
                                outputTypeArray={true}
                                value={Number(get(data, "tinhTrangVeSinh"))}
                                data={KHA_NANG_TU_CHAM_SOC} onChange={e => {
                                    onChange(["tinhTrangVeSinh"], e);
                                }}></SingleChoice>
                        </FieldItem>
                        <FieldItem title={"Răng miệng"} direction={2} titleMinWidth={150}>
                            <SingleChoice
                                disabled={readOnly}
                                className="flex1"
                                type="radio"
                                outputTypeArray={true}
                                value={get(data, "rangMieng")}
                                data={RANG_MIENG} onChange={e => {
                                    onChange(["rangMieng"], e);
                                }}></SingleChoice>
                        </FieldItem>
                        <FieldItem title={"Thân thể"} direction={2} titleMinWidth={150}>
                            <SingleChoice
                                disabled={readOnly}
                                className="flex1"
                                type="radio"
                                outputTypeArray={true}
                                value={get(data, "thanThe")}
                                data={THAN_THE} onChange={e => {
                                    onChange(["thanThe"], e);
                                }}></SingleChoice>
                        </FieldItem>
                        <FieldItem title={"Tóc"} direction={2} titleMinWidth={150}>
                            <SingleChoice
                                disabled={readOnly}
                                type="radio"
                                className="flex1"
                                outputTypeArray={true}
                                value={get(data, "toc")}
                                data={TOC} onChange={e => {
                                    onChange(["toc"], e);
                                }}></SingleChoice>
                        </FieldItem>
                        <FieldItem title={"Khác"} direction={2}>
                            <InputTimeout
                                disabled={readOnly}
                                className="flex1"
                                value={get(data, "veSinhCaNhanKhac")}
                                onChange={e => {
                                    onChange(["veSinhCaNhanKhac"], e);
                                }}></InputTimeout>
                        </FieldItem>
                    </FieldItem>
                </Col>
            </Row>
        </Main >
    )
}
export default GiacNgu;