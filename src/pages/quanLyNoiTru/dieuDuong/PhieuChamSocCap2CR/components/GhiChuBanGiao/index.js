import React from 'react'
import { TextField } from 'components'
import FieldItem from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/FieldItem'
import { Main } from './styled'
import { get } from 'lodash'

const GhiChuBanGiao = ({ onChange, data, readOnly, ...props }) => {

    return (
        <Main>
            <FieldItem title={`Ghi chú bàn giao`}>
                <TextField
                    disabled={readOnly}
                    type="html"
                    className="flex1"
                    html={get(data, "banGiao")}
                    onChange={e => onChange(["banGiao"], e)}
                >
                </TextField>
            </FieldItem>

        </Main >
    )
}
export default GhiChuBanGiao;