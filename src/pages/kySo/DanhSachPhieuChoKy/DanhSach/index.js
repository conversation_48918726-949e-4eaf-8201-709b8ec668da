import React, { useMemo, useRef } from "react";
import {
  Table<PERSON>rapper,
  Pagination,
  HeaderSearch,
  Tooltip,
  Checkbox,
} from "components";
import { Main } from "./styled";
import { useDispatch } from "react-redux";
import moment from "moment";
import { useEnum, useLazyKVMap, useListAll, useLoading, useStore } from "hooks";
import { useTranslation } from "react-i18next";
import { SVG } from "assets";
import {
  ENUM,
  LOAI_BIEU_MAU,
  TRANG_THAI_KY,
  VI_TRI_PHIEU_IN,
  MAN_HINH_PHIEU_IN,
  MA_BIEU_MAU_EDITOR,
} from "constants/index";
import ModalSignPrint from "../ModalSignPrint";
import { combineUrlParams } from "utils/index";
import {
  checkIsPhieuKySo,
  getThongTinKySo,
  getChuKySo,
} from "utils/phieu-utils";
import ModalTrinhKy from "../ModalTrinhKy";
import ModalHoSoBenhAn from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalHoSoBenhAn";
import printProvider, { printJS } from "data-access/print-provider";
import editorUtils from "utils/editor-utils";
import { toSafePromise } from "lib-utils";

const DanhSach = () => {
  const { t } = useTranslation();
  const { showLoading, hideLoading } = useLoading();

  const refSignPrint = useRef(null);
  const refModalTrinhKy = useRef(null);
  const listData = useStore("danhSachPhieuChoKy.listData", []);
  const selectionIdx = useStore("danhSachPhieuChoKy.selectionIdx", []);
  const dataSortColumn = useStore("danhSachPhieuChoKy.dataSortColumn", {});
  const { totalElements, page, size, isSelectAll, first, last, isLoading } =
    useStore("danhSachPhieuChoKy", {});
  const [listtrangThaiKy] = useEnum(ENUM.TRANG_THAI_KY);
  const [dataTrangThaiBenhAn] = useEnum(ENUM.TRANG_THAI_BENH_AN);

  const [getTrangThaiBenhAnById] = useLazyKVMap(dataTrangThaiBenhAn);

  const [listAllQuyenKy] = useListAll(
    "quyenKy",
    {
      active: true,
      size: "",
      page: "",
    },
    true
  );

  const {
    danhSachPhieuChoKy: {
      getList,
      onSizeChange,
      onSortChange,
      updateData,
      searchByParams,
    },
    phieuIn: { getListPhieu, tuChoiKyPhieu, getThongTinPhieu },
  } = useDispatch();

  const onClickSort = (key, value) => {
    onSortChange({ [key]: value });
  };

  const onChangePage = (page) => {
    getList({ page: page - 1, isSelectAll });
  };

  const handleSizeChange = (size) => {
    onSizeChange({ size, dataSearch: { isSelectAll } });
  };

  const setRowClassName = (record) => {
    return "";
  };

  const onViewFile = (data) => () => {
    if (data.loaiBieuMau === LOAI_BIEU_MAU.BIEU_MAU_CHINH_SUA) {
      const lichSuKyId = data.id;
      let kySoParams = {};
      if (checkIsPhieuKySo(data)) {
        const phieu = {
          dsSoPhieu: [data],
        };
        kySoParams = getThongTinKySo(phieu);
        kySoParams.chuKySo = getChuKySo(data);
      }
      const payload = {
        nbDotDieuTriId: data.nbDotDieuTriId,
        kySo: true,
        soPhieu: data.soPhieu,
        ...kySoParams,
        lichSuKyId: lichSuKyId,
        baoCaoId: data.baoCaoId,
      };

      //với tờ điều trị ngoại trú, soPhieu = id bản ghi khám + khoa chỉ định id
      //==> thực hiện tách để lấy id bản ghi khám => mở link editor
      if (data.maBaoCao === "EMR_BA077.1") {
        payload.ngoaiTru = true;

        const soPhieuStr = (data.soPhieu || "") + "";
        const khoaChiDinhIdStr = (data.khoaChiDinhId || "") + "";

        if (soPhieuStr.endsWith(khoaChiDinhIdStr)) {
          const idKham = soPhieuStr.substring(
            0,
            soPhieuStr.length - khoaChiDinhIdStr.length
          );

          window.open(
            combineUrlParams(
              `/editor/bao-cao/${data.maBaoCao}/${idKham}`,
              payload
            )
          );
        } else {
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${data.maBaoCao}/${data.soPhieu}`,
              payload
            )
          );
        }
      } else if (data.maBaoCao === "EMR_BA111") {
        payload.phieuXuatId = data.soPhieu;
        window.open(
          combineUrlParams(`/editor/bao-cao/${data.maBaoCao}`, payload)
        );
      } else {
        window.open(
          combineUrlParams(
            `/editor/bao-cao/${data.maBaoCao}/${data.soPhieu}`,
            payload
          )
        );
      }
    } else {
      refSignPrint.current &&
        refSignPrint.current.show(data, () => {
          searchByParams({});
        });
    }
  };

  const onTrinhKy = (data) => () => {
    const chuKySo = getChuKySo(data);

    refModalTrinhKy.current &&
      refModalTrinhKy.current.show(
        {
          id: data?.id,
          chuKySo,
          quyenKyId: data?.[`quyenKy${chuKySo}Id`],
          khoaChiDinhId: data?.khoaChiDinhId,
          khoaThucHienId: data?.khoaThucHienId,
        },
        () => {
          searchByParams({});
        }
      );
  };

  const onTuChoiKy = (data) => async () => {
    try {
      showLoading();
      await tuChoiKyPhieu(data?.id);

      searchByParams({});
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const refModalHoSoBenhAn = useRef(null);

  const onShowHoSoBenhAn = (item) => () => {
    refModalHoSoBenhAn.current &&
      refModalHoSoBenhAn.current.show({
        nbThongTinId: item.nbThongTinId,
        nbDotDieuTriId: item.nbDotDieuTriId,
      });
  };

  const handleInPhieu = (item) => {
    showLoading();
    getListPhieu({
      nbDotDieuTriId: item?.nbDotDieuTriId,
      maManHinh: MAN_HINH_PHIEU_IN.KY_SO,
      maViTri: VI_TRI_PHIEU_IN.KY_SO.IN_PHIEU,
      chiDinhTuDichVuId: item?.id,
    }).then(async (res) => {
      const phieu = res[0]; // ở màn hình này, vị trí này chỉ thiết lập phiếu BHXH

      phieu.linkEditor = editorUtils.getLinkHeadless({
        nbDotDieuTriId: item?.nbDotDieuTriId,
        nbThongTinId: item?.nbThongTinId,
        maManHinh: MAN_HINH_PHIEU_IN.KY_SO,
        maViTri: VI_TRI_PHIEU_IN.KY_SO.IN_PHIEU,
        maNb: item?.maNb,
        isInHsba: true,
        khoaChiDinhId: item?.khoaChiDinhId,
        ma: phieu.ma,
        // nbGoiDvId: phieu.nbGoiDvId,
        baoCaoId: phieu.baoCaoId,
        loaiBieuMau: phieu.loaiBieuMau,
        mhParams: {
          nbDotDieuTriId: item?.nbDotDieuTriId,
          nbThongTinId: item?.nbThongTinId,
          maManHinh: MAN_HINH_PHIEU_IN.KY_SO,
          maViTri: VI_TRI_PHIEU_IN.KY_SO.IN_PHIEU,
          maNb: item?.maNb,
          khoaChiDinhId: item?.khoaChiDinhId,
        },
      });

      const [err, thongTinPhieu] = await toSafePromise(
        getThongTinPhieu({
          phieu,
          nbDotDieuTriId: item?.nbDotDieuTriId,
          baoCaoId: phieu.baoCaoId,
          isInHsba: true,
        })
      );
      if (err) {
        console.error(err);
        hideLoading();
        return;
      }

      if (thongTinPhieu.filePdf) {
        const filePdf = await printProvider.getMergePdf(thongTinPhieu.filePdf);
        printJS({
          printable: filePdf,
          type: "pdf",
        });
        hideLoading();
      } else {
        hideLoading();
      }
    });
  };

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: 50,
      dataIndex: "index",
      key: "index",
      align: "center",
    },
    {
      title: (
        <HeaderSearch
          title={t("kySo.tenPhieu")}
          sort_key="tenBaoCao"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["tenBaoCao"] || ""}
        />
      ),
      width: 180,
      dataIndex: "tenBaoCao",
      key: "tenBaoCao",
      render: (item) => {
        return <b>{item}</b>;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.maHoSo")}
          sort_key="maHoSo"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["maHoSo"] || ""}
        />
      ),
      width: 100,
      dataIndex: "maHoSo",
      key: "maHoSo",
      render: (item) => {
        return <b>{item}</b>;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kySo.hoVaTenNb")}
          sort_key="tenNb"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["tenNb"] || ""}
        />
      ),
      width: 180,
      dataIndex: "tenNb",
      key: "tenNb",
    },
    {
      title: (
        <HeaderSearch
          title={t("common.loaiDoiTuong")}
          sort_key="tenLoaiDoiTuong"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["tenLoaiDoiTuong"] || ""}
        />
      ),
      width: 120,
      dataIndex: "tenLoaiDoiTuong",
      key: "tenLoaiDoiTuong",
    },
    {
      title: (
        <HeaderSearch
          title={t("kySo.nguoiTrinhKy")}
          sort_key="tenNguoiTrinhKy"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["tenNguoiTrinhKy"] || ""}
        />
      ),
      width: 120,
      dataIndex: "tenNguoiTrinhKy",
      key: "tenNguoiTrinhKy",
      render: (item) => {
        return item;
      },
    },
    {
      title: <HeaderSearch title={t("kySo.vaiTroDuocTrinhKy")} />,
      width: 160,
      dataIndex: "vaiTroDuocTrinhKy",
      key: "vaiTroDuocTrinhKy",
      render: (item, data) => {
        const chuKySo = getChuKySo(data);

        return listAllQuyenKy.find((x) => x.id === data[`quyenKy${chuKySo}Id`])
          ?.ten;
      },
    },
    {
      title: <HeaderSearch title={t("kySo.taiKhoanDuocTrinhKy")} />,
      width: 120,
      dataIndex: "taiKhoanDuocTrinhKy",
      key: "taiKhoanDuocTrinhKy",
      render: (item, data) => {
        if (data?.trangThai !== TRANG_THAI_KY.TRINH_KY) {
          return "";
        }

        return (data?.dsChuKy || []).find(
          (x) => x.trangThai === TRANG_THAI_KY.TRINH_KY
        )?.tenNguoiKy;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kySo.nguoiKy")}
          sort_key="tenNguoiKy"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["tenNguoiKy"] || ""}
        />
      ),
      width: 120,
      dataIndex: "tenNguoiKy",
      key: "tenNguoiKy",
      render: (item) => {
        return item;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.soPhieu")}
          sort_key="soPhieu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["soPhieu"] || ""}
        />
      ),
      width: 80,
      dataIndex: "soPhieu",
      key: "soPhieu",
      render: (item) => {
        return item;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.trangThai")}
          sort_key="trangThai"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["trangThai"] || ""}
        />
      ),
      width: 80,
      dataIndex: "trangThai",
      key: "trangThai",
      render: (item) => {
        return listtrangThaiKy.find((x) => x.id === item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("hsba.trangThaiBenhAn")}
          sort_key="trangThaiBenhAn"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["trangThaiBenhAn"] || ""}
        />
      ),
      width: 150,
      dataIndex: "trangThaiBenhAn",
      key: "trangThaiBenhAn",
      render: (item) => {
        return getTrangThaiBenhAnById(item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("baoCao.khoaNB")}
          sort_key="tenKhoaNb"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["tenKhoaNb"] || ""}
        />
      ),
      width: 120,
      dataIndex: "tenKhoaNb",
      key: "tenKhoaNb",
    },
    {
      title: (
        <HeaderSearch
          title={t("kySo.thoiGianTrinhKy")}
          sort_key="thoiGianTrinhKy"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["thoiGianTrinhKy"] || ""}
        />
      ),
      width: 100,
      dataIndex: "thoiGianTrinhKy",
      key: "thoiGianTrinhKy",
      render: (item) => {
        return item && moment(item)?.format("DD/MM/YYYY");
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kySo.thoiGianKy")}
          sort_key="thoiGianKy"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["thoiGianKy"] || ""}
        />
      ),
      width: 100,
      dataIndex: "thoiGianKy",
      key: "thoiGianKy",
      render: (item) => {
        return item && moment(item)?.format("DD/MM/YYYY");
      },
    },
    {
      title: <HeaderSearch title={t("kySo.xemPhieu")} />,
      width: 100,
      dataIndex: "",
      key: "",
      align: "center",
      fixed: "right",
      render: (item, data) => {
        return (
          <>
            {MA_BIEU_MAU_EDITOR["P044"].maBaoCao == data.maBaoCao && (
              <Tooltip title={t("common.inPhieu")}>
                <SVG.IcPrint
                  onClick={() => handleInPhieu(item)}
                  className="ic-action"
                />
              </Tooltip>
            )}

            {[TRANG_THAI_KY.DA_KY, TRANG_THAI_KY.CHUA_KY].includes(
              data.trangThai
            ) && (
              <Tooltip title={t("phieuIn.trinhKy")}>
                <SVG.IcTrinhKy
                  color={"var(--color-blue-primary)"}
                  className="ic-action"
                  onClick={onTrinhKy(data)}
                />
              </Tooltip>
            )}

            {[TRANG_THAI_KY.TRINH_KY].includes(data.trangThai) && (
              <Tooltip title={t("phieuIn.tuChoiKy")}>
                <SVG.IcTuChoiKy
                  color={"var(--color-blue-primary)"}
                  className="ic-action"
                  onClick={onTuChoiKy(data)}
                />
              </Tooltip>
            )}

            <Tooltip title={t("kySo.xemPhieu")}>
              <SVG.IcEye className="ic-action" onClick={onViewFile(data)} />
            </Tooltip>

            <Tooltip title={t("quanLyNoiTru.xemHoSoBenhAn")}>
              <SVG.IcHsba className="icon" onClick={onShowHoSoBenhAn(data)} />
            </Tooltip>
          </>
        );
      },
    },
  ];

  const onSelectChange = (selectedRowKeys, data) => {
    updateData({ selectionIdx: [...new Set(selectedRowKeys)] });
  };

  const isCheckedAll = useMemo(() => {
    return (
      listData?.length &&
      listData.map((item) => item.index).every((i) => selectionIdx.includes(i))
    );
  }, [listData, selectionIdx]);

  const onCheckAll = (e) => {
    let selectedRowKeys = e.target?.checked
      ? (listData || []).map((x) => x.index)
      : [];
    updateData({ selectionIdx: selectedRowKeys });
  };

  const rowSelection = {
    columnTitle: (
      <HeaderSearch
        title={
          <Checkbox onChange={onCheckAll} checked={isCheckedAll}></Checkbox>
        }
      />
    ),
    columnWidth: 40,
    onChange: onSelectChange,
    selectedRowKeys: selectionIdx,
  };

  return (
    <Main noPadding={true}>
      <TableWrapper
        columns={columns}
        dataSource={listData}
        rowKey={(record) => record.index} // id bị trùng dùng index or remove
        rowClassName={setRowClassName}
        scroll={{ x: 1800 }}
        rowSelection={rowSelection}
        loading={isLoading}
      />
      <Pagination
        listData={listData}
        onChange={onChangePage}
        current={page + 1}
        pageSize={size}
        total={totalElements}
        onShowSizeChange={handleSizeChange}
        first={first}
        last={last}
        isLoading={isLoading}
      />
      <ModalSignPrint ref={refSignPrint} />
      <ModalTrinhKy ref={refModalTrinhKy} />
      <ModalHoSoBenhAn ref={refModalHoSoBenhAn} />
    </Main>
  );
};

export default DanhSach;
