import React, { useRef, useEffect } from "react";
import { ModalTemplate, Button } from "components";
import { useTranslation } from "react-i18next";
import { Main } from "./styled";
import { useDispatch } from "react-redux";
import { Form, Input, message } from "antd";
import { HOTKEY, ROLES } from "constants/index";
import { useParams } from "react-router-dom";
import { useConfirm, useStore, useLoading } from "hooks";
import { SVG } from "assets";
import { checkRole } from "lib-utils/role-utils";

const PhatHanhThe = ({ isTamUng }) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const refModal = useRef(null);
  const [form] = Form.useForm();
  const { id } = useParams();
  const { showLoading, hideLoading } = useLoading();

  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan");
  const thongTinBenhNhan = useStore("nbDotDieuTri.thongTinBenhNhan");

  let _ttBenhNhan = isTamUng ? thongTinCoBan : thongTinBenhNhan;
  const { tuoi, tenNb, maThe } = _ttBenhNhan || {};

  const {
    nbTheNb: { createOrEdit: postRecord },
    nbTheNbHuy: { createOrEdit: postRecordHuy, huyTheAna },
    nbDotDieuTri: { getById, getByTongHopId, getThongTinCoBan },
  } = useDispatch();

  const onFinishModal = () => {
    form.validateFields().then((values) => {
      postRecord(
        {
          nbDotDieuTriId: id,
          maThe: values.maThe,
        },
        { ignoreMessage: true }
      )
        .then((res) => {
          if (!res.code) {
            message.success(t("tiepDon.phatHanhTheRFIDThanhCong"));
            if (id) {
              getById(id);
              getByTongHopId(id);
              getThongTinCoBan(id);
            }
            refModal.current.hide();
            form.resetFields();
          } else {
            message.error(res.message);
          }
        })
        .catch((err) =>
          message.error(err.message || t("common.xayRaLoiVuiLongThuLaiSau"))
        );
    });
  };

  const hotKeys = [
    {
      keyCode: HOTKEY.ESC,
      onEvent: () => {
        refModal.current.hide();
      },
    },
    {
      keyCode: HOTKEY.F4,
      onEvent: onFinishModal,
    },
  ];

  const onCancel = () => {
    form.resetFields();
    refModal.current.hide();
  };

  const onHuyTheAna = (id) => {
    showConfirm(
      {
        title: t("common.thongBao"),
        content: `${t("tiepDon.xacNhanBaoMatTheRFID")}`,
        cancelText: t("common.quayLai"),
        okText: t("common.xacNhan"),
        classNameOkText: "button-warning",
        showBtnOk: true,
        typeModal: "warning",
      },
      async () => {
        try {
          showLoading();
          await huyTheAna(id);
        } catch (error) {
          console.error(error);
        } finally {
          hideLoading();
        }
      }
    );
  };

  return (
    <>
      {maThe && (
        <>
          {checkRole([ROLES["TIEP_DON"].HUY_THE_RFID]) && (
            <Button
              onClick={() => {
                showConfirm(
                  {
                    title: t("tiepDon.huyTheRFID"),
                    content: t("tiepDon.xacNhanHuyTheRFID").replace(
                      "{0}",
                      tenNb
                    ),
                    cancelText: t("common.quayLai"),
                    okText: t("common.dongY"),
                    classNameOkText: "button-error",
                    showImg: true,
                    showBtnOk: true,
                    typeModal: "error",
                  },
                  () => {
                    postRecordHuy(
                      {
                        nbDotDieuTriId: id,
                        maThe: maThe,
                      },
                      { ignoreMessage: true }
                    )
                      .then((res) => {
                        message.success(t("tiepDon.huyTheRFIDThanhCong"));
                        if (id) {
                          getById(id);
                          getByTongHopId(id);
                          getThongTinCoBan(id);
                        }
                      })
                      .catch((err) => {
                        message.error(t("tiepDon.huyTheRFIDThatBai"));
                      });
                  }
                );
              }}
              rightIcon={<SVG.IcPhatHanhThe />}
            >
              {t("tiepDon.huyTheRFID")}
            </Button>
          )}
          {checkRole([ROLES["TIEP_DON"].BAO_MAT_THE_RFID]) && (
            <Button onClick={() => onHuyTheAna(id)}>
              {t("tiepDon.baoMatTheRFID")}
            </Button>
          )}
        </>
      )}

      {maThe === null && checkRole([ROLES["TIEP_DON"].PHAT_THE_RFID]) && (
        <Button
          onClick={() => refModal.current.show()}
          rightIcon={<SVG.IcPhatHanhThe />}
        >
          {t("tiepDon.phatHanhTheRFID")}
        </Button>
      )}

      <ModalTemplate
        destroyOnClose
        autoFocus={false}
        getContainer={false}
        width={640}
        ref={refModal}
        title={t("tiepDon.phatHanhTheRFID")}
        closable={false}
        onCancel={onCancel}
        hotKeys={hotKeys}
        rightTitle={
          <div>
            {tenNb} - {tuoi} {t("common.tuoi")}
          </div>
        }
        actionLeft={<Button.QuayLai onClick={onCancel} />}
        actionRight={
          <Button
            type="primary"
            minWidth={100}
            rightIcon={<SVG.IcSuccess />}
            iconHeight={15}
            onClick={onFinishModal}
          >
            {t("common.xacNhan")}
          </Button>
        }
      >
        <Main>
          <Form form={form} layout="vertical">
            <Form.Item label={t("tiepDon.maSoThe")} name="maThe">
              <Input
                autoFocus={true}
                className="input-option"
                placeholder={t("tiepDon.vuiLongNhapMaSoThe")}
                autoComplete="off"
              />
            </Form.Item>
          </Form>
        </Main>
      </ModalTemplate>
    </>
  );
};

export default PhatHanhThe;
