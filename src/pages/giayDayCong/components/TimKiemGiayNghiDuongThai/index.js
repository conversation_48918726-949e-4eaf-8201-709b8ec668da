import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { BaseSearch, Button } from "components";
import useChangeScreen from "pages/giayDayCong/config";
import { useTranslation } from "react-i18next";
import { useEnum, useStore, useConfirm } from "hooks";
import { getAllQueryString } from "hooks/useQueryString/queryString";
import moment from "moment";
import {
  ENUM,
  LOAI_GIAY_GUI_CONG_BHXH,
  ROLES,
  TRANG_THAI_DAY_CONG,
} from "constants/index";
import { checkRole } from "lib-utils/role-utils";
import { cleanEmptyData, isArray } from "utils";

const TimKiemGiayNghiDuongThai = ({ selectedRowKeys }) => {
  const { t } = useTranslation();
  const { showConfirm } = useConfirm();
  const { onChangeScreen } = useChangeScreen();
  const [listTrangThaiDayCong] = useEnum(ENUM.TRANG_THAI_DAY_CONG);
  const dataSearch = useStore("giayNghiDuongThai.dataSearch", {});

  const [state, _setState] = useState({});

  const setState = (data) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const {
    nbGiayNghiDuongThai: {
      searchNbGiayNghiDuongThaiByParams,
      dayGiayNghiDuongThai,
      clearData,
    },
  } = useDispatch();

  useEffect(() => {
    return () => {
      clearData();
    };
  }, []);

  useEffect(() => {
    const { page, size, dataSortColumn, ...queries } = getAllQueryString();

    if (queries.dsTrangThai) {
      let dsTrangThai = [];
      queries.dsTrangThai
        .split(",")
        .map((item) => dsTrangThai.push(Number(item)));
      queries.dsTrangThai = dsTrangThai;
    } else {
      queries.dsTrangThai = [TRANG_THAI_DAY_CONG.TAO_MOI];
    }

    if (queries.tuThoiGianVaoVien) {
      queries.tuThoiGianVaoVien =
        queries.tuThoiGianVaoVien === "-"
          ? undefined
          : moment(Number(queries.tuThoiGianVaoVien));
    }
    if (queries.denThoiGianVaoVien) {
      queries.denThoiGianVaoVien =
        queries.denThoiGianVaoVien === "-"
          ? undefined
          : moment(Number(queries.denThoiGianVaoVien));
    }

    setState(queries);
  }, []);

  const onDayPhieuHangLoat = () => {
    showConfirm(
      {
        title: t("giayDayCong.xacNhanGui"),
        content: t("giayDayCong.xacNhanGuiGiamDinhHangLoatLenCongBaoHiem"),
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        classNameOkText: "button-confirm",
        showBtnOk: true,
        typeModal: "warning",
      },
      async () => {
        const {
          dsTrangThai,
          maHoSo,
          maNb,
          tenNb,
          tuThoiGianVaoVien,
          denThoiGianVaoVien,
        } = dataSearch || {};

        const body = isArray(selectedRowKeys, 1)
          ? {
              dsId: selectedRowKeys,
            }
          : cleanEmptyData({
              dsTrangThai,
              maHoSo,
              maNb,
              tenNb,
              tuThoiGianVaoVien,
              denThoiGianVaoVien,
            });
        dayGiayNghiDuongThai(body).then(() => {
          searchNbGiayNghiDuongThaiByParams({});
        });
      }
    );
  };

  return (
    <BaseSearch
      cacheData={{ ...state, loaiGiayGuiCongBHXH: 4 }}
      dataInput={[
        {
          widthInput: "120px",
          placeholder: t("giayDayCong.loaiGiayGuiCongBHXH"),
          keyValueInput: "loaiGiayGuiCongBHXH",
          functionChangeInput: onChangeScreen,
          type: "select",
          listSelect: LOAI_GIAY_GUI_CONG_BHXH,
          allowClear: true,
        },
        {
          widthInput: "300px",
          placeholder: t("khth.timHoTenMaHsMaBaQuetQr"),
          keyValueInput: "maHoSo",
          isScanQR: true,
          trimStr: true,
          functionChangeInput: searchNbGiayNghiDuongThaiByParams,
          keysFlexible: [
            {
              key: "tenNb",
              type: "string",
            },
            {
              key: "maHoSo",
              type: "maHoSo",
            },
            {
              key: "maBenhAn",
              type: "number7",
            },
          ],
        },
        {
          widthInput: "180px",
          popoverMinWidth: "180px",
          title: t("common.trangThai"),
          keyValueInput: "dsTrangThai",
          functionChangeInput: ({ dsTrangThai }) => {
            searchNbGiayNghiDuongThaiByParams({
              dsTrangThai,
            });

            setState({ dsTrangThai });
          },
          type: "selectCheckbox",
          defaultValue: state.dsTrangThai,
          hasCheckAll: true,
          virtual: true,
          listSelect: listTrangThaiDayCong,
        },
        {
          widthInput: "232px",
          type: "dateOptions",
          state: state,
          setState: setState,
          functionChangeInput: (e) => {
            searchNbGiayNghiDuongThaiByParams(
              {
                tuThoiGianVaoVien: e.tuThoiGianVaoVien?.format(
                  "YYYY-MM-DD 00:00:00"
                ),
                denThoiGianVaoVien: e.denThoiGianVaoVien?.format(
                  "YYYY-MM-DD 23:59:59"
                ),
              },
              !!e.tuThoiGianVaoVien
            );
          },
          title: t("quanLyNoiTru.phongGiuong.ngayVaoVien"),
          placeholder: t("giayDayCong.chonNgayVaoVien"),
          format: "DD/MM/YYYY",
          keyValueInput: ["tuThoiGianVaoVien", "denThoiGianVaoVien"],
        },
        ...(checkRole([
          ROLES["GIAY_DAY_CONG"].GIAY_NGHI_DUONG_THAI_DAY_HANG_LOAT,
        ])
          ? [
              {
                widthInput: "200px",
                type: "addition",
                component: (
                  <div style={{ paddingLeft: 20 }}>
                    <Button
                      height={36}
                      onClick={onDayPhieuHangLoat}
                      type="primary"
                    >
                      {t("giayDayCong.action.guiGiamDinhBHXHHangLoat")}
                    </Button>
                  </div>
                ),
              },
            ]
          : []),
      ]}
    />
  );
};

export default TimKiemGiayNghiDuongThai;
