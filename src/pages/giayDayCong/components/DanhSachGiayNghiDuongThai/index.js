import React, { useEffect, useRef } from "react";
import { Main } from "./styled";
import { Tooltip, TableWrapper, Pagination } from "components";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { useEnum, useStore } from "hooks";
import { ENUM, ROLES, TRANG_THAI_DAY_CONG } from "constants/index";
import { SVG } from "assets";
import { getAllQueryString } from "hooks/useQueryString/queryString";
import { checkRole } from "lib-utils/role-utils";

const { Column, Setting } = TableWrapper;

const DanhSachGiayNghiDuongThai = ({ selectedRowKeys, setSelectedRowKeys }) => {
  const { t } = useTranslation();
  const refSettings = useRef(null);
  const [listTrangThaiDayCong] = useEnum(ENUM.TRANG_THAI_DAY_CONG);
  const { listData, totalElements, page, size, dataSortColumn, isLoading } =
    useStore(
      "nbGiayNghiDuongThai",
      {},
      {
        fields: "listData,totalElements,page,size,dataSortColumn,isLoading",
      }
    );

  const {
    nbGiayNghiDuongThai: {
      onSearch,
      onSizeChange,
      onSortChange,
      searchNbGiayNghiDuongThaiByParams,
    },
    phieuIn: { showFileEditor },
  } = useDispatch();

  useEffect(() => {
    const {
      page,
      size,
      dataSortColumn = "{}",
      ...queries
    } = getAllQueryString();

    if (queries.dsTrangThai) {
      let dsTrangThai = [];
      queries.dsTrangThai
        .split(",")
        .map((item) => dsTrangThai.push(Number(item)));
      queries.dsTrangThai = dsTrangThai;
    } else {
      queries.dsTrangThai = [TRANG_THAI_DAY_CONG.TAO_MOI];
    }

    if (queries.tuThoiGianVaoVien) {
      queries.tuThoiGianVaoVien =
        queries.tuThoiGianVaoVien === "-"
          ? undefined
          : moment(Number(queries.tuThoiGianVaoVien)).format(
              "YYYY-MM-DD 00:00:00"
            );
    }
    if (queries.denThoiGianVaoVien) {
      queries.denThoiGianVaoVien =
        queries.denThoiGianVaoVien === "-"
          ? undefined
          : moment(Number(queries.denThoiGianVaoVien)).format(
              "YYYY-MM-DD 23:59:59"
            );
    }

    searchNbGiayNghiDuongThaiByParams({
      ...queries,
    });
  }, []);

  const onClickSort = (key, value) => {
    onSortChange({ [key]: value });
  };

  const columns = [
    Column({
      title: t("common.stt"),
      width: "80px",
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
    }),
    Column({
      title: t("danhMuc.hoTen"),
      sort_key: "tenNb",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["tenNb"] || "",
      width: "200px",
      dataIndex: "tenNb",
      key: "tenNb",
      i18Name: "danhMuc.hoTen",
    }),
    Column({
      title: t("giayDayCong.ngaySinh"),
      width: "150px",
      dataIndex: "ngaySinh",
      key: "ngaySinh",
      i18Name: "giayDayCong.ngaySinh",
      render: (field, item, index) =>
        field
          ? moment(field).format(item?.chiNamSinh ? "YYYY" : "DD / MM / YYYY")
          : "",
    }),
    Column({
      title: t("common.maBenhAn"),
      sort_key: "maBenhAn",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["maBenhAn"] || "",
      width: "100px",
      dataIndex: "maBenhAn",
      key: "maBenhAn",
      i18Name: "common.maBenhAn",
    }),
    Column({
      title: t("giayDayCong.maHoSo"),
      sort_key: "maHoSo",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["maHoSo"] || "",
      width: "120px",
      dataIndex: "maHoSo",
      key: "maHoSo",
      i18Name: "giayDayCong.maHoSo",
    }),
    Column({
      title: t("giayDayCong.maTheBhyt"),
      sort_key: "maTheBhyt",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["maTheBhyt"] || "",
      width: "150px",
      dataIndex: "maTheBhyt",
      key: "maTheBhyt",
      i18Name: "giayDayCong.maTheBhyt",
    }),
    Column({
      title: t("giayDayCong.maSoBaoHiemXaHoi"),
      sort_key: "soBaoHiemXaHoi",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["soBaoHiemXaHoi"] || "",
      width: "150px",
      dataIndex: "soBaoHiemXaHoi",
      key: "soBaoHiemXaHoi",
      i18Name: "giayDayCong.maSoBaoHiemXaHoi",
    }),
    Column({
      title: t("giayDayCong.trangThai"),
      sort_key: "trangThai",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["trangThai"] || "",
      width: "150px",
      dataIndex: "trangThai",
      key: "trangThai",
      i18Name: "giayDayCong.trangThai",
      render: (item) =>
        (listTrangThaiDayCong || []).find((x) => x.id == item)?.ten || "",
    }),
    Column({
      title: t("giayDayCong.ngayChungTu"),
      sort_key: "ngayChungTu",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["ngayChungTu"] || "",
      width: "150px",
      dataIndex: "ngayChungTu",
      i18Name: "giayDayCong.ngayChungTu",
      key: "ngayChungTu",
      render: (field, item, index) =>
        field ? moment(field).format("DD/MM/YYYY HH:mm") : "",
    }),
    Column({
      title: (
        <>
          {t("common.tienIch")} <Setting refTable={refSettings} />
        </>
      ),
      width: "100px",
      align: "center",
      fixed: "right",
      ignore: true,
      render: (list, item, index) => {
        return (
          <div className="ic-action">
            {checkRole([
              ROLES["GIAY_DAY_CONG"].GIAY_NGHI_DUONG_THAI_CHI_TIET,
            ]) && (
              <Tooltip title={t("giayDayCong.action.xemChiTiet")}>
                <SVG.IcEye onClick={onViewDetail(item)} className="ic-action" />
              </Tooltip>
            )}
          </div>
        );
      },
    }),
  ];

  const onChangePage = (page) => {
    onSearch({ page: page - 1 });
  };

  const handleSizeChange = (size) => {
    onSizeChange({ size });
  };

  const onRow = (record) => {
    return {
      onClick: (event) => {
        const selection = window.getSelection();
        if (selection.type !== "Range") {
          onViewDetail(record)(event);
        }
      },
    };
  };

  const onViewDetail = (data) => (e) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    if (!checkRole([ROLES["GIAY_DAY_CONG"].GIAY_NGHI_DUONG_THAI_CHI_TIET]))
      return;
    const { id } = data;
    showFileEditor({
      phieu: { ma: "P218" },
      nbDotDieuTriId: id,
    });
  };

  return (
    <Main noPadding={true}>
      <TableWrapper
        rowSelection={{
          selectedRowKeys,
          onChange: setSelectedRowKeys,
        }}
        columns={columns}
        loading={!!isLoading}
        dataSource={listData || []}
        onRow={onRow}
        rowKey={(record) => record.id}
        tableName="table_GIAYDAYCONG_GiayNghiDuongThai"
        ref={refSettings}
      />
      {!!totalElements && (
        <Pagination
          listData={listData || []}
          onChange={onChangePage}
          current={page + 1}
          pageSize={size}
          total={totalElements}
          onShowSizeChange={handleSizeChange}
          pageSizeOptions={[10, 20, 50, 100, 200]}
        />
      )}
    </Main>
  );
};

export default DanhSachGiayNghiDuongThai;
