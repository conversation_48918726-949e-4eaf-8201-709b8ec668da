import React, { useState } from "react";
import { Page } from "components";
import { useTranslation } from "react-i18next";
import TimKiemGiayNghiDuongThai from "../components/TimKiemGiayNghiDuongThai";
import DanhSachGiayNghiDuongThai from "../components/DanhSachGiayNghiDuongThai";
import { Main } from "./styled";

const GiayNghiDuongThai = (props) => {
  const { t } = useTranslation();
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  return (
    <Page
      breadcrumb={[
        {
          title: t("giayDayCong.danhSachGiayDayCong"),
          link: "/danh-sach-giay-day-cong",
        },
        {
          title: t("giayDayCong.danhSachGiayChungNhanNghiDuongThai"),
          link: "/giay-day-cong/giay-chung-nhan-nghi-duong-thai",
        },
      ]}
      title={t("giayDayCong.danhSachGiayChungNhanNghiDuongThai")}
    >
      <Main>
        <TimKiemGiayNghiDuongThai selectedRowKeys={selectedRowKeys} />
        <DanhSachGiayNghiDuongThai
          selectedRowKeys={selectedRowKeys}
          setSelectedRowKeys={setSelectedRowKeys}
        />
      </Main>
    </Page>
  );
};

export default GiayNghiDuongThai;
