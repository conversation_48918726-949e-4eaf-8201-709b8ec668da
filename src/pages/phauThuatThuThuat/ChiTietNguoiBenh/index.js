import React, {
  useMemo,
  useRef,
  memo,
  useEffect,
  useState,
  Fragment,
} from "react";
import { Menu, message, Space, Empty } from "antd";
import {
  DatePicker,
  Radio,
  Popover,
  Tooltip,
  Dropdown,
  Button,
  Card,
  Tabs,
  InputTimeout,
  ModalSignPrint,
  ThongTinBenhNhan,
  AlertMessage,
} from "components";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useParams } from "react-router-dom";
import { Main, MainPage, GlobalStyle } from "./styled";
import ThongTinNguoiThucHien from "./ThongTinNguoiThucHien";
import ThongTinPTTT from "./ThongTinPTTT";
import ChiDinhDichVuKyThuat from "./ChiDinhDichVuKyThuat";
import ChiDinhThuoc from "./ChiDinhThuoc";
import ChiDinhVatTu from "./ChiDinhVatTu";
import ThongTinPhongGiuong from "./ThongTinPhongGiuong";
import moment from "moment";
import Icon from "@ant-design/icons";
import ChiDinhNgoaiDieuTri from "./ChiDinhNgoaiDieuTri";
import ChiDinhMau from "./ChiDinhMau";
import useThongTinNb from "./hook/useThongTinNb";
import {
  useCache,
  useLoading,
  useQueryString,
  useStore,
  useThietLap,
  useListAll,
  useConfirm,
  useQueryAll,
} from "hooks";
import printProvider, { printJS } from "data-access/print-provider";
import { flatten } from "lodash";
import nbDvKyThuatProvider from "data-access/nb-dv-ky-thuat-provider";
import ChiDinhHoaChat from "./ChiDinhHoaChat";
import { transformObjToQueryString } from "hooks/useQueryString/queryString";
import {
  DOI_TUONG_KCB,
  LIST_PHIEU_CHON_TIEU_CHI,
  LOAI_DICH_VU,
  THIET_LAP_CHUNG,
  MA_BIEU_MAU_EDITOR,
  CACHE_KEY,
  LIST_PHIEU_CHON_TIEU_CHI_IN_THEO_SO_PHIEU,
  DATA_MODE_DS_NB,
  ROLES,
  TRANG_THAI_DICH_VU,
  TRANG_THAI_HOAN,
  LOAI_IN,
  TRANG_THAI_NB,
} from "constants/index";
import { setQueryStringValue } from "hooks/useQueryString/queryString";
import TabBox from "../TabBox";
import { SVG } from "assets";
import { containText, openInNewTab } from "utils";
import { infoPatients } from "../DanhSachNguoiBenh";
import { checkIsPhieuKySo } from "utils/phieu-utils";
import { checkRole } from "lib-utils/role-utils";
import { createUniqueText } from "lib-utils";
import { showError } from "utils/message-utils";
import ChiDinhPhacDoDieuTri from "./ChiDinhPhacDoDieuTri";
import { query } from "redux-store/stores";
import ThuocVatTu from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ToDieuTri/ChiDinhDichVu/ThuocVatTu";
import DanhSachBenhNhanSidePanel from "./DanhSachBenhNhanSidePanel";
import DanhSachBenhNhan from "./DanhSachBenhNhan";

import ModalChonTieuChi from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ModalChonTieuChi";
import ModalChonTieuChiBangKe from "components/ModalChonTieuChiBangKe";
import ModalPhieuChungNhanPTTT from "./ModalPhieuChungNhanPTTT";
import ModalChonPhieuCongKhaiVtyt from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ToDieuTri/modals/ModalChonPhieuCongKhaiVtyt";
import ModalInPhieuHuyThuoc from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalInPhieuHuyThuoc";
import ModalInPhieuCamDoanChapNhanPTTT from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalInPhieuCamDoanChapNhanPTTT";
import ModalInPhieu from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalInPhieuTomTatBenhAn";
import ModalChonPhieuCanThiepDuoc from "pages/kho/DuyetDuocLamSang/ChiTietDuyetDuocLamSang/modal/ModalChonPhieuCanThiepDuoc";
import ModalInChiDinhTheoDV from "pages/khamBenh/components/StepWrapper/ModalInChiDinhTheoDV";
import ModalChuyenPhieuLinhBu from "pages/chanDoanHinhAnh/CDHATDCN/ChiTietDichVu/ModalChuyenPhieuLinhBu";
import ModalPhieuPHCN from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ToDieuTri/modals/ModalPhieuPHCN";
import ModalInBangKiemNbTruocPhauThuat from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalInBangKiemNbTruocPhauThuat";
import ModalPhieuBanGiaoNguoiBenhSauPhauThuat from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalPhieuBanGiaoNguoiBenhSauPhauThuat";
import ModalInPhieuChamSoc from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalInPhieuChamSoc";

const LIST_FILE_SHOW_MODAL = {
  P178: 40,
  P727: 70,
};

const ChiTietNguoiBenh = ({ history }) => {
  const [tab] = useQueryString("tab", "0");
  const { t } = useTranslation();
  const { id } = useParams();
  const { state: locationState } = useLocation();
  const refModalInChiDinhTheoDV = useRef(null);
  const refModalChonTieuChiBangKe = useRef(null);
  const refModalChonTieuChi = useRef();
  const refModalPhoeuChungNhanPTTT = useRef(null);
  const refModalSignPrint = useRef(null);
  const refModalChonPhieuCongKhaiVtyt = useRef(null);
  const refModalInPhieuHuyThuoc = useRef(null);
  const refDanhSachBenhNhanSidePanel = useRef(null);
  const refModalInPhieuCamDoanChapNhanPTTT = useRef(null);
  const refModalInPhieu = useRef(null);
  const refModalChonPhieuCanThiepDuoc = useRef(null);
  const refChuyenPhieuLinhBu = useRef(null);
  const refModalPhieuPHCN = useRef(null);
  const refModalInBangKiemNbTruocPhauThuat = useRef(null);
  const refModalPhieuBanGiaoNguoiBenhSauPhauThuat = useRef(null);
  const refModalInPhieuChamSoc = useRef(null);

  const { showConfirm } = useConfirm();

  const [dataNHOM_GIAI_PHAU_BENH] = useThietLap(
    THIET_LAP_CHUNG.NHOM_GIAI_PHAU_BENH
  );
  const [BAT_BUOC_NHAP_LOAI_PTTT] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_NHAP_LOAI_PTTT
  );
  const [BANG_KE_IN_THEO_KHOA_CHI_DINH] = useThietLap(
    THIET_LAP_CHUNG.BANG_KE_IN_THEO_KHOA_CHI_DINH
  );
  const [MA_NHOM_DICH_VU_CAP1_PT] = useThietLap(
    THIET_LAP_CHUNG.MA_NHOM_DICH_VU_CAP1_PT
  );
  const [XML130_TRUONG_BAT_BUOC] = useThietLap(
    THIET_LAP_CHUNG.XML130_TRUONG_BAT_BUOC
  );
  const [dataTAB_VAT_TU_THEO_THUOC] = useThietLap(
    THIET_LAP_CHUNG.TAB_VAT_TU_THEO_THUOC
  );

  const [BAT_BUOC_NHAP_TRUONG_LOAI_MO] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_NHAP_TRUONG_LOAI_MO
  );
  const [dataKE_THUOC_VAT_TU_KHONG_PHU_THUOC_TRANG_THAI_DICH_VU, loadFinish] =
    useThietLap(
      THIET_LAP_CHUNG.KE_THUOC_VAT_TU_KHONG_PHU_THUOC_TRANG_THAI_DICH_VU
    );

  const [dataMAC_DINH_IN_DV_CHUA_THANH_TOAN_PHIEU_CHI_DINH_TONG_HOP] =
    useThietLap(
      THIET_LAP_CHUNG.MAC_DINH_IN_DV_CHUA_THANH_TOAN_PHIEU_CHI_DINH_TONG_HOP,
      "FALSE"
    );
  const [dataHIEN_THI_CAC_TRUONG_CHO_PT_MAT] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_CAC_TRUONG_CHO_PT_MAT
  );
  const [
    dataKHOA_CHI_DINH_DICH_VU_TU_MAN_HINH_PTTT,
    loadFinishKHOA_CHI_DINH_DICH_VU_TU_MAN_HINH_PTTT,
  ] = useThietLap(THIET_LAP_CHUNG.KHOA_CHI_DINH_DICH_VU_TU_MAN_HINH_PTTT);

  const { id: authId } = useStore("auth.auth", {}, { field: "id" });

  const [valuePhieuChiDinh, setValuePhieuChiDinh] = useCache(
    "",
    CACHE_KEY.DATA_PTTT_OPTION_IN_PHIEU_CHI_DINH,
    dataMAC_DINH_IN_DV_CHUA_THANH_TOAN_PHIEU_CHI_DINH_TONG_HOP?.eval() ? 4 : 2,
    false
  );
  const [valuePhieuChiDinhTH, setValuePhieuChiDinhTH] = useCache(
    "",
    CACHE_KEY.DATA_PTTT_OPTION_IN_PHIEU_CHI_DINH_TONG_HOP,
    dataMAC_DINH_IN_DV_CHUA_THANH_TOAN_PHIEU_CHI_DINH_TONG_HOP?.eval() ? 4 : 2,
    false
  );

  const [modeDsNb, setModeDsNb] = useCache(
    authId,
    CACHE_KEY.DATA_MODE_DS_NB_PTTT,
    DATA_MODE_DS_NB.DRAWER,
    false
  );
  const {
    pttt: {
      onSearchDanhSachPhauThuatThuThuat,
      getById,
      saveThongTinPTTT,
      saveNguoiThucHien,
      getNguoiThucHien,
      updateData,
    },
    khamBenh: { getTatCaGiayChiDinh },
    khoa: { getKhoaTheoTaiKhoan },
    phuongPhapVoCam: { getListAllPhuongPhapVoCam },
    nhanVien: { getListAllPhauThuatVien },
    phieuIn: {
      getListPhieu,
      getFilePhieuIn,
      showFileEditor,
      getDataDanhSachPhieu,
    },
    thietLapChonKho: { getListThietLapChonKhoTheoTaiKhoan },
    chiDinhKhamBenh: { updateConfigData },
    nbDvKho: { getNbTonTaiTraKho },
    chiDinhDichVuKho: { getListDichVuThuoc },
    chiDinhHoaChat: { getListDichVuHoaChat },
    quanLyNoiTru: { getTieuChi },
    chiDinhGoiPTTT: { getChiTietDvTrongGoi, updateDichVuNbGoiPTTT },
  } = useDispatch();

  const { showLoading, hideLoading } = useLoading();

  const { nbTonTaiVatTu } = useSelector((state) => state.nbDvKho);
  const chiTietPhauThuat = useStore("pttt.chiTietPhauThuat", {});
  const dataChange = useStore("pttt.dataChange", false);
  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan");
  const { listFilter, disabledSave, disabledSave2 } = useSelector(
    (state) => state.pttt
  );
  const [listAllNhomDichVuCap1] = useListAll("nhomDichVuCap1", {}, true);

  const queryPtttCungCaKip = useQueryAll(
    query.pttt.queryDsPtttCungCa({
      params: {
        id,
        nbDotDieuTriId: chiTietPhauThuat.nbDotDieuTriId,
      },
      enabled: !!chiTietPhauThuat.nbDotDieuTriId,
      persist: false,
      cacheTime: 0,
      staleTime: 0,
    })
  );

  const { data: listAllPtttCungCaKip } = queryPtttCungCaKip;

  const [thongTinBenhNhan] = useThongTinNb();
  const [state, _setState] = useState({
    activeKey: tab,
    collapse: true,
    popoverVisible: false,
    phieuIn: "",
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    if (chiTietPhauThuat && Object.keys(chiTietPhauThuat)?.length) {
      getListDichVuThuoc({
        nbDotDieuTriId: chiTietPhauThuat.nbDotDieuTriId,
        chiDinhTuDichVuId: chiTietPhauThuat?.id,
        dsTrangThaiHoan: [
          TRANG_THAI_HOAN.THUONG,
          TRANG_THAI_HOAN.CHO_DUYET_HOAN,
          TRANG_THAI_HOAN.CHO_DUYET_DOI,
        ],
        chiDinhTuLoaiDichVu: LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
      });
      getListDichVuHoaChat({
        nbDotDieuTriId: chiTietPhauThuat.nbDotDieuTriId,
        chiDinhTuDichVuId: chiTietPhauThuat?.id,
        dsTrangThaiHoan: [
          TRANG_THAI_HOAN.THUONG,
          TRANG_THAI_HOAN.CHO_DUYET_HOAN,
          TRANG_THAI_HOAN.CHO_DUYET_DOI,
        ],
        chiDinhTuLoaiDichVu: LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
      });
    }
  }, [chiTietPhauThuat?.id]);

  const canEdit = useMemo(() => {
    const { trangThai, trangThaiHoan } = chiTietPhauThuat || {};
    const { trangThaiNb } = thongTinCoBan || {};

    const isDisabled =
      [25, 43, 155].includes(trangThai) || trangThaiHoan === 40;

    const isEditWithRole =
      checkRole([ROLES["PHAU_THUAT_THU_THUAT"].KE_DICH_VU]) &&
      trangThai === TRANG_THAI_DICH_VU.DA_CO_KET_QUA && // 155
      trangThaiHoan === 0 &&
      [5, 10, 20, 30, 40, 45, 50, 200].includes(trangThaiNb);

    return isEditWithRole || !isDisabled;
  }, [chiTietPhauThuat, thongTinCoBan]);

  const canEditThuocVatTu = useMemo(() => {
    if (checkRole([ROLES["PHAU_THUAT_THU_THUAT"].KE_THUOC_VAT_TU])) return true;
    if (!loadFinish) return false;
    if (dataKE_THUOC_VAT_TU_KHONG_PHU_THUOC_TRANG_THAI_DICH_VU?.eval()) {
      return thongTinCoBan?.trangThaiNb < TRANG_THAI_NB.DA_RA_VIEN;
    }
    return canEdit;
  }, [
    canEdit,
    dataKE_THUOC_VAT_TU_KHONG_PHU_THUOC_TRANG_THAI_DICH_VU,
    loadFinish,
    thongTinCoBan,
  ]);

  const renderText = () => {
    return (
      <Tooltip
        placement="topLeft"
        overlayInnerStyle={{ width: "fit-content" }}
        overlayStyle={{ maxWidth: "700px" }}
        title={`${
          chiTietPhauThuat?.maDichVu ? `(${chiTietPhauThuat?.maDichVu})` : ""
        } ${chiTietPhauThuat?.tenDichVu || ""}`}
      >
        {`${t("common.chiDinhVatTu").toUpperCase()} ${
          chiTietPhauThuat?.maDichVu ? `- (${chiTietPhauThuat?.maDichVu})` : ""
        } ${chiTietPhauThuat?.tenDichVu || ""}`}
      </Tooltip>
    );
  };

  const listTabs = [
    {
      name: t("pttt.thongTinPTTT"),
      component: <ThongTinPTTT listAllPtttCungCaKip={listAllPtttCungCaKip} />,
      iconTab: <SVG.IcThongTinPttt />,
      isShow: true,
      key: "0",
    },
    {
      name: t("pttt.thongTinNguoiThucHien"),
      component: <ThongTinNguoiThucHien />,
      iconTab: <SVG.IcThongTinNguoiThucHien />,
      isShow: true,
      key: "1",
    },
    {
      name: t("common.chiDinhDichVu"),
      component: <ChiDinhDichVuKyThuat canEdit={canEdit} />,
      iconTab: <SVG.IcChiDinhDichVu />,
      isShow: true,
      key: "2",
    },
    {
      name: t("common.chiDinhThuoc"),
      component: <ChiDinhThuoc canEdit={canEditThuocVatTu} />,
      iconTab: <SVG.IcDonThuoc color={"var(--color-gray-primary)"} />,
      isShow: true,
      key: "3",
    },
    {
      name: t("common.chiDinhVatTu"),
      component: <ChiDinhVatTu canEdit={canEditThuocVatTu} />,
      iconTab: <SVG.IcVatTu />,
      isShow: true,
      title: renderText(),
      keyTab: "chiDinhVatTu",
      key: "4",
    },
    ...(dataTAB_VAT_TU_THEO_THUOC?.eval()
      ? [
          {
            name: t("common.thuocVatTu"),
            component: <ThuocVatTu canEdit={canEditThuocVatTu} />,
            iconTab: <SVG.IcDonThuoc color={"var(--color-gray-primary)"} />,
            isShow: true,
            key: "9",
          },
        ]
      : []),
    {
      name: t("pttt.chiDinhMau"),
      component: <ChiDinhMau canEdit={canEdit} />,
      iconTab: <SVG.IcMau />,
      isShow: true,
      key: "5",
    },
    {
      name: t("common.chiDinhHoaChat"),
      component: <ChiDinhHoaChat canEdit={canEdit} />,
      iconTab: <SVG.IcHoaChat />,
      isShow: true,
      key: "6",
    },
    {
      name: t("common.chiDinhNgoaiDieuTri"),
      component: (
        <ChiDinhNgoaiDieuTri
          canEdit={canEdit}
          queryPtttCungCaKip={queryPtttCungCaKip}
        />
      ),
      iconTab: <SVG.IcDichVu />,
      isShow: true,
      key: "7",
    },
    {
      name: t("common.thongTinPhongGiuong"),
      component: <ThongTinPhongGiuong />,
      iconTab: <SVG.IcPhongGiuong />,
      isShow: true,
      key: "8",
    },
    {
      name: t("title.phacDoDieuTri"),
      iconTab: <SVG.IcPhacDoDieuTri2 />,
      component: <ChiDinhPhacDoDieuTri />,
      key: "10",
      isShow: true,
    },
  ];

  useEffect(() => {
    if (tab) {
      setState({ activeKey: tab });
    }
  }, [tab]);

  const parseKhoaChiDinhDvTuMHPTTT = () => {
    let isKhoaChiDinhDvTuMHPTTT = false;
    let dsMaDvKhoaThucHien = [];

    if (!dataKHOA_CHI_DINH_DICH_VU_TU_MAN_HINH_PTTT) {
    } else {
      const _strArr = dataKHOA_CHI_DINH_DICH_VU_TU_MAN_HINH_PTTT.split("/");
      if (_strArr.length == 1) {
        isKhoaChiDinhDvTuMHPTTT = _strArr[0]?.eval();
      } else if (_strArr.length == 2) {
        isKhoaChiDinhDvTuMHPTTT = _strArr[0]?.eval();
        dsMaDvKhoaThucHien = _strArr[1].split(",");
      }
    }
    return { isKhoaChiDinhDvTuMHPTTT, dsMaDvKhoaThucHien };
  };

  useEffect(() => {
    if (
      loadFinishKHOA_CHI_DINH_DICH_VU_TU_MAN_HINH_PTTT &&
      chiTietPhauThuat.id &&
      thongTinBenhNhan &&
      Object.keys(thongTinBenhNhan)?.length
    ) {
      const { isKhoaChiDinhDvTuMHPTTT, dsMaDvKhoaThucHien } =
        parseKhoaChiDinhDvTuMHPTTT();

      updateConfigData({
        configData: {
          chiDinhTuDichVuId: chiTietPhauThuat.id,
          nbDotDieuTriId: chiTietPhauThuat.nbDotDieuTriId,
          nbThongTinId: chiTietPhauThuat.nbThongTinId,
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
          dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
          khoaChiDinhId: chiTietPhauThuat.khoaThucHienId,
          thongTinNguoiBenh: thongTinBenhNhan,
          isPhauThuat: true,
          trangThaiKham: "",
          canLamSang: true,
          phongThucHienId: chiTietPhauThuat.phongThucHienId,
          doiTuongKcb: thongTinBenhNhan.doiTuongKcb,
          dsDinhMucThuocChiTiet: chiTietPhauThuat.dsDinhMucThuocChiTiet || [],
          dsDinhMucVtytChiTiet: chiTietPhauThuat.dsDinhMucVtytChiTiet || [],
          thoiGianThucHien: chiTietPhauThuat.thoiGianTiepNhan,
          khoaChiDinhMHPTTTId: chiTietPhauThuat.khoaChiDinhId,
          isKhoaChiDinhDvTuMHPTTT,
          dsMaDvKhoaThucHien,
        },
      });
    }
  }, [
    chiTietPhauThuat,
    thongTinBenhNhan,
    loadFinishKHOA_CHI_DINH_DICH_VU_TU_MAN_HINH_PTTT,
  ]);

  useEffect(() => {
    let param = { active: true, page: "", size: "" };
    getKhoaTheoTaiKhoan({ ...param });
    getListAllPhuongPhapVoCam({ ...param });
    getListAllPhauThuatVien({
      dsMaThietLapVanBang: THIET_LAP_CHUNG.BAC_SI,
      ...param,
    });
    return () => {
      updateConfigData({
        configData: null,
      });
    };
  }, []);

  useEffect(() => {
    if (chiTietPhauThuat?.id) {
      getNbTonTaiTraKho({
        nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
        khoaChiDinhId: chiTietPhauThuat.khoaThucHienId,
      });
    }
  }, [chiTietPhauThuat]);

  useEffect(() => {
    if (id) {
      getNguoiThucHien(id);
      getById(id).then((data) => {
        onSearchDanhSachPhauThuatThuThuat({
          size: 500,
          dataSearch: {
            nbDotDieuTriId: data?.nbDotDieuTriId,
            khoaThucHienId: data?.khoaThucHienId,
            dsPhongThucHienId: data?.phongThucHienId,
            dsTrangThai: [],
            dsTrangThaiHoan: [0, 10, 20],
          },
        });
      });
      return () => {
        updateData({ chiTietPhauThuat: null, dataChange: false });
      };
    }
  }, [id]);

  const onChange = (tab) => {
    if (
      (state.activeKey == "0" || state.activeKey == "1") &&
      !isDisableSave &&
      dataChange
    ) {
      onSaveThongTinPTTT();
      updateData({ dataChange: false });
    }
    setState({ activeKey: tab });
    setQueryStringValue("tab", tab);
  };

  useEffect(() => {
    if (
      thongTinBenhNhan?.id &&
      chiTietPhauThuat?.id &&
      thongTinBenhNhan?.doiTuongKcb &&
      state.activeKey
    ) {
      const loaiDichVuMap = {
        3: LOAI_DICH_VU.THUOC,
        4: LOAI_DICH_VU.VAT_TU,
        5: LOAI_DICH_VU.CHE_PHAM_MAU,
        6: LOAI_DICH_VU.HOA_CHAT,
      };

      const payload = {
        khoaNbId: thongTinBenhNhan?.khoaNbId,
        khoaChiDinhId: chiTietPhauThuat?.khoaThucHienId,
        doiTuong: thongTinBenhNhan?.doiTuong,
        loaiDoiTuongId: thongTinBenhNhan?.loaiDoiTuongId,
        capCuu: thongTinBenhNhan?.capCuu,
        phongId: chiTietPhauThuat?.phongThucHienId,
        noiTru: ![
          DOI_TUONG_KCB.NGOAI_TRU,
          DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
          DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
        ].includes(thongTinBenhNhan?.doiTuongKcb),
        canLamSang: true,
      };

      if (loaiDichVuMap[state.activeKey]) {
        getListThietLapChonKhoTheoTaiKhoan({
          ...payload,
          loaiDichVu: loaiDichVuMap[state.activeKey],
        });
      }

      if (state.activeKey === "9") {
        [LOAI_DICH_VU.THUOC, LOAI_DICH_VU.VAT_TU].forEach((dv) =>
          getListThietLapChonKhoTheoTaiKhoan({ ...payload, loaiDichVu: dv })
        );
      }
    }
  }, [
    thongTinBenhNhan?.id,
    chiTietPhauThuat?.id,
    thongTinBenhNhan?.doiTuongKcb,
    state.activeKey,
  ]);

  const checkDichVuTrongGoi = () => {
    if (chiTietPhauThuat?.nbGoiPtTtId) {
      getChiTietDvTrongGoi({
        nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
        nbGoiPtTtId: chiTietPhauThuat?.nbGoiPtTtId,
      }).then((response) => {
        let updateData = [];
        (response?.data || []).forEach((element) => {
          if (
            moment(element?.thoiGianThucHien).isBefore(
              moment(chiTietPhauThuat?.thoiGianThucHien)
            )
          ) {
            updateData.push({ id: element.id, nbGoiPtTtId: null });
          }
        });

        if (updateData.length) {
          updateDichVuNbGoiPTTT(updateData);
        }
      });
    }
  };

  const isCheckNhomDvCap1 = useMemo(
    () =>
      listAllNhomDichVuCap1.find(
        (x) => x.id === chiTietPhauThuat.nhomDichVuCap1Id
      )?.ma === MA_NHOM_DICH_VU_CAP1_PT,
    [listAllNhomDichVuCap1, chiTietPhauThuat, MA_NHOM_DICH_VU_CAP1_PT]
  );

  const onSaveThongTinPTTT = () => {
    if (!chiTietPhauThuat.thoiGianThucHien) {
      message.error(t("pttt.vuiLongNhapThoiGianBatDauPttt"));
      return;
    }
    if (!chiTietPhauThuat.tyLeTtDv) {
      message.error(t("pttt.vuiLongNhapTyLeThanhToan"));
      return;
    }
    if (!chiTietPhauThuat.dichVuId) {
      message.error(t("pttt.vuiLongNhapTenDichVu"));
      return;
    }

    if (chiTietPhauThuat.phanLoai !== 0 && !chiTietPhauThuat.phanLoai) {
      message.error(t("pttt.chuaNhapPhanLoaiPttt"));
      return;
    }

    if (BAT_BUOC_NHAP_LOAI_PTTT?.eval() && !chiTietPhauThuat.loaiPtTt) {
      message.error(t("pttt.chuaNhapLoaiPttt"));
      return;
    }

    if (
      !chiTietPhauThuat.viTriPhauThuat &&
      dataHIEN_THI_CAC_TRUONG_CHO_PT_MAT?.eval()
    ) {
      message.error(t("pttt.vuiLongChonViTriPhauThuat"));
      return;
    }

    if (
      BAT_BUOC_NHAP_TRUONG_LOAI_MO?.eval() &&
      isCheckNhomDvCap1 &&
      !chiTietPhauThuat.loaiMo
    ) {
      message.error(t("pttt.chuaNhapLoaiMo"));
      return;
    }

    if (
      isCheckNhomDvCap1 &&
      XML130_TRUONG_BAT_BUOC?.eval() &&
      !chiTietPhauThuat.phuongPhapVoCamId
    ) {
      message.error(t("pttt.chuaNhapPhuongPhapVoCam"));
      return;
    }

    if (state.activeKey === "0") {
      showLoading();
      saveThongTinPTTT()
        .then((res) => {
          if (res && res.code === 0) {
            getById(id).then((data) => {
              checkDichVuTrongGoi();

              onSearchDanhSachPhauThuatThuThuat({
                size: 500,
                dataSearch: {
                  nbDotDieuTriId: data?.nbDotDieuTriId,
                  khoaThucHienId: data?.khoaThucHienId,
                  dsPhongThucHienId: data?.phongThucHienId,
                  dsTrangThai: [],
                  dsTrangThaiHoan: [0, 10, 20],
                },
              });
            });
            message.success(t("common.capNhatThanhCong"));
          }
        })
        .finally(() => hideLoading());
    } else if (state.activeKey === "1") {
      showLoading();
      saveNguoiThucHien()
        .then((res) => {
          if (res && res.code === 0) {
            message.success(t("common.capNhatThanhCong"));
          }
        })
        .catch((e) => {
          if (e?.code === 8429) {
            showConfirm(
              {
                title: t("common.thongBao"),
                content: e?.message,
                cancelText: t("common.huy"),
                okText: t("common.dongY"),
                classNameOkText: "button-warning",
                showBtnOk: checkRole([
                  ROLES["PHAU_THUAT_THU_THUAT"]
                    .BO_QUA_CANH_BAO_THOI_GIAN_THUC_HIEN,
                ])
                  ? true
                  : false,
              },
              () => {
                saveNguoiThucHien({ boQuaCheckNguoiThucHien: true }).then(
                  (res) => {
                    if (res && res.code === 0) {
                      message.success(t("common.capNhatThanhCong"));
                    }
                  }
                );
              }
            );
          }
        })
        .finally(() => hideLoading());
    }
  };

  const editThongTinNguoiThucHien = useMemo(() => {
    const isDisabled =
      [TRANG_THAI_DICH_VU.DA_CO_KET_QUA].includes(chiTietPhauThuat.trangThai) ||
      chiTietPhauThuat?.trangThaiHoan === TRANG_THAI_HOAN.KHONG_THUC_HIEN;

    const isEditWithRole =
      checkRole([
        ROLES["PHAU_THUAT_THU_THUAT"].SUA_THONG_TIN_NGUOI_THUC_HIEN,
      ]) &&
      chiTietPhauThuat?.trangThai === TRANG_THAI_DICH_VU.DA_CO_KET_QUA && // 155
      chiTietPhauThuat?.trangThaiHoan === TRANG_THAI_HOAN.THUONG;

    return isEditWithRole || !isDisabled;
  }, [chiTietPhauThuat, thongTinCoBan]);

  const isDisableSave = useMemo(() => {
    const disable = [TRANG_THAI_DICH_VU.DA_CO_KET_QUA].includes(
      chiTietPhauThuat?.trangThai
    );

    if (state.activeKey === "0")
      return (
        !checkRole([
          ROLES.PHAU_THUAT_THU_THUAT
            .CHINH_SUA_THONG_TIN_PHAU_THUAT_SAU_KHI_HOAN_THANH_RA_VIEN,
        ]) &&
        (disabledSave ||
          disable ||
          chiTietPhauThuat?.trangThaiHoan === TRANG_THAI_HOAN.KHONG_THUC_HIEN)
      );

    if (state.activeKey === "1") return !editThongTinNguoiThucHien;

    return disable;
  }, [
    disabledSave,
    chiTietPhauThuat?.trangThaiHoan,
    chiTietPhauThuat?.trangThai,
    state.activeKey,
    editThongTinNguoiThucHien,
  ]);

  const onFilterDate = (date) => {
    if (date) {
      onSearchDanhSachPhauThuatThuThuat({
        size: 500,
        dataSearch: {
          nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
          tuThoiGianThucHien: date[0].format("YYYY-MM-DD HH:mm:ss"),
          denThoiGianThucHien: date[1].format("YYYY-MM-DD HH:mm:ss"),
          khoaThucHienId: chiTietPhauThuat?.khoaThucHienId,
          dsPhongThucHienId: chiTietPhauThuat?.phongThucHienId,
          dsTrangThai: [],
          dsTrangThaiHoan: [0, 10, 20],
        },
      });
    } else {
      onSearchDanhSachPhauThuatThuThuat({
        size: 500,
        dataSearch: {
          nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
          khoaThucHienId: chiTietPhauThuat?.khoaThucHienId,
          dsPhongThucHienId: chiTietPhauThuat?.phongThucHienId,
          dsTrangThai: [],
          dsTrangThaiHoan: [0, 10, 20],
        },
      });
    }
  };

  const onSelectDv = (item) => () => {
    history.push({
      pathname: `/phau-thuat-thu-thuat/chi-tiet-phau-thuat/${item.id}`,
      search: `?tab=${state.activeKey}`,
    });
  };

  const getListPhieuInGiayTo = () => {
    getListPhieu({
      nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
      maManHinh: "009",
      maViTri: "00901",
      dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
      chiDinhTuDichVuId: chiTietPhauThuat?.id,
    }).then((listPhieu) => {
      setState({ listPhieu });
    });
  };

  useEffect(() => {
    if (chiTietPhauThuat?.id) {
      getListPhieuInGiayTo();
    }
  }, [chiTietPhauThuat?.id]);

  const onPrintPhieu = (item) => async () => {
    let mhParams = {};
    if (checkIsPhieuKySo(item)) {
      //nếu là ký số thì lấy các thông tin về lịch sử ký + tên chân ký + báo cáo id
      mhParams = {
        nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
        maManHinh: "009",
        maViTri: "00901",
        dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
        chiDinhTuDichVuId: chiTietPhauThuat?.id,
        kySo: true,
        maPhieuKy: item.ma,
        baoCaoId: item.baoCaoId,
      };
    }
    if (item?.type == "editor") {
      if (LIST_FILE_SHOW_MODAL[item.ma]) {
        if (
          BANG_KE_IN_THEO_KHOA_CHI_DINH?.toLowerCase() === "true" &&
          ["P178", "P727"].includes(item.ma)
          //P178: Bảng kê chi tiết KCB nội trú
          //P727: Bảng kê chi tiết chi phí phẫu thuật (Viện phí, Tiện ích, Hao phí)
        ) {
          refModalChonTieuChiBangKe.current.show(
            {
              khoaNbId: [thongTinBenhNhan?.khoaNbId],
              khoaChiDinhId:
                item.ma === "P727" && chiTietPhauThuat?.khoaChiDinhId,
            },
            (values) => {
              const data = {
                id: chiTietPhauThuat?.nbDotDieuTriId,
                ma: item.ma,
                maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                  ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                  : "",
                loai: LIST_FILE_SHOW_MODAL[item.ma],
                loaiIn:
                  item.dsBaoCao && item.dsBaoCao.length > 0
                    ? item.dsBaoCao[0]?.loaiIn
                    : null,
                tuThoiGianThucHien: values.tuThoiGianThucHien
                  ? moment(values.tuThoiGianThucHien).format()
                  : null,
                denThoiGianThucHien: values.denThoiGianThucHien
                  ? moment(values.denThoiGianThucHien).format()
                  : null,
                dsKhoaChiDinhId: values.dsKhoaChiDinhId,

                nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
                maManHinh: "009",
                maViTri: "00901",
              };

              window.refModalViewPhieu.current &&
                window.refModalViewPhieu.current.show(data);
            }
          );
        } else {
          window.refModalViewPhieu.current &&
            window.refModalViewPhieu.current.show({
              id: chiTietPhauThuat?.nbDotDieuTriId,
              ma: item.ma,
              maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                : "",
              loai: LIST_FILE_SHOW_MODAL[item.ma],
              loaiIn:
                item.dsBaoCao && item.dsBaoCao.length > 0
                  ? item.dsBaoCao[0]?.loaiIn
                  : null,

              nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
              maManHinh: "009",
              maViTri: "00901",
            });
        }
      } else {
        if (
          [
            "P519",
            "P550",
            "P733",
            "P730",
            "P739",
            "P741",
            "P760",
            "P765",
            "P771",
            "P780",
            "P951",
            "P1060",
            "P1067",
            "P1003",
          ].includes(item.ma)
        ) {
          showFileEditor({
            phieu: item,
            nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
            id: id,
            chiDinhTuDichVuId: chiTietPhauThuat?.id,
            chiDinhTuLoaiDichVu: LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
            ma: item.ma,
            maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
              ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
              : "",
            mhParams,
          });
        } else if (
          item.ma !== "P111" &&
          LIST_PHIEU_CHON_TIEU_CHI.includes(item.ma)
        ) {
          const dataSearch = {
            nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
            dataNHOM_GIAI_PHAU_BENH,
          };
          let dsTieuChi = await getTieuChi({
            maBaoCao: item.ma,
            dataSearch:
              item.ma === "P137"
                ? {
                    nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
                    chiDinhTuDichVuId: chiTietPhauThuat?.id,
                    dsChiDinhTuLoaiDichVu: 40,
                  }
                : dataSearch,
            dsSoPhieu: item.dsSoPhieu,
          });
          dsTieuChi = (dsTieuChi || []).map((item) => {
            return {
              id: item.id,
              ten: (
                <span
                  title={`${item.maDichVu}-${item.tenDichVu}-${moment(
                    item.thoiGianThucHien
                  ).format("DD/MM/YYYY")}`}
                >
                  <b>
                    {item.maDichVu}-{item.tenDichVu}
                  </b>
                  -{moment(item.thoiGianThucHien).format("DD/MM/YYYY")}
                </span>
              ),
              value: item.id,
              uniqueText: createUniqueText(
                `${item.maDichVu}-${item.tenDichVu}-${moment(
                  item.thoiGianThucHien
                ).format("DD/MM/YYYY")}`
              ),
            };
          });
          refModalChonTieuChi &&
            refModalChonTieuChi.current.show(
              { data: dsTieuChi },
              (idTieuChi) => {
                mhParams.id = idTieuChi;

                showFileEditor({
                  phieu: item,
                  nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
                  nbDvXetNgiemId: idTieuChi,
                  ma: item.ma,
                  id: idTieuChi,
                  mhParams,
                });
              }
            );
        } else if (
          LIST_PHIEU_CHON_TIEU_CHI_IN_THEO_SO_PHIEU.includes(item.ma)
        ) {
          let dsTieuChi = item.dsSoPhieu || [];
          dsTieuChi = dsTieuChi.map((item) => {
            return {
              id: item.soPhieu,
              ten: (
                <span
                  title={`${item.ten1}-${item.ten2}-${moment(
                    item.thoiGianThucHien
                  ).format("DD/MM/YYYY")}`}
                >
                  <b>
                    {item.ten1}-{item.ten2}
                  </b>
                  -{moment(item.thoiGianThucHien).format("DD/MM/YYYY")}
                </span>
              ),
              uniqueText: createUniqueText(
                `${item.ten1}-${item.ten2}-${moment(
                  item.thoiGianThucHien
                ).format("DD/MM/YYYY")}`
              ),
              value: item.soPhieu,
            };
          });
          if (dsTieuChi?.length === 1) {
            if (checkIsPhieuKySo(item)) {
              mhParams.id = dsTieuChi[0]?.id;
            }
            showFileEditor({
              phieu: item,
              nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
              nbDvXetNgiemId: dsTieuChi[0]?.id,
              ma: item.ma,
              id: dsTieuChi[0]?.id,
              mhParams,
            });
          } else {
            refModalChonTieuChi &&
              refModalChonTieuChi.current.show(
                { data: dsTieuChi },
                (idTieuChi) => {
                  if (checkIsPhieuKySo(item)) {
                    mhParams.id = idTieuChi;
                  }
                  showFileEditor({
                    phieu: item,
                    nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
                    nbDvXetNgiemId: idTieuChi,
                    ma: item.ma,
                    id: idTieuChi,
                    mhParams,
                  });
                }
              );
          }
        } else if (["P073", "P135"].includes(item.ma)) {
          refModalChonTieuChi &&
            refModalChonTieuChi.current.show(
              {
                data: (item.dsSoPhieu || []).map((x) => {
                  let _thoiGian = moment(x?.thoiGianThucHien).format(
                    "DD/MM/YYYY HH:mm:ss"
                  );
                  let _ten =
                    item.ma == "P073"
                      ? `${_thoiGian} ${x?.ten1} - ${x?.ten2}`
                      : `Số phiếu ${x?.ten1}`;

                  return {
                    id: x.soPhieu,
                    ten: _ten,
                  };
                }),
              },
              (idTieuChi) => {
                showFileEditor({
                  phieu: item,
                  nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
                  ma: item.ma,
                  id: idTieuChi,
                  mhParams,
                });
              }
            );
        } else if (item.ma == "P197") {
          //Phiếu thực hiện và công khai vật tư y tế tiêu hao
          refModalChonPhieuCongKhaiVtyt.current &&
            refModalChonPhieuCongKhaiVtyt.current.show(
              {
                khoaChiDinhId: chiTietPhauThuat.khoaThucHienId,
                dsSoPhieu: item.dsSoPhieu || [],
              },
              (filterData) => {
                showFileEditor({
                  phieu: item,
                  id: filterData?.id,
                  nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  chiDinhTuDichVuId: chiTietPhauThuat?.id,
                  chiDinhTuLoaiDichVu: LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
                  khoaChiDinhId: filterData?.khoaChiDinhId,
                  tuThoiGian: filterData?.tuThoiGian,
                  denThoiGian: filterData?.denThoiGian,
                  mhParams,
                });
              }
            );
        } else if (item.ma == "P149") {
          refModalInPhieuHuyThuoc.current &&
            refModalInPhieuHuyThuoc.current.show(
              {
                khoaChiDinhId: chiTietPhauThuat.khoaThucHienId,
                dsSoPhieu: item.dsSoPhieu || [],
              },
              (filterData) => {
                showFileEditor({
                  phieu: item,
                  id: filterData?.id,
                  nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  khoaChiDinhId: filterData?.khoaChiDinhId,
                  dsPhieuLinhId: (filterData?.dsPhieuLinhId || []).join(","),
                  tuThoiGian: filterData?.tuThoiGian,
                  denThoiGian: filterData?.denThoiGian,
                  mhParams,
                });
                return;
              }
            );
          return;
        } else if (
          ["P361", "P1187", "P1188", "P1190", "P1189"].includes(item.ma)
        ) {
          //P361: Phiếu chăm sóc cấp 2-3
          //Lọc bỏ phiếu có số phiếu = nbDotDieuTriId
          const _dsSoPhieu = (item.dsSoPhieu || []).filter(
            (x) => x.soPhieu != chiTietPhauThuat?.nbDotDieuTriId
          );
          refModalInPhieuChamSoc.current &&
            refModalInPhieuChamSoc.current.show(
              {
                khoaLamViec: { id: chiTietPhauThuat?.khoaChiDinhId },
                dsSoPhieu: _dsSoPhieu,
                ma: item.ma,
                tenPhieu: item.ten,
              },
              (data) => {
                const { thoiGianThucHien, khoaChiDinhId, id: idPhieu } = data;

                showFileEditor({
                  phieu: item,
                  id: idPhieu,
                  nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  chiDinhTuDichVuId: chiTietPhauThuat?.id,
                  khoaChiDinhId,
                  thoiGianThucHien,
                  mhParams,
                });
              }
            );
        } else if (["P537", "P1210"].includes(item.ma)) {
          //Giấy cam đoan chấp nhận phẫu thuật, thủ thuật và gây mô hồi sức (Mẫu dùng cho bệnh viện phổi)
          let _dsSoPhieu = item.dsSoPhieu;
          if (item.ma === "P1210") {
            _dsSoPhieu = (item.dsSoPhieu || []).filter(
              (x) => x.soPhieu != chiTietPhauThuat?.nbDotDieuTriId
            );
          }
          refModalInPhieuCamDoanChapNhanPTTT.current &&
            refModalInPhieuCamDoanChapNhanPTTT.current.show(
              {
                khoaChiDinhId: chiTietPhauThuat.khoaThucHienId,
                dsSoPhieu: _dsSoPhieu || [],
              },
              (filterData) => {
                showFileEditor({
                  phieu: item,
                  id: filterData?.id,
                  nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  khoaChiDinhId: filterData?.khoaChiDinhId,
                  thoiGianThucHien: filterData?.thoiGianThucHien,
                  mhParams,
                });
              }
            );
        } else if (item.ma === "P993") {
          refModalPhieuBanGiaoNguoiBenhSauPhauThuat.current &&
            refModalPhieuBanGiaoNguoiBenhSauPhauThuat.current.show(
              {},
              ({ khoaChiDinhId, denKhoaId, thoiGianBanGiao }) => {
                try {
                  showFileEditor({
                    phieu: item,
                    nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
                    ma: item.ma,
                    maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                      ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                      : "",
                    chiDinhTuDichVuId: chiTietPhauThuat?.nbDotDieuTriId,
                    thoiGianThucHien: thoiGianBanGiao,
                    khoaChiDinhId,
                    denKhoaId,
                    mhParams,
                  });
                } catch (error) {
                  console.log(error);
                }
              }
            );
        } else if (item.ma === "P141") {
          refModalPhieuPHCN.current &&
            refModalPhieuPHCN.current.show(item, (values) => {
              showFileEditor({
                phieu: item,
                nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
                id: id,
                ma: item.ma,
                loai: 10,
                loaiIn:
                  item.dsBaoCao && item.dsBaoCao.length > 0
                    ? item.dsBaoCao[0]?.loaiIn
                    : null,
                maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                  ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                  : "",
                tuThoiGian: values.tuThoiGianThucHien,
                denThoiGian: values.denThoiGianThucHien,
                mhParams,
              });
            });
        } else if (["P490"].includes(item.ma)) {
          //Lọc bỏ phiếu có số phiếu = nbDotDieuTriId
          const _dsSoPhieu = (item.dsSoPhieu || []).filter(
            (x) =>
              x.soPhieu != chiTietPhauThuat?.nbDotDieuTriId &&
              x.soPhieu &&
              x.soPhieu !== "null"
          );
          //Tóm tắt bệnh án
          refModalInPhieu.current &&
            refModalInPhieu.current.show(
              {
                khoaChiDinhId: chiTietPhauThuat.khoaThucHienId,
                dsSoPhieu: _dsSoPhieu,
                ten: item.ten,
              },
              (data) => {
                const { thoiGianThucHien, khoaChiDinhId, id: idPhieu } = data;
                showFileEditor({
                  phieu: item,
                  id: idPhieu,
                  nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  chiDinhTuDichVuId: chiTietPhauThuat?.nbDotDieuTriId,
                  khoaChiDinhId,
                  thoiGianThucHien,
                  mhParams,
                });
              }
            );
        } else if (item.ma == "P990") {
          //Bảng kiểm chuẩn bị và bàn giao NB trước phẫu thuật không theo DV PTTT
          const _dsSoPhieu = (item.dsSoPhieu || []).filter(
            (x) => x.soPhieu != chiTietPhauThuat?.nbDotDieuTriId
          );

          refModalInBangKiemNbTruocPhauThuat.current &&
            refModalInBangKiemNbTruocPhauThuat.current.show(
              {
                defaultValue: chiTietPhauThuat.khoaThucHienId,
                tenPhieu: item.ten,
                dsSoPhieu: _dsSoPhieu,
              },
              (filterData = {}) => {
                showFileEditor({
                  phieu: item,
                  id: filterData?.id,
                  nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  khoaChiDinhId: filterData?.khoaChiDinhId,
                  denKhoaId: filterData?.denKhoaId,
                  thoiGianThucHien: filterData?.thoiGianThucHien,
                  mhParams,
                });
              }
            );
        } else if (item.ma == "P086") {
          // Không truyền id vào editor
          showFileEditor({
            phieu: item,
            nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
            nbDvCdhaTdcnPtTtId: id,
            chiDinhTuDichVuId: chiTietPhauThuat?.id,
            chiDinhTuLoaiDichVu: LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
            ma: item.ma,
            khoaChiDinhId: chiTietPhauThuat.khoaThucHienId,
            maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
              ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
              : "",
            mhParams,
          });
        } else {
          showFileEditor({
            phieu: item,
            nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
            id: id,
            nbDvCdhaTdcnPtTtId: id,
            chiDinhTuDichVuId: chiTietPhauThuat?.id,
            chiDinhTuLoaiDichVu: LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
            ma: item.ma,
            khoaChiDinhId: chiTietPhauThuat.khoaThucHienId,
            maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
              ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
              : "",
            mhParams,
          });
        }
      }
    } else {
      let params = {
        listPhieus: [item],
        nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
        chiDinhTuLoaiDichVu: LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
        showError: true,
        id: id,
        chiDinhTuDichVuId: chiTietPhauThuat?.id,
      };
      const onInPhieu = async (data) => {
        try {
          showLoading();
          const { finalFile, dsPhieu } = await getFilePhieuIn(data);
          if ((dsPhieu || []).every((x) => x?.loaiIn == LOAI_IN.MO_TAB)) {
            openInNewTab(finalFile);
          } else {
            printJS({
              printable: finalFile,
              type: "pdf",
            });
          }
        } catch (error) {
        } finally {
          hideLoading();
        }
      };
      if (["P060", "P065"].includes(item.ma)) {
        refModalPhoeuChungNhanPTTT.current &&
          refModalPhoeuChungNhanPTTT.current.show({}, async (data) => {
            if (checkIsPhieuKySo(item)) {
              refModalSignPrint.current &&
                refModalSignPrint.current.showToSign({
                  phieuKy: item,
                  payload: {
                    id: id,
                    maManHinh: "009",
                    maViTri: "00901",
                    nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
                    ...data,
                  },
                  kyTheoTungPhieu: true,
                });
            } else {
              Object.assign(params, data);
              await onInPhieu(params);
            }
          });
      } else if (["P114"].includes(item.ma)) {
        const phieu = item.dsSoPhieu.find((phieu) => phieu.soPhieu == id);
        if (checkIsPhieuKySo(item) && phieu) {
          refModalSignPrint.current &&
            refModalSignPrint.current.showToSign({
              phieuKy: item,
              payload: {
                id: id,
                maManHinh: "009",
                maViTri: "00901",
                dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
                nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
                chiDinhTuDichVuId: chiTietPhauThuat.id,
                isKyPhieu: true,
              },
            });
        } else {
          onInPhieu(params);
        }
      } else if (["P070", "P069"].includes(item.ma)) {
        if (checkIsPhieuKySo(item)) {
          refModalSignPrint.current &&
            refModalSignPrint.current.showToSign({
              phieuKy: item,
              payload: {
                id: id,
                maManHinh: "009",
                maViTri: "00901",
                dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
                nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
                isKyPhieu: true,
                chiDinhTuDichVuId: chiTietPhauThuat.id,
              },
            });
        } else {
          onInPhieu(params);
        }
      } else {
        let addParam = {};
        if (item.ma === "P075") {
          if (valuePhieuChiDinhTH == 2) {
            addParam = { inPhieuChiDinh: false };
          } else if (valuePhieuChiDinhTH == 4 || valuePhieuChiDinhTH == 3) {
            const chuaThanhToan = valuePhieuChiDinhTH == 4;
            const callback =
              valuePhieuChiDinhTH == 3
                ? (data) => {
                    if (checkIsPhieuKySo(item)) {
                      refModalSignPrint.current &&
                        refModalSignPrint.current.showToSign({
                          phieuKy: item,
                          payload: {
                            maManHinh: "009",
                            maViTri: "00901",
                            dsChiDinhTuLoaiDichVu:
                              LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
                            nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
                            isKyPhieu: true,
                            chiDinhTuDichVuId: chiTietPhauThuat?.id,
                            ...data,
                          },
                        });
                    } else {
                      addParam = { ...addParam, ...data };

                      onInPhieu({ ...params, ...addParam });
                    }
                  }
                : null;

            onPrintTheoDichVu(chuaThanhToan, callback);
            return;
          }
        }

        if (checkIsPhieuKySo(item)) {
          refModalSignPrint.current &&
            refModalSignPrint.current.showToSign({
              phieuKy: item,
              payload: {
                maManHinh: "009",
                maViTri: "00901",
                dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
                nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
                isKyPhieu: true,
                chiDinhTuDichVuId: chiTietPhauThuat?.id,
                ...addParam,
              },
              kyTheoTungPhieu: ["P071"].includes(item.ma),
            });
        } else {
          onInPhieu({ ...params, ...addParam });
        }
      }
    }
  };

  const contentPhieuChiDinh = () => {
    return (
      <div
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}
      >
        <Radio.Group
          value={valuePhieuChiDinh}
          onChange={(e) => {
            setState({
              popoverVisible: false,
            });
            setValuePhieuChiDinh(e.target.value);
          }}
        >
          <Space direction="vertical">
            <Radio value={1}>{t("pttt.tatCaChiDinh")}</Radio>
            <Radio value={2}>{t("pttt.chiDinhChuaIn")}</Radio>
            <Radio value={3}>{t("pttt.inChiDinhTheoDichVu")}</Radio>
            <Radio value={4}>{t("khamBenh.tatCaCacDichVuChuaThanhToan")}</Radio>
          </Space>
        </Radio.Group>
      </div>
    );
  };

  const contentPhieuChiDinhTongHop = () => {
    return (
      <div
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}
      >
        <Radio.Group
          value={valuePhieuChiDinhTH}
          onChange={(e) => {
            setState({
              popoverVisiblePhieuTH: false,
            });
            setValuePhieuChiDinhTH(e.target.value);
          }}
        >
          <Space direction="vertical">
            <Radio value={1}>{t("pttt.tatCaChiDinh")}</Radio>
            <Radio value={2}>{t("pttt.chiDinhChuaIn")}</Radio>
            <Radio value={3}>{t("pttt.inChiDinhTheoDichVu")}</Radio>
            <Radio value={4}>{t("khamBenh.tatCaCacDichVuChuaThanhToan")}</Radio>
          </Space>
        </Radio.Group>
      </div>
    );
  };

  const onPrint = async () => {
    let res = await getTatCaGiayChiDinh({
      nbDotDieuTriId: chiTietPhauThuat.nbDotDieuTriId,
      chiDinhTuDichVuId: chiTietPhauThuat.id,
      chiDinhTuLoaiDichVu: LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
    });
    const dsFilePdf = res?.reduce((acc, item) => {
      if (Array.isArray(item)) {
        // xử lý phiếu , có những phiếu có mảng con bên trong
        let list = item.map((itemChild) => itemChild.file.pdf);
        acc = [...acc, [...list]];
        return acc;
      }
      acc = [...acc, ...item.filePdf];
      return acc;
    }, []);
    const s = await getDataDanhSachPhieu({
      dsFile: flatten(dsFilePdf),
      mode: 0,
    });
    if ((res || []).every((x1) => (x1 || []).every((x2) => x2.loaiIn == 20))) {
      openInNewTab(s);
    } else {
      printJS({
        printable: s,
        type: "pdf",
      });
    }
  };

  const onPrintPhieuChuaIn = async () => {
    let res = await getTatCaGiayChiDinh({
      nbDotDieuTriId: chiTietPhauThuat.nbDotDieuTriId,
      chiDinhTuDichVuId: chiTietPhauThuat.id,
      inPhieuChiDinh: false,
      chiDinhTuLoaiDichVu: LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
    });
    if (res?.length === 0) {
      message.error(t("khamBenh.khongConPhieuChiDinhChuaIn"));
      return null;
    }
    const dsFilePdf = res?.reduce((acc, item) => {
      if (Array.isArray(item)) {
        // xử lý phiếu , có những phiếu có mảng con bên trong
        let list = item.map((itemChild) => itemChild.file.pdf);
        acc = [...acc, [...list]];
        return acc;
      }
      acc = [...acc, ...item.filePdf];
      return acc;
    }, []);
    const s = await getDataDanhSachPhieu({
      dsFile: flatten(dsFilePdf),
      mode: 0,
    });
    if ((res || []).every((x1) => (x1 || []).every((x2) => x2.loaiIn == 20))) {
      openInNewTab(s);
    } else {
      printJS({
        printable: s,
        type: "pdf",
      });
    }
  };

  const onPrintTheoDichVu = async (chuaThanhToan, callback) => {
    showLoading();
    try {
      let res = null;
      if (!chuaThanhToan) {
        res = await nbDvKyThuatProvider.getDvPhieuChiDinh({
          nbDotDieuTriId: chiTietPhauThuat.nbDotDieuTriId,
          chiDinhTuDichVuId: chiTietPhauThuat.id,
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
        });
        refModalInChiDinhTheoDV.current.show(
          {
            data: res.data,
            chiDinhTuLoaiDichVu: LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
            nbDotDieuTriId: chiTietPhauThuat.nbDotDieuTriId,
            chiDinhTuDichVuId: chiTietPhauThuat.id,
          },
          callback
        );
      } else {
        res = await nbDvKyThuatProvider.getPhieuChiDinhTongHop({
          nbDotDieuTriId: chiTietPhauThuat.nbDotDieuTriId,
          dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
          chiDinhTuDichVuId: chiTietPhauThuat.id,
          thanhToan: false,
        });
        await printProvider.printPdf(res?.data);
      }
    } catch (error) {
      showError(error?.message);
    } finally {
      hideLoading();
    }
  };

  const onPrintPhieuChiDinh = (item) => async (e) => {
    // e.preventDefault();
    // e.stopPropagation();
    showLoading();
    if (item.kySo) {
      refModalSignPrint.current &&
        refModalSignPrint.current.showToSign({
          phieuKy: item,
          payload: {
            nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
            maManHinh: "009",
            maViTri: "00901",
            chiDinhTuDichVuId: chiTietPhauThuat?.id,
            dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
          },
        });
    } else {
      switch (valuePhieuChiDinh) {
        case 1: {
          // tất cả chỉ định
          await onPrint();
          break;
        }
        case 2: {
          // chỉ định chưa in
          await onPrintPhieuChuaIn();
          break;
        }
        case 3: {
          // in chỉ định theo dịch vụ
          await onPrintTheoDichVu();
          break;
        }
        case 4: {
          // in chỉ định theo dịch vụ
          await onPrintTheoDichVu(true);
          break;
        }
        default:
          // tất cả chỉ định
          await onPrint();
          break;
      }
    }
    hideLoading();
  };

  const changeModeDsNb = (modeDsNb) => {
    setModeDsNb(modeDsNb);
  };

  const menu = useMemo(() => {
    const phieuIns = (state?.listPhieu || []).filter((phieu) =>
      containText(phieu.ten, state.phieuIn)
    );
    const handlePrint = (phieu) => {
      if (phieu.ma === "P219") {
        return onPrintPhieuChiDinh(phieu)();
      }
      onPrintPhieu(phieu)();
    };
    return (
      <Menu
        onClick={({ key }) => {
          if (key === "-1") {
            if (phieuIns.length === 1) {
              handlePrint(phieuIns[0]);
            }
            return;
          }
          handlePrint(phieuIns[key]);
        }}
        items={[
          {
            key: "-1",
            label: (
              <div
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
              >
                <div className="input-box">
                  <SVG.IcSearch />
                  <InputTimeout
                    autoFocus
                    placeholder={t("common.timKiemTenPhieuIn")}
                    value={state.phieuIn}
                    onChange={(e) => setState({ phieuIn: e })}
                  />
                </div>
              </div>
            ),
          },
          ...(phieuIns.length
            ? phieuIns.map((item, index) => {
                if (item.ma === "P219") {
                  return {
                    key: index,
                    label: (
                      <div style={{ display: "flex" }}>
                        <div style={{ flex: 1 }}>
                          {item.ten || item.tenBaoCao}
                        </div>

                        <Popover
                          getPopupContainer={(trigger) => trigger.parentNode}
                          overlayClassName={"step-wrapper-in-options left"}
                          placement="leftTop"
                          content={contentPhieuChiDinh()}
                          visible={state.popoverVisible}
                          trigger="click"
                        >
                          <SVG.IcOption
                            alt=""
                            onClick={(e) => {
                              e.stopPropagation();
                              e.preventDefault();
                              setState({
                                popoverVisible: !state.popoverVisible,
                              });
                            }}
                          />
                        </Popover>
                      </div>
                    ),
                  };
                } else if (item.ma === "P075") {
                  return {
                    key: index,
                    label: (
                      <div style={{ display: "flex" }}>
                        <div style={{ flex: 1 }}>
                          {item.ten || item.tenBaoCao}
                        </div>

                        <Popover
                          getPopupContainer={(trigger) => trigger.parentNode}
                          overlayClassName={"step-wrapper-in-options left"}
                          placement="leftTop"
                          content={contentPhieuChiDinhTongHop()}
                          visible={state.popoverVisiblePhieuTH}
                          trigger="click"
                        >
                          <SVG.IcOption
                            alt=""
                            onClick={(e) => {
                              e.stopPropagation();
                              e.preventDefault();
                              setState({
                                popoverVisiblePhieuTH:
                                  !state.popoverVisiblePhieuTH,
                              });
                            }}
                          />
                        </Popover>
                      </div>
                    ),
                  };
                } else {
                  return {
                    key: index,
                    label: (
                      <a href={() => false}>{item.ten || item.tenBaoCao}</a>
                    ),
                  };
                }
              })
            : [
                {
                  key: "empty",
                  label: (
                    <Empty
                      className="empty"
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                    />
                  ),
                },
              ]),
        ]}
      />
    );
  }, [
    state?.listPhieu,
    id,
    valuePhieuChiDinh,
    valuePhieuChiDinhTH,
    state.popoverVisible,
    state.popoverVisiblePhieuTH,
    state.phieuIn,
  ]);

  const onChuyenPhieuLinhBu = () => {
    refChuyenPhieuLinhBu.current && refChuyenPhieuLinhBu.current.show();
  };

  return (
    <MainPage
      breadcrumb={[
        {
          link: "/phau-thuat-thu-thuat",
          title: t("pttt.quanLyPhauThuatThuThuat"),
        },
        {
          link:
            "/phau-thuat-thu-thuat/danh-sach-nguoi-benh" +
            transformObjToQueryString(locationState),
          title: t("pttt.danhSachPhauThuatThuThuat"),
        },
        {
          link: window.location.pathname,
          title: t("pttt.chiTietPhauThuat"),
        },
      ]}
      title={
        <>
          {t("pttt.chiTietPhauThuat")}
          <AlertMessage
            className={"alert-message"}
            keyThietLap={THIET_LAP_CHUNG.TEN_CANH_BAO_MH_PTTT_CDHA}
          />
        </>
      }
      titleRight={
        <div className="flex flex-center">
          <SVG.IcLocation />
          <b>
            {thongTinBenhNhan?.maKhoaNb} - {thongTinBenhNhan?.tenKhoaNb}
          </b>
        </div>
      }
      actionLeft={
        <>
          <Button.QuayLai onClick={() => history.goBack()} />
          {nbTonTaiVatTu && (
            <Button
              onClick={() => {
                history.push(
                  `/phau-thuat-thu-thuat/tra-hang-hoa/${chiTietPhauThuat?.nbDotDieuTriId}/${chiTietPhauThuat?.id}`
                );
              }}
              name="traThuocVatTuHoaChat"
            >
              {t("quanLyNoiTru.traThuocVatTuHoaChat")}
            </Button>
          )}
          {checkRole([ROLES["CHAN_DOAN_HINH_ANH"].CHUYEN_PHIEU_LINH_BU]) && (
            <Button onClick={onChuyenPhieuLinhBu}>
              {t("quanLyNoiTru.chuyenPhieuLinhBu")}
            </Button>
          )}
        </>
      }
      actionRight={
        <>
          <Dropdown
            overlay={menu}
            trigger={["click"]}
            overlayClassName="phauThuatThuThuat__button-print"
            destroyPopupOnHide
          >
            <Button
              type="default"
              rightIcon={<SVG.IcPrint className="ic-print" />}
              minWidth={100}
              onClick={getListPhieuInGiayTo}
            >
              {t("khamBenh.inGiayTo")}
            </Button>
          </Dropdown>
          <Button
            type="primary"
            rightIcon={<SVG.IcSave />}
            minWidth={100}
            onClick={onSaveThongTinPTTT}
            disabled={isDisableSave}
          >
            {t("common.luu")}
          </Button>
        </>
      }
      contentRight={
        modeDsNb === DATA_MODE_DS_NB.MODULE && (
          <Card className="right-content" noPadding={true} bottom={0} top={10}>
            <DanhSachBenhNhan
              modeDsNb={modeDsNb}
              changeModeDsNb={changeModeDsNb}
              khoaLamViec={state.khoaLamViec}
            />
          </Card>
        )
      }
    >
      <GlobalStyle />
      <Main>
        <ThongTinBenhNhan
          nbDotDieuTriId={thongTinBenhNhan?.id}
          isShowGiaHanThe={false}
          openDrawerDsNb={
            refDanhSachBenhNhanSidePanel.current &&
            refDanhSachBenhNhanSidePanel.current.showDrawer
          }
          modeDsNb={modeDsNb}
          isShowDeNghiTamUng={checkRole([
            ROLES["PHAU_THUAT_THU_THUAT"].DE_NGHI_TAM_UNG,
          ])}
        />
        <Card className="content" bottom={0}>
          <Tabs.Left
            activeKey={state.activeKey}
            tabPosition={"left"}
            tabWidth={220}
            type="card"
            moreIcon={<SVG.IcExpandDown />}
            onChange={onChange}
            className={`tab-main ${
              state.collapse ? "collapse-tab" : "show-more"
            }`}
            tabBarExtraContent={
              <div className={`content-tabs-extra`}>
                <div className="title-tabs-extra">
                  <span>{t("pttt.dsPhauThuatThuThuat")}</span>
                  <Tooltip
                    title={t(
                      state.collapse ? "common.moRong" : "common.thuGon"
                    )}
                    overlayStyle={{ whiteSpace: "nowrap" }}
                    overlayInnerStyle={{ width: "fit-content" }}
                    visible={state.off ? false : undefined}
                    onVisibleChange={(e) => {
                      if (e && state.off) {
                        setState({ off: false });
                      }
                    }}
                  >
                    <Icon
                      onClick={() => {
                        setState({ collapse: !state.collapse, off: true });
                      }}
                      className="icon-collapse-ds"
                      component={state.collapse ? SVG.IcExtend : SVG.IcCollapse}
                    />
                  </Tooltip>
                </div>
                <div className="picker-range-date">
                  <DatePicker.RangePicker
                    placeholder={[t("common.tuNgay"), t("common.denNgay")]}
                    format="DD/MM/YYYY"
                    onChange={onFilterDate}
                  ></DatePicker.RangePicker>
                </div>
                <div className="wrapper-list-extra">
                  {listFilter?.map((item, index) => (
                    <Tooltip
                      key={index}
                      title={item.tenDichVu}
                      placement="right"
                    >
                      <div
                        className={`item-tabs-extra trangThai${
                          item.trangThai
                        } ${item?.id == id ? "active-tabs-extra" : ""}`}
                        onClick={onSelectDv(item)}
                      >
                        <div className="top-child">
                          <span>{item.tenDichVu}</span>
                          <span>{item.tyLeTtDv || 0}%</span>
                        </div>
                        <div className="bottom-child">
                          <span>
                            {moment(item.thoiGianThucHien).format(
                              "DD/MM/YYYY HH:mm:ss"
                            )}
                          </span>
                          <span>
                            (
                            {t(
                              (infoPatients || []).find(
                                (x) => x.trangThai === item.trangThai
                              )?.dataIndex || ""
                            )}
                            )
                          </span>
                        </div>
                      </div>
                    </Tooltip>
                  ))}
                </div>
              </div>
            }
          >
            {listTabs.map((obj, i) => {
              return (
                <Tabs.TabPane
                  key={obj?.key}
                  tab={
                    <div>
                      {obj?.iconTab}
                      {obj?.name}
                    </div>
                  }
                  disabled={!obj.isShow}
                >
                  {i != (dataTAB_VAT_TU_THEO_THUOC?.eval() ? 8 : 7) ? (
                    <TabBox
                      queryPtttCungCaKip={queryPtttCungCaKip}
                      title={
                        obj?.keyTab === "chiDinhVatTu" ? obj?.title : obj?.name
                      }
                    >
                      {obj?.component}
                    </TabBox>
                  ) : (
                    <Fragment key={i}>{obj?.component}</Fragment>
                  )}
                </Tabs.TabPane>
              );
            })}
          </Tabs.Left>
        </Card>
        <DanhSachBenhNhanSidePanel
          modeDsNb={modeDsNb}
          ref={refDanhSachBenhNhanSidePanel}
          khoaLamViec={state.khoaLamViec}
          onChangeMode={changeModeDsNb}
        />
      </Main>

      <ModalInChiDinhTheoDV ref={refModalInChiDinhTheoDV} />
      <ModalChonTieuChi ref={refModalChonTieuChi} />
      <ModalChonTieuChiBangKe ref={refModalChonTieuChiBangKe} />
      <ModalPhieuChungNhanPTTT ref={refModalPhoeuChungNhanPTTT} />
      <ModalSignPrint ref={refModalSignPrint} />
      <ModalChonPhieuCongKhaiVtyt ref={refModalChonPhieuCongKhaiVtyt} />
      <ModalInPhieuHuyThuoc ref={refModalInPhieuHuyThuoc} />
      <ModalInPhieuCamDoanChapNhanPTTT
        ref={refModalInPhieuCamDoanChapNhanPTTT}
      />
      <ModalInPhieu ref={refModalInPhieu} />
      <ModalChonPhieuCanThiepDuoc
        ref={refModalChonPhieuCanThiepDuoc}
      ></ModalChonPhieuCanThiepDuoc>
      <ModalChuyenPhieuLinhBu ref={refChuyenPhieuLinhBu} />
      <ModalPhieuPHCN ref={refModalPhieuPHCN} />
      <ModalInBangKiemNbTruocPhauThuat
        ref={refModalInBangKiemNbTruocPhauThuat}
      />
      <ModalPhieuBanGiaoNguoiBenhSauPhauThuat
        ref={refModalPhieuBanGiaoNguoiBenhSauPhauThuat}
      />
      <ModalInPhieuChamSoc ref={refModalInPhieuChamSoc} />
    </MainPage>
  );
};

export default memo(ChiTietNguoiBenh);
