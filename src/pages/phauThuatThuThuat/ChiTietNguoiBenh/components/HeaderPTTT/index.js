import React, { useRef, useMemo } from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { message } from "antd";
import {
  useConfirm,
  useLoading,
  useQueryAll,
  useQueryString,
  useStore,
  useThietLap,
} from "hooks";
import { LazyLoad, Button } from "components";
import useThongTinNb from "pages/phauThuatThuThuat/ChiTietNguoiBenh/hook/useThongTinNb";
import { checkRole } from "lib-utils/role-utils";
import ModalSoDoPhongGiuong from "pages/quanLyNoiTru/ModalSoDoPhongGiuong";
import ModalHoanThanhPhauThuat from "../ModalHoanThanhPhauThuat";
import ModalKiemTraHoSo from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalKiemTraHoSo";
import { query } from "redux-store/stores";
import nbDvCdhaProvider from "data-access/nb-dv-cdha-tdcn-pt-tt-provider";
import {
  LOAI_DICH_VU,
  ROLES,
  THIET_LAP_CHUNG,
  TRANG_THAI_NB,
} from "constants/index";
import { SVG } from "assets";
import { toSafePromise } from "lib-utils";

const HeaderPTTT = ({ queryPtttCungCaKip }) => {
  const { t } = useTranslation();
  const { showAsyncConfirm } = useConfirm();
  const { showLoading, hideLoading } = useLoading();
  const chiTietPhauThuat = useStore("pttt.chiTietPhauThuat", {});
  const dataDetailDefault = useStore("pttt.dataDetailDefault", {});
  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan");
  const refModalSoDoPhongGiuong = useRef(null);
  const refModalThemThongTin = useRef(null);
  const [tab] = useQueryString("tab", "0");
  const { data: listAllNhomDichVuCap1 } = useQueryAll(
    query.nhomDichVuCap1.queryAllNhomDichVuCap1
  );

  const {
    pttt: {
      tiepNhan,
      huyTiepDon,
      huyHoanThanh,
      khongPhauThuat,
      updateThongTinPTTT,
      updateThongTinNguoiThucHien,
      thucHienPhauThuat,
      chuyenHoiTinh,
      huyChuyenHoiTinh,
      themThongTin,
    },
    noiTruPhongGiuong: { onSearch },
    nbDichVuKyThuat: { xacNhanTrungNguoiThucHien },
  } = useDispatch();
  const refHoanThanhPhauThuat = useRef(null);
  const refModalKiemTraHoSo = useRef(null);
  const [thongTinBenhNhan] = useThongTinNb();
  const [BAT_BUOC_NHAP_LOAI_PTTT] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_NHAP_LOAI_PTTT
  );
  const [dataKIEM_TRA_THUC_HIEN_DICH_VU] = useThietLap(THIET_LAP_CHUNG.KIEM_TRA_THUC_HIEN_DICH_VU, "false");

  const dataTHEM_TRANG_THAI_DA_CHUYEN_HOI_TINH_PTTTT =
    useThietLap(
      THIET_LAP_CHUNG.THEM_TRANG_THAI_DA_CHUYEN_HOI_TINH_PTTTT,
      ""
    )[0].toLowerCase() === "true";
  const [dataXML130_TRUONG_BAT_BUOC] = useThietLap(
    THIET_LAP_CHUNG.XML130_TRUONG_BAT_BUOC
  );
  const [dataMA_NHOM_DICH_VU_CAP1_PT] = useThietLap(
    THIET_LAP_CHUNG.MA_NHOM_DICH_VU_CAP1_PT
  );
  const [dataTINH_TY_LE_THANH_TOAN_PT_TT] = useThietLap(
    THIET_LAP_CHUNG.TINH_TY_LE_THANH_TOAN_PT_TT
  );
  const [dataBAT_BUOC_NHAP_CHAN_DOAN_SAU_PT] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_NHAP_CHAN_DOAN_SAU_PT
  );
  const [dataHIEN_THI_CAC_TRUONG_CHO_PT_MAT] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_CAC_TRUONG_CHO_PT_MAT
  );

  const onThemMoi = async () => {
    refModalSoDoPhongGiuong.current &&
      refModalSoDoPhongGiuong.current.show(
        {
          nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
          khoaId: thongTinCoBan?.khoaNbId,
        },
        {
          callback: () => {
            onSearch({
              nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
              sort: "tuThoiGian,asc",
            });
            refModalSoDoPhongGiuong.current &&
              refModalSoDoPhongGiuong.current.hide();
          },
        }
      );
  };

  const isCheckNhomDvCap1 = useMemo(
    () =>
      listAllNhomDichVuCap1.find(
        (x) => x.id === chiTietPhauThuat.nhomDichVuCap1Id
      )?.ma === dataMA_NHOM_DICH_VU_CAP1_PT,
    [listAllNhomDichVuCap1, chiTietPhauThuat, dataMA_NHOM_DICH_VU_CAP1_PT]
  );

  const clickBtnHeader = (type) => async () => {
    let callApi;
    let messageSuccess = "";
    if (!chiTietPhauThuat.thoiGianThucHien) {
      message.error(t("pttt.vuiLongNhapThoiGianBatDauPttt"));
      return;
    }
    if (!chiTietPhauThuat.tyLeTtDv) {
      message.error(t("pttt.vuiLongNhapTyLeThanhToan"));
      return;
    }
    if (!chiTietPhauThuat.dichVuId) {
      message.error(t("pttt.vuiLongNhapTenDichVu"));
      return;
    }
    if (
      !chiTietPhauThuat.viTriPhauThuat &&
      dataHIEN_THI_CAC_TRUONG_CHO_PT_MAT?.eval()
    ) {
      message.error(t("pttt.vuiLongChonViTriPhauThuat"));
      return;
    }
    if (
      !chiTietPhauThuat?.loaiPtTt &&
      BAT_BUOC_NHAP_LOAI_PTTT.toLowerCase() === "true" &&
      type === "hoanThanh"
    ) {
      message.error(t("pttt.chuaNhapLoaiPttt"));
      return;
    }

    if (
      chiTietPhauThuat?.phauThuat &&
      dataBAT_BUOC_NHAP_CHAN_DOAN_SAU_PT?.eval() &&
      type === "hoanThanh"
    ) {
      const chanDoan = chiTietPhauThuat?.chanDoan?.trim();
      if (!chanDoan) {
        message.error(t("pttt.chuaNhapChanDoanSauPttt"));
        return;
      }
      if (chanDoan && !dataDetailDefault?.chanDoan) {
        themThongTin({
          id: chiTietPhauThuat?.id,
          chanDoan: chanDoan,
        });
      }
    }

    if (
      type === "hoanThanh" &&
      isCheckNhomDvCap1 &&
      dataXML130_TRUONG_BAT_BUOC?.eval() &&
      !chiTietPhauThuat.phuongPhapVoCamId
    ) {
      message.error(t("pttt.chuaNhapPhuongPhapVoCam"));
      return;
    }

    if (
      chiTietPhauThuat.phanLoai !== 0 &&
      !chiTietPhauThuat.phanLoai &&
      type === "hoanThanh"
    ) {
      message.error(t("pttt.chuaNhapPhanLoaiPttt"));
      return;
    }

    switch (type) {
      case "tiepNhan":
        callApi = tiepNhan;
        //Khi nhấn tiếp nhận, lấy thời gian tiếp nhận = thời gian bắt đầu PT (thoiGianThucHien)
        // + NB đã ra viện (trangThainb>=100): nhấn tiếp nhận=> tgian tiếp nhận = tgian thực hiện dv, tgian thực hiện giữ nguyên như cũ
        // + Chưa ra viện (trangThainb<100): nhấn tiếp nhận=> tgian tiếp nhận = tgian nhấn nút, update tgian thực hiện = tgian tiếp nhận
        if (thongTinCoBan.trangThaiNb >= TRANG_THAI_NB.DA_RA_VIEN) {
          updateThongTinPTTT({
            thoiGianTiepNhan: chiTietPhauThuat?.thoiGianThucHien,
          });
        } else {
          let nowTime = moment().format("YYYY-MM-DD HH:mm:ss");
          updateThongTinPTTT({
            thoiGianTiepNhan: nowTime,
            thoiGianThucHien: nowTime,
          });
        }
        messageSuccess = t("pttt.tiepNhanThanhCong");
        break;
      case "huyTiepNhan":
        callApi = huyTiepDon;
        messageSuccess = t("pttt.huyTiepNhanThanhCong");
        break;
      case "chuyenHoiTinh":
        callApi = chuyenHoiTinh;
        messageSuccess = t("pttt.chuyenHoiTinhThanhCong");
        break;
      case "huyChuyenHoiTinh":
        callApi = huyChuyenHoiTinh;
        messageSuccess = t("pttt.huyChuyenHoiTinhThanhCong");
        break;
      case "hoanThanh":
        if (dataKIEM_TRA_THUC_HIEN_DICH_VU?.eval()) {
          const [err, res] = await toSafePromise(xacNhanTrungNguoiThucHien({
            dichVuId: chiTietPhauThuat.id,
            nguoiThucHienId: chiTietPhauThuat.nguoiThucHienId,
            thoiGianThucHien: chiTietPhauThuat.thoiGianThucHien,
            thoiGianCoKetQua: chiTietPhauThuat.thoiGianCoKetQua || moment().format("YYYY-MM-DD HH:mm:ss"),
            duKienKetQua: chiTietPhauThuat.duKienKetQua,
            showLoading,
            showAsyncConfirm,
            hideLoading
          }));
          if (res) {
            showLoading,
              refHoanThanhPhauThuat.current &&
              refHoanThanhPhauThuat.current.show({ isHoanThanh: true });
          }
        }
        else {
          refHoanThanhPhauThuat.current &&
            refHoanThanhPhauThuat.current.show({ isHoanThanh: true });

        }
        break;
      case "huyHoanThanh":
        callApi = huyHoanThanh;
        messageSuccess = t("pttt.huyHoanThanhThanhCong");
        break;
      case "khongPhauThuat":
        callApi = khongPhauThuat;
        messageSuccess = t("pttt.khongPhauThuatThanhCong");
        break;
      case "chuyenKhoa":
        refHoanThanhPhauThuat.current && refHoanThanhPhauThuat.current.show({});
        break;
      case "huyKhongPhauThuat":
        callApi = thucHienPhauThuat;
        messageSuccess = t("pttt.huyKhongPhauThuatThanhCong");
        break;
      default:
        break;
    }
    if (callApi) {
      showLoading();
      callApi({
        saveData:
          type === "tiepNhan" && !dataTINH_TY_LE_THANH_TOAN_PT_TT?.eval(),
      })
        .then((res) => {
          if (res && res.code === 0) {
            message.success(messageSuccess);
          }
          if (
            type === "tiepNhan" &&
            chiTietPhauThuat.chiDinhTuLoaiDichVu ===
            LOAI_DICH_VU.PHAU_THUAT_THU_THUAT
          ) {
            async function fetchData() {
              let thongTinDichVu = await nbDvCdhaProvider.getChiTietPTTT(
                chiTietPhauThuat.chiDinhTuDichVuId
              );
              let thongTinNguoiThucHien =
                await nbDvCdhaProvider.getNguoiThucHien(
                  chiTietPhauThuat.chiDinhTuDichVuId
                );
              updateThongTinPTTT({
                buongPtTtId: thongTinDichVu?.data?.buongPtTtId,
              });
              updateThongTinNguoiThucHien({
                ...thongTinNguoiThucHien?.data,
                id: chiTietPhauThuat.id,
              });
            }
            fetchData();
          }
        })
        .finally(() => hideLoading());
    }
  };
  const onKiemTraHoSo = (payload = {}) => {
    refModalKiemTraHoSo.current?.show({
      khoaLamViec: { ten: chiTietPhauThuat?.tenKhoaChiDinh },
      nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
      ...payload,
    });
  };

  const onThemThongTin = () => {
    refModalThemThongTin.current?.show();
  };

  return (
    <>
      {tab === "0" &&
        checkRole([
          ROLES["PHAU_THUAT_THU_THUAT"].HIEN_THI_BUTTON_SAO_CHEP_THONG_TIN,
        ]) && (
          <Button type="default" onClick={onThemThongTin}>
            {t("common.saoChep")}
          </Button>
        )}
      {chiTietPhauThuat.trangThaiHoan !== 40 ? (
        [25, 43].some((i) => i === chiTietPhauThuat.trangThai) ? (
          <>
            <Button
              type="primary"
              rightIcon={<SVG.IcSuccess color={"var(--color-blue-primary)"} />}
              minWidth={100}
              onClick={clickBtnHeader("tiepNhan")}
            >
              {t("common.tiepNhan")}
            </Button>
            <Button
              type="default"
              rightIcon={<SVG.IcCloseCircle />}
              minWidth={100}
              onClick={clickBtnHeader("khongPhauThuat")}
            >
              {t("pttt.khongPhauThuat")}
            </Button>
          </>
        ) : chiTietPhauThuat.trangThai === 63 ? (
          <>
            {dataTHEM_TRANG_THAI_DA_CHUYEN_HOI_TINH_PTTTT ? (
              <Button
                type="primary"
                rightIcon={
                  <SVG.IcSuccess color={"var(--color-blue-primary)"} />
                }
                minWidth={100}
                onClick={clickBtnHeader("chuyenHoiTinh")}
              >
                {t("pttt.chuyenHoiTinh")}
              </Button>
            ) : (
              <>
                {(checkRole([ROLES["PHAU_THUAT_THU_THUAT"].HOAN_THANH]) ||
                  thongTinCoBan.trangThaiNb >= TRANG_THAI_NB.DA_RA_VIEN) && (
                    <Button
                      type="primary"
                      rightIcon={
                        <SVG.IcSuccess color={"var(--color-blue-primary)"} />
                      }
                      minWidth={100}
                      onClick={clickBtnHeader("hoanThanh")}
                    >
                      {t("common.hoanThanh")}
                    </Button>
                  )}
              </>
            )}

            <Button
              type="primary"
              rightIcon={<SVG.IcCloseCircle />}
              minWidth={100}
              onClick={clickBtnHeader("huyTiepNhan")}
            >
              {t("pttt.huyTiepNhan")}
            </Button>
            <Button
              type="default"
              rightIcon={<SVG.IcCloseCircle />}
              minWidth={100}
              onClick={clickBtnHeader("khongPhauThuat")}
            >
              {t("pttt.khongPhauThuat")}
            </Button>
          </>
        ) : chiTietPhauThuat.trangThai === 95 ? (
          <>
            <Button
              type="primary"
              rightIcon={<SVG.IcSuccess color={"var(--color-blue-primary)"} />}
              minWidth={100}
              onClick={clickBtnHeader("huyChuyenHoiTinh")}
            >
              {t("pttt.huyChuyenHoiTinh")}
            </Button>

            {(checkRole([ROLES["PHAU_THUAT_THU_THUAT"].HOAN_THANH]) ||
              thongTinCoBan.trangThaiNb >= TRANG_THAI_NB.DA_RA_VIEN) && (
                <Button
                  type="primary"
                  rightIcon={
                    <SVG.IcSuccess color={"var(--color-blue-primary)"} />
                  }
                  minWidth={100}
                  onClick={clickBtnHeader("hoanThanh")}
                >
                  {t("common.hoanThanh")}
                </Button>
              )}
          </>
        ) : chiTietPhauThuat.trangThai === 155 ? (
          <>
            {(checkRole([ROLES["PHAU_THUAT_THU_THUAT"].HUY_HOAN_THANH]) ||
              thongTinCoBan.trangThaiNb >= TRANG_THAI_NB.DA_RA_VIEN) && (
                <Button
                  type="primary"
                  rightIcon={
                    <SVG.IcSuccess color={"var(--color-blue-primary)"} />
                  }
                  minWidth={100}
                  onClick={clickBtnHeader("huyHoanThanh")}
                >
                  {t("pttt.huyHoanThanh")}
                </Button>
              )}

            {thongTinBenhNhan?.khoaNbId ===
              chiTietPhauThuat?.khoaThucHienId && (
                <Button
                  type="primary"
                  minWidth={100}
                  onClick={clickBtnHeader("chuyenKhoa")}
                >
                  {t("pttt.chuyenKhoa")}
                </Button>
              )}
          </>
        ) : (
          <></>
        )
      ) : (
        checkRole([ROLES["PHAU_THUAT_THU_THUAT"].HUY_KHONG_PHAU_THUAT]) && (
          <>
            <Button
              type="primary"
              minWidth={100}
              onClick={clickBtnHeader("huyKhongPhauThuat")}
            >
              {t("pttt.huyKhongPhauThuat")}
            </Button>
          </>
        )
      )}
      {tab === "8" && (
        <Button
          type="success"
          minWidth={100}
          onClick={onThemMoi}
          rightIcon={<SVG.IcAdd />}
        >
          {t("common.themMoi")}
        </Button>
      )}

      <ModalHoanThanhPhauThuat
        ref={refHoanThanhPhauThuat}
        onKiemTraHoSo={onKiemTraHoSo}
      />
      <ModalKiemTraHoSo ref={refModalKiemTraHoSo} />
      <ModalSoDoPhongGiuong ref={refModalSoDoPhongGiuong} />
      {checkRole([
        ROLES["PHAU_THUAT_THU_THUAT"].HIEN_THI_BUTTON_SAO_CHEP_THONG_TIN,
      ]) && (
          <LazyLoad
            component={() =>
              import(
                "pages/phauThuatThuThuat/ChiTietNguoiBenh/components/ModalThemThongTin"
              )
            }
            ref={refModalThemThongTin}
            queryPtttCungCaKip={queryPtttCungCaKip}
          />
        )}
    </>
  );
};

HeaderPTTT.propTypes = {};

export default HeaderPTTT;
