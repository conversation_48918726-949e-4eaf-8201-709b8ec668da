import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from "react";
import { Form, Input, message } from "antd";
import { Button, Select, ModalTemplate } from "components";
import { useTranslation } from "react-i18next";
import { MainTeamplate } from "./styled";
import { useListAll, useLoading, useStore } from "hooks";
import { useDispatch } from "react-redux";

const ModalDoiKhoaChiDinh = (props, ref) => {
  const { t } = useTranslation();
  const { showLoading, hideLoading } = useLoading();

  const refModal = useRef(null);

  const [form] = Form.useForm();

  const {
    pttt: { themThongTin, onChangeInputSearch },
  } = useDispatch();

  const [state, _setState] = useState({ show: false });
  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };

  const [listAllKhoa] = useListAll("khoa", { active: true }, state.show);

  useImperativeHandle(ref, () => ({
    show: (data) => {
      setState({
        show: true,
        currentItem: data.item,
      });
    },
  }));

  useEffect(() => {
    if (state?.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state?.show]);

  const onOK = (isOk) => {
    if (isOk) {
      form.submit();
    } else {
      form.resetFields();
      setState({ show: false });
    }
  };
  const onHandleSubmit = async (values) => {
    try {
      showLoading();

      const payload = {
        id: state.currentItem?.id,
        nbDichVu: {
          khoaChiDinhId: values.khoaChiDinhId,
        },
      };

      await themThongTin(payload);
      message.success(t("pttt.doiKhoaChiDinhDichVuThanhCong"));

      onChangeInputSearch({});
      onOK(false);
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  return (
    <ModalTemplate
      ref={refModal}
      width={600}
      title={t("pttt.chuyenKhoaChiDinh")}
      actionLeft={<Button.QuayLai onClick={() => onOK(false)} />}
      actionRight={
        <Button
          type="primary"
          minWidth={100}
          iconHeight={15}
          onClick={() => onOK(true)}
        >
          {t("common.xacNhan")}
        </Button>
      }
      destroyOnClose
      onCancel={() => onOK(false)}
    >
      <MainTeamplate>
        <Form form={form} layout="vertical" onFinish={onHandleSubmit}>
          <Form.Item
            label={t("pttt.khoaChiDinhMoi")}
            name="khoaChiDinhId"
            rules={[
              {
                required: true,
                message: t("danhMuc.vuiLongChonKhoaChiDinh"),
              },
            ]}
          >
            <Select
              data={listAllKhoa}
              placeholder={t("danhMuc.chonKhoaChiDinh")}
            />
          </Form.Item>

          <Form.Item label={t("pttt.khoaChiDinhCu")}>
            <Input
              value={state.currentItem?.tenKhoaChiDinh || ""}
              disabled
            ></Input>
          </Form.Item>
        </Form>
      </MainTeamplate>
    </ModalTemplate>
  );
};
export default forwardRef(ModalDoiKhoaChiDinh);
