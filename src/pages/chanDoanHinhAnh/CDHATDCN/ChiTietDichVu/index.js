import React, { useMemo, useRef, memo, useEffect, useState } from "react";
import { Col, message, Row } from "antd";
import {
  <PERSON><PERSON>,
  <PERSON>,
  Tabs,
  Tooltip,
  ThongTinBenh<PERSON>han,
  AlertMessage,
} from "components";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { useLocation, useParams } from "react-router-dom";
import { Main, MainPage } from "./styled";
import ThongTinDichVu from "./ThongTinDichVu";
import ChiDinhDichVuKyThuat from "./ChiDinhDichVuKyThuat";
import ChiDinhThuoc from "./ChiDinhThuoc";
import ChiDinhVatTu from "./ChiDinhVatTu";
import LichSuThucHien from "./LichSuThucHien";
import ThongTinNguoiThucHien from "./ThongTinNguoiThucHien";
import {
  useLoading,
  useQueryString,
  useStore,
  useCache,
  useLayer,
  useThietLap,
  useConfirm,
} from "hooks";
import {
  CACHE_KEY,
  DOI_TUONG_KCB,
  LOAI_DICH_VU,
  TRANG_THAI_DICH_VU_CDHA,
  DATA_MODE_DS_NB,
  THIET_LAP_CHUNG,
  ROLES,
} from "constants/index";
import {
  setQueryStringValue,
  transformObjToQueryString,
} from "hooks/useQueryString/queryString";
import HenDieuTri from "./HenDieuTri";
import { SVG } from "assets";
import HeaderCDHA from "pages/chanDoanHinhAnh/components/HeaderCDHA";
import DanhSachBenhNhanSidePanel from "./DanhSachBenhNhanSidePanel";
import DanhSachBenhNhan from "./DanhSachBenhNhan";
import { checkRole } from "lib-utils/role-utils";
import { sleep } from "utils/index";
import ModalChuyenPhieuLinhBu from "pages/chanDoanHinhAnh/CDHATDCN/ChiTietDichVu/ModalChuyenPhieuLinhBu";
import moment from "moment";
import { toSafePromise } from "lib-utils";

const ChiTietDichVu = ({ history }) => {
  const [tab] = useQueryString("tab", "0");
  const { t } = useTranslation();
  const { dichVuId, nbDotDieuTriId } = useParams();
  const { state: locationState } = useLocation();
  const { queryString } = locationState || {};
  const layerId = useLayer();

  const refThongTinChung = useRef(null);
  const refThongTinNguoiThucHien = useRef(null);
  const refDanhSachBenhNhanSidePanel = useRef(null);
  const refChuyenPhieuLinhBu = useRef(null);
  const {
    tiepNhanCDHA: {
      getById,
      coKetQuaDv,
      huyKetQuaDv,
      clearData,
      getListDichVuTheoNbDotDieuTri,
    },

    khoa: { getKhoaTheoTaiKhoan },
    thietLapChonKho: { getListThietLapChonKhoTheoTaiKhoan },
    chiDinhKhamBenh: { updateConfigData },
    nbGoiDv: { getByNbThongTinId },
    nbDvKho: { getNbTonTaiTraKho },
    nhomDichVuCap2: { searchTongHopDichVuCap2 },
    nbDichVuKyThuat: { xacNhanTrungNguoiThucHien }
  } = useDispatch();

  const { showLoading, hideLoading } = useLoading();
  const { showAsyncConfirm } = useConfirm();

  const chiTietDichVuCDHA = useStore("tiepNhanCDHA.chiTietDichVuCDHA", {});
  const listDichVuTheoNbDotDieuTri = useStore(
    "tiepNhanCDHA.listDichVuTheoNbDotDieuTri",
    []
  );
  const dsSlDichVuTheoPhong = useStore("khamBenh.dsSlDichVuTheoPhong", []);
  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan");
  const nbTonTaiVatTu = useStore("nbDvKho.nbTonTaiVatTu");
  const {
    nhanVienId,
    chucVuId,
    id: authId,
  } = useStore("auth.auth", {}, { field: "nhanVienId, chucVuId, id" });

  const [dataKHONG_HEN_NGAY_THUC_HIEN_DICH_VU_VUOT_DINH_MUC] = useThietLap(
    THIET_LAP_CHUNG.KHONG_HEN_NGAY_THUC_HIEN_DICH_VU_VUOT_DINH_MUC
  );

  const [dataKIEM_TRA_THUC_HIEN_DICH_VU] = useThietLap(THIET_LAP_CHUNG.KIEM_TRA_THUC_HIEN_DICH_VU, "false");

  const [modeDsNb, setModeDsNb] = useCache(
    authId,
    CACHE_KEY.DATA_MODE_DS_NB_CDHATDCN,
    DATA_MODE_DS_NB.DRAWER,
    false
  );

  const [state, _setState] = useState({
    activeKey: tab,
    collapse: true,
  });

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const renderText = () => {
    return (
      <Tooltip
        placement="topLeft"
        overlayStyle={{ whiteSpace: "nowrap", maxWidth: "700px" }}
        overlayInnerStyle={{ width: "fit-content" }}
        title={`${chiTietDichVuCDHA?.maDichVu ? `(${chiTietDichVuCDHA?.maDichVu})` : ""
          } ${chiTietDichVuCDHA?.tenDichVu || ""}`}
      >
        {`${t("common.chiDinhVatTu").toUpperCase()} ${chiTietDichVuCDHA?.maDichVu
          ? `- (${chiTietDichVuCDHA?.maDichVu})`
          : ""
          } ${chiTietDichVuCDHA?.tenDichVu || ""}`}
      </Tooltip>
    );
  };

  const isChoTiepDonPage = useMemo(() => {
    return (
      !chiTietDichVuCDHA?.trangThai ||
      TRANG_THAI_DICH_VU_CDHA.CHO_TIEP_DON.includes(
        chiTietDichVuCDHA?.trangThai
      )
    );
  }, [chiTietDichVuCDHA?.trangThai]);

  const canEdit = useMemo(() => {
    let roleAdmin = false,
      roleBase = false;
    if (checkRole([ROLES["CHAN_DOAN_HINH_ANH"].CHI_DICH_DV])) {
      roleAdmin =
        [155, 25, 35, 43, 63].includes(chiTietDichVuCDHA?.trangThai) &&
        chiTietDichVuCDHA.trangThaiHoan === 0;
    }
    if (
      checkRole([ROLES["CHAN_DOAN_HINH_ANH"].CHI_DICH_DV_KHI_DV_HOAN_THANH])
    ) {
      const condition1 =
        chiTietDichVuCDHA?.trangThai === 155 &&
        chiTietDichVuCDHA.trangThaiHoan === 0 &&
        [5, 10, 20, 30, 40, 45, 50, 200].includes(thongTinCoBan?.trangThaiNb);
      const condition2 =
        [25, 35, 43, 63].includes(chiTietDichVuCDHA?.trangThai) &&
        chiTietDichVuCDHA.trangThaiHoan === 0;

      roleBase = condition1 || condition2;
    }
    return roleAdmin || roleBase;
  }, [chiTietDichVuCDHA, thongTinCoBan]);

  const listTabs = useMemo(() => {
    if (!chiTietDichVuCDHA.id) return [];

    if (isChoTiepDonPage)
      return [
        {
          name: t("common.thongTinDichVu"),
          component: <ThongTinDichVu ref={refThongTinChung} />,
          iconTab: <SVG.IcThongTinPttt />,
          isShow: true,
        },
      ];
    //NOTE: Khi thêm tab mới or đổi vị trí của tab thì cần check lại logic các button thì đang dùng tab = 1 con số để check
    return [
      {
        name: t("common.thongTinDichVu"),
        component: <ThongTinDichVu ref={refThongTinChung} />,
        iconTab: <SVG.IcThongTinPttt />,
        isShow: true,
      },
      {
        name: t("pttt.thongTinNguoiThucHien"),
        component: <ThongTinNguoiThucHien ref={refThongTinNguoiThucHien} />,
        iconTab: <SVG.IcThongTinNguoiThucHien />,
        isShow: true,
      },
      {
        name: t("common.chiDinhDichVu"),
        component: (
          <ChiDinhDichVuKyThuat
            canEdit={canEdit}
            layerId={state.activeKey == 2 ? layerId : null}
          />
        ),
        iconTab: <SVG.IcChiDinhDichVu />,
        isShow: true,
      },
      {
        name: t("common.chiDinhThuoc"),
        component: (
          <ChiDinhThuoc
            canEdit={canEdit}
            layerId={state.activeKey == 3 ? layerId : null}
          />
        ),
        iconTab: <SVG.IcDonThuoc color={"var(--color-gray-primary)"} />,
        isShow: true,
      },
      {
        name: t("common.chiDinhVatTu"),
        title: renderText(),
        component: (
          <ChiDinhVatTu
            canEdit={canEdit}
            layerId={state.activeKey == 4 ? layerId : null}
          />
        ),
        iconTab: <SVG.IcVatTu />,
        isShow: true,
        keyTab: "chiDinhVatTu",
      },
      {
        name: t("common.henDieuTri"),
        component: <HenDieuTri />,
        iconTab: <SVG.IcCalendar />,
        isShow: true,
      },
    ];
  }, [chiTietDichVuCDHA.id, canEdit, state.activeKey]);

  useEffect(() => {
    if (dichVuId) {
      getById(dichVuId);
    }
  }, [dichVuId]);

  useEffect(() => {
    if (dichVuId && chiTietDichVuCDHA?.khoaThucHienId) {
      getNbTonTaiTraKho({
        nbDotDieuTriId: nbDotDieuTriId,
        khoaChiDinhId: chiTietDichVuCDHA?.khoaThucHienId,
      });
    }
  }, [dichVuId, chiTietDichVuCDHA]);

  useEffect(() => {
    if (
      dichVuId &&
      thongTinCoBan &&
      Object.keys(thongTinCoBan).length &&
      chiTietDichVuCDHA &&
      Object.keys(chiTietDichVuCDHA).length
    ) {
      updateConfigData({
        configData: {
          chiDinhTuDichVuId: dichVuId,
          nbDotDieuTriId: nbDotDieuTriId,
          nbThongTinId: thongTinCoBan.nbThongTinId,
          chiDinhTuLoaiDichVu: chiTietDichVuCDHA.loaiDichVu,
          dsChiDinhTuLoaiDichVu: chiTietDichVuCDHA.loaiDichVu,
          khoaChiDinhId: chiTietDichVuCDHA.khoaThucHienId,
          thongTinNguoiBenh: thongTinCoBan,
          isPhauThuat: false,
          trangThaiKham: "",
          phongThucHienId: thongTinCoBan.phongThucHienId,
          doiTuongKcb: thongTinCoBan.doiTuongKcb,
          canLamSang: true,
          thoiGianThucHien: chiTietDichVuCDHA.thoiGianTiepNhan,
        },
      });
      getByNbThongTinId({
        nbThongTinId: thongTinCoBan.nbThongTinId,
        page: "",
        size: "",
      });
    }
  }, [dichVuId, chiTietDichVuCDHA, thongTinCoBan]);

  useEffect(() => {
    let param = { active: true, page: "", size: "" };
    getKhoaTheoTaiKhoan({ ...param });
    return () => {
      clearData();
    };
  }, []);

  useEffect(() => {
    if (thongTinCoBan.maNb && chiTietDichVuCDHA.nhomDichVuCap2Id)
      getListDichVuTheoNbDotDieuTri({
        maNb: thongTinCoBan.maNb,
        nhomDichVuCap2Id: chiTietDichVuCDHA.nhomDichVuCap2Id,
        page: 0,
      });
  }, [thongTinCoBan.maNb, chiTietDichVuCDHA.nhomDichVuCap2Id]);

  useEffect(() => {
    if (chiTietDichVuCDHA.nhomDichVuCap1Id) {
      searchTongHopDichVuCap2({
        page: "",
        size: "",
        active: true,
        sort: "ten,asc",
        nhomDichVuCap1Id: chiTietDichVuCDHA.nhomDichVuCap1Id,
      });
    }
  }, [chiTietDichVuCDHA.nhomDichVuCap1Id]);

  const onChange = (tab) => {
    setState({ activeKey: tab });
    if (tab === "3" || tab === "4") {
      let loaiDichVu = LOAI_DICH_VU.THUOC;
      if (tab === "4") {
        loaiDichVu = LOAI_DICH_VU.VAT_TU;
      }
      let payload = {
        khoaChiDinhId: chiTietDichVuCDHA?.khoaThucHienId,
        doiTuong: thongTinCoBan?.doiTuong,
        loaiDoiTuongId: thongTinCoBan?.loaiDoiTuongId,
        capCuu: thongTinCoBan?.capCuu,
        phongId: chiTietDichVuCDHA?.phongThucHienId,
        noiTru: ![
          DOI_TUONG_KCB.NGOAI_TRU,
          DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
          DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
        ].includes(thongTinCoBan.doiTuongKcb),
        canLamSang: true,
        loaiDichVu: loaiDichVu,
        nhanVienId,
        chucVuId,
      };
      getListThietLapChonKhoTheoTaiKhoan({ ...payload });
    }
    setQueryStringValue("tab", tab);
  };

  useEffect(() => {
    if (chiTietDichVuCDHA?.id && thongTinCoBan?.doiTuongKcb) onChange(tab);
  }, [chiTietDichVuCDHA?.id, thongTinCoBan?.doiTuongKcb]);

  const onSave = async () => {
    switch (state.activeKey) {
      case "0":
        const day = moment(chiTietDichVuCDHA?.thoiGianThucHien).day();
        const thongTinNgayVuotDinhMuc =
          dataKHONG_HEN_NGAY_THUC_HIEN_DICH_VU_VUOT_DINH_MUC
            ?.split(",")
            ?.map((item) => item?.toLowerCase());
        if (
          (thongTinNgayVuotDinhMuc?.includes("t7") && day === 6) ||
          (thongTinNgayVuotDinhMuc?.includes("cn") && day === 0)
        ) {
          refThongTinChung.current &&
            (await refThongTinChung.current.onSave(false));
        } else {
          if (dsSlDichVuTheoPhong?.length) {
            const slDichVu = dsSlDichVuTheoPhong?.find(
              (x) =>
                x.ngayThucHien ===
                moment(chiTietDichVuCDHA?.thoiGianThucHien).format("YYYY-MM-DD")
            );

            let soLuongDinhMucNgoaiTru =
              slDichVu?.dinhMucNgoaiTru - (slDichVu?.soLuongNgoaiTru || 0);
            let soLuongDinhMucNoiTru =
              slDichVu?.dinhMucNoiTru - (slDichVu?.soLuongNoiTru || 0);
            if (
              (slDichVu?.dinhMucNgoaiTru &&
                soLuongDinhMucNgoaiTru <= 0 &&
                [
                  DOI_TUONG_KCB.NGOAI_TRU,
                  DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
                  DOI_TUONG_KCB.NHAN_THUOC_THEO_HEN,
                  DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
                  DOI_TUONG_KCB.CAC_TRUONG_HOP_KHAC,
                ].includes(thongTinCoBan.doiTuongKcb)) ||
              (slDichVu?.dinhMucNoiTru &&
                soLuongDinhMucNoiTru <= 0 &&
                [
                  DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU,
                  DOI_TUONG_KCB.DIEU_TRI_NOI_TRU,
                  DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_BAN_NGAY,
                  DOI_TUONG_KCB.DIEU_TRI_LUU_TRU_TAI_TRAM_Y_TE_TUYEN_XA,
                  DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_DUOI_BON_GIO,
                ].includes(thongTinCoBan.doiTuongKcb)) ||
              !slDichVu
            ) {
              message.error(
                `Ngày hiện tại đã hết định mức. Vui lòng chọn ngày thực hiện từ ngày ${moment(
                  chiTietDichVuCDHA?.thoiGianThucHien
                )
                  .add(1, "days")
                  .format("YYYY-MM-DD")}`
              );
              return;
            }
          }

          refThongTinChung.current &&
            (await refThongTinChung.current.onSave(false));
        }

        break;

      case "1":
        refThongTinNguoiThucHien.current &&
          (await refThongTinNguoiThucHien.current.onSave());
        break;

      default:
        break;
    }
  };

  const isDisableSave = () => {
    if (state.activeKey === "0" || state.activeKey === "1") return false;
    return true;
  };

  const onCoKetQua = async () => {
    try {
      showLoading();
      await onSave(false);
      await sleep(300);
      if (dataKIEM_TRA_THUC_HIEN_DICH_VU.eval()) {
        const [err, res] = await toSafePromise(xacNhanTrungNguoiThucHien({
          dichVuId: dichVuId,
          nguoiThucHienId: chiTietDichVuCDHA.nguoiThucHienId,
          thoiGianThucHien: chiTietDichVuCDHA.thoiGianThucHien,
          thoiGianCoKetQua: chiTietDichVuCDHA.thoiGianCoKetQua || moment().format("YYYY-MM-DD HH:mm:ss"),
          duKienKetQua: chiTietDichVuCDHA.duKienKetQua,
          showLoading,
          showAsyncConfirm,
          hideLoading
        }));
        if (!res) return;
        showLoading();
      }
      await coKetQuaDv([
        {
          id: dichVuId,
          thoiGianCoKetQua: thoiGianCoKetQua,
          nguoiDocKqId: chiTietDichVuCDHA.nguoiDocKqId,
        },
      ]);
    } catch (error) {
      console.error("Lỗi trong quá trình xác nhận có kết quả:", error);
    } finally {
      await getById(dichVuId);
      hideLoading();
    }
  };

  const onHuyKetQua = async () => {
    try {
      showLoading();
      await huyKetQuaDv([dichVuId]);
      await getById(dichVuId);
    } catch (error) {
    } finally {
      hideLoading();
    }
  };

  const changeModeDsNb = (modeDsNb) => {
    setModeDsNb(modeDsNb);
  };

  const onChuyenPhieuLinhBu = () => {
    refChuyenPhieuLinhBu.current && refChuyenPhieuLinhBu.current.show();
  };
  return (
    <MainPage
      breadcrumb={[
        { link: "/chan-doan-hinh-anh", title: t("cdha.cdha") },
        {
          title: isChoTiepDonPage
            ? t("cdha.choTiepDon")
            : t("cdha.thucHienCdhaTdcn"),
          link: isChoTiepDonPage
            ? "/chan-doan-hinh-anh/cho-tiep-don" +
            transformObjToQueryString(queryString)
            : "/chan-doan-hinh-anh/thuc-hien-cdha-tdcn" +
            transformObjToQueryString(queryString),
        },
        {
          title: t("cdha.chiTietDichVu"),
        },
      ]}
      contentRight={
        modeDsNb === DATA_MODE_DS_NB.MODULE && (
          <Card className="right-content" noPadding={true} bottom={0} top={10}>
            <DanhSachBenhNhan
              modeDsNb={modeDsNb}
              changeModeDsNb={changeModeDsNb}
              isChoTiepDonPage={isChoTiepDonPage}
            />
          </Card>
        )
      }
      actionLeft={
        <>
          <Button.QuayLai onClick={() => history.goBack()} />
          {nbTonTaiVatTu && (
            <Button
              onClick={() => {
                history.push(
                  `/chan-doan-hinh-anh/tra-hang-hoa/${nbDotDieuTriId}/${dichVuId}`
                );
              }}
              name="traThuocVatTuHoaChat"
            >
              {t("quanLyNoiTru.traThuocVatTuHoaChat")}
            </Button>
          )}
          {checkRole([ROLES["CHAN_DOAN_HINH_ANH"].CHUYEN_PHIEU_LINH_BU]) && (
            <Button onClick={onChuyenPhieuLinhBu}>
              {t("quanLyNoiTru.chuyenPhieuLinhBu")}
            </Button>
          )}
        </>
      }
      actionRight={
        <>
          <Button
            type="primary"
            minWidth={100}
            onClick={onSave}
            disabled={isDisableSave()}
            rightIcon={<SVG.IcSave />}
          >
            {t("common.luu")}
          </Button>

          {TRANG_THAI_DICH_VU_CDHA.DA_TIEP_NHAN.includes(
            chiTietDichVuCDHA.trangThai
          ) && (
              <Button
                onClick={onCoKetQua}
                iconHeight={20}
                type="primary"
                minWidth={100}
                rightIcon={<SVG.IcSuccess color={"var(--color-blue-primary)"} />}
                throttleMs={3000}
              >
                {t("common.coKetQua")}
              </Button>
            )}

          {TRANG_THAI_DICH_VU_CDHA.DA_CO_KET_QUA.includes(
            chiTietDichVuCDHA.trangThai
          ) && (
              <Button
                onClick={onHuyKetQua}
                iconHeight={20}
                type="primary"
                minWidth={100}
                rightIcon={<SVG.IcCancel />}
              >
                {t("common.huyKetQua")}
              </Button>
            )}
        </>
      }
    >
      <Main>
        <Row>
          <h1>{t("cdha.chiTietDichVu")}</h1>
          <AlertMessage
            keyThietLap={THIET_LAP_CHUNG.TEN_CANH_BAO_MH_PTTT_CDHA}
          />
          <Col className="header-left" span={24}>
            <ThongTinBenhNhan
              nbDotDieuTriId={nbDotDieuTriId}
              openDrawerDsNb={
                refDanhSachBenhNhanSidePanel.current &&
                refDanhSachBenhNhanSidePanel.current.showDrawer
              }
              modeDsNb={modeDsNb}
            />
          </Col>
        </Row>
        <Card className="content" bottom={50}>
          <Tabs.Left
            key={listTabs.length}
            defaultActiveKey={state.activeKey}
            tabPosition={"left"}
            tabWidth={220}
            type="card"
            moreIcon={<SVG.IcExpandDown />}
            onChange={onChange}
            className={`tab-main ${state.collapse ? "collapse-tab" : "show-more"
              }`}
            tabBarExtraContent={
              <LichSuThucHien listDichVu={listDichVuTheoNbDotDieuTri} />
            }
          >
            {listTabs.map((obj, i) => {
              return (
                <Tabs.TabPane
                  key={i}
                  tab={
                    <div>
                      {obj?.iconTab}
                      {obj?.name}
                    </div>
                  }
                  disabled={!obj.isShow}
                >
                  <Tabs.TabBox
                    title={
                      obj?.keyTab === "chiDinhVatTu" ? obj?.title : obj?.name
                    }
                    rightAction={<HeaderCDHA />}
                  >
                    {obj?.component}
                  </Tabs.TabBox>
                </Tabs.TabPane>
              );
            })}
          </Tabs.Left>
        </Card>
        <DanhSachBenhNhanSidePanel
          modeDsNb={modeDsNb}
          ref={refDanhSachBenhNhanSidePanel}
          onChangeMode={changeModeDsNb}
          isChoTiepDonPage={isChoTiepDonPage}
        />
        <ModalChuyenPhieuLinhBu ref={refChuyenPhieuLinhBu} />
      </Main>
    </MainPage>
  );
};

export default memo(ChiTietDichVu);
