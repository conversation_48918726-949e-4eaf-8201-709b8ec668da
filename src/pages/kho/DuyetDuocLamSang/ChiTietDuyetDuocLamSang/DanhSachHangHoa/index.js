import React, { useEffect, useMemo, useRef, useState } from "react";
import { InputTimeout, TableWrapper, Tooltip } from "components";
import { useDispatch } from "react-redux";
import { Main } from "./styled";
import { useTranslation } from "react-i18next";
import { formatNumber, isArray } from "utils/index";
import { SVG } from "assets";
import ModalChiTietLinhThuoc from "../modal/ModalChiTietLinhThuoc";
import {
  PHIEU_LINH_BU_TU_TRUC,
  THUOC_LINH_DU_TRU,
  THUOC_LINH_NOI_TRU,
  THUOC_TRA_NOI_TRU,
  PHIEU_XUAT_CHUYEN_KHO,
} from "../../DsDuyetDuocLamSang/config";
import { useStore, useThietLap } from "hooks";
import moment from "moment";
import { customSortBySttAndName } from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/PhieuLinh/utils";
import { THIET_LAP_CHUNG } from "constants/index";
import { groupByMaThuoc } from "utils/kho-utils";
import { cloneDeep } from "lodash";

const { Column, Setting } = TableWrapper;

const DanhSachHangHoa = ({ dsLoai }) => {
  const { t } = useTranslation();
  const refModalChiTietLinhThuoc = useRef(null);
  const refSetting = useRef();

  const [selectedId, setSelectedId] = useState(null);
  const isEdit = useStore("nbDvThuocDuocLamSang.isEdit", false);
  const dsThuocEdit = useStore("nbDvThuocDuocLamSang.dsThuocEdit", []);
  const chiTietPhieuNhapXuat = useStore(
    "nbDvThuocDuocLamSang.chiTietPhieuNhapXuat",
    {}
  );
  const listNbDvThuocDuocLamSang = useStore(
    "nbDvThuocDuocLamSang.listNbDvThuocDuocLamSang",
    {}
  );
  const [dataGOP_DONG_THUOC, finishGOP_DONG_THUOC] = useThietLap(
    THIET_LAP_CHUNG.GOP_DONG_THUOC
  );
  const {
    nbDvThuocDuocLamSang: {
      onChangeInputSearch,
      updateData: updateDataDvThuocDuocLamSang,
    },
  } = useDispatch();

  const dataSource = useMemo(() => {
    let _data = [],
      keyPath = "ma";
    if (
      dsLoai === THUOC_LINH_DU_TRU ||
      dsLoai === THUOC_TRA_NOI_TRU ||
      dsLoai === PHIEU_XUAT_CHUYEN_KHO
    ) {
      _data = customSortBySttAndName(listNbDvThuocDuocLamSang, [, "tenDichVu"]);
      keyPath = "maDichVu";
    } else {
      _data = customSortBySttAndName(
        chiTietPhieuNhapXuat?.dsNhapXuatChiTiet || [],
        ["dichVu.sttPhieuLinh", "dichVu.ten"]
      );
      keyPath = "dichVu.ma";
    }

    updateDataDvThuocDuocLamSang({ orgDsThuocEdit: _data });

    if (finishGOP_DONG_THUOC && dataGOP_DONG_THUOC?.eval()) {
      _data = groupByMaThuoc(cloneDeep(_data), keyPath);
    }
    return _data;
  }, [
    chiTietPhieuNhapXuat,
    listNbDvThuocDuocLamSang,
    dsLoai,
    finishGOP_DONG_THUOC,
    dataGOP_DONG_THUOC,
  ]);

  useEffect(() => {
    if (isEdit) {
      updateDataDvThuocDuocLamSang({ dsThuocEdit: cloneDeep(dataSource) });
    } else {
      updateDataDvThuocDuocLamSang({ dsThuocEdit: [] });
    }
  }, [isEdit]);

  const onRow = (record) => ({
    onClick: (e) => {
      if (isEdit) return;
      setSelectedId(record.id);

      const selection = window.getSelection();
      if (selection.type !== "Range") {
        refModalChiTietLinhThuoc.current?.show({
          phieuNhapXuatId: record.phieuNhapXuatId,
          dichVuId: record.dichVuId,
        });
      }
    },
  });

  const onSearchInput = (key) => (e) => {
    let value = "";
    if (e?.target) {
      if (e.target.hasOwnProperty("checked")) value = e.target.checked;
      else value = e.target.value;
    } else value = e;

    onChangeInputSearch({
      [key]: value,
    });
  };

  const onChangeList = (key, record) => (value) => {
    let dsThuocEdit = cloneDeep(dataSource);
    dsThuocEdit = dsThuocEdit.map((item) => {
      if (item.id === record.id) {
        item[key] = value;
      }
      return item;
    });
    updateDataDvThuocDuocLamSang({ dsThuocEdit });
  };

  const generateTonKhoColumns = (kho, configList) => {
    return configList.map(({ dataIndex, label }) => {
      const title = kho ? `${t(label)} (${kho?.ten})` : t(label);
      return Column({
        title,
        dataIndex,
        key: dataIndex,
        i18Name: title,
        width: 100,
        align: "right",
        show: false,
        render: (item) => (item ? formatNumber(item) : item === 0 ? 0 : ""),
      });
    });
  };

  const columns =
    dsLoai === THUOC_LINH_DU_TRU ||
    dsLoai === THUOC_TRA_NOI_TRU ||
    dsLoai === PHIEU_XUAT_CHUYEN_KHO
      ? [
          Column({
            title: t("common.stt"),
            width: 60,
            dataIndex: "index",
            key: "index",
            align: "center",
            fixed: "left",
            ignore: true,
            render: (_, __, index) => index + 1,
          }),
          Column({
            title: t("kho.maHangHoa"),
            width: 140,
            dataIndex: "maDichVu",
            key: "maDichVu",
            i18Name: "kho.maHangHoa",
            renderSearch: (
              <InputTimeout
                placeholder={t("kho.tenHangHoa")}
                onChange={onSearchInput("maDichVu")}
              />
            ),
          }),
          Column({
            title: t("kho.tenHangHoa"),
            width: 180,
            dataIndex: "tenDichVu",
            key: "tenDichVu",
            i18Name: "kho.tenHangHoa",
            renderSearch: (
              <InputTimeout
                placeholder={t("kho.tenHangHoa")}
                onChange={onSearchInput("tenDichVu")}
              />
            ),
          }),
          Column({
            title: t("kho.dvt"),
            width: 100,
            dataIndex: "tenDonViTinh",
            key: "tenDonViTinh",
            i18Name: "kho.dvt",
          }),
          ...(dsLoai === THUOC_LINH_DU_TRU
            ? [
                Column({
                  title: t("kho.dvtSoCap"),
                  width: 100,
                  key: "tenDvtSoCap",
                  dataIndex: "tenDvtSoCap",
                  i18Name: "kho.dvtSoCap",
                }),
              ]
            : []),
          Column({
            title: t("kho.hoatChat"),
            width: 120,
            dataIndex: "tenHoatChat",
            key: "tenHoatChat",
            i18Name: "kho.hoatChat",
          }),
          Column({
            title: t("kho.hamLuong"),
            width: 120,
            dataIndex: "hamLuong",
            key: "hamLuong",
            i18Name: "kho.hamLuong",
          }),
          Column({
            title: t("kho.slYeuCau"),
            dataIndex: "soLuongYeuCau",
            key: "soLuongYeuCau",
            i18Name: "kho.slYeuCau",
            width: 80,
            align: "right",
          }),
          ...(dsLoai === THUOC_LINH_DU_TRU
            ? [
                Column({
                  title: t("kho.slYeuCauSoCap"),
                  dataIndex: "soLuongYeuCauSoCap",
                  key: "soLuongYeuCauSoCap",
                  i18Name: "kho.slYeuCauSoCap",
                  width: 80,
                  align: "right",
                }),
              ]
            : []),
          Column({
            title: t("kho.slDuyet"),
            dataIndex: "soLuong",
            key: "soLuong",
            i18Name: "kho.slDuyet",
            width: 80,
            align: "right",
            render: (field) => (field == 0 ? field : formatNumber(field)),
          }),
          ...(dsLoai === THUOC_LINH_DU_TRU
            ? [
                Column({
                  title: t("kho.slDuyetSoCap"),
                  dataIndex: "soLuongSoCap",
                  key: "soLuongSoCap",
                  i18Name: "kho.slDuyetSoCap",
                  width: 80,
                  align: "right",
                  render: (_, record) => {
                    let soLuongSoCap =
                      record?.soLuongSoCap === 0 ? 0 : record?.soLuongSoCap;
                    if (isEdit) {
                      return (
                        <InputTimeout
                          value={soLuongSoCap}
                          onChange={onChangeList("soLuongSoCap", record)}
                          type="number"
                          min={0}
                        />
                      );
                    }
                    return soLuongSoCap ? formatNumber(soLuongSoCap) : "";
                  },
                }),
              ]
            : []),
          Column({
            title: t("kho.soLo"),
            dataIndex: "soLo",
            key: "soLo",
            i18Name: "kho.soLo",
            width: 100,
          }),
          Column({
            title: t("kho.quyetDinhThau.title"),
            dataIndex: "quyetDinhThau",
            key: "quyetDinhThau",
            i18Name: "kho.quyetDinhThau.title",
            width: 100,
          }),
          Column({
            title: t("quanLyNoiTru.dvNoiTru.hanSuDung"),
            width: 100,
            dataIndex: "ngayHanSuDung",
            key: "ngayHanSuDung",
            i18Name: "quanLyNoiTru.dvNoiTru.hanSuDung",
            render: (item) => item && moment(item).format("DD/MM/YYYY"),
          }),
          ...(dsLoai === THUOC_LINH_DU_TRU
            ? [
                Column({
                  title: t("kho.coSoTren"),
                  dataIndex: "coSoTren",
                  key: "coSoTren",
                  i18Name: "kho.coSoTren",
                  width: 80,
                  align: "right",
                  render: (_, record) =>
                    record?.coSoTren === 0
                      ? 0
                      : record?.coSoTren
                      ? formatNumber(record.coSoTren)
                      : "",
                }),
                Column({
                  title: t("kho.coSoDuoiKhoNhap"),
                  dataIndex: "coSoDuoi",
                  key: "coSoDuoi",
                  i18Name: "kho.coSoDuoiKhoNhap",
                  width: 80,
                  align: "right",
                  render: (_, record) =>
                    record?.coSoDuoi === 0
                      ? 0
                      : record?.coSoDuoi
                      ? formatNumber(record.coSoDuoi)
                      : "",
                }),
                Column({
                  title: t("kho.soLuongTonThucTeSoCap"),
                  dataIndex: "soLuongTon",
                  key: "soLuongTon",
                  i18Name: "kho.soLuongTonThucTeSoCap",
                  width: 80,
                  align: "right",
                  render: (_, record) =>
                    record?.soLuongTon === 0
                      ? 0
                      : record?.soLuongTon
                      ? formatNumber(record.soLuongTon)
                      : "",
                }),
                ...generateTonKhoColumns(chiTietPhieuNhapXuat.kho, [
                  {
                    key: "slNhapTon",
                    dataIndex: "slNhapTon",
                    label: "kho.slTonThucTe",
                  },
                  {
                    key: "slNhapTonSoCap",
                    dataIndex: "slNhapTonSoCap",
                    label: "kho.slTonThucTeSoCapDdls",
                  },
                  {
                    key: "slNhapTonKhaDung",
                    dataIndex: "slNhapTonKhaDung",
                    label: "kho.slTonKhaDung",
                  },
                  {
                    key: "slNhapTonKhaDungSoCap",
                    dataIndex: "slNhapTonKhaDungSoCap",
                    label: "kho.slTonKhaDungSoCapDdls",
                  },
                ]),
                ...generateTonKhoColumns(chiTietPhieuNhapXuat.khoDoiUng, [
                  {
                    key: "slTon",
                    dataIndex: "slTon",
                    label: "kho.slTonThucTe",
                  },
                  {
                    key: "slTonSoCap",
                    dataIndex: "slTonSoCap",
                    label: "kho.slTonThucTeSoCapDdls",
                  },
                  {
                    key: "slTonKhaDung",
                    dataIndex: "slTonKhaDung",
                    label: "kho.slTonKhaDung",
                  },
                  {
                    key: "slTonKhaDungSoCap",
                    dataIndex: "slTonKhaDungSoCap",
                    label: "kho.slTonKhaDungSoCapDdls",
                  },
                ]),
              ]
            : []),
          Column({
            title: (
              <>
                {t("common.tienIch")}
                <Setting refTable={refSetting} />
              </>
            ),
            width: 100,
            key: "action",
            fixed: "right",
            ignore: true,
            align: "center",
            render: (record) => {
              return (
                <>
                  <Tooltip title={t("common.xemChiTiet")}>
                    <SVG.IcEye className="ic-action" />
                  </Tooltip>
                </>
              );
            },
          }),
        ]
      : [
          Column({
            title: t("common.stt"),
            width: 60,
            dataIndex: "index",
            key: "index",
            align: "center",
            fixed: "left",
            ignore: true,
            render: (_, __, index) => index + 1,
          }),
          Column({
            title: t("kho.maHangHoa"),
            width: 140,
            dataIndex: "dichVu",
            key: "maDichVu",
            i18Name: "kho.maHangHoa",
            render: (item) => item.ma,
          }),
          Column({
            title: t("kho.tenHangHoa"),
            width: 180,
            dataIndex: "dichVu",
            key: "tenHangHoa",
            i18Name: "kho.tenHangHoa",
            render: (item) => {
              return (
                <div>
                  {item.ten} - {item.hamLuong}
                </div>
              );
            },
          }),
          Column({
            title: t("kho.hamLuong"),
            width: 120,
            dataIndex: "dichVu",
            key: "hamLuong",
            i18Name: "kho.hamLuong",
            render: (item) => {
              return <div>{item.hamLuong}</div>;
            },
          }),
          Column({
            title: t("kho.hoatChat"),
            width: 120,
            dataIndex: "dichVu",
            key: "tenHoatChat",
            i18Name: "kho.hoatChat",
            render: (item) => {
              return <div>{item.tenHoatChat}</div>;
            },
          }),
          ...(dsLoai === THUOC_LINH_NOI_TRU || dsLoai === PHIEU_LINH_BU_TU_TRUC
            ? [
                Column({
                  title: t("kho.slYeuCauSoCap"),
                  dataIndex: "soLuongSoCapYeuCau",
                  key: "soLuongSoCapYeuCau",
                  i18Name: "kho.slYeuCauSoCap",
                  width: 80,
                  align: "right",
                }),
              ]
            : []),
          ...(dsLoai === THUOC_LINH_NOI_TRU || dsLoai === PHIEU_LINH_BU_TU_TRUC
            ? [
                Column({
                  title: t("kho.slDuyetSoCap"),
                  dataIndex: "soLuongSoCap",
                  key: "soLuongSoCap",
                  i18Name: "kho.slDuyetSoCap",
                  width: 80,
                  align: "right",
                  render: (_, record) =>
                    record?.soLuongSoCap === 0
                      ? 0
                      : record?.soLuongSoCap
                      ? formatNumber(record.soLuongSoCap)
                      : "",
                }),
              ]
            : []),
          ...(dsLoai === THUOC_LINH_NOI_TRU || dsLoai === PHIEU_LINH_BU_TU_TRUC
            ? [
                Column({
                  title: t("kho.dvtSoCap"),
                  width: 100,
                  dataIndex: "dichVu",
                  key: "tenDvtSoCap",
                  i18Name: "kho.dvtSoCap",
                  render: (item) => {
                    return <div>{item.tenDvtSoCap}</div>;
                  },
                }),
              ]
            : []),
          Column({
            title: t("kho.slYeuCau"),
            dataIndex: "soLuongYeuCau",
            key: "soLuongYeuCau",
            i18Name: "kho.slYeuCau",
            width: 80,
            align: "right",
          }),
          Column({
            title: t("kho.slDuyet"),
            dataIndex: "soLuong",
            key: "soLuong",
            i18Name: "kho.slDuyet",
            width: 80,
            align: "right",
            render: (field) => (field == 0 ? field : formatNumber(field)),
          }),
          ...(dsLoai === THUOC_LINH_NOI_TRU
            ? [
                Column({
                  title: t("quanLyNoiTru.slHuy"),
                  width: 80,
                  dataIndex: "soLuongHuy",
                  key: "soLuongHuy",
                  i18Name: "quanLyNoiTru.slHuy",
                  align: "right",
                  render: (item, data) =>{
                    return parseFloat(item?.toPrecision(12));
                  }                  
                }),
              ]
            : []),
          Column({
            title: t("kho.dvt"),
            width: 100,
            dataIndex: "dichVu",
            key: "tenDonViTinh",
            i18Name: "kho.dvt",
            render: (item) => {
              return <div>{item.tenDonViTinh}</div>;
            },
          }),
          Column({
            title: t("common.donGia"),
            width: 100,
            dataIndex: "loNhap",
            key: "giaKhongBaoHiem",
            i18Name: "common.donGia",
            align: "right",
            render: (item) => (item.giaKhongBaoHiem || 0).formatPrice(),
          }),
          Column({
            title: t("kho.quyetDinhThau.tyLeThanhToanBh"),
            width: 100,
            dataIndex: "dichVu",
            key: "tyLeBhTt",
            i18Name: "kho.quyetDinhThau.tyLeThanhToanBh",
            align: "right",
            render: (item) => item.tyLeBhTt,
          }),
          Column({
            title: (
              <>
                {t("common.tienIch")}
                <Setting refTable={refSetting} />
              </>
            ),
            width: 100,
            key: "action",
            fixed: "right",
            ignore: true,
            align: "center",
            render: (record) => {
              return (
                <>
                  {!isEdit && (
                    <Tooltip title={t("common.xemChiTiet")}>
                      <SVG.IcEye className="ic-action" />
                    </Tooltip>
                  )}
                </>
              );
            },
          }),
        ];

  return (
    <Main noPadding={true}>
      <TableWrapper
        columns={columns}
        dataSource={isArray(dsThuocEdit, true) ? dsThuocEdit : dataSource}
        rowKey={(record) => `${record.id}`}
        ref={refSetting}
        tableName="table_Kho_DsDuyetDuocLamSang_HangHoaLinhNoiTru"
        styleWrap={{ height: "100%" }}
        onRow={onRow}
        rowClassName={(record) =>
          record.id == selectedId ? "row-selected-detail" : ""
        }
      />
      <ModalChiTietLinhThuoc ref={refModalChiTietLinhThuoc} dsLoai={dsLoai} />
    </Main>
  );
};

export default DanhSachHangHoa;
