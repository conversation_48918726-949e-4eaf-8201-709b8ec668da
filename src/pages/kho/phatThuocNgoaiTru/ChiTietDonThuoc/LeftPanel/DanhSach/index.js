import React, { useState, useEffect, useRef, useMemo } from "react";
import { useDispatch } from "react-redux";
import { Row } from "antd";
import { <PERSON><PERSON><PERSON>, HeaderSearch, TableWrapper } from "components";
import { Main, Header, PopoverCustom } from "./styled";
import { cloneDeep, isArray, orderBy } from "lodash";
import moment from "moment";
import { useEnum, useListAll, useLoading, useStore, useThietLap } from "hooks";
import {
  ENUM,
  HOTKEY,
  LOAI_NHAP_XUAT,
  THIET_LAP_CHUNG,
  TRANG_THAI_HOAN,
} from "constants/index";
import { SVG } from "assets";
import Icon from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { isNumber, roundToDigits } from "utils/index";
import ModalLichSuSuDungThuoc from "../../ModalLichSuSuDungThuoc";
import fileUtils from "utils/file-utils";
import ModalSapXepThuoc from "pages/khamBenh/DonThuoc/ModalSapXepThuoc";
import { useParams } from "react-router-dom";

const { Column } = TableWrapper;

const DanhSach = ({ layerId }) => {
  const infoPatientInit = useRef(null);
  const refModalLichSuSuDungThuoc = useRef(null);
  const { t } = useTranslation();
  const infoPatient = useStore("thuocChiTiet.infoPatient");
  const [listAllDonViTinh] = useListAll("donViTinh", {}, true);
  const [listTrangThaiDichVu] = useEnum(ENUM.TRANG_THAI_VACXIN);
  const { showLoading, hideLoading } = useLoading();
  const [listAllPhanLoaiThuoc] = useListAll("phanLoaiThuoc", {}, true);
  const [PHAN_LOAI_THUOC_HUONG_THAN] = useThietLap(
    THIET_LAP_CHUNG.PHAN_LOAI_THUOC_HUONG_THAN,
    "true"
  );
  const [PHAN_LOAI_THUOC_GAY_NGHIEN] = useThietLap(
    THIET_LAP_CHUNG.PHAN_LOAI_THUOC_GAY_NGHIEN,
    "true"
  );
  const {
    thuocChiTiet: { updateData, inTemThuoc, searchDonThuoc },
    phimTat: { onRegisterHotkey },
  } = useDispatch();
  const refMoDalSapXepThuoc = useRef(null);
  const { id } = useParams();

  const [listTrangThaiHoan] = useEnum(ENUM.TRANG_THAI_HOAN);
  const refSearch = useRef();
  const refSettings = useRef(null);

  const [state, _setState] = useState({
    dsThuoc: [],
    discount: 1,
    listTypeDiscountInPopup: [],
    listVisiblePopupOnLine: [],
    dsDichVu: [],
    dataSortColumn: {},
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const [TU_DONG_LAM_TRON_SO_LUONG_KE_THUOC] = useThietLap(
    THIET_LAP_CHUNG.TU_DONG_LAM_TRON_SO_LUONG_KE_THUOC
  );

  const onRow = (record, index) => {
    return {
      onClick: (event) => {
        updateData({ selectedDonThuoc: record });
        // onShowAndHandleUpdate(record);
      },
    };
  };
  useEffect(() => {
    // đăng ký phím tắt
    onRegisterHotkey({
      layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.F6, //F6
          onEvent: () => {
            refSearch.current && refSearch.current.focus();
          },
        },
      ],
    });
  }, []);

  const onClickSort = (key, value) => {
    setState({
      dataSortColumn: {
        ...state.dataSortColumn,
        [key]: value,
      },
    });
  };

  useEffect(() => {
    // set giá trị render lần đầu cho biến infoPatientInit, để xử lý sort
    if (
      infoPatientInit.current === null ||
      (infoPatientInit.current &&
        Object.keys(infoPatientInit.current).length === 0)
    ) {
      infoPatientInit.current = cloneDeep(infoPatient);
    }
  }, [infoPatient]);

  const contentNamePopover = (item) => {
    return (
      <div
        style={{
          pointerEvents: "none",
        }}
      >
        <div>
          <b>{`${item?.nbDichVu?.dichVu?.ma} - ${item?.nbDichVu?.dichVu?.ten} - ${item?.nbDichVu?.dichVu?.hamLuong}`}</b>
        </div>
        <div>
          {t("hsba.lieuDungCachDung")}: <b>{item?.cachDung}</b>
        </div>

        <div>
          {t("nhaThuoc.dotDung")}: <b>{item?.dotDung}</b>
        </div>
        <div>
          {t("nhaThuoc.thoiGianDung")}:{" "}
          <b>{`Từ ${
            item?.ngayThucHienTu &&
            moment(item?.ngayThucHienTu).format("DD/MM/YYYY")
          } - Đến 
        ${
          item?.ngayThucHienDen &&
          moment(item?.ngayThucHienDen).format("DD/MM/YYYY")
        } `}</b>
        </div>
        <div>
          {t("common.duongDung")} :{" "}
          <b>{item?.nbDichVu?.dichVu?.tenDuongDung}</b>
        </div>
        <div>
          {t("nhaThuoc.loNhap")} : <b>{item?.nbDvKho?.loNhap?.soLo}</b>
        </div>
        <div>
          {t("nhaThuoc.trangThaiThanhToan")} :{" "}
          <b>
            {`${
              !infoPatient?.phieuThu?.thanhToan
                ? t("nhaThuoc.chuaThanhToan")
                : t("nhaThuoc.daThanhToan")
            }`}
          </b>
        </div>
        <div>
          {t("kho.khoaChiDinh")} : <b>{item?.nbDichVu?.khoaChiDinh?.ten}</b>
        </div>
        <div>
          {t("nhaThuoc.ngayKe")} :{" "}
          <b>
            {item?.nbDichVu?.thoiGianChiDinh &&
              moment(item?.nbDichVu?.thoiGianChiDinh).format("DD/MM/YYYY")}
          </b>
        </div>
      </div>
    );
  };

  const onPrintTemThuoc = (data) => {
    inTemThuoc({
      phieuNhapXuatId: infoPatient?.phieuXuat?.id,
      loaiNhapXuat: 120,
      dsId: data?.nbDichVu?.id,
    });
  };

  const onSettings = () => {
    refSettings && refSettings.current.settings();
  };

  const TinhSoCap = (field, item, heSoDinhMuc = 1) => {
    let value = item?.[field] ? item?.[field] / heSoDinhMuc : null;
    value =
      value && TU_DONG_LAM_TRON_SO_LUONG_KE_THUOC.toLowerCase() == "true"
        ? roundToDigits(value, 3)
        : value;
    return value;
  };
  const onViewHdsd = (dsTaiLieuHdsd) => async (e) => {
    e.stopPropagation();
    try {
      showLoading();
      const byteArray = await fileUtils.getFromUrl({
        url: fileUtils.absoluteFileUrl(dsTaiLieuHdsd[0]),
      });
      const blob = new Blob([new Uint8Array(byteArray)], {
        type: "application/pdf",
      });
      const blobUrl = window.URL.createObjectURL(blob);

      window.open(blobUrl, "_blank").focus();
    } catch (error) {
      console.error(error?.message || error);
    } finally {
      hideLoading();
    }
  };
  const columns = useMemo(() => {
    if (
      infoPatient.phieuXuat?.loaiNhapXuat == LOAI_NHAP_XUAT.XUAT_VAC_XIN_TIEM
    ) {
      return [
        Column({
          title: t("common.stt"),
          width: 50,
          dataIndex: "index",
          key: "index",
          align: "center",
          i18Name: "common.stt",
        }),
        Column({
          title: t("kho.tenHangHoa"),
          width: 220,
          dataIndex: "tenDichVu",
          key: "tenDichVu",
          align: "center",
          i18Name: "kho.tenHangHoa",
          render: (item, record) => {
            const { lasa, nguyCoCao, dsTaiLieuHdsd } = record || {};

            return (
              <div className="flex gap-4">
                {isArray(dsTaiLieuHdsd, true) && (
                  <SVG.IcInfo onClick={onViewHdsd(dsTaiLieuHdsd)} />
                )}
                {lasa && (
                  <Tooltip
                    title={t("nhaThuoc.luuYThuocLasa")}
                    placement="topLeft"
                  >
                    <SVG.IcLasa />
                  </Tooltip>
                )}
                {nguyCoCao && (
                  <Tooltip
                    title={t("nhaThuoc.luuYThuocNguyCoCao")}
                    placement="topLeft"
                  >
                    <SVG.IcNguyCoCao />
                  </Tooltip>
                )}
                {item}
              </div>
            );
          },
        }),
        Column({
          title: t("kho.dvt"),
          width: 80,
          dataIndex: "donViTinhId",
          key: "donViTinhId",
          align: "center",
          i18Name: "kho.dvt",
          render: (value) => {
            return listAllDonViTinh?.find((item) => item.id == value)?.ten;
          },
        }),
        Column({
          title: t("kho.soLo"),
          width: 100,
          dataIndex: "soLo",
          key: "soLo",
          align: "center",
          i18Name: "kho.soLo",
        }),

        Column({
          title: t("kho.slDuyet"),
          width: 80,
          dataIndex: "soLuong",
          key: "soLuong",
          align: "center",
          i18Name: "kho.slDuyet",
        }),
        Column({
          title: t("kho.hanSuDung"),
          width: 120,
          dataIndex: "ngayHanSuDung",
          key: "ngayHanSuDung",
          align: "center",
          i18Name: "kho.hanSuDung",
          render: (value) => {
            return value?.toDateObject().format("dd/MM/yyyy");
          },
        }),
        Column({
          title: t("kho.phatThuoc.bacSiChiDinh"),
          width: 160,
          dataIndex: "tenBacSiChiDinh",
          key: "tenBacSiChiDinh",
          align: "center",
          i18Name: "kho.phatThuoc.bacSiChiDinh",
        }),

        Column({
          title: t("common.trangThai"),
          width: 100,
          dataIndex: "trangThai",
          key: "trangThai",
          align: "center",
          i18Name: "common.trangThai",
          render: (value) => {
            return (
              listTrangThaiDichVu?.find((item) => item.id == value)?.ten || ""
            );
          },
        }),
      ];
    } else {
      return [
        {
          title: <HeaderSearch title={t("common.stt")} />,
          width: 50,
          dataIndex: "index",
          key: "index",
          align: "center",
          ignore: true,
        },
        {
          title: (
            <HeaderSearch
              title={t("kho.phatThuoc.tenThuoc")}
              sort_key="nbDichVu.dichVu.ten"
              onClickSort={onClickSort}
              dataSort={state?.dataSortColumn["nbDichVu.dichVu.ten"] || ""}
            />
          ),
          width: 240,
          // dataIndex: "nbDichVu",
          key: "ten",
          i18Name: "kho.phatThuoc.tenThuoc",
          show: true,
          className: "tenDv",
          render: (item) => {
            const { lasa, nguyCoCao, dsTaiLieuHdsd } =
              item?.nbDichVu?.dichVu || {};
            return (
              <div className="flex gap-4">
                {isArray(dsTaiLieuHdsd, true) && (
                  <SVG.IcInfo onClick={onViewHdsd(dsTaiLieuHdsd)} />
                )}
                {lasa && (
                  <Tooltip
                    title={t("nhaThuoc.luuYThuocLasa")}
                    placement="topLeft"
                  >
                    <SVG.IcLasa />
                  </Tooltip>
                )}
                {nguyCoCao && (
                  <Tooltip
                    title={t("nhaThuoc.luuYThuocNguyCoCao")}
                    placement="topLeft"
                  >
                    <SVG.IcNguyCoCao />
                  </Tooltip>
                )}
                <div>
                  <PopoverCustom
                    className="popup-custom"
                    placement="right"
                    content={contentNamePopover(item)}
                    trigger={["hover", "click"]}
                  >
                    <div style={{ color: "#0762F7", fontWeight: "bold" }}>
                      {item?.nbDichVu?.dichVu?.ten}
                    </div>
                  </PopoverCustom>
                </div>
              </div>
            );
          },
        },
        {
          title: (
            <HeaderSearch
              title={t("kho.phatThuoc.hamLuong")}
              sort_key="nbDichVu.dichVu.hamLuong"
              onClickSort={onClickSort}
              dataSort={state?.dataSortColumn["nbDichVu.dichVu.hamLuong"] || ""}
            />
          ),
          width: 160,
          dataIndex: "nbDichVu",
          key: "hamLuong",
          i18Name: "kho.phatThuoc.hamLuong",
          show: true,
          render: (item) => {
            return item?.dichVu?.hamLuong;
          },
        },
        {
          title: <HeaderSearch title={t("kho.phatThuoc.lanNgay")} />,
          width: 80,
          dataIndex: "soLan1Ngay",
          key: "soLan1Ngay",
          align: "right",
          show: false,
          i18Name: "kho.phatThuoc.lanNgay",
        },
        {
          title: <HeaderSearch title={t("kho.phatThuoc.slLan")} />,
          width: 80,
          dataIndex: "soLuong1Lan",
          key: "soLuong1Lan",
          align: "right",
          show: false,
          i18Name: "kho.phatThuoc.slLan",
        },
        {
          title: <HeaderSearch title={t("kho.phatThuoc.soNgay")} />,
          width: 80,
          dataIndex: "soNgay",
          key: "soNgay",
          align: "right",
          show: false,
          i18Name: "kho.phatThuoc.soNgay",
        },
        {
          title: (
            <HeaderSearch
              title={t("kho.phatThuoc.slPhat")}
              sort_key="nbDichVu.soLuong"
              onClickSort={onClickSort}
              dataSort={state?.dataSortColumn["nbDichVu.soLuong"] || ""}
            />
          ),
          width: 80,
          dataIndex: "nbDichVu",
          key: "soLuongBan",
          align: "right",
          i18Name: "kho.phatThuoc.slPhat",
          show: true,
          render: (item, record) =>
            TinhSoCap("soLuong", item, record?.nbDichVu?.dichVu?.heSoDinhMuc),
        },
        {
          title: (
            <HeaderSearch
              title={t("kho.phatThuoc.slKe")}
              sort_key="nbDvKho.soLuongYeuCau"
              onClickSort={onClickSort}
              dataSort={state?.dataSortColumn["nbDvKho.soLuongYeuCau"] || ""}
            />
          ),
          key: "soLuongYeuCau",
          width: 70,
          dataIndex: "nbDvKho",
          align: "right",
          i18Name: "kho.phatThuoc.slKe",
          show: true,
          render: (item, record) =>
            TinhSoCap(
              "soLuongYeuCau",
              item,
              record?.nbDichVu?.dichVu?.heSoDinhMuc
            ),
        },
        {
          title: (
            <HeaderSearch
              title={t("kho.phatThuoc.dvt")}
              sort_key="nbDichVu.dichVu.tenDonViTinh"
              onClickSort={onClickSort}
              dataSort={
                state?.dataSortColumn["nbDichVu.dichVu.tenDonViTinh"] || ""
              }
            />
          ),
          width: 65,
          dataIndex: "nbDvKho",
          key: "tenDonViTinh",
          align: "center",
          i18Name: "kho.phatThuoc.dvt",
          show: true,
          render: (item, record) => {
            return record?.nbDichVu?.dichVu?.tenDvtSoCap;
          },
        },
        {
          title: (
            <HeaderSearch
              title={t("kho.phatThuoc.trangThaiHoan")}
              sort_key="nbDichVu.trangThaiHoan"
              onClickSort={onClickSort}
              dataSort={state?.dataSortColumn["nbDichVu.trangThaiHoan"] || ""}
            />
          ),
          width: 130,
          dataIndex: "nbDichVu",
          key: "trangThaiHoan",
          align: "center",
          i18Name: "kho.phatThuoc.trangThaiHoan",
          show: true,
          render: (item) => {
            return listTrangThaiHoan?.find((x) => x.id === item?.trangThaiHoan)
              ?.ten;
          },
        },
        {
          title: (
            <HeaderSearch
              title={t("kho.phatThuoc.giaBaoHiem")}
              sort_key="nbDichVu.trangThaiHoan"
              onClickSort={onClickSort}
              dataSort={state?.dataSortColumn["nbDichVu.giaBaoHiem"] || ""}
            />
          ),
          width: 110,
          dataIndex: "nbDichVu",
          key: "giaBaoHiem",
          align: "right",
          i18Name: "kho.phatThuoc.giaBaoHiem",
          show: true,
          render: (item) => {
            return (item?.giaBaoHiem || 0).formatPrice();
          },
        },
        {
          title: (
            <HeaderSearch
              title={t("kho.phatThuoc.giaKhongBaoHiem")}
              sort_key="nbDichVu.trangThaiHoan"
              onClickSort={onClickSort}
              dataSort={state?.dataSortColumn["nbDichVu.giaKhongBaoHiem"] || ""}
            />
          ),
          width: 115,
          dataIndex: "nbDichVu",
          key: "giaKhongBaoHiem",
          align: "right",
          i18Name: "kho.phatThuoc.giaKhongBaoHiem",
          show: true,
          render: (item) => {
            return (item?.giaKhongBaoHiem || 0).formatPrice();
          },
        },
        {
          title: (
            <HeaderSearch
              title={t("kho.phatThuoc.giaPhuThu")}
              sort_key="nbDichVu.giaPhuThu"
              onClickSort={onClickSort}
              dataSort={state?.dataSortColumn["nbDichVu.giaPhuThu"] || ""}
            />
          ),
          width: 110,
          dataIndex: "nbDichVu",
          key: "giaPhuThu",
          align: "right",
          i18Name: "kho.phatThuoc.giaPhuThu",
          show: true,
          render: (item) => {
            return (item?.giaPhuThu || 0).formatPrice();
          },
        },
        {
          title: (
            <HeaderSearch
              title={t("kho.phatThuoc.bacSiChiDinh")}
              sort_key="nbDichVu.bacSiChiDinh.ten"
              onClickSort={onClickSort}
              dataSort={
                state?.dataSortColumn["nbDichVu.bacSiChiDinh.ten"] || ""
              }
            />
          ),
          width: 130,
          dataIndex: "nbDichVu",
          key: "bacSiChiDinh",
          align: "center",
          i18Name: "kho.phatThuoc.bacSiChiDinh",
          show: true,
          render: (item) => {
            return item?.bacSiChiDinh?.ten;
          },
        },
        {
          title: (
            <HeaderSearch
              title={t("common.phongChiDinh")}
              sort_key="nbDvKho.tenPhongChiDinh"
              onClickSort={onClickSort}
              dataSort={state?.dataSortColumn["nbDvKho.tenPhongChiDinh"] || ""}
            />
          ),
          width: 180,
          dataIndex: "nbDvKho",
          key: "tenPhongChiDinh",
          align: "center",
          i18Name: "common.phongChiDinh",
          show: true,
          render: (item) => {
            return item?.tenPhongChiDinh;
          },
        },
        {
          title: <HeaderSearch title={t("thuNgan.tongTienBH")} />,
          width: 120,
          dataIndex: "nbDichVu",
          key: "tongTienBh",
          align: "right",
          i18Name: "thuNgan.tongTienBH",
          show: true,
          render: (item, record) => {
            const soLuong = TinhSoCap(
              "soLuongYeuCau",
              record?.nbDvKho,
              item?.dichVu?.heSoDinhMuc
            );
            const tongTien =
              isNumber(soLuong) && isNumber(item?.giaBaoHiem)
                ? soLuong * item.giaBaoHiem
                : "";
            return (
              <div style={{ color: "#0762F7", fontWeight: "bold" }}>
                {tongTien ? tongTien.formatPrice() : ""}
              </div>
            );
          },
        },
        {
          title: (
            <HeaderSearch
              title={t("kho.phatThuoc.thanhTien")}
              sort_key="nbDichVu.thanhTien"
              onClickSort={onClickSort}
              dataSort={state?.dataSortColumn["nbDichVu.thanhTien"] || ""}
            />
          ),
          width: 120,
          dataIndex: "nbDichVu",
          key: "thanhTien",
          align: "right",
          i18Name: "kho.phatThuoc.thanhTien",
          show: true,
          render: (item) => {
            return (
              <div style={{ color: "#0762F7", fontWeight: "bold" }}>
                {(item?.thanhTien || 0).formatPrice()}
              </div>
            );
          },
        },
        {
          title: (
            <HeaderSearch
              title={t("kho.phanLoaiThuoc")}
              sort_key="phanLoaiDvKhoId"
              onClickSort={onClickSort}
              dataSort={state?.dataSortColumn["phanLoaiDvKhoId"] || ""}
            />
          ),
          width: 150,
          dataIndex: "phanLoaiDvKhoId",
          key: "phanLoaiDvKhoId",
          i18Name: "kho.phanLoaiThuoc",
          show: false,
          render: (item, data) =>
            listAllPhanLoaiThuoc?.find(
              (x) => x.id === data?.nbDichVu?.dichVu?.phanLoaiDvKhoId
            )?.ten,
        },
        {
          width: 80,
          title: (
            <HeaderSearch
              title={
                <>
                  <SVG.IcSetting
                    onClick={onSettings}
                    style={{ cursor: "pointer" }}
                  />
                </>
              }
            />
          ),
          align: "center",
          fixed: "right",
          ignore: true,
          render: (item, data, index) => {
            return (
              <div className="ic-action">
                <Tooltip title={t("phieuIn.inTemThuoc")} placement="bottom">
                  <Icon
                    component={SVG.IcPrint}
                    onClick={() => onPrintTemThuoc(data)}
                    className="icon"
                  />
                </Tooltip>
              </div>
            );
          },
        },
      ];
    }
  }, [infoPatient]);

  const dataSource = useMemo(() => {
    let data = [];
    if (
      infoPatient.phieuXuat?.loaiNhapXuat == LOAI_NHAP_XUAT.XUAT_VAC_XIN_TIEM
    ) {
      data = (infoPatient?.dsVacXin || [])
        .filter((x) =>
          [TRANG_THAI_HOAN.THUONG, TRANG_THAI_HOAN.CHO_DUYET_HOAN]?.includes(
            x.trangThaiHoan
          )
        )
        .map((x, index) => ({ ...x, index: index + 1 }));
    } else {
      data = (infoPatient?.dsThuoc || [])
        .filter((x) =>
          [TRANG_THAI_HOAN.THUONG, TRANG_THAI_HOAN.CHO_DUYET_HOAN]?.includes(
            x.nbDichVu?.trangThaiHoan
          )
        )
        .map((x, index) => ({ ...x, index: index + 1 }));
    }
    data = orderBy(data, ["nbDichVu.sttHienThi"], "asc");
    return data;
  }, [infoPatient]);

  const onShowLichSuSuDungThuoc = () => {
    refModalLichSuSuDungThuoc.current &&
      refModalLichSuSuDungThuoc.current.show();
  };

  const onSaveModalCustomizeColumn = () => {
    searchDonThuoc(id);
  };
  return (
    <Main noPadding={true} bottom={0}>
      <Header>
        <div className="header">
          <Row className="header-row">
            <div className="content">{t("kho.dsChiTiet")}</div>
            <div style={{ display: "flex", alignItems: "center" }}>
              <Tooltip title={t("common.sapXepThongTinThuoc")}>
                <SVG.IcSetting
                  onClick={() =>
                    refMoDalSapXepThuoc.current &&
                    refMoDalSapXepThuoc.current.show({})
                  }
                />
              </Tooltip>
              <Tooltip title={t("kho.lichSu.lichSuSuDungThuoc")}>
                <SVG.IcList onClick={onShowLichSuSuDungThuoc} />
              </Tooltip>
            </div>
          </Row>
        </div>
      </Header>
      <TableWrapper
        scroll={{ y: 453 }}
        rowKey={"id"}
        onRow={onRow}
        rowClassName={(record, index) => {
          const phanLoaiThuoc = listAllPhanLoaiThuoc?.find(
            (x) => x.id === record?.nbDichVu?.dichVu?.phanLoaiDvKhoId
          );
          if (
            PHAN_LOAI_THUOC_HUONG_THAN?.split(",")?.includes(
              phanLoaiThuoc?.ma
            ) ||
            PHAN_LOAI_THUOC_GAY_NGHIEN?.split(",")?.includes(phanLoaiThuoc?.ma)
          ) {
            return "row-color";
          }
          return "";
        }}
        columns={columns}
        dataSource={dataSource}
        ref={refSettings}
        tableName="table_KHO_PhatThuocNgoaiTru_DSThuoc"
      />

      <ModalLichSuSuDungThuoc ref={refModalLichSuSuDungThuoc} />
      <ModalSapXepThuoc
        ref={refMoDalSapXepThuoc}
        listData={dataSource}
        onSaveModalCustomizeColumn={onSaveModalCustomizeColumn}
      />
    </Main>
  );
};

export default DanhSach;
