import React, { useEffect, useMemo, useRef } from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { selectMaTen } from "redux-store/selectors";
import moment from "moment";
import { Col, Form, Input, message, Row } from "antd";
import { isArray, openInNewTab } from "utils";
import ModalNhapLyDo from "pages/kho/components/ModalNhapLyDo";
import Header1 from "pages/kho/components/Header1";
import { DateTimePicker, Select } from "components";
import { useStore, useListAll, useThietLap } from "hooks";
import { checkRole } from "lib-utils/role-utils";
import { ROLES, THIET_LAP_CHUNG } from "constants/index";
import ActionCustom from "./Action";
import { Main } from "./styled";

const { Item } = Form;

const ThongTinPhieuNhap = ({ form, isFormChange, setIsFormChange }) => {
  const listTongHop = useStore("hinhThucNhapXuat.listTongHop", []);
  const listNguonNhapKho = useStore("nguonNhapKho.listDataTongHop", []);
  const listDataTongHop = useStore("doiTac.listDataTongHop", []);
  const { t } = useTranslation();
  const thongTinPhieu = useStore("phieuNhapXuat.thongTinPhieu", {});
  const [listAllBenhVien] = useListAll("benhVien", {}, true);
  const listPhieuXuat = useStore("phieuXuat.listPhieuXuat", []);
  const [dataCHAN_NHAP_KHO_KHI_HET_HAN_HD] = useThietLap(
    THIET_LAP_CHUNG.CHAN_NHAP_KHO_KHI_HET_HAN_HD
  );

  const {
    nhapKho: { kiemTraSoHoaDon },
    nguonNhapKho: { onSearchTongHop },
    hinhThucNhapXuat: { getTongHop },
    doiTac: { getListTongHop },
    phieuNhapXuat: { patch },
    phieuXuat: { getListPhieuXuat },
  } = useDispatch();

  const refModalNhapLyDo = useRef(null);

  useEffect(() => {
    getTongHop({ active: true, dsHinhThucNhapXuat: 10, page: "", size: "" });
  }, []);

  const onBlur = (type) => (e) => {
    const value = e?.hasOwnProperty("target")
      ? e.target.value
      : e?.hasOwnProperty("_d")
      ? moment(e._d)
      : e;
    if (type == "soHoaDon") {
      //TODO: check trung hoa don
      kiemTraSoHoaDon({
        [type]: value,
        id: thongTinPhieu?.id ? thongTinPhieu?.id : 0,
        ngayHoaDon: moment(form.getFieldValue("ngayHoaDon")).format(
          "YYYY-MM-DD"
        ),
      });
    }
    if (type == "ngayHoaDon" && e) {
      kiemTraSoHoaDon({
        [type]: moment(value).format("YYYY-MM-DD"),
        id: thongTinPhieu?.id ? thongTinPhieu?.id : 0,
        soHoaDon: thongTinPhieu?.soHoaDon,
      });
    }
  };

  // console.log(thongTinPhieu?.nguoiTaoPhieu);

  useEffect(() => {
    if (thongTinPhieu) {
      let data = {
        tenKho: thongTinPhieu?.kho?.ten,
        nhaCungCapId: thongTinPhieu?.nhaCungCapId,
        tenQuyetDinhThau: thongTinPhieu?.quyetDinhThau?.quyetDinhThau
          ? `${thongTinPhieu?.quyetDinhThau?.quyetDinhThau} ${
              thongTinPhieu?.quyetDinhThau?.soThau
                ? `- ${thongTinPhieu?.quyetDinhThau?.soThau}`
                : ""
            }`
          : "",
        nguonNhapKhoId: thongTinPhieu?.nguonNhapKhoId,
        thoiGianDuyet: moment(thongTinPhieu?.thoiGianDuyet),
        hinhThucNhapXuatId: thongTinPhieu?.hinhThucNhapXuatId,
        soHoaDon: thongTinPhieu?.soHoaDon,
        ngayHoaDon: moment(thongTinPhieu?.ngayHoaDon),
        kyHieuHoaDon: thongTinPhieu?.kyHieuHoaDon,
        soHopDong: thongTinPhieu?.soHopDong,
        ngayHopDong: thongTinPhieu?.ngayHopDong
          ? moment(thongTinPhieu?.ngayHopDong)
          : null,
        ngayHetHanHopDong: thongTinPhieu?.ngayHetHanHopDong
          ? moment(thongTinPhieu?.ngayHetHanHopDong)
          : null,
        ghiChu: thongTinPhieu?.ghiChu,
        tenNguoiTao: thongTinPhieu?.nguoiTaoPhieu?.ten,
        soPhieu: thongTinPhieu?.soPhieu,
        csKcbChuyenGiaoId: thongTinPhieu?.csKcbChuyenGiaoId,
        phieuDoiUngId: thongTinPhieu?.phieuDoiUngId,
        lyDo: thongTinPhieu?.lyDo,
      };

      form.setFieldsValue(data);
      let dataSearch = {
        active: true,
        dsLoaiDichVu: thongTinPhieu?.kho?.dsLoaiDichVu,
        thau: !!thongTinPhieu?.quyetDinhThauId,
        page: "",
        size: "",
      };
      onSearchTongHop({ dataSearch });
      getListTongHop({
        dsLoaiDoiTac: [20],
        active: true,
        page: "",
        size: "",
        dsLoaiDichVu: thongTinPhieu?.kho?.dsLoaiDichVu,
      });
      getListPhieuXuat({
        page: 0,
        size: 9999,
        dataSearch: {
          active: true,
          dsLoaiNhapXuat: [10],
          dsKhoId: [thongTinPhieu?.khoId],
        },
      });
    }
  }, [thongTinPhieu]);

  const listAllBV = useMemo(() => {
    let result = [];
    if (isArray(listAllBenhVien, true)) {
      result = listAllBenhVien.map((item) => ({
        ...item,
        ten: `${item.ma} - ${item.ten}`,
      }));
    }
    return result;
  }, [listAllBenhVien]);

  const onhandleSubmit = (values) => {
    let payload = {
      nhaCungCapId: values?.nhaCungCapId,
      nguonNhapKhoId: values?.nguonNhapKhoId,
      hinhThucNhapXuatId: values?.hinhThucNhapXuatId,
      soHoaDon: values?.soHoaDon,
      ngayHoaDon: moment(values?.ngayHoaDon).format("YYYY-MM-DD"),
      kyHieuHoaDon: values?.kyHieuHoaDon,
      soHopDong: values?.soHopDong,
      ngayHopDong: values?.ngayHopDong
        ? moment(values?.ngayHopDong).format("YYYY-MM-DD")
        : null,
      ngayHetHanHopDong: values?.ngayHetHanHopDong
        ? moment(values?.ngayHetHanHopDong).format("YYYY-MM-DD")
        : null,
      ghiChu: values?.ghiChu,
      id: thongTinPhieu?.id,
      nguoiDuyetId: thongTinPhieu.nguoiTaoPhieu?.id,
      csKcbChuyenGiaoId: thongTinPhieu.csKcbChuyenGiaoId,
      phieuDoiUngId: values?.phieuDoiUngId,
      thoiGianDuyet: moment(values?.thoiGianDuyet).format(
        "YYYY-MM-DD HH:mm:ss"
      ),
    };

    if (
      dataCHAN_NHAP_KHO_KHI_HET_HAN_HD?.eval() &&
      payload.ngayHopDong &&
      payload.ngayHoaDon &&
      payload.ngayHetHanHopDong
    ) {
      const ngayHopDong = moment(payload.ngayHopDong, "YYYY-MM-DD");
      const ngayHoaDon = moment(payload.ngayHoaDon, "YYYY-MM-DD");
      const ngayHetHan = moment(payload.ngayHetHanHopDong, "YYYY-MM-DD");

      if (
        !(
          ngayHoaDon.isSameOrAfter(ngayHopDong) &&
          ngayHoaDon.isSameOrBefore(ngayHetHan)
        )
      ) {
        message.error(
          t("kho.hopDongDaHetHieuLucDate", {
            ngayHetHanHopDong: ngayHetHan.format("DD / MM / YYYY"),
          })
        );
        return;
      }
    }

    patch(payload).finally(() => {
      setIsFormChange(false);
    });
  };

  return (
    <Main>
      <Form
        form={form}
        layout="vertical"
        style={{ width: "100%" }}
        onFinish={onhandleSubmit}
        onValuesChange={() => {
          setIsFormChange(true);
        }}
      >
        <Header1
          title={t("kho.thongTinPhieuNhap")}
          noPadding={true}
          bottom={10}
          left={0}
        />

        <Row gutter={[0, 0]}>
          <Col span={23}>
            <Item
              label={
                <div
                  className="pointer"
                  onClick={() => openInNewTab("/kho/quan-tri-kho")}
                >
                  {t("kho.khoNhap")}
                </div>
              }
              name="tenKho"
              style={{ width: "100%" }}
            >
              <Input disabled />
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={
                <div
                  className="pointer"
                  onClick={() => openInNewTab("/kho/quan-ly-thau")}
                >
                  {t("kho.quyetDinhThau.title")}
                </div>
              }
              name="tenQuyetDinhThau"
              style={{ width: "100%" }}
            >
              <Input className="input-option" disabled></Input>
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={
                <div
                  className="pointer"
                  onClick={() => openInNewTab("/danh-muc/doi-tac")}
                >
                  {t("kho.nhaCungCap")}
                </div>
              }
              name="nhaCungCapId"
              rules={[
                {
                  required: true,
                  message: t("kho.vuiLongChonNhaCungCap"),
                },
              ]}
              style={{ width: "100%" }}
            >
              <Select
                allowClear
                showSearch
                placeholder={t("kho.chonNhaCungCap")}
                data={listDataTongHop}
                getLabel={selectMaTen}
              />
            </Item>
          </Col>

          <Col span={23}>
            <Item
              label={
                <div
                  className="pointer"
                  onClick={() => openInNewTab("/danh-muc/nguon-nhap-kho")}
                >
                  {t("kho.nguonNhapKho")}
                </div>
              }
              name="nguonNhapKhoId"
              rules={[
                {
                  required: true,
                  message: t("kho.vuiLongChonNguonNhapKho"),
                },
              ]}
              style={{ width: "100%" }}
            >
              <Select
                allowClear
                showSearch
                placeholder={t("kho.vuiLongChonNguonNhapKho")}
                data={listNguonNhapKho}
              />
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={
                <div
                  className="pointer"
                  onClick={() => openInNewTab("/danh-muc/hinh-thuc-nhap-xuat")}
                >
                  {t("kho.hinhThucNhap")}
                </div>
              }
              name="hinhThucNhapXuatId"
              style={{ width: "100%" }}
            >
              <Select
                allowClear
                showSearch
                placeholder={t("kho.vuiLongChonHinhThucNhap")}
                data={listTongHop}
              />
            </Item>
          </Col>

          <Col span={23}>
            <Item
              label={t("kho.soHoaDon")}
              name="soHoaDon"
              style={{ width: "100%" }}
              rules={[
                {
                  required: true,
                  message: t("kho.vuiLongNhapSoHoaDon"),
                },
                {
                  whitespace: true,
                  message: t("kho.vuiLongNhapSoHoaDon"),
                },
              ]}
            >
              <Input
                className="input-option"
                placeholder={t("kho.vuiLongNhapSoHoaDon")}
              />
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={t("kho.ngayHoaDon")}
              name="ngayHoaDon"
              style={{ width: "100%" }}
              rules={[
                {
                  required: true,
                  message: t("kho.vuiLongChonNgayHoaDon"),
                },
              ]}
            >
              <DateTimePicker
                style={{ width: "100%" }}
                placeholder={t("kho.vuiLongChonNgayHoaDon")}
                format="DD / MM / YYYY"
                onBlur={onBlur("ngayHoaDon")}
                showTime={false}
              />
            </Item>
          </Col>

          <Col span={23}>
            <Item
              label={t("kho.kyHieuHoaDon")}
              name="kyHieuHoaDon"
              style={{ width: "100%" }}
            >
              <Input
                className="input-option"
                placeholder={t("kho.vuiLongNhapKyHieuHoaDon")}
              />
            </Item>
          </Col>

          <Col span={23}>
            <Item
              label={t("kho.soHopDong")}
              name="soHopDong"
              style={{ width: "100%" }}
            >
              <Input
                className="input-option"
                placeholder={t("kho.vuiLongNhapSoHopDong")}
              />
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={t("kho.quyetDinhThau.ngayHopDong")}
              name="ngayHopDong"
              style={{ width: "100%" }}
            >
              <DateTimePicker
                style={{ width: "100%" }}
                placeholder={t("kho.quyetDinhThau.chonNgayHopDong")}
                format="DD / MM / YYYY"
                showTime={false}
              />
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={t("kho.quyetDinhThau.ngayHetHanHopDong")}
              name="ngayHetHanHopDong"
              style={{ width: "100%" }}
            >
              <DateTimePicker
                style={{ width: "100%" }}
                placeholder={t("kho.quyetDinhThau.chonNgayHetHanHopDong")}
                format="DD / MM / YYYY"
                showTime={false}
              />
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={t("kho.soPhieu")}
              name="soPhieu"
              style={{ width: "100%" }}
            >
              <Input className="input-option" disabled />
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={<div className="pointer">{t("kho.thoiGianDuyet")}</div>}
              name="thoiGianDuyet"
              style={{ width: "100%" }}
            >
              <DateTimePicker
                disabled={
                  !checkRole([ROLES["KHO"].SUA_THOI_GIAN_DUYET_NGUOI_DUYET])
                }
                style={{ width: "100%" }}
                placeholder={t("kho.vuiLongChonThoiGianDuyet")}
                // format="DD / MM / YYYY"
                onBlur={onBlur("thoiGianDuyet")}
                showTime={true}
              />
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={
                <div
                  className="pointer"
                  onClick={() => openInNewTab("/danh-muc/benh-vien")}
                >
                  {t("kho.csytChuyenToi")}
                </div>
              }
              name="csKcbChuyenGiaoId"
              style={{ width: "100%" }}
              initialValue={thongTinPhieu?.csKcbChuyenGiaoId}
            >
              <Select
                allowClear
                showSearch
                placeholder={t("kho.vuiLongChonCsytChuyenToi")}
                data={listAllBV}
              />
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={<div className="pointer">{t("kho.chungTuHuy")}</div>}
              name="phieuDoiUngId"
              style={{ width: "100%" }}
              initialValue={thongTinPhieu?.phieuDoiUngId}
            >
              <Select
                allowClear
                showSearch
                placeholder={t("kho.chonChungTuHuy")}
                data={listPhieuXuat}
                getLabel={(item) => `${item.soPhieu} - ${item.soHoaDon}`}
              />
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={t("kho.ghiChu")}
              name="ghiChu"
              style={{ width: "100%" }}
            >
              <Input.TextArea
                rows={3}
                className="input-option"
                placeholder={t("kho.vuiLongNhapGhiChu")}
              />
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={t("khoMau.lyDoHuyDuyet")}
              name="lyDo"
              style={{ width: "100%" }}
            >
              <Input className="input-option" disabled />
            </Item>
          </Col>
          <Col span={23}>
            <Item
              label={t("kho.nguoiLapPhieu")}
              name="tenNguoiTao"
              style={{ width: "100%" }}
            >
              <Input className="input-option" disabled />
            </Item>
          </Col>
        </Row>
      </Form>
      <ActionCustom form={form} isFormChange={isFormChange} />
      <ModalNhapLyDo ref={refModalNhapLyDo} />
    </Main>
  );
};

export default ThongTinPhieuNhap;
