import { Radio, message } from "antd";
import React, {
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
  useEffect,
} from "react";
import { useSelector, useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { useHistory } from "react-router-dom";
import IcExpand from "assets/images/kho/icExpand.png";
import {
  Checkbox,
  Button,
  ModalTemplate,
  Pagination,
  TableWrapper,
  InputTimeout,
} from "components";
import moment from "moment";
import { useStore } from "hooks";
import { Main } from "./styled";

const { Column, Setting } = TableWrapper;
const ModalDanhSachHangHoa = (props, ref) => {
  const refModal = useRef(null);
  const refSettings = useRef(null);
  const refOnSelected = useRef(null);
  const refCurrentTime = useRef(null);
  const refTimeOut = useRef(null);
  const [state, _setState] = useState({ dataVatTuBo: {}, dataSource: [] });
  const history = useHistory();
  const { t } = useTranslation();
  const setState = (data = {}) => {
    _setState((_state) => ({
      ..._state,
      ...data,
    }));
  };

  const {
    listQuyetDinhThauChiTiet,
    page = 0,
    size = 10,
    totalElements,
    dataSortColumn,
  } = useSelector((state) => state.quyetDinhThauChiTiet);

  const { thongTinPhieu, nhapKhongTheoThau } = useStore(
    "phieuNhapXuat",
    {},
    {
      fields: "thongTinPhieu, nhapKhongTheoThau",
    }
  );

  const khoHienTai = useStore("kho.currentItem", {});

  const {
    quyetDinhThauChiTiet: {
      onSearch,
      onChangeSort,
      onChangeInputSearch,
      addItemVatTuCon,
    },
  } = useDispatch();

  useImperativeHandle(ref, () => ({
    show: (
      { ten, ma, dichVuId, vatTuBoId, data, hiddenButon = false },
      onSelected
    ) => {
      setState({
        index: -1,
        ten,
        dataVatTuBo: data,
        hiddenButon,
      });
      if (vatTuBoId) {
        onSearch({
          dataSearch: {
            vatTuBoId,
          },
          fromTongHop: true,
          page: 0,
          size: 1000,
        }).then((res) => {
          if (res.data?.length !== 1) refModal.current?.show?.();
        });
      } else {
        onSearch({
          dataSearch: {
            quyetDinhThauId: thongTinPhieu?.quyetDinhThauId,
            nhaCungCapId: thongTinPhieu?.nhaCungCapId,
            dsLoaiDichVu: khoHienTai?.dsLoaiDichVu,
            dichVuId: dichVuId,
            ten: ten,
            ma: ma,
            thau: history.location.pathname.includes("phieu-nhap-khac")
              ? false
              : history.location.pathname.includes("chinh-sua")
              ? !!thongTinPhieu?.quyetDinhThauId
              : !nhapKhongTheoThau,
            dsMucDichSuDung: khoHienTai.nhaThuoc ? 20 : 10,
          },
          fromTongHop: true,
          page: 0,
          size: 1000,
        }).then((res) => {
          if (res.data?.length !== 1) refModal.current?.show?.();
          else {
            onCancel(res.data[0]);
            // updateData({ thongTinPhieu: {} });
          }
        });
      }
      refOnSelected.current = onSelected;
      // refModal.current && refModal.current.show();
    },
  }));

  useEffect(() => {
    return () => {
      if (refTimeOut.current) {
        clearTimeout(refTimeOut.current);
      }
    };
  }, []);

  const onSearchInput = (key) => (e) => {
    if (key === "ten") {
      setState({ ten: e?.target?.value });
    }
    if (refTimeOut.current) {
      clearTimeout(refTimeOut.current);
      refTimeOut.current = null;
    }
    refTimeOut.current = setTimeout(
      (key, e) => {
        let value = "";
        if (e) {
          if (e?.hasOwnProperty("checked")) value = e?.checked;
          else value = e?.value || e;
        } else value = e;
        onChangeInputSearch({
          fromTongHop: true,
          [key]: value,
          thau: history.location.pathname.includes("chinh-sua")
            ? !!thongTinPhieu?.quyetDinhThauId
            : !nhapKhongTheoThau,
          dsMucDichSuDung: khoHienTai.nhaThuoc ? 20 : 10,
        });
      },
      500,
      key,
      e?.target || e
    );
  };

  const onClickSort = (key, value) => {
    onChangeSort({
      fromTongHop: true,
      [key]: value,
    });
  };

  const onShowChiTiet = (index, data) => {
    if (!data?.vatTuCha) {
      return null;
    }
    let newData = state?.dataSource.filter(
      (item) => item.vatTuBoId === data?.id
    );
    let dataSource = (state?.dataSource || []).map((item) => {
      return {
        ...item,
        expand: newData.includes(item) ? !item?.expand : item?.expand,
      };
    });
    setState({ dataSource });
  };

  const onChangeVatTuCon = (index) => (e) => {
    state.dataSource[index]["vatTuCon"] = e?.target?.checked;
  };

  const columns = [
    Column({
      title: " ",
      key: "check",
      width: 60,
      align: "center",
      ignore: true,
      columnIndex: 0,
      render: (_, data, index) => {
        return (
          <div>
            {!data?.vatTuBoId && !data?.vatTuCha && (
              <Radio
                checked={state.index === index}
                // onChange={() => {
                //   onCancel(index);
                // }}
              />
            )}
            {data?.vatTuBoId && (
              <Checkbox
                defaultChecked={data?.vatTuCon}
                onChange={onChangeVatTuCon(index)}
              />
            )}
          </div>
        );
      },
    }),
    Column({
      title: (
        <>
          {t("common.stt")}
          <Setting refTable={refSettings} />
        </>
      ),
      width: 60,
      dataIndex: "index2",
      key: "index2",
      align: "center",
      ignore: true,
      columnIndex: 0,
      render: (item, data, index) => {
        return (
          <div onClick={() => onShowChiTiet(index, data)}>
            {!data?.vatTuCha && data?.index}
            {data?.vatTuCha && (
              <img src={IcExpand} alt={`expand stt ${data?.index}`} />
            )}
          </div>
        );
      },
    }),
    Column({
      title: t("kho.maHangHoa"),
      sort_key: "ma",
      dataSort: dataSortColumn?.["ma"] || 0,
      onClickSort: onClickSort,
      width: 100,
      dataIndex: "ma",
      key: "ma",
      i18Name: "kho.maHangHoa",
      renderSearch: (
        <InputTimeout
          placeholder={t("kho.timMaHangHoa")}
          onChange={onSearchInput("ma")}
        />
      ),
    }),
    Column({
      title: t("kho.tenHangHoa"),
      sort_key: "ten",
      dataSort: dataSortColumn?.["ten"] || 0,
      onClickSort: onClickSort,
      width: 180,
      dataIndex: "ten",
      key: "ten",
      i18Name: "kho.tenHangHoa",
      renderSearch: (
        <InputTimeout
          value={state?.ten}
          placeholder={t("kho.timTenHangHoa")}
          onChange={onSearchInput("ten")}
        />
      ),
      render: (item, data) => (
        <label style={{ fontWeight: `${data?.vatTuCha ? "bold" : ""}` }}>
          {item}
        </label>
      ),
    }),
    Column({
      title: t("kho.tenHoatChat"),
      sort_key: "tenHoatChat",
      dataSort: dataSortColumn?.["tenHoatChat"] || 0,
      onClickSort: onClickSort,
      dataIndex: "tenHoatChat",
      key: "tenHoatChat",
      i18Name: "kho.tenHoatChat",
      width: 150,
    }),
    Column({
      title: t("kho.hamLuong"),
      sort_key: "hamLuong",
      dataSort: dataSortColumn?.["hamLuong"] || 0,
      onClickSort: onClickSort,
      dataIndex: "hamLuong",
      key: "hamLuong",
      i18Name: "kho.hamLuong",
      width: 120,
      renderSearch: (
        <InputTimeout
          placeholder={t("kho.timHamLuong")}
          onChange={onSearchInput("hamLuong")}
        />
      ),
    }),
    Column({
      title: t("kho.quyetDinhThau.title"),
      sort_key: "quyetDinhThau",
      dataSort: dataSortColumn?.["quyetDinhThau"] || 0,
      onClickSort: onClickSort,
      dataIndex: "quyetDinhThau",
      key: "quyetDinhThau",
      i18Name: "kho.quyetDinhThau.title",
      width: 120,
      renderSearch: (
        <InputTimeout
          placeholder={t("kho.quyetDinhThau.timQuyetDinhThau")}
          onChange={onSearchInput("quyetDinhThau")}
        />
      ),
    }),
    Column({
      title: t("kho.quyetDinhThau.soThau"),
      sort_key: "soThau",
      dataSort: dataSortColumn?.["soThau"] || 0,
      onClickSort: onClickSort,
      dataIndex: "soThau",
      key: "soThau",
      i18Name: "kho.quyetDinhThau.soThau",
      width: 100,
      renderSearch: (
        <InputTimeout
          placeholder={t("common.timKiem")}
          onChange={onSearchInput("soThau")}
        />
      ),
    }),
    Column({
      title: t("kho.quyetDinhThau.soVisa"),
      sort_key: "soVisa",
      dataSort: dataSortColumn?.["soVisa"] || 0,
      onClickSort: onClickSort,
      dataIndex: "soVisa",
      key: "soVisa",
      i18Name: "kho.quyetDinhThau.soVisa",
      width: 120,
    }),
    Column({
      title: t("common.giaBH"),
      sort_key: "giaBaoHiem",
      dataSort: dataSortColumn?.["giaBaoHiem"] || 0,
      onClickSort: onClickSort,
      dataIndex: "giaBaoHiem",
      key: "giaBaoHiem",
      i18Name: "common.giaBH",
      width: 120,
      render: (item) => {
        return (item && item.formatPrice()) || "";
      },
    }),
    Column({
      title: t("common.giaKhongBH"),
      sort_key: "giaKhongBaoHiem",
      dataSort: dataSortColumn?.["giaKhongBaoHiem"] || 0,
      onClickSort: onClickSort,
      dataIndex: "giaKhongBaoHiem",
      key: "giaKhongBaoHiem",
      i18Name: "common.giaKhongBH",
      width: 120,
      render: (item) => {
        return (item && item.formatPrice()) || "";
      },
    }),
    Column({
      title: t("kho.phatThuoc.giaPhuThu"),
      sort_key: "giaPhuThu",
      dataSort: dataSortColumn?.["giaPhuThu"] || 0,
      onClickSort: onClickSort,
      dataIndex: "giaPhuThu",
      key: "giaPhuThu",
      i18Name: "kho.phatThuoc.giaPhuThu",
      width: 120,
      render: (item) => {
        return (item && item.formatPrice()) || "";
      },
    }),
    Column({
      title: t("kho.tyLeBhTt"),
      sort_key: "tyLeBhTt",
      dataSort: dataSortColumn?.["tyLeBhTt"] || 0,
      onClickSort: onClickSort,
      dataIndex: "tyLeBhTt",
      key: "tyLeBhTt",
      i18Name: "kho.tyLeBhTt",
      width: 120,
    }),
    Column({
      title: t("kho.quyetDinhThau.slThau"),
      dataIndex: "soLuongThau",
      sort_key: "soLuongThau",
      dataSort: dataSortColumn?.["soLuongThau"] || 0,
      onClickSort: onClickSort,
      key: "soLuongThau",
      i18Name: "kho.quyetDinhThau.slThau",
      width: 100,
      align: "right",
      render: (item) => {
        return (item && item.formatPrice()) || "";
      },
    }),
    Column({
      title: t("kho.quyetDinhThau.slDuocPhepMua"),
      dataIndex: "soLuongDuocPhepMua",
      sort_key: "soLuongDuocPhepMua",
      dataSort: dataSortColumn?.["soLuongDuocPhepMua"] || 0,
      onClickSort: onClickSort,
      key: "soLuongDuocPhepMua",
      i18Name: "kho.quyetDinhThau.slDuocPhepMua",
      width: 150,
      align: "left",
      renderSearch: (
        <InputTimeout
          placeholder={t("kho.quyetDinhThau.timSlDuocPhepMua")}
          onChange={onSearchInput("soLuongDuocPhepMua")}
        />
      ),
      render: (item) => {
        return item && item.formatPrice();
      },
    }),
    Column({
      title: t("kho.quyetDinhThau.soLuongConLai"),
      dataIndex: "soLuongCon",
      sort_key: "soLuongCon",
      dataSort: dataSortColumn?.["soLuongCon"] || 0,
      onClickSort: onClickSort,
      key: "soLuongCon",
      i18Name: "kho.quyetDinhThau.soLuongConLai",
      width: 100,
      align: "right",
      render: (item) => {
        return (item && item.formatPrice()) || "";
      },
    }),
    Column({
      title: t("kho.nhaCungCap"),
      dataIndex: "tenNhaCungCap",
      sort_key: "tenNhaCungCap",
      dataSort: dataSortColumn?.["tenNhaCungCap"] || 0,
      onClickSort: onClickSort,
      key: "tenNhaCungCap",
      i18Name: "kho.nhaCungCap",
      width: 150,
      align: "left",
      renderSearch: !thongTinPhieu?.nhaCungCapId && (
        <InputTimeout
          placeholder={t("kho.quyetDinhThau.timNhaCungCap")}
          onChange={onSearchInput("tenNhaCungCap")}
        />
      ),
    }),
    Column({
      title: t("kho.quyetDinhThau.giaNhapSauVat"),
      dataIndex: "giaNhapSauVat",
      sort_key: "giaNhapSauVat",
      dataSort: dataSortColumn?.["giaNhapSauVat"] || 0,
      onClickSort: onClickSort,
      key: "giaNhapSauVat",
      i18Name: "kho.quyetDinhThau.giaNhapSauVat",
      width: 150,
      align: "left",
      renderSearch: (
        <InputTimeout
          placeholder={t("common.timKiem")}
          onChange={onSearchInput("giaNhapSauVat")}
        />
      ),
      render: (item) => {
        return item && item.formatPrice();
      },
    }),
  ];
  const onCancel = (record, index) => {
    let data = record;
    if (
      nhapKhongTheoThau === false &&
      moment().isAfter(moment(data?.ngayHieuLuc))
    ) {
      message.warning(
        `${t("kho.goiThauDaHetHieuLucNgay")} ${moment(data?.ngayHieuLuc).format(
          `DD/MM/YYYY`
        )}`
      );
    }

    let dataCon = (state?.dataSource || []).filter((item) => {
      return (
        item?.vatTuCon &&
        item.vatTuBoId ===
          (thongTinPhieu.quyetDinhThauId
            ? data?.quyetDinhThauChiTietId
            : data?.dichVuId)
      );
    });
    if (dataCon.length) {
      data = { ...data, dsNhapXuatChiTiet: dataCon };
    }
    refOnSelected.current && refOnSelected.current(data);
    refModal.current?.hide?.();
  };
  const onChangePage = (page) => {
    onSearch({
      fromTongHop: true,
      page: page - 1,
      size,
    });
  };
  const onSizeChange = (size) => {
    onSearch({
      fromTongHop: true,
      page: 0,
      size: size,
    });
  };
  const onRow = (record, index) => {
    if (record?.vatTuCha || record?.vatTuBoId > 0) {
      return null;
    }
    return {
      onClick: (e) => {
        if (
          !refCurrentTime.current ||
          new Date() - refCurrentTime.current > 2000
        ) {
          refCurrentTime.current = new Date();
          onCancel(record, index);
        }
      },
    };
  };

  useEffect(() => {
    let data = [];
    Object.keys(listQuyetDinhThauChiTiet).forEach((key) => {
      const { dsVatTuBoChiTiet, index, dichVuId, quyetDinhThauChiTietId } =
        listQuyetDinhThauChiTiet[key];
      data.push(listQuyetDinhThauChiTiet[key]);
      if (dsVatTuBoChiTiet?.length) {
        let payload = {
          ten: "Chi tiết bộ phận",
          vatTuCha: true,
          id: thongTinPhieu?.quyetDinhThauId
            ? quyetDinhThauChiTietId
            : dichVuId,
          dichVuId: dichVuId,
        };
        data.push(payload);
      }
      dsVatTuBoChiTiet?.length &&
        dsVatTuBoChiTiet.map((item, idx) =>
          data.push({
            ...item,
            expand: false,
            index: index + "." + (idx + 1),
            vatTuCon: true,
          })
        );
    });
    setState({
      dataSource: (data || []).filter(
        (item) =>
          !(state?.dataVatTuBo?.dsNhapXuatChiTiet || [])
            .map((x) => x.dichVuId)
            .includes(item?.dichVuId)
      ),
    });
  }, [listQuyetDinhThauChiTiet, state?.dataVatTuBo]);
  const onOk = () => {
    addItemVatTuCon(state.dataSource || []);
    refModal.current?.hide?.();
    setState({ show: false });
  };

  return (
    <>
      {state?.dataSource?.length !== 1 && (
        <ModalTemplate ref={refModal} title={t("kho.danhSachHangHoa")}>
          <Main>
            <TableWrapper
              rowClassName={(record, index) => {
                return index % 2 === 0
                  ? `table-row-even ${
                      index === state?.listServiceSelected?.length - 1
                        ? "add-border"
                        : ""
                    }`
                  : `table-row-odd ${
                      index === state?.listServiceSelected?.length - 1
                        ? "add-border"
                        : ""
                    }`;
              }}
              columns={columns}
              dataSource={(state?.dataSource || []).filter(
                (x) => x.expand || x.expand === undefined
              )}
              tableName="table_KHO_PhieuNhap_NhaCungCap_ModalDsHangHoa"
              onRow={onRow}
              ref={refSettings}
              rowKey={(record) =>
                record.dichVuId +
                "_" +
                record.quyetDinhThauId +
                "_" +
                record.quyetDinhThauChiTietId +
                "_" +
                record.ten
              }
              //   rowClassName={setRowClassName}
            />
            <Pagination
              onChange={onChangePage}
              current={page + 1}
              pageSize={size}
              listData={listQuyetDinhThauChiTiet}
              total={totalElements}
              onShowSizeChange={onSizeChange}
              stylePagination={{ flex: 1, justifyContent: "flex-start" }}
            />
            {state?.hiddenButon && (
              <div className="footer-action">
                <div className="selected-item"></div>
                <Button minWidth={100} onClick={onCancel}>
                  {t("common.huy")}
                </Button>
                <Button type="primary" minWidth={100} onClick={onOk}>
                  {t("common.them")}
                </Button>
              </div>
            )}
          </Main>
        </ModalTemplate>
      )}
    </>
  );
};

export default forwardRef(ModalDanhSachHangHoa);
