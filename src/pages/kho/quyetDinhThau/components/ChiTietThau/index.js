import React, { useEffect, useRef } from "react";
import { Main } from "./styled";
import {
  Checkbox,
  Pagination,
  HeaderSearch,
  TableWrapper,
  Select,
  Button,
  DatePicker,
} from "components";
import { HIEU_LUC, HOTKEY, ENUM } from "constants/index";
import { useSelector, useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { SVG } from "assets";
import moment from "moment";
import { useEnum, useListAll, useLoading } from "hooks";
import { InputTimeout } from "components";
import ModalImport from "components/DanhMuc/ModalImport";
import { Menu } from "antd";
import { selectMaTen } from "redux-store/selectors";

let timer = null;

const dateFormat = "DD-MM-YYYY";

const ChiTietThau = ({
  handleChangeshowTable,
  showFullTable,
  collapseStatus,
  handleCollapsePane,
  layerId,
  onReset = () => {},
}) => {
  const { onRegisterHotkey } = useDispatch().phimTat;
  const refClickBtnAdd = useRef();
  const refSelectRow = useRef();
  const refSettings = useRef(null);
  const refModalImport = useRef(null);
  const { t } = useTranslation();

  const {
    listQuyetDinhThauChiTiet,
    dataEditDefault,
    page,
    size,
    totalElements,
    dataSortColumn,
    dataSearch,
  } = useSelector((state) => state.quyetDinhThauChiTiet);
  const { listAllQuyetDinhThau } = useSelector((state) => state.quyetDinhThau);
  const [listAllPhuongPhapCheBien] = useListAll("phuongPhapCheBien", {}, true);
  const { listAllNhaSanXuat, listAllNhaCungCap } = useSelector(
    (state) => state.doiTac
  );
  const { listAllXuatXu } = useSelector((state) => state.xuatXu);
  const { listAllDonViTinh } = useSelector((state) => state.donViTinh);
  const { listAllDuongDung } = useSelector((state) => state.duongDung);
  const { listAllHoatChat } = useSelector((state) => state.hoatChat);

  const [listNhomThau] = useEnum(ENUM.NHOM_THAU);
  const [listGoiThau] = useEnum(ENUM.GOI_THAU);
  const [listLoaiThuocThau] = useEnum(ENUM.LOAI_THUOC_THAU);
  const { listDataTongHop: listNhomChiPhiBh } = useSelector(
    (state) => state.nhomChiPhi
  );
  const { showLoading, hideLoading } = useLoading();

  const {
    quyetDinhThauChiTiet: {
      onSizeChange,
      onSortChange,
      onChangeInputSearch,
      updateData,
      onSearch,
      onExportTheoParam,
      onExport,
      onImport,
    },
    kichCo: { getListAllKichCo },
  } = useDispatch();

  useEffect(() => {
    onRegisterHotkey({
      layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.F1, //F1
          onEvent: (e) => {
            refClickBtnAdd.current && refClickBtnAdd.current(e);
          },
        },
        {
          keyCode: HOTKEY.UP, //up
          onEvent: (e) => {
            if (refSelectRow.current && e?.target?.nodeName !== "INPUT")
              refSelectRow.current(-1);
          },
        },
        {
          keyCode: HOTKEY.DOWN, //down
          onEvent: (e) => {
            if (refSelectRow.current && e?.target?.nodeName !== "INPUT")
              refSelectRow.current(1);
          },
        },
      ],
    });
  }, []);

  refSelectRow.current = (index) => {
    const indexNextItem =
      (listQuyetDinhThauChiTiet?.findIndex(
        (item) => item.id === dataEditDefault?.id
      ) || 0) + index;
    if (-1 < indexNextItem && indexNextItem < listQuyetDinhThauChiTiet.length) {
      updateData({ dataEditDefault: listQuyetDinhThauChiTiet[indexNextItem] });
      document
        .getElementsByClassName(
          "row-id-" + listQuyetDinhThauChiTiet[indexNextItem]?.id
        )[0]
        .scrollIntoView({ block: "end", behavior: "smooth" });
    }
  };
  refClickBtnAdd.current = onReset;

  const onSearchInput = (key) => (e) => {
    let value = "";
    if (e?.target) {
      if (e.target.hasOwnProperty("checked")) value = e.target.checked;
      else value = e.target.value;
    } else value = e;

    onChangeInputSearch({ [key]: value });
  };

  const onRow = (record, index) => ({
    onClick: (event) => {
      updateData({ dataEditDefault: record });
      getListAllKichCo(
        { page: "", size: "", active: true, dichVuId: record?.dichVuId },
        { saveCache: false }
      );
    },
  });

  const onChangePage = (page) => {
    onSearch({ page: page - 1 });
  };

  const handleSizeChange = (size) => {
    onSizeChange({ size: size });
  };

  const onClickSort = (key, value) => {
    onSortChange({
      [key]: value,
    });
  };

  const onSearchDate = (key) => (e) => {
    const value = e ? e.format("YYYY-MM-DD") : null;
    onChangeInputSearch({ [key]: value });
  };

  const onSettings = () => {
    refSettings.current && refSettings.current.settings();
  };

  const widthColStt = () => {
    const length = String((Number(page) + 1) * Number(size)).length;
    return length * 10 + 40;
  };

  const columns = [
    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("common.stt")}
              <SVG.IcSetting onClick={onSettings} className="icon" />
            </>
          }
        />
      ),
      width: widthColStt(),
      dataIndex: "index",
      key: "index",
      align: "center",
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.maHangHoa")}
          sort_key="dichVu.ma"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.ma"] || 0}
          search={
            <InputTimeout
              placeholder={t("kho.timMaHangHoa")}
              onChange={onSearchInput("dichVu.ma")}
            />
          }
        />
      ),
      width: 200,
      dataIndex: "dichVu",
      key: "maHangHoa",
      className: "ngayHieuLuc",
      show: true,
      i18Name: "kho.maHangHoa",
      render: (item) => {
        return item && item?.ma;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.tenHangHoa")}
          sort_key="dichVuId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVuId"] || 0}
          search={
            <InputTimeout
              placeholder={t("kho.quyetDinhThau.timDichVu")}
              onChange={onSearchInput("dichVu.ten")}
            />
          }
        />
      ),
      width: 200,
      dataIndex: "dichVu",
      key: "tenHangHoa",
      className: "ngayHieuLuc",
      show: true,
      i18Name: "kho.tenHangHoa",
      render: (item) => {
        return item && item?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.hamLuong")}
          sort_key="hamLuong"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.hamLuong || 0}
          search={
            <InputTimeout
              placeholder={t("kho.timHamLuong")}
              onChange={onSearchInput("hamLuong")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "hamLuong",
      key: "hamLuong",
      show: true,
      i18Name: "kho.hamLuong",
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.donViTinh")}
          sort_key="dichVu.tenDvtSoCap"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.tenDvtSoCap"] || 0}
          search={
            <InputTimeout
              placeholder={t("kho.quyetDinhThau.timDonViTinh")}
              onChange={onSearchInput("dichVu.tenDvtSoCap")}
            />
          }
        />
      ),
      width: 100,
      dataIndex: "dichVu",
      key: "tenDvtSoCap",
      show: true,
      i18Name: "kho.quyetDinhThau.donViTinh",
      render: (item) => {
        return item && item.tenDvtSoCap;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.soThau")}
          sort_key="quyetDinhThau.soThau"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["quyetDinhThau.soThau"] || 0}
          search={
            <InputTimeout
              placeholder={t("kho.quyetDinhThau.timSoThau")}
              onChange={onSearchInput("quyetDinhThau.soThau")}
            />
          }
        />
      ),
      width: 180,
      dataIndex: "quyetDinhThau",
      key: "quyetDinhThau",
      show: true,
      i18Name: "kho.quyetDinhThau.soThau",
      render: (item) => {
        return item && item?.soThau;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.title")}
          sort_key="quyetDinhThauId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["quyetDinhThauId"] || 0}
          searchSelect={
            <Select
              placeholder={t("kho.quyetDinhThau.timQuyetDinhThau")}
              data={listAllQuyetDinhThau}
              onChange={onSearchInput("quyetDinhThauId")}
              ten="quyetDinhThau"
            />
          }
        />
      ),
      width: 180,
      dataIndex: "quyetDinhThau",
      key: "quyetDinhThau",
      show: true,
      i18Name: "kho.quyetDinhThau.title",
      render: (item) => {
        return item && item?.quyetDinhThau;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.maHangHoaTrungThau")}
          sort_key="maTrungThau"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["maTrungThau"] || 0}
          search={
            <InputTimeout
              placeholder={t("kho.quyetDinhThau.timMaTrungThau")}
              onChange={onSearchInput("maTrungThau")}
            />
          }
        />
      ),
      width: 150,
      align: "right",
      dataIndex: "maTrungThau",
      key: "maTrungThau",
      show: true,
      i18Name: "kho.quyetDinhThau.maHangHoaTrungThau",
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.tenHangHoaTrungThau")}
          sort_key="tenTrungThau"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["tenTrungThau"] || 0}
          search={
            <InputTimeout
              placeholder={t("kho.quyetDinhThau.timTenTrungThau")}
              onChange={onSearchInput("tenTrungThau")}
            />
          }
        />
      ),
      width: 150,
      align: "right",
      dataIndex: "tenTrungThau",
      key: "tenTrungThau",
      show: true,
      i18Name: "kho.quyetDinhThau.tenHangHoaTrungThau",
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.maHangHoaDauThau")}
          sort_key="maDauThau"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["maDauThau"] || 0}
          search={
            <InputTimeout
              placeholder={t("kho.quyetDinhThau.timMaDauThau")}
              onChange={onSearchInput("maDauThau")}
            />
          }
        />
      ),
      width: 150,
      align: "right",
      dataIndex: "maDauThau",
      key: "maDauThau",
      show: true,
      i18Name: "kho.quyetDinhThau.maHangHoaDauThau",
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.slThau")}
          sort_key="soLuongThau"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["soLuongThau"] || 0}
          search={
            <InputTimeout
              placeholder={t("kho.quyetDinhThau.timSoLuongThau")}
              onChange={onSearchInput("soLuongThau")}
            />
          }
        />
      ),
      width: 150,
      align: "right",
      dataIndex: "soLuongThau",
      key: "soLuongThau",
      show: true,
      i18Name: "kho.quyetDinhThau.slThau",
      render: (item) => {
        return item && item.formatPrice();
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.slDuocPhepMua")}
          sort_key="soLuongDuocPhepMua"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["soLuongDuocPhepMua"] || 0}
          search={
            <InputTimeout
              placeholder={t("kho.quyetDinhThau.timSlDuocPhepMua")}
              onChange={onSearchInput("soLuongDuocPhepMua")}
            />
          }
        />
      ),
      width: 120,
      align: "right",
      dataIndex: "soLuongDuocPhepMua",
      key: "soLuongDuocPhepMua",
      show: true,
      i18Name: "kho.quyetDinhThau.slDuocPhepMua",
      render: (item) => {
        return item && item.formatPrice();
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.giaNhapSauVat")}
          sort_key="giaNhapSauVat"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["giaNhapSauVat"] || 0}
          search={
            <InputTimeout
              placeholder={t("kho.quyetDinhThau.timGiaNhapSauVat")}
              onChange={onSearchInput("giaNhapSauVat")}
            />
          }
        />
      ),
      width: 120,
      align: "right",
      dataIndex: "giaNhapSauVat",
      key: "giaNhapSauVat",
      show: true,
      i18Name: "kho.quyetDinhThau.giaNhapSauVat",
      render: (item) => {
        return item && item.formatPrice();
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.donGiaKhongBh")}
          sort_key="giaKhongBaoHiem"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["giaKhongBaoHiem"] || 0}
          search={
            <InputTimeout
              placeholder={t("kho.quyetDinhThau.timDonGiaKhongBh")}
              onChange={onSearchInput("giaKhongBaoHiem")}
            />
          }
        />
      ),
      width: 150,
      align: "right",
      dataIndex: "giaKhongBaoHiem",
      key: "giaKhongBaoHiem",
      show: true,
      i18Name: "kho.quyetDinhThau.donGiaKhongBh",
      render: (item) => {
        return item && item.formatPrice();
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.donGiaBh")}
          sort_key="giaBaoHiem"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["giaBaoHiem"] || 0}
          search={
            <InputTimeout
              placeholder={t("kho.quyetDinhThau.timDonGiaBh")}
              onChange={onSearchInput("giaBaoHiem")}
            />
          }
        />
      ),
      width: 150,
      align: "right",
      dataIndex: "giaBaoHiem",
      key: "giaBaoHiem",
      show: true,
      i18Name: "kho.quyetDinhThau.donGiaBh",
      render: (item) => {
        return item && item.formatPrice();
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.phuThu")}
          sort_key="giaPhuThu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["giaPhuThu"] || 0}
          search={
            <InputTimeout
              placeholder={t("kho.quyetDinhThau.timDonGiaPhuThu")}
              onChange={onSearchInput("giaPhuThu")}
            />
          }
        />
      ),
      width: 150,
      align: "right",
      dataIndex: "giaPhuThu",
      key: "giaPhuThu",
      show: true,
      i18Name: "kho.quyetDinhThau.phuThu",
      render: (item) => {
        return item && item.formatPrice();
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.quyCach")}
          sort_key="quyCach"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["quyCach"] || 0}
          search={
            <InputTimeout
              placeholder={t("kho.quyetDinhThau.timQuyCach")}
              onChange={onSearchInput("quyCach")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "quyCach",
      key: "quyCach",
      show: true,
      i18Name: "kho.quyetDinhThau.quyCach",
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.nhaCungCap")}
          sort_key="nhaCungCapId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["nhaCungCapId"] || 0}
          searchSelect={
            <Select
              placeholder={t("kho.quyetDinhThau.timNhaCungCap")}
              data={listAllNhaCungCap}
              onChange={onSearchInput("nhaCungCapId")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "nhaCungCapId",
      key: "nhaCungCapId",
      show: true,
      i18Name: "kho.quyetDinhThau.nhaCungCap",
      render: (item) => {
        return (
          listAllNhaCungCap &&
          (listAllNhaCungCap.find((e) => e.id === item) || [])?.ten
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.maGoiThau")}
          sort_key="goiThau"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["goiThau"] || 0}
          searchSelect={
            <Select
              placeholder={t("kho.quyetDinhThau.timMaGoiThau")}
              data={listGoiThau}
              onChange={onSearchInput("goiThau")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "goiThau",
      key: "goiThau",
      show: true,
      i18Name: "kho.quyetDinhThau.maGoiThau",
      render: (item) => {
        return (
          listGoiThau && (listGoiThau.find((e) => e.id === item) || [])?.ten
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.soVisa")}
          sort_key="soVisa"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["soVisa"] || 0}
          search={
            <InputTimeout
              placeholder={t("kho.quyetDinhThau.timSoViSa")}
              onChange={onSearchInput("soVisa")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "soVisa",
      key: "soVisa",
      show: true,
      i18Name: "kho.quyetDinhThau.soVisa",
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.nhomThau")}
          sort_key="nhomThau"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["nhomThau"] || 0}
          searchSelect={
            <Select
              placeholder={t("kho.quyetDinhThau.timNhomThau")}
              data={listNhomThau}
              onChange={onSearchInput("nhomThau")}
            />
          }
        />
      ),
      width: 100,
      dataIndex: "nhomThau",
      key: "nhomThau",
      show: true,
      i18Name: "kho.quyetDinhThau.nhomThau",
      render: (item) => {
        return (
          listNhomThau && (listNhomThau.find((e) => e.id === item) || [])?.ten
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.nhomChiPhi")}
          sort_key="nhomChiPhiId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["nhomChiPhiId"] || 0}
          searchSelect={
            <Select
              placeholder={t("kho.quyetDinhThau.timNhomChiPhi")}
              data={listNhomChiPhiBh}
              onChange={onSearchInput("nhomChiPhiId")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "nhomChiPhiId",
      key: "nhomChiPhiId",
      show: true,
      i18Name: "kho.quyetDinhThau.nhomChiPhi",
      render: (item) => {
        return (
          listNhomChiPhiBh &&
          (listNhomChiPhiBh.find((e) => e.id === item) || [])?.ten
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.tyLeThanhToanBh")}
          sort_key="tyLeBhTt"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["tyLeBhTt"] || 0}
          search={
            <InputTimeout
              placeholder={t("kho.quyetDinhThau.timTyLeTTBH")}
              onChange={onSearchInput("tyLeBhTt")}
            />
          }
        />
      ),
      width: 100,
      align: "right",
      dataIndex: "tyLeBhTt",
      key: "tyLeBhTt",
      show: true,
      i18Name: "kho.quyetDinhThau.tyLeThanhToanBh",
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.nguongThau")}
          sort_key="nguongThau"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["nguongThau"] || 0}
          search={
            <InputTimeout
              placeholder={t("kho.quyetDinhThau.timNguongThau")}
              onChange={onSearchInput("nguongThau")}
            />
          }
        />
      ),
      width: 150,
      align: "right",
      dataIndex: "nguongThau",
      key: "nguongThau",
      show: true,
      i18Name: "kho.quyetDinhThau.nguongThau",
      render: (item) => {
        return item && item.formatPrice();
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.loaiThuoc")}
          sort_key="loaiThuoc"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["loaiThuoc"] || 0}
          searchSelect={
            <Select
              placeholder={t("kho.quyetDinhThau.timLoaiThuoc")}
              data={listLoaiThuocThau}
              onChange={onSearchInput("loaiThuoc")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "loaiThuoc",
      key: "loaiThuoc",
      show: true,
      i18Name: "kho.quyetDinhThau.loaiThuoc",
      render: (item) => {
        return (
          listLoaiThuocThau &&
          (listLoaiThuocThau.find((e) => e.id === item) || [])?.ten
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.nuocSanXuat")}
          sort_key="xuatXuId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["xuatXuId"] || 0}
          searchSelect={
            <Select
              placeholder={t("kho.quyetDinhThau.timNuocSanXuat")}
              data={listAllXuatXu}
              onChange={onSearchInput("xuatXuId")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "xuatXu",
      key: "nuocSanXuatId",
      show: true,
      i18Name: "kho.quyetDinhThau.nuocSanXuat",
      render: (item) => {
        return item && item?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.nhaSanXuat")}
          sort_key="nhaSanXuatId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["nhaSanXuatId"] || 0}
          searchSelect={
            <Select
              placeholder={t("kho.quyetDinhThau.timNhaSanXuat")}
              data={listAllNhaSanXuat}
              onChange={onSearchInput("nhaSanXuatId")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "nhaSanXuatId",
      key: "nhaSanXuatId",
      show: true,
      i18Name: "common.nhaSanXuat",
      render: (item) => {
        return (
          item &&
          listAllNhaSanXuat &&
          (listAllNhaSanXuat.find((e) => e.id === item) || [])?.ten
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.soHopDong")}
          sort_key="soHopDong"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.soHopDong || 0}
          search={
            <InputTimeout
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("soHopDong")}
            />
          }
        />
      ),
      width: 100,
      dataIndex: "soHopDong",
      key: "soHopDong",
      show: true,
      i18Name: "kho.quyetDinhThau.soHopDong",
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.duongDung")}
          sort_key="duongDungId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.duongDungId || 0}
          searchSelect={
            <Select
              placeholder={t("danhMuc.timDuongDung")}
              data={listAllDuongDung}
              onChange={onSearchInput("duongDungId")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "duongDungId",
      key: "duongDungId",
      show: true,
      i18Name: "kho.quyetDinhThau.duongDung",
      render: (item) => {
        return listAllDuongDung.find((i) => i.id === item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.maHoatChat")}
          sort_key="maHoatChat"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.maHoatChat || 0}
          searchSelect={
            <Select
              placeholder={t("kho.quyetDinhThau.timMaHoatChat")}
              data={listAllHoatChat}
              onChange={onSearchInput("hoatChatId")}
              getLabel={selectMaTen}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "hoatChatId",
      key: "maHoatChat",
      show: true,
      i18Name: "kho.quyetDinhThau.maHoatChat",
      render: (item) => {
        let findItem = listAllHoatChat?.find((e) => e.id === item);
        if (findItem) {
          return `${findItem.ma} - ${findItem.ten}`;
        }
        return "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.maAnhXa")}
          sort_key="maTuongDuong"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.maTuongDuong || 0}
          search={
            <InputTimeout
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("maTuongDuong")}
            />
          }
        />
      ),
      width: 100,
      dataIndex: "maTuongDuong",
      key: "maTuongDuong",
      show: true,
      i18Name: "kho.quyetDinhThau.maAnhXa",
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.tranBaoHiem")}
          sort_key="nguongThau"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["tranBaoHiem"] || 0}
          search={
            <InputTimeout
              placeholder={t("kho.quyetDinhThau.timTranBaoHiem")}
              onChange={onSearchInput("tranBaoHiem")}
            />
          }
        />
      ),
      width: 150,
      align: "right",
      dataIndex: "tranBaoHiem",
      key: "tranBaoHiem",
      show: true,
      i18Name: "kho.quyetDinhThau.tranBaoHiem",
      render: (item) => {
        return item && item.formatPrice();
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.soLuongHangHoaDaNhap")}
          sort_key="soLuongDuocPhepMua"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["soLuongDuocPhepMua"] || 0}
          search={
            <InputTimeout
              placeholder={t("kho.quyetDinhThau.timSoLuongHangHoaDaNhap")}
              onChange={onSearchInput("soLuongNhap")}
            />
          }
        />
      ),
      width: 120,
      align: "right",
      dataIndex: "soLuongNhap",
      key: "soLuongNhap",
      show: true,
      i18Name: "kho.quyetDinhThau.soLuongHangHoaDaNhap",
      render: (item) => {
        return item && item.formatPrice();
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.soLuongHangHoaDaTraLaiNcc")}
          sort_key="soLuongTra"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["soLuongDuocPhepMua"] || 0}
          search={
            <InputTimeout
              placeholder={t("kho.quyetDinhThau.timSoLuongHangDaTraLaiNcc")}
              onChange={onSearchInput("soLuongTra")}
            />
          }
        />
      ),
      width: 120,
      align: "right",
      dataIndex: "soLuongTra",
      key: "soLuongTra",
      show: true,
      i18Name: "kho.quyetDinhThau.soLuongHangHoaDaTraLaiNcc",
      render: (item) => {
        return item && item.formatPrice();
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.soLuongHangHoaConLai")}
          sort_key="soLuongCon"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["soLuongCon"] || 0}
          search={
            <InputTimeout
              placeholder={t("kho.quyetDinhThau.timSoLuongHangHoaConLai")}
              onChange={onSearchInput("soLuongCon")}
            />
          }
        />
      ),
      width: 120,
      align: "right",
      dataIndex: "soLuongCon",
      key: "soLuongCon",
      show: true,
      i18Name: "kho.quyetDinhThau.soLuongHangHoaConLai",
      render: (item) => {
        return item && item.formatPrice();
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.maAtc")}
          sort_key="maAtc"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.maAtc || 0}
          search={
            <InputTimeout
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("maAtc")}
            />
          }
        />
      ),
      width: 100,
      dataIndex: "maAtc",
      key: "maAtc",
      show: true,
      i18Name: "kho.quyetDinhThau.maAtc",
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.phuongPhapCheBien")}
          sort_key="dsPhuongPhapCheBienId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.dsPhuongPhapCheBienId || 0}
          searchSelect={
            <Select
              placeholder={t("common.timKiem")}
              data={listAllPhuongPhapCheBien}
              onChange={onSearchInput("dsPhuongPhapCheBienId")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "dsPhuongPhapCheBien",
      key: "dsPhuongPhapCheBien",
      show: true,
      i18Name: "danhMuc.phuongPhapCheBien",
      render: (item, data) => item?.map((i) => i.ten).join(", "),
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.dangBaoChe")}
          sort_key="dangBaoChe"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.dangBaoChe || 0}
          search={
            <InputTimeout
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("dangBaoChe")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "dangBaoChe",
      key: "dangBaoChe",
      show: true,
      i18Name: "danhMuc.dangBaoChe",
    },
    {
      title: (
        <HeaderSearch
          sort_key="maSinhPham"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.maSinhPham || 0}
          search={
            <InputTimeout
              placeholder={t("danhMuc.maSinhPham")}
              onChange={onSearchInput("maSinhPham")}
            />
          }
          title={t("danhMuc.maSinhPham")}
        />
      ),
      width: 100,
      dataIndex: "maSinhPham",
      key: "maSinhPham",
      show: true,
      i18Name: "danhMuc.maSinhPham",
    },
    {
      title: (
        <HeaderSearch
          sort_key="maSinhHieu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.maSinhHieu || 0}
          search={
            <InputTimeout
              placeholder={t("danhMuc.maHieuSinhPham")}
              onChange={onSearchInput("maSinhHieu")}
            />
          }
          title={t("danhMuc.maHieuSinhPham")}
        />
      ),
      width: 100,
      dataIndex: "maSinhHieu",
      key: "maSinhHieu",
      show: true,
      i18Name: "danhMuc.maHieuSinhPham",
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.ngayHopDong")}
          searchSelect={
            <DatePicker
              format={dateFormat}
              placeholder={t("common.timKiem")}
              onChange={onSearchDate("ngayHopDong")}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "ngayHopDong",
      key: "ngayHopDong",
      show: true,
      i18Name: "kho.quyetDinhThau.ngayHopDong",
      render: (item) => {
        return item ? moment(item).format("DD/MM/YYYY") : "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.ngayHetHanHopDong")}
          searchSelect={
            <DatePicker
              format={dateFormat}
              placeholder={t("common.timKiem")}
              onChange={onSearchDate("ngayHetHanHopDong")}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "ngayHetHanHopDong",
      key: "ngayHetHanHopDong",
      show: true,
      i18Name: "kho.quyetDinhThau.ngayHetHanHopDong",
      render: (item) => {
        return item ? moment(item).format("DD/MM/YYYY") : "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.ghiChu")}
          sort_key="ghiChu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.ghiChu || 0}
          search={
            <InputTimeout
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("ghiChu")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "ghiChu",
      key: "ghiChu",
      show: true,
      i18Name: "common.ghiChu",
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.coHieuLuc")}
          sort_key="active"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["active"] || 0}
          searchSelect={
            <Select
              defaultValue=""
              data={HIEU_LUC}
              placeholder={t("kho.chonHieuLuc")}
              onChange={onSearchInput("active")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "active",
      key: "active",
      align: "center",
      show: true,
      i18Name: "kho.coHieuLuc",
      render: (item) => {
        return <Checkbox checked={item} onClick={() => {}} />;
      },
    },
  ];
  const onExportDuLieu = async () => {
    try {
      showLoading();
      const keysToRemove = ["dichVu.ma", "dichVu.ten"];
      const filteredData = Object.fromEntries(
        Object.entries(dataSearch).filter(
          ([key]) => !keysToRemove.includes(key)
        )
      );

      const payload = {
        ...filteredData,
        ...((dataSearch["dichVu.ma"] || dataSearch["dichVu.ten"]) && {
          dichVu: {
            ma: dataSearch["dichVu.ma"],
            ten: dataSearch["dichVu.ten"],
          },
        }),
      };
      await onExportTheoParam(payload);
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const menuMultiTab = () => (
    <Menu
      items={[
        {
          key: 0,
          label: (
            <a
              onClick={() =>
                refModalImport.current &&
                refModalImport.current.show({ isModalVisible: true })
              }
            >
              <div className="flex icon_utilities gap-8">
                <SVG.IcUpload />
                <span>{t("danhMuc.nhapDuLieuTitle", { title: "" })}</span>
              </div>
            </a>
          ),
        },
        {
          key: 1,
          label: (
            <a onClick={onExport}>
              <div className="flex icon_utilities gap-8">
                <SVG.IcDownload />
                <span>{t("danhMuc.xuatDuLieuTitle", { title: "" })}</span>
              </div>
            </a>
          ),
        },
        {
          key: 2,
          label: (
            <a onClick={onExportDuLieu}>
              <div className="flex icon_utilities gap-8">
                <SVG.IcDownload />
                <span>
                  {t("danhMuc.xuatDuLieuTitle", { title: "theo bộ lọc" })}
                </span>
              </div>
            </a>
          ),
        },
      ]}
    />
  );

  return (
    <Main>
      <TableWrapper
        scroll={{ x: 1000 }}
        classNameRow={"custom-header"}
        styleMain={{ marginTop: 0 }}
        rowKey={(record) => record?.id}
        styleContainerButtonHeader={{
          display: "flex",
          width: "100%",
          justifyContent: "flex-end",
          alignItems: "center",
          paddingRight: 35,
        }}
        buttonHeader={[
          {
            content: (
              <Button
                type="success"
                onClick={onReset}
                rightIcon={<SVG.IcAdd />}
              >
                {t("common.themMoiF1")}
              </Button>
            ),
          },
          {
            className: `btn-change-full-table ${
              showFullTable ? "small" : "large"
            }`,
            title: showFullTable ? <SVG.IcShowThuNho /> : <SVG.IcShowFull />,
            onClick: handleChangeshowTable,
          },

          {
            className: "btn-collapse",
            title: collapseStatus ? <SVG.IcExtend /> : <SVG.IcCollapse />,
            onClick: handleCollapsePane,
          },
        ]}
        columns={columns}
        dataSource={listQuyetDinhThauChiTiet}
        onRow={onRow}
        rowClassName={(record, index) =>
          moment(new Date(record?.quyetDinhThau?.ngayHieuLuc)).diff(
            moment(new Date()),
            "days"
          ) < 0
            ? "row-yellow"
            : dataEditDefault?.id === record.id
            ? "row-actived row-id-" + record.id
            : "row-id-" + record.id
        }
        menuMultiTab={menuMultiTab}
        tableName="table_Kho-danhSachDichVuTrongThau-chiTietThau"
        ref={refSettings}
        virtual={size >= 500}
      />
      {!!totalElements && (
        <Pagination
          onChange={onChangePage}
          current={page + 1}
          pageSize={size}
          listData={listQuyetDinhThauChiTiet}
          total={totalElements}
          onShowSizeChange={handleSizeChange}
          style={{ flex: 1, justifyContent: "flex-end" }}
          pageSizeOptions={["10", "20", "50", "100", "200", "500", "1000"]}
        />
      )}
      <ModalImport onImport={onImport} ref={refModalImport} />
    </Main>
  );
};

export default ChiTietThau;
