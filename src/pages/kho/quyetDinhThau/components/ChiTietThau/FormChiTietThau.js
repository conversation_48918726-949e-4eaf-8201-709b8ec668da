import React, { useState, useEffect, useRef, useMemo } from "react";
import { Input, Form, InputNumber, message } from "antd";
import {
  Select,
  SelectLoadMore,
  Checkbox,
  DateTimePicker,
  InputTimeout,
} from "components";
import EditWrapper from "components/MultiLevelTab/EditWrapper";
import { useDispatch, useSelector } from "react-redux";
import { normalizeNumber, openInNewTab, parseFloatNumber } from "utils";
import ModalListDichVuTimKiem from "./ModalListDichVuTimKiem";
import TableChiTietKichCo from "./TableChiTietKichCo";
import TableChiTietBo from "./TableChiTietBo";
import TabPanel from "components/MultiLevelTab/TabPanel";
import FormWraper from "components/FormWraper";
import dichVuKhoProvider from "data-access/categories/dm-dich-vu-kho-provider";
import { useEnum, useListAll, useStore, useThietLap } from "hooks";
import {
  ENUM,
  HOTKEY,
  LOAI_DICH_VU,
  THIET_LAP_CHUNG,
  ROLES,
} from "constants/index";
import { useTranslation } from "react-i18next";
import TextArea from "antd/lib/input/TextArea";
import { InputNumberFormat } from "components/common";
import { Main } from "./styled";
import { lowerFirst } from "lodash";
import moment from "moment";
import { checkRole } from "lib-utils/role-utils";

const FormChiTietThau = (
  { layerId, ignoreField = [], form, onReset = () => {}, ...props },
  ref
) => {
  const [state, _setState] = useState({
    vatTuBo: false,
    vatTuKichCo: false,
    disabled: false,
    showField: false,
    isLoaiVatTu: false,
    isLoaiThuoc: false,
    disabledHamLuong: false,
    tyLeBhTt: null,
    tyLeTtDv: null,
  });
  const setState = (data) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const { t } = useTranslation();

  const { onRegisterHotkey } = useDispatch().phimTat;
  const refClickBtnSave = useRef();
  const refAutoFocus = useRef();
  const refSearchHangHoa = useRef();
  const { listAllQuyetDinhThau } = useSelector((state) => state.quyetDinhThau);
  const { newRecordThau } = useSelector((state) => state.dichVuKho);

  const { listAllXuatXu } = useSelector((state) => state.xuatXu);
  const { listAllNhomDichVuKhoCap1 } = useSelector(
    (state) => state.nhomDichVuKho
  );
  const { listDataVatTuBo: listDataVatTuBoDanhMuc } = useSelector(
    (state) => state.danhMucVatTu
  );
  const listDataTongHop = useStore("nhomChiPhi.listDataTongHop", []);
  const [listAllDuongDung] = useListAll("duongDung", {}, true);
  const listAllHoatChat = useStore("hoatChat.listAllHoatChat", []);
  const listAllNhaSanXuat = useStore("doiTac.listAllNhaSanXuat", []);
  const listAllNhaCungCap = useStore("doiTac.listAllNhaCungCap", []);
  const dataEditDefault = useStore(
    "quyetDinhThauChiTiet.dataEditDefault",
    null
  );
  const [listAllPhuongPhapCheBien] = useListAll("phuongPhapCheBien", {}, true);
  const [dataNHAP_KICH_CO_VAT_TU] = useThietLap(
    THIET_LAP_CHUNG.NHAP_KICH_CO_VAT_TU,
    "0"
  );

  const {
    doiTac: { getListAllNhaSanXuat, getListAllNhaCungCap },
    nhomChiPhi: { onSearchTongHop },
  } = useDispatch();

  const {
    quyetDinhThauChiTiet: {
      onSearch: onSearchQuyetDinhThau,
      createOrEdit,
      createBatch,
    },
    danhMucVatTu: { getListVatTuBo: getListVatTuBoDanhMuc },
    dichVuKho: { onSearch },
  } = useDispatch();

  const [listgoiThau] = useEnum(ENUM.GOI_THAU);
  const [listnhomThau] = useEnum(ENUM.NHOM_THAU);
  const [listloaiThuocThau] = useEnum(ENUM.LOAI_THUOC_THAU);

  useEffect(() => {
    onRegisterHotkey({
      layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.F4, //F4
          onEvent: (e) => {
            refClickBtnSave.current && refClickBtnSave.current(e);
          },
        },
      ],
    });
  }, []);

  useEffect(() => {
    if (listDataVatTuBoDanhMuc.length) {
      setState({ listVatTuCon: listDataVatTuBoDanhMuc });
    }
  }, [listDataVatTuBoDanhMuc]);

  useEffect(() => {
    if (dataEditDefault?.id) {
      const newInfo = {
        ...dataEditDefault,
        nam: dataEditDefault?.quyetDinhThau?.nam,
        ma: dataEditDefault?.dichVu?.ma,
        tenDvtSoCap: dataEditDefault?.dichVu?.tenDvtSoCap,
        hamLuong: dataEditDefault?.hamLuong,
        tenNhomDvKhoCap1: dataEditDefault?.dichVu?.tenNhomDvKhoCap1,
        tenDuongDung: dataEditDefault?.dichVu?.tenDuongDung,
        tenQuyetDinhThau: dataEditDefault?.quyetDinhThau?.quyetDinhThau,
        tenNhomChiPhi: dataEditDefault?.nhomChiPhi?.ten,
        ghiChu: dataEditDefault?.ghiChu,
        maAtc: dataEditDefault?.maAtc,
        nhaNhapKhau:
          dataEditDefault?.dichVu?.loaiDichVu === LOAI_DICH_VU.THUOC &&
          dataEditDefault?.dichVu?.nhaNhapKhau,
        dieuKienBaoQuan:
          dataEditDefault?.dichVu?.loaiDichVu === LOAI_DICH_VU.THUOC &&
          dataEditDefault?.dichVu?.dieuKienBaoQuan,
        chatLuongCamQuan:
          dataEditDefault?.dichVu?.loaiDichVu === LOAI_DICH_VU.THUOC &&
          dataEditDefault?.dichVu?.chatLuongCamQuan,
        ngayHopDong:
          dataEditDefault.ngayHopDong && moment(dataEditDefault.ngayHopDong),
        ngayHetHanHopDong:
          dataEditDefault.ngayHetHanHopDong &&
          moment(dataEditDefault.ngayHetHanHopDong),
      };
      setState({
        loaiDichVuThau: dataEditDefault?.dichVu?.loaiDichVu
          ? [dataEditDefault?.dichVu?.loaiDichVu]
          : [],
        showField:
          dataEditDefault?.dichVu?.loaiDichVu !== 100 &&
          dataEditDefault?.dichVu?.loaiDichVu !== 110,
        isLoaiVatTu:
          dataEditDefault?.dichVu?.loaiDichVu === LOAI_DICH_VU.VAT_TU,
        isLoaiThuoc: dataEditDefault?.dichVu?.loaiDichVu === LOAI_DICH_VU.THUOC,
        dataEdit: newInfo,
        disabled:
          dataEditDefault?.trangThai && dataEditDefault?.trangThai !== 10,
        disabledHamLuong:
          dataEditDefault?.quyetDinhThau?.trangThai &&
          dataEditDefault?.quyetDinhThau?.trangThai !== 10,
        vatTuBo: newInfo.dichVu?.vatTuBo,
        vatTuKichCo: newInfo.dichVu?.vatTuKichCo,
        dataVatTuCha: newInfo,
        id: dataEditDefault?.id,
        tyLeBhTt: dataEditDefault?.dichVu?.tyLeBhTt,
        tyLeTtDv: dataEditDefault?.dichVu?.tyLeTtDv,
        addParam: {
          dsLoaiDichVu: dataEditDefault?.dichVu?.loaiDichVu || [],
        },
      });
      getListAllNhaSanXuat({
        page: "",
        size: "",
        active: true,
        loaiDichVu: dataEditDefault?.dichVu?.loaiDichVu,
      });
      getListAllNhaCungCap({
        page: "",
        size: "",
        active: true,
        loaiDichVu: dataEditDefault?.dichVu?.loaiDichVu,
      });

      form.setFieldsValue(newInfo);
    } else {
      form.resetFields();
      setState({
        tyLeBhTt: null,
        tyLeTtDv: null,
      });
    }
  }, [dataEditDefault]);

  useEffect(() => {
    if (newRecordThau) {
      async function fillThongTin() {
        let listNhomChiPhi = [];
        if (newRecordThau?.loaiDichVu === LOAI_DICH_VU.VAC_XIN) {
          listNhomChiPhi = await onSearchTongHop({
            page: "",
            size: "",
            active: true,
          });
        } else {
          listNhomChiPhi = await onSearchTongHop({
            page: "",
            size: "",
            active: true,
            dichVuId: newRecordThau?.id,
          });
        }

        const newInfo = {
          ...newRecordThau,
          giaNhap: newRecordThau?.giaNhap,
          giaNhapSauVat: newRecordThau?.giaNhapSauVat,
          giaKhongBaoHiem: newRecordThau?.giaKhongBaoHiem,
          giaBaoHiem: newRecordThau?.giaBaoHiem,
          quyCach: newRecordThau?.quyCach,
          nhaCungCapId: newRecordThau?.nhaCungCapId,
          soVisa: newRecordThau?.soVisa,
          tyLeBhTt: newRecordThau?.tyLeBhTt,
          xuatXuId: newRecordThau?.xuatXuId,
          nhaSanXuatId: newRecordThau?.nhaSanXuatId,
          tranBaoHiem: newRecordThau?.tranBaoHiem,
          hamLuong: newRecordThau?.hamLuong,
          hoatChatId: newRecordThau?.hoatChatId,
          donViTinhId: newRecordThau?.donViTinhId,
          ma: newRecordThau?.ma,
          maKyHieu: newRecordThau?.maKyHieu,
          tenDuongDung: newRecordThau?.tenDuongDung,
          giaTran: newRecordThau?.giaTran,
          dichVuId: newRecordThau?.id,
          maTuongDuong: newRecordThau?.maTuongDuong,
          tenNhomDvKhoCap1:
            newRecordThau?.loaiDichVu === 90
              ? null
              : newRecordThau?.tenNhomDvKhoCap1,
          tenDvtSoCap: newRecordThau?.tenDvtSoCap,
          dsPhuongPhapCheBienId: newRecordThau?.dsPhuongPhapCheBienId,
          maAtc: newRecordThau?.maAtc,
          dangBaoChe: newRecordThau?.dangBaoChe,
          maTrungThau: newRecordThau.ma,
          maDauThau: newRecordThau.ma,
          ngayHopDong:
            newRecordThau.ngayHopDong && moment(newRecordThau.ngayHopDong),
          ngayHetHanHopDong:
            newRecordThau.ngayHetHanHopDong &&
            moment(newRecordThau.ngayHetHanHopDong),
          nhomChiPhiId:
            listNhomChiPhi?.length === 1 ? listNhomChiPhi[0].id : null,
        };
        form.setFieldsValue(newInfo);
        setState({
          showField:
            newRecordThau?.loaiDichVu !== 100 &&
            newRecordThau?.loaiDichVu !== 110,
          isLoaiVatTu: newRecordThau?.loaiDichVu === LOAI_DICH_VU.VAT_TU,
          isLoaiThuoc: newRecordThau?.loaiDichVu === LOAI_DICH_VU.THUOC,
          vatTuBo: newRecordThau?.vatTuBo,
          vatTuKichCo: newRecordThau?.vatTuKichCo,
          dataVatTuCha: {
            ...newInfo,
            quyetDinhThauId: form.getFieldValue("quyetDinhThauId"),
            dichVu: newRecordThau?.dichVu,
          },
          tyLeBhTt: newRecordThau?.tyLeBhTt,
          tyLeTtDv: newRecordThau?.tyLeTtDv,
        });

        if (newRecordThau?.vatTuBo) {
          getListVatTuBoDanhMuc({
            vatTuBoId: newRecordThau?.id,
            "dichVu.loaiDichVu": newRecordThau?.loaiDichVu,
          });
        }
      }
      fillThongTin();
    }
  }, [newRecordThau]);

  const onFillThongTinDichVu = async ({ data, ten }) => {
    if (data.length === 1) {
      let dichVu = data[0];
      let listNhomChiPhi = [];
      if (dichVu?.loaiDichVu === LOAI_DICH_VU.VAC_XIN) {
        listNhomChiPhi = await onSearchTongHop({
          page: "",
          size: "",
          active: true,
        });
      } else {
        listNhomChiPhi = await onSearchTongHop({
          page: "",
          size: "",
          active: true,
          dichVuId: dichVu?.id,
        });
      }
      let newInfo = {
        ...dichVu,
        giaNhap: dichVu.giaNhap,
        giaNhapSauVat: dichVu.giaNhapSauVat,
        giaKhongBaoHiem: dichVu.giaKhongBaoHiem,
        giaBaoHiem: dichVu.giaBaoHiem,
        quyCach: dichVu.quyCach,
        nhaCungCapId: dichVu.nhaCungCapId,
        soVisa: dichVu.soVisa,
        tyLeBhTt: dichVu.tyLeBhTt,
        xuatXuId: dichVu.xuatXuId,
        nhaSanXuatId: dichVu.nhaSanXuatId,
        tranBaoHiem: dichVu.tranBaoHiem,
        hamLuong: dichVu.hamLuong,
        hoatChatId: dichVu.hoatChatId,
        donViTinhId: dichVu.donViTinhId,
        ma: dichVu.ma,
        maKyHieu: dichVu.maKyHieu,
        tenDuongDung: dichVu.tenDuongDung,
        giaTran: dichVu.giaTran,
        dichVuId: dichVu.id,
        maTuongDuong: dichVu.maTuongDuong,
        tenNhomDvKhoCap1:
          dichVu.loaiDichVu === 90 ? null : dichVu.tenNhomDvKhoCap1,
        tenDvtSoCap: dichVu?.tenDvtSoCap,
        maAtc: dichVu?.maAtc,
        maTrungThau: dichVu.ma,
        maDauThau: dichVu.ma,
        ngayHopDong: dichVu.ngayHopDong && moment(dichVu.ngayHopDong),
        ngayHetHanHopDong:
          dichVu.ngayHetHanHopDong && moment(dichVu.ngayHetHanHopDong),
        nhomChiPhiId:
          listNhomChiPhi?.length === 1 ? listNhomChiPhi[0].id : null,
      };
      if (dichVu.loaiDichVu === LOAI_DICH_VU.THUOC && dichVu.hoatChatId) {
        const selectedHoatChat = listAllHoatChat.find(
          (x) => x.id === dichVu.hoatChatId
        );
        newInfo.maTuongDuong = selectedHoatChat?.ma || "";
      }
      form.setFieldsValue(newInfo);
      setState({
        showField: dichVu.loaiDichVu !== 100 && dichVu.loaiDichVu !== 110,
        isLoaiVatTu: dichVu.loaiDichVu === LOAI_DICH_VU.VAT_TU,
        isLoaiThuoc: dichVu.loaiDichVu === LOAI_DICH_VU.THUOC,
        tyLeBhTt: dichVu?.tyLeBhTt,
        tyLeTtDv: dichVu?.tyLeTtDv,
        vatTuBo: dichVu.vatTuBo,
        vatTuKichCo: dichVu.vatTuKichCo,
        dataVatTuCha: {
          ...newInfo,
          quyetDinhThauId: form.getFieldValue("quyetDinhThauId"),
          dichVu: dichVu,
        },
      });
    } else {
      refSearchHangHoa.current &&
        refSearchHangHoa.current.show({
          ten: ten,
          loaiDichVu: state?.loaiDichVuThau,
        });
    }
  };
  const onShowDsHangHoa = (e, item) => {
    onSearch({
      dataSearch: { ten: item.ten, ...state?.addParam },
    }).then((s) => {
      onFillThongTinDichVu({ data: s?.data, ten: item.ten });
    });
  };

  const onValuesChange = (changedValues, allValues) => {
    if (state.isLoaiThuoc && changedValues.hasOwnProperty("hoatChatId")) {
      const selectedHoatChat = listAllHoatChat.find(
        (x) => x.id === changedValues.hoatChatId
      );
      form.setFieldsValue({ maTuongDuong: selectedHoatChat?.ma || "" });
    }
    if (changedValues.hasOwnProperty("ngayHopDong")) {
      if (
        changedValues?.ngayHopDong &&
        changedValues?.ngayHopDong instanceof moment
      ) {
        const _ngayHetHanHopDong = changedValues.ngayHopDong
          .clone()
          .add(1, "years");

        form.setFieldsValue({ ngayHetHanHopDong: _ngayHetHanHopDong });
      }
    }
  };
  const onChangeField = (fieldName, value, option) => {
    if ("quyetDinhThau" === fieldName) {
      let loaiDV = option.dsLoaiDichVu || [];
      setState({
        showField: !loaiDV.includes(100) && !loaiDV.includes(110),
        isLoaiVatTu: loaiDV === LOAI_DICH_VU.VAT_TU,
        isLoaiThuoc: loaiDV === LOAI_DICH_VU.THUOC,
        loaiDichVuThau: loaiDV,
        addParam: { dsLoaiDichVu: loaiDV },
        disabledHamLuong: option.trangThai != 10, //cho phép sửa hàm lượng khi quyết định thầu ở trạng thái chưa hoàn thành (10)
      });
      form.setFieldsValue({ nam: option.nam });
      getListAllNhaSanXuat({
        page: "",
        size: "",
        active: true,

        dsLoaiDichVu: loaiDV,
      });
      getListAllNhaCungCap({
        page: "",
        size: "",
        active: true,
        dsLoaiDichVu: loaiDV,
      });
    }
    if ("soLuongThau" === fieldName) {
      form.setFieldsValue({
        soLuongDuocPhepMua: value?.target?.value,
        nguongThau: value?.target?.value,
      });
      setState({
        dataVatTuCha: {
          ...state?.dataVatTuCha,
          soLuongThau: value?.target?.value,
        },
      });
    }

    if ("maTrungThau" === fieldName) {
      form.setFieldsValue({ maDauThau: value?.target?.value });
    }
    if ("nhaCungCapId" === fieldName) {
      setState({
        dataVatTuCha: { ...state?.dataVatTuCha, nhaCungCapId: value },
      });
    }
  };
  const validateSoLuong = async (rule, value) => {
    const soLuongThau =
      normalizeNumber(form.getFieldValue("soLuongThau")) != "undefined"
        ? normalizeNumber(form.getFieldValue("soLuongThau"))
        : 0;
    const soLuong =
      normalizeNumber(value) != "undefined" ? normalizeNumber(value) : 0;
    if (soLuong > soLuongThau * 1.2)
      return Promise.reject(
        new Error(t("kho.quyetDinhThau.khongDuocPhepNhapSlLonHon120%SlThau"))
      );
  };

  const onSave = (e) => {
    form.submit();
  };
  refClickBtnSave.current = onSave;

  const onFinishFailed = () => {
    message.error(t("danhMuc.vuiLongDienDayDuTt"));
  };

  const handleSumitForm = async (values) => {
    try {
      let payload = {
        ...values,
        ngayHopDong: values?.ngayHopDong?.format("YYYY-MM-DD"),
        ngayHetHanHopDong: values?.ngayHetHanHopDong?.format("YYYY-MM-DD"),
        id: dataEditDefault?.id,
      };
      [
        "giaKhongBaoHiem",
        "giaNhapSauVat",
        "giaBaoHiem",
        "giaPhuThu",
        "nguongThau",
        "soLuongThau",
        "soLuongDuocPhepMua",
      ].forEach((key) => {
        if (
          typeof values?.[key] === "string" ||
          values?.[key] instanceof String
        ) {
          payload[key] = parseFloatNumber(values?.[key]);
        }
      });

      const s = await createOrEdit(payload);

      let bodyVatTuCon = (state?.listVatTuCon || []).map((item) => {
        return {
          ...item,
          vatTuBoId: s?.id,
        };
      });
      if (bodyVatTuCon.length) {
        await createBatch(bodyVatTuCon);
      }

      onSearchQuyetDinhThau({ isSearchInput: true });
    } catch (error) {
      console.error(error);
    }
  };

  const handleErrors = (errors) => {
    console.log("errors: ", errors);
  };

  const selectedVatTuCon = (item) => {
    setState({ listVatTuCon: item });
  };

  const listDataThauTongHop = useMemo(() => {
    let item = listAllQuyetDinhThau.find(
      (x) => x.id === dataEditDefault?.quyetDinhThauId && x.trangThai !== 10
    );
    if (item) {
      return [item, ...listAllQuyetDinhThau.filter((x) => x.trangThai === 10)];
    } else {
      return listAllQuyetDinhThau.filter((x) => x.trangThai === 10);
    }
  }, [listAllQuyetDinhThau, dataEditDefault]);
  const onCancel = (data) => {
    form.setFieldsValue(data);
  };

  const addValue = useMemo(() => {
    let _addValue = [];
    if (dataEditDefault?.id) {
      _addValue = [
        ..._addValue,
        {
          value: dataEditDefault?.dichVuId,
          label: `${dataEditDefault?.dichVu?.ma} - ${dataEditDefault?.dichVu?.ten}`,
        },
      ];
    }

    if (newRecordThau?.id) {
      _addValue = [
        ..._addValue,
        {
          value: newRecordThau?.id,
          label: `${newRecordThau?.ma} - ${newRecordThau?.ten}`,
        },
      ];
    }

    return _addValue;
  }, [dataEditDefault, newRecordThau]);

  const onKeyDown = (e) => {
    if (e.keyCode === 13) {
      onSearch({
        dataSearch: { ten: e?.target?.value, ...state?.addParam },
      }).then((s) => {
        onFillThongTinDichVu({ data: s?.data, ten: e?.target?.value });
      });
      e.stopPropagation();
    }
  };

  return (
    <Main>
      <TabPanel>
        <EditWrapper
          title={t("kho.quyetDinhThau.thongTinChiTiet")}
          onCancel={() => onCancel(dataEditDefault)}
          onSave={onSave}
          showAdded={false}
          isShowSaveButton={true}
          isShowCancelButton={true}
        >
          <FormWraper
            form={form}
            layout="vertical"
            onFinish={handleSumitForm}
            onFinishFailed={onFinishFailed}
            onError={handleErrors}
            style={{ width: "100%" }}
            className="form-custom"
            onValuesChange={onValuesChange}
          >
            <Form.Item
              label={t("kho.quyetDinhThau.title")}
              name="quyetDinhThauId"
              rules={[
                {
                  required: true,
                  message: t("kho.quyetDinhThau.vuiLongNhapQuyetDinhThau"),
                },
              ]}
            >
              <Select
                placeholder={t("kho.quyetDinhThau.chonThauTruocKhiChonHangHoa")}
                data={listDataThauTongHop}
                ten="quyetDinhThau"
                getLabel={(item) => `${item.quyetDinhThau || ''} ${item.soThau ? " - " + item.soThau : ''}`}
                onChange={(value, option) =>
                  onChangeField("quyetDinhThau", value, option)
                }
                disabled={state?.disabled}
              />
            </Form.Item>
            <Form.Item
              label={t("kho.tenHangHoa")}
              name="dichVuId"
              rules={[
                {
                  required: true,
                  message: t("kho.vuiLongChonHangHoa"),
                },
              ]}
            >
              <SelectLoadMore
                api={
                  state?.loaiDichVuThau?.length
                    ? dichVuKhoProvider.searchAll
                    : null
                }
                mapData={(i) => ({
                  value: i.id,
                  ten: i.ten,
                  label: `${i.ma} - ${i.ten}`,
                })}
                keySearch={"ten"}
                placeholder={t("kho.vuiLongChonHangHoa")}
                onChange={onShowDsHangHoa}
                disabled={!form.getFieldValue("quyetDinhThauId")}
                refSelect={refAutoFocus}
                addParam={state?.addParam}
                addValue={addValue}
                onInputKeyDown={onKeyDown}
              />
            </Form.Item>
            <Form.Item
              label={t("kho.quyetDinhThau.maHangHoaTrungThau")}
              name="maTrungThau"
              rules={[
                {
                  required: true,
                  message: t("kho.quyetDinhThau.vuiLongNhapMaTrungThau"),
                },
              ]}
            >
              <Input
                className="input-option"
                placeholder={t("kho.quyetDinhThau.vuiLongNhapMaTrungThau")}
                disabled={state?.disabled}
                onChange={(value) => onChangeField("maTrungThau", value)}
              />
            </Form.Item>
            <Form.Item
              label={
                <div className="color">
                  {t("kho.quyetDinhThau.tenHangHoaTrungThau")}
                </div>
              }
              name="tenTrungThau"
            >
              <Input
                className="input-option"
                placeholder={t("kho.quyetDinhThau.vuiLongNhapTenTrungThau")}
                disabled={state?.disabled}
              />
            </Form.Item>
            <Form.Item
              label={t("kho.quyetDinhThau.maHangHoaDauThau")}
              name="maDauThau"
            >
              <Input
                className="input-option"
                placeholder={t("kho.quyetDinhThau.vuiLongNhapMaDauThau")}
                disabled={state?.disabled}
              />
            </Form.Item>
            <Form.Item
              label={t("kho.quyetDinhThau.slThau")}
              name="soLuongThau"
              rules={[
                {
                  required: true,
                  message: t("kho.quyetDinhThau.vuiLongNhapSlThau"),
                },
              ]}
            >
              <InputNumberFormat
                className="input-option"
                placeholder={t("kho.quyetDinhThau.vuiLongNhapSlThau")}
                min={0}
                disabled={state?.disabled}
                onChange={(value) => onChangeField("soLuongThau", value)}
              />
            </Form.Item>
            <Form.Item
              label={t("kho.quyetDinhThau.slDuocPhepMua")}
              name="soLuongDuocPhepMua"
              rules={[
                {
                  required: true,
                  message: t("kho.quyetDinhThau.vuiLongNhapSlDuocPhepMua"),
                },
                {
                  validator: validateSoLuong,
                },
              ]}
            >
              <InputNumberFormat
                className="input-option"
                placeholder={t("kho.quyetDinhThau.vuiLongNhapSlDuocPhepMua")}
                min={0}
                decimalScale={2}
                disabled={state?.disabled}
              />
            </Form.Item>
            <Form.Item
              label={t("kho.quyetDinhThau.giaNhapSauVat")}
              name="giaNhapSauVat"
              rules={[
                {
                  required: true,
                  message: t("kho.quyetDinhThau.vuiLongNhapGiaSauVat"),
                },
              ]}
            >
              <InputNumberFormat
                className="input-option"
                placeholder={t("kho.quyetDinhThau.vuiLongNhapGiaSauVat")}
                min={0}
                decimalScale={3}
                disabled={state?.disabled}
              />
            </Form.Item>
            <Form.Item
              label={t("kho.quyetDinhThau.donGiaKhongBh")}
              name="giaKhongBaoHiem"
            >
              <InputNumberFormat
                className="input-option"
                placeholder={t("kho.quyetDinhThau.vuiLongNhapGiaKhongBh")}
                min={0}
                decimalScale={3}
                disabled={state?.disabled}
                onChange={(value) => onChangeField("giaKhongBaoHiem", value)}
              />
            </Form.Item>

            <Form.Item
              label={t("kho.quyetDinhThau.donGiaBh")}
              name="giaBaoHiem"
              rules={[
                {
                  required: true,
                  message: t("kho.quyetDinhThau.vuiLongNhapGiaBh"),
                },
              ]}
            >
              <InputNumberFormat
                className="input-option"
                placeholder={t("kho.quyetDinhThau.vuiLongNhapGiaBh")}
                min={0}
                decimalScale={3}
                disabled={state?.disabled}
                onChange={(value) => onChangeField("giaBaoHiem", value)}
              />
            </Form.Item>

            {checkRole([ROLES["KHO"].HIEN_THI_TY_LE_THANH_TOAN_DANH_MUC]) && (
              <>
                <Form.Item
                  label={t("kho.quyetDinhThau.tiLeThanhToanBHDanhMuc")}
                >
                  <Input
                    className="input-option"
                    disabled={true}
                    value={state.tyLeBhTt}
                  />
                </Form.Item>
                <Form.Item
                  label={t("kho.quyetDinhThau.tiLeThanhToanDichVuDanhMuc")}
                >
                  <Input
                    className="input-option"
                    disabled={true}
                    value={state.tyLeTtDv}
                  />
                </Form.Item>
              </>
            )}

            {!(state?.loaiDichVuThau || []).includes(110) && (
              <Form.Item label={t("kho.quyetDinhThau.phuThu")} name="giaPhuThu">
                <InputNumberFormat
                  className="input-option"
                  placeholder={t("kho.quyetDinhThau.vuiLongNhapGiaPhuThu")}
                  min={0}
                  decimalScale={3}
                  disabled={state?.disabled}
                  onChange={(value) => onChangeField("giaPhuThu", value)}
                />
              </Form.Item>
            )}
            <Form.Item
              label={t("kho.quyetDinhThau.quyCach")}
              name="quyCach"
              rules={[
                {
                  required: true,
                  message: t("kho.quyetDinhThau.vuiLongNhapQuyCach"),
                },
              ]}
            >
              <Input
                className="input-option"
                placeholder={t("kho.quyetDinhThau.vuiLongNhapQuyCach")}
                disabled={state?.disabled}
              />
            </Form.Item>
            <Form.Item
              label={t("kho.quyetDinhThau.nhaCungCap")}
              name="nhaCungCapId"
              rules={[
                {
                  required: true,
                  message: t("kho.quyetDinhThau.vuiLongChonNhaCungCap"),
                },
              ]}
            >
              <Select
                placeholder={t("kho.quyetDinhThau.vuiLongChonNhaCungCap")}
                data={listAllNhaCungCap}
                disabled={state?.disabled}
              />
            </Form.Item>
            {(state?.showField || state.isLoaiVatTu) && (
              <Form.Item
                label={
                  <div className="color">
                    {t("kho.quyetDinhThau.maGoiThau")}
                  </div>
                }
                name="goiThau"
                rules={
                  !state.isLoaiVatTu
                    ? [
                        {
                          required: true,
                          message: t("kho.quyetDinhThau.vuiLongChonMaGoiThau"),
                        },
                      ]
                    : []
                }
              >
                <Select
                  placeholder={t("kho.quyetDinhThau.vuiLongChonMaGoiThau")}
                  data={listgoiThau}
                  disabled={state?.disabled}
                />
              </Form.Item>
            )}

            <Form.Item
              label={
                <div className="color">{t("kho.quyetDinhThau.soVisa")}</div>
              }
              name="soVisa"
              rules={[
                {
                  required: !!state?.showField,
                  message: t("kho.quyetDinhThau.vuiLongNhapSoVisa"),
                },
              ]}
            >
              <Input
                className="input-option"
                placeholder={t("kho.quyetDinhThau.vuiLongNhapSoVisa")}
                disabled={state?.disabled}
              />
            </Form.Item>

            {(state?.showField || state.isLoaiVatTu) && (
              <Form.Item
                label={
                  <div className="color">{t("kho.quyetDinhThau.nhomThau")}</div>
                }
                name="nhomThau"
                rules={
                  !state.isLoaiVatTu
                    ? [
                        {
                          required: true,
                          message: t("kho.quyetDinhThau.vuiLongChonNhomThau"),
                        },
                      ]
                    : []
                }
              >
                <Select
                  placeholder={t("kho.quyetDinhThau.vuiLongChonNhomThau")}
                  data={listnhomThau}
                  disabled={state?.disabled}
                />
              </Form.Item>
            )}
            <Form.Item
              label={t("kho.quyetDinhThau.nhomChiPhi")}
              name="nhomChiPhiId"
              rules={[
                {
                  required: true,
                  message: t("kho.quyetDinhThau.vuiLongChonNhomChiPhi"),
                },
              ]}
            >
              <Select
                placeholder={t("kho.quyetDinhThau.vuiLongChonNhomChiPhi")}
                data={listDataTongHop}
              ></Select>
            </Form.Item>
            <Form.Item
              label={
                <div className="color">
                  {t("kho.quyetDinhThau.tyLeThanhToanBh")}
                </div>
              }
              name="tyLeBhTt"
              rules={[
                {
                  required: true,
                  message: t("kho.quyetDinhThau.vuiLongNhapTyLeThanhToanBh"),
                },
              ]}
            >
              <InputNumber
                className="input-option"
                placeholder={t("kho.quyetDinhThau.vuiLongNhapTyLeThanhToanBh")}
                formatter={(value) => `${value}%`}
                parser={(value) => value.replace("%", "")}
                min={0}
                max={100}
                disabled={state?.disabled}
              />
            </Form.Item>
            <Form.Item
              label={t("kho.quyetDinhThau.nguongThau")}
              name="nguongThau"
              rules={[
                {
                  required: true,
                  message: t("kho.quyetDinhThau.vuiLongNhapNguongThau"),
                },
              ]}
            >
              <InputNumberFormat
                className="input-option"
                placeholder={t("kho.quyetDinhThau.vuiLongNhapNguongThau")}
                disabled={state?.disabled}
                decimalScale={3}
              />
            </Form.Item>
            {state?.showField && (
              <Form.Item
                label={t("kho.quyetDinhThau.loaiThuoc")}
                name="loaiThuoc"
                rules={[
                  {
                    required: true,
                    message: t("kho.quyetDinhThau.vuiLongChonLoaiThuoc"),
                  },
                ]}
              >
                <Select
                  placeholder={t("kho.quyetDinhThau.vuiLongChonLoaiThuoc")}
                  data={listloaiThuocThau}
                  disabled={state?.disabled}
                />
              </Form.Item>
            )}
            <Form.Item
              label={
                <div
                  className="pointer"
                  onClick={() => openInNewTab("/danh-muc/xuat-xu")}
                >
                  {t("kho.quyetDinhThau.xuatXu")}
                </div>
              }
              name="xuatXuId"
              rules={[
                {
                  required: true,
                  message: t("kho.quyetDinhThau.vuiLongChonXuatXu"),
                },
              ]}
            >
              <Select
                placeholder={t("kho.quyetDinhThau.vuiLongChonXuatXu")}
                data={listAllXuatXu}
                disabled={state?.disabled}
              />
            </Form.Item>
            <Form.Item label={t("common.nhaSanXuat")} name="nhaSanXuatId">
              <Select
                placeholder={t("kho.quyetDinhThau.vuiLongChonNhaSanXuat")}
                data={listAllNhaSanXuat}
                disabled={state?.disabled}
              />
            </Form.Item>
            {!(
              (state?.loaiDichVuThau || []).includes(90) ||
              (state?.loaiDichVuThau || []).includes(110)
            ) && (
              <Form.Item
                label={t("kho.quyetDinhThau.tranBaoHiem")}
                name="tranBaoHiem"
              >
                <Input disabled className="input-option" />
              </Form.Item>
            )}
            <Form.Item
              label={t("kho.quyetDinhThau.soHopDong")}
              name="soHopDong"
            >
              <Input
                disabled={state?.disabled}
                className="input-option"
                placeholder={t("kho.quyetDinhThau.vuiLongNhapSoHopDong")}
              />
            </Form.Item>
            {state?.showField && (
              <Form.Item label={t("kho.quyetDinhThau.giaTran")} name="giaTran">
                <Input disabled className="input-option" />
              </Form.Item>
            )}

            {state?.showField && (
              <Form.Item
                label={
                  <div className="color">
                    {t("kho.quyetDinhThau.duongDung")}
                  </div>
                }
                name="duongDungId"
              >
                <Select
                  data={listAllDuongDung}
                  className="input-option"
                  placeholder={t("danhMuc.vuiLongChonTitle", {
                    title: lowerFirst(t("kho.quyetDinhThau.duongDung")),
                  })}
                />
              </Form.Item>
            )}
            {state?.showField && (
              <Form.Item
                label={
                  <div className="color">
                    {t("kho.quyetDinhThau.maHoatChat")}
                  </div>
                }
                name="hoatChatId"
              >
                <Select
                  data={listAllHoatChat.map((item) => ({
                    ...item,
                    ten: `${item.ma} - ${item.ten}`,
                  }))}
                  className="input-option"
                  placeholder={t("danhMuc.vuiLongChonTitle", {
                    title: lowerFirst(t("kho.quyetDinhThau.maHoatChat")),
                  })}
                />
              </Form.Item>
            )}
            {state?.showField && (
              <Form.Item
                label={t("kho.quyetDinhThau.hamLuong")}
                name="hamLuong"
              >
                <Input
                  disabled={state.disabledHamLuong}
                  className="input-option"
                  placeholder={t("danhMuc.vuiLongNhapTitle", {
                    title: lowerFirst(t("kho.quyetDinhThau.hamLuong")),
                  })}
                />
              </Form.Item>
            )}
            <Form.Item label={t("kho.quyetDinhThau.maHangHoa")} name="ma">
              <Input disabled className="input-option" />
            </Form.Item>
            <Form.Item label={t("kho.quyetDinhThau.nam")} name="nam">
              <Input disabled className="input-option" />
            </Form.Item>
            <Form.Item
              label={t("kho.quyetDinhThau.donViTinh")}
              name="tenDvtSoCap"
            >
              <Input disabled className="input-option" />
            </Form.Item>
            <Form.Item
              label={
                <div className="color">{t("kho.quyetDinhThau.maAnhXa")}</div>
              }
              name="maTuongDuong"
            >
              <Input
                className="input-option"
                placeholder={t("danhMuc.vuiLongNhapTitle", {
                  title: lowerFirst(t("kho.quyetDinhThau.maAnhXa")),
                })}
              />
            </Form.Item>
            {!state?.showField && (
              <Form.Item label={t("kho.quyetDinhThau.maHieu")} name="maKyHieu">
                <Input
                  className="input-option"
                  placeholder={t("danhMuc.vuiLongNhapTitle", {
                    title: lowerFirst(t("kho.quyetDinhThau.maHieu")),
                  })}
                />
              </Form.Item>
            )}
            <Form.Item
              label={t("danhMuc.nhomVatTuCap1")}
              name="tenNhomDvKhoCap1"
            >
              <Select
                data={listAllNhomDichVuKhoCap1}
                disabled
                className="input-option"
              />
            </Form.Item>
            <Form.Item
              label={t("kho.quyetDinhThau.soLuongHangHoaDaNhap")}
              name="soLuongNhap"
            >
              <InputNumberFormat
                className="input-option"
                min={0}
                decimalScale={3}
                disabled={true}
              />
            </Form.Item>
            <Form.Item
              label={t("kho.quyetDinhThau.soLuongHangHoaDaTraLaiNcc")}
              name="soLuongTra"
            >
              <InputNumberFormat
                className="input-option"
                min={0}
                decimalScale={3}
                disabled={true}
              />
            </Form.Item>
            <Form.Item
              label={t("kho.quyetDinhThau.soLuongHangHoaConLai")}
              name="soLuongCon"
            >
              <InputNumberFormat
                className="input-option"
                min={0}
                decimalScale={3}
                disabled={true}
              />
            </Form.Item>
            <Form.Item
              label={t("kho.quyetDinhThau.maAtc")}
              name="maAtc"
              rules={[
                {
                  max: 250,
                  message: "Không đươc nhập quá 250 ký tự",
                },
              ]}
            >
              <Input
                className="input-option"
                placeholder={t("danhMuc.vuiLongNhapTitle", {
                  title: lowerFirst(t("kho.quyetDinhThau.maAtc")),
                })}
              />
            </Form.Item>
            <Form.Item
              label={t("danhMuc.phuongPhapCheBien")}
              name="dsPhuongPhapCheBienId"
            >
              <Select
                placeholder={t("danhMuc.vuiLongChonTitle", {
                  title: lowerFirst(t("danhMuc.phuongPhapCheBien")),
                })}
                data={listAllPhuongPhapCheBien}
                mode="multiple"
              />
            </Form.Item>
            <Form.Item
              label={t("danhMuc.dangBaoChe")}
              name="dangBaoChe"
              rules={[
                {
                  max: 255,
                  message: t("danhMuc.khongDuocNhapQuaNumKyTu", {
                    num: 255,
                  }),
                },
              ]}
            >
              <Input
                className="input-option"
                placeholder={t("danhMuc.vuiLongNhapTitle", {
                  title: lowerFirst(t("danhMuc.dangBaoChe")),
                })}
              />
            </Form.Item>
            <Form.Item
              label={t("danhMuc.maSinhPham")}
              name="maSinhPham"
              rules={[
                { max: 50, message: t("danhMuc.khongDuocNhapQua50KyTu") },
              ]}
            >
              <Input
                className="input-option"
                placeholder={t("danhMuc.vuiLongNhapTitle", {
                  title: lowerFirst(t("danhMuc.maSinhPham")),
                })}
              />
            </Form.Item>
            <Form.Item
              label={t("danhMuc.maHieuSinhPham")}
              name="maSinhHieu"
              rules={[
                { max: 50, message: t("danhMuc.khongDuocNhapQua50KyTu") },
              ]}
            >
              <Input
                className="input-option"
                placeholder={t("danhMuc.vuiLongNhapTitle", {
                  title: lowerFirst(t("danhMuc.maHieuSinhPham")),
                })}
              />
            </Form.Item>
            <Form.Item
              label={t("kho.quyetDinhThau.ngayHopDong")}
              name="ngayHopDong"
            >
              <DateTimePicker
                placeholder={t("kho.quyetDinhThau.chonNgayHopDong")}
                format="DD/MM/YYYY"
                showTime={false}
              />
            </Form.Item>
            <Form.Item
              label={t("kho.quyetDinhThau.ngayHetHanHopDong")}
              name="ngayHetHanHopDong"
            >
              <DateTimePicker
                placeholder={t("kho.quyetDinhThau.chonNgayHetHanHopDong")}
                format="DD/MM/YYYY"
                showTime={false}
              />
            </Form.Item>
            <Form.Item label={t("common.ghiChu")} name="ghiChu">
              <TextArea
                style={{ minHeight: 100 }}
                className="input-option"
                placeholder={t("danhMuc.vuiLongNhapTitle", {
                  title: lowerFirst(t("common.ghiChu")),
                })}
              />
            </Form.Item>
            {dataEditDefault?.dichVu?.loaiDichVu === LOAI_DICH_VU.THUOC && (
              <>
                <Form.Item label={t("danhMuc.nhaNhapKhau")} name="nhaNhapKhau">
                  <Input className="input-option" disabled />
                </Form.Item>
                <Form.Item
                  label={t("danhMuc.dieuKienBaoQuan")}
                  name="dieuKienBaoQuan"
                >
                  <Input className="input-option" disabled />
                </Form.Item>
                <Form.Item
                  label=" "
                  name="chatLuongCamQuan"
                  valuePropName="checked"
                >
                  <Checkbox>{t("danhMuc.chatLuongCamQuan")}</Checkbox>
                </Form.Item>
              </>
            )}
            {state?.dataEdit && (
              <Form.Item label=" " name="active" valuePropName="checked">
                <Checkbox disabled={state?.disabled}>
                  {t("kho.coHieuLuc")}
                </Checkbox>
              </Form.Item>
            )}
          </FormWraper>
          {state?.vatTuKichCo && dataNHAP_KICH_CO_VAT_TU == "1" && (
            <TableChiTietKichCo />
          )}
          {state?.vatTuBo && (
            <TableChiTietBo
              selectedVatTuCon={selectedVatTuCon}
              listVatTuCon={state?.listVatTuCon}
              vatTuCha={state?.dataVatTuCha}
            />
          )}

          <ModalListDichVuTimKiem ref={refSearchHangHoa} />
        </EditWrapper>
      </TabPanel>
    </Main>
  );
};

export default FormChiTietThau;
