import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import moment from "moment";
import { useCache, useQueryAll, useSearchParams, useStore } from "hooks";
import nbDvKhoProvider from "data-access/kho/nb-dv-kho-provider";
import { query } from "redux-store/stores";
import { combineSort, cleanObjectUpdate } from "utils";
import { checkRole } from "lib-utils/role-utils";
import { ROLES } from "constants";
import { CACHE_KEY } from "constants/index";

export const useNbDvKho = () => {
  const [totalElements, setTotalElements] = useState(0);
  const authId = useStore("auth.auth.id", null);
  const [finishLoad, setFinishLoad] = useState(false);
  const [cacheData, setCacheData, loadFinishKhoaChiDinh] = useCache(
    authId,
    CACHE_KEY.CACHE_DATA_DS_NB_SU_DUNG_THUOC,
    null,
    false
  );

  const { searchParams, setSearchParams } = useSearchParams({
    defaultValue: {
      page: 0,
      size: 50,
      dataSortColumn: {},
      tuThoiGianThucHien: moment().startOf("day").format("YYYY-MM-DD 00:00:00"),
      denThoiGianThucHien: moment().endOf("day").format("YYYY-MM-DD 23:59:59"),
      dsKhoaChiDinhId: cacheData?.khoaChiDinh?.id,
    },
  });

  const hasRoleFilterAll = checkRole([
    ROLES["KHO"].LOC_TAT_CA_KHOA_CHI_DINH_DANH_SACH_NB_SU_DUNG_THUOC,
  ]);

  const [localSearchParams, setLocalSearchParams] = useState({});

  const { data: listKhoTheoTaiKhoan } = useQueryAll(
    query.kho.queryKhoTheoTaiKhoan
  );
  const { data: listKhoaTheoTaiKhoan, isFetched: isFetchedKhoaTheoTaiKhoan } =
    useQueryAll(query.khoa.queryKhoaTheoTaiKhoan);

  const { data: listAllKhoa } = useQueryAll(
    query.khoa.queryAllKhoa({
      enabled: hasRoleFilterAll,
    })
  );

  const onSortChange = (key, value) => {
    const newSort = cleanObjectUpdate(
      searchParams.dataSortColumn || {},
      key,
      value
    );
    setSearchParams({ sort: newSort, page: 0 });
  };

  const onChangeKhoaChiDinh = (data) => {
    setCacheData({
      ...cacheData,
      khoaChiDinh: data,
    });
    setSearchParams({
      page: 0,
    });
  };

  const onSearchChange = (params) => {
    setSearchParams({
      page: 0,
      ...params,
    });
  };

  const params = {
    page: searchParams.page,
    size: searchParams.size,
    ...searchParams,
    ...localSearchParams,
    sort: combineSort(searchParams.dataSortColumn || {}),
  };

  const { data: dataNbDvKho, isLoading: isLoadingDanhSach } = useQuery({
    queryKey: ["nb-dv-kho", params],
    queryFn: () => {
      const newParams = {
        ...params,
      };
      if (!params.dsKhoaChiDinhId) {
        // Fake result nếu không có quyền và tài khoản không được phân khoa
        if (!hasRoleFilterAll && listKhoaTheoTaiKhoan?.length === 0) {
          return Promise.resolve({
            data: [],
            totalElements: 0,
          });
        }

        // Nếu có quyền thì lọc tất cả
        // Không có quyền thì lấy tất cả khoa chi định theo tài khoản
        newParams.dsKhoaChiDinhId = hasRoleFilterAll
          ? null
          : listKhoaTheoTaiKhoan?.map((x) => x.id);
      }

      return nbDvKhoProvider.getNbDvKho(newParams);
    },
    enabled: isFetchedKhoaTheoTaiKhoan && !!finishLoad,
    onSuccess: (data) => {
      setTotalElements(data?.totalElements || 0);
    },
  });

  return {
    dataSortColumn: searchParams.dataSortColumn || {},
    totalElements,
    page: searchParams.page,
    size: searchParams.size,
    dataNbSuDungThuoc: dataNbDvKho?.data || [],
    isLoading: isLoadingDanhSach,
    searchParams,
    setSearchParams,
    onChangeKhoaChiDinh,
    onSortChange,
    onSearchChange,
    listKhoTheoTaiKhoan,
    listKhoaTheoTaiKhoan,
    isFetchedKhoaTheoTaiKhoan,
    localSearchParams,
    setLocalSearchParams,
    cacheData,
    setCacheData,
    finishLoad,
    setFinishLoad,
    loadFinishKhoaChiDinh,
    listAllKhoa,
    hasRoleFilterAll,
  };
};

export default useNbDvKho;
