import React, { useRef } from "react";
import { useTranslation } from "react-i18next";
import { BaseSearch } from "components";
import moment from "moment";

const TimKiem = ({
  searchParams,
  onSearchChange,
  listKhoTheoTaiKhoan,
  listKhoaTheoTaiKhoan,
  onChangeKhoaChiDinh,
  listAllKhoa,
  hasRoleFilterAll,
}) => {
  const { t } = useTranslation();
  const refOptions1 = useRef(null);
  const refOptions2 = useRef(null);

  const dataInput = [
    {
      widthInput: "250px",
      type: "select",
      placeholder: t("kho.chonKho"),
      keyValueInput: "dsKhoId",
      functionChangeInput: onSearchChange,
      title: t("kho.kho"),
      listSelect: listKhoTheoTaiKhoan,
    },
    {
      widthInput: "250px",
      type: "select",
      placeholder: t("baoCao.chonKhoaChiDinh"),
      keyValueInput: "dsKhoaChiDinhId",
      functionChangeInput: (_, option) => {
        onChangeKhoaChiDinh(option);
      },
      title: t("baoCao.khoaChiDinh"),
      listSelect: hasRoleFilterAll ? listAllKhoa : listKhoaTheoTaiKhoan,
    },
    {
      customWidth: "200px",
      type: "dateOptions",
      state: searchParams,
      functionChangeInput: (e) => {
        onSearchChange({
          tuThoiGianChiDinh: e.tuThoiGianChiDinh?.format("YYYY-MM-DD 00:00:00"),
          denThoiGianChiDinh: e.denThoiGianChiDinh?.format(
            "YYYY-MM-DD 23:59:59"
          ),
        });
      },
      title: t("cdha.thoiGianChiDinh"),
      placeholder: t("common.chonThoiGianChiDinh"),
      keyValueInput: ["tuThoiGianChiDinh", "denThoiGianChiDinh"],
      format: "DD/MM/YYYY",
      ref: refOptions1,
      renderValue: () => {
        if (
          !searchParams.tuThoiGianChiDinh ||
          !searchParams.denThoiGianChiDinh
        ) {
          return t("common.tatCa");
        }
        return `${moment(searchParams.tuThoiGianChiDinh).format(
          "DD/MM/YYYY"
        )} - ${moment(searchParams.denThoiGianChiDinh).format("DD/MM/YYYY")}`;
      },
    },
    {
      customWidth: "200px",
      type: "dateOptions",
      state: searchParams,
      functionChangeInput: (e) => {
        onSearchChange({
          tuThoiGianThucHien: e.tuThoiGianThucHien?.format(
            "YYYY-MM-DD 00:00:00"
          ),
          denThoiGianThucHien: e.denThoiGianThucHien?.format(
            "YYYY-MM-DD 23:59:59"
          ),
        });
      },
      title: t("common.ngayThucHien"),
      placeholder: t("common.chonThoiGianThucHien"),
      keyValueInput: ["tuThoiGianThucHien", "denThoiGianThucHien"],
      format: "DD/MM/YYYY",
      ref: refOptions2,
      renderValue: () => {
        if (
          !searchParams.tuThoiGianThucHien ||
          !searchParams.denThoiGianThucHien
        ) {
          return t("common.tatCa");
        }
        return `${moment(searchParams.tuThoiGianThucHien).format(
          "DD/MM/YYYY"
        )} - ${moment(searchParams.denThoiGianThucHien).format("DD/MM/YYYY")}`;
      },
    },
    {
      widthInput: "250px",
      placeholder: t("common.soPhieu"),
      keyValueInput: "soPhieuLinh",
      functionChangeInput: onSearchChange,
    },
  ];

  return <BaseSearch cacheData={searchParams} dataInput={dataInput} />;
};

export default TimKiem;
