import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { Page } from "components";
import TimKiem from "./components/TimKiem";
import DanhSach from "./components/DanhSach";
import useNbDvKho from "./hooks/useNbDvKho";
import ModalChonKhoaChiDinh from "./components/ModalChonKhoaChiDinh";
import { Main } from "./styled";
import { isEmpty, isNil } from "lodash";
import { checkRole } from "lib-utils/role-utils";
import { ROLES } from "constants";

const DanhSachNbSuDungThuoc = () => {
  const { t } = useTranslation();
  const refModalChonKhoaChiDinh = useRef(null);

  const {
    searchParams,
    setSearchParams,
    dataNbSuDungThuoc,
    onSortChange,
    isLoading,
    dataSortColumn,
    onSearchChange,
    totalElements,
    size,
    page,
    listKhoTheoTaiKhoan,
    listKhoaTheoTaiKhoan,
    localSearchParams,
    setLocalSearchParams,
    cacheData,
    loadFinishKhoaChiDinh,
    isFetchedKhoaTheoTaiKhoan,
    finishLoad,
    setFinishLoad,
    onChangeKhoaChiDinh,
    listAllKhoa,
    hasRoleFilterAll,
  } = useNbDvKho();

  const handleRoleFilterAll = () => {
    if (hasRoleFilterAll) {
      onChangeKhoaChiDinh(null);
      setFinishLoad(true);
      return true;
    }

    if (!listKhoaTheoTaiKhoan?.length) {
      onChangeKhoaChiDinh(null);
      setFinishLoad(true);
      return true;
    }

    return false;
  };

  const handleNoRole = () => {
    if (listKhoaTheoTaiKhoan?.length === 1) {
      onChangeKhoaChiDinh(listKhoaTheoTaiKhoan[0]);
      setFinishLoad(true);
    } else {
      refModalChonKhoaChiDinh.current?.show(
        {
          data: listKhoaTheoTaiKhoan,
        },
        ({ khoaChiDinh }) => {
          onChangeKhoaChiDinh(khoaChiDinh);
          setFinishLoad(true);
        }
      );
    }
  };

  const processInitial = () => {
    if (handleRoleFilterAll()) {
      return;
    }
    handleNoRole();
  };

  const restoreCachedDepartmentSelection = () => {
    const cachedKhoaChiDinh = (
      hasRoleFilterAll ? listAllKhoa : listKhoaTheoTaiKhoan
    ).find((item) => item.id === cacheData?.khoaChiDinh?.id);

    // Nếu khoa chi định có trong danh sách khoa chi định thì set lại khoa chi định
    if (cachedKhoaChiDinh) {
      setFinishLoad(true);
      onChangeKhoaChiDinh(cachedKhoaChiDinh);
    } else {
      // trường hợp khoa chi định không có trong danh sách khoa chi định thì bật popup chọn khoa chi định
      processInitial();
    }
  };

  const processCachedData = () => {
    // Nếu có chọn khoa chi định
    if (cacheData?.khoaChiDinh) {
      restoreCachedDepartmentSelection();
    } else {
      // trường hợp xoá khoa chi định từ bộ lọc thì set lại null
      onChangeKhoaChiDinh(null);
      setFinishLoad(true);
    }
  };

  useEffect(() => {
    if (loadFinishKhoaChiDinh && isFetchedKhoaTheoTaiKhoan && !finishLoad) {
      // Nếu đã thao tác chọn đã lưu cache
      if (!isNil(cacheData)) {
        processCachedData();
      } else {
        // trường hợp chưa thao tác chọn khoa chi định
        // bật popup chọn khoa chi định
        processInitial();
      }
    }
  }, [
    loadFinishKhoaChiDinh,
    cacheData,
    isFetchedKhoaTheoTaiKhoan,
    listKhoaTheoTaiKhoan,
  ]);

  return (
    <Page
      breadcrumb={[
        {
          title: t("kho.kho"),
          link: "/kho",
        },
        {
          title: t("kho.danhSachNbSuDungThuoc"),
          link: "/kho/danh-sach-nb-su-dung-thuoc",
        },
      ]}
      title={t("kho.danhSachNbSuDungThuoc")}
    >
      <Main>
        <TimKiem
          searchParams={searchParams}
          setSearchParams={setSearchParams}
          onSearchChange={onSearchChange}
          listKhoTheoTaiKhoan={listKhoTheoTaiKhoan}
          listKhoaTheoTaiKhoan={listKhoaTheoTaiKhoan}
          onChangeKhoaChiDinh={onChangeKhoaChiDinh}
          listAllKhoa={listAllKhoa}
          hasRoleFilterAll={hasRoleFilterAll}
        />
        <DanhSach
          dataSource={dataNbSuDungThuoc}
          onSortChange={onSortChange}
          isLoading={isLoading || !finishLoad}
          dataSortColumn={dataSortColumn}
          totalElements={totalElements}
          size={size}
          page={page}
          localSearchParams={localSearchParams}
          setLocalSearchParams={setLocalSearchParams}
        />
        <ModalChonKhoaChiDinh ref={refModalChonKhoaChiDinh} />
      </Main>
    </Page>
  );
};

export default DanhSachNbSuDungThuoc;
