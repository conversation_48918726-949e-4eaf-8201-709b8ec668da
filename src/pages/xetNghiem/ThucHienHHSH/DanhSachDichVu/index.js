import React, { useEffect, useMemo, useRef, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { groupBy, isEqual, cloneDeep, flatten, orderBy } from "lodash";
import moment from "moment";
import { message } from "antd";
import { CaretDownOutlined, CaretUpOutlined } from "@ant-design/icons";
import {
  TableWrapper,
  Select,
  Button,
  HeaderSearch,
  Checkbox,
  Tooltip,
  InputTimeout,
} from "components";
import {
  isNumber,
  TRANG_THAI,
  TRANG_THAI_FILTER,
  LOAI_KET_QUA,
} from "pages/xetNghiem/configs";
import { checkRole } from "lib-utils/role-utils";
import {
  ENUM,
  HIEU_LUC,
  HOTKEY,
  LOAI_IN,
  ROLES,
  THIET_LAP_CHUNG,
  LOAI_DICH_VU,
} from "constants/index";
import printProvider from "data-access/print-provider";
import {
  useConfirm,
  useEnum,
  useLoading,
  usePrevious,
  useThietLap,
} from "hooks";
import { SVG } from "assets";
import { isArray, locPhieuLisPacs, openInNewTab } from "utils/index";
import { Main } from "./styled";
import useViTriChamCong, {
  KEYS_VI_TRI_CHAM_CONG_XET_NGHIEM,
} from "pages/khamBenh/ChiDinhDichVu/ModalChiDinhDichVu/useViTriChamCong";
import useThietLapGiaoDien from "pages/khamBenh/ChiDinhDichVu/ModalChiDinhDichVu/useThietLapGiaoDien";
import { usePhongLayMau, useXetNghiem } from "pages/xetNghiem/contexts";
import { toSafePromise } from "lib-utils";

const { Setting } = TableWrapper;

const DanhSachDichVu = ({ layerId, onShowInfo }) => {
  const { showConfirm, showAsyncConfirm } = useConfirm();
  const { nbDotDieuTriId, soPhieu } = useParams();
  const { showLoading, hideLoading } = useLoading();
  const { t } = useTranslation();

  const { DATA_TEN_HIEN_THI } = useThietLapGiaoDien();

  const [
    renderDataListVTCC,
    renderTitleVTCC,
    checkTonTaiThietLap,
    getTenField,
  ] = useViTriChamCong({
    show: true,
  });

  const refInputMaDv = useRef(null);
  const refSettings = useRef(null);
  const [state, _setState] = useState({
    isCheckedAll: false,
    actionXNSuccess: false,
    dataTable: [],
    listKeys: [],
    selectedRowKeys: [], // [{soPhieu}-{nhomDichVuCap2Id}-{dichVuId}]
    danhSachDichVu: [],
    dsPhuCapPtTtChiTiet: {},
    error: false,
  });
  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };
  const [listKetQuaXetNghiem] = useEnum(ENUM.KET_QUA_XET_NGHIEM);
  const [listTrangThaiDichVu] = useEnum(ENUM.TRANG_THAI_DICH_VU);
  const [listPhanLoaiKetQuaXetNghiem] = useEnum(
    ENUM.PHAN_LOAI_KET_QUA_XET_NGHIEM
  );
  const [DICH_VU_VI_SINH_KSNK] = useThietLap(
    THIET_LAP_CHUNG.DICH_VU_VI_SINH_KSNK
  );
  const [dataKIEM_TRA_THUC_HIEN_DICH_VU] = useThietLap(THIET_LAP_CHUNG.KIEM_TRA_THUC_HIEN_DICH_VU, "false");

  const { listServices } = useSelector((state) => state.xnHuyetHocSinhHoa);
  const { listAllMaMay } = useSelector((state) => state.maMay);
  const { listAllDonViTinh } = useSelector((state) => state.donViTinh);

  const { xetNghiemState, dsTrangThaiNb: dsTrangThai } = useXetNghiem();
  const { data: listPhongTheoTaiKhoan, isFetched } = usePhongLayMau();
  const { phongThucHienId, tuThoiGian, denThoiGian, finishedLoadState } =
    xetNghiemState;
  const preDsTrangThai = usePrevious(dsTrangThai);

  const { danhSachDichVu } = state;

  const {
    xnHuyetHocSinhHoa: {
      onSearch,
      capNhatKetQua,
      xacNhanKetQua,
      duyetKetQua,
      xacNhanTiepNhanMau,
      onChangeInputSearch,
    },
    layMauXN: { xacNhanLayMau },
    maMay: { getListAllMaMay },
    donViTinh: { getListAllDonViTinh },
    phimTat: { onRegisterHotkey },
    nbXetNghiem: { getKetQuaXNPdf },
    pttt: { getDsPhuCapPTTTChiTiet },
    nbDichVuKyThuat: { xacNhanTrungNguoiThucHien }
  } = useDispatch();

  const dsDvViSinhKSNK = useMemo(() => {
    let data = [];
    if (typeof DICH_VU_VI_SINH_KSNK === "string") {
      data = DICH_VU_VI_SINH_KSNK.split(",");
    }

    return data;
  }, [DICH_VU_VI_SINH_KSNK]);

  useEffect(() => {
    onRegisterHotkey({
      layerId: layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.F3,
          onEvent: () => {
            refInputMaDv.current && refInputMaDv.current.focus();
          },
        },
      ],
    });
    getListAllMaMay({ page: "", size: "", active: true });
    getListAllDonViTinh({ page: "", size: "", active: true });
  }, []);

  useEffect(() => {
    const newState = {
      selectedRowKeys: [],
      isCheckedAll: false,
    };
    if (nbDotDieuTriId && isFetched && finishedLoadState) {
      const param = {};
      //nếu không thay đổi lọc theo trạng thái thì giữ nguyên
      if (!isEqual(preDsTrangThai, dsTrangThai)) {
        param.trangThai = "";
        param.dsTrangThai = getDefaultTrangThai();
        newState.trangThai = "";
      }

      onSearch({
        dataSearch: {
          nbDotDieuTriId,
          dsMaThietLapNhomDvCap2: "NHOM_SH_HH",
          dsPhongThucHienId: phongThucHienId
            ? phongThucHienId
            : listPhongTheoTaiKhoan.map((i) => i.id),
          thoiGianThucHienTu: tuThoiGian
            ? moment(tuThoiGian).format("YYYY-MM-DD 00:00:00")
            : "",
          thoiGianThucHienDen: denThoiGian
            ? moment(denThoiGian).format("YYYY-MM-DD 23:59:59")
            : "",
          ...param,
        },
      });
    }
    setState(newState);
  }, [
    nbDotDieuTriId,
    dsTrangThai,
    phongThucHienId,
    tuThoiGian,
    denThoiGian,
    listPhongTheoTaiKhoan,
    isFetched,
    finishedLoadState,
  ]);

  const listTrangThai = useMemo(() => {
    //trả về danh sách trạng thái theo điều kiện lọc bên ds người bệnh
    return listTrangThaiDichVu.filter((item) =>
      (dsTrangThai.length ? dsTrangThai : TRANG_THAI_FILTER).includes(item.id)
    );
  }, [dsTrangThai, listTrangThaiDichVu]);

  const getDefaultTrangThai = () => {
    return listTrangThai.filter((item) => item.id).map((item) => item.id);
  };

  useEffect(() => {
    if (!nbDotDieuTriId) return;
    setState({ danhSachDichVu: cloneDeep(listServices) });

    return () => {
      setState({ danhSachDichVu: [] });
    };
  }, [listServices, nbDotDieuTriId]);

  const { listKeys, dataTable } = useMemo(() => {
    let listAllKeys = []; // Id record format: {soPhieu}-{nhomDichVuCap2Id}-{dichVuId}
    let dataTable = [];
    const groupBySoPhieu = groupBy(danhSachDichVu, "soPhieu");
    let index = 0;
    Object.keys(groupBySoPhieu).forEach((item) => {
      const dataSoPhieu = groupBySoPhieu[item];
      //nếu đang tìm theo số phiếu thì chỉ trả về số phiếu đang tìm kiếm
      if (soPhieu && dataSoPhieu[0].soPhieu != soPhieu) return;
      const groupByDichVu = groupBy(dataSoPhieu, "tenNhomDichVuCap2");
      let thoiGianThucHien =
        isArray(dataSoPhieu, true) && dataSoPhieu[0].thoiGianThucHien
          ? moment(dataSoPhieu[0].thoiGianThucHien).format("DD/MM/YYYY")
          : "";
      let obj = {
        type: "soPhieu",
        stt: `${t("xetNghiem.soPhieu")}: ${item}`,
        id: item,
        dataIndex: index++,
      };
      if (thoiGianThucHien) {
        obj.stt += ` - ${thoiGianThucHien}`;
        obj.colSpan = "100%";
      }
      dataTable.push(obj);
      listAllKeys.push(item);

      Object.keys(groupByDichVu).forEach((d) => {
        let dichVu = groupByDichVu[d];
        //dataIndex sử dụng dể đánh tabIndex
        dataTable.push({
          type: "dichVuCap2",
          stt: d,
          id: `${item}-${groupByDichVu[d]?.[0]?.nhomDichVuCap2Id}`,
          dataIndex: index++,
        });
        dichVu = dichVu.map((v, idx) => {
          const children = orderBy(v.dsChiSoCon || [], ["stt"], ["asc"]).map(
            (chiso) => ({
              ...chiso,
              maDichVu: chiso.maChiSoCon,
              tenDichVu: chiso.tenChiSoCon,
              isChiSoCon: true,
              trangThai: v.trangThai,
            })
          );
          const item2 = {
            ...v,
            children,
            stt: idx + 1,
            id: `${item}-${v.nhomDichVuCap2Id}-${v.id}`,
            recordId: v.id,
            donVi: listAllDonViTinh?.find((item) => item?.id == v?.donViTinhId)
              ?.ten,
            dataIndex: index++,
          };

          item2.children = item2.children.map((item) => {
            item.parentId = item2.id;
            item.dataIndex = index++;
            return item;
          });
          listAllKeys.push(`${item}-${v.nhomDichVuCap2Id}-${v.id}`);
          return item2;
        });

        listAllKeys.push(`${item}-${groupByDichVu[d]?.[0]?.nhomDichVuCap2Id}`);
        dataTable = [...dataTable, ...dichVu];
      });
    });
    return {
      listKeys: listAllKeys,
      dataTable,
    };
  }, [danhSachDichVu, soPhieu, tuThoiGian, denThoiGian]);

  const formatDataIds = (data) => {
    const listServiceIds = [];
    data.forEach((item) => {
      if (
        item.indexOf("-") > 0 &&
        item.indexOf("-") !== item.lastIndexOf("-")
      ) {
        const id = item.slice(item.lastIndexOf("-") + 1, item.length);
        listServiceIds.push(parseInt(id));
      }
    });
    return listServiceIds;
  };
  const selectedRowKeys = useMemo(() => {
    return state.selectedRowKeys || [];
  }, [state.selectedRowKeys]);

  const selectedBtn = useMemo(() => {
    const data = formatDataIds(selectedRowKeys);
    const listStatus = dataTable
      .filter((d) => d.nbDotDieuTriId && data.includes(d.recordId))
      .map((u) => u.trangThai);
    return [...new Set(listStatus)];
  }, [selectedRowKeys, dataTable]);

  const selectedServiceHasGuiLis = useMemo(() => {
    const data = formatDataIds(selectedRowKeys);
    return dataTable
      .filter((d) => d.nbDotDieuTriId && data.includes(d.recordId))
      .map((u) => u.guiLis);
  }, [selectedRowKeys, dataTable]);

  useEffect(() => {
    setState({
      selectedRowKeys: [],
    });
  }, [danhSachDichVu]);

  const onHuyMau = (status) => async () => {
    try {
      showLoading();
      const data = formatDataIds(selectedRowKeys);
      if (!data.length) {
        message.error(t("xetNghiem.moiChonDichVu"));
        return;
      }
      await xacNhanLayMau({ data, status });
      onChangeInputSearch({});
    } catch (error) {
    } finally {
      hideLoading();
    }
  };

  const onTiepNhanMau = (status) => async () => {
    try {
      showLoading();
      const data = formatDataIds(selectedRowKeys);
      if (!data.length) {
        message.error(t("xetNghiem.moiChonDichVu"));
        return;
      }
      await xacNhanTiepNhanMau({ data, status });
    } catch (error) {
      if (error.code == 7609) {
        showConfirm({
          title: t("common.thongBao"),
          content: error.message,
          okText: t("common.dong"),
          classNameOkText: "button-closel",
          showBtnOk: true,
        });
      }
    } finally {
      hideLoading();
    }
  };

  const onLuuKetQua = async () => {
    try {
      showLoading();
      const data = dataTable
        .filter(
          (d) => d.nbDotDieuTriId && state.selectedRowKeys?.includes(d.id)
        )
        .map((item) => {
          const id = item.id.slice(
            item.id.lastIndexOf("-") + 1,
            item.id.length
          );
          return {
            id: parseInt(id),
            ketQua: item.ketQua || "",
            banLuan: item.banLuan || "",
            ketLuan: item.ketLuan || "",
            maMayId: item.maMayId || "",

            nguoiThucHien2Id: item.nguoiThucHien2Id ?? null,
            phuThucHien1Id: item.phuThucHien1Id ?? null,
            phuThucHien2Id: item.phuThucHien2Id ?? null,
            phuThucHien3Id: item.phuThucHien3Id ?? null,
            thanhVienKhacId: item.thanhVienKhacId ?? null,

            viKhuan: item.viKhuan,
            canhBao: item.canhBao || "",
            dsChiSoCon: item.dsChiSoCon.map((item) => {
              item.phanLoaiKetQua = null;
              return item;
            }),
          };
        });

      if (!data.length) {
        message.error(t("xetNghiem.moiChonDichVu"));
        return;
      }
      await capNhatKetQua({ data });
    } catch (error) {
    } finally {
      hideLoading();
    }
  };

  const onCoKetQua = (status) => async () => {
    try {
      showLoading();
      await onLuuKetQua();
      const data = formatDataIds(selectedRowKeys);
      if (!data.length) {
        message.error(t("xetNghiem.moiChonDichVu"));
        return;
      }
      let dataXacNhan = [];
      if (dataKIEM_TRA_THUC_HIEN_DICH_VU?.eval()) {

        for (let i = 0; i < data.length; i++) {
          const dichVu = listServices.find((d) => d.id == data[i]);
          const [err, res] = await toSafePromise(xacNhanTrungNguoiThucHien({
            dichVuId: data[i],
            nguoiThucHienId: dichVu.nguoiThucHienId,
            thoiGianThucHien: dichVu.thoiGianThucHien,
            thoiGianCoKetQua: dichVu.thoiGianCoKetQua || moment().format("YYYY-MM-DD HH:mm:ss"),
            duKienKetQua: dichVu.duKienKetQua,
            showLoading,
            showAsyncConfirm,
            hideLoading
          }));
          if (res)
            dataXacNhan.push(data[i]);
        }
      } else
        dataXacNhan = data;
        
      if (!dataXacNhan.length)
        return;
      showLoading();
      await xacNhanKetQua({ data: dataXacNhan, status });
    } catch (error) {
    } finally {
      hideLoading();
    }
  };

  const onDuyetKetQua = (isConfirm) => async () => {
    try {
      showLoading();
      const data = formatDataIds(selectedRowKeys);
      if (!data.length) {
        message.error(t("xetNghiem.moiChonDichVu"));
        return;
      }
      await duyetKetQua({ data, status: isConfirm });
    } catch (error) {
    } finally {
      hideLoading();
    }
  };

  const onSearchInput = (key, requiredNumber) => (e) => {
    if (!nbDotDieuTriId) return;
    let value = "";
    if (e?.target) {
      if (e.target.hasOwnProperty("checked")) value = e.target.checked;
      else value = e.target.value;
    } else if (e?._d) value = e.format("YYYY-MM-DD");
    else value = e;
    if (requiredNumber && !isNumber(value) && value) return;
    if (key == "trangThai") {
      setState({ trangThai: value });
      const param = {};
      if (!value) {
        param.trangThai = "";
        param.dsTrangThai = getDefaultTrangThai();
      } else {
        param.trangThai = value;
        param.dsTrangThai = [];
      }
      onChangeInputSearch(param);
    } else
      onChangeInputSearch({
        [key]: value,
      });
  };

  const onViewDetail = (data) => () => {
    const findIndex = danhSachDichVu.findIndex(
      (x) => data.id == `${x?.soPhieu}-${x?.nhomDichVuCap2Id}-${x.id}`
    );
    onShowInfo && onShowInfo(data, findIndex);
    setState({
      dataTable: null,
    });
  };
  // nbDvXetNghiemId
  const onChangeEditBox = (type, index, record) => (e) => {
    if (type == "ketQua") {
      const value = e.target ? e.target?.value : e;
      if (`${value}`.length > 1500) {
        message.error(t("xetNghiem.vuiLongNhapKetQuaKhongQua1500KyTu"));
        return;
      }
    }
    let tableChildren = dataTable.find(
      (x) => x.recordId == record?.nbDvXetNghiemId
    );
    let idx = dataTable.indexOf(tableChildren);
    let value = "";
    if (dataTable) {
      if (e?.target) {
        if (type == "viKhuan") {
          value = e.target.checked;
        } else {
          value = e.target.value;
        }
      } else {
        value = e ? String(e) : "";
      }
      if (idx) {
        dataTable[idx].dsChiSoCon[index][type] = value;
      } else {
        dataTable[index][type] = value;
      }

      //xử lý lưu kết quả luôn khi check nhiễm khuẩn
      // if (type == "viKhuan") {
      //   onLuuKetQuaNhiemKhuan(dataTable[index]);
      // }
    }
  };
  const renderKetQua = (record, value, type, index) => {
    const isEdittable = TRANG_THAI["XAC_NHAN_KET_QUA"].includes(
      record.trangThai
    );
    switch (record.loaiKetQua) {
      case LOAI_KET_QUA.SO:
        return isEdittable ? (
          <InputTimeout
            onClick={onClickEdit}
            tabIndex={getTabIndex(1000, record.dataIndex)}
            className={showClassByInput(record?.phanLoaiKetQua)}
            value={value}
            type="number"
            onChange={onChangeEditBox(type, index, record)}
            disabled={
              !(
                selectedRowKeys.includes(record.id) ||
                selectedRowKeys.includes(record.parentId)
              )
            }
          />
        ) : (
          <div className={showClassByInput(record?.phanLoaiKetQua)}>
            {value}
          </div>
        );
      case LOAI_KET_QUA.CHON_GIA_TRI:
        return isEdittable ? (
          <Select
            tabIndex={getTabIndex(1000, record.dataIndex)}
            className={showClassByInput(record?.phanLoaiKetQua)}
            defaultValue={value ? parseInt(value) : ""}
            onChange={onChangeEditBox(type, index, record)}
            placeholder={t("xetNghiem.chonKetQua")}
            data={listKetQuaXetNghiem}
            allowClear={false}
            disabled={
              !(
                selectedRowKeys.includes(record.id) ||
                selectedRowKeys.includes(record.parentId)
              )
            }
          />
        ) : (
          listKetQuaXetNghiem.find((d) => d.id === parseInt(value))?.ten
        );
      default:
        return isEdittable ? (
          <InputTimeout
            onClick={onClickEdit}
            tabIndex={getTabIndex(1000, record.dataIndex)}
            className={showClassByInput(record?.phanLoaiKetQua)}
            value={value}
            onChange={onChangeEditBox(type, index, record)}
            disabled={
              !(
                selectedRowKeys.includes(record.id) ||
                selectedRowKeys.includes(record.parentId)
              )
            }
          />
        ) : (
          value
        );
    }
  };
  const renderViTriChamCong = (record, value, type, index) => {
    const isEdittable = TRANG_THAI["XAC_NHAN_KET_QUA"].includes(
      record.trangThai
    );

    if (["soPhieu", "dichVuCap2"].includes(record.type)) {
      return null;
    } else if (
      checkTonTaiThietLap(
        type,
        state.dsPhuCapPtTtChiTiet?.[record.id],
        LOAI_DICH_VU.XET_NGHIEM
      )
    ) {
      if (
        isEdittable &&
        (selectedRowKeys.includes(record.id) ||
          selectedRowKeys.includes(record.parentId))
      ) {
        const fieldKey =
          type.indexOf("phuPtv") == 0
            ? type.replace("phuPtv", "phuThucHien")
            : type;

        return (
          <Select
            value={record[fieldKey]}
            valueNumber={true}
            data={renderDataListVTCC(type)}
            onChange={onChangeEditBox(fieldKey, index, record)}
          />
        );
      } else {
        return value;
      }
    } else {
      return null;
    }
  };

  const renderExclamation = (type) => (value, row, index) => {
    if (row.nbDvXetNghiemId) return null;
    const obj = {
      children: (
        <Tooltip title={t("xetNghiem.chiTietDichVu")} placement="bottom">
          <SVG.IcInfoFill onClick={onViewDetail(row, index - 2)} />
        </Tooltip>
      ),
      props: {},
    };
    if (["soPhieu", "dichVuCap2"].includes(row.type)) {
      obj.children = null;
      obj.props.colSpan = 0;
    }
    return obj;
  };

  const getTabIndex = (col, index) => {
    return col + index;
  };
  const onClickEdit = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const renderContent = (type, fieldKey) => (value, row, index) => {
    let children = null;
    switch (type) {
      case "viTriChamCong":
        children = renderViTriChamCong(row, value, fieldKey, index);
        break;
      case "viKhuan":
        if (!row.isChiSoCon && dsDvViSinhKSNK.includes(row.maDichVu)) {
          children = (
            <Checkbox
              checked={value}
              disabled={
                ![...TRANG_THAI["XAC_NHAN_KET_QUA"]].includes(row.trangThai)
              }
              onChange={onChangeEditBox(type, index, row)}
            />
          );
        }
        break;
      case "thoiGianThucHien":
        children = moment(value).format("DD/MM/YYYY");
        break;
      case "ketQua":
        children = renderKetQua(row, value, type, index);
        break;
      case "trangThai":
        const currentItem = listTrangThaiDichVu.find((d) => d.id === value);
        children = currentItem?.ten;
        break;
      case "maMayId":
        const item = listAllMaMay?.find((x) => x.id == value);
        if (TRANG_THAI["XAC_NHAN_KET_QUA"].includes(row.trangThai)) {
          children = row.isChiSoCon ? (
            item?.ten
          ) : (
            <Select
              onClick={onClickEdit}
              tabIndex={getTabIndex(4000, row.dataIndex)}
              data={listAllMaMay}
              defaultValue={item?.ten}
              onChange={onChangeEditBox(type, index, row)}
              disabled={
                !(
                  selectedRowKeys.includes(row.id) ||
                  selectedRowKeys.includes(row.parentId)
                )
              }
            ></Select>
          );
        } else {
          children = item?.ten;
        }
        break;
      case "ecrCl":
        children = value || "";
        break;
      case "egfr":
        children = value || "";
        break;
      case "phuongPhap":
        children = value || "";
        break;
      case "canhBao":
        if (
          selectedRowKeys.includes(row.id) ||
          selectedRowKeys.includes(row.parentId)
        ) {
          children = (
            <InputTimeout
              onClick={onClickEdit}
              tabIndex={getTabIndex(3000, row.dataIndex)}
              value={value}
              rows={2}
              onChange={onChangeEditBox(type, index, row)}
              disabled={
                !flatten([
                  TRANG_THAI["CO_KET_QUA"],
                  TRANG_THAI["DUYET_KET_QUA"],
                ]).includes(row.trangThai)
              }
            />
          );
        } else children = value || "";
        break;
      default:
        if (
          (("banLuan" === type && !row.isChiSoCon) || "ketLuan" === type) &&
          TRANG_THAI["XAC_NHAN_KET_QUA"].includes(row.trangThai)
        ) {
          children = (
            <InputTimeout
              onClick={onClickEdit}
              tabIndex={getTabIndex(
                "ketLuan" === type ? 2000 : 3000,
                row.dataIndex
              )}
              value={value}
              rows={2}
              onChange={onChangeEditBox(type, index, row)}
              disabled={
                !(
                  selectedRowKeys.includes(row.id) ||
                  selectedRowKeys.includes(row.parentId)
                )
              }
            />
          );
        } else children = value;
    }

    const obj = {
      children,
      props: {},
    };

    return obj;
  };

  const onCheckAll = (e) => {
    setState({
      selectedRowKeys: e.target?.checked ? listKeys : [],
      isCheckedAll: e.target?.checked,
    });
  };

  const getThongTinPhuCap = async (selectedKeys) => {
    const data = dataTable.filter(
      (item) =>
        selectedKeys.includes(item.id) &&
        !state.dsPhuCapPtTtChiTiet?.hasOwnProperty(item.id) &&
        !["soPhieu", "dichVuCap2"].includes(item.type) &&
        (item.phanLoaiPtTt || item.phanLoaiPtTt == 0)
    );

    let _dsPhuCapPtTtChiTiet = {};

    if (data.length > 0) {
      const resAll = await Promise.all(
        data.map(async (item) => {
          return await getDsPhuCapPTTTChiTiet({
            dichVuId: item.dichVuId,
            nhomDichVuCap2Id: item.nhomDichVuCap2Id,
            nhomDichVuCap1Id: item.nhomDichVuCap1Id,
            phanLoai: item.phanLoaiPtTt,
            ngay: moment().format("YYYY-MM-DD"),
          });
        })
      );

      data.forEach((item, index) => {
        _dsPhuCapPtTtChiTiet[item.id] =
          resAll[index]?.dsPhuCapPtTtChiTiet || [];
      });
    }

    setState({
      selectedRowKeys: selectedKeys,
      dsPhuCapPtTtChiTiet: {
        ...state.dsPhuCapPtTtChiTiet,
        ..._dsPhuCapPtTtChiTiet,
      },
    });
  };

  const onSelectChange = (newSelectedRowKeys) => {
    let updatedSelectedKeys = newSelectedRowKeys;

    const isAdding = selectedRowKeys.length < newSelectedRowKeys.length;
    const selectedKey = isAdding
      ? newSelectedRowKeys
        .filter((x) => !selectedRowKeys.includes(x))
        .toString()
      : selectedRowKeys
        .filter((x) => !newSelectedRowKeys.includes(x))
        .toString();
    if (isAdding) {
      if (selectedKey.indexOf("-") < 0) {
        // select all sophieu => add all dich vu + nhom dich vu thuoc so phieu
        const newList = listKeys.filter(
          (item) => item.includes(`${selectedKey}-`) || item === selectedKey
        );
        updatedSelectedKeys = [...updatedSelectedKeys, ...newList];
        updatedSelectedKeys = [...new Set(updatedSelectedKeys)];
      } else if (selectedKey.indexOf("-") === selectedKey.lastIndexOf("-")) {
        const soPhieu = selectedKey.slice(0, selectedKey.indexOf("-"));
        const newList = listKeys.filter((item) =>
          item.includes(`${selectedKey}-`)
        );

        updatedSelectedKeys = [...updatedSelectedKeys, ...newList];
        updatedSelectedKeys = [...new Set(updatedSelectedKeys)];

        const itemNotSelectedInsoPhieu = listKeys.filter((item) => {
          if (
            item.indexOf("-") !== item.lastIndexOf("-") &&
            !updatedSelectedKeys.includes(item) &&
            item.slice(0, item.indexOf("-")) === soPhieu
          )
            return true;
          return false;
        });
        if (!itemNotSelectedInsoPhieu.length) {
          updatedSelectedKeys = [...updatedSelectedKeys, soPhieu];
        }
      } else {
        const group = selectedKey.slice(0, selectedKey.lastIndexOf("-"));
        const currentItemsInGroup = selectedRowKeys.filter((item) =>
          item.includes(group)
        ).length;
        const itemsInGroup = listKeys.filter((item) =>
          item.includes(group)
        ).length;
        if (currentItemsInGroup + 2 === itemsInGroup) {
          updatedSelectedKeys = [...updatedSelectedKeys, group];
        }
      }
    } else {
      if (selectedKey.indexOf("-") < 0) {
        // select all sophieu => remove all dich vu + nhom dich vu thuoc so phieu
        updatedSelectedKeys = selectedRowKeys.filter(
          (item) => !item.includes(`${selectedKey}-`) && item !== selectedKey
        );
      } else if (selectedKey.indexOf("-") === selectedKey.lastIndexOf("-")) {
        // select tenNhomDichVuCap2 => remove all dich vu thuoc nhom dich vu cap 2
        updatedSelectedKeys = selectedRowKeys.filter(
          (item) => !item.includes(`${selectedKey}-`) && item !== selectedKey
        );
        const soPhieu = selectedKey.slice(0, selectedKey.indexOf("-"));
        const otherItemInsoPhieu = updatedSelectedKeys.filter((item) =>
          item.includes(`${soPhieu}-`)
        );
        if (!otherItemInsoPhieu.length) {
          // Remove all sophieu
          updatedSelectedKeys = updatedSelectedKeys.filter(
            (item) => item !== soPhieu
          );
        }
      } else {
        const soPhieuNhomdichVu = selectedKey.slice(
          0,
          selectedKey.lastIndexOf("-")
        );
        const otherItemInDichVu = selectedRowKeys.filter(
          (item) =>
            item.includes(`${soPhieuNhomdichVu}-`) && item !== selectedKey
        );
        const soPhieu = selectedKey.slice(0, selectedKey.indexOf("-"));
        if (!otherItemInDichVu.length) {
          // Remove all dichvu
          updatedSelectedKeys = selectedRowKeys.filter(
            (item) =>
              !item.includes(`${soPhieuNhomdichVu}-`) &&
              item !== soPhieuNhomdichVu
          );
        }
        const otherItemInsoPhieu = updatedSelectedKeys.filter((item) =>
          item.includes(`${soPhieu}-`)
        );
        if (!otherItemInsoPhieu.length) {
          // Remove all sophieu
          updatedSelectedKeys = updatedSelectedKeys.filter(
            (item) => item !== soPhieu
          );
        }
      }
    }

    getThongTinPhuCap(updatedSelectedKeys);
  };

  const rowSelection = {
    columnTitle: (
      <HeaderSearch
        title={<Checkbox onChange={onCheckAll} checked={state.isCheckedAll} />}
      />
    ),
    columnWidth: 50,
    onChange: onSelectChange,
    selectedRowKeys: selectedRowKeys,
  };

  const showClassByInput = (item) => {
    let strClass = "input-center";
    if (item == 0) {
      strClass = "input-center";
    }
    if (item == 10) {
      strClass = "input-left";
    }
    if (item == 20 || item == 30) {
      strClass = "input-right";
    }
    return strClass;
  };

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: "40px",
      dataIndex: "stt",
      key: "stt",
      align: "center",
      ignore: true,
      render: (text, row, index) => {
        if (!["soPhieu", "dichVuCap2"].includes(row.type)) {
          return <span>{text}</span>;
        }
        return {
          children: <span>{text}</span>,
          props: {
            colSpan: row.colSpan || 2,
            style: {
              textAlign: "left",
              fontSize: 18,
              fontWeight: "bold",
              ...(row.type === "soPhieu"
                ? { background: "#054ab9", color: "#ffffff" }
                : {}),
            },
          },
        };
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("xetNghiem.maDV")}
          search={
            <InputTimeout
              ref={refInputMaDv}
              placeholder={t("xetNghiem.timMaThietLap")}
              onChange={onSearchInput("maDichVu")}
            />
          }
        />
      ),
      align: "left",
      width: "150px",
      dataIndex: "maDichVu",
      key: "maDichVu",
      show: true,
      i18Name: "xetNghiem.maDV",
      render: renderContent("maDichVu"),
    },
    {
      title: (
        <HeaderSearch
          title={t("common.tenDichVu")}
          search={
            <InputTimeout
              placeholder={t("xetNghiem.timTheoGiaTri")}
              onChange={onSearchInput("tenDichVu")}
            />
          }
        />
      ),
      width: "250px",
      align: "left",
      dataIndex: "tenDichVu",
      key: "tenDichVu",
      show: true,
      i18Name: "common.tenDichVu",
      render: renderContent("tenDichVu"),
    },
    {
      title: (
        <HeaderSearch
          title={t("common.ketQua")}
          search={
            <InputTimeout
              placeholder={t("xetNghiem.timTheoMoTa")}
              onChange={onSearchInput("ketQua", true)}
            />
          }
        />
      ),
      width: "140px",
      textAlign: "left",
      dataIndex: "ketQua",
      key: "ketQua",
      show: true,
      i18Name: "common.ketQua",
      render: renderContent("ketQua"),
    },
    {
      title: (
        <HeaderSearch
          title={t("xetNghiem.donVi")}
          search={
            <InputTimeout
              placeholder={t("xetNghiem.timTheoMoTa")}
              onChange={onSearchInput("donVi")}
            />
          }
        />
      ),
      width: "100px",
      align: "left",
      dataIndex: "donVi",
      key: "donVi",
      show: true,
      i18Name: "xetNghiem.donVi",
      render: renderContent("donVi"),
    },
    {
      title: (
        <HeaderSearch
          title={t("xetNghiem.nhiemKhuan")}
          searchSelect={
            <Select
              placeholder={t("xetNghiem.nhiemKhuan")}
              data={HIEU_LUC}
              onChange={onSearchInput("viKhuan")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: "100px",
      align: "center",
      dataIndex: "viKhuan",
      key: "viKhuan",
      show: true,
      i18Name: "xetNghiem.nhiemKhuan",
      render: renderContent("viKhuan"),
    },
    {
      title: (
        <HeaderSearch
          title={t("xetNghiem.giaTriThamChieu")}
          search={
            <InputTimeout
              placeholder={t("xetNghiem.timTheoMoTa")}
              onChange={onSearchInput("ketQuaThamChieu", true)}
            />
          }
        />
      ),
      width: "140px",
      textAlign: "left",
      dataIndex: "ketQuaThamChieu",
      key: "ketQuaThamChieu",
      show: true,
      i18Name: "xetNghiem.giaTriThamChieu",
      render: renderContent("ketQuaThamChieu"),
    },
    {
      title: (
        <HeaderSearch
          search={
            <InputTimeout
              placeholder={t("xetNghiem.timTheoChiSoThap")}
              onChange={onSearchInput("chiSoThap", true)}
            />
          }
          title={t("xetNghiem.chiSoThap")}
        />
      ),
      width: "120px",
      dataIndex: "chiSoThap",
      key: "chiSoThap",
      align: "right",
      show: true,
      i18Name: "xetNghiem.timTheoChiSoThap",
      render: renderContent("chiSoThap"),
    },
    {
      title: (
        <HeaderSearch
          search={
            <InputTimeout
              placeholder={t("xetNghiem.timTheoChiSoCao")}
              onChange={onSearchInput("chiSoCao", true)}
            />
          }
          title={t("xetNghiem.chiSoCao")}
        />
      ),
      width: "120px",
      dataIndex: "chiSoCao",
      key: "chiSoCao",
      align: "right",
      show: true,
      i18Name: "xetNghiem.timTheoChiSoCao",
      render: renderContent("guiLis"),
    },
    {
      title: <HeaderSearch title={t("xetNghiem.danhGiaKetQua")} />,
      width: "120px",
      align: "center",
      dataIndex: "phanLoaiKetQua",
      key: "phanLoaiKetQua",
      show: true,
      i18Name: "xetNghiem.danhGiaKetQua",
      render: (item, data) => {
        return listPhanLoaiKetQuaXetNghiem.find((x) => x.id == item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.ketLuan")}
          search={
            <InputTimeout
              placeholder={t("xetNghiem.timTheoMoTa")}
              onChange={onSearchInput("ketLuan")}
            />
          }
        />
      ),
      width: "200px",
      align: "left",
      dataIndex: "ketLuan",
      key: "ketLuan",
      show: true,
      i18Name: "common.ketLuan",
      render: renderContent("ketLuan"),
    },
    {
      title: (
        <HeaderSearch
          title={t("xetNghiem.banLuan")}
          search={
            <InputTimeout
              placeholder={t("xetNghiem.timTheoMoTa")}
              onChange={onSearchInput("banLuan")}
            />
          }
        />
      ),
      width: "200px",
      align: "left",
      dataIndex: "banLuan",
      key: "banLuan",
      show: true,
      i18Name: "xetNghiem.banLuan",
      render: renderContent("banLuan"),
    },
    {
      title: (
        <HeaderSearch
          title={t("xetNghiem.maMay")}
          searchSelect={
            <Select
              placeholder={t("xetNghiem.timMaMay")}
              data={listAllMaMay}
              onChange={onSearchInput("maMayId")}
            />
          }
        />
      ),
      width: "200px",
      align: "left",
      dataIndex: "maMayId",
      key: "maMayId",
      show: true,
      i18Name: "xetNghiem.maMay",
      render: renderContent("maMayId"),
    },
    {
      title: (
        <HeaderSearch
          title="eCrCl"
          search={
            <InputTimeout
              placeholder="ecrCl"
              onChange={onSearchInput("eCrCl", true)}
            />
          }
        />
      ),
      width: "100px",
      align: "center",
      dataIndex: "ecrCl",
      key: "ecrCl",
      show: true,
      i18Name: "xetNghiem.eCrCl",
      render: renderContent("ecrCl"),
    },
    {
      title: (
        <HeaderSearch
          title="eGFR"
          search={
            <InputTimeout
              placeholder="egfr"
              onChange={onSearchInput("egfr", true)}
            />
          }
        />
      ),
      width: "100px",
      align: "center",
      dataIndex: "egfr",
      key: "egfr",
      show: true,
      i18Name: "xetNghiem.eGFR",
      render: renderContent("egfr"),
    },
    {
      title: <HeaderSearch title={t("xetNghiem.phuongPhapXetNghiem")} />,
      width: "200px",
      align: "left",
      dataIndex: "phuongPhap",
      key: "phuongPhap",
      show: true,
      i18Name: "xetNghiem.phuongPhapXetNghiem",
      render: renderContent("phuongPhap"),
    },
    {
      title: <HeaderSearch title={t("xetNghiem.ghiChu")} />,
      width: "200px",
      align: "left",
      dataIndex: "ghiChu",
      key: "ghiChu",
      show: true,
      i18Name: "xetNghiem.ghiChu",
      render: renderContent("ghiChu"),
    },
    {
      title: <HeaderSearch title={t("common.canhBao")} />,
      width: "200px",
      align: "left",
      dataIndex: "canhBao",
      key: "canhBao",
      show: true,
      i18Name: "common.canhBao",
      render: renderContent("canhBao"),
    },
    {
      title: (
        <HeaderSearch
          title={t("common.trangThai")}
          searchSelect={
            <Select
              defaultValue=""
              data={listTrangThai}
              value={state.trangThai}
              placeholder={t("common.chonTrangThai")}
              onChange={onSearchInput("trangThai")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: "150px",
      align: "left",
      dataIndex: "trangThai",
      key: "trangThai",
      show: true,
      i18Name: "common.trangThai",
      render: renderContent("trangThai"),
    },
    ...KEYS_VI_TRI_CHAM_CONG_XET_NGHIEM.map((key) => ({
      title: (
        <HeaderSearch title={DATA_TEN_HIEN_THI[key]} isTitleCenter={true} />
      ),
      dataIndex: getTenField(key, LOAI_DICH_VU.XET_NGHIEM),
      key: key,
      width: 160,
      show: false,
      i18Name: DATA_TEN_HIEN_THI[key],
      render: renderContent("viTriChamCong", key),
    })),
    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("common.chiTiet")}
              <Setting refTable={refSettings} />
            </>
          }
        />
      ),
      width: "70px",
      fixed: "right",
      align: "center",
      ignore: true,
      render: renderExclamation("exclamation"),
    },
  ];

  const onViewFileKetQua = async () => {
    try {
      showLoading();
      const soPhieus = [];
      selectedRowKeys.forEach((item) => {
        if (item.indexOf("-") < 0) {
          soPhieus.push(parseInt(item));
        }
      });

      const setId = new Set();
      dataTable.forEach((item) => {
        if (item.soPhieuId) {
          setId.add(item.soPhieuId);
        }
      });

      let payload = { nbDotDieuTriId, dsSoPhieuId: Array.from(setId) };

      const s = await getKetQuaXNPdf(payload);

      const { dsPhieuHis, dsPhieuLis, dsPhieuPacs } = s || {};
      const dsPhieu = locPhieuLisPacs(
        { dsPhieuHis, dsPhieuLis, dsPhieuPacs },
        { allData: false, isLocPhieu: true }
      );
      const dsPhieuFull = locPhieuLisPacs(
        { dsPhieuHis, dsPhieuLis, dsPhieuPacs },
        { allData: true, isLocPhieu: true }
      );
      if (
        isArray(dsPhieuFull, true) &&
        dsPhieuFull.every((x) => x?.loaiIn == LOAI_IN.MO_TAB)
      ) {
        const finalFile = await printProvider.getMergePdf(dsPhieu);
        openInNewTab(finalFile);
      } else {
        printProvider.printPdf(dsPhieuFull);
      }
    } catch (error) {
      console.log("error", error);
    } finally {
      hideLoading();
    }
  };

  const showHuyMauButton =
    selectedServiceHasGuiLis?.length > 0 &&
    (selectedServiceHasGuiLis.includes(true)
      ? checkRole([ROLES["XET_NGHIEM"].HIEN_THI_HUY_MAU_SHHH])
      : true);

  const renderHeaderRight = () => (
    <>
      {selectedBtn.some((item) =>
        TRANG_THAI["XAC_NHAN_TIEP_NHAN_MAU"].includes(item)
      ) && (
          <>
            {checkRole([ROLES["XET_NGHIEM"].HUY_MAU_HH]) && showHuyMauButton && (
              <Button type="default" height={30} onClick={onHuyMau("cancel")}>
                {t("xetNghiem.huyMau")}
              </Button>
            )}
            {checkRole([ROLES["XET_NGHIEM"].TIEP_NHAN_MAU_HH]) && (
              <Button
                type="primary"
                height={30}
                onClick={onTiepNhanMau("accept")}
              >
                {t("xetNghiem.tiepNhanMau")}
              </Button>
            )}
          </>
        )}
      {selectedBtn.some((item) =>
        TRANG_THAI["XAC_NHAN_KET_QUA"].includes(item)
      ) &&
        checkRole([ROLES["XET_NGHIEM"].NHAP_KET_QUA_HH]) && (
          <>
            {showHuyMauButton && (
              <Button
                type="default"
                height={30}
                onClick={onTiepNhanMau("cancel")}
              >
                {t("xetNghiem.huyTiepNhanMau")}
              </Button>
            )}
            <Button type="primary" height={30} onClick={onLuuKetQua}>
              {t("xetNghiem.luuKetQua")}
            </Button>
            <Button type="primary" height={30} onClick={onCoKetQua("accept")}>
              {t("common.coKetQua")}
            </Button>
          </>
        )}

      {selectedBtn.some((item) => TRANG_THAI["CO_KET_QUA"].includes(item)) &&
        checkRole([ROLES["XET_NGHIEM"].DUYET_KET_QUA_HH]) && (
          <>
            <Button type="default" height={30} onClick={onCoKetQua("cancel")}>
              {t("common.huyKetQua")}
            </Button>
            <Button
              type="primary"
              height={30}
              onClick={onDuyetKetQua("accept")}
            >
              {t("xetNghiem.duyetKetQua")}
            </Button>
          </>
        )}
      {selectedBtn.some((item) =>
        TRANG_THAI["DUYET_KET_QUA"].includes(item)
      ) && (
          <>
            {checkRole([ROLES["XET_NGHIEM"].HUY_DUYET_KET_QUA_HH]) && (
              <Button
                type="default"
                height={30}
                onClick={onDuyetKetQua("cancel")}
              >
                {t("xetNghiem.huyDuyetKetQua")}
              </Button>
            )}
          </>
        )}
      {selectedBtn.some(
        (item) =>
          TRANG_THAI["CO_KET_QUA"].includes(item) ||
          TRANG_THAI["DUYET_KET_QUA"].includes(item)
      ) && (
          <>
            <Button type="default" height={30} onClick={onViewFileKetQua}>
              {t("xetNghiem.xemKetQuaPdf")}
            </Button>
          </>
        )}
    </>
  );
  const renderEmptyTextLeftTable = () => {
    return (
      <div style={{ marginTop: 130 }}>
        <div style={{ color: "#c3c3c3", fontSize: 14 }}>
          {t("common.khongCoDuLieuPhuHop")}
        </div>
      </div>
    );
  };

  return (
    <Main
      title={t("xetNghiem.danhSachDichVu")}
      headerRight={renderHeaderRight()}
      top={0}
      bottom={0}
    >
      <TableWrapper
        scroll={{ x: 1000 }}
        columns={columns}
        dataSource={dataTable}
        expandIconColumnIndex={3}
        className="custom-nestedtable"
        rowSelection={rowSelection}
        rowKey={(record) => record.id}
        rowClassName={(record) => (record.type === "soPhieu" ? "sophieu" : "")}
        expandable={{
          expandIcon: ({ expanded, onExpand, record }) => {
            if (!record.children?.length) return null;
            return (
              <div className="ic-expand" onClick={(e) => onExpand(record, e)}>
                {expanded ? <CaretDownOutlined /> : <CaretUpOutlined />}
              </div>
            );
          },
        }}
        locale={{
          emptyText: renderEmptyTextLeftTable(),
        }}
        ref={refSettings}
        tableName={`table_XETNGHIEM_DanhSachDichVu`}
      // expandRowByClick
      />
    </Main>
  );
};

export default DanhSachDichVu;
