import React, { useState, useEffect, useMemo, useRef } from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { Collapse, Form, Menu, message } from "antd";
import FormContent from "./components/formContent";
import KetLuan from "./components/ketLuan";
import NhomChiSoCon from "./components/nhomChiSoCon";
import { Button, Dropdown, Select } from "components";
import { TRANG_THAI, SERVICE_STATUS } from "../../configs";
import { checkRole } from "lib-utils/role-utils";
import { LOAI_IN, ROLES, THIET_LAP_CHUNG, TRANG_THAI_DICH_VU } from "constants/index";
import printProvider from "data-access/print-provider";
import { SVG } from "assets";
import { isArray, locPhieuLisPacs, openInNewTab } from "utils";
import ModalChiTietDichVuGBPVS from "./components/ModalChiTietDichVuGBPVS";
import { useConfirm, useLoading, useStore, useThietLap } from "hooks";
import { Main } from "./styled";
import { useXetNghiem } from "pages/xetNghiem/contexts";
import { cloneDeep } from "lodash";
import { toSafePromise } from "lib-utils";

const { Panel } = Collapse;

const ThongTinDichVu = () => {
  const { showConfirm, showAsyncConfirm } = useConfirm();
  const { showLoading, hideLoading } = useLoading();
  const { t } = useTranslation();
  const [form] = Form.useForm();

  const listAllMauKetQuaXN = useStore("mauKetQuaXN.listAllMauKetQuaXN", []);
  const infoDichVu = useStore("xnGiaiPhauBenhViSinh.infoDichVu");
  const listAllMaMay = useStore("maMay.listAllMaMay", []);
  const nhanVienId = useStore("auth.auth.nhanVienId");

  const { dsTrangThaiNb } = useXetNghiem();

  const {
    mauKetQuaXN: { getListAllMauKetQuaXN },
    maMay: { getListAllMaMay },
    xetNghiem: { updateKetQuaXetNghiem },
    layMauXN: { xacNhanLayMau },
    xnGiaiPhauBenhViSinh: {
      xacNhanKetQua,
      duyetKetQua,
      xacNhanTiepNhanMau,
      updateData,
      getDsDichVuChiDinhXN,
      getPhieuKetQua,
    },
    xetNghiemChiSoCon: { getDmDvKyThuatById },
    nbDichVuKyThuat: { xacNhanTrungNguoiThucHien },
  } = useDispatch();
  const [dataKIEM_TRA_THUC_HIEN_DICH_VU] = useThietLap(THIET_LAP_CHUNG.KIEM_TRA_THUC_HIEN_DICH_VU, "false");

  const [mauSelected, setMauSelected] = useState({});
  const [dsChiSoCon, setDsChiSoSon] = useState([]);
  const isDisabled = infoDichVu.trangThai !== SERVICE_STATUS.DA_TIEP_NHAN_MAU;

  const trangThaiDichVu = infoDichVu.trangThai;

  const allowEditNguoiThucHien =
    checkRole([ROLES["PHAU_THUAT_THU_THUAT"].SUA_THONG_TIN_NGUOI_THUC_HIEN]) &&
    [TRANG_THAI_DICH_VU.DA_CO_KET_QUA, TRANG_THAI_DICH_VU.DA_DUYET].includes(
      trangThaiDichVu
    ) && // 155,160
    infoDichVu.trangThaiHoan === 0;

  const refChiTietDichVu = useRef();

  useEffect(() => {
    getListAllMauKetQuaXN({ page: "", size: "", active: true });
    getListAllMaMay({ page: "", size: "", active: true });
  }, []);

  useEffect(() => {
    if (infoDichVu.dichVuId) {
      getDmDvKyThuatById(infoDichVu.dichVuId);
    }
  }, [infoDichVu.dichVuId]);

  const onChangeMauKetQuaXN = (value, item) => {
    setMauSelected(item);
  };

  const handleClickSave = () => {
    form
      .validateFields()
      .then((values) => {
        values = {
          ...values,
          dsChiSoCon,
          benhVienThucHienId: values.benhVienThucHienId ?? null,
          nguoiTiepNhanId: values.nguoiTiepNhanId ?? null,
          nguoiThucHienId: values.nguoiThucHienId ?? null,
          nguoiDuyetKetQuaId: values.nguoiDuyetKetQuaId ?? null,
          nguoiThucHien2Id: values.nguoiThucHien2Id ?? null,
          phuThucHien1Id: values.phuThucHien1Id ?? null,
          phuThucHien2Id: values.phuThucHien2Id ?? null,
          phuThucHien3Id: values.phuThucHien3Id ?? null,
          thanhVienKhacId: values.thanhVienKhacId ?? null,
        };
        if (infoDichVu.id) {
          updateKetQuaXetNghiem([{ id: infoDichVu.id, ...values }])
            .then((s) => {
              updateData({ infoDichVu: s[0] });
              getDsDichVuChiDinhXN({
                nbDotDieuTriId: infoDichVu.nbDotDieuTriId,
              });
            })
            .catch((err) => {
              //case dv đã được tiếp nhận điều tra => reload lại thông tin
              if (err?.code == 8723) {
                getDsDichVuChiDinhXN({
                  nbDotDieuTriId: infoDichVu.nbDotDieuTriId,
                }).then((res) => {
                  let _infoDichVu = res.find((x) => x.id === infoDichVu.id);
                  if (_infoDichVu) {
                    updateData({ infoDichVu: _infoDichVu });
                  }
                });
              }
            });
        }
      })
      .catch((err) => { });
  };

  const showInfo = (data) => {
    setDsChiSoSon(data);
  };

  const listPanel = [
    {
      header: (
        <div>
          {infoDichVu.nhomDichVuCap2Id == 66
            ? t("xetNghiem.viSinhKySinhTrung")
            : `${t("xetNghiem.giaiPhauBenh")} - ${t("xetNghiem.viSinh")}`}
        </div>
      ),
      content: <FormContent infoDichVu={infoDichVu} form={form} />,
      key: 1,
    },
    {
      header: (
        <div>
          {t("common.ketLuan")}{" "}
          <Select
            placeholder={t("xetNghiem.chonMauKetLuan")}
            data={listAllMauKetQuaXN}
            onChange={onChangeMauKetQuaXN}
            onClick={(e) => {
              e.stopPropagation();
            }}
            disabled={isDisabled}
          />
        </div>
      ),
      content: (
        <>
          <KetLuan
            infoDichVu={infoDichVu}
            mauSelected={mauSelected}
            listAllMaMay={listAllMaMay}
            form={form}
            dsChiSoCon={dsChiSoCon}
          />
          <NhomChiSoCon infoDichVu={infoDichVu} showInfo={showInfo} />
        </>
      ),
      key: 2,
    },
  ];

  const onTiepNhanMau = (trangThaiDichVu) => () => {
    const xacNhanTiepNhanMauFunc = (s) => {
      xacNhanTiepNhanMau({
        data: [infoDichVu.id],
        status: trangThaiDichVu,
      }).then((res) => {
        if (res?.code === 0) {
          const newInfoDichVu = {
            ...infoDichVu,
            ...s[0],
            trangThai: res?.data?.[0]?.trangThai,
          };
          updateData({ infoDichVu: cloneDeep(newInfoDichVu) });
          getDsDichVuChiDinhXN({ nbDotDieuTriId: infoDichVu.nbDotDieuTriId });
        }

        if (res.code == 7609) {
          showConfirm({
            title: t("common.thongBao"),
            content: res.message,
            okText: t("common.dong"),
            classNameOkText: "button-closel",
            showBtnOk: true,
          });
        }
      });
    };

    if (trangThaiDichVu === "accept") {
      form
        .validateFields()
        .then((values) => {
          values = { ...values, dsChiSoCon };
          values.nguoiTiepNhanId = nhanVienId;

          if (infoDichVu.id) {
            updateKetQuaXetNghiem([{ id: infoDichVu.id, ...values }]).then(
              (s) => {
                xacNhanTiepNhanMauFunc(s);
              }
            );
          }
        })
        .catch((err) => {
          reject();
        });
    }

    if (trangThaiDichVu === "cancel" && infoDichVu.id) {
      xacNhanTiepNhanMauFunc();
    }
  };

  const onHuyMau = (trangThaiDichVu) => () => {
    xacNhanLayMau({ data: [infoDichVu.id], status: trangThaiDichVu }).then(
      (res) => {
        if (res?.code === 0) {
          const newInfoDichVu = {
            ...infoDichVu,
            trangThai: res?.data?.[0]?.trangThai,
          };
          updateData({ infoDichVu: newInfoDichVu });

          getDsDichVuChiDinhXN({ nbDotDieuTriId: infoDichVu.nbDotDieuTriId });
        }
      }
    );
  };

  const onCoKetQua = (trangThaiDichVu) => () => {
    const xacNhanKetQuaFunc = (s = [{}]) =>
      xacNhanKetQua({ data: [infoDichVu.id], status: trangThaiDichVu }).then(
        (res) => {
          if (res?.code === 0) {
            const newInfoDichVu = {
              ...infoDichVu,
              ...s[0],
              trangThai: res?.data?.[0]?.trangThai,
            };

            updateData({ infoDichVu: newInfoDichVu });
            getDsDichVuChiDinhXN({
              nbDotDieuTriId: infoDichVu.nbDotDieuTriId,
            });
          }
        }
      );

    if (trangThaiDichVu === "accept") {
      form
        .validateFields()
        .then((values) => {
          values = { ...values, dsChiSoCon };
          values.nguoiThucHienId = nhanVienId;

          if (infoDichVu.id) {
            updateKetQuaXetNghiem([{ id: infoDichVu.id, ...values }]).then(
              async (s) => {
                if (dataKIEM_TRA_THUC_HIEN_DICH_VU?.eval()) {
                  const [err, res] = await toSafePromise(xacNhanTrungNguoiThucHien({
                    dichVuId: infoDichVu.id,
                    nguoiThucHienId: infoDichVu.nguoiThucHienId,
                    thoiGianThucHien: infoDichVu.thoiGianThucHien,
                    thoiGianCoKetQua: infoDichVu.thoiGianCoKetQua || moment().format("YYYY-MM-DD HH:mm:ss"),
                    duKienKetQua: infoDichVu.duKienKetQua,
                    showLoading,
                    showAsyncConfirm,
                    hideLoading
                  }));
                  if (res) {
                    xacNhanKetQuaFunc(s);
                  }
                } else {
                  xacNhanKetQuaFunc(s);
                }
              }
            );
          }
        })
        .catch((err) => {
          reject();
        });
    }

    if (trangThaiDichVu === "cancel" && infoDichVu.id) {
      xacNhanKetQuaFunc();
    }
  };

  const onDuyetKetQua = (trangThaiDichVu) => () => {
    const duyetKetQuaFunc = (s) => {
      duyetKetQua({ data: [infoDichVu.id], status: trangThaiDichVu }).then(
        (res) => {
          if (res?.code === 0) {
            if (res.code === 0) {
              const newInfoDichVu = {
                ...infoDichVu,
                ...s[0],
                trangThai: res?.data?.[0]?.trangThai,
              };
              updateData({ infoDichVu: newInfoDichVu });
              getDsDichVuChiDinhXN({
                nbDotDieuTriId: infoDichVu.nbDotDieuTriId,
              });
            }
          }
        }
      );
    };

    if (trangThaiDichVu === "accept") {
      form
        .validateFields()
        .then((values) => {
          values = { ...values, dsChiSoCon };
          values.nguoiDuyetKetQuaId = nhanVienId;

          if (infoDichVu.id) {
            updateKetQuaXetNghiem([{ id: infoDichVu.id, ...values }]).then(
              (s) => {
                duyetKetQuaFunc(s);
              }
            );
          }
        })
        .catch((err) => {
          reject();
        });
    }

    if (trangThaiDichVu === "cancel" && infoDichVu.id) {
      duyetKetQuaFunc();
    }
  };

  const onInPhieuKetQuaXetNghiem = () => {
    getPhieuKetQua({
      nbDotDieuTriId: infoDichVu.nbDotDieuTriId,
      dsTrangThai: dsTrangThaiNb,
    }).then(async (s) => {
      const dsPhieu = locPhieuLisPacs(s, {
        allData: false,
        isLocPhieu: true,
        isXetNghiem: true,
      });
      const dsPhieuFull = locPhieuLisPacs(s, {
        allData: true,
        isLocPhieu: true,
        isXetNghiem: true,
      });
      if (dsPhieu.length > 0) {
        if (
          isArray(dsPhieuFull, true) &&
          dsPhieuFull.every((x) => x?.loaiIn == LOAI_IN.MO_TAB)
        ) {
          const finalFile = await printProvider.getMergePdf(dsPhieu);
          openInNewTab(finalFile);
        } else {
          printProvider.printPdf(dsPhieuFull);
        }
      } else {
        message.error("Không tồn tại phiếu kết quả!");
      }
    });
  };

  const menu = useMemo(() => {
    const phieus = [
      { ten: "Phiếu kết quả xét nghiệm", action: onInPhieuKetQuaXetNghiem },
      {
        ten: "Phiếu kết quả xét nghiệm sinh học phân tử",
        action: () =>
          window.open("/editor/bao-cao/EMR_XN04.1/" + infoDichVu.id),
      },
    ];
    return (
      <Menu
        items={phieus.map((item, index) => ({
          key: index,
          label: <a onClick={item.action}>{item.ten || item.tenBaoCao}</a>,
        }))}
      />
    );
  }, [infoDichVu?.id]);

  const onShowChiTietDichVu = () => {
    if (infoDichVu?.id)
      refChiTietDichVu.current && refChiTietDichVu.current.show({ infoDichVu });
  };

  const showHuyMauButton =
    infoDichVu?.guiLis === true
      ? checkRole([ROLES["XET_NGHIEM"].HIEN_THI_HUY_MAU_GPBVS])
      : true;

  return (
    <Main
      top={0}
      bottom={0}
      title={
        <div className="header-left__title">
          <div>
            {t("common.dichVu")}:{" "}
            <span className="header-left__title--bold">
              {infoDichVu.maDichVu}
              {infoDichVu.tenDichVu && ` - ${infoDichVu.tenDichVu}`}
            </span>
          </div>
          <div>
            {t("xetNghiem.chanDoanSoBo")}:{" "}
            <span className="header-left__title--bold">
              {infoDichVu.cdSoBo}
            </span>
          </div>
        </div>
      }
      headerRight={
        <>
          <span className="header-right__detail">
            {t("xetNghiem.chiTietDichVu")}
            <SVG.IcInfo color={"white"} onClick={onShowChiTietDichVu} />
          </span>

          {TRANG_THAI["XAC_NHAN_TIEP_NHAN_MAU"].includes(trangThaiDichVu) && (
            <>
              {checkRole([ROLES["XET_NGHIEM"].TIEP_NHAN_MAU_GPB]) && (
                <Button type="primary" onClick={onTiepNhanMau("accept")}>
                  {t("xetNghiem.tiepNhanMau")}
                </Button>
              )}
              {checkRole([ROLES["XET_NGHIEM"].HUY_MAU_GPB]) &&
                showHuyMauButton && (
                  <Button type="default" onClick={onHuyMau("cancel")}>
                    {t("xetNghiem.huyMau")}
                  </Button>
                )}
            </>
          )}

          {TRANG_THAI["XAC_NHAN_KET_QUA"].includes(trangThaiDichVu) &&
            checkRole([ROLES["XET_NGHIEM"].NHAP_KET_QUA_GPB]) && (
              <>
                {showHuyMauButton && (
                  <Button type="primary" onClick={onTiepNhanMau("cancel")}>
                    {t("xetNghiem.huyTiepNhanMau")}
                  </Button>
                )}
                <Button type="primary" onClick={onCoKetQua("accept")}>
                  {t("common.coKetQua")}
                </Button>
              </>
            )}

          {TRANG_THAI["CO_KET_QUA"].includes(trangThaiDichVu) &&
            checkRole([ROLES["XET_NGHIEM"].DUYET_KET_QUA_GPB]) && (
              <>
                <Button type="default" onClick={onCoKetQua("cancel")}>
                  {t("common.huyKetQua")}
                </Button>
                <Button type="primary" onClick={onDuyetKetQua("accept")}>
                  {t("xetNghiem.duyetKetQua")}
                </Button>
              </>
            )}

          {TRANG_THAI["DUYET_KET_QUA"].includes(trangThaiDichVu) &&
            checkRole([ROLES["XET_NGHIEM"].HUY_DUYET_KET_QUA_GPB]) && (
              <>
                <Button type="default" onClick={onDuyetKetQua("cancel")}>
                  {t("xetNghiem.huyDuyetKetQua")}
                </Button>
              </>
            )}

          {TRANG_THAI["IN"].includes(trangThaiDichVu) &&
            checkRole([ROLES["XET_NGHIEM"].IN_KET_QUA_GPB]) && (
              <Dropdown overlay={menu} placement="left">
                <Button
                  type="default"
                  minWidth={100}
                  rightIcon={<SVG.IcPrint />}
                >
                  <span>{t("xetNghiem.inKetQua")}</span>
                </Button>
              </Dropdown>
            )}

          {(TRANG_THAI["SAVE"].includes(trangThaiDichVu) ||
            allowEditNguoiThucHien) &&
            checkRole([ROLES["XET_NGHIEM"].NHAP_KET_QUA_GPB]) && (
              <Button
                type="default"
                onClick={handleClickSave}
                disabled={!infoDichVu.id}
              >
                {t("xetNghiem.luuLai")}
              </Button>
            )}
        </>
      }
    >
      <Collapse defaultActiveKey={["1", "2"]} expandIconPosition="end">
        {listPanel.map((panel) => (
          <Panel key={panel.key} header={panel.header}>
            {panel.content}
          </Panel>
        ))}
      </Collapse>
      <ModalChiTietDichVuGBPVS ref={refChiTietDichVu} />
    </Main>
  );
};

export default ThongTinDichVu;
