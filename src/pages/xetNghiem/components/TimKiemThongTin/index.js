import React from "react";
import { Col } from "antd";
import ThongTinBN from "../ThongTinBenhNhan";
import PhongLayMau from "../PhongLayMau";
import { Main } from "./styled";

const TimKiemThongTin = ({
  layerId,
  hideSoPhieu,
  hidePhongLayMau,
  isThuocVTYTHoaChat,
}) => {
  return (
    <Main gutter={[16, 16]} style={{ margin: 0 }}>
      <Col span={8} className="left">
        <PhongLayMau layerId={layerId} hidePhongLayMau={hidePhongLayMau} />
      </Col>
      <Col span={16} className="right">
        <ThongTinBN
          layerId={layerId}
          hideSoPhieu={hideSoPhieu}
          isThuocVTYTHoaChat={isThuocVTYTHoaChat}
        />
      </Col>
    </Main>
  );
};

export default TimKiemThongTin;
