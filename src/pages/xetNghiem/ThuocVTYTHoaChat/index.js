import React, { useEffect } from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { Row } from "antd";

import { useGuid } from "hooks";

import TimKiemThongTin from "../components/TimKiemThongTin";
import { XetNghiemWrapper } from "../contexts";
import { MainPage, Main } from "./styled";
import ChiDinhThuocVTYTHoaChat from "./ChiDinhThuocVTYTHoaChat";
import DanhSachChiDinhThuocVTYTHoaChat from "./DanhSachChiDinhThuocVTYTHoaChat";
import { Card } from "components";

const ThuocVTYTHoaChat = () => {
  const { t } = useTranslation();

  const layerId = useGuid();
  const {
    phimTat: { onAddLayer, onRemoveLayer },
  } = useDispatch();

  useEffect(() => {
    onAddLayer({ layerId });
    return () => {
      onRemoveLayer({ layerId });
    };
  }, []);

  return (
    <XetNghiemWrapper>
      <MainPage
        breadcrumb={[
          { link: "/xet-nghiem", title: t("xetNghiem.xetNghiem") },
          {
            link: "/xet-nghiem/thuoc-vat-tu-hoa-chat",
            title: t("xetNghiem.thuocVTYTHoaChatKemXetNghiem"),
          },
        ]}
      >
        <Main>
          <TimKiemThongTin
            layerId={layerId}
            hideSoPhieu={true}
            hidePhongLayMau={true}
            isThuocVTYTHoaChat={true}
          />
          <Row className="list-area" gutter={[16, 16]} style={{ margin: 0 }}>
            <Card style={{ width: "100%", height: "100%" }}>
              <div className="content-wrapper">
                <ChiDinhThuocVTYTHoaChat layerId={layerId} />
                <DanhSachChiDinhThuocVTYTHoaChat />
              </div>
            </Card>
          </Row>
        </Main>
      </MainPage>
    </XetNghiemWrapper>
  );
};

export default ThuocVTYTHoaChat;
