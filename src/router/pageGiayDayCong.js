import { ROLES } from "constants/index";
import { Page } from "pages/constants";
import React from "react";
const SubPageGiayDayCong = React.lazy(() =>
  import("pages/home/<USER>/GiayDayCong")
);
const DanhSachGiayNghiHuong = React.lazy(() =>
  import("pages/giayDayCong/GiayNghiHuong")
);
const DanhSachNbTuVong = React.lazy(() => import("pages/giayDayCong/NbTuVong"));
const DanhSachNbRaVien = React.lazy(() => import("pages/giayDayCong/NbRaVien"));
const DanhSachPhieuTomTatBa = React.lazy(() =>
  import("pages/giayDayCong/PhieuTomTatBa")
);
const DanhSachGiayChungSinh = React.lazy(() =>
  import("pages/giayDayCong/GiayChungSinh")
);
const DanhSachGiayKskLaiXe = React.lazy(() =>
  import("pages/giayDayCong/GiayKskLaiXe")
);
const DanhSachGiayNghiDuongThai = React.lazy(() =>
  import("pages/giayDayCong/GiayNghiDuongThai")
);

export default {
  subPageGiayDayCong: {
    component: Page(SubPageGiayDayCong, [ROLES["HE_THONG"].GIAY_DAY_CONG]),
    accessRoles: [],
    path: "/danh-sach-giay-day-cong",
    exact: true,
  },
  dsGiayNghiHuong: {
    component: Page(DanhSachGiayNghiHuong, [
      ROLES["GIAY_DAY_CONG"].GIAY_NGHI_HUONG_DS,
    ]),
    accessRoles: [],
    path: "/giay-day-cong/giay-nghi-huong",
    exact: true,
  },
  dsNbTuVong: {
    component: Page(DanhSachNbTuVong, []),
    accessRoles: [],
    path: "/giay-day-cong/nb-tu-vong",
    exact: true,
  },
  dsNbRaVien: {
    component: Page(DanhSachNbRaVien, [ROLES["GIAY_DAY_CONG"].NB_RA_VIEN_DS]),
    accessRoles: [],
    path: "/giay-day-cong/nb-ra-vien",
    exact: true,
  },
  dsPhieuTomTatBa: {
    component: Page(DanhSachPhieuTomTatBa, []),
    accessRoles: [],
    path: "/giay-day-cong/phieu-tom-tat-ba",
    exact: true,
  },
  dsGiayChungSinh: {
    component: Page(DanhSachGiayChungSinh, [
      ROLES["GIAY_DAY_CONG"].GIAY_CHUNG_SINH_DS,
    ]),
    accessRoles: [ROLES["GIAY_DAY_CONG"].GIAY_CHUNG_SINH_DS],
    path: "/giay-day-cong/giay-chung-sinh",
    exact: true,
  },
  dsGiayKskLaiXe: {
    component: Page(DanhSachGiayKskLaiXe, [
      ROLES["GIAY_DAY_CONG"].GIAY_KSK_LAI_XE_DS,
    ]),
    accessRoles: [ROLES["GIAY_DAY_CONG"].GIAY_KSK_LAI_XE_DS],
    path: "/giay-day-cong/giay-ksk-lai-xe",
    exact: true,
  },
  dsGiayNghiDuongThai: {
    component: Page(DanhSachGiayNghiDuongThai, [
      ROLES["GIAY_DAY_CONG"].GIAY_NGHI_DUONG_THAI_DS,
    ]),
    accessRoles: [ROLES["GIAY_DAY_CONG"].GIAY_NGHI_DUONG_THAI_DS],
    path: "/giay-day-cong/giay-chung-nhan-nghi-duong-thai",
    exact: true,
  },
};
