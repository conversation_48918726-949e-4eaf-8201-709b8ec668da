import React, {
  useState,
  forwardRef,
  useImperativeHandle,
  useRef,
  useEffect,
} from "react";
import { Main } from "./styled";
import { Col, message } from "antd";
import { useDispatch } from "react-redux";
import DanhSachPhieu from "./DanhSachPhieu";
import XemTruoc from "./XemTruoc";
import { useTranslation } from "react-i18next";
import { ModalTemplate } from "components";
import { isArray, sleep } from "utils/index";
import { HOTKEY } from "constants/index";

const ModalSignPrint = ({ onClickPhieuInQr }, ref) => {
  const refModal = useRef(null);
  const refRefreshFunc = useRef(null);
  const refDanhSachPhieu = useRef(null);
  const { t } = useTranslation();

  const {
    phieuIn: { getPhieuIn, getPhieuInVaKy, updateData, getListPhieu },
  } = useDispatch();
  const [state, _setState] = useState({
    isKyPhieu: false,
    listPhieu: [],
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  useImperativeHandle(ref, () => ({
    show: ({ fromScanModal, ...payload }) => {
      if (!fromScanModal) {
        getPhieuIn(payload);
        setState({
          show: true,
          data: payload,
          isKyPhieu: payload.isKyPhieu,
          kyTheoTungPhieu: payload.kyTheoTungPhieu,
        });
      } else {
        updateData({
          listPhieu: [payload],
        });
        setState({
          show: true,
          data: {},
        });
      }
    },
    showToSign: (
      { phieuKy, payload, kyTheoTungPhieu = false },
      refreshFunc = async (maPhieu) => {
        const resfreshPhieu = await getListPhieu({
          ...payload,
        });
        return resfreshPhieu.find(
          (x) => x.ma === maPhieu || x.ma === phieuKy?.ma
        );
      }
    ) => {
      getPhieuInVaKy({ phieuKy, ...payload })
        .then(async (res) => {
          if (
            typeof onClickPhieuInQr === "function" &&
            isArray(res, true) &&
            res.some((i) =>
              ["P075", "P193", "P028", "P319", "P051"].includes(i.ma)
            )
          ) {
            await sleep(300);
            onClickPhieuInQr();
          }
        })
        .catch((err) => {
          if (err?.code == -1) {
            message.error(err?.message);
            onOK(false, null)();
          }
        });
      // await
      setState({
        show: true,
        isKyPhieu: true,
        data: payload,
        kyTheoTungPhieu,
      });

      refRefreshFunc.current = refreshFunc;
    },
  }));

  const onRefreshPhieuKy = async (maPhieu) => {
    if (refRefreshFunc.current) {
      const newPhieuKy = await refRefreshFunc.current(maPhieu);
      getPhieuInVaKy({
        phieuKy: isArray(newPhieuKy) ? newPhieuKy[0] : newPhieuKy,
        ...state.data,
      });
    } else {
      getPhieuIn({
        ...state.data,
      });
    }
  };

  const onOK = (isOk, type) => (e) => {
    if (!isOk) {
      updateData({ listPhieu: [], selectedIds: "" });
      setState({
        show: false,
        isKyPhieu: false,
      });
    }
  };

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  const hotKeys = [
    {
      keyCode: HOTKEY.F2,
      onEvent: () => {
        refDanhSachPhieu.current?.onPrint();
      },
    },
  ];

  return (
    <ModalTemplate
      ref={refModal}
      onCancel={onOK(false, null)}
      title={t("phieuIn.kyVaIn")}
      centered={true}
      hotKeys={hotKeys}
    >
      <Main className="modal-content" gutter={[8, 8]}>
        <Col md={16} xl={16} xxl={16}>
          <XemTruoc data={state.data} />
        </Col>
        <Col md={8} xl={8} xxl={8}>
          <DanhSachPhieu
            ref={refDanhSachPhieu}
            showButtonPrint={true}
            data={state.data}
            isKyPhieu={state.isKyPhieu}
            refreshPhieuKy={onRefreshPhieuKy}
            kyTheoTungPhieu={state.kyTheoTungPhieu}
            khoaChiDinhId={state.khoaChiDinhId}
          />
        </Col>
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalSignPrint);
