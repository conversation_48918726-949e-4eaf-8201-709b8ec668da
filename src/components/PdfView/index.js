import React, { useState, useEffect, useCallback, useRef } from "react";
import { Spin } from "antd";
import { Document, Page, pdfjs } from "react-pdf";
import { A4 } from "constants/index";
import fileUtils from "utils/file-utils";
import styled from "styled-components";
import { useRect } from "hooks";

pdfjs.GlobalWorkerOptions.workerSrc = `/js/pdf.worker.min.js`;

const PdfContainer = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: auto;

  .react-pdf__Document {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
  }

  .react-pdf__Page {
    margin-bottom: 10px;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.2);
    display: flex;
    justify-content: center;
  }

  .react-pdf__Page__canvas {
    max-width: 100%;
    object-fit: contain;
  }
`;

const SpinnerContainer = styled.div`
  width: ${(props) => props.$width || A4.width}px;
  height: ${(props) => props.$height || 520}px;
  display: flex;
  justify-content: center;
  align-items: center;
`;

const PdfView = (props) => {
  const {
    src,
    useFullWidth = false,
    scale = 1,
    showPageNumbers = false,
  } = props;

  const refContainer = useRef(null);

  const rectContainer = useRect(refContainer);

  const [state, _setState] = useState({
    pageNumber: 0,
    pages: [],
    loading: true,
    error: null,
    blobUrl: null,
  });

  const setState = useCallback((data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  }, []);

  const { pageNumber, pages, loading, blobUrl, error } = state;

  const onDocumentLoadSuccess = useCallback(
    ({ numPages }) => {
      const list = [];
      for (let i = 1; i <= numPages; i += 1) {
        list.push(i);
      }
      setState({
        pages: list,
        pageNumber: numPages,
        loading: false,
      });
    },
    [setState]
  );

  const handleDocumentLoadError = useCallback(() => {
    setState({
      error: "Failed to load PDF document",
      loading: false,
    });
  }, []);

  useEffect(() => {
    setState({ loading: true, error: null });

    fileUtils
      .getFromUrl({ url: fileUtils.absoluteFileUrl(src) })
      .then(async (s) => {
        const blobUrl = await fileUtils.arrayBufferToBlobUrl(
          s,
          "application/pdf",
          true
        );
        setState({
          blobUrl: blobUrl,
        });
      })
      .catch((error) => {
        console.error("Error loading PDF:", error);
        setState({ error: "Failed to load PDF file", loading: false });
      });

    return () => {
      // Cleanup blob URL on unmount
      if (state.blobUrl) {
        URL.revokeObjectURL(state.blobUrl);
      }
    };
  }, [src]);

  if (error) {
    return <div>{error}</div>;
  }

  const getPageWidth = () => {
    if (!useFullWidth) {
      return props.width || A4.width;
    }
    return rectContainer?.width ?? undefined;
  };

  const getPageHeight = () => {
    if (!useFullWidth) {
      return props.height || A4.height;
    }
  };

  return (
    <PdfContainer ref={refContainer}>
      {blobUrl ? (
        <Document
          file={blobUrl}
          onLoadSuccess={onDocumentLoadSuccess}
          onLoadError={handleDocumentLoadError}
          loading={
            <Spin spinning>
              <SpinnerContainer $width={props.width} $height={props.height} />
            </Spin>
          }
        >
          {pages.map((pageNum, index) => (
            <React.Fragment key={index}>
              <Page
                height={getPageHeight()}
                width={getPageWidth()}
                pageNumber={pageNum}
                scale={scale}
                renderTextLayer={false}
                renderAnnotationLayer={false}
                loading={
                  <Spin spinning>
                    <SpinnerContainer
                      $width={(rectContainer?.width || A4.width) * 0.8}
                      $height={400}
                    />
                  </Spin>
                }
              />
              {showPageNumbers && (
                <div style={{ textAlign: "center", marginBottom: "10px" }}>
                  {pageNum} / {pageNumber}
                </div>
              )}
            </React.Fragment>
          ))}
        </Document>
      ) : (
        <Spin spinning={loading}>
          <SpinnerContainer $width={props.width} $height={props.height} />
        </Spin>
      )}
    </PdfContainer>
  );
};

export default PdfView;
