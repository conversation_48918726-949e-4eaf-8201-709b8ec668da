import React, {
  useEffect,
  useState,
  forwardRef,
  useImperativeHandle,
  useRef,
} from "react";
import T from "prop-types";
import { Button, Row, Col, Radio, Input, Checkbox, InputNumber } from "antd";
import moment from "moment";
import { Main } from "./styled";
import {
  DeleteOutlined,
  PlusOutlined,
  FontColorsOutlined,
} from "@ant-design/icons";
import {
  EditorTool,
  AlignConfig,
  FontSizeConfig,
  FontStyleConfig,
} from "components/editor/config";
const { FieldName, PickColor } = EditorTool;
import { useTranslation } from "react-i18next";
const DropDownListProps = forwardRef((props, ref) => {
  const [state, _setState] = useState({
    fieldName: "",
    type: "multiple",
    checkList: [],
    variable: [],
    changeFields: [],
    disabled: false,
    readOnly: false,
    blockSignLevel: 0,
    defaultFromHIS: false,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const { t } = useTranslation();
  const refTimeOut = useRef();
  const { apiFields, updateComponents } = props;

  useImperativeHandle(ref, () => ({
    fieldName: state.fieldName,
    type: state.type,
    checkList: state.checkList,
    variable: state.variable,
    changeFields: state.changeFields,
    noLabel: state.noLabel,
    disabled: state.disabled,
    labelWidth: state.labelWidth,
    contentAlign: state.contentAlign,
    readOnly: state.readOnly,
    blockSignLevel: state.blockSignLevel,
    defaultFromHIS: state.defaultFromHIS,
    markSpanRow: state.markSpanRow,
    lineHeight: state.lineHeight || 1.5,
    fontSize: state.fontSize,
    contentColor: state.contentColor,
    listType: state.listType,
    dataApi: state.dataApi,
    fieldValue: state.fieldValue,
    fieldDisplay: state.fieldDisplay,
    minRow: state.minRow,
    displayText: state.displayText,
    bold: state.bold,
    italic: state.italic,
    underline: state.underline,
    margeDataDisplay: state.margeDataDisplay,
    dataIsArray: state.dataIsArray,
    printHide: state.printHide,
    ruleDisplay: state.ruleDisplay,
    ruleName: state.ruleName,
    ruleValue: state.ruleValue,
    defaultValues: state.defaultValues,
    onFieldChange: state.onFieldChange,
    fieldName2: state.fieldName2,
    fieldDisplay2: state.fieldDisplay2,
    choPhepNhapChu: state.choPhepNhapChu,
  }));

  useEffect(() => {
    if (props.state.key) {
      setState({
        fieldName: props.state.props.fieldName,
        type: props.state.props.type || "multiple",
        checkList: props.state.props.checkList || [],
        variable: props.state.props.variable || [], //các biến tham số cho api
        changeFields: props.state.props.changeFields || [], //các trường thay đổi theo
        noLabel: props.state.props.noLabel,
        disabled: props.state.props.disabled || false,
        labelWidth: props.state.props.labelWidth,
        contentAlign: props.state.props.contentAlign || "left",
        readOnly: props.state.props.readOnly || false,
        blockSignLevel: props.state.props.blockSignLevel,
        defaultFromHIS: props.state.props.defaultFromHIS || false,
        markSpanRow:
          props.state.props.markSpanRow === undefined
            ? true
            : props.state.props.markSpanRow,
        lineHeight: props.state.props.lineHeight || 1.5,
        fontSize: props.state.props.fontSize || 12,
        contentColor: props.state.props.contentColor,
        listType: props.state.props.listType || 2, //loại dữ liệu hiển thị 1/api 2/customize
        dataApi: props.state.props.dataApi, //api danh sách
        fieldValue: props.state.props.fieldValue, //trường giá trị
        fieldDisplay: props.state.props.fieldDisplay, //trường hiển thị
        minRow: props.state.props.minRow, //số dòng mặc định
        displayText: props.state.props.displayText,
        bold: props.state.props.bold,
        italic: props.state.props.italic,
        underline: props.state.props.underline,
        margeDataDisplay: props.state.props.margeDataDisplay,
        dataIsArray: props.state.props.dataIsArray,
        printHide: props.state.props.printHide,
        ruleDisplay: props.state.props.ruleDisplay,
        ruleName: props.state.props.ruleName,
        ruleValue: props.state.props.ruleValue,
        defaultValues: props.state.props.defaultValues,
        onFieldChange: props.state.props.onFieldChange,
        fieldName2: props.state.props.fieldName2 || "",
        fieldDisplay2: props.state.props.fieldDisplay2,
        choPhepNhapChu: props.state.props.choPhepNhapChu,
      });
    }
  }, [props.state]);

  const changeValue = (type) => (value) => {
    setState({
      [type]: value,
    });
  };
  const updateItem = (item, key) => (e) => {
    const value = e.target.value;
    const newList = state.checkList.map((obj) =>
      obj.key === item.key
        ? {
            ...obj,
            [key]: value,
            ...(key == "label" ? { labelValue: value } : {}),
          }
        : obj
    );
    setState({
      checkList: newList,
    });
    if (refTimeOut.current) {
      clearTimeout(refTimeOut.current);
    }
    refTimeOut.current = setTimeout(() => {
      updateComponents({
        ...props.state,
        props: {
          ...props.state.props,
          checkList: newList,
        },
      });
    }, 500);
  };

  const removeItem = (itemKey) => () => {
    const newList = state.checkList.filter((item) => item.key !== itemKey);
    setState({
      checkList: newList,
    });
    updateComponents({
      ...props.state,
      props: {
        ...props.state.props,
        checkList: newList,
      },
    });
  };

  const addCheckItem = () => {
    const newList = state.checkList;
    const item = { label: "test", value: "text", key: moment().valueOf() };
    const list = [...newList, item];
    setState({
      checkList: list,
    });
    updateComponents({
      ...props.state,
      props: {
        ...props.state.props,
        checkList: list,
      },
    });
  };

  const updateVariable = (item, key) => (e) => {
    let value = "";
    if (e?.target?.hasOwnProperty("checked")) value = e.target.checked;
    else if (e?.target?.hasOwnProperty("value")) value = e.target.value;
    else value = e;
    const newList = state.variable.map((obj) =>
      obj.key === item.key
        ? {
            ...obj,
            [key]: value,
          }
        : obj
    );
    setState({
      variable: newList,
    });
    updateComponents({
      ...props.state,
      props: {
        ...props.state.props,
        checkList: newList,
      },
    });
  };
  const removeVariable = (itemKey) => () => {
    const newList = state.variable.filter((item) => item.key !== itemKey);
    setState({
      variable: newList,
    });
    updateComponents({
      ...props.state,
      props: {
        ...props.state.props,
        variable: newList,
      },
    });
  };
  const addVariable = () => {
    const newList = state.variable;
    const item = {
      name: "",
      value: null,
      fromUrl: true,
      key: moment().valueOf(),
    };
    const list = [...newList, item];
    setState({
      variable: list,
    });
    updateComponents({
      ...props.state,
      props: {
        ...props.state.props,
        variable: list,
      },
    });
  };

  const updateChangeFields = (item, key) => (e) => {
    let value = "";
    if (e?.target?.hasOwnProperty("checked")) value = e.target.checked;
    else if (e?.target?.hasOwnProperty("value")) value = e.target.value;
    else value = e;
    const newList = state.changeFields.map((obj) =>
      obj.key === item.key
        ? {
            ...obj,
            [key]: value,
          }
        : obj
    );

    setState({
      changeFields: newList,
    });
    updateComponents({
      ...props.state,
      props: {
        ...props.state.props,
        checkList: newList,
      },
    });
  };
  const removeChangeFields = (itemKey) => () => {
    const newList = state.changeFields.filter((item) => item.key !== itemKey);
    setState({
      changeFields: newList,
    });
    updateComponents({
      ...props.state,
      props: {
        ...props.state.props,
        changeFields: newList,
      },
    });
  };
  const addChangeFields = () => {
    const newList = state.changeFields;
    const item = {
      targetField: null,
      sourceValue: null,
      key: moment().valueOf(),
    };
    const list = [...newList, item];
    setState({
      changeFields: list,
    });
    updateComponents({
      ...props.state,
      props: {
        ...props.state.props,
        changeFields: list,
      },
    });
  };

  const changeInput = (type) => (e) => {
    if (type == "blockSignLevel") {
      if (/^\d+$/.test(e.target.value) || e.target.value == "") {
        setState({
          [type]: e.target.value,
        });
      }
    } else {
      setState({
        [type]: e.target.value,
      });
    }
  };
  const changeDataFormEMR = (e) => {
    changeValue("disabled")(!e.target.checked);
  };

  const changeCheckbox = (type) => (e) => {
    changeValue(type)(e.target.checked);
  };
  const onChangeFontStyle = (type, value) => {
    setState({
      [type]: value,
    });
  };
  return (
    <Main>
      <Row gutter={[12, 12]}>
        <Col span={8}>
          <span>{"Field name: "}</span>
        </Col>

        <Col span={16}>
          <FieldName
            style={{ width: "100%" }}
            onSelect={changeValue("fieldName")}
            value={state.fieldName}
            apiFields={apiFields}
          />
        </Col>
        <Col span={8}>
          <span>{"Field name 2: "}</span>
        </Col>
        <Col span={16}>
          <FieldName
            style={{ width: "100%" }}
            onSelect={changeValue("fieldName2")}
            value={state.fieldName2}
            apiFields={apiFields}
          />
        </Col>
        <Col span={24}>
          FieldName2 dùng để set giá trị khi fieldName bị null
        </Col>
        <Col span={8}>
          <span>{"Type: "}</span>
        </Col>

        <Col span={16}>
          <Radio.Group onChange={changeInput("type")} value={state.type}>
            <Radio value={"onlyOne"}>{t("editor.chon1GiaTri")}:</Radio>
            <Radio value={"multiple"}>{t("editor.chonNhieuGiaTri")}:</Radio>
          </Radio.Group>
        </Col>
        {state.type === "onlyOne" && (
          <>
            <Col span={8}>
              <span>{t("editor.duLieuDangMang")}:</span>
            </Col>
            <Col span={16}>
              <Checkbox
                checked={state.dataIsArray}
                onChange={changeCheckbox("dataIsArray")}
              />
            </Col>
          </>
        )}
        <Col span={8}>
          <span>{t("editor.choPhepNhapChu")}:</span>
        </Col>
        <Col span={16}>
          <Checkbox
            checked={state.choPhepNhapChu}
            onChange={changeCheckbox("choPhepNhapChu")}
          />
        </Col>
        <Col span={8}>
          <span>{t("editor.khongHienThiNhan")}:</span>
        </Col>
        <Col span={16}>
          <Checkbox
            checked={state.noLabel}
            onChange={changeCheckbox("noLabel")}
          />
        </Col>
      </Row>

      <Row gutter={[12, 12]}>
        <Col span={8}>
          <span>{t("editor.giaTriMacDinh")}</span>
        </Col>
        <Col span={16}>
          <Input
            value={state.defaultValues}
            onChange={changeInput("defaultValues")}
            size={"small"}
          />
        </Col>
        <Col span={8}>
          <span>{t("editor.doRongNhan")}:</span>
        </Col>
        <Col span={16}>
          <Input
            className="option-content"
            style={{ flex: 1 }}
            value={state.labelWidth}
            onChange={changeInput("labelWidth")}
            size={"small"}
          />
        </Col>
        <Col span={8}>
          <span>{t("editor.coChu")}</span>
        </Col>
        <Col span={16}>
          <FontSizeConfig
            changeFont={changeValue("fontSize")}
            fontSize={state.fontSize}
          />
        </Col>
        <Col span={8}>
          <span>{"Content Align: "}</span>
        </Col>
        <Col span={16}>
          <AlignConfig
            changeAlign={changeValue("contentAlign")}
            contentAlign={state.contentAlign}
          />
        </Col>

        {state.displayText && (
          <>
            <Col span={8}>
              <span>{"Font Style: "}</span>
            </Col>
            <Col span={16}>
              <FontStyleConfig
                bold={state.bold}
                italic={state.italic}
                underline={state.underline}
                onChange={onChangeFontStyle}
              />
            </Col>
          </>
        )}

        <Col span={8}>
          <span>{"Content Color: "}</span>
        </Col>
        <Col span={16}>
          <PickColor
            iconComponent={FontColorsOutlined}
            title={t("editor.chonMauChu")}
            dataColor={state.contentColor || "black"}
            changeColor={changeValue("contentColor")}
          />
        </Col>
        <Col span={8}>
          <span>{t("editor.khoaOCapKy")}</span>
        </Col>

        <Col span={16}>
          <Input
            value={state.blockSignLevel}
            onChange={changeInput("blockSignLevel")}
            size={"small"}
          />
        </Col>
        <Col span={8}>
          <span>{t("editor.chiDoc")}:</span>
        </Col>

        <Col span={16}>
          <Checkbox
            onChange={changeCheckbox("readOnly")}
            checked={state.readOnly}
          />
        </Col>

        <Col span={8}>
          <span>{t("editor.anKhiIn")}:</span>
        </Col>

        <Col span={16}>
          <Checkbox
            onChange={changeCheckbox("printHide")}
            checked={state.printHide}
          />
        </Col>
        <Col span={8}>
          <span>{t("editor.hienThiChu")}:</span>
        </Col>

        <Col span={16}>
          <Checkbox
            onChange={changeCheckbox("displayText")}
            checked={state.displayText}
          />
        </Col>
        {/* <Col span={8}>
          <span>{"Dữ liệu từ EMR: "}</span>
        </Col>

        <Col span={16}>
          <Checkbox onChange={changeDataFormEMR} checked={!state.disabled} />
        </Col> */}
      </Row>
      {/* chỉ hiển thị khi đánh dấu lấy dữ liệu từ EMR */}
      {/* {!state.disabled && (
        <Row gutter={[12, 12]}>
          <Col span={8}>
            <span>{"Giá trị ban đầu từ HIS: "}</span>
          </Col>
          <Col span={16}>
            <Checkbox
              onChange={changeCheckbox("defaultFromHIS")}
              checked={state.defaultFromHIS}
            />
          </Col>
        </Row>
      )} */}

      {!state.disabled && (
        <Row gutter={[12, 12]}>
          <Col span={8}>
            <span>{t("editor.hienThiDanhDauDong")}:</span>
          </Col>
          <Col span={16}>
            <Checkbox
              onChange={changeCheckbox("markSpanRow")}
              checked={state.markSpanRow}
            />
          </Col>
        </Row>
      )}
      <Row gutter={[12, 12]}>
        <Col span={8}>
          <span>{t("editor.doCaoDongVanBan")}:</span>
        </Col>
        <Col span={16}>
          <InputNumber
            value={state.lineHeight}
            onChange={changeValue("lineHeight")}
            placeholder={1.5}
            min={1}
            step={0.1}
            size={"small"}
          />
        </Col>
      </Row>
      <Row gutter={[12, 12]}>
        <Col span={8}>
          <span>{t("editor.soDongMacDinh")}:</span>
        </Col>
        <Col span={16}>
          <InputNumber
            value={state.minRow}
            onChange={changeValue("minRow")}
            placeholder={1}
            min={1}
            step={1}
            size={"small"}
          />
        </Col>
      </Row>
      <Row gutter={[12, 12]}>
        <Col span={8}>
          <span>{t("editor.duLieuDanhSach")}</span>
        </Col>
        <Col span={16}>
          <Radio.Group
            onChange={changeInput("listType")}
            value={state.listType}
          >
            <Radio value={1}>{t("editor.tuApi")}</Radio>
            <Radio value={2}>{t("editor.tuChon")}</Radio>
            <Radio value={3}>{t("editor.tuDuLieuPhieu")}</Radio>
          </Radio.Group>
        </Col>
      </Row>

      {state.listType == 2 && (
        <>
          <ul>
            {state.checkList.map((item) => (
              <li key={item.key} className={"item-main"}>
                <div className={"item-option"}>
                  <span className="option-label">{t("editor.nhan")}:</span>
                  <Input
                    className="option-content"
                    style={{ flex: 1 }}
                    value={item.label}
                    onChange={updateItem(item, "label")}
                    size={"small"}
                  />
                </div>
                <div className={"item-option"}>
                  <span className="option-label">{t("editor.giaTri")}:</span>
                  <Input
                    className="option-content"
                    style={{ flex: 1 }}
                    value={item.value}
                    onChange={updateItem(item, "value")}
                    size={"small"}
                  />
                  <Button
                    icon={<DeleteOutlined />}
                    size={"small"}
                    onClick={removeItem(item.key)}
                  />
                </div>
              </li>
            ))}
          </ul>
          <Button
            className={"add-btn"}
            icon={<PlusOutlined />}
            size={"small"}
            onClick={addCheckItem}
          />
        </>
      )}
      {[1, 3].includes(state.listType) && (
        <>
          <Row gutter={[12, 12]}>
            {state.listType === 1 && (
              <>
                <Col span={8}>
                  <span>{"API: "}:</span>
                </Col>
                <Col span={16}>
                  <Input
                    size="small"
                    value={state.dataApi}
                    onChange={changeInput("dataApi")}
                  />
                </Col>
              </>
            )}

            <Col span={8}>
              <span>{t("editor.truongGiaTri")}:</span>
            </Col>
            <Col span={16}>
              <Input
                size="small"
                value={state.fieldValue}
                onChange={changeInput("fieldValue")}
              />
            </Col>
            <Col span={8}>
              <span>{t("editor.gopDuLieuHienThi")}</span>
            </Col>
            <Col span={16}>
              <Checkbox
                checked={state.margeDataDisplay}
                onChange={changeCheckbox("margeDataDisplay")}
              />
            </Col>
            <Col span={8}>
              <span>{t("editor.truongHienThi")}</span>
            </Col>
            <Col span={16}>
              <Input
                size="small"
                value={state.fieldDisplay}
                onChange={changeInput("fieldDisplay")}
              />
              {state.margeDataDisplay ? (
                <div>{`${t("editor.viDu")}: {id}-{ten}`}</div>
              ) : (
                <div>{`${t("editor.viDu")}: ten`}</div>
              )}
            </Col>
            <Col span={8}>
              <span>{t("editor.truongHienThi")} 2</span>
            </Col>
            <Col span={16}>
              <Input
                size="small"
                value={state.fieldDisplay2}
                onChange={changeInput("fieldDisplay2")}
              />
            </Col>
            <Col span={24}>
              <i>
                Nếu thiết lập trường hiển thị 2 thì trường hiển thị 1 sẽ hiện ở
                popup còn trường hiển thị 2 sẽ hiện sau khi chọn
              </i>
            </Col>
          </Row>
          <fieldset>
            <legend>
              <span>{t("editor.khaiBaoThamSo")}</span>
            </legend>
            <ul>
              {state.variable.map((item) => (
                <li key={item.key} className={"item-main"}>
                  <div className={"item-option"}>
                    <span className="option-label">{t("editor.thamSo")}:</span>
                    <Input
                      placeholder={t("editor.nhapTenThamSo")}
                      className="option-content"
                      style={{ flex: 1 }}
                      value={item.name}
                      onChange={updateVariable(item, "name")}
                      size={"small"}
                    />
                  </div>
                  <div className={"item-option"}>
                    <span className="option-label">{t("editor.giaTri")}:</span>
                    <FieldName
                      apiFields={apiFields}
                      value={item.value}
                      placeholder={t("editor.chonTruongDuLieu")}
                      onSelect={updateVariable(item, "value")}
                    />
                    <Button
                      icon={<DeleteOutlined />}
                      size={"small"}
                      onClick={removeVariable(item.key)}
                    />
                  </div>
                  <div className={"item-option"}>
                    <span className="option-label">{t("editor.tuUrl")}</span>
                    <Checkbox
                      onChange={updateVariable(item, "fromUrl")}
                      checked={item.fromUrl}
                    />
                  </div>
                </li>
              ))}
            </ul>
            <Button
              className={"add-btn"}
              icon={<PlusOutlined />}
              size={"small"}
              onClick={addVariable}
            />
          </fieldset>
          <fieldset>
            <legend className="flex">
              <div className="mr-5">{t("editor.themDieuKienHienThi")}</div>
              <Checkbox
                onChange={changeCheckbox("ruleDisplay")}
                checked={state.ruleDisplay}
              />
            </legend>
            <span>{t("editor.themDieuKienHienThiMoTa")}</span>
            {state.ruleDisplay && (
              <Row gutter={[12, 12]}>
                <Col span={8}>
                  <span>{t("editor.tenTruongDieuKien")}</span>
                </Col>
                <Col span={16}>
                  <Input
                    size="small"
                    value={state.ruleName}
                    onChange={changeInput("ruleName")}
                  />
                </Col>
                <Col span={8}>
                  <span>{t("editor.giaTriTruongDieuKien")}</span>
                </Col>
                <Col span={16}>
                  <Input
                    size="small"
                    value={state.ruleValue}
                    onChange={changeInput("ruleValue")}
                  />
                </Col>
              </Row>
            )}
          </fieldset>
          {state.listType == 1 && (
            <fieldset>
              <legend className="flex">
                <div className="mr-5">
                  {t("editor.thayDoiDuLieuTruongKhac")}
                </div>
                <Checkbox
                  onChange={changeCheckbox("onFieldChange")}
                  checked={state.onFieldChange}
                />
              </legend>
              <ul>
                {state.onFieldChange &&
                  state.changeFields.map((item) => (
                    <li key={item.key} className={"item-main"}>
                      <div className={"item-option"}>
                        <span className="option-label">
                          {t("editor.truongDuocThayDoi")}:
                        </span>
                        <FieldName
                          style={{ width: "100%" }}
                          value={item.targetField}
                          apiFields={apiFields}
                          onSelect={updateChangeFields(item, "targetField")}
                        />
                        <Button
                          icon={<DeleteOutlined />}
                          size={"small"}
                          onClick={removeChangeFields(item.key)}
                        />
                      </div>
                      <div className={"item-option"}>
                        <span className="option-label">
                          {t("editor.giaTriDuocChon")}:
                        </span>
                        <Input
                          className="option-content"
                          style={{ flex: 1 }}
                          value={item.sourceValue}
                          onChange={updateChangeFields(item, "sourceValue")}
                          size={"small"}
                        />
                      </div>
                    </li>
                  ))}
              </ul>

              <Button
                className={"add-btn"}
                icon={<PlusOutlined />}
                size={"small"}
                onClick={addChangeFields}
              />
            </fieldset>
          )}
        </>
      )}
    </Main>
  );
});

DropDownListProps.defaultProps = {
  state: {},
};

DropDownListProps.propTypes = {
  state: T.shape({}),
};

export default DropDownListProps;
