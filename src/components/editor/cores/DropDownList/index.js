import React, {
  useRef,
  useState,
  useEffect,
  forwardRef,
  useImperative<PERSON>andle,
  useContext,
  useMemo,
} from "react";
import T from "prop-types";
import { Main, GlobalStyles } from "./styled";
import TextEdit from "components/editor/cores/TextEdit";
import { useDispatch } from "react-redux";
import { getValueForm, MODE } from "utils/editor-utils";
import { message, Select } from "antd";
import { client } from "client/request";
import lodash, { cloneDeep, get, isArray } from "lodash";
import cacheUtils from "lib-utils/cache-utils";
import { EMR2Context, EMRContext, useEditor } from "components/editor/config";
import { containText, getUniqueData } from "utils";
import { SVG } from "assets";
import { useTranslation } from "react-i18next";
import { getAllQueryString } from "hooks/useQueryString/queryString";
import { useGuid, useEstimateLine } from "hooks";
import classNames from "classnames";
import mainamReactNativeStringUtils from "mainam-react-native-string-utils";
// import { refValues } from "pages/editor/report/components/File";
const dropDownListRef = React.createRef({});

export const refLoadingDroplistStatus = React.createRef({});
export const refDropDownListError = React.createRef({});

const DropDownList = forwardRef((props, ref) => {
  const idComponent = useGuid();

  const queries = getAllQueryString();
  const { t } = useTranslation();
  const [state, _setState] = useState({
    disable: false,
    itemFocused: {},
    values: [],
    localCheckList: [],
    listOptionFromApi: [],
    showIconClear: false,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const refContainer = useRef(null);

  const refTimeout = useRef();
  const refCheckAll = useRef(false);
  const context = useContext(EMRContext);
  const {
    component: { init },
    files: { updateEditor },
  } = useDispatch();

  const { editorId } = useContext(EMR2Context);
  const signStatus = useEditor(editorId, "signStatus", {});

  if (!dropDownListRef.current) {
    dropDownListRef.current = {};
  }
  if (!refLoadingDroplistStatus.current) {
    refLoadingDroplistStatus.current = {};
  }
  if (!refDropDownListError.current) {
    refDropDownListError.current = {};
  }

  const {
    component,
    mode,
    formChange,
    form,
    //value emr global sử dụng trong replication row khi replication row truyền vào là form chính là valuerow
    //values = valueEMR khi không nằm trong replication row
    valueEMR,
    valuesHIS,
    textTransform,
    blockWidth,
  } = props;

  const labelRef = useRef(null);
  const mainRef = useRef(null);
  const label = mode === MODE.config ? "label" : "";
  const itemProps = component.props || {};

  const lineHeightText =
    (((itemProps.fontSize || props.fontSize) * 4) / 3) *
    (itemProps.lineHeight || 1.5);
  let minHeight = itemProps.line
    ? itemProps.line * lineHeightText
    : lineHeightText;

  const { lineCount, lineHeight } = useEstimateLine({
    extentLine: 0,
    lineHeightText: lineHeightText,
    containerRef: refContainer,
    getComputeElement: () => {
      return mainRef.current.querySelector(
        ".ant-select-selection-item-content"
      );
    },
  });

  useEffect(() => {
    const isDisable = context.isDisable;
    let disable = isDisable && isDisable({ itemProps, signStatus, props });
    if (state.disable != disable) {
      setState({
        disable,
      });
    }
  }, [signStatus, itemProps, props.disable]);

  useImperativeHandle(ref, () => ({
    collectCheckList: () => {
      let newLocalCheckList = (state.localCheckList || []).map((item) => {
        item.label = item.labelValue;
        return item;
      });
      return newLocalCheckList;
    },
    collectLabel: () => {
      return state.labelValue;
    },
  }));

  const handleFocusItem = (item) => () => {
    setState({
      itemFocused: item,
    });
  };
  let refTimeoutCallData = useRef();
  useEffect(() => {
    if (itemProps) {
      //nếu loại là dữ liệu tự chọn
      if (!itemProps.listType || itemProps.listType == 2) {
        setState({
          localCheckList: itemProps.checkList,
          labelValue: itemProps.label || itemProps.labelValue,
        });
      } else if (itemProps.listType == 3) {
        const newData = {
          labelValue: itemProps.label || itemProps.labelValue,
        };
        if (Object.keys(form || {}).length) {
          newData.localCheckList = getValueForm(form, itemProps.fieldName);
        }
        setState({
          ...newData,
        });
      } else {
        setState({
          labelValue: itemProps.label || itemProps.labelValue,
        });

        if (refTimeoutCallData.current)
          clearTimeout(refTimeoutCallData.current);
        refTimeoutCallData.current = setTimeout(() => {
          getData();
        }, 500);
      }
    }
  }, [JSON.stringify(itemProps), form?.id, formChange]);

  useEffect(() => {
    return () => {
      if (refLoadingDroplistStatus.current?.hasOwnProperty(idComponent))
        delete refLoadingDroplistStatus.current[idComponent];
      if (refDropDownListError?.hasOwnProperty(idComponent)) {
        delete refDropDownListError.current[idComponent];
      }
    };
  }, []);

  const mapDataFromApi = (data = [], itemProps) => {
    if (!data?.length || !itemProps) return [];

    const { fieldDisplay, fieldValue, margeDataDisplay, fieldDisplay2 } =
      itemProps;

    // Cache function references
    const getValue = margeDataDisplay
      ? convertValue
      : (params) => lodash.get(params.obj, params.param, "");

    // Sử dụng array buffer với kích thước đã biết trước
    const result = new Array(data.length);

    // Dùng for loop thay vì map để tối ưu performance
    for (let i = 0; i < data.length; i++) {
      const el = data[i];
      const label = getValue({ param: fieldDisplay, obj: el });
      const labelDisPlay = getValue({ param: fieldDisplay2, obj: el });
      result[i] = {
        ...el,
        value: `${lodash.get(el, fieldValue, "")}`,
        label,
        labelDisPlay: fieldDisplay2 ? labelDisPlay : null,
      };
    }

    return result;
    // Early return nếu không có data hoặc itemProps
  };
  const getData = async () => {
    if (mode == MODE.config) return;
    let api = itemProps.dataApi + "";
    try {
      const listVariable = itemProps.variable || []; //lấy danh sách các tham số đã khai báo
      listVariable.forEach((variable) => {
        //duyệt qua danh sách các tham số
        if (variable.value) {
          let value = "";

          if (!variable.fromUrl) {
            value = queries[variable.value];
          } else {
            value = form[variable.value]; //lấy giá trị tương ứng với tham số
            if (value === undefined) {
              //nếu không có dữ liệu thì lấy từ valueEMR url
              value = (queries || {})[variable.value] || "";
            }
          }
          api = api.replaceAll(`{${variable?.name || ""}}`, value); //replace tham số với giá trị nhận được
          //tham số truyền vào api sẽ có rule như sau: {tenThamSo}
        }
      });
      refLoadingDroplistStatus.current[idComponent] = true;

      //sau khi replace xong theo tham số khai báo mà vẫn còn các tham số thì sẽ lấy mặc định
      //các dữ liệu sẽ được replace theo dữ liệu của patient
      api = context.fillDataToParams ? context.fillDataToParams(api) : api;
      let dataCache = await cacheUtils.read("DROPDOWNLIST", api, null, false);
      if (dataCache) {
        const listOptionFromApi = mapDataFromApi(dataCache, itemProps);
        refLoadingDroplistStatus.current[idComponent] = false;
        setState({
          listOptionFromApi,
          labelValue: itemProps.label || itemProps.labelValue,
        });
      }

      if (!dropDownListRef.current?.[api]) {
        dropDownListRef.current[api] = {};
        const s = await client.get(api);
        if (s?.data?.code == 0) {
          // Sử dụng
          const data = getUniqueData(s?.data?.data || [], itemProps.fieldValue);
          cacheUtils.save("DROPDOWNLIST", api, data, false);
          const listOptionFromApi = mapDataFromApi(data, itemProps);
          refLoadingDroplistStatus.current[idComponent] = false;
          if (!dataCache) {
            setState({
              listOptionFromApi,
            });
          }
          Object.keys(dropDownListRef.current[api]).forEach((key) => {
            if (key !== itemProps.fieldName) {
              dropDownListRef.current[api][key](listOptionFromApi);
            }
          });
        }
      } else {
        dropDownListRef.current[api][itemProps.fieldName] = (data) => {
          refLoadingDroplistStatus.current[idComponent] = false;

          const listOptionFromApi = mapDataFromApi(data, itemProps);

          setState({
            listOptionFromApi,
          });
        };
      }
    } catch (error) {
      message.error(
        ` ${itemProps.label ? `${itemProps.label}: ` : ""} ${
          error?.message
        } ${api}`
      );
      refDropDownListError.current[idComponent] = {
        api,
        label: itemProps.label,
        message: error.message,
      };
    }
  };

  useEffect(() => {
    let values = getValueForm(form, itemProps.fieldName);
    if (!values && values !== 0) {
      values = [];
    }
    if (itemProps.ruleDisplay) {
      if (Array.isArray(values)) {
        values = values
          .filter(
            (item) => get(item, itemProps.ruleName) + "" == itemProps.ruleValue
          )
          .map((item) => {
            return get(item, itemProps.fieldValue, "") + "";
          });
        values = Array.from(new Set(values));
      }
    } else {
      if (Array.isArray(values)) {
        if (itemProps.type === "onlyOne") {
          values = values.map((item) => item + "")[0] || "";
        } else {
          values = values.map((item) => item + "");
        }
      } else {
        values = values + "";
      }
    }
    if (!values.length) {
      if (itemProps.defaultValues) {
        if (itemProps.type === "onlyOne") {
          if (itemProps.dataIsArray) {
            values = [itemProps.defaultValues];
          } else {
            values = itemProps.defaultValues;
          }
        } else {
          values = itemProps.defaultValues.split(",");
        }
        if (formChange && formChange[itemProps.fieldName])
          formChange[itemProps.fieldName](values);
      }
    }

    setState({
      values,
    });
  }, [form, itemProps, formChange]);

  const handleBlueItem = () => {
    setState({
      itemFocused: {},
    });
  };

  const handleFocus = () => {
    if (mode === "config") {
      init(component);
    }
  };

  const dataList = useMemo(() => {
    if ([2, 3].includes(itemProps.listType)) {
      if (itemProps.type === "onlyOne") {
        return state.localCheckList || [];
      } else {
        if (itemProps.listType === 2) {
          return [
            { label: t("editor.tatCa"), value: "all" },
            ...(state.localCheckList || []),
          ];
        } else {
          return state.localCheckList || [];
        }
      }
    } else {
      if (itemProps.type === "onlyOne") {
        return state.listOptionFromApi || [];
      } else {
        return [
          {
            label: t("editor.tatCa"),
            value: "all",
          },
          ...(state.listOptionFromApi || []),
        ];
      }
    }
  }, [state.listOptionFromApi, state.localCheckList]);

  const handleOnChange = (value) => {
    if (state.disable) {
      return;
    }

    if (itemProps.type === "onlyOne") {
      if (itemProps.fieldName && formChange[itemProps.fieldName]) {
        const newValue = isArray(value) ? value.pop() : value;
        if (state.values.includes(newValue)) {
          setState({
            values: [],
          });
          itemProps.fieldName && formChange[itemProps.fieldName]("");
        } else {
          setState({
            values: [newValue],
          });

          //  Nếu là là dữ liệu từ trong form thì mặc định đẩy lên là mảng
          if (itemProps.listType === 3) {
            const listData = state.localCheckList.map((item) => {
              const value =
                itemProps.ruleValue === "true" ? true : itemProps.ruleValue;
              return {
                ...item,
                [itemProps.ruleName]:
                  item[itemProps.fieldValue] + "" == newValue ? value : null,
              };
            });
            formChange[itemProps.fieldName](listData);
          } else {
            if (itemProps.dataIsArray) {
              formChange[itemProps.fieldName]([newValue]);
            } else {
              formChange[itemProps.fieldName](newValue);
              if (itemProps.onFieldChange && itemProps.targetField) {
                const itemSelected = state.listOptionFromApi.find(
                  (el) => el.value == newValue
                );
                const valueChangeKey = get(
                  itemSelected,
                  `${itemProps.sourceValue}`
                );
                const {
                  refValues,
                } = require("pages/editor/report/components/File");
                formChange[itemProps.targetField](valueChangeKey);
                refValues.current[itemProps.targetField] = valueChangeKey;
                const uniqKey = mainamReactNativeStringUtils.guid();

                updateEditor(editorId, {
                  fileData: cloneDeep(refValues.current),
                  uniqKey,
                });
              }
              if (
                itemProps.onFieldChange &&
                itemProps.changeFields?.length > 0
              ) {
                itemProps.changeFields.forEach((field) => {
                  const itemSelected = state.listOptionFromApi.find(
                    (el) => el.value == newValue
                  );
                  const valueChangeKey = get(
                    itemSelected,
                    `${field.sourceValue}`
                  );
                  const {
                    refValues,
                  } = require("pages/editor/report/components/File");
                  formChange[field.targetField](valueChangeKey);
                  refValues.current[field.targetField] = valueChangeKey;
                  const uniqKey = mainamReactNativeStringUtils.guid();

                  updateEditor(editorId, {
                    fileData: cloneDeep(refValues.current),
                    uniqKey,
                  });
                });
              }
            }
          }
        }
      }
    } else {
      let newValue = value;
      if (value.includes("all")) {
        if (refCheckAll.current) {
          refCheckAll.current = false;
          newValue = [];
        } else {
          refCheckAll.current = true;
          newValue = dataList
            .map((item) =>
              itemProps.listType === 2
                ? item.value + ""
                : itemProps.margeDataDisplay
                ? item["value"]
                : getValueForm(item, itemProps.fieldValue) + ""
            )
            .filter((item) => item !== "all");
        }
      }
      if (itemProps.fieldName && formChange[itemProps.fieldName]) {
        setState({
          values: newValue,
        });
        if (itemProps.fieldName && formChange[itemProps.fieldName]) {
          if (itemProps.listType === 3) {
            const listData = state.localCheckList.map((item) => {
              const value =
                itemProps.ruleValue === "true" ? true : itemProps.ruleValue;
              return {
                ...item,
                [itemProps.ruleName]: newValue.includes(
                  item[itemProps.fieldValue] + ""
                )
                  ? value
                  : null,
              };
            });
            formChange[itemProps.fieldName](listData);
          } else {
            formChange[itemProps.fieldName](newValue);
            if (itemProps.onFieldChange) {
              const itemSelected = state.listOptionFromApi.filter(
                (el) => el.value == newValue
              );
              const valueChangeKey = itemSelected.map((el) =>
                get(el, `${itemProps.sourceValue}`)
              );
              const {
                refValues,
              } = require("pages/editor/report/components/File");
              refValues.current[itemProps.targetField] = valueChangeKey;
              updateEditor(editorId, {
                fileData: cloneDeep(refValues.current),
              });

              (itemProps.changeFields || []).forEach((field) => {
                const itemSelected = state.listOptionFromApi.filter(
                  (el) => el.value == newValue
                );
                const valueChangeKey = itemSelected.map((el) =>
                  get(el, `${field.sourceValue}`)
                );
                const {
                  refValues,
                } = require("pages/editor/report/components/File");
                refValues.current[field.targetField] = valueChangeKey;
                updateEditor(editorId, {
                  fileData: cloneDeep(refValues.current),
                });
              });
            }
          }
        }
      }
    }
  };
  const handleClear = (e) => {
    e.stopPropagation();
    e.preventDefault();
    setState({
      values: [],
    });
    itemProps.fieldName && formChange[itemProps.fieldName](null);
  };

  const convertValue = ({ param = "", obj }) => {
    if (!param) return "";

    // Sử dụng regex để tìm tất cả các mẫu {key}
    const replaced = param.replace(/\{([^}]+)\}/g, (_, key) =>
      lodash.get(obj, key, "")
    );
    return replaced
      .split("-")
      .filter((e) => e && e !== " ")
      .join("-");
  };

  const filterOption = (input, option) => {
    return containText(option?.props?.children, input);
  };

  const text = useMemo(() => {
    return (state.localCheckList || []).find(
      (item) => item.value == state.values
    )?.labelValue;
  }, [state.localCheckList, state.values]);

  const options = useMemo(() => {
    return {
      contentColor: itemProps.contentColor || "black",
      popupClassName:
        itemProps.fieldName + `-${Math.trunc(Math.random() * 1000)}`,
    };
  }, [itemProps.fieldName]);
  const handleFocusSelect = () => {
    mainRef.current.querySelector(
      ".ant-select-selection-search"
    ).style.display = "block";
    setState({
      showIconClear: true,
    });
  };

  const handleBlurSelect = () => {
    setTimeout(() => {
      mainRef.current.querySelector(
        ".ant-select-selection-search"
      ).style.display = "none";
      setState({
        showIconClear: false,
      });
    }, 500);
  };

  const showMarkSpanRow =
    itemProps.markSpanRow === undefined ? true : itemProps.markSpanRow;
  const isHideLineMark = state.disable && !showMarkSpanRow;

  const renderDottedLines = () => {
    if (isHideLineMark) return null;
    return (
      <div
        ref={refContainer}
        className={classNames("dotted-lines", {
          "show-print-mark-span-row": showMarkSpanRow,
        })}
        style={{
          position: "absolute",
          inset: 0,
          pointerEvents: "none",
          zIndex: 1,
          display: "flex",
          flexDirection: "column",
          width: "100%",
          height: "100%",
        }}
      >
        {Array(lineCount)
          .fill(null)
          .map((_, index) => (
            <div
              key={index}
              style={{
                height: lineHeight,
                lineHeight: lineHeight ? lineHeight - 2 + "px" : undefined,
                width: "100%",
                flexShrink: 0,
                paddingTop: 7,
                overflow: "hidden",
                fontSize: "8pt",
                wordBreak: "break-word",
                overflowWrap: "break-word",
                whiteSpace: "nowrap",
                fontWeight: "normal",
                letterSpacing: "3px",
                opacity: showMarkSpanRow ? 0.6 : 0.3,
              }}
            >
              {".".repeat(300)}
            </div>
          ))}
      </div>
    );
  };

  return (
    <>
      <GlobalStyles
        contentColor={options.contentColor}
        popupClassName={options.popupClassName}
        widthPopup={itemProps.widthPopup}
      />
      <Main
        ref={mainRef}
        onClick={handleFocus}
        itemProps={itemProps}
        hadLabel={!!itemProps.label}
        data-type="dropdownlist"
        showMarkSpanRow={showMarkSpanRow}
        lineHeight={lineHeightText}
        minHeight={minHeight}
        fontSize={itemProps.fontSize || props.fontSize} //parrent font size
        contentAlign={itemProps.contentAlign || "left"}
        mode={mode}
        disabled={state.disable}
        minRow={itemProps.minRow || 1} //số dòng mặc định
        readOnly={itemProps.readOnly}
        fontWeight={itemProps.bold ? "bold" : ""}
        fontStyle={itemProps.italic ? "italic" : ""}
        textDecoration={itemProps.underline ? "underline" : ""}
        contentColor={itemProps.contentColor || "black"}
        values={state.values?.length}
        className={itemProps.printHide ? "hidden-element" : ""}
      >
        {renderDottedLines()}
        {!itemProps.noLabel && (
          <TextEdit
            id={`${component.type}_${component.key}`}
            className={"text-field-label"}
            defaultValue={itemProps.label || label}
            ref={labelRef}
            mode={mode}
            onChange={(value) => {
              setState({
                labelValue: value,
              });
            }}
            textTransform={textTransform}
            width={itemProps.labelWidth}
            disabled={false} //allow edit in config mode
          />
        )}
        {itemProps.displayText ? (
          <div className="text-select">
            {mode !== MODE.config ? text : "text"}
          </div>
        ) : (
          <>
            {[1, 3].includes(itemProps.listType) ||
            state.localCheckList?.length > 0 ? (
              <>
                <Select
                  dropdownStyle={props.dropdownStyle}
                  onChange={handleOnChange}
                  disabled={state.disable}
                  style={{ width: blockWidth, ...props.style }}
                  mode={itemProps.choPhepNhapChu ? "tags" : "multiple"}
                  value={state.values}
                  showSearch
                  onFocus={handleFocusSelect}
                  onBlur={handleBlurSelect}
                  filterOption={filterOption}
                  popupClassName={options.popupClassName}
                  onClick={handleFocusSelect}
                  optionLabelProp="label"
                  dropdownMatchSelectWidth={
                    itemProps.dropdownMatchSelectWidth || 300
                  }
                >
                  {itemProps.listType == 2
                    ? dataList.map((item) => {
                        return (
                          <Select.Option
                            key={item.key}
                            value={item.value + ""}
                            label={item.labelValue || item.label || ""}
                          >
                            {item.labelDisplay || item.label || ""}
                          </Select.Option>
                        );
                      })
                    : dataList.map((item, index) => {
                        return (
                          <Select.Option
                            key={item.value}
                            value={item.value}
                            label={`${item.labelDisPlay || item.label}`}
                          >
                            {item.label}
                          </Select.Option>
                        );
                      })}
                </Select>
                {!state.disable &&
                !itemProps.readOnly &&
                state.showIconClear &&
                state?.values.length !== 0 ? (
                  <SVG.IcCancel
                    className={`icon-clear`}
                    onClick={handleClear}
                  />
                ) : null}
              </>
            ) : (
              <Select />
            )}
          </>
        )}
      </Main>
    </>
  );
});

DropDownList.defaultProps = {
  form: {},
  component: {
    props: {
      checkList: [],
      direction: "ltr",
    },
  },
  line: {},
  disabled: false,
  mode: MODE.config,
};

DropDownList.propTypes = {
  form: T.shape({}),
  component: T.shape({}),
  line: T.shape({}),
  updateContent: T.func,
  disabled: T.bool,
  mode: T.string,
};

export default DropDownList;
