import React, {
  forwardRef,
  Fragment,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { cloneDeep, get } from "lodash";
import { Popover } from "components";
import { DS_CHUAN_DOAN_BY_VAN_DE, DS_VAN_DE } from "../constants";
import { SVG } from "assets";
import { PopoverStyled } from "../styled";
import PopoverSelect from "../../Components/PopoverSelect";
import { DeboundInput } from "module_html_editor/Components";
import { Checkbox } from "antd";
const STATUS = {
  ACTIVE: 1,
  INACTIVE: 0,
};

const LIST_KEY_CHANGE = Object.values(DS_CHUAN_DOAN_BY_VAN_DE).map((el) => ({
  key: el.key,
  dsKey: el.dsKey,
}));
const RenderChanDoan = (
  { data, refValue, formChangeValue, arr, itemProps },
  ref
) => {
  const [listTr, setListTr] = useState(DS_VAN_DE);
  const [listTrCanThiep, setListTrCanThiep] = useState(DS_VAN_DE);
  const [dataCheckbox, setDataCheckbox] = useState([]);
  const refTd = useRef({});
  useEffect(() => {
    const listData = DS_VAN_DE.map((el) => {
      return {
        ...el,
        status: (refValue.current?.vanDe || []).includes(el.stt)
          ? STATUS.ACTIVE
          : STATUS.INACTIVE,
      };
    });
    const listDataCanThiep = DS_VAN_DE.map((el) => {
      return {
        ...el,
        status: (refValue.current?.canThiepDieuDuong || []).includes(el.stt)
          ? STATUS.ACTIVE
          : STATUS.INACTIVE,
      };
    });
    setListTrCanThiep(listDataCanThiep);
    setListTr(listData);
    setDataCheckbox(data?.mucTieu || []);
  }, [data]);
  const onClick = (index) => () => {
    if (listTr[index]?.status) {
      listTr[index].status = STATUS.INACTIVE;
    } else {
      listTr[index].status = STATUS.ACTIVE;
    }
    const vanDe = listTr
      .filter((el) => el.status === STATUS.ACTIVE)
      .map((el) => el.stt);
    refValue.current["vanDe"] = vanDe;
    formChangeValue();
    setListTr(cloneDeep(listTr));
  };

  const onClickCanThiep = (index) => () => {
    if (listTrCanThiep[index]?.status) {
      listTrCanThiep[index].status = STATUS.INACTIVE;
    } else {
      listTrCanThiep[index].status = STATUS.ACTIVE;
    }

    const canThiepDieuDuong = listTrCanThiep
      .filter((el) => el.status === STATUS.ACTIVE)
      .map((el) => el.stt);
    refValue.current["canThiepDieuDuong"] = canThiepDieuDuong;
    formChangeValue();
    setListTrCanThiep(cloneDeep(listTrCanThiep));
  };

  useImperativeHandle(
    ref,
    () => ({
      changeKey: (key, colIndex, value) => {
        const index = LIST_KEY_CHANGE.findIndex((el) =>
          el.dsKey
            ? el.dsKey.some((e) => key.includes(e))
            : key.includes(el.key)
        );
        if (index >= 0) {
          const dsChanDoan = DS_CHUAN_DOAN_BY_VAN_DE[index + 1].dsChanDoan;
          const dsKeHoach = DS_CHUAN_DOAN_BY_VAN_DE[index + 1].dsKeHoach;
          const dsNoiDungChamSoc =
            DS_CHUAN_DOAN_BY_VAN_DE[index + 1].dsNoiDungChamSoc;
          const valuesDsChanDoan = dsChanDoan
            .map((el) => el.listNoiDung)
            .flat(Infinity);
          const valuesDsMucTieu = dsKeHoach
            .map((el) => el.listNoiDung)
            .flat(Infinity);
          const valuesDsNoiDung = dsNoiDungChamSoc
            .map((el) => el.listNoiDung)
            .flat(Infinity);
          const indexDsVanDe = (
            refValue.current.dsChiTiet[colIndex]["dsVanDe"] || []
          ).findIndex((el) => el.stt === index + 1);
          const indexDsCanThiep = (
            refValue.current.dsChiTiet[colIndex]["dsCanThiep"] || []
          ).findIndex((el) => el.stt === index + 1);

          if (refTd.current[`${index + 1}_${colIndex}_${1}`]) {
            refTd.current[`${index + 1}_${colIndex}_${1}`].setData(
              valuesDsChanDoan.map((i) => ({
                value: i.id,
                label: i.label,
              }))
            );
            refTd.current[`${index + 1}_${colIndex}_${2}`].setData(
              valuesDsMucTieu.map((i) => ({
                value: i.id,
                label: i.label,
              }))
            );
            if (refTd.current[`${index + 1}_${colIndex}_${3}`]) {
              refTd.current[`${index + 1}_${colIndex}_${3}`].setData(
                valuesDsNoiDung.map((i) => ({
                  value: i.id,
                  label: i.label,
                }))
              );
            }
          }
          if (indexDsCanThiep >= 0) {
            const valuesDsCanThiepCurrent = get(
              refValue.current.dsChiTiet,
              `[${colIndex}]["dsCanThiep"][${indexDsCanThiep}]`
            );
            valuesDsCanThiepCurrent.mucTieu =
              valuesDsCanThiepCurrent.mucTieu.filter((el) =>
                valuesDsNoiDung.some((i) => i.id === el)
              );
          }
          if (indexDsVanDe >= 0) {
            const valuesDsVanDeCurrent =
              refValue.current.dsChiTiet[colIndex]["dsVanDe"][indexDsVanDe];

            valuesDsVanDeCurrent.vanDe = valuesDsVanDeCurrent.vanDe.filter(
              (el) => valuesDsChanDoan.some((i) => i.id === el)
            );
            valuesDsVanDeCurrent.mucTieu = valuesDsVanDeCurrent.mucTieu.filter(
              (el) => valuesDsChanDoan.some((i) => i.id === el)
            );
          }
          if (!listTrCanThiep[index].status && valuesDsNoiDung?.length) {
            listTrCanThiep[index].status = STATUS.ACTIVE;
            setListTrCanThiep(cloneDeep(listTrCanThiep));
          }
          if (
            !listTr[index].status &&
            (valuesDsMucTieu.length || valuesDsChanDoan.length)
          ) {
            listTr[index].status = STATUS.ACTIVE;
            setListTr(cloneDeep(listTr));
          }
          refValue.current.canThiepDieuDuong = listTrCanThiep
            .filter((el) => el.status)
            .map((el) => el.stt);
          refValue.current.vanDe = listTr
            .filter((el) => el.status)
            .map((el) => el.stt);
          formChangeValue();
        }
      },
    }),
    [listTr]
  );

  const onChangeInput = (key, idx) => (value) => {
    refValue.current["dsChiTiet"][idx][`${key}`] = value;
    formChangeValue();
  };

  const onChangeCheckbox = (index) => (e) => {
    let data = cloneDeep(dataCheckbox);
    if (e?.target?.checked) {
      data.push(index + 1);
    } else {
      data = data.filter((x) => x !== index + 1);
    }
    refValue.current.mucTieu = data;
    setDataCheckbox(data);
    formChangeValue();
  };
  return (
    <>
      <tr>
        <td colSpan={3}>
          <div>
            <div className="flex" style={{ alignItems: "center" }}>
              <b> Chẩn đoán ĐD</b>
              <Popover
                title={"Danh sách chẩn đoán"}
                content={
                  <PopoverStyled>
                    {DS_VAN_DE.map((vanDe, index) => {
                      const isActive = listTr.find(
                        (el) => el.stt === vanDe.stt && el.status
                      );
                      return (
                        <div
                          key={vanDe.key}
                          onClick={onClick(index)}
                          className={`item ${isActive ? "active" : ""}`}
                        >
                          <div style={{ flex: 1 }}>Vấn đề {vanDe.key}</div>
                          {isActive && (
                            <SVG.IcDelete className={"icon-remove"} />
                          )}
                        </div>
                      );
                    })}
                  </PopoverStyled>
                }
                trigger="click"
              >
                <SVG.IcAdd className="icon-add hide-print-btn" />
              </Popover>
            </div>
          </div>
        </td>
        <td></td>
        <td></td>
        <td></td>
      </tr>
      {listTr
        .filter((el) => el.status)
        .map((itemTr, index) => (
          <Fragment key={itemTr.stt}>
            <tr>
              <td colSpan={3}>Vấn đề {itemTr.key}</td>
              {arr.map((itemTd, indexTd) => {
                return (
                  <RenderTd
                    stt={itemTr.stt}
                    type={1}
                    colIndex={indexTd}
                    data={data}
                    key={indexTd}
                    refValue={refValue}
                    refTd={refTd}
                    listTr={listTr}
                    formChangeValue={formChangeValue}
                  ></RenderTd>
                );
              })}
            </tr>
            <tr>
              <td colSpan={3}>
                Mục tiêu{" "}
                <Checkbox
                  checked={dataCheckbox?.includes(index + 1) ? true : false}
                  onChange={onChangeCheckbox(index)}
                />
              </td>
              {arr.map((itemTd, indexTd) => {
                return (
                  <RenderTd
                    key={indexTd}
                    stt={itemTr.stt}
                    type={2}
                    colIndex={indexTd}
                    data={data}
                    refTd={refTd}
                    refValue={refValue}
                    listTr={listTr}
                    formChangeValue={formChangeValue}
                  ></RenderTd>
                );
              })}
            </tr>
          </Fragment>
        ))}
      <tr>
        <td colSpan={3}>Chuẩn đoán điều dưỡng/Vấn đề chăm sóc khác</td>
        {arr.map((itemTd, idx) => {
          return (
            <td key={idx}>
              <DeboundInput
                readOnly={false}
                value={get(
                  refValue.current,
                  `dsChiTiet[${idx}]["ctVanDeChamSoc"]`
                )}
                onChange={onChangeInput("ctVanDeChamSoc", idx)}
                type="multipleline"
                lineHeightText={1.5}
                fontSize={9}
                minHeight={9 + 6}
                markSpanRow={false}
                contentAlign="center"
              ></DeboundInput>
            </td>
          );
        })}
      </tr>

      <tr>
        <td colSpan={3}>Mục tiêu khác</td>
        {new Array(4).fill({}).map((itemTd, idx) => {
          return (
            <td key={idx}>
              <DeboundInput
                readOnly={false}
                value={get(
                  refValue.current,
                  `dsChiTiet[${idx}]["mucTieuKhac"]`
                )}
                onChange={onChangeInput("mucTieuKhac", idx)}
                type="multipleline"
                lineHeightText={1.5}
                fontSize={9}
                minHeight={9 + 6}
                markSpanRow={false}
                contentAlign="center"
              ></DeboundInput>
            </td>
          );
        })}
      </tr>
      <tr>
        <td colSpan={3}>
          <div>
            <div className="flex" style={{ alignItems: "center" }}>
              <b> Can thiệp điều dưỡng</b>
              {itemProps.themDuLieuCanThiepDieuDuong && (
                <Popover
                  title={"Can thiệp điều dưỡng"}
                  content={
                    <PopoverStyled>
                      {DS_VAN_DE.map((vanDe, index) => {
                        const isActive = listTrCanThiep.find(
                          (el) => el.stt === vanDe.stt && el.status
                        );
                        return (
                          <div
                            key={vanDe.key}
                            onClick={onClickCanThiep(index)}
                            className={`item ${isActive ? "active" : ""}`}
                          >
                            <div style={{ flex: 1 }}>Can thiệp {vanDe.key}</div>
                            {isActive && (
                              <SVG.IcDelete className={"icon-remove"} />
                            )}
                          </div>
                        );
                      })}
                    </PopoverStyled>
                  }
                  trigger="click"
                >
                  <SVG.IcAdd className="icon-add hide-print-btn" />
                </Popover>
              )}
            </div>
          </div>
        </td>
        <td></td>
        <td></td>
        <td></td>
      </tr>
      {listTrCanThiep
        .filter((el) => el.status)
        .map((itemTr) => (
          <Fragment key={itemTr.stt}>
            <tr>
              <td colSpan={3}>Can thiệp {itemTr.key}</td>
              {new Array(4).fill(cloneDeep({})).map((itemTd, indexTd) => {
                return (
                  <RenderTd
                    stt={itemTr.stt}
                    type={3}
                    colIndex={indexTd}
                    data={data}
                    key={indexTd}
                    refValue={refValue}
                    refTd={refTd}
                    listTr={listTrCanThiep}
                    formChangeValue={formChangeValue}
                  ></RenderTd>
                );
              })}
            </tr>
          </Fragment>
        ))}
    </>
  );
};
const RenderTd = ({
  stt,
  type,
  colIndex,
  refValue,
  refTd,
  listTr,
  formChangeValue,
}) => {
  const [value, setValue] = useState([]);
  const [data, setData] = useState([]);
  refTd.current[`${stt}_${colIndex}_${type}`] = {
    setValue: (e) => {
      setValue(e);
    },
    setData: (e) => {
      setData(e);
    },
  };

  useEffect(() => {
    const dsVanDe = get(refValue.current, `dsChiTiet[${colIndex}].dsVanDe`, []);
    const dsCanThiep = get(
      refValue.current,
      `dsChiTiet[${colIndex}].dsCanThiep`,
      []
    );

    const value = (dsVanDe || []).find((el) => el.stt == stt) || {};
    const valueCanThiep = (dsCanThiep || []).find((el) => el.stt == stt) || {};
    switch (type) {
      case 1:
        setValue(value?.vanDe);
        break;
      case 2:
        setValue(value?.mucTieu);
        break;

      default:
        setValue(valueCanThiep?.mucTieu);
        break;
    }
  }, [stt, type, listTr]);

  const dsChanDoan = stt
    ? DS_CHUAN_DOAN_BY_VAN_DE[stt].dsChanDoan
        .reduce((a, b) => {
          a.push(...(b?.listNoiDung || []));
          return a;
        }, [])
        .map((el) => ({
          value: el.id,
          label: el.label,
        }))
    : [];
  const dsKeHoach = stt
    ? DS_CHUAN_DOAN_BY_VAN_DE[stt].dsKeHoach
        .reduce((a, b) => {
          a.push(...(b?.listNoiDung || []));
          return a;
        }, [])
        .map((el) => ({
          value: el.id,
          label: el.label,
        }))
    : [];
  const dsNoiDungChamSoc = stt
    ? DS_CHUAN_DOAN_BY_VAN_DE[stt].dsNoiDungChamSoc
        .reduce((a, b) => {
          a.push(...(b?.listNoiDung || []));
          return a;
        }, [])
        .map((el) => ({
          value: el.id,
          label: el.label,
        }))
    : [];

  const onChangeInput = (value) => {
    const dsVanDe = refValue.current.dsChiTiet[colIndex].dsVanDe || [];
    const indexDsVanDe = dsVanDe.findIndex((el) => el.stt == stt);
    switch (type) {
      case 1:
        if (indexDsVanDe >= 0) {
          dsVanDe[indexDsVanDe] = {
            ...dsVanDe[indexDsVanDe],
            vanDe: value,
          };
        } else {
          dsVanDe.push({
            stt: stt,
            vanDe: value,
            mucTieu: [],
          });
        }
        break;
      case 2:
        if (indexDsVanDe >= 0) {
          dsVanDe[indexDsVanDe] = {
            ...dsVanDe[indexDsVanDe],
            mucTieu: value,
          };
        } else {
          dsVanDe.push({
            stt: stt,
            vanDe: [],
            mucTieu: value,
          });
        }
        break;

      default:
        if (!refValue.current.dsChiTiet[colIndex].dsCanThiep) {
          refValue.current.dsChiTiet[colIndex].dsCanThiep = [];
        }
        const dsCanThiep = refValue.current.dsChiTiet[colIndex].dsCanThiep;
        const indexDsCanThiep = dsCanThiep.findIndex((el) => el.stt == stt);
        if (indexDsCanThiep >= 0) {
          dsCanThiep[indexDsCanThiep] = {
            ...dsCanThiep[indexDsCanThiep],
            mucTieu: value,
          };
        } else {
          dsCanThiep.push({
            stt: stt,
            vanDe: [],
            mucTieu: value,
          });
        }
        break;
    }
    formChangeValue();
    setValue(value);
  };
  const getData = () => {
    switch (type) {
      case 1:
        return dsChanDoan;
      case 2:
        return dsKeHoach;
      default:
        return dsNoiDungChamSoc;
    }
  };
  return (
    <td>
      <PopoverSelect
        data={
          [12, 13, 15].includes(stt)
            ? getData()
            : data?.length
            ? data
            : getData()
        }
        value={value}
        onChangeValue={onChangeInput}
        isMultiple={true}
        trigger="click"
        isShowSearch={true}
      ></PopoverSelect>
    </td>
  );
};

RenderChanDoan.propTypes = {};

export default forwardRef(RenderChanDoan);
