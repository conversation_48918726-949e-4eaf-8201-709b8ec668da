import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { TR } from "../constants";
import { cloneDeep, get } from "lodash";
import { DeboundInput } from "components/editor/config";
import AppDatePicker from "../../DatePicker";
import { updateObject } from "utils";
import { Button, Col, message, Popover, Row } from "antd";
import CheckGroups from "../../CheckGroups";
import { useQueryString } from "hooks";
import { SVG } from "assets";
import PopoverSelect from "../../Components/PopoverSelect";
import RenderChanDoan from "./RenderChanDoanDieuDuong";
import ImageSign from "../../ImageSign";
import { combineFields } from "utils/editor-utils";
import RenderNhapXuat from "./RenderNhapXuat";
import CopyPasteCol from "../../Components/CopyPasteCol";
import nbChiSoSongProvider from "data-access/nb-chi-so-song-provider";
import { useTranslation } from "react-i18next";
import { refConfirm } from "app";
import moment from "moment";
import RenderGhiChuBanGiao from "./RenderChiChuBanGiao";
import HeaderFormChamSocC2PSHN from "../../Components/HeaderFormChamSocC2PSHN";
import { PlusOutlined } from "@ant-design/icons";
import { PopoverStyled } from "../styled";

const arr = new Array(4).fill(cloneDeep({}));

const RenderTable = ({
  item,
  mode,
  tableIndex,
  refValueForm,
  formChange,
  onRemove,
  itemProps,
  form,
  onAddTenTimThai,
  dataCopy,
  setDataCopy,
  refModalSinhHieu,
  toSo,
  handleAdd,
  index,
  showAdd,
}) => {
  const { t } = useTranslation();
  const [state, _setState] = useState({
    value: {},
  });
  const [rowSpanSinhTon, setRowSpanSinhTon] = useState(20);
  const [nbDotDieuTriId] = useQueryString("nbDotDieuTriId");
  const [khoaChiDinhId] = useQueryString("khoaChiDinhId");
  const TRUOC_DE_SAU_DE = [
    {
      id: 1,
      ten: "Trước đẻ",
    },
    {
      id: 2,
      ten: "Sau đẻ",
    },
  ];
  useEffect(() => {
    const row =
      (itemProps.loaiPhieu === 2 ? 20 : 5) +
      (form.dsTheoDoi?.[0]?.dsTenTimThai?.length || 0);
    setRowSpanSinhTon(row);
  }, [form?.dsTheoDoi, itemProps.loaiPhieu]);

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const refChanDoan = useRef();
  const refValue = useRef();
  const refBmi = useRef();
  const refTongXuat = useRef();
  const refTongNhap = useRef();

  useEffect(() => {
    refValue.current = cloneDeep(item || {});
    setState({
      value: refValue.current,
    });
  }, [item]);

  const onChangeHuyetAp = (index) => (value) => {
    if (value.split("/").length - 1 !== 1) {
      message.error("Nhập sai quy tắc. Nhập đúng ví dụ: 120/90 ");
      return;
    }
    const arr = value.split("/");
    const huyetApTamThu = +arr[0];
    const huyetApTamTruong = +arr[1];
    if (!refValue.current["dsChiTiet"][index]?.chiSoSong) {
      refValue.current["dsChiTiet"][index].chiSoSong = {
        chiDinhTuLoaiDichVu: 201,
        khoaChiDinhId: form.khoaChiDinhId,
        nbDotDieuTriId: form.nbDotDieuTriId,
      };
    }
    if (huyetApTamThu < huyetApTamTruong) {
      message.error("Huyết áp tâm thu cần lớn hơn huyết áp tâm trương");
    } else {
      updateObject(
        refValue.current["dsChiTiet"][index].chiSoSong,
        `huyetApTamThu`,
        huyetApTamThu
      );
      updateObject(
        refValue.current["dsChiTiet"][index].chiSoSong,
        `huyetApTamTruong`,
        huyetApTamTruong
      );
    }
  };

  const onChangeInput = useCallback(
    (key, idx) => (value) => {
      const keySplit = (key || "").split(".");
      let key1, key2;
      key1 = keySplit[0];
      if (keySplit.length > 1) {
        key2 = keySplit[1];
      }
      if (
        !key.includes("thoiGianThucHien") &&
        !refValue.current.dsChiTiet[idx]?.thoiGianThucHien
      ) {
        message.error("Vui lòng nhập thời gian thực hiện!");
      }
      if (key.includes("huyetAp")) {
        onChangeHuyetAp(idx)(value);
      } else {
        if (!key2) {
          updateObject(refValue.current.dsChiTiet[idx], key1, value);
        } else {
          if (!refValue.current.dsChiTiet[idx][key1]) {
            if (key1.includes("chiSoSong")) {
              refValue.current.dsChiTiet[idx][key1] = {
                chiDinhTuLoaiDichVu: 201,
                khoaChiDinhId: form.khoaChiDinhId,
                nbDotDieuTriId: form.nbDotDieuTriId,
              };
            } else {
              refValue.current.dsChiTiet[idx][key1] = {};
            }
          }
          if (get(refValue.current.dsChiTiet[idx], `${key1}`)) {
            updateObject(
              refValue.current.dsChiTiet[idx][key1],
              `${key2}`,
              value
            );
          } else {
            refValue.current.dsChiTiet[idx][key1] = {};
            updateObject(
              refValue.current.dsChiTiet[idx][key1],
              `${key2}`,
              value
            );
          }
        }
      }
      refChanDoan.current.changeKey(key, idx, value);

      if (["chiSoSong.canNang", "chiSoSong.chieuCao"].includes(key)) {
        const { canNang, chieuCao } = get(
          refValue.current,
          `dsChiTiet[${idx}].chiSoSong`,
          {}
        );
        if (canNang && chieuCao) {
          const bmi = canNang / ((chieuCao / 100) * (chieuCao / 100));
          refValue.current["dsChiTiet"][idx]["chiSoSong"]["bmi"] =
            bmi.toFixed(2);
          refBmi.current.setValue(refValue.current);
        }
      }
      if (key.includes("dichNhap2")) {
        const { thuocDichTruyen, chePhamMau, anUong, khac } = get(
          refValue.current,
          `dsChiTiet[${idx}].dichNhap2`,
          {}
        );
        const tongNhap =
          (thuocDichTruyen ? parseFloat(thuocDichTruyen) : 0) +
          (chePhamMau ? parseFloat(chePhamMau) : 0) +
          (anUong ? parseFloat(anUong) : 0) +
          (khac ? parseFloat(khac) : 0);
        updateObject(
          refValue.current.dsChiTiet[idx]["dichNhap2"],
          `tongNhap`,
          tongNhap
        );
        refTongNhap.current.setValue(refValue.current);
      }

      if (key.includes("dichXuat2")) {
        const { nuocTieu, dichDanLuu, khac } = get(
          refValue.current,
          `dsChiTiet[${idx}].dichXuat2`,
          {}
        );
        const tongXuat =
          (nuocTieu ? parseFloat(nuocTieu) : 0) +
          (dichDanLuu ? parseFloat(dichDanLuu) : 0) +
          (khac ? parseFloat(khac) : 0);
        updateObject(
          refValue.current.dsChiTiet[idx]["dichXuat2"],
          `tongXuat`,
          tongXuat
        );
        refTongXuat.current.setValue(refValue.current);
      }
      formChangeValue();
    },
    [refValueForm, form, Object.keys(formChange || {}).length, tableIndex]
  );

  const formChangeValue = () => {
    const indexKhung = refValueForm.findIndex((el) => el.stt === tableIndex);
    refValueForm[indexKhung] = refValue.current;
    formChange["dsTheoDoi"] && formChange["dsTheoDoi"](refValueForm);
  };

  const onChangeValueChung = useCallback(
    (key) => (value) => {
      formChange[key](value);
    },
    [Object.keys(formChange || {}).length]
  );

  const handleCopy = (idx) => () => {
    const dataCopy = cloneDeep(refValue.current.dsChiTiet[idx]);
    if (dataCopy.chiSoSong) {
      delete dataCopy.chiSoSong.id;
      delete dataCopy.chiSoSong.createdAt;
      delete dataCopy.chiSoSong.createdBy;
      delete dataCopy.chiSoSong.nguoiThucHienId;
      delete dataCopy.chiSoSong.updatedAt;
      delete dataCopy.chiSoSong.updatedBy;
      delete dataCopy.chiSoSongId;
    }
    setDataCopy({
      col: idx,
      data: dataCopy,
    });
    message.success("Copy dữ liệu thành công!");
  };

  const hanldePaste = (idx) => () => {
    const dataCurrent = cloneDeep(refValue.current.dsChiTiet[idx]);
    refValue.current.dsChiTiet[idx] = cloneDeep(dataCopy.data);

    refValue.current.dsChiTiet[idx].stt = dataCurrent.stt;
    setState({
      value: cloneDeep(refValue.current),
    });
    formChangeValue();
  };

  const handleDelete = (idx) => () => {
    refConfirm.current &&
      refConfirm.current.show(
        {
          title: t("common.thongBao"),
          content: `${t("common.banCoChacMuonXoa")} cột ${idx + 1}`,
          cancelText: t("common.huy"),
          okText: t("common.dongY"),
          classNameOkText: "button-warning",
          showBtnOk: true,
          typeModal: "warning",
        },
        async () => {
          try {
            if (refValue.current.dsChiTiet[idx]?.chiSoSong?.id) {
              await nbChiSoSongProvider.onDelete(
                refValue.current.dsChiTiet[idx]?.chiSoSong?.id
              );
            }
            refValue.current.dsChiTiet[idx] = {};
            setState({
              value: cloneDeep(refValue.current),
            });
            formChangeValue();
          } catch (error) {
            message.error(error?.message || t("kiosk.coLoiXayRaVuiLongThuLai"));
          }
        }
      );
  };

  const onSelectSinhHieu = (idx) => () => {
    refModalSinhHieu.current &&
      refModalSinhHieu.current.show(
        {
          nbDotDieuTriId: form?.nbDotDieuTriId || nbDotDieuTriId,
          khoaChiDinhId: form?.khoaChiDinhId || khoaChiDinhId,
        },
        (sinhHieu) => {
          const ngayThucHien = sinhHieu.thoiGianThucHien
            ? moment(sinhHieu.thoiGianThucHien).format("YYYY-MM-DD")
            : "";
          const thoiGian = sinhHieu.thoiGianThucHien
            ? moment(sinhHieu.thoiGianThucHien).format("HH:mm:ss")
            : "";
          const allChiTiet = refValueForm
            .map((el, index) => {
              return el.dsChiTiet;
            })
            .flat(Infinity);
          let chiTietOld = allChiTiet.find(
            (el) =>
              el.chiSoSong?.thoiGianThucHien &&
              moment(el.chiSoSong?.thoiGianThucHien).format(
                "DD/MM/YYYY HH:mm"
              ) === moment(sinhHieu.thoiGianThucHien).format("DD/MM/YYYY HH:mm")
          );
          if (chiTietOld) {
            message.error(
              t("editor.nbDaTonTaiSinhHieu", {
                time: moment(chiTietOld.chiSoSong.thoiGianThucHien).format(
                  "DD/MM/YYYY HH:mm:ss"
                ),
              })
            );
          } else {
            refValue.current.dsChiTiet[idx]["chiSoSong"] = sinhHieu;
            refValue.current.dsChiTiet[idx].ngayThucHien = ngayThucHien;
            refValue.current.dsChiTiet[idx].thoiGian = thoiGian;
            refValue.current.dsChiTiet[idx].thoiGianThucHien = moment(
              sinhHieu.thoiGianThucHien
            ).format("YYYY-MM-DD HH:mm:00");
            setState({
              value: cloneDeep(refValue.current),
            });
            formChange["dsTheoDoi"] && formChange["dsTheoDoi"](refValueForm);
          }
        }
      );
  };

  const listTr = useMemo(() => {
    return TR;
  }, [itemProps]);

  const onClick = (index) => () => {
    let vanDeKhac = state?.value?.vanDeKhac || [];
    if (vanDeKhac?.includes(index)) {
      vanDeKhac = vanDeKhac?.filter((x) => x != index);
    } else {
      vanDeKhac.push(index);
    }
    refValue.current["vanDeKhac"] = vanDeKhac;
    const data = cloneDeep(refValue.current);
    setState({
      value: data,
    });
    formChangeValue();
  };

  const render = useMemo(() => {
    return (
      <>
        {itemProps.vienPSHN ? (
          <HeaderFormChamSocC2PSHN
            form={form}
            mode={mode}
            formChange={formChange}
            loaiPhieu={itemProps.loaiPhieu}
            toSo={toSo}
            titleCap2={"II, III"}
            itemProps={itemProps}
          ></HeaderFormChamSocC2PSHN>
        ) : (
          <div className="header-form">
            <Row className="flex">
              <Col span={6} className="content-title-left">
                <div className="center">{form?.tieuDeTrai1}</div>
                <div className="center">
                  <b>{form?.tieuDeTrai2}</b>
                </div>
                <div className="center">{form?.tenKhoaNb}</div>
              </Col>
              <Col span={12} className="title-center">
                <div className="ten-phieu">PHIẾU THEO DÕI VÀ CHĂM SÓC</div>
                <div>
                  {itemProps.loaiPhieu == 2 ? "(Cấp 1)" : "(Cấp 2 - 3)"}{" "}
                </div>
                <DeboundInput
                  type="multipleline"
                  lineHeightText={1.5}
                  fontSize={12}
                  minHeight={24}
                  styleMain={{ width: 100 }}
                  label="Tờ số  : "
                  onChange={onChangeInput("toSo")}
                  value={item.toSo}
                  inputNumber={true}
                />
              </Col>
              <Col span={6} className="title-right">
                <div>Số vào viện: {form?.maBenhAn}</div>
                <div>Mã LK: {form?.maHoSo}</div>
              </Col>
            </Row>
            <Row className="name-patient">
              <Col span={12}>
                Họ tên người bệnh: ​ <b>{form.tenNb}</b>
              </Col>
              <Col span={4}>Tuổi: {form.tuoi}</Col>
              <Col span={8}>
                <CheckGroups
                  component={{
                    props: {
                      direction: "rtl",
                      type: "onlyOne",
                      checkList: [
                        {
                          label: "Giới tính: Nam",
                          value: 1,
                        },
                        {
                          label: "Nữ",
                          value: 2,
                        },
                      ],
                      fieldName: "gioiTinh",
                      readOnly: true,
                    },
                  }}
                  mode={mode}
                  form={form}
                  formChange={formChange}
                />
              </Col>
            </Row>
            <Row>
              <Col span={24}>
                <span style={{ marginRight: 10, minWidth: 200 }}>
                  Phòng: {form.tenPhong}
                </span>
                <span>Giường: {form.soHieuGiuong}</span>
              </Col>
            </Row>
            <Row>
              <Col span={24}>
                <span style={{ marginRight: 10 }}>
                  Chẩn đoán: {form.tenCdChinh}
                </span>
              </Col>
            </Row>
            <Row>
              <Col span={24} className="flex">
                <CheckGroups
                  component={{
                    props: {
                      direction: "rtl",
                      type: "onlyOne",
                      checkList: [
                        {
                          label: "Tiền sử dị ứng: ",
                          value: 1,
                        },
                        {
                          label: "Chưa ghi nhận",
                          value: 2,
                        },
                      ],
                      fieldName: "tienSuDiUng",
                    },
                  }}
                  mode={mode}
                  form={form}
                  formChange={formChange}
                />
                <DeboundInput
                  size={"small"}
                  value={get(form, "ghiRo", "")}
                  onChange={onChangeValueChung("ghiRo")}
                  type="multipleline"
                  lineHeightText={1.5}
                  fontSize={12}
                  minHeight={24}
                  styleMain={{ flex: 1 }}
                  label="Có, Dị nguyên: "
                />
              </Col>
            </Row>
          </div>
        )}

        <table style={{ marginTop: 20 }}>
          <tbody>
            {listTr.map((item, index) => {
              const keySplit = (item?.key || "").split(".");
              let key1, key2;
              key1 = keySplit[0];
              if (keySplit.length > 1) {
                key2 = keySplit[1];
              }
              if (item.key == "dsVanDe") {
                return (
                  <RenderChanDoan
                    ref={refChanDoan}
                    onChangeInput={onChangeInput}
                    data={state.value}
                    refValue={refValue}
                    formChangeValue={formChangeValue}
                    arr={arr}
                    itemProps={itemProps}
                  />
                );
              } else if (
                ["dichXuat2.tongXuat", "dichNhap2.tongNhap"].includes(item.key)
              ) {
                return (
                  <tr>
                    <td colSpan={3}>
                      {item.key === "dichXuat2.tongXuat"
                        ? "Tổng xuất (ml)"
                        : "Tổng nhập (ml)"}
                    </td>
                    <RenderNhapXuat
                      ref={
                        item.key === "dichXuat2.tongXuat"
                          ? refTongXuat
                          : refTongNhap
                      }
                      value={state.value}
                      formChangeValue={formChangeValue}
                      refValue={refValue}
                      refValueForm={refValueForm}
                      tableIndex={tableIndex}
                      formChange={formChange}
                      keyItem={item.key}
                    />
                  </tr>
                );
              } else if (item.key === "ghiChuBanGiao") {
                return (
                  <RenderGhiChuBanGiao
                    arr={arr}
                    value={state.value}
                    formChangeValue={formChangeValue}
                  />
                );
              } else {
                if (
                  ([
                    "truocDe.dichAmDao",
                    "truocDe.mauSacDichAmDao",
                    "truocDe.muiDichAmDao",
                    "truocDe.raNuocAmDao",
                    "truocDe.mauSacNuocAmDao",
                    "truocDe.muiNuocAmDao",
                    "truocDe.raMauAmDao",
                    "truocDe.mauSacMauAmDao",
                    "truocDe.slMauAmDao",
                    "truocDe.raMauKhac",
                    "truocDe.tinhTrangDauOi",
                    "truocDe.tinhTrangNuocOi",
                    "truocDe.truocDeKhac",
                    "truocDe.timThai",
                    "truocDe.conCoTuCung",
                    "truocDe.tinhTrangCoTuCung",
                    "truocDe.ctCoTuCung",
                    "truocDe.ngoi",
                    "truocDe.the",
                    "truocDe.ctNgoiThe",
                  ].includes(item.key) &&
                    !state?.value?.vanDeKhac?.includes(1)) ||
                  ([
                    "sauDe.tinhTrangVu",
                    "sauDe.dichAmDao",
                    "sauDe.mauSacDichAmDao",
                    "sauDe.muiDichAmDao",
                    "sauDe.coTuCung",
                    "sauDe.ctCoTuCung",
                    "sauDe.raMauAmDao",
                    "sauDe.slMauAmDao",
                    "sauDe.mauMauAmDao",
                    "sauDe.muiMauAmDao",
                    "sauDe.vetKhauTsm",
                    "sauDe.vetMo",
                    "sauDe.vetChich",
                    "sauDe.sauDeKhac",
                  ].includes(item.key) &&
                    !state?.value?.vanDeKhac?.includes(2))
                ) {
                  return null;
                }
                return (
                  <tr key={`${item.key}_${index}`}>
                    {(item.label1 || item.span) && (
                      <td
                        rowSpan={
                          item.key === "chiSoSong.mach"
                            ? rowSpanSinhTon
                            : item.rowSpan1 || 1
                        }
                        colSpan={item.colSpan1 || 1}
                        style={{ position: "relative" }}
                      >
                        {!index ? (
                          <div>
                            {showAdd && (
                              <Button
                                size="small"
                                className="btn-add"
                                onClick={handleAdd}
                                icon={<PlusOutlined />}
                              ></Button>
                            )}
                            <SVG.IcDelete
                              className="ic-remove"
                              onClick={onRemove(tableIndex)}
                            />
                          </div>
                        ) : null}
                        {item.span ? (
                          <span>{item.span}</span>
                        ) : item.key === "vanDeKhac" ? (
                          <div
                            style={{ display: "flex", alignItems: "center" }}
                          >
                            <b>{item.label1}</b>
                            <Popover
                              content={
                                <PopoverStyled>
                                  {TRUOC_DE_SAU_DE.map((truocDe, index) => {
                                    const isActive =
                                      state?.value?.vanDeKhac?.includes(
                                        truocDe.id
                                      );
                                    return (
                                      <div
                                        key={truocDe.id}
                                        onClick={onClick(truocDe.id)}
                                        className={`item ${
                                          isActive ? "active" : ""
                                        }`}
                                      >
                                        <div style={{ flex: 1 }}>
                                          {truocDe.ten}
                                        </div>
                                        {isActive && (
                                          <SVG.IcDelete
                                            className={"icon-remove"}
                                          />
                                        )}
                                      </div>
                                    );
                                  })}
                                </PopoverStyled>
                              }
                              trigger="click"
                            >
                              <SVG.IcAdd className="icon-add hide-print-btn" />
                            </Popover>
                          </div>
                        ) : (
                          <b>{item.label1}</b>
                        )}
                      </td>
                    )}
                    {item.label2 && (
                      <td
                        rowSpan={item.rowSpan2 || 1}
                        colSpan={item.colSpan2 || 1}
                        style={{ width: 50, minWidth: 50, maxWidth: 50 }}
                      >
                        {item.label2}
                      </td>
                    )}
                    {item.hint && (
                      <td
                        className="hint"
                        rowSpan={item.rowSpanHint || 1}
                        colSpan={item.colSpanHint || 1}
                        style={{ width: 100, minWidth: 100 }}
                      >
                        {(item.hint || []).map((item, index) => (
                          <span key={index}>{item}</span>
                        ))}
                      </td>
                    )}

                    {arr.map((el, idx) => {
                      let dataDroplist = cloneDeep(item.data);
                      const tmNgoaiBien = get(
                        refValue.current,
                        `dsChiTiet[${idx}].tmNgoaiBien`,
                        []
                      );
                      const tmTrungTam = get(
                        refValue.current,
                        `dsChiTiet[${idx}].tmTrungTam`,
                        []
                      );
                      if (
                        ["dsChamSoc"].includes(item.key) &&
                        ((tmTrungTam || []).some((x) => [2, 3].includes(x)) ||
                          (tmNgoaiBien || []).some((x) => [2, 3].includes(x)))
                      ) {
                        dataDroplist = dataDroplist.concat([
                          {
                            label: "Thông tráng catherter bằng DD NaCL0,9%",
                            value: 13,
                          },
                          {
                            label: "Đặt lại catheter",
                            value: 14,
                          },
                        ]);
                      }
                      return item.disable ? (
                        <td key={`${item.key}_${index}_${idx}`}></td>
                      ) : item.key == "ngayThucHien" ? (
                        <td
                          key={`${item.key}_${index}_${idx}`}
                          style={{
                            width: 150,
                            minWidth: 150,
                            maxWidth: 150,
                            position: "relative",
                            
                          }}
                          className={`col-element`}
                        >
                          <CopyPasteCol
                            colIndex={idx}
                            handlePaste={hanldePaste(idx)}
                            handleCopy={handleCopy(idx)}
                            dataCopy={dataCopy}
                            handleDelete={handleDelete(idx)}
                            onSelectSinhHieu={onSelectSinhHieu(idx)}
                          />
                          <AppDatePicker
                            component={{
                              props: {
                                contentAlign: "center",
                                dateTimeFormat: "HH:mm D/M/Y",
                                fieldName: "value",
                                disableOnblur: true,
                                fontSize: 8,
                              },
                            }}
                            form={{
                              value: get(
                                state.value,
                                `dsChiTiet[${idx}].thoiGianThucHien`
                              ),
                            }}
                            mode={mode}
                            formChange={{
                              value: (e) => {
                                try {
                                  onChangeInput(`thoiGianThucHien`, idx)(e);
                                } catch (error) {}
                              },
                            }}
                          />
                        </td>
                      ) : item.type === "droplist" ? (
                        <td>
                          <PopoverSelect
                            data={dataDroplist}
                            value={get(
                              state.value,
                              `dsChiTiet[${idx}][${key1}]${
                                key2 ? `${key2}` : ""
                              }`
                            )}
                            onChangeValue={(e) => {
                              onChangeInput(item.key, idx)(e);
                            }}
                            isMultiple={!(item.mode == "onlyOne")}
                            trigger="click"
                            isShowSearch={
                              item?.isShowSearch ||
                              (Array.isArray(item.data) ? item.data : [])
                                .length > 15
                            }
                          />
                        </td>
                      ) : item.type == "sign" ? (
                        <td key={`${item.key}_${index}_${idx}`}>
                          <ImageSign
                            component={{
                              props: {
                                ...itemProps,
                                isMultipleSign: true,
                                viTri: get(
                                  state.value,
                                  `dsChiTiet[${idx}].stt`
                                ),
                                customText: "Ký",
                                dataSign: {
                                  id: form.id,
                                  soPhieu: form?.lichSuKy?.soPhieu,
                                  lichSuKyId: form?.lichSuKy?.id,
                                },
                              },
                            }}
                            form={{
                              ...combineFields(form),
                            }}
                          />
                        </td>
                      ) : (
                        <td key={`${item.key}_${index}_${idx}`}>
                          <DeboundInput
                            readOnly={false}
                            value={get(
                              state.value,
                              `dsChiTiet[${idx}]${key1}${
                                key2 ? `[${key2}]` : ""
                              }`
                            )}
                            onChange={onChangeInput(item.key, idx)}
                            type="multipleline"
                            lineHeightText={1.5}
                            fontSize={9}
                            minHeight={9 + 6}
                            markSpanRow={false}
                            contentAlign="center"
                            inputNumber={item.type === "number"}
                            typeNumber={"float"}
                          />
                        </td>
                      );
                    })}
                  </tr>
                );
              }
            })}
          </tbody>
        </table>
      </>
    );
  }, [
    state.value,
    onChangeValueChung,
    refValueForm,
    rowSpanSinhTon,
    dataCopy,
    tableIndex,
    listTr,
    itemProps,
  ]);
  return render;
};

export default RenderTable;
