import React from "react";
// <PERSON><PERSON> sinh không thở máy
export const TR = [
  {
    label1: (
      <div>
        <div>
          <b>Giờ:</b>
        </div>
        <div>
          <b>Ngày /tháng /năm</b>
        </div>
      </div>
    ),
    colSpan1: 2,
    key: "ngayThucHien",
  },

  {
    label1: "Nhận định",
    rowSpan1: 11,
    label2: "SPO2(%)",
    key: "chiSoSong.spo2",
    type: "string",
  },

  {
    label2: "Nhịp thở (lần/phút)",
    key: "chiSoSong.nhipTho",
    type: "string",
  },
  {
    label2: "Nhịp tim (l/phút)",
    key: "nhipTim",
    type: "string",
  },
  {
    label2: "Nhiệt độ (°C)",
    key: "chiSoSong.nhietDo",
    type: "string",
  },
  {
    label2: "Tri giác",
    type: "droplist",
    key: "triGiac",
    data: [
      { label: "Tỉnh", value: 1 },
      { label: "<PERSON> bì", value: 2 },
      { label: "Vật vã", value: 3 },
      { label: "Kích thích", value: 4 },
      { label: "Hôn mê", value: 5 },
    ],
  },

  {
    label2: "Hô hấp",
    type: "droplist",
    data: [
      { label: "Không khó thở", value: 1 },
      { label: "Khó thở nhẹ", value: 2 },
      { label: "Khó thở kèm rút lõm cơ hô hấp", value: 3 },
      { label: "Thở qua Mask", value: 4 },
      { label: "Trẻ tự thở", value: 5 },
      { label: "Thở gắng sức", value: 6 },
      { label: "Thở CPAP qua Mask", value: 7 },
      { label: "Thở oxi qua Mask", value: 8 },
    ],
    key: "hoHap",
  },

  {
    label2: "Da",
    type: "droplist",
    data: [
      { label: "Hồng", value: 1 },
      { label: "Xanh", value: 2 },
      { label: "Nhợt", value: 3 },
      { label: "Tái nhợt", value: 4 },
      { label: "Xanh tái", value: 5 },
      { label: "Vân tím", value: 6 },
      { label: "Vàng", value: 7 },
    ],
    key: "da",
  },

  {
    label2: "Nước tiểu",
    type: "string",

    key: "nuocTieu",
  },
  {
    label2: "Phân",
    type: "droplist",
    data: [
      { label: "Không có phân", value: 1 },
      { label: "Phân su", value: 2 },
      { label: "Phân hoa cà hoa cải", value: 3 },
      { label: "Phân lẫn nhày", value: 4 },
      { label: "Phân lẫn máu", value: 5 },
      { label: "Phân vàng", value: 6 },
      { label: "Phân nhiều nước", value: 7 },
    ],
    key: "phan",
  },

  {
    label2: "Cân nặng (gram)",
    type: "string",
    key: "chiSoSong.canNang",
  },
  {
    label2: "Nhận định khác",
    type: "string",
    key: "nhanDinhKhac",
  },
  {
    label2: "GDSK",
    type: "droplist",
    key: "tuVanGiaoDuc",
    colSpan2: 2,
    data: [
      {
        label: "Tư vấn giáo dục sức khoẻ cho NB trong ngày đầu vào viện",
        value: 1,
      },
      {
        label: "Tư vấn giáo dục sức khoẻ cho NB trong thời gian nằm viện",
        value: 2,
      },
      {
        label: "Tư vấn giáo dục sức khoẻ cho NB chuẩn bị ra viện",
        value: 3,
      },
    ],
  },
  {
    label1: "Chẩn đoán",
    rowSpan1: 2,
    label2: "Chẩn đoán",
    type: "droplist",
    key: "chanDoanNguyCo",
    data: [
      { label: "Nguy cơ nhiễm khuẩn", value: 1 },
      { label: "Nguy cơ vàng da", value: 2 },
      { label: "Nguy cơ hạ đường huyết", value: 3 },
      { label: "Nguy cơ suy hô hấp", value: 4 },
      { label: "Nguy cơ hạ thân nhiệt", value: 5 },
      { label: "Nguy cơ hăm", value: 6 },
      { label: "Loét do nằm lâu", value: 7 },
      { label: "Nguy cơ viêm ruột hoại tử", value: 8 },
      { label: "Nguy cơ thủng tạng rỗng", value: 9 },
      { label: "Nguy cơ viêm loét da do tỳ đè", value: 10 },
      { label: "Nguy cơ xẹp phổi", value: 11 },
      { label: "Nguy cơ sẹo vách mũi", value: 12 },
      { label: "Nguy cơ hít sặc do ăn qua sond", value: 13 },
      { label: "Nguy cơ hít sặc do dị dạng đường tiêu hóa", value: 14 },
      { label: "Nguy cơ hít sặc do nôn trớ nhiều", value: 15 },
    ],
  },
  {
    label2: "Chẩn đoán khác",
    type: "string",
    key: "chanDoanNguyCoKhac",
  },
  {
    label1: "Mục tiêu",
    rowSpan1: 2,
    label2: "Mục tiêu",
    type: "droplist",
    key: "mucTieu",
    data: [
      { label: "Giảm nguy cơ nhiễm khuẩn/ giảm nguy cơ vàng da", value: 1 },
      {
        label: "Giảm nguy cơ suy hô hấp đảm bảo thân nhiệt trẻ ổn định",
        value: 2,
      },
      { label: "Đảm bảo vệ sinh da khô sạch", value: 3 },
      {
        label: "Giảm nguy cơ loét do tì đè tránh nguy cơ sẹo vách mũi",
        value: 4,
      },
      { label: "NB không có nguy cơ hít sặc trong quá trình cho ăn", value: 5 },
      { label: "NB không có nguy cơ hạ đường huyết", value: 6 },
    ],
  },
  {
    label2: "Mục tiêu khác",
    type: "string",
    key: "mucTieuKhac",
  },
  {
    label1: "Thực hiện chăm sóc",
    label2: "Sữa (ml)",
    type: "string",
    key: "sua",
    rowSpan1: 8,
  },
  {
    label2: "Thở Oxy (lít/phút)",
    type: "string",
    key: "thoOxy",
  },
  {
    label2: "Chăm sóc da",
    key: "csDa",
    type: "droplist",
    data: [
      {
        label: "Tắm",
        value: 1,
      },
      {
        label: "VS toàn thân",
        value: 2,
      },
      {
        label: "Thay bỉm",
        value: 3,
      },
      { label: "Bôi kem dưỡng ẩm", value: 4 },
      { label: "Bôi kem hăm", value: 5 },
      { label: "Bôi kem theo Chỉ định", value: 6 },
    ],
  },

  {
    label2: "Chăm sóc hốc tự nhiên",
    key: "csHocTuNhien",
    type: "string",
  },
  {
    label2: "Tư thế",
    key: "tuThe",
    type: "droplist",
    data: [
      {
        label: "Nghiêng phải",
        value: 1,
      },
      {
        label: "Ngiêng trái",
        value: 2,
      },
      {
        label: "Nằm ngửa",
        value: 3,
      },
      {
        label: "Nằm sấp",
        value: 4,
      },
      {
        label: "Đầu cao",
        value: 5,
      },
      {
        label: "Đầu bằng",
        value: 6,
      },
      {
        label: "Đầu thấp",
        value: 7,
      },
    ],
  },
  {
    label2: "Y lệnh sơ sinh",
    key: "ylenhSoSinh",
    type: "droplist",
    data: [
      {
        label: "Cấp cứu sặc sữa",
        value: 1,
      },
      {
        label: "Chăm sóc rốn sơ sinh nhiễm khuẩn",
        value: 2,
      },
      {
        label: "Lấy máu gót chân sơ sinh",
        value: 3,
      },
      {
        label: "Xóa bóp thư giãn cho trẻ sơ sinh",
        value: 4,
      },
      {
        label: "Tắm trẻ sơ sinh",
        value: 5,
      },
      {
        label: "Cho trẻ ăn qua sonde dạ dày",
        value: 6,
      },
      {
        label:
          "Hỗ trợ điều trị vết thương dưới 5cm bằng tia Plasma áp dụng cho chăm sóc rốn trẻ sơ sinh",
        value: 7,
      },
      {
        label: "Chăm sóc trẻ sơ sinh được chiếu đèn điều trị vàng da",
        value: 8,
      },
      {
        label: "Chăm sóc, theo dõi trẻ sơ sinh bằng phương pháp Căng gu ru",
        value: 9,
      },
      {
        label: "Xử trí ban đầu dị vật đường thở trẻ sơ sinh",
        value: 10,
      },
      {
        label: "Xử trí ban đầu hạ thân nhiệt ở trẻ sơ sinh",
        value: 11,
      },
      {
        label: "Xử trí ban đầu hạ đường huyết ở trẻ sơ sinh",
        value: 12,
      },
      {
        label: "Xử trí chảy máu rốn",
        value: 13,
      },
      { label: "Hút dịch qua ống NKQ", value: 14 },
      { label: "Hút dịch hầu họng", value: 15 },
      { label: "Chăm sóc NB thở máy", value: 16 },
      { label: "Chăm sóc NB thở CPAP", value: 17 },
      { label: "Truyền tĩnh mạch bằng bơm tiêm điện", value: 18 },
    ],
  },
  {
    label2: "Thực hiện y lệnh",
    key: "thucHienYLenh",
    type: "droplist",
    data: [
      {
        label: "Thực hiện y lệnh thuốc",
        value: 1,
      },
      {
        label: "Thực hiện y lệnh xét nghiệm",
        value: 2,
      },
      {
        label: "Thực hiện y lệnh chẩn đoán hình ảnh",
        value: 3,
      },
      {
        label: "Phụ giúp BS làm thủ thuật ",
        value: 4,
      },
      {
        label: "Đặt ống thông dạ dày",
        value: 5,
      },
      {
        label: "Đặt ống thông hậu môn",
        value: 6,
      },
      {
        label: " Đặt sonde dạ dày",
        value: 7,
      },
      {
        label: "Rút sonde các loại ",
        value: 8,
      },
      {
        label: "Đánh giá huyết áp",
        value: 9,
      },
      {
        label: " Đánh giá mạch",
        value: 10,
      },
      {
        label: "Chăm sóc catheter tĩnh mạch trung tâm",
        value: 11,
      },
      {
        label: "Chăm sóc ống nội khí quản",
        value: 12,
      },
      {
        label: "Chăm sóc catheter tĩnh mạch ngoại vi",
        value: 13,
      },
      {
        label: "Chăm sóc ống nội khí quản",
        value: 14,
      },
      {
        label: "Chăm sóc vết loét",
        value: 15,
      },
      {
        label: "Tắm cho người bệnh",
        value: 16,
      },
      {
        label: "Cho người bệnh thở oxy qua mặt nạ hoăc qua sonde mũi.",
        value: 17,
      },
      {
        label: "Truyền tĩnh mạch",
        value: 18,
      },
      {
        label: "Thực hiện y lệnh truyền dịch",
        value: 19,
      },
      {
        label: "Truyền máu",
        value: 20,
      },
    ],
  },
  {
    label2: "Thực hiện CS khác",
    type: "string",
    key: "thucHienChamSocKhac",
    colSpan2: 1,
  },

  {
    label2: "Đánh giá",
    type: "droplist",
    data: [
      {
        label: "NB ổn định",
        value: 1,
      },
      {
        label: "Thực hiện thuốc an toàn/ thực hiện thủ thuật an toàn",
        value: 2,
      },
      {
        label: "NB cần theo dõi sát",
        value: 3,
      },
      {
        label: "Trẻ hồng hào",
        value: 4,
      },
      {
        label: "Trẻ bú tốt",
        value: 5,
      },
      {
        label: "NB nặng",
        value: 6,
      },
      {
        label: "NB nguy kịch",
        value: 7,
      },
      {
        label: "NB tiên lượng tử vong",
        value: 8,
      },
    ],
    key: "danhGia",
    colSpan2: 2,
  },
  {
    label2: "Đánh giá khác",
    type: "string",
    key: "danhGiaKhac",
    colSpan2: 2,
  },

  {
    label2: "Bàn giao",
    type: "droplist",
    key: "banGiao",
    colSpan2: 2,
    data: [
      { label: "Thực hiện y lệnh thuốc", value: 1 },
      { label: "Thực hiện y lệnh CLS", value: 2 },
      { label: "NB nặng", value: 3 },
      { label: "NB cần theo dõi sát", value: 4 },
    ],
  },
  {
    label2: "Bàn giao khác",
    type: "string",
    key: "banGiaoKhac",
    colSpan2: 2,
  },
  {
    label2: "Tên điều dưỡng thực hiện",
    type: "sign",
    key: "banGiao",
    colSpan2: 2,
  },
];
// Sơ sinh thở máy
export const TR_2 = [
  {
    label1: (
      <div>
        <div>
          <b>Giờ:</b>
        </div>
        <div>
          <b>Ngày /tháng /năm</b>
        </div>
      </div>
    ),
    colSpan1: 2,
    key: "ngayThucHien",
  },

  {
    label1: "Nhận định",
    rowSpan1: 16,
    label2: "Kiểu thở",
    key: "kieuTho",
    type: "droplist",
    data: [
      { label: "HFO", value: 1 },
      { label: "SIMV", value: 2 },
      { label: "CPAP", value: 3 },
      { label: "SPONT", value: 4 },
    ],
  },

  {
    label2: "Áp lực đường thở PIP/PEEP",
    key: "apLucDuongTho",
    type: "string",
  },
  {
    label2: "FiO2 (%)",
    key: "fio2",
    type: "string",
  },
  {
    label2: "NKQ + số/cố định mức",
    type: "string",
    key: "nkqDinhMuc",
  },

  {
    label2: "CPAP 2 gọng",
    type: "string",
    key: "cpap2Gong",
  },
  {
    label2: "SPO2 (%)",
    type: "string",
    key: "chiSoSong.spo2",
  },
  {
    label2: "Nhịp tim (lần/phút)",
    type: "string",
    key: "nhipTim",
  },
  {
    label2: "Nhiệt độ (°C)",
    type: "string",
    key: "chiSoSong.nhietDo",
  },
  {
    label2: "Tri giác",
    type: "droplist",
    key: "triGiac",
    data: [
      { label: "Tỉnh", value: 1 },
      { label: "Li bì", value: 2 },
      { label: "Vật vã", value: 3 },
      { label: "Kích thích", value: 4 },
      { label: "Hôn mê", value: 5 },
    ],
  },
  {
    label2: "Trương lực cơ",
    type: "droplist",
    key: "truongLucCo",
    data: [
      { label: "Bình thường", value: 1 },
      { label: "Tăng", value: 2 },
      { label: "Giảm", value: 3 },
    ],
  },
  {
    label2: "Hô hấp",
    type: "droplist",
    data: [
      { label: "Không khó thở", value: 1 },
      { label: "Khó thở nhẹ", value: 2 },
      { label: "Khó thở kèm rút lõm cơ hô hấp", value: 3 },
      { label: "Thở qua Mask", value: 4 },
      { label: "Trẻ tự thở", value: 5 },
      { label: "Thở gắng sức", value: 6 },
      { label: "Thở CPAP qua Mask", value: 7 },
      { label: "Thở oxi qua Mask", value: 8 },
    ],
    key: "hoHap",
  },

  {
    label2: "Da",
    type: "droplist",
    data: [
      { label: "Hồng", value: 1 },
      { label: "Xanh", value: 2 },
      { label: "Nhợt", value: 3 },
      { label: "Tái nhợt", value: 4 },
      { label: "Xanh tái", value: 5 },
      { label: "Vân tím", value: 6 },
      { label: "Vàng", value: 7 },
    ],
    key: "da",
  },

  {
    label2: "Nước tiểu",
    type: "string",
    key: "nuocTieu",
  },

  {
    label2: "Phân",
    type: "droplist",
    data: [
      { label: "Không có phân", value: 1 },
      { label: "Phân su", value: 2 },
      { label: "Phân hoa cà hoa cải", value: 3 },
      { label: "Phân lẫn nhày", value: 4 },
      { label: "Phân lẫn máu", value: 5 },
      { label: "Phân vàng", value: 6 },
      { label: "Phân nhiều nước", value: 7 },
    ],
    key: "phan",
  },

  {
    label2: "Cân nặng (gram)",
    type: "string",
    key: "chiSoSong.canNang",
  },
  {
    label2: "Nhận định khác",
    type: "string",
    key: "nhanDinhKhac",
  },
  {
    label2: "GDSK",
    type: "droplist",
    key: "tuVanGiaoDuc",
    colSpan2: 2,
    data: [
      {
        label: "Tư vấn giáo dục sức khoẻ cho NB trong ngày đầu vào viện",
        value: 1,
      },
      {
        label: "Tư vấn giáo dục sức khoẻ cho NB trong thời gian nằm viện",
        value: 2,
      },
      {
        label: "Tư vấn giáo dục sức khoẻ cho NB chuẩn bị ra viện",
        value: 3,
      },
    ],
  },
  {
    label1: "Chẩn đoán",
    rowSpan1: 2,
    label2: "Chẩn đoán",
    type: "droplist",
    key: "chanDoanNguyCo",
    data: [
      { label: "Nguy cơ nhiễm khuẩn", value: 1 },
      { label: "Nguy cơ vàng da", value: 2 },
      { label: "Nguy cơ hạ đường huyết", value: 3 },
      { label: "Nguy cơ suy hô hấp", value: 4 },
      { label: "Nguy cơ hạ thân nhiệt", value: 5 },
      { label: "Nguy cơ hăm", value: 6 },
      { label: "Loét do nằm lâu", value: 7 },
      { label: "Nguy cơ viêm ruột hoại tử", value: 8 },
      { label: "Nguy cơ thủng tạng rỗng", value: 9 },
      { label: "Nguy cơ viêm loét da do tỳ đè", value: 10 },
      { label: "Nguy cơ xẹp phổi", value: 11 },
      { label: "Nguy cơ sẹo vách mũi", value: 12 },
      { label: "Nguy cơ hít sặc do ăn qua sond", value: 13 },
      { label: "Nguy cơ hít sặc do dị dạng đường tiêu hóa", value: 14 },
      { label: "Nguy cơ hít sặc do nôn trớ nhiều", value: 15 },
    ],
  },
  {
    label2: "Chẩn đoán khác",
    type: "string",
    key: "chanDoanNguyCoKhac",
  },
  {
    label1: "Mục tiêu",
    rowSpan1: 2,
    label2: "Mục tiêu CS",
    type: "droplist",
    key: "mucTieu",
    data: [
      { label: "Giảm nguy cơ nhiễm khuẩn/ giảm nguy cơ vàng da", value: 1 },
      {
        label: "Giảm nguy cơ suy hô hấp đảm bảo thân nhiệt trẻ ổn định",
        value: 2,
      },
      { label: "Đảm bảo vệ sinh da khô sạch", value: 3 },
      {
        label: "Giảm nguy cơ loét do tì đè tránh nguy cơ sẹo vách mũi",
        value: 4,
      },
      { label: "NB không có nguy cơ hít sặc trong quá trình cho ăn", value: 5 },
      { label: "NB không có nguy cơ hạ đường huyết", value: 6 },
    ],
  },
  {
    label2: "Mục tiêu khác",
    type: "string",
    key: "mucTieuKhac",
  },
  {
    label1: "Thực hiện chăm sóc",
    label2: "Sữa (ml/bữa)",
    type: "string",
    key: "sua",
    rowSpan1: 7,
  },
  {
    label2: "CS da",
    key: "csDa",
    type: "droplist",
    data: [
      {
        label: "Tắm",
        value: 1,
      },
      {
        label: "Vệ sinh toàn thân",
        value: 2,
      },
      {
        label: "Thay bỉm",
        value: 3,
      },
    ],
  },
  {
    label2: "Chăm sóc hốc tự nhiên (mắt, mũi…)",
    key: "csHocTuNhien",
    type: "string",
  },
  {
    label2: "Tư thế người bệnh",
    key: "tuThe",
    type: "droplist",
    data: [
      {
        label: "Nghiêng phải",
        value: 1,
      },
      {
        label: "Ngiêng trái",
        value: 2,
      },
      {
        label: "Nằm ngửa thẳng",
        value: 3,
      },
      {
        label: "Nằm sấp",
        value: 4,
      },
      {
        label: "Đầu cao",
        value: 5,
      },
      {
        label: "Đầu bằng",
        value: 6,
      },
      {
        label: "Đầu thấp",
        value: 7,
      },
    ],
  },
  {
    label2: "Y lệnh sơ sinh",
    key: "ylenhSoSinh",
    type: "droplist",
    data: [
      {
        label: "Cấp cứu sặc sữa",
        value: 1,
      },
      {
        label: "Chăm sóc rốn sơ sinh nhiễm khuẩn",
        value: 2,
      },
      {
        label: "Lấy máu gót chân sơ sinh",
        value: 3,
      },
      {
        label: "Xóa bóp thư giãn cho trẻ sơ sinh",
        value: 4,
      },
      {
        label: "Tắm trẻ sơ sinh",
        value: 5,
      },
      {
        label: "Cho trẻ ăn qua sonde dạ dày",
        value: 6,
      },
      {
        label:
          "Hỗ trợ điều trị vết thương dưới 5cm bằng tia Plasma áp dụng cho chăm sóc rốn trẻ sơ sinh",
        value: 7,
      },
      {
        label: "Chăm sóc trẻ sơ sinh được chiếu đèn điều trị vàng da",
        value: 8,
      },
      {
        label: "Chăm sóc, theo dõi trẻ sơ sinh bằng phương pháp Căng gu ru",
        value: 9,
      },
      {
        label: "Xử trí ban đầu dị vật đường thở trẻ sơ sinh",
        value: 10,
      },
      {
        label: "Xử trí ban đầu hạ thân nhiệt ở trẻ sơ sinh",
        value: 11,
      },
      {
        label: "Xử trí ban đầu hạ đường huyết ở trẻ sơ sinh",
        value: 12,
      },
      {
        label: "Xử trí chảy máu rốn",
        value: 13,
      },
      { label: "Hút dịch qua ống NKQ", value: 14 },
      { label: "Hút dịch hầu họng", value: 15 },
      { label: "Chăm sóc NB thở máy", value: 16 },
      { label: "Chăm sóc NB thở CPAP", value: 17 },
      { label: "Truyền tĩnh mạch bằng bơm tiêm điện", value: 18 },
    ],
  },
  {
    label2: "Thực hiện y lệnh",
    key: "thucHienYLenh",
    type: "droplist",
    data: [
      {
        label: "Thực hiện y lệnh thuốc",
        value: 1,
      },
      {
        label: "Thực hiện y lệnh xét nghiệm",
        value: 2,
      },
      {
        label: "Thực hiện y lệnh chẩn đoán hình ảnh",
        value: 3,
      },
      {
        label: "Phụ giúp BS làm thủ thuật ",
        value: 4,
      },
      {
        label: "Đặt ống thông dạ dày",
        value: 5,
      },
      {
        label: "Đặt ống thông hậu môn",
        value: 6,
      },
      {
        label: " Đặt sonde dạ dày",
        value: 7,
      },
      {
        label: "Rút sonde các loại ",
        value: 8,
      },
      {
        label: "Đánh giá huyết áp",
        value: 9,
      },
      {
        label: " Đánh giá mạch",
        value: 10,
      },
      {
        label: "Chăm sóc catheter tĩnh mạch trung tâm",
        value: 11,
      },
      {
        label: "Chăm sóc ống nội khí quản",
        value: 12,
      },
      {
        label: "Chăm sóc catheter tĩnh mạch ngoại vi",
        value: 13,
      },
      {
        label: "Chăm sóc ống nội khí quản",
        value: 14,
      },
      {
        label: "Chăm sóc vết loét",
        value: 15,
      },
      {
        label: "Tắm cho người bệnh",
        value: 16,
      },
      {
        label: "Cho người bệnh thở oxy qua mặt nạ hoăc qua sonde mũi.",
        value: 17,
      },
      {
        label: "Truyền tĩnh mạch",
        value: 18,
      },
      {
        label: "Thực hiện y lệnh truyền dịch",
        value: 19,
      },
      {
        label: "Truyền máu",
        value: 20,
      },
      { label: "Hút dịch qua ống NKQ", value: 21 },
      { label: "Hút dịch hầu họng", value: 22 },
      { label: "Chăm sóc NB thở máy", value: 23 },
      { label: "Chăm sóc NB thở CPAP", value: 24 },
      { label: "Truyền tĩnh mạch bằng bơm tiêm điện", value: 25 },
    ],
  },
  {
    label2: "Thực hiện CS khác",
    type: "string",
    key: "thucHienChamSocKhac",
    colSpan2: 1,
  },
  {
    label2: "Đánh giá",
    type: "droplist",
    data: [
      {
        label: "NB ổn định",
        value: 1,
      },
      {
        label: "Thực hiện thuốc an toàn/ thực hiện thủ thuật an toàn",
        value: 2,
      },
      {
        label: "NB cần theo dõi sát",
        value: 3,
      },
      {
        label: "Trẻ hồng hào",
        value: 4,
      },
      {
        label: "Trẻ bú tốt",
        value: 5,
      },
      {
        label: "NB nặng",
        value: 6,
      },
      {
        label: "NB nguy kịch",
        value: 7,
      },
      {
        label: "NB tiên lượng tử vong",
        value: 8,
      },
    ],
    key: "danhGia",
    colSpan2: 2,
  },
  {
    label2: "Đánh giá khác",
    type: "string",
    key: "danhGiaKhac",
    colSpan2: 2,
  },
  {
    label2: "Bàn giao",
    type: "droplist",
    key: "banGiao",
    colSpan2: 2,
    data: [
      { label: "Thực hiện y lệnh thuốc", value: 1 },
      { label: "Thực hiện y lệnh CLS", value: 2 },
      { label: "NB nặng", value: 3 },
      { label: "NB cần theo dõi sát", value: 4 },
    ],
  },
  {
    label2: "Bàn giao khác",
    type: "string",
    key: "banGiaoKhac",
    colSpan2: 2,
  },
  {
    label2: "Tên điều dưỡng thực hiện",
    type: "sign",
    key: "banGiao",
    colSpan2: 2,
  },
];
// Sơ sinh can thiệp
export const TR_3 = [
  {
    label1: (
      <div>
        <div>
          <b>Giờ:</b>
        </div>
        <div>
          <b>Ngày /tháng /năm</b>
        </div>
      </div>
    ),
    colSpan1: 2,
    key: "ngayThucHien",
  },

  {
    label1: "Nhận định",
    rowSpan1: 10,
    label2: "Nhịp thở (lần/phút)",
    key: "chiSoSong.nhipTho",
    type: "string",
  },
  {
    label2: "Nhịp tim (l/phút)",
    key: "nhipTim",
    type: "string",
  },
  {
    label2: "Nhiệt độ (°C)",
    key: "chiSoSong.nhietDo",
    type: "string",
  },
  {
    label2: "Tri giác",
    type: "droplist",
    key: "triGiac",
    data: [
      { label: "Tỉnh", value: 1 },
      { label: "Li bì", value: 2 },
      { label: "Quấy khóc", value: 3 },
    ],
  },

  {
    label2: "Hô hấp",
    type: "droplist",
    data: [
      { label: "Không khó thở", value: 1 },
      { label: "Khó thở nhẹ", value: 2 },
      { label: "Khó thở khi gắng sức", value: 3 },
      { label: "Khó thở kèm rút lõm cơ hô hấp", value: 4 },
      { label: "Thở qua Mask", value: 5 },
      { label: "Thở oxi", value: 6 },
      { label: "Trẻ tự thở", value: 7 },
    ],
    key: "hoHap",
  },

  {
    label2: "Da",
    type: "droplist",
    data: [
      { label: "Hồng", value: 1 },
      { label: "Xanh", value: 2 },
      { label: "Nhợt", value: 3 },
      { label: "Tái nhợt", value: 4 },
      { label: "Xanh tái", value: 5 },
      { label: "Vân tím", value: 6 },
      { label: "Vàng", value: 7 },
    ],
    key: "da",
  },

  {
    label2: "Nước tiểu",
    type: "string",
    key: "nuocTieu",
  },
  {
    label2: "Phân",
    type: "droplist",
    data: [
      { label: "Không có phân", value: 1 },
      { label: "Phân su", value: 2 },
      { label: "Phân hoa cà hoa cải", value: 3 },
      { label: "Phân lẫn nhày", value: 4 },
      { label: "Phân lẫn máu", value: 5 },
    ],
    key: "phan",
  },

  {
    label2: "Cân nặng (gram)",
    type: "string",
    key: "chiSoSong.canNang",
  },
  {
    label2: "Nhận định khác",
    type: "string",
    key: "nhanDinhKhac",
  },
  {
    label2: "GDSK",
    type: "droplist",
    key: "tuVanGiaoDuc",
    colSpan2: 2,
    data: [
      {
        label: "Tư vấn giáo dục sức khoẻ cho NB trong ngày đầu vào viện",
        value: 1,
      },
      {
        label: "Tư vấn giáo dục sức khoẻ cho NB trong thời gian nằm viện",
        value: 2,
      },
      {
        label: "Tư vấn giáo dục sức khoẻ cho NB chuẩn bị ra viện",
        value: 3,
      },
    ],
  },
  {
    label1: "Chẩn đoán",
    rowSpan1: 2,
    label2: "Chẩn đoán",
    type: "droplist",
    key: "chanDoanNguyCo",
    data: [
      { label: "Nguy cơ nhiễm khuẩn", value: 1 },
      { label: "Nguy cơ vàng da", value: 2 },
      { label: "Nguy cơ hạ đường huyết", value: 3 },
      { label: "Nguy cơ suy hô hấp", value: 4 },
      { label: "Nguy cơ hạ thân nhiệt", value: 5 },
      { label: "Nguy cơ hăm", value: 6 },
      { label: "Loét do nằm lâu", value: 7 },
      { label: "Nguy cơ viêm ruột hoại tử", value: 8 },
      { label: "Nguy cơ thủng tạng rỗng", value: 9 },
      { label: "Nguy cơ viêm loét da do tỳ đè", value: 10 },
      { label: "Nguy cơ xẹp phổi", value: 11 },
      { label: "Nguy cơ sẹo vách mũi", value: 12 },
      { label: "Nguy cơ hít sặc do ăn qua sond", value: 13 },
    ],
  },
  {
    label2: "Chẩn đoán khác",
    type: "string",
    key: "chanDoanNguyCoKhac",
  },
  {
    label1: "Mục tiêu",
    rowSpan1: 2,
    label2: "Mục tiêu",
    type: "droplist",
    key: "mucTieu",
    data: [
      { label: "Giảm nguy cơ nhiễm khuẩn/ giảm nguy cơ vàng da", value: 1 },
      {
        label: "Giảm nguy cơ suy hô hấp đảm bảo thân nhiệt trẻ ổn định",
        value: 2,
      },
      { label: "Đảm bảo vệ sinh da khô sạch", value: 3 },
      {
        label: "Giảm nguy cơ loét do tì đè tránh nguy cơ sẹo vách mũi",
        value: 4,
      },
      { label: "NB không có nguy cơ hít sặc trong quá trình cho ăn", value: 5 },
      { label: "NB không có nguy cơ hạ đường huyết", value: 6 },
    ],
  },
  {
    label2: "Mục tiêu khác",
    type: "string",
    key: "mucTieuKhac",
  },
  {
    label1: "Thực hiện chăm sóc",
    label2: "Sữa (ml)",
    type: "string",
    key: "sua",
    rowSpan1: 6,
  },
  {
    label2: "Chăm sóc da",
    key: "csDa",
    type: "droplist",
    data: [
      {
        label: "Tắm",
        value: 1,
      },
      {
        label: "Thay bỉm",
        value: 2,
      },
    ],
  },
  {
    label2: "Chăm sóc hốc tự nhiên",
    key: "csHocTuNhien",
    type: "string",
  },
  {
    label2: "Y lệnh sơ sinh",
    key: "ylenhSoSinh",
    type: "droplist",
    data: [
      {
        label: "Cấp cứu sặc sữa",
        value: 1,
      },
      {
        label: "Chăm sóc rốn sơ sinh nhiễm khuẩn",
        value: 2,
      },
      {
        label: "Lấy máu gót chân sơ sinh",
        value: 3,
      },
      {
        label: "Xóa bóp thư giãn cho trẻ sơ sinh",
        value: 4,
      },
      {
        label: "Tắm trẻ sơ sinh",
        value: 5,
      },
      {
        label: "Cho trẻ ăn qua sonde dạ dày",
        value: 6,
      },
      {
        label:
          "Hỗ trợ điều trị vết thương dưới 5cm bằng tia Plasma áp dụng cho chăm sóc rốn trẻ sơ sinh",
        value: 7,
      },
      {
        label: "Chăm sóc trẻ sơ sinh được chiếu đèn điều trị vàng da",
        value: 8,
      },
      {
        label: "Chăm sóc, theo dõi trẻ sơ sinh bằng phương pháp Căng gu ru",
        value: 9,
      },
      {
        label: "Xử trí ban đầu dị vật đường thở trẻ sơ sinh",
        value: 10,
      },
      {
        label: "Xử trí ban đầu hạ thân nhiệt ở trẻ sơ sinh",
        value: 11,
      },
      {
        label: "Xử trí ban đầu hạ đường huyết ở trẻ sơ sinh",
        value: 12,
      },
      {
        label: "Xử trí chảy máu rốn",
        value: 13,
      },
    ],
  },
  {
    label2: "Thực hiện y lệnh",
    key: "thucHienYLenh",
    type: "droplist",
    data: [
      {
        label: "Thực hiện y lệnh thuốc",
        value: 1,
      },
      {
        label: "Thực hiện y lệnh xét nghiệm",
        value: 2,
      },
      {
        label: "Thực hiện y lệnh chẩn đoán hình ảnh",
        value: 3,
      },
      {
        label: "Phụ giúp BS làm thủ thuật ",
        value: 4,
      },
      {
        label: "Đặt ống thông dạ dày",
        value: 5,
      },
      {
        label: "Đặt ống thông hậu môn",
        value: 6,
      },
      {
        label: "Đặt sonde dạ dày",
        value: 7,
      },
      {
        label: "Rút sonde các loại ",
        value: 8,
      },
      {
        label: "Đánh giá huyết áp",
        value: 9,
      },
      {
        label: "Đánh giá mạch",
        value: 10,
      },
      {
        label: "Chăm sóc catheter tĩnh mạch trung tâm",
        value: 11,
      },
      {
        label: "Chăm sóc ống nội khí quản",
        value: 12,
      },
      {
        label: "Chăm sóc catheter tĩnh mạch ngoại vi",
        value: 13,
      },
      {
        label: "Chăm sóc ống nội khí quản",
        value: 14,
      },
      {
        label: "Chăm sóc vết loét",
        value: 15,
      },
      {
        label: "Tắm cho người bệnh",
        value: 16,
      },
      {
        label: "Cho người bệnh thở oxy qua mặt nạ hoăc qua sonde mũi.",
        value: 17,
      },
      {
        label: "Truyền tĩnh mạch",
        value: 18,
      },
      {
        label: "Thực hiện y lệnh truyền dịch",
        value: 19,
      },
      {
        label: "Truyền máu",
        value: 20,
      },
    ],
  },
  {
    label2: "Thực hiện CS khác",
    type: "string",
    key: "thucHienChamSocKhac",
    colSpan2: 1,
  },
  {
    label2: "Đánh giá",
    type: "droplist",
    data: [
      {
        label: "NB ổn định",
        value: 1,
      },
      {
        label: "Thực hiện thuốc an toàn/ thực hiện thủ thuật an toàn",
        value: 2,
      },
      {
        label: "NB cần theo dõi sát",
        value: 3,
      },
      {
        label: "Trẻ hồng hào",
        value: 4,
      },
      { label: "Trẻ bú tốt", value: 5 },
    ],
    key: "danhGia",
    colSpan2: 2,
  },
  {
    label2: "Đánh giá khác",
    type: "string",
    key: "danhGiaKhac",
    colSpan2: 2,
  },

  {
    label2: "Bàn giao",
    type: "droplist",
    key: "banGiao",
    colSpan2: 2,
    data: [
      { label: "Thực hiện y lệnh thuốc", value: 1 },
      { label: "Thực hiện y lệnh CLS", value: 2 },
    ],
  },
  {
    label2: "Bàn giao khác",
    type: "string",
    key: "banGiaoKhac",
    colSpan2: 2,
  },
  {
    label2: "Tên điều dưỡng thực hiện",
    type: "sign",
    colSpan2: 2,
  },
];
// Sơ sinh thở máy PSHN
export const TR_4 = [
  {
    label1: (
      <div>
        <div>
          <b>Giờ:</b>
        </div>
        <div>
          <b>Ngày /tháng /năm</b>
        </div>
      </div>
    ),
    colSpan1: 2,
    key: "ngayThucHien",
  },

  {
    label1: "Nhận định",
    rowSpan1: 22,
    label2: "Kiểu thở",
    key: "kieuTho",
    type: "droplist",
    data: [
      { label: "HFO", value: 1 },
      { label: "SIMV", value: 2 },
      { label: "CPAP", value: 3 },
      { label: "SPONT", value: 4 },
    ],
  },
  {
    label2: "Tần số",
    type: "string",
    key: "tanSo",
  },
  {
    label2: "Áp lực đường thở PIP/PEEP",
    key: "apLucDuongTho",
    type: "string",
  },
  {
    label2: "FiO2 (%)",
    key: "fio2",
    type: "string",
  },
  {
    label2: "SPO2 (%)",
    type: "string",
    key: "chiSoSong.spo2",
  },
  {
    label2: "Nhịp tim (lần/phút)",
    type: "string",
    key: "nhipTim",
  },
  {
    label2: "Nhiệt độ (°C)",
    type: "string",
    key: "chiSoSong.nhietDo",
  },

  {
    label2: "Huyết áp (mmHg)",
    type: "string",
    key: "chiSoSong.huyetAp",
  },
  {
    label2: "Đường huyết (mmol/L)",
    type: "string",
    key: "duongHuyet",
  },
  {
    label2: "Đường thở",
    type: "droplist",
    key: "duongTho",
    data: [
      { label: "Gọng mũi", value: 1 },
      { label: "Ống NKQ 2,5", value: 2 },
      { label: "Ống NKQ 3", value: 3 },
      { label: "Ống NKQ 3,5", value: 4 },
      { label: "Ống NKQ 4", value: 5 },
      { label: "Cố định 7", value: 6 },
      { label: "Cố định 8", value: 7 },
      { label: "Cố định 9", value: 8 },
      { label: "Cố định 10", value: 9 },
    ],
  },
  {
    label2: "Dịch tịch tiết hầu họng",
    type: "droplist",
    key: "dichTietHauHong",
    data: [
      { label: "Dịch Trắng", value: 1 },
      { label: "Dịch đục", value: 2 },
      { label: "Dịch vàng", value: 3 },
      { label: "Dịch xanh", value: 4 },
      { label: "Dịch nâu", value: 5 },
      { label: "Dịch máu", value: 6 },
    ],
  },

  {
    label2: "Tri giác",
    type: "droplist",
    key: "triGiac",
    data: [
      { label: "Tỉnh", value: 1 },
      { label: "Li bì", value: 2 },
      { label: "Vật vã", value: 3 },
      { label: "Kích thích", value: 4 },
      { label: "Hôn mê", value: 5 },
      { label: "Chậm", value: 6 },
      { label: "Duy trì an thần", value: 7 },
    ],
  },
  {
    label2: "Trương lực cơ",
    type: "droplist",
    key: "truongLucCo",
    data: [
      { label: "Bình thường", value: 1 },
      { label: "Tăng", value: 2 },
      { label: "Giảm", value: 3 },
    ],
  },
  {
    label2: "Da, niêm mạc",
    type: "droplist",
    data: [
      { label: "Hồng", value: 1 },
      { label: "Xanh", value: 2 },
      { label: "Nhợt", value: 3 },
      { label: "Tái nhợt", value: 4 },
      { label: "Xanh tái", value: 5 },
      { label: "Vân tím", value: 6 },
      { label: "Vàng", value: 7 },
      { label: "Vàng nhạt", value: 8 },
      { label: "Tím", value: 9 },
    ],
    key: "da",
  },

  {
    label2: "Bụng",
    type: "droplist",
    data: [
      { label: "Mềm", value: 1 },
      { label: "Chướng", value: 2 },
      { label: "Bình thường", value: 3 },
    ],
    key: "bung",
  },
  {
    label2: "Tính chất dịch dạ dày",
    type: "droplist",
    data: [
      { label: "Sữa ăn tiêu", value: 1 },
      { label: "Nôn trớ", value: 2 },
      { label: "Sữa đang tiêu", value: 3 },
      { label: "Sữa không tiêu", value: 4 },
      { label: "Dịch xanh", value: 5 },
      { label: "Dịch vàng", value: 6 },
      { label: "Dịch máu", value: 7 },
      { label: "Dịch trong ", value: 8 },
    ],
    key: "tinhChat",
  },
  {
    label2: "Rốn",
    type: "droplist",
    data: [
      { label: "Khô", value: 1 },
      { label: "Ướt", value: 2 },
      { label: "Mùi hôi", value: 3 },
      { label: "Rỉ dịch", value: 4 },
    ],
    key: "ron",
  },
  {
    label2: "Cathater tĩnh mạch",
    type: "droplist",
    data: [
      { label: "Ngoại biên", value: 1 },
      { label: "Trung tâm", value: 2 },
      { label: "Không có", value: 3 },
      { label: "Mu bàn tay", value: 4 },
      { label: "Cẳng tay", value: 5 },
      { label: "Nách", value: 6 },
      { label: "Mu bàn chân", value: 7 },
      { label: "Cẳng chân", value: 8 },
      { label: "Đầu tĩnh mạch rốn", value: 9 },
      { label: "Tĩnh mạch trung tâm tay", value: 10 },
      { label: "Tĩnh mạch trung tâm chân", value: 11 },
      { label: "Tĩnh mạch trung tâm đầu", value: 12 },
      { label: "Hoạt động tốt", value: 13 },
      { label: "Viêm TM – VIP 1", value: 14 },
      { label: "Viêm TM – VIP 2", value: 15 },
      { label: "Viêm TM – VIP 3", value: 16 },
      { label: "Viêm TM – VIP 4", value: 17 },
      { label: "Viêm TM – VIP 5", value: 18 },
    ],
    key: "cathaterTinhMach",
  },

  {
    label2: "Nước tiểu",
    type: "string",
    key: "nuocTieu",
  },

  {
    label2: "Phân",
    type: "droplist",
    data: [
      { label: "Không có phân", value: 1 },
      { label: "Phân su", value: 2 },
      { label: "Phân hoa cà hoa cải", value: 3 },
      { label: "Phân lẫn nhày", value: 4 },
      { label: "Phân lẫn máu", value: 5 },
      { label: "Phân vàng", value: 6 },
      { label: "Phân nhiều nước", value: 7 },
    ],
    key: "phan",
  },

  {
    label2: "Cân nặng (gram)",
    type: "string",
    key: "chiSoSong.canNang",
  },
  {
    label2: "Nhận định khác",
    type: "string",
    key: "nhanDinhKhac",
  },
  {
    label1: "Chẩn đoán",
    rowSpan1: 2,
    label2: "Chẩn đoán",
    type: "droplist",
    key: "chanDoanNguyCo",
    data: [
      { label: "Nguy cơ nhiễm khuẩn", value: 1 },
      { label: "Nguy cơ vàng da", value: 2 },
      { label: "Nguy cơ hạ đường huyết", value: 3 },
      { label: "Nguy cơ suy hô hấp", value: 4 },
      { label: "Nguy cơ hạ thân nhiệt", value: 5 },
      { label: "Nguy cơ hăm", value: 6 },
      { label: "Loét do nằm lâu", value: 7 },
      { label: "Nguy cơ viêm ruột hoại tử", value: 8 },
      { label: "Nguy cơ thủng tạng rỗng", value: 9 },
      { label: "Nguy cơ viêm loét da do tỳ đè", value: 10 },
      { label: "Nguy cơ xẹp phổi", value: 11 },
      { label: "Nguy cơ sẹo vách mũi", value: 12 },
      { label: "Nguy cơ hít sặc do ăn qua sond", value: 13 },
      { label: "Nguy cơ hít sặc do dị dạng đường tiêu hóa", value: 14 },
      { label: "Nguy cơ hít sặc do nôn trớ nhiều", value: 15 },
      { label: "Suy hô hấp", value: 16 },
      { label: "Sốt", value: 17 },
      { label: "Hạ thân nhiệt", value: 18 },
      { label: "Vàng da", value: 19 },
      { label: "Nhiễm khuẩn", value: 20 },
      { label: "Loét do tỳ đè", value: 21 },
      { label: "Tổn thương vách mũi", value: 22 },
      { label: "Nguy cơ xơ phổi", value: 23 },
      { label: "Nguy cơ viêm phổi thở máy", value: 24 },
    ],
  },
  {
    label2: "Chẩn đoán khác",
    type: "string",
    key: "chanDoanNguyCoKhac",
  },
  {
    label1: "Mục tiêu",
    rowSpan1: 2,
    label2: "Mục tiêu CS",
    type: "droplist",
    key: "mucTieu",
    data: [
      { label: "Giảm nguy cơ nhiễm khuẩn/ giảm nguy cơ vàng da", value: 1 },
      {
        label: "Giảm nguy cơ suy hô hấp",
        value: 2,
      },
      {
        label: "Đảm bảo thân nhiệt trẻ ổn định",
        value: 3,
      },
      { label: "Đảm bảo vệ sinh da khô sạch", value: 4 },
      {
        label: "Giảm nguy cơ loét do tì đè tránh nguy cơ sẹo vách mũi",
        value: 5,
      },
      { label: "NB không có nguy cơ hít sặc trong quá trình cho ăn", value: 6 },
      { label: "NB không có nguy cơ hạ đường huyết", value: 7 },

      { label: "Giảm nguy cơ xẹp phổi", value: 8 },
      { label: "Phòng nguy cơ xẹp phổi", value: 9 },
      { label: "Giảm nguy cơ xơ phổi", value: 10 },
      { label: "Phòng nguy cơ xơ phổi", value: 11 },
      { label: "Giảm nguy cơ viêm phổi thở máy", value: 12 },
      { label: "Phòng nguy cơ viêm phổi thở máy", value: 13 },
    ],
  },
  {
    label2: "Mục tiêu khác",
    type: "string",
    key: "mucTieuKhac",
  },
  {
    label1: "Can thiệp điều dưỡng",
    label2: "Sữa (ml/bữa)",
    type: "string",
    key: "sua",
    rowSpan1: 6,
  },
  {
    label2: "CS da",
    key: "csDa",
    type: "droplist",
    data: [
      {
        label: "Tắm",
        value: 1,
      },
      {
        label: "Vệ sinh toàn thân",
        value: 2,
      },
      {
        label: "Thay bỉm",
        value: 3,
      },
    ],
  },
  {
    label2: "Chăm sóc hốc tự nhiên (mắt, mũi…)",
    key: "csHocTuNhien",
    type: "string",
  },
  {
    label2: "Tư thế người bệnh",
    key: "tuThe",
    type: "droplist",
    data: [
      {
        label: "Nghiêng phải",
        value: 1,
      },
      {
        label: "Ngiêng trái",
        value: 2,
      },
      {
        label: "Nằm ngửa thẳng",
        value: 3,
      },
      {
        label: "Nằm sấp",
        value: 4,
      },
      {
        label: "Đầu cao",
        value: 5,
      },
      {
        label: "Đầu bằng",
        value: 6,
      },
      {
        label: "Đầu thấp",
        value: 7,
      },
    ],
  },
  {
    label2: "Thực hiện y lệnh",
    key: "thucHienYLenh",
    type: "droplist",
    data: [
      {
        label: "Thực hiện y lệnh thuốc",
        value: 1,
      },
      {
        label: "Thực hiện y lệnh xét nghiệm",
        value: 2,
      },
      {
        label: "Thực hiện y lệnh chẩn đoán hình ảnh",
        value: 3,
      },
      {
        label: "Phụ giúp BS làm thủ thuật ",
        value: 4,
      },
      {
        label: "Đặt ống thông dạ dày",
        value: 5,
      },
      {
        label: "Đặt ống thông hậu môn",
        value: 6,
      },
      {
        label: " Đặt sonde dạ dày",
        value: 7,
      },
      {
        label: "Rút sonde các loại ",
        value: 8,
      },
      {
        label: "Đánh giá huyết áp",
        value: 9,
      },
      {
        label: " Đánh giá mạch",
        value: 10,
      },
      {
        label: "Chăm sóc catheter tĩnh mạch trung tâm",
        value: 11,
      },
      {
        label: "Chăm sóc ống nội khí quản",
        value: 12,
      },
      {
        label: "Chăm sóc catheter tĩnh mạch ngoại vi",
        value: 13,
      },
      {
        label: "Chăm sóc ống nội khí quản",
        value: 14,
      },
      {
        label: "Chăm sóc vết loét",
        value: 15,
      },
      {
        label: "Tắm cho người bệnh",
        value: 16,
      },
      {
        label: "Cho người bệnh thở oxy qua mặt nạ hoăc qua sonde mũi.",
        value: 17,
      },
      {
        label: "Truyền tĩnh mạch",
        value: 18,
      },
      {
        label: "Thực hiện y lệnh truyền dịch",
        value: 19,
      },
      {
        label: "Truyền máu",
        value: 20,
      },
      { label: "Hút dịch qua ống NKQ", value: 21 },
      { label: "Hút dịch hầu họng", value: 22 },
      { label: "Chăm sóc NB thở máy", value: 23 },
      { label: "Chăm sóc NB thở CPAP", value: 24 },
      { label: "Truyền tĩnh mạch bằng bơm tiêm điện", value: 25 },
      { label: "Chiếu đèn", value: 26 },
      { label: "Theo dõi đường huyết", value: 27 },
      { label: "Theo dõi nước tiểu", value: 28 },
      { label: "Theo dõi thân nhiệt", value: 29 },
    ],
  },
  {
    label2: "Thực hiện CS khác",
    type: "string",
    key: "thucHienChamSocKhac",
    colSpan2: 1,
  },
  {
    label2: "Đánh giá",
    type: "droplist",
    data: [
      {
        label: "NB ổn định",
        value: 1,
      },
      {
        label: "Thực hiện thuốc an toàn/ thực hiện thủ thuật an toàn",
        value: 2,
      },
      {
        label: "NB cần theo dõi sát",
        value: 3,
      },
      {
        label: "Trẻ hồng hào",
        value: 4,
      },
      {
        label: "Trẻ bú tốt",
        value: 5,
      },
      {
        label: "NB nặng",
        value: 6,
      },
      {
        label: "NB nguy kịch",
        value: 7,
      },
      {
        label: "NB tiên lượng tử vong",
        value: 8,
      },
    ],
    key: "danhGia",
    colSpan2: 2,
  },
  {
    label2: "Đánh giá khác",
    type: "string",
    key: "danhGiaKhac",
    colSpan2: 2,
  },
  {
    label2: "Bàn giao",
    type: "droplist",
    key: "banGiao",
    colSpan2: 2,
    data: [
      { label: "Thực hiện y lệnh thuốc", value: 1 },
      { label: "Thực hiện y lệnh CLS", value: 2 },
      { label: "NB nặng", value: 3 },
      { label: "NB cần theo dõi sát", value: 4 },
    ],
  },
  {
    label2: "Bàn giao khác",
    type: "string",
    key: "banGiaoKhac",
    colSpan2: 2,
  },
  {
    label2: "Tên điều dưỡng thực hiện",
    type: "sign",
    key: "banGiao",
    colSpan2: 2,
  },
];
// Sơ sinh không thở máy PSHN
export const TR_5 = [
  {
    label1: (
      <div>
        <div>
          <b>Giờ:</b>
        </div>
        <div>
          <b>Ngày /tháng /năm</b>
        </div>
      </div>
    ),
    colSpan1: 2,
    key: "ngayThucHien",
  },

  {
    label1: "Nhận định",
    rowSpan1: 16,
    label2: "SPO2(%)",
    key: "chiSoSong.spo2",
    type: "string",
  },

  {
    label2: "Nhịp thở (lần/phút)",
    key: "chiSoSong.nhipTho",
    type: "string",
  },
  {
    label2: "Nhịp tim (l/phút)",
    key: "nhipTim",
    type: "string",
  },
  {
    label2: "Nhiệt độ (°C)",
    key: "chiSoSong.nhietDo",
    type: "string",
  },
  {
    label2: "Tri giác",
    type: "droplist",
    key: "triGiac",
    data: [
      { label: "Tỉnh", value: 1 },
      { label: "Li bì", value: 2 },
      { label: "Vật vã", value: 3 },
      { label: "Kích thích", value: 4 },
      { label: "Hôn mê", value: 5 },
      { label: "Chậm", value: 6 },
      { label: "Quấy khóc", value: 7 },
      { label: "Phản xạ tốt", value: 8 },
      { label: "Phản xạ yếu", value: 9 },
      { label: "Phản xạ trung bình", value: 10 },
      { label: "Tăng trương cơ lục", value: 11 },
      { label: "Giảm trương cơ lực", value: 12 },
    ],
  },

  {
    label2: "Hô hấp",
    type: "droplist",
    data: [
      { label: "Không khó thở", value: 1 },
      { label: "Khó thở nhẹ", value: 2 },
      { label: "Khó thở kèm rút lõm cơ hô hấp", value: 3 },
      { label: "Thở qua Mask", value: 4 },
      { label: "Trẻ tự thở", value: 5 },
      { label: "Thở gắng sức", value: 6 },
      { label: "Thở CPAP qua Mask", value: 7 },
      { label: "Thở oxi qua Mask", value: 8 },
      { label: "Thở oxy gọng mũi", value: 9 },
      { label: "Thở oxy lều", value: 10 },
      { label: "Thở oxy qua ống NKQ", value: 11 },
      { label: "Thở oxy ngắt quãng", value: 12 },
    ],
    key: "hoHap",
  },

  {
    label2: "Da, niêm mạc",
    type: "droplist",
    data: [
      { label: "Hồng", value: 1 },
      { label: "Xanh", value: 2 },
      { label: "Nhợt", value: 3 },
      { label: "Tái nhợt", value: 4 },
      { label: "Xanh tái", value: 5 },
      { label: "Vân tím", value: 6 },
      { label: "Vàng", value: 7 },
      { label: "Vàng nhạt", value: 8 },
      { label: "Tím", value: 9 },
    ],
    key: "da",
  },

  {
    label2: "Nước tiểu",
    type: "string",

    key: "nuocTieu",
  },
  {
    label2: "Phân",
    type: "droplist",
    data: [
      { label: "Không có phân", value: 1 },
      { label: "Phân su", value: 2 },
      { label: "Phân hoa cà hoa cải", value: 3 },
      { label: "Phân lẫn nhày", value: 4 },
      { label: "Phân lẫn máu", value: 5 },
      { label: "Phân vàng", value: 6 },
      { label: "Phân nhiều nước", value: 7 },
    ],
    key: "phan",
  },

  {
    label2: "Cân nặng (gram)",
    type: "string",
    key: "chiSoSong.canNang",
  },
  {
    label2: "Đường huyết(mmol/L)",
    type: "string",
    key: "duongHuyet",
  },
  {
    label2: "Bụng",
    type: "droplist",
    key: "bung",
    data: [
      { label: "Mềm", value: 1 },
      { label: "Chướng", value: 2 },
      { label: "Bình thường", value: 3 },
    ],
    mode: "onlyOne",
    dataIsArr: true,
  },
  {
    label2: "Tính chất",
    type: "droplist",
    key: "tinhChat",
    data: [
      { label: "Ăn tiêu", value: 1 },
      { label: "Nôn trớ", value: 2 },
      { label: "Sữa đang tiêu", value: 3 },
      { label: "Sữa không tiêu", value: 4 },
      { label: "Dịch xanh", value: 5 },
      { label: "Dịch vàng", value: 6 },
      { label: "Dịch máu", value: 7 },
      { label: "Dịch trong", value: 8 },
    ],
  },
  {
    label2: "Rốn",
    type: "droplist",
    key: "ron",
    data: [
      { label: "Khô", value: 1 },
      { label: "Ướt", value: 2 },
      { label: "Mùi hôi", value: 3 },
      { label: "Rỉ dịch", value: 4 },
    ],
    mode: "onlyOne",
    dataIsArr: true,
  },
  {
    label2: "Cathater tĩnh mạch",
    type: "droplist",
    key: "cathaterTinhMach",
    data: [
      { label: "Ngoài biên", value: 1 },
      { label: "Trung tâm", value: 2 },
      { label: "Không có", value: 3 },
      { label: "Mu bàn tay", value: 4 },
      { label: "Cẳng tay", value: 5 },
      { label: "Nách", value: 6 },
      { label: "Mu bàn chân", value: 7 },
      { label: "Cằng chân", value: 8 },
      { label: "Đầu", value: 9 },
      { label: "Tĩnh mạch rốn", value: 10 },
      { label: "Tĩnh mạch trung tâm tay", value: 11 },
      { label: "Tĩnh mạch trung tâm chân", value: 12 },
      { label: "Tĩnh mạch trung tâm đầu", value: 13 },
      { label: "Hoạt động tốt", value: 14 },
      { label: "Viêm TM-VIP1", value: 15 },
      { label: "Viêm TM-VIP2", value: 16 },
      { label: "Viêm TM-VIP3", value: 17 },
      { label: "Viêm TM-VIP4", value: 18 },
      { label: "Viêm TM-VIP5", value: 19 },
    ],
  },

  {
    label2: "Nhận định khác",
    type: "string",
    key: "nhanDinhKhac",
  },
  {
    label2: "GDSK",
    type: "droplist",
    key: "tuVanGiaoDuc",
    colSpan2: 2,
    data: [
      {
        label: "Tư vấn giáo dục sức khoẻ cho NB trong ngày đầu vào viện",
        value: 1,
      },
      {
        label: "Tư vấn giáo dục sức khoẻ cho NB trong thời gian nằm viện",
        value: 2,
      },
      {
        label: "Tư vấn giáo dục sức khoẻ cho NB chuẩn bị ra viện",
        value: 3,
      },
    ],
  },
  {
    label1: "Chẩn đoán",
    rowSpan1: 2,
    label2: "Chẩn đoán",
    type: "droplist",
    key: "chanDoanNguyCo",
    data: [
      { label: "Nguy cơ nhiễm khuẩn", value: 1 },
      { label: "Nguy cơ vàng da", value: 2 },
      { label: "Nguy cơ hạ đường huyết", value: 3 },
      { label: "Nguy cơ suy hô hấp", value: 4 },
      { label: "Nguy cơ hạ thân nhiệt", value: 5 },
      { label: "Nguy cơ hăm", value: 6 },
      { label: "Loét do nằm lâu", value: 7 },
      { label: "Nguy cơ viêm ruột hoại tử", value: 8 },
      { label: "Nguy cơ thủng tạng rỗng", value: 9 },
      { label: "Nguy cơ viêm loét da do tỳ đè", value: 10 },
      { label: "Nguy cơ xẹp phổi", value: 11 },
      { label: "Nguy cơ sẹo vách mũi", value: 12 },
      { label: "Nguy cơ hít sặc do ăn qua sond", value: 13 },
      { label: "Nguy cơ hít sặc do dị dạng đường tiêu hóa", value: 14 },
      { label: "Nguy cơ hít sặc do nôn trớ nhiều", value: 15 },
      { label: "Suy hô hấp", value: 16 },
      { label: "Sốt", value: 17 },
      { label: "Hạ thân nhiệt", value: 18 },
      { label: "Vàng da", value: 19 },
      { label: "Nhiễm khuẩn", value: 20 },
      { label: "Loét do tỳ đè", value: 21 },
      { label: "Tổn thương vách mũi", value: 22 },
      { label: "Nguy cơ xơ phổi", value: 23 },
    ],
  },
  {
    label2: "Chẩn đoán khác",
    type: "string",
    key: "chanDoanNguyCoKhac",
  },
  {
    label1: "Mục tiêu",
    rowSpan1: 2,
    label2: "Mục tiêu",
    type: "droplist",
    key: "mucTieu",
    data: [
      { label: "Giảm nguy cơ nhiễm khuẩn/ giảm nguy cơ vàng da", value: 1 },
      {
        label: "Giảm nguy cơ suy hô hấp đảm bảo thân nhiệt trẻ ổn định",
        value: 2,
      },
      { label: "Đảm bảo vệ sinh da khô sạch", value: 3 },
      {
        label: "Giảm nguy cơ loét do tì đè tránh nguy cơ sẹo vách mũi",
        value: 4,
      },
      { label: "NB không có nguy cơ hít sặc trong quá trình cho ăn", value: 5 },
      { label: "NB không có nguy cơ hạ đường huyết", value: 6 },
      { label: "Giảm nguy cơ xẹp phổi", value: 7 },
      { label: "Phòng nguy cơ xẹp phổi", value: 8 },
      { label: "Giảm nguy cơ xơ phổi", value: 9 },
      { label: "Phòng nguy cơ xơ phổi", value: 10 },
    ],
  },
  {
    label2: "Mục tiêu khác",
    type: "string",
    key: "mucTieuKhac",
  },
  {
    label1: "Can thiệp điều dưỡng",
    label2: "Sữa (ml)",
    type: "string",
    key: "sua",
    rowSpan1: 7,
  },
  {
    label2: "Thở Oxy (lít/phút)",
    type: "string",
    key: "thoOxy",
  },
  {
    label2: "Chăm sóc da",
    key: "csDa",
    type: "droplist",
    data: [
      {
        label: "Tắm",
        value: 1,
      },
      {
        label: "VS toàn thân",
        value: 2,
      },
      {
        label: "Thay bỉm",
        value: 3,
      },
      { label: "Bôi kem dưỡng ẩm", value: 4 },
      { label: "Bôi kem hăm", value: 5 },
      { label: "Bôi kem theo Chỉ định", value: 6 },
    ],
  },

  {
    label2: "Chăm sóc hốc tự nhiên",
    key: "csHocTuNhien",
    type: "string",
  },
  {
    label2: "Tư thế",
    key: "tuThe",
    type: "droplist",
    data: [
      {
        label: "Nghiêng phải",
        value: 1,
      },
      {
        label: "Ngiêng trái",
        value: 2,
      },
      {
        label: "Nằm ngửa",
        value: 3,
      },
      {
        label: "Nằm sấp",
        value: 4,
      },
      {
        label: "Đầu cao",
        value: 5,
      },
      {
        label: "Đầu bằng",
        value: 6,
      },
      {
        label: "Đầu thấp",
        value: 7,
      },
    ],
  },

  {
    label2: "Thực hiện y lệnh",
    key: "thucHienYLenh",
    type: "droplist",
    data: [
      {
        label: "Thực hiện y lệnh thuốc",
        value: 1,
      },
      {
        label: "Thực hiện y lệnh xét nghiệm",
        value: 2,
      },
      {
        label: "Thực hiện y lệnh chẩn đoán hình ảnh",
        value: 3,
      },
      {
        label: "Phụ giúp BS làm thủ thuật ",
        value: 4,
      },
      {
        label: "Đặt ống thông dạ dày",
        value: 5,
      },
      {
        label: "Đặt ống thông hậu môn",
        value: 6,
      },
      {
        label: " Đặt sonde dạ dày",
        value: 7,
      },
      {
        label: "Rút sonde các loại ",
        value: 8,
      },
      {
        label: "Đánh giá huyết áp",
        value: 9,
      },
      {
        label: " Đánh giá mạch",
        value: 10,
      },
      {
        label: "Chăm sóc catheter tĩnh mạch trung tâm",
        value: 11,
      },
      {
        label: "Chăm sóc ống nội khí quản",
        value: 12,
      },
      {
        label: "Chăm sóc catheter tĩnh mạch ngoại vi",
        value: 13,
      },
      {
        label: "Chăm sóc ống nội khí quản",
        value: 14,
      },
      {
        label: "Chăm sóc vết loét",
        value: 15,
      },
      {
        label: "Tắm cho người bệnh",
        value: 16,
      },
      {
        label: "Cho người bệnh thở oxy qua mặt nạ hoăc qua sonde mũi.",
        value: 17,
      },
      {
        label: "Truyền tĩnh mạch",
        value: 18,
      },
      {
        label: "Thực hiện y lệnh truyền dịch",
        value: 19,
      },
      {
        label: "Truyền máu",
        value: 20,
      },
      { label: "Chiếu đèn", value: 21 },
      { label: "Theo dõi đường huyết", value: 22 },
      { label: "Theo dõi nước tiểu", value: 23 },
      { label: "Theo dõi thân nhiệt", value: 24 },
      { label: "Theo dõi dịch vào ra", value: 25 },
      { label: "Tập bú", value: 26 },
      { label: "Theo dõi tím", value: 27 },
    ],
  },
  {
    label2: "Thực hiện CS khác",
    type: "string",
    key: "thucHienChamSocKhac",
    colSpan2: 1,
  },

  {
    label2: "Đánh giá",
    type: "droplist",
    data: [
      {
        label: "NB ổn định",
        value: 1,
      },
      {
        label: "Thực hiện thuốc an toàn/ thực hiện thủ thuật an toàn",
        value: 2,
      },
      {
        label: "NB cần theo dõi sát",
        value: 3,
      },
      {
        label: "Trẻ hồng hào",
        value: 4,
      },
      {
        label: "Trẻ bú tốt",
        value: 5,
      },
      {
        label: "NB nặng",
        value: 6,
      },
      {
        label: "NB nguy kịch",
        value: 7,
      },
      {
        label: "NB tiên lượng tử vong",
        value: 8,
      },
      {
        label: "Trẻ tự bú được",
        value: 9,
      },
    ],
    key: "danhGia",
    colSpan2: 2,
  },
  {
    label2: "Đánh giá khác",
    type: "string",
    key: "danhGiaKhac",
    colSpan2: 2,
  },

  {
    label2: "Bàn giao",
    type: "droplist",
    key: "banGiao",
    colSpan2: 2,
    data: [
      { label: "Thực hiện y lệnh thuốc", value: 1 },
      { label: "Thực hiện y lệnh CLS", value: 2 },
      { label: "NB nặng", value: 3 },
      { label: "NB cần theo dõi sát", value: 4 },
    ],
  },
  {
    label2: "Bàn giao khác",
    type: "string",
    key: "banGiaoKhac",
    colSpan2: 2,
  },
  {
    label2: "Tên điều dưỡng thực hiện",
    type: "sign",
    key: "banGiao",
    colSpan2: 2,
  },
];
// Sơ sinh can thiệp PSHN
export const TR_6 = [
  {
    label1: (
      <div>
        <div>
          <b>Giờ:</b>
        </div>
        <div>
          <b>Ngày /tháng /năm</b>
        </div>
      </div>
    ),
    colSpan1: 2,
    key: "ngayThucHien",
  },

  {
    label1: "Nhận định",
    rowSpan1: 17,
    label2: "Nhịp thở (lần/phút)",
    key: "chiSoSong.nhipTho",
    type: "string",
  },
  {
    label2: "Nhịp tim (l/phút)",
    key: "nhipTim",
    type: "string",
  },
  {
    label2: "Nhiệt độ (°C)",
    key: "chiSoSong.nhietDo",
    type: "string",
  },
  {
    label2: "SPO2",
    key: "chiSoSong.spo2",
    type: "string",
  },
  {
    label2: "Tri giác",
    type: "droplist",
    key: "triGiac",
    data: [
      { label: "Tỉnh", value: 1 },
      { label: "Li bì", value: 2 },
      { label: "Quấy khóc", value: 3 },
      { label: "Lơ mơ", value: 4 },
    ],
  },
  {
    label2: "Hô hấp",
    type: "droplist",
    data: [
      { label: "Không khó thở", value: 1 },
      { label: "Khó thở nhẹ", value: 2 },
      { label: "Khó thở khi gắng sức", value: 3 },
      { label: "Khó thở kèm rút lõm cơ hô hấp", value: 4 },
      { label: "Thở qua Mask", value: 5 },
      { label: "Thở oxi", value: 6 },
      { label: "Trẻ tự thở", value: 7 },
      { label: "Thở khò khè", value: 8 },
      { label: "Thở rít", value: 9 },
      { label: "Thở đều", value: 10 },
    ],
    key: "hoHap",
  },
  {
    label2: "Trương lực cơ",
    type: "droplist",
    data: [
      { label: "Tốt", value: 1 },
      { label: "Mềm", value: 2 },
      { label: "Nhão", value: 3 },
      { label: "Khác", value: 4 },
    ],
    key: "truongLucCo",
    mode: "onlyOne",
    dataIsArr: true,
  },
  {
    label2: "Phản xạ",
    type: "droplist",
    data: [
      { label: "Tốt", value: 1 },
      { label: "Trung bình", value: 2 },
      { label: "Chậm", value: 3 },
    ],
    key: "phanXa",
    mode: "onlyOne",
    dataIsArr: true,
  },
  {
    label2: "Tiếng khóc bất thường",
    type: "droplist",
    data: [
      { label: "Không", value: 1 },
      { label: "Có", value: 2 },
      { label: "Rên", value: 3 },
      { label: "Thét", value: 4 },
    ],
    key: "khocBatThuong",
    mode: "onlyOne",
    dataIsArr: true,
  },
  {
    label2: "Da",
    type: "droplist",
    data: [
      { label: "Hồng", value: 1 },
      { label: "Xanh", value: 2 },
      { label: "Nhợt", value: 3 },
      { label: "Tái nhợt", value: 4 },
      { label: "Xanh tái", value: 5 },
      { label: "Vân tím", value: 6 },
      { label: "Vàng", value: 7 },
      { label: "Tím", value: 8 },
      { label: "Vàng nhạt", value: 9 },
      { label: "Vàng đậm", value: 10 },
      { label: "Vàng toàn thân", value: 11 },
      { label: "Da nổi mẩn/ban đỏ", value: 12 },
    ],
    key: "da",
  },
  {
    label2: "Tình trạng bú",
    type: "droplist",
    data: [
      { label: "Bú đạt", value: 1 },
      { label: "Bú kém", value: 2 },
      { label: "Bỏ bú", value: 3 },
      { label: "Ăn sonde", value: 4 },
      { label: "Ăn thìa", value: 5 },
    ],
    key: "tinhTrangBu",
    mode: "onlyOne",
    dataIsArr: true,
  },
  {
    label2: "Nôn trớ",
    type: "droplist",
    data: [
      { label: "Không", value: 1 },
      { label: "Dịch trong", value: 2 },
      { label: "Dịch nâu", value: 3 },
      { label: "Dịch vàng", value: 4 },
      { label: "Dịch đỏ", value: 5 },
      { label: "Dịch máu", value: 6 },
    ],
    key: "nonTro",
    mode: "onlyOne",
    dataIsArr: true,
  },
  {
    label2: "Bụng",
    type: "droplist",
    data: [
      { label: "Bụng mềm", value: 1 },
      { label: "Bụng chướng ít", value: 2 },
      { label: "Bụng chướng nhiều", value: 3 },
    ],
    key: "bung",
    mode: "onlyOne",
    dataIsArr: true,
  },
  {
    label2: "Rốn",
    type: "droplist",
    data: [
      { label: "Rốn khô", value: 1 },
      { label: "Rốn ướt", value: 2 },
      { label: "Rốn có mùi hôi", value: 3 },
      { label: "Rốn rỉ dịch", value: 3 },
      { label: "Rốn rỉ máu", value: 3 },
    ],
    key: "ron",
    mode: "onlyOne",
    dataIsArr: true,
  },
  {
    label2: "Nước tiểu",
    type: "droplist",
    data: [
      { label: "Có", value: 1 },
      { label: "Không", value: 2 },
      { label: "Không rõ", value: 3 },
    ],
    key: "nuocTieu",
    mode: "onlyOne",
  },
  {
    label2: "Phân",
    type: "droplist",
    data: [
      { label: "Không có phân", value: 1 },
      { label: "Phân su", value: 2 },
      { label: "Phân hoa cà hoa cải", value: 3 },
      { label: "Phân lẫn nhày", value: 4 },
      { label: "Phân lẫn máu", value: 5 },
      { label: "Chưa thải", value: 6 },
      { label: "Đã thải", value: 7 },
    ],
    key: "phan",
  },
  {
    label2: "Nhận định khác",
    type: "string",
    key: "nhanDinhKhac",
  },

  {
    label1: "Chẩn đoán",
    rowSpan1: 2,
    label2: "Chẩn đoán",
    type: "droplist",
    key: "chanDoanNguyCo",
    data: [
      { label: "Nguy cơ nhiễm khuẩn", value: 1 },
      { label: "Nguy cơ vàng da", value: 2 },
      { label: "Nguy cơ hạ đường huyết", value: 3 },
      { label: "Nguy cơ suy hô hấp", value: 4 },
      { label: "Nguy cơ hạ thân nhiệt", value: 5 },
      { label: "Nguy cơ hăm", value: 6 },
      { label: "Loét do nằm lâu", value: 7 },
      { label: "Nguy cơ viêm ruột hoại tử", value: 8 },
      { label: "Nguy cơ thủng tạng rỗng", value: 9 },
      { label: "Nguy cơ viêm loét da do tỳ đè", value: 10 },
      { label: "Nguy cơ xẹp phổi", value: 11 },
      { label: "Nguy cơ sẹo vách mũi", value: 12 },
      { label: "Nguy cơ hít sặc do ăn qua sond", value: 13 },
      { label: "Nhiễm khuẩn", value: 14 },
      { label: "Sốt", value: 15 },
      { label: "Vàng da", value: 16 },
      { label: "Loét tỳ đè", value: 17 },
      { label: "Nguy cơ viêm phổi", value: 18 },
      { label: "Nguy cơ loét tỳ đè", value: 19 },
      { label: "Bỏng", value: 20 },
      { label: "Nguy cơ co giật", value: 21 },
      { label: "Nôn trớ", value: 22 },
      { label: "Bụng chướng", value: 23 },
      { label: "Nguy cơ xuất huyết tiêu hoá", value: 24 },
      { label: "Hạ thân nhiệt", value: 25 },
      { label: "Hạ đường huyết", value: 26 },
    ],
  },
  {
    label2: "Chẩn đoán khác",
    type: "string",
    key: "chanDoanNguyCoKhac",
  },

  {
    label1: "Mục tiêu",
    rowSpan1: 2,
    label2: "Mục tiêu",
    type: "droplist",
    key: "mucTieu",
    data: [
      { label: "Giảm nguy cơ nhiễm khuẩn/ giảm nguy cơ vàng da", value: 1 },
      {
        label: "Giảm nguy cơ suy hô hấp đảm bảo thân nhiệt trẻ ổn định",
        value: 2,
      },
      { label: "Đảm bảo vệ sinh da khô sạch", value: 3 },
      {
        label: "Giảm nguy cơ loét do tì đè tránh nguy cơ sẹo vách mũi",
        value: 4,
      },
      { label: "NB không có nguy cơ hít sặc trong quá trình cho ăn", value: 5 },
      { label: "NB không có nguy cơ hạ đường huyết", value: 6 },
      { label: "Giảm sốt", value: 7 },
      { label: "Giảm nôn trớ", value: 8 },
      { label: "Giảm bụng chướng", value: 9 },
    ],
  },
  {
    label2: "Mục tiêu khác",
    type: "string",
    key: "mucTieuKhac",
  },

  {
    label1: "Thực hiện chăm sóc",
    label2: "Thực hiện y lệnh",
    key: "thucHienYLenh",
    rowSpan1: 2,
    type: "droplist",
    data: [
      {
        label: "Thực hiện y lệnh thuốc",
        value: 1,
      },
      {
        label: "Thực hiện y lệnh xét nghiệm",
        value: 2,
      },
      {
        label: "Thực hiện y lệnh chẩn đoán hình ảnh",
        value: 3,
      },
      {
        label: "Phụ giúp BS làm thủ thuật ",
        value: 4,
      },
      {
        label: "Đặt ống thông dạ dày",
        value: 5,
      },
      {
        label: "Đặt ống thông hậu môn",
        value: 6,
      },
      {
        label: "Đặt sonde dạ dày",
        value: 7,
      },
      {
        label: "Rút sonde các loại ",
        value: 8,
      },
      {
        label: "Đánh giá huyết áp",
        value: 9,
      },
      {
        label: "Đánh giá mạch",
        value: 10,
      },
      {
        label: "Chăm sóc catheter tĩnh mạch trung tâm",
        value: 11,
      },
      {
        label: "Chăm sóc ống nội khí quản",
        value: 12,
      },
      {
        label: "Chăm sóc catheter tĩnh mạch ngoại vi",
        value: 13,
      },
      {
        label: "Chăm sóc ống nội khí quản",
        value: 14,
      },
      {
        label: "Chăm sóc vết loét",
        value: 15,
      },
      {
        label: "Tắm cho người bệnh",
        value: 16,
      },
      {
        label: "Cho người bệnh thở oxy qua mặt nạ hoăc qua sonde mũi.",
        value: 17,
      },
      {
        label: "Truyền tĩnh mạch",
        value: 18,
      },
      {
        label: "Thực hiện y lệnh truyền dịch",
        value: 19,
      },
      {
        label: "Truyền máu",
        value: 20,
      },
    ],
  },
  {
    label2: "Thực hiện CS khác",
    type: "string",
    key: "thucHienChamSocKhac",
    colSpan2: 1,
  },

  {
    label2: "Đánh giá",
    type: "droplist",
    data: [
      {
        label: "NB ổn định",
        value: 1,
      },
      {
        label: "Thực hiện thuốc an toàn/ thực hiện thủ thuật an toàn",
        value: 2,
      },
      {
        label: "NB cần theo dõi sát",
        value: 3,
      },
      {
        label: "Trẻ hồng hào",
        value: 4,
      },
      { label: "Trẻ bú tốt", value: 5 },
    ],
    key: "danhGia",
    colSpan2: 2,
  },
  {
    label2: "Đánh giá khác",
    type: "string",
    key: "danhGiaKhac",
    colSpan2: 2,
  },

  {
    label2: "Bàn giao",
    type: "droplist",
    key: "banGiao",
    colSpan2: 2,
    data: [
      { label: "Thực hiện y lệnh thuốc", value: 1 },
      { label: "Thực hiện y lệnh CLS", value: 2 },
      { label: "Không", value: 3 },
    ],
  },
  {
    label2: "Bàn giao khác",
    type: "string",
    key: "banGiaoKhac",
    colSpan2: 2,
  },
  {
    label2: "Tên điều dưỡng thực hiện",
    type: "sign",
    colSpan2: 2,
  },
];
