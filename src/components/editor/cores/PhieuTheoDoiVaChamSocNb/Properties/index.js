import React, {
  useState,
  useEffect,
  useImperativeHandle,
  forwardRef,
} from "react";
import T from "prop-types";
import { Row, Col, Select, InputNumber, Checkbox, Input } from "antd";
import { Main } from "./styled";
import { useTranslation } from "react-i18next";
import { FontColorsOutlined } from "@ant-design/icons";
import {
  EditorTool,
  AlignConfig,
  FontSizeConfig,
} from "components/editor/config";
import { DS_LOAI_KY, VI_TRI_CA } from "../../ImageSign/constanst";
const { PickColor } = EditorTool;

const PhieuTheoDoiVaChamSocNbProperties = forwardRef((props, ref) => {
  const { t } = useTranslation();
  const { apiFields } = props;
  const [state, _setState] = useState({});
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    if (props.state.key) {
      let newState = {
        ...props.state.props,
      };
      setState(newState);
    }
  }, [props.state]);

  useImperativeHandle(ref, () => {
    return {
      ...state,
    };
  });

  const onChangeValue = (type) => (value) => {
    setState({
      [type]: value,
    });
  };

  const onChangeCheckbox = (type) => (e) => {
    onChangeValue(type)(e.target.checked);
  };

  const onChangeInput = (type) => (e) => {
    onChangeValue(type)(e.target.value);
    setState({
      [type]: e.target.value,
    });
  };

  const changeCheckbox = (target) => (e) => {
    setState({
      [target]: e.target.checked,
    });
  };

  return (
    <Main>
      <Row gutter={[12, 12]}>
        <Col span={8}>
          <span>{t("editor.loaiKy")}</span>
        </Col>
        <Col span={16}>
          <Select
            showSearch
            size={"small"}
            style={{ width: "100%" }}
            value={state.loaiKy}
            onSelect={onChangeValue("loaiKy")}
          >
            {DS_LOAI_KY.map((item, index) => (
              <Select.Option key={index} value={item.id}>
                {t(item.ten)}
              </Select.Option>
            ))}
          </Select>
        </Col>

        <Col span={8}>
          <span>{t("editor.capKy")}</span>
        </Col>
        <Col span={16}>
          <InputNumber
            type="number"
            size={"small"}
            min={1}
            value={state.capKy}
            onChange={onChangeValue("capKy")}
          ></InputNumber>
        </Col>
        <Col span={8}>{t("editor.hienTenChuKyVietTat")}</Col>
        <Col span={16}>
          <Checkbox
            style={{ width: "100%" }}
            onChange={changeCheckbox("showTenVietTat")}
            checked={state.showTenVietTat}
          />
        </Col>
        <Col span={8}>
          <span>{t("editor.rong")}</span>
        </Col>
        <Col span={16}>
          <InputNumber
            type="number"
            size={"small"}
            min={1}
            value={state.width}
            onChange={onChangeValue("width")}
          />
        </Col>
        <Col span={8}>
          <span>{t("editor.cao")}</span>
        </Col>
        <Col span={16}>
          <InputNumber
            type="number"
            size={"small"}
            min={1}
            value={state.height}
            onChange={onChangeValue("height")}
          />
        </Col>
        <Col span={8}>
          <span>{t("editor.viTriChuKy")}</span>
        </Col>
        <Col span={16}>
          <AlignConfig
            changeAlign={onChangeValue("contentAlign")}
            contentAlign={state.contentAlign}
          />
        </Col>
        <Col span={8}>
          <span>{t("editor.hienThiNguoiKy")}</span>
        </Col>
        <Col span={16}>
          <Checkbox
            checked={state.showPatientSign}
            onChange={changeCheckbox("showPatientSign")}
          />
        </Col>
        {state.showPatientSign && (
          <>
            <Col span={8}>
              <span>{t("editor.anThoiGianKy")}</span>
            </Col>
            <Col span={16}>
              <Checkbox
                checked={state.anThoiGianKy}
                onChange={changeCheckbox("anThoiGianKy")}
              />
            </Col>
            <Col span={8}>
              <span>{t("editor.viTriNguoiKy")}</span>
            </Col>
            <Col span={16}>
              <Select
                showSearch
                size={"small"}
                style={{ width: "100%" }}
                value={state.viTriCa}
                onSelect={onChangeValue("viTriCa")}
              >
                {VI_TRI_CA.map((item, index) => (
                  <Select.Option key={index} value={item.id}>
                    {t(item.ten)}
                  </Select.Option>
                ))}
              </Select>
            </Col>
            <Col span={8}>
              <span>{t("editor.mauChuNguoiKy")}</span>
            </Col>
            <Col span={16}>
              <PickColor
                iconComponent={FontColorsOutlined}
                title={t("editor.chonMauChu")}
                dataColor={state.contentColor || "black"}
                changeColor={onChangeValue("contentColor")}
              />
            </Col>
            <Col span={8}>
              <span>{t("editor.coChuNguoiKy")}</span>
            </Col>
            <Col span={16}>
              <FontSizeConfig
                changeFont={onChangeValue("fontSize")}
                fontSize={state.fontSize}
              />
            </Col>
          </>
        )}
        <Col span={8}>
          <span>{t("editor.hienThiCA")}</span>
        </Col>
        <Col span={16}>
          <Checkbox
            checked={state.showCa}
            onChange={changeCheckbox("showCa")}
          />
        </Col>
        {state.showCa && (
          <>
            <Col span={8}>
              <span>{t("editor.viTriCA")}</span>
            </Col>
            <Col span={16}>
              <Select
                showSearch
                size={"small"}
                style={{ width: "100%" }}
                value={state.viTriAnhCa}
                onSelect={onChangeValue("viTriAnhCa")}
              >
                {VI_TRI_CA.map((item, index) => (
                  <Select.Option key={index} value={item.id}>
                    {t(item.ten)}
                  </Select.Option>
                ))}
              </Select>
            </Col>
            <Col span={8}>
              <span>{t("editor.doRongCA")}</span>
            </Col>
            <Col span={16}>
              <InputNumber
                type="number"
                size={"small"}
                min={1}
                value={state.widthCa}
                onChange={onChangeValue("widthCa")}
              ></InputNumber>
            </Col>
          </>
        )}

        <Col span={8}>
          <span>{t("editor.khoaBieuMauSauKhiKy")}</span>
        </Col>
        <Col span={16}>
          <Checkbox
            checked={state.disableIfSigned}
            onChange={changeCheckbox("disableIfSigned")}
          />
        </Col>

        <Col span={8}>
          <span>{t("editor.choPhepHuyKhiDaKy")}</span>
        </Col>
        <Col span={16}>
          <Checkbox
            checked={state.allowReset}
            onChange={changeCheckbox("allowReset")}
          />
        </Col>
      </Row>
    </Main>
  );
});

PhieuTheoDoiVaChamSocNbProperties.defaultProps = {
  state: {},
};

PhieuTheoDoiVaChamSocNbProperties.propTypes = {
  state: T.shape({}),
};

export default PhieuTheoDoiVaChamSocNbProperties;
