import React, {
  useEffect,
  useState,
  useRef,
  useMemo,
  forwardRef,
  useImperativeHandle,
  memo,
  useCallback,
} from "react";
import moment from "moment";
import { convert } from "utils/editor-utils";
import { combineFields, MODE } from "utils/editor-utils";
import Row from "./Row";
import { cloneDeep, debounce, get } from "lodash";
import { useTranslation } from "react-i18next";
import { useConfirm } from "hooks";
import { isObject } from "utils";
import { client } from "client/request";
import { getAllQueryString } from "hooks/useQueryString/queryString";

const Khung = forwardRef((props, ref) => {
  const { showConfirm } = useConfirm();
  const queries = getAllQueryString();

  const {
    rows,
    cols,
    checkDisableWhenRowSigned,
    fieldName,
    colSelected,
    fileConfig,
    formChange,
    mode,
    updateComponents,
    component,
    formId,
    form,
    localComponents,
    keysHadConfig,
    //value emr global sử dụng trong replication row khi replication row truyền vào là form chính là valuerow
    //values = valueEMR khi không nằm trong replication row
    valueEMR,
    valuesHIS,
    onRowsLengthChange,
    isDeleteRow,
    isPhieuChamSoc,
    minRow,
    viTriKyBatDau,
    gopVoiDuLieu2,
    copyDuLieuHangTrenKhiThemMoi,
    hienThiDuLieuTuAPI,
    dataApi,
    variable,
    hienThiDuLieuMoiLenDau,
  } = props;
  const [state, _setState] = useState({
    tableRows: [],
  });

  const refDuLieu2 = useRef([]);
  const refTimeoutCallData = useRef();

  const [listSignKey, setListSignKey] = useState({});
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const { t } = useTranslation();
  const prevValuesRef = useRef([]);
  useImperativeHandle(ref, () => ({
    onAddRow,
  }));

  const rowCopy = useMemo(() => {
    return (rows || []).find((r) => r.rowKey);
  }, [rows]);

  const setFormKey = (rowKey) => (key) => (value) => {
    if (!prevValuesRef.current) prevValuesRef.current = [];
    let obj = prevValuesRef.current.find((item) => item.key === rowKey);
    if (!obj) {
      if (hienThiDuLieuMoiLenDau) {
        const maxStt = prevValuesRef.current.reduce((max, item) => {
          return item.stt && item.stt > max ? item.stt : max;
        }, 0);
        prevValuesRef.current.unshift({
          key: rowKey,
          [key]: value,
          stt: maxStt + 1,
        });
      } else {
        prevValuesRef.current.push({ key: rowKey, [key]: value });
      }
    } else {
      prevValuesRef.current = prevValuesRef.current.map((item) =>
        item.key === rowKey ? { ...obj, [key]: value } : item
      );
    }
    if (!hienThiDuLieuMoiLenDau) {
      prevValuesRef.current.sort((a, b) => a.key - b.key);
    }
    const valuesAll = prevValuesRef.current.map((item) => {
      Object.keys(item).forEach((el) => {
        if (isObject(item[el]) && !Array.isArray(item[el])) {
          delete item[el];
        }
      });
      const convertData = convert(item);
      const value = convertData[fieldName];
      if (hienThiDuLieuMoiLenDau && value) {
        value.stt = item.stt;
      }
      if (value) return value;
      const data = get(convertData, fieldName.replaceAll("_", "."));
      if (hienThiDuLieuMoiLenDau && data) {
        data.stt = item.stt;
      }
      return data || {};
    });

    if (gopVoiDuLieu2) {
      const valuesDuLieu2 = prevValuesRef.current.map((item) => {
        Object.keys(item).forEach((el) => {
          if (isObject(item[el]) && !Array.isArray(item[el])) {
            delete item[el];
          }
        });
        const convertData = convert(item);
        const value = convertData["duLieu2"];

        if (value) {
          if (hienThiDuLieuMoiLenDau) {
            data.stt = item.stt;
          }
          return value;
        }
        const data = get(convertData, "duLieu2".replaceAll("_", "."));
        if (hienThiDuLieuMoiLenDau) {
          data.stt = item.stt;
        }
        return data || {};
      });
      formChange["duLieu2"](valuesDuLieu2);
    }

    formChange[fieldName](valuesAll);
  };

  useEffect(() => {
    let tableValue = form[fieldName] ? form[fieldName] : [];
    if (hienThiDuLieuTuAPI) {
      return;
    }
    if (gopVoiDuLieu2) {
      refDuLieu2.current = get(form, "duLieu2", []);
      if (
        !refDuLieu2.current ||
        refDuLieu2.current?.length < tableValue?.length
      ) {
        refDuLieu2.current = tableValue.map((el, index) => {
          return get(refDuLieu2.current, `[${index}]`, {});
        });
      }
    }

    //check nếu table được cấu hình fieldName
    if (minRow && parseInt(minRow) > tableValue.length && !form[fieldName]) {
      tableValue = new Array(parseInt(minRow)).fill({}).map((item, index) => ({
        ...(tableValue[index] || {}),
      }));
    }
    if (tableValue.length < 1 && state.tableRows.length < 1) {
      //nếu không được cấu hình fieldName hoặc form data đang empty thì tiến hành thêm mới dòng
      if (Object.keys(formChange).length > 2) {
        onAddRow(true);
      }
    } else {
      if (tableValue.length > 0) {
        prevValuesRef.current = tableValue.map((item, index) => {
          const itemDuLieu2 = get(refDuLieu2.current, `[${index}]`);
          return {
            ...combineFields(item, {}, fieldName),
            ...combineFields(itemDuLieu2, {}, "duLieu2"),
            key: index,
            takeMe: true,
            lichSuKy: form.lichSuKy,
            soPhieu: form.soPhieu,
            index: viTriKyBatDau ? parseInt(viTriKyBatDau) + index : index,
          };
        });
        if (hienThiDuLieuMoiLenDau && prevValuesRef.current?.length) {
          if (
            prevValuesRef.current.every((el) => !get(el, `${fieldName}_stt`))
          ) {
            prevValuesRef.current.forEach((el, idx) => {
              el.stt = viTriKyBatDau ? parseInt(viTriKyBatDau) + idx : idx + 1;
            });
          } else {
            prevValuesRef.current.forEach((el, idx) => {
              el.stt = get(el, `${fieldName}_stt`);
            });
          }
        }
        if (!isPhieuChamSoc) {
          const tableRows = tableValue.map((item, index) => ({
            mainKey: rowCopy?.key,
            key: index,
            lichSuKy: form.lichSuKy,
            soPhieu: form.soPhieu,
            index: viTriKyBatDau ? parseInt(viTriKyBatDau) + index : index,
            stt: item.stt,
          }));
          setState({
            tableRows,
          });
        } else {
          const indexHeader =
            rows.findIndex((e) => e.isHeader) > -1
              ? rows.findIndex((e) => e.isHeader)
              : 0;
          const getIndex = rows.findIndex((e, index) => e.rowKey) - indexHeader;
          const tableRows = tableValue.map((item, index) => {
            if (index + 1 < getIndex) {
              return {
                ...rows[index + 1],
                keyValue: index,
                lichSuKy: form.lichSuKy,
                soPhieu: form.soPhieu,
              };
            } else {
              return {
                mainKey: rowCopy.key,
                key: index,
                lichSuKy: form.lichSuKy,
                soPhieu: form.soPhieu,
              };
            }
          });
          setState({
            tableRows: tableRows,
          });
        }
        onRowsLengthChange && onRowsLengthChange(tableValue);
      }
    }
  }, [form, formChange, minRow]);

  const getData = async () => {
    if (mode == MODE.config) return;
    let api = dataApi + "";
    const listVariable = variable || []; //lấy danh sách các tham số đã khai báo
    listVariable.forEach((variable) => {
      //duyệt qua danh sách các tham số
      if (variable.value) {
        let value = "";

        if (!variable.fromUrl) {
          value = queries[variable.value];
        } else {
          value = form[variable.value]; //lấy giá trị tương ứng với tham số
          if (value === undefined) {
            //nếu không có dữ liệu thì lấy từ valueEMR url
            value = (queries || {})[variable.value] || "";
          }
        }
        api = api.replaceAll(`{${variable?.name || ""}}`, value); //replace tham số với giá trị nhận được
        //tham số truyền vào api sẽ có rule như sau: {tenThamSo}
      }
    });

    const s = await client.get(api);
    const tableValue = s?.data?.data || [];
    prevValuesRef.current = tableValue.map((item, index) => {
      return {
        ...combineFields(item, {}, fieldName),
        key: index,
      };
    });
    if (
      hienThiDuLieuMoiLenDau &&
      prevValuesRef.current.every((el) => !get(el, `${fieldName}_stt`))
    ) {
      prevValuesRef.current.forEach((el, idx) => {
        el.stt = viTriKyBatDau ? parseInt(viTriKyBatDau) + idx : idx;
      });
    }
    const tableRows = tableValue.map((item, index) => ({
      mainKey: rowCopy?.key,
      key: index,
    }));

    setState({
      tableRows,
    });
  };

  //Xử lý nếu get dữ liệu bảng từ API
  useEffect(() => {
    if (hienThiDuLieuTuAPI && dataApi) {
      if (refTimeoutCallData.current) clearTimeout(refTimeoutCallData.current);
      refTimeoutCallData.current = setTimeout(() => {
        getData();
      }, 500);
    }
  }, [hienThiDuLieuTuAPI, dataApi, form, hienThiDuLieuMoiLenDau]);

  const onAddRow = (newForm) => {
    if (rowCopy) {
      const obj = {
        mainKey: rowCopy.key,
        key: moment().valueOf(),
        lichSuKy: form.lichSuKy,
        soPhieu: form.soPhieu,
        takeMe: true,
      };

      if (hienThiDuLieuMoiLenDau) {
        const maxStt = prevValuesRef.current.reduce((max, item) => {
          return item.stt && item.stt > max ? item.stt : max;
        }, 0);
        obj.stt = maxStt + 1;
      }

      let obj2 = [];
      if (newForm && isPhieuChamSoc) {
        obj2 = rows.filter((e, index) => !e.isHeader && !e.rowKey);
      }
      let tableRows = [...state.tableRows, ...obj2, obj];

      if (hienThiDuLieuMoiLenDau) {
        tableRows = [obj, ...state.tableRows, ...obj2];
      }
      setState({
        tableRows,
      });
      if (newForm && formChange && fieldName && formChange[fieldName]) {
        tableRows.forEach((row, idx) => {
          row.index = viTriKyBatDau ? parseInt(viTriKyBatDau) + idx : idx;
        });
        prevValuesRef.current = tableRows;
        formChange[fieldName](tableRows);
      }
      if (copyDuLieuHangTrenKhiThemMoi) {
        prevValuesRef.current = [
          ...prevValuesRef.current,
          {
            ...prevValuesRef.current[prevValuesRef.current?.length - 1],
            ...obj,
          },
        ];
      }
      onRowsLengthChange && onRowsLengthChange(tableRows);
    }
  };

  const formChangeKeys = useMemo(() => {
    return Object.keys(formChange).filter((key) => {
      return (
        key.split("_")[0] === fieldName || key.indexOf(fieldName + "_") == 0
      );
    });
  }, [formChange]);

  const onChange = (row) => {
    const rowKey = row.keyValue || row.keyValue === 0 ? row.keyValue : row.key;
    let obj = {};
    formChangeKeys.forEach((key) => {
      obj[key] = setFormKey(rowKey)(key);
    });
    obj = new Proxy(obj, {
      get(target, prop, receiver) {
        // Nếu field đã tồn tại (dù là undefined) => trả lại giá trị gốc
        if (prop in target) return Reflect.get(target, prop, receiver);

        // Nếu không tồn tại => trả về function mặc định
        return setFormKey(rowKey)(prop);
      },
    });
    obj.getAllData = () => {
      return form;
    };
    obj.setMultiData = (data = {}) => {
      let newState = {};
      const prevValues = prevValuesRef.current;
      const value = prevValues.find((item) => item.key === rowKey);
      let newForm = prevValues;
      if (!value) {
        const newValue = { key: rowKey, ...data };
        newForm.push(newValue);
      } else {
        const newValue = { ...value, ...data };
        newForm = prevValues.map((item) =>
          item.key === rowKey ? newValue : item
        );
      }
      prevValuesRef.current = newForm;
      setState(newState);
      formChange[fieldName](
        newForm.map((item) => {
          const convertData = convert(item);
          const value = convertData[fieldName];
          if (value) return value;
          return get(convertData, fieldName.replaceAll("_", ""));
        })
      );
    };

    return obj;
  };

  const debounceFunc = useCallback(
    debounce(() => {
      setListSignKey(cloneDeep(listSignKey));
    }, 1000),
    []
  );

  const setRowSign = (key, value) => {
    listSignKey[key] = value;
    debounceFunc();
  };

  return state.tableRows.map((row, index) => {
    let rowValue = prevValuesRef.current.find((item) => {
      return (
        item?.key ===
        (row?.keyValue || row?.keyValue === 0 ? row?.keyValue : row?.key)
      );
    });

    if (hienThiDuLieuMoiLenDau && rowValue) {
      rowValue = cloneDeep(rowValue);
      rowValue.index = row.stt;
    }

    const onChangeRow = onChange(row);
    const handleDelete =
      ({ rowKey }) =>
      () => {
        showConfirm(
          {
            title: t("common.thongBao"),
            content: `${t("common.banCoChacMuonXoa")}`,
            cancelText: t("common.huy"),
            okText: t("common.dongY"),
            classNameOkText: "button-warning",
            showBtnOk: true,
            typeModal: "warning",
          },
          () => {
            const tableRows = state.tableRows.filter(
              (item) => item.key !== rowKey
            );
            prevValuesRef.current = prevValuesRef.current.filter(
              (item) => item.key !== rowKey
            );
            formChange[fieldName](
              prevValuesRef.current.map((item) => {
                const convertData = convert(item);
                const value = convertData[fieldName];
                if (value) return value;
                return get(convertData, fieldName.replaceAll("_", "."));
              })
            );
            setState({
              tableRows,
            });
          }
        );
      };
    const disable = listSignKey[row.key]?.block;

    return (
      <Row
        rowValue={rowValue}
        key={row.key}
        rowIndex={index}
        mode={mode}
        formId={formId}
        //value emr global sử dụng trong replication row khi replication row truyền vào là form chính là valuerow
        //values = valueEMR khi không nằm trong replication row
        updateComponents={updateComponents}
        formChange={onChangeRow}
        valueEMR={valueEMR}
        valuesHIS={valuesHIS} //[dataFromHis]
        fileConfig={fileConfig}
        cols={cols}
        component={component}
        row={row}
        keysHadConfig={keysHadConfig}
        localComponents={localComponents}
        colSelected={colSelected}
        level={props.level}
        disable={props.disable || disable}
        fontSize={props.fontSize}
        lineHeightText={props.lineHeightText}
        rowHeight={props.rowHeight} //dùng trong tính năng set rowHeight của component page và layout
        handleDelete={handleDelete}
        setRowSign={setRowSign}
        isDeleteRow={isDeleteRow}
        rowKey={row.key}
        sttBatDau={props.sttBatDau || 1}
      />
    );
  });
});

export default memo(Khung);
