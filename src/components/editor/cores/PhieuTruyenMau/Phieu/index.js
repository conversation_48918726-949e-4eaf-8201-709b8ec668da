import React, { useEffect, useMemo, useState } from "react";
import { Main } from "./styled";
import { Button, Col, Row } from "antd";
import { ENUM, GIOI_TINH_BY_VALUE, THIET_LAP_CHUNG } from "constants/index";
import { PlusOutlined } from "@ant-design/icons";
import DropDownList from "../../DropDownList";
import { AM_DUONG_TINH } from "../constants";
import { useEnum, useLazyKVMap, useThietLap } from "hooks";
import DatePicker from "../../DatePicker";
import { DeboundInput } from "components/editor/config";
import ImageSign from "../../ImageSign";
import { convert, MODE } from "utils/editor-utils";
import TextEdit from "../../TextEdit";
import { get } from "lodash";
import { isArray, isNumber } from "utils";
import Barcode from "../../Barcode";
import moment from "moment";
import { useTranslation } from "react-i18next";
import { HeaderF<PERSON>, HeaderForm2, HeaderForm3 } from "./HeaderForm";

const DATA_DEFAULT = {
  thoiGian: null,
  tocDo: null,
  niemMac: null,
  nhipTho: null,
  mach: null,
  nhietDo: null,
  khac: null,
  huyetAp: null,
};
const Phieu = (props) => {
  const {
    data,
    mode,
    onFormChange,
    index,
    notBreakPage,
    itemProps,
    component,
    form,
    refLabel,
  } = props;
  const [listNhomMau] = useEnum(ENUM.NHOM_MAU);
  const [dataHIEN_THI_KET_THUC_TRUYEN_LUC_PHIEU_TRUYEN_MAU] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_KET_THUC_TRUYEN_LUC_PHIEU_TRUYEN_MAU
  );
  const { t } = useTranslation();
  const [dataTheoDoi, setDataTheoDoi] = useState([]);
  useEffect(() => {
    setDataTheoDoi(
      data.theoDoi ||
        new Array(itemProps.soDongTruyenMau || 3).fill({}).map((item) => ({
          ...DATA_DEFAULT,
        }))
    );
  }, [data.theoDoi]);

  const formChange = (key) => (value) => {
    onFormChange({
      key,
      value,
      index,
    });
  };

  const onChangeDataTable =
    ({ key, idx }) =>
    (value) => {
      dataTheoDoi[idx][key] = value;
      onFormChange({
        key: "theoDoi",
        value: dataTheoDoi,
        index,
      });
    };

  const ketQuaXetNghiemHoaHopMienDichConfigs = useMemo(() => {
    return {
      ong1Muoi:
        props?.itemProps?.ketQuaXetNghiemHoaHopMienDichOng1Muoi ??
        AM_DUONG_TINH,
      ong1Globulin:
        props?.itemProps?.ketQuaXetNghiemHoaHopMienDichOng1Globulin ??
        AM_DUONG_TINH,
      ong2Muoi:
        props?.itemProps?.ketQuaXetNghiemHoaHopMienDichOng2Muoi ??
        AM_DUONG_TINH,
      ong2Globulin:
        props?.itemProps?.ketQuaXetNghiemHoaHopMienDichOng2Globulin ??
        AM_DUONG_TINH,
    };
  }, [
    props?.itemProps?.ketQuaXetNghiemHoaHopMienDichOng1Muoi,
    props?.itemProps?.ketQuaXetNghiemHoaHopMienDichOng1Globulin,
    props?.itemProps?.ketQuaXetNghiemHoaHopMienDichOng2Muoi,
    props?.itemProps?.ketQuaXetNghiemHoaHopMienDichOng2Globulin,
  ]);

  const renderBody = () => {
    return dataTheoDoi.map((item, idx) => {
      return (
        <tr>
          <td>
            <DatePicker
              component={{
                props: {
                  contentAlign: "left",
                  dateTimeFormat: "HH:mm D/M/Y",
                  fieldName: "value",
                },
              }}
              form={{
                value: item.thoiGian,
              }}
              mode={mode}
              formChange={{
                value: (value) => {
                  onChangeDataTable({ key: "thoiGian", idx })(value);
                },
              }}
            ></DatePicker>
          </td>
          <td>
            <DeboundInput
              rows={1}
              readOnly={false}
              value={item?.tocDo || ""}
              onChange={(e) => onChangeDataTable({ key: "tocDo", idx })(e)}
              type="multipleline"
              lineHeightText={1.5}
              fontSize={12}
              minHeight={12 + 6}
              width="250"
              markSpanRow={false}
              contentAlign="center"
            ></DeboundInput>
          </td>
          <td>
            <DeboundInput
              rows={1}
              readOnly={false}
              value={item?.niemMac || ""}
              onChange={(e) => onChangeDataTable({ key: "niemMac", idx })(e)}
              type="multipleline"
              lineHeightText={1.5}
              fontSize={12}
              minHeight={12 + 6}
              markSpanRow={false}
              contentAlign="center"
            ></DeboundInput>
          </td>
          <td>
            <DeboundInput
              rows={1}
              readOnly={false}
              value={item?.nhipTho || ""}
              onChange={(e) => onChangeDataTable({ key: "nhipTho", idx })(e)}
              type="multipleline"
              lineHeightText={1.5}
              fontSize={12}
              minHeight={12 + 6}
              markSpanRow={false}
              contentAlign="center"
            ></DeboundInput>
          </td>
          <td>
            <DeboundInput
              rows={1}
              readOnly={false}
              value={item?.mach || ""}
              onChange={(e) => onChangeDataTable({ key: "mach", idx })(e)}
              type="multipleline"
              lineHeightText={1.5}
              fontSize={12}
              minHeight={12 + 6}
              markSpanRow={false}
              contentAlign="center"
            ></DeboundInput>
          </td>
          <td>
            <DeboundInput
              rows={1}
              readOnly={false}
              value={item?.nhietDo || ""}
              onChange={(e) => onChangeDataTable({ key: "nhietDo", idx })(e)}
              type="multipleline"
              lineHeightText={1.5}
              fontSize={12}
              minHeight={12 + 6}
              markSpanRow={false}
              contentAlign="center"
            ></DeboundInput>
          </td>
          <td>
            <DeboundInput
              rows={1}
              readOnly={false}
              value={item?.huyetAp || ""}
              onChange={(e) => onChangeDataTable({ key: "huyetAp", idx })(e)}
              type="multipleline"
              lineHeightText={1.5}
              fontSize={12}
              minHeight={12 + 6}
              markSpanRow={false}
              contentAlign="center"
            ></DeboundInput>
          </td>
          <td>
            <DeboundInput
              rows={1}
              readOnly={false}
              value={item?.khac || ""}
              onChange={(e) => onChangeDataTable({ key: "khac", idx })(e)}
              type="multipleline"
              lineHeightText={1.5}
              fontSize={12}
              minHeight={12 + 6}
              markSpanRow={false}
            ></DeboundInput>
          </td>
        </tr>
      );
    });
  };

  const renderNhomMau = (id, type) => {
    let text = getLabelMau(id, type);
    if (text) {
      if (["+", "-"].includes(text[text.length - 1])) {
        text += itemProps.isBVP
          ? ` / Rh ${text[text.length - 1]}`
          : ` / Rh (D)${text[text.length - 1]}`;
      } else {
        text += itemProps.isBVP ? ` / Rh +` : ` / Rh (D)+`;
      }
    }
    return text;
  };

  const renderAmTinh = (value) => {
    return value ? (
      <b style={{ marginLeft: 5 }}>
        {" "}
        {AM_DUONG_TINH.find((item) => item.value === value)?.ten}
      </b>
    ) : (
      ""
    );
  };

  const renderKetQuaHoaHopMienDichOng1Muoi = (value) => {
    return value ? (
      <b style={{ marginLeft: 5 }}>
        {" "}
        {
          ketQuaXetNghiemHoaHopMienDichConfigs.ong1Muoi.find(
            (item) => String(item.value) === String(value)
          )?.label
        }
      </b>
    ) : (
      ""
    );
  };

  const renderKetQuaHoaHopMienDichOng1Globulin = (value) => {
    return value ? (
      <b style={{ marginLeft: 5 }}>
        {" "}
        {
          ketQuaXetNghiemHoaHopMienDichConfigs.ong1Globulin.find(
            (item) => String(item.value) === String(value)
          )?.label
        }
      </b>
    ) : (
      ""
    );
  };

  const renderKetQuaHoaHopMienDichOng2Muoi = (value) => {
    return value ? (
      <b style={{ marginLeft: 5 }}>
        {" "}
        {
          ketQuaXetNghiemHoaHopMienDichConfigs.ong2Muoi.find(
            (item) => String(item.value) === String(value)
          )?.label
        }
      </b>
    ) : (
      ""
    );
  };

  const renderKetQuaHoaHopMienDichOng2Globulin = (value) => {
    return value ? (
      <b style={{ marginLeft: 5 }}>
        {" "}
        {
          ketQuaXetNghiemHoaHopMienDichConfigs.ong2Globulin.find(
            (item) => String(item.value) === String(value)
          )?.label
        }
      </b>
    ) : (
      ""
    );
  };

  const { listNhomMauNguoiBenh, listNhomMauChePham } = useMemo(() => {
    const processArray = (array) =>
      isArray(array, 1)
        ? array.map((item) => ({
            ...item,
            value: isNumber(item.value) ? parseInt(item.value) : item.value,
          }))
        : [];

    return {
      listNhomMauNguoiBenh: processArray(component?.props?.nhomMauNguoiBenh),
      listNhomMauChePham: processArray(component?.props?.nhomMauChePham),
    };
  }, [component?.props?.nhomMauNguoiBenh, component?.props?.nhomMauChePham]);

  const dataNhomMau = useMemo(() => {
    return listNhomMau.map((item) => ({
      value: item.id,
      label: item.ten,
    }));
  }, [listNhomMau]);

  const [getNhomMauEnum] = useLazyKVMap(dataNhomMau, "value");
  const [getNhomMauNguoiBenh] = useLazyKVMap(listNhomMauNguoiBenh, "value");
  const [getNhomMauChePham] = useLazyKVMap(listNhomMauChePham, "value");

  const getLabelMau = (value, type) => {
    const getterMap = {
      nhomMauNguoiBenh: getNhomMauNguoiBenh,
      nhomMauChePham: getNhomMauChePham,
    };

    const getter = getterMap[type];
    return getter(value)?.label ?? getNhomMauEnum(value)?.label ?? "";
  };

  const handleAdd = () => {
    const newValue = {
      thoiGian: null,
      tocDoTruyen: null,
      mauSacNiemMac: null,
      nhipTho: null,
      mach: null,
      thanNhiet: null,
      huyetAp: null,
      nhungDienBienKhac: null,
    };
    setDataTheoDoi([...dataTheoDoi, newValue]);
  };

  const renderChuKy = ({ key }) => {
    let chuKySo;
    if (itemProps[`${key}_loaiKy`] != 2) {
      chuKySo =
        parseInt((itemProps[`${key}_fieldName`] || "").replace("chuKy", "")) ||
        0;
    }

    return (
      <div style={{ marginBottom: 50 }}>
        <ImageSign
          mode={mode}
          component={{
            props: {
              fieldName: itemProps[`${key}_fieldName`],
              fontSize: itemProps[`${key}_fontSizeCa`] || 12,
              capKy: itemProps[`${key}_capKy`],
              alignCa: "left",
              contentColor: itemProps[`${key}_colorCa`] || "red",
              width: itemProps[`${key}_width`] || 100,
              height: itemProps[`${key}_height`] || 100,
              viTriCa: itemProps[`${key}_viTriCa`],
              showCa: itemProps[`${key}_showAnhCa`],
              viTriAnhCa: itemProps[`${key}_viTriAnhCa`],
              widthCa: itemProps[`${key}_widthCa`],
              allowReset: itemProps[`${key}_allowReset`],
              isPatient: itemProps[`${key}_isPatient`],
              loaiKy: itemProps[`${key}_loaiKy`],
              dataSign: {
                kySo: data.kySo,
                chuKySo,
                id: data.soPhieu,
                soPhieu: data.soPhieu || data.id,
                lichSuKyId: data.lichSuKy?.id,
                tenChanKy: data[`dsTenChanKy${chuKySo}`]?.length
                  ? data[`dsTenChanKy${chuKySo}`][0]
                  : "",
                nbDotDieuTriId: data.nbDotDieuTriId,
                lichSuKy: data.lichSuKy,
              },
            },
          }}
          form={data}
          formChange={{
            setMultiData: (value) => {
              const _chuKy = convert(value);
              formChange(itemProps[`${key}_fieldName`])(
                _chuKy[itemProps[`${key}_fieldName`]]
              );
            },
          }}
        />
      </div>
    );
  };

  const headerForm = useMemo(() => {
    switch (itemProps.titleTemplate) {
      case 1:
        return <HeaderForm data={data} mode={mode}></HeaderForm>;
      case 2:
        return <HeaderForm2 data={data} mode={mode}></HeaderForm2>;
      case 3:
        return <HeaderForm3 data={data} mode={mode}></HeaderForm3>;
      default:
        <HeaderForm data={data} mode={mode}></HeaderForm>;
        break;
    }
  }, [itemProps.titleTemplate, data, mode]);
  return (
    <Main
      className="phieu-truyen-mau"
      notBreakPage={notBreakPage}
      isPadding={index}
      itemProps={itemProps}
    >
      {headerForm}
      <div className="title">
        <div className="flex-center fs25">PHIẾU TRUYỀN MÁU</div>
        <div className="flex-center text-underline">
          PHẦN I: PHIẾU XÉT NGHIỆM HÒA HỢP MIỄN DỊCH TRUYỀN MÁU{" "}
        </div>
      </div>
      <Row>
        <Col span={13}>
          Họ tên người bệnh: <b>{data.tenNb}</b>
        </Col>
        <Col span={6}>
          {itemProps.isChoRay ? (
            <div>
              Năm sinh:{" "}
              <b>
                {moment(data.ngaySinh).format("YYYY")}{" "}
                {data.tuoi2 ? `(${data?.tuoi2})` : ""}
              </b>
            </div>
          ) : (
            <div>
              Tuổi: <b>{data.tuoi2}</b>
            </div>
          )}
        </Col>
        <Col span={5}>
          <div style={{ textAlign: "right" }}>
            Giới Tính: <b>{GIOI_TINH_BY_VALUE[data.gioiTinh] || ""}</b>{" "}
          </div>
        </Col>
      </Row>

      <Row>
        <Col span={13}>
          Khoa: <b>{data.tenKhoaChiDinh || ""}</b>
        </Col>
        <Col span={11} className="show-line">
          Phòng: <b>{data.tenPhong ? ` ${data.tenPhong}` : ""}</b>
        </Col>
        <Col span={13}>
          Số giường: <b> {data.soHieuGiuong || ""}</b>
        </Col>
        <Col span={11} className="show-line">
          Đối tượng:{" "}
          <b> {data.tenLoaiDoiTuong ? ` ${data.tenLoaiDoiTuong}` : ""}</b>
        </Col>
      </Row>
      <Row>
        <Col span={24}>
          Chẩn đoán:{" "}
          <b>{`${data.maCdChinh || ""}${data.maCdChinh ? " - " : ""}${
            data.tenCdChinh || ""
          }`}</b>
        </Col>
      </Row>
      <Row>
        <Col span={13}>
          Loại chế phẩm: <b>{data.tenDichVu || ""}</b>
        </Col>
        <Col span={11}>
          Số lượng:{" "}
          <b>
            {itemProps.hienThiSoLuongTheoTheTich
              ? data.theTich
                ? `${data.theTich} ml`
                : ""
              : "1"}
          </b>
        </Col>
      </Row>
      {/* . */}
      <Row>
        <Col span={24}>
          Mã túi đựng chế phẩm:{" "}
          <b style={{ fontSize: 18 }}>{data.maTuiMau || ""}</b>
        </Col>
      </Row>
      <Row>
        <Col span={13} className="flex-center">
          <span style={{ width: 150 }}>Ngày điều chế: </span>
          <DatePicker
            component={{
              props: {
                contentAlign: "left",
                dateTimeFormat: "D/m/y",
                fieldName: "value",
              },
            }}
            form={{
              value: data.ngaySanXuat,
            }}
            mode={mode}
            formChange={{
              value: (value) => {
                formChange("ngaySanXuat")(value);
              },
            }}
          />
        </Col>
        <Col span={11} className="flex-center">
          <span style={{ width: 150 }}>Hạn sử dụng: </span>
          <DatePicker
            component={{
              props: {
                contentAlign: "left",
                dateTimeFormat: "D/m/y",
                fieldName: "value",
              },
            }}
            form={{
              value: data.ngayHanSuDung,
            }}
            mode={mode}
            formChange={{
              value: (value) => {
                formChange("ngayHanSuDung")(value);
              },
            }}
          />
        </Col>
      </Row>
      <div>
        <b>I. {itemProps.textXacDinhNhomMau || "XÁC ĐỊNH NHÓM MÁU"}</b>
      </div>
      <Row className="flex">
        <Col span={12} className="border">
          <Row>
            <Col span={10}>Nhóm máu người bệnh: </Col>
            <Col span={14}>
              {renderNhomMau(
                data.nhomMauNb || data.nhomMau,
                "nhomMauNguoiBenh"
              )}
            </Col>
          </Row>
          <Row>
            <Col span={10}>Nhóm máu chế phẩm: </Col>
            <Col span={14}>{renderNhomMau(data.nhomMau, "nhomMauChePham")}</Col>
          </Row>
          <Row>
            <Col span={10}>Ghi chú: </Col>
            <Col span={14}>{data.ghiChu}</Col>
          </Row>
          <Row>
            <Col span={24}>CÁC XÉT NGHIỆM KHÁC:</Col>
          </Row>
          {itemProps.chinhSuaXetNghiemKhac ? (
            <DeboundInput
              rows={1}
              readOnly={false}
              value={data.xetNghiemKhac}
              onChange={formChange("xetNghiemKhac")}
              type="multipleline"
              lineHeightText={1.5}
              fontSize={12}
              minHeight={12 + 6}
              markSpanRow={itemProps.markSpanRow}
            />
          ) : (
            <>
              <Row>
                <Col span={12} className="flex">
                  Sốt rét: {renderAmTinh(data.sotRet)}
                </Col>
                <Col span={12} className="flex">
                  Giang Mai: {renderAmTinh(data.giangMai)}
                </Col>
              </Row>
              <Row>
                <Col span={8} className="flex">
                  HCV: {renderAmTinh(data.hcv)}
                </Col>
                <Col span={8} className="flex">
                  HBV: {renderAmTinh(data.hbv)}
                </Col>
                <Col span={8} className="flex">
                  HIV: {renderAmTinh(data.hiv)}
                </Col>
              </Row>
            </>
          )}
        </Col>
        <Col span={12} className="flex">
          <table className="table1">
            <thead>
              <tr>
                <td className="hide-border-left" colSpan={3}>
                  Kết quả xét nghiệm hòa hợp miễn dịch
                </td>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="hide-border-left"></td>
                <td>Môi trường muối {itemProps.isBVP ? "22°C" : ""}</td>
                <td>37°C/Kháng globulin</td>
              </tr>
              <tr>
                <td className="hide-border-left">Ống 1</td>
                <td>{renderKetQuaHoaHopMienDichOng1Muoi(data.muoi1)}</td>
                <td>
                  {renderKetQuaHoaHopMienDichOng1Globulin(data.globulin1)}
                </td>
              </tr>
              <tr>
                <td className="hide-border-left">Ống 2</td>
                <td>{renderKetQuaHoaHopMienDichOng2Muoi(data.muoi2)}</td>
                <td>
                  {renderKetQuaHoaHopMienDichOng2Globulin(data.globulin2)}
                </td>
              </tr>
            </tbody>
          </table>
        </Col>
      </Row>
      <Row>
        <Col span={14}>
          {mode === MODE.config ? (
            <>
              <TextEdit
                id={`${component.type}_${component.key}`}
                className={"text-field-label"}
                defaultValue={itemProps.label || "label"}
                onChange={(e) => {
                  let value = e;
                  if (value === "<br>") {
                    value = "";
                  }
                  refLabel.current = value;
                }}
                mode={mode}
                width={"500"}
                disabled={false} //allow edit in config mode
              />
            </>
          ) : (
            (itemProps.label || "").replaceAll("label", "")
          )}
        </Col>
        <Col span={10} className="flex-center">
          <DatePicker
            component={{
              props: {
                contentAlign: "center",
                dateTimeFormat: "HH/mm_D...m...y...",
                fieldName: "value",
              },
            }}
            form={{
              value: data.thoiGianDuyet,
            }}
            mode={mode}
            disabled={true}
            formChange={{
              value: (value) => {
                formChange("ngay")(value);
              },
            }}
          />
        </Col>
      </Row>
      <Row gutter={[12, 0]}>
        {[
          { ten: "Người phát máu thứ 1", fieldName: "nguoiPhatMau1" },
          { ten: "Người phát máu thứ 2", fieldName: "nguoiPhatMau2" },
          { ten: "Người làm xét nghiệm", fieldName: "nguoiLamXetNghiem" },
          { ten: "Trưởng khoa xét nghiệm", fieldName: "truongKhoaXetNghiem" },
        ].map((item) => (
          <Col
            span={12}
            key={item.fieldName}
            order={get(itemProps, `${item.fieldName}_viTri`)}
          >
            {get(itemProps, `${item.fieldName}_show`) ? (
              <div>
                <div className="flex-center">
                  <b>{itemProps[`${item.fieldName}_tenHienThi`] || item.ten}</b>
                </div>
                <div className="flex-center">(Ký và ghi rõ họ tên)</div>
                <div className="flex-center">
                  {renderChuKy({
                    key: item.fieldName,
                  })}
                </div>
              </div>
            ) : null}

            {itemProps.showNguoiLamXetNghiem &&
            item.fieldName === "nguoiLamXetNghiem" ? (
              <DeboundInput
                rows={1}
                readOnly={false}
                value={data?.tenNguoiPhat1 || ""}
                onChange={formChange("tenNguoiPhat1")}
                type="multipleline"
                lineHeightText={1.5}
                fontSize={12}
                minHeight={12 + 6}
                width="250"
                contentAlign="center"
                markSpanRow={itemProps.markSpanRow}
              />
            ) : null}
          </Col>
        ))}
      </Row>

      {/* {itemProps.showNguoiLamXetNghiem && (
        <Row style={{ marginTop: 50 }}>
          <Col span={14}></Col>
          <Col
            span={10}
            style={{ width: "100%" }}
            className="debound-center"
          ></Col>
        </Row>
      )} */}

      <div
        className="flex-center bold text-underline"
        style={{ marginTop: 10 }}
      >
        PHẦN II. THEO DÕI TRUYỀN MÁU LÂM SÀNG
      </div>
      <Row>
        <Col span={12} className="flex show-line">
          <span>Lần truyền máu thứ:&nbsp;</span>
          <div style={{ flex: 1 }}>
            <DeboundInput
              readOnly={false}
              value={data?.lanTruyenMau || ""}
              onChange={formChange("lanTruyenMau")}
              type="multipleline"
              lineHeightText={1.5}
              fontSize={12}
              minHeight={12 + 6}
              width="250"
              markSpanRow={itemProps.markSpanRow}
            />
          </div>
        </Col>
        <Col span={12} className="flex">
          <span>Kết quả hòa hợp tại giường:&nbsp;</span>
          <DropDownList
            component={{
              props: {
                checkList: AM_DUONG_TINH,
                fieldName: "ketQuaHoaHopTaiGiuong",
                listType: 2,
                noLabel: true,
                minHeight: "10",
                fontSize: "12",
                lineHeight: "1.5",
                markSpanRow: itemProps.markSpanRow,
                ...(itemProps.ketQuaHoaHopTaiGiuongTuyChon?.length > 0
                  ? {
                      checkList: itemProps.ketQuaHoaHopTaiGiuongTuyChon,
                      type: "onlyOne",
                    }
                  : {}),
              },
            }}
            blockWidth="150px"
            className={"drop-list"}
            form={{
              ketQuaHoaHopTaiGiuong: data.ketQuaHoaHopTaiGiuong,
              ...(itemProps.ketQuaHoaHopTaiGiuongTuyChon?.length > 0
                ? {
                    ketQuaHoaHopTaiGiuong:
                      data.ketQuaHoaHopTaiGiuong?.length > 0
                        ? data.ketQuaHoaHopTaiGiuong[0]
                        : null,
                  }
                : {}),
            }}
            formChange={{
              ketQuaHoaHopTaiGiuong: (value) => {
                if (itemProps.ketQuaHoaHopTaiGiuongTuyChon?.length > 0) {
                  formChange("ketQuaHoaHopTaiGiuong")(value ? [value] : null);
                } else {
                  formChange("ketQuaHoaHopTaiGiuong")(value);
                }
              },
            }}
          />
        </Col>
      </Row>
      <Row>
        <Col span={12} className="flex">
          <span>{t("editor.dinhNhomMauNguoiCho")}&nbsp;</span>
          <DropDownList
            component={{
              props: {
                type: "onlyOne",
                checkList: dataNhomMau,
                fieldName: "value",
                listType: 2,
                noLabel: true,
                minHeight: "10",
                fontSize: "12",
                lineHeight: "1.5",
                markSpanRow: itemProps.markSpanRow,
                ...(itemProps.dinhNhomMauNguoiChoTuyChon?.length > 0
                  ? {
                      checkList: itemProps.dinhNhomMauNguoiChoTuyChon,
                    }
                  : {}),
              },
            }}
            blockWidth="150px"
            className={"drop-list"}
            form={{
              value: data.nhomMauCho,
            }}
            formChange={{
              value: (value) => {
                formChange("nhomMauCho")(value);
              },
            }}
          />
        </Col>
        <Col span={12} className="flex">
          <span>{t("editor.dinhNhomMauNguoiBenh")}&nbsp;</span>
          <DropDownList
            component={{
              props: {
                type: "onlyOne",
                checkList: dataNhomMau,
                fieldName: "value",
                listType: 2,
                noLabel: true,
                minHeight: "10",
                fontSize: "12",
                lineHeight: "1.5",
                markSpanRow: itemProps.markSpanRow,
              },
            }}
            mode={mode}
            blockWidth="150px"
            form={{
              value: data.nhomMauNhan,
            }}
            formChange={{
              value: (value) => {
                formChange("nhomMauNhan")(value);
              },
            }}
          />
        </Col>
      </Row>
      <div className="flex">
        <span style={{ width: 200, marginBottom: 5 }}>
          Bắt đầu truyền lúc:{" "}
        </span>
        <DatePicker
          component={{
            props: {
              contentAlign: "left",
              dateTimeFormat: "HH/mm_D...m...y...",
              fieldName: "value",
            },
          }}
          form={{
            value: data.thoiGianBatDau,
          }}
          mode={mode}
          formChange={{
            value: (value) => {
              formChange("thoiGianBatDau")(value);
            },
          }}
        />
      </div>
      <div className="table2">
        <table>
          <thead>
            <tr>
              <td style={{ width: 137, maxWidth: 137 }}>Thời gian</td>
              <td style={{ width: 90, maxWidth: 90 }}>
                Tốc độ truyền (giọt/phút)
              </td>
              <td style={{ width: 96, maxWidth: 96 }}>
                {itemProps.isBVP ? <div>Màu sắc</div> : ""}

                <div>Da, niêm mạc</div>
              </td>
              <td style={{ width: 96, maxWidth: 96 }}>Nhịp thở (lần/phút)</td>
              <td style={{ width: 80, maxWidth: 80 }}>Mạch (lần/phút)</td>

              {itemProps.isBVP ? (
                <>
                  <td style={{ width: 80, maxWidth: 80 }}>Huyết áp</td>
                  <td style={{ width: 80, maxWidth: 80 }}>Thân nhiệt (°C)</td>
                </>
              ) : (
                <>
                  <td style={{ width: 80, maxWidth: 80 }}>Thân nhiệt (°C)</td>
                  <td style={{ width: 80, maxWidth: 80 }}>Huyết áp</td>
                </>
              )}

              <td>Diễn biến khác khi truyền</td>
            </tr>
          </thead>
          <tbody>{renderBody()}</tbody>
        </table>
        <div>
          <Button
            className="btn-add"
            onClick={handleAdd}
            icon={<PlusOutlined />}
          ></Button>
        </div>
      </div>
      <Row>
        <Col span={14} className="flex" style={{ marginTop: 5 }}>
          <span style={{ width: 200 }}>Ngừng truyền lúc:</span>
          <DatePicker
            component={{
              props: {
                contentAlign: "left",
                dateTimeFormat: "HH/mm_D...m...y...",
                fieldName: "value",
              },
            }}
            form={{
              value: data.thoiGianKetThuc,
            }}
            mode={mode}
            formChange={{
              value: (value) => {
                formChange("thoiGianKetThuc")(value);
              },
            }}
          />
        </Col>
        {dataHIEN_THI_KET_THUC_TRUYEN_LUC_PHIEU_TRUYEN_MAU?.eval() && (
          <Col span={14} className="flex" style={{ marginTop: 5 }}>
            <span style={{ width: 200 }}>Kết thúc truyền lúc:</span>
            <DatePicker
              component={{
                props: {
                  contentAlign: "left",
                  dateTimeFormat: "HH/mm_D...m...y...",
                  fieldName: "value",
                },
              }}
              form={{
                value: data.thoiGianKetThucTruyen,
              }}
              mode={mode}
              formChange={{
                value: (value) => {
                  formChange("thoiGianKetThucTruyen")(value);
                },
              }}
            />
          </Col>
        )}
        <Col span={10} className="flex " style={{ alignItems: "end" }}>
          <span style={{ width: 250 }}>Số lượng máu thực tế đã truyền:</span>
          <div style={{ width: "calc(100% - 250px)" }}>
            <DeboundInput
              readOnly={false}
              value={data?.soLuong || ""}
              onChange={formChange("soLuong")}
              type="multipleline"
              lineHeightText={1}
              fontSize={12}
              minHeight={12 + 6}
              markSpanRow={itemProps.markSpanRow}
            ></DeboundInput>
          </div>
          <span>ml</span>
        </Col>
      </Row>
      <div className="flex">
        <span style={{ width: 220 }}>Nhận xét quá trình truyền máu: </span>
        <div style={{ width: "calc(100% - 220px)" }}>
          <DeboundInput
            readOnly={false}
            value={data?.nhanXet || ""}
            onChange={formChange("nhanXet")}
            type="multipleline"
            lineHeightText={1.5}
            fontSize={12}
            minHeight={12 + 6}
            markSpanRow={itemProps.markSpanRow}
          ></DeboundInput>
        </div>
      </div>
      <Row style={{ marginBottom: 20 }}>
        <Col span={12}>
          {itemProps.bacSiDieuTri_show ||
          itemProps.bacSiDieuTri_show === undefined ? (
            <>
              <div className="bold flex-center">
                {itemProps[`_bacSiDieuTri_tenHienThi`] || "BÁC SĨ ĐIỀU TRỊ"}
              </div>
              <div className=" flex-center"> (Ký và ghi rõ họ tên)</div>
              <div className=" flex-center">
                {renderChuKy({
                  key: "bacSiDieuTri",
                })}
              </div>
            </>
          ) : null}
        </Col>
        <Col span={12}>
          {itemProps.dieuDuongTruyenMau_show ||
          itemProps.dieuDuongTruyenMau_show === undefined ? (
            <>
              <div className="bold flex-center">
                {" "}
                {itemProps[`_dieuDuongTruyenMau_tenHienThi`] ||
                  "ĐIỀU DƯỠNG TRUYỀN MÁU"}{" "}
              </div>
              <div className=" flex-center"> (Ký và ghi rõ họ tên)</div>
              <div className=" flex-center">
                <div>
                  {renderChuKy({
                    key: "dieuDuongTruyenMau",
                  })}
                </div>
              </div>
            </>
          ) : null}
        </Col>
      </Row>
    </Main>
  );
};

Phieu.propTypes = {};

export default Phieu;
