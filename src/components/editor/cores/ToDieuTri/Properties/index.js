import React, {
  useState,
  useEffect,
  useImperativeHandle,
  forwardRef,
  useRef,
} from "react";
import { useTranslation } from "react-i18next";
import T from "prop-types";
import { Row, Col, Input, Checkbox, InputNumber, Select, Collapse } from "antd";
import { Button, Popover, TableWrapper, Dropdown } from "components";
import { DragDropContext, Draggable, Droppable } from "react-beautiful-dnd";
import {
  DS_TIEU_DE_PHAI,
  LIST_CHI_SO_SONG,
  LIST_SAP_XEP_DIEN_BIEN_BENH,
  LIST_SAP_XEP_THONG_TIN_CHUNG,
  LIST_SAP_XEP_Y_LENH,
  LIST_TUY_CHINH_TRUONG_SAP_XEP_Y_LENH,
} from "../DanhSachToDieuTri/ultils";
import { useListAll, useStore } from "hooks";
import { SVG } from "assets";
import ModalThongSo from "./ModalThongSo";
import { DS_LOAI_KY, VI_TRI_CA } from "../../ImageSign/constanst";
import { EditorTool, FontSizeConfig } from "components/editor/config";
import { Main, GlobalStyle } from "./styled";
import { FontColorsOutlined } from "@ant-design/icons";

const { PickColor, FieldName } = EditorTool;
const { Column } = TableWrapper;
const { Panel } = Collapse;
const BangTheoDoiBenhNhanHSTCProperties = forwardRef((props, ref) => {
  const [state, _setState] = useState({
    hienDiaChi: true,
    maSoBm: "MS: 39/BV-01",
    listCss: LIST_CHI_SO_SONG,
    listSapXepYLenh: LIST_SAP_XEP_Y_LENH,
    listSapXepDienBienBenh: LIST_SAP_XEP_DIEN_BIEN_BENH,
    defaultThongSo: [],
    positionHeaderRight: DS_TIEU_DE_PHAI,
    listSapXepThongTinChung: LIST_SAP_XEP_THONG_TIN_CHUNG,
    tuyChinhTruongSapXepYLenh: LIST_TUY_CHINH_TRUONG_SAP_XEP_Y_LENH,
  });

  const { t } = useTranslation();

  const refThongSo = useRef(null);
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const [listAllChiSoSong] = useListAll("chiSoSong", {}, true);
  const formInfo = useStore("config.formInfo");
  useEffect(() => {
    if (props.state.key) {
      let allChiSoSong = props.state.props.listCss || LIST_CHI_SO_SONG;
      listAllChiSoSong
        .map((item) => ({
          ...item,
          fieldName: item.ma,
          show: true,
        }))
        .forEach((css) => {
          if (!allChiSoSong.some((el) => el.fieldName === css.ma)) {
            allChiSoSong.push(css);
          }
        });
      if (
        LIST_SAP_XEP_Y_LENH.length > props.state.props.listSapXepYLenh?.length
      ) {
        const newItem = LIST_SAP_XEP_Y_LENH.find(
          (item) =>
            !props.state.props.listSapXepYLenh.some(
              (el) => el.fieldName === item.fieldName
            )
        );
        if (newItem) {
          props.state.props.listSapXepYLenh.push(newItem);
        }
      }
      if (
        LIST_SAP_XEP_DIEN_BIEN_BENH.length >
        props.state.props.listSapXepDienBienBenh?.length
      ) {
        const newItem = LIST_SAP_XEP_DIEN_BIEN_BENH.find(
          (item) =>
            !props.state.props.listSapXepDienBienBenh.some(
              (el) => el.fieldName === item.fieldName
            )
        );
        if (newItem) {
          props.state.props.listSapXepDienBienBenh.push(newItem);
        }
      }
      if (
        DS_TIEU_DE_PHAI?.length > props.state.props.positionHeaderRight?.length
      ) {
        const newItem = DS_TIEU_DE_PHAI.find(
          (item) =>
            !props.state.props.positionHeaderRight.some(
              (el) => el.fieldName === item.fieldName
            )
        );
        if (newItem) {
          props.state.props.positionHeaderRight.push(newItem);
        }
      }
      let newState = {
        viTriDichVu: props.state.props.viTriDichVu || 0,
        showHLDD: props.state.props.showHLDD || false,
        maSoBm: props.state.props.maSoBm || "MS: 39/BV-01",
        hienDiaChi: props.state.props.hienDiaChi,
        textCot1: props.state.props.textCot1,
        textCot2: props.state.props.textCot2,
        hideDvTT: props.state.props.hideDvTT || false,
        showDuongDungThuoc:
          props.state.props.showDuongDungThuoc == -undefined
            ? true
            : props.state.props.showDuongDungThuoc,
        listCss: allChiSoSong,
        hideLogo: props.state.props.hideLogo || false,
        defaultThongSo: props.state.props.defaultThongSo || [],
        hienSoLuongDvt:
          props.state.props.hienSoLuongDvt != null
            ? props.state.props.hienSoLuongDvt
            : true,
        hienThuocNhaThuoc:
          props.state.props.hienThuocNhaThuoc != null
            ? props.state.props.hienThuocNhaThuoc
            : true,
        hienThuocKeNgoai:
          props.state.props.hienThuocKeNgoai != null
            ? props.state.props.hienThuocKeNgoai
            : true,
        hienThuocTuVan:
          props.state.props.hienThuocTuVan != null
            ? props.state.props.hienThuocTuVan
            : false,
        hienBSDieuTri:
          props.state.props.hienBSDieuTri != null
            ? props.state.props.hienBSDieuTri
            : true,
        hienLoaiChiDinh:
          props.state.props.hienLoaiChiDinh != null
            ? props.state.props.hienLoaiChiDinh
            : false,
        hienThiSLBuoi:
          props.state.props.hienThiSLBuoi != null
            ? props.state.props.hienThiSLBuoi
            : false,
        hienThiTocDoTruyen:
          props.state.props.hienThiTocDoTruyen != null
            ? props.state.props.hienThiTocDoTruyen
            : false,
        hienThiDonViTocDoTruyen:
          props.state.props.hienThiDonViTocDoTruyen != null
            ? props.state.props.hienThiDonViTocDoTruyen
            : false,
        gopDv:
          props.state.props.gopDv != null ? props.state.props.gopDv : false,
        themXoaToDieuTri:
          props.state.props.themXoaToDieuTri != null
            ? props.state.props.themXoaToDieuTri
            : false,
        hienThiNam:
          props.state.props.hienThiNam != null
            ? props.state.props.hienThiNam
            : false,
        hienThiGio:
          props.state.props.hienThiGio != null
            ? props.state.props.hienThiGio
            : true,
        gopToDieuTriNhieuNgay:
          props.state.props.hienThiNam != null
            ? props.state.props.gopToDieuTriNhieuNgay
            : false,
        hienThiDichVuKham:
          props.state.props.hienThiNam != null
            ? props.state.props.hienThiDichVuKham
            : false,
        listSapXepYLenh:
          props.state.props.listSapXepYLenh || LIST_SAP_XEP_Y_LENH,
        listSapXepDienBienBenh:
          props.state.props.listSapXepDienBienBenh ||
          LIST_SAP_XEP_DIEN_BIEN_BENH,
        viTriCa: props.state.props.viTriCa,
        loaiKy: props.state.props.loaiKy,
        showCa: props.state.props.showCa,
        colorCa: props.state.props.colorCa,
        fontSizeCa: props.state.props.fontSizeCa,
        viTriAnhCa: props.state.props.viTriAnhCa,
        showAnhCa: props.state.props.showAnhCa,
        widthCa: props.state.props.widthCaTruongKhoa,
        capKy: props.state.props.capKy,
        capKyTruongKhoa: props.state.props.capKyTruongKhoa,
        viTriCaTruongKhoa: props.state.props.viTriCaTruongKhoa,
        loaiKyTruongKhoa: props.state.props.loaiKyTruongKhoa,
        showCaTruongKhoa: props.state.props.showCaTruongKhoa,
        colorCaTruongKhoa: props.state.props.colorCaTruongKhoa,
        fontSizeCaTruongKhoa: props.state.props.fontSizeCaTruongKhoa,
        viTriAnhCaTruongKhoa: props.state.props.viTriAnhCaTruongKhoa,
        showAnhCaTruongKhoa: props.state.props.showAnhCaTruongKhoa,
        widthCaTruongKhoa: props.state.props.widthCaTruongKhoa,
        showPatientSignTruongKhoa: props.state.props.showPatientSignTruongKhoa,
        hienThiTenTruongKhoa: props.state.props.hienThiTenTruongKhoa,
        markSpanRow: props.state.props.markSpanRow,
        khoaPhieuSauKhiKy: props.state.props.khoaPhieuSauKhiKy || false,
        tieuDeCuaPhieu:
          props.state.props.tieuDeCuaPhieu || "PHIẾU THEO DÕI ĐIỀU TRỊ",
        tieuDeThoiGian: props.state.props.tieuDeThoiGian || "THỜI GIAN",
        tieuDeDienBienBenh:
          props.state.props.tieuDeDienBienBenh || "DIỄN BIẾN BỆNH",
        tieuDeChiDinh: props.state.props.tieuDeChiDinh || "CHỈ ĐỊNH",
        tieuDeDanDoHLDD: props.state.props.tieuDeDanDoHLDD || "DẶN DÒ HL/ĐD",
        showPatientSign: props.state.props.showPatientSign,
        contentColor: props.state.props.contentColor,
        contentColorTruongKhoa: props.state.props.contentColorTruongKhoa,
        positionHeaderRight:
          props.state.props.positionHeaderRight || DS_TIEU_DE_PHAI,
        showLogo: props.state.props.showLogo === false ? false : true,
        showTitleLeft1: props.state.props.showTitleLeft1,
        showTitleLeft2: props.state.props.showTitleLeft2,
        listSapXepThongTinChung:
          props.state.props.listSapXepThongTinChung ||
          LIST_SAP_XEP_THONG_TIN_CHUNG,
        tuyChinhTruongSapXepYLenh:
          props.state.props.tuyChinhTruongSapXepYLenh ||
          LIST_TUY_CHINH_TRUONG_SAP_XEP_Y_LENH,
        isPSHN: props.state.props.isPSHN || false,
        isToDieuTriPS: props.state.props.isToDieuTriPS || false,
        hienThiChanKyTruongKhoa:
          props.state.props.hienThiChanKyTruongKhoa || false,
        kyTuThuocNhaThuoc: props.state.props.kyTuThuocNhaThuoc,
        kyTuThuocKeNgoai: props.state.props.kyTuThuocKeNgoai,
        gopToDieuTriKhiIn: props.state.props.gopToDieuTriKhiIn,
        viTriChanKyBacSi: props.state.props.viTriChanKyBacSi || 2,
        kyTuCon: props.state.props.kyTuCon,
      };

      setState(newState);
    }
  }, [JSON.stringify(props.state), listAllChiSoSong]);

  useImperativeHandle(ref, () => {
    return {
      viTriDichVu: state.viTriDichVu,
      showHLDD: state.showHLDD,
      maSoBm: state.maSoBm,
      hienDiaChi: state.hienDiaChi,
      textCot1: state.textCot1,
      textCot2: state.textCot2,
      hideDvTT: state.hideDvTT,
      showDuongDungThuoc: state.showDuongDungThuoc,
      listCss: state.listCss,
      showDuongDungThuoc: state.showDuongDungThuoc,
      defaultThongSo: state.defaultThongSo,
      hienSoLuongDvt: state.hienSoLuongDvt,
      hienThuocNhaThuoc: state.hienThuocNhaThuoc,
      hienThuocKeNgoai: state.hienThuocKeNgoai,
      hienThuocTuVan: state.hienThuocTuVan,
      hienBSDieuTri: state.hienBSDieuTri,
      hienLoaiChiDinh: state.hienLoaiChiDinh,
      hienThiSLBuoi: state.hienThiSLBuoi,
      hienThiTocDoTruyen: state.hienThiTocDoTruyen,
      hienThiDonViTocDoTruyen: state.hienThiDonViTocDoTruyen,
      gopDv: state.gopDv,
      themXoaToDieuTri: state.themXoaToDieuTri,
      listSapXepYLenh: state.listSapXepYLenh,
      listSapXepDienBienBenh: state.listSapXepDienBienBenh,
      viTriCa: state.viTriCa,
      loaiKy: state.loaiKy,
      showCa: state.showCa,
      colorCa: state.colorCa,
      fontSizeCa: state.fontSizeCa,
      viTriAnhCa: state.viTriAnhCa,
      showAnhCa: state.showAnhCa,
      viTriCaTruongKhoa: state.viTriCaTruongKhoa,
      widthCa: state.widthCaTruongKhoa,
      capKy: state.capKy,
      capKyTruongKhoa: state.capKyTruongKhoa,
      loaiKyTruongKhoa: state.loaiKyTruongKhoa,
      showCaTruongKhoa: state.showCaTruongKhoa,
      colorCaTruongKhoa: state.colorCaTruongKhoa,
      fontSizeCaTruongKhoa: state.fontSizeCaTruongKhoa,
      viTriAnhCaTruongKhoa: state.viTriAnhCaTruongKhoa,
      showAnhCaTruongKhoa: state.showAnhCaTruongKhoa,
      widthCaTruongKhoa: state.widthCaTruongKhoa,
      showPatientSignTruongKhoa: state.showPatientSignTruongKhoa,
      hienThiTenTruongKhoa: state.hienThiTenTruongKhoa,
      markSpanRow: state.markSpanRow,
      khoaPhieuSauKhiKy: state.khoaPhieuSauKhiKy || false,
      tieuDeCuaPhieu: state.tieuDeCuaPhieu || "PHIẾU THEO DÕI ĐIỀU TRỊ",
      tieuDeThoiGian: state.tieuDeThoiGian || "THỜI GIAN",
      tieuDeDienBienBenh: state.tieuDeDienBienBenh || "DIỄN BIẾN BỆNH",
      tieuDeChiDinh: state.tieuDeChiDinh || "CHỈ ĐỊNH",
      tieuDeDanDoHLDD: state.tieuDeDanDoHLDD || "DẶN DÒ HL/ĐD",
      showPatientSign: state.showPatientSign,
      contentColor: state.contentColor,
      contentColorTruongKhoa: state.contentColorTruongKhoa,
      positionHeaderRight: state.positionHeaderRight,
      showLogo: state.showLogo,
      showTitleLeft1: state.showTitleLeft1,
      showTitleLeft2: state.showTitleLeft2,
      listSapXepThongTinChung: state.listSapXepThongTinChung,
      tuyChinhTruongSapXepYLenh: state.tuyChinhTruongSapXepYLenh,
      isPSHN: state.isPSHN,
      hienThiNam: state.hienThiNam,
      hienThiGio: state.hienThiGio,
      isToDieuTriPS: state.isToDieuTriPS,
      hienThiChanKyTruongKhoa: state.hienThiChanKyTruongKhoa || false,
      kyTuThuocNhaThuoc: state.kyTuThuocNhaThuoc,
      kyTuThuocKeNgoai: state.kyTuThuocKeNgoai,
      gopToDieuTriKhiIn: state.gopToDieuTriKhiIn,
      viTriChanKyBacSi: state.viTriChanKyBacSi,
      gopToDieuTriNhieuNgay: state.gopToDieuTriNhieuNgay,
      kyTuCon: state.kyTuCon,
      hienThiDichVuKham: state.hienThiDichVuKham,
    };
  });

  const onChangeValue = (type) => (value) => {
    setState({
      [type]: value,
    });
  };
  const onChangeCheckBox = (type) => (e) => {
    onChangeValue(type)(e.target.checked);
  };
  const onChangeInput = (type) => (e) => {
    onChangeValue(type)(e.target.value);
  };

  const reorder = (list, startIndex, endIndex) => {
    const result = Array.from(list);
    const [removed] = result.splice(startIndex, 1);
    result.splice(endIndex, 0, removed);

    return result;
  };
  const onDragEnd = (key) => (result) => {
    try {
      if (!result.destination) {
        return;
      }
      if (key === "Css") {
        const items = reorder(
          state.listCss,
          result.source.index,
          result.destination.index
        );
        setState({
          listCss: items,
        });
      } else if (key === "yLenh") {
        const items = reorder(
          state.listSapXepYLenh,
          result.source.index,
          result.destination.index
        );
        setState({
          listSapXepYLenh: items,
        });
      } else if (key === "dienBienBenh") {
        const items = reorder(
          state.listSapXepDienBienBenh,
          result.source.index,
          result.destination.index
        );
        setState({
          listSapXepDienBienBenh: items,
        });
      } else if (key === "headerRight") {
        const items = reorder(
          state.positionHeaderRight,
          result.source.index,
          result.destination.index
        );
        setState({
          positionHeaderRight: items,
        });
      } else if (key === "thongTinChung") {
        const items = reorder(
          state.listSapXepThongTinChung,
          result.source.index,
          result.destination.index
        );
        setState({
          listSapXepThongTinChung: items,
        });
      }
    } catch (error) {
      console.log(error);
    }
  };
  const onChangeCss = (key) => (e) => {
    const item = state.listCss.find((item) => item.fieldName === key);
    item.show = e.target.checked;
    setState({
      listCss: state.listCss,
    });
  };

  const onChangeHeaderRight = (key) => (e) => {
    const item = state.positionHeaderRight.find(
      (item) => item.fieldName === key
    );
    item.show = e.target.checked;
    setState({
      positionHeaderRight: state.positionHeaderRight,
    });
  };

  const onChangeSapXepYLenh = (key) => (e) => {
    const item = state.listSapXepYLenh.find((item) => item.fieldName === key);
    item.show = e.target.checked;
    setState({
      listSapXepYLenh: state.listSapXepYLenh,
    });
  };

  const onChangeSapXepDienBienBenh = (key) => (e) => {
    const item = state.listSapXepDienBienBenh.find(
      (item) => item.fieldName === key
    );
    item.show = e.target.checked;
    setState({
      listSapXepDienBienBenh: state.listSapXepDienBienBenh,
    });
  };

  const onChangeSapXepThongTinChung = (keyItem, keyChange) => (e) => {
    const item = state.listSapXepThongTinChung.find(
      (item) => item.fieldName === keyItem
    );
    if (keyChange === "show") {
      item.show = e.target.checked;
      setState({
        listSapXepThongTinChung: state.listSapXepThongTinChung,
      });
    } else {
      item.tenHienThi = e.target.value;
      setState({
        listSapXepThongTinChung: state.listSapXepThongTinChung,
      });
    }
  };

  const onRemove = (data) => (e) => {
    e.preventDefault();
    e.stopPropagation();
    const defaultThongSo = state.defaultThongSo.filter(
      (item) => item.key !== data.key
    );
    setState({
      defaultThongSo,
    });
  };

  const columnsThongSo = [
    Column({
      title: "STT",
      width: 20,
      dataIndex: "index",
      key: "index",
      align: "center",
      render: (item, _, index) => index + 1,
    }),
    Column({
      title: t("editor.tenMau"),
      width: 60,
      dataIndex: "tenMau",
      key: "tenMau",
    }),
    Column({
      title: t("editor.tienIch"),
      width: 20,
      dataIndex: "action",
      key: "action",
      render: (_, data, index) => <SVG.IcDelete onClick={onRemove(data)} />,
    }),
  ];

  const onThemMoiThongSo = () => {
    refThongSo.current &&
      refThongSo.current.show({}, (data) => {
        async function fetchData() {
          await setState({
            defaultThongSo: [...state.defaultThongSo, data],
          });
          props.handleSubmit();
        }
        fetchData();
      });
  };

  const onRow = (record, index) => {
    return {
      onClick: () => {
        refThongSo.current &&
          refThongSo.current.show(
            { data: state.defaultThongSo[index] },
            (data) => {
              state.defaultThongSo[index] = data;
              async function fetchData() {
                await setState({
                  defaultThongSo: state.defaultThongSo,
                });
                props.handleSubmit();
              }
              fetchData();
            }
          );
      },
    };
  };
  return (
    <>
      <GlobalStyle />
      <Main>
        <Row gutter={[12, 12]}>
          <Col span={16}>
            <span>{t("editor.mauHeader01")}:</span>
          </Col>
          <Col span={8}>
            <Checkbox
              onChange={onChangeCheckBox("isPSHN")}
              checked={state.isPSHN}
            />
          </Col>
          <Col span={16}>
            <span>{t("editor.toDieuTriPhuSan")}:</span>
          </Col>
          <Col span={8}>
            <Checkbox
              onChange={onChangeCheckBox("isToDieuTriPS")}
              checked={state.isToDieuTriPS}
            />
          </Col>
          <Col span={8}>{t("editor.viTriDichVu")}:</Col>
          <Col span={16}>
            <Select
              fit={true}
              value={state.viTriDichVu}
              onChange={onChangeValue("viTriDichVu")}
              options={[
                { label: t("editor.cotYLenh"), value: 2 },
                { label: t("editor.cotDienBien"), value: 1 },
              ]}
            ></Select>
          </Col>

          <Col span={8}>{t("editor.maSoBM")}:</Col>
          <Col span={16}>
            <Input onChange={onChangeInput("maSoBm")} value={state.maSoBm} />
          </Col>
          <Col span={16}>
            <span>{t("editor.hienThiDanhDauDong")}:</span>
          </Col>
          <Col span={8}>
            <Checkbox
              onChange={onChangeCheckBox("markSpanRow")}
              checked={state.markSpanRow}
            />
          </Col>
          <Col span={16}>
            <span>{t("editor.khoaPhieuSauKhiKy")}:</span>
          </Col>
          <Col span={8}>
            <Checkbox
              onChange={onChangeCheckBox("khoaPhieuSauKhiKy")}
              checked={state.khoaPhieuSauKhiKy}
            />
          </Col>
          <Col span={16}>
            <span>{t("editor.hienThiChanKyTruongKhoa")}:</span>
          </Col>
          <Col span={8}>
            <Checkbox
              onChange={onChangeCheckBox("hienThiChanKyTruongKhoa")}
              checked={state.hienThiChanKyTruongKhoa}
            />
          </Col>
          {state.hienThiChanKyTruongKhoa && (
            <Col span={24}>
              <Collapse>
                <Panel
                  header={
                    <div>
                      <div>{t("editor.chanKyTruongKhoa")}</div>
                    </div>
                  }
                  key={"chanKyTruongKhoa"}
                >
                  <Row gutter={[12, 12]}>
                    <Col span={9}>
                      <span>{t("editor.loaiKy")}</span>
                    </Col>
                    <Col span={15}>
                      <Select
                        showSearch
                        size={"small"}
                        style={{ width: "100%" }}
                        value={state.loaiKyTruongKhoa}
                        onSelect={onChangeValue("loaiKyTruongKhoa")}
                      >
                        {DS_LOAI_KY.map((item, index) => (
                          <Select.Option key={index} value={item.id}>
                            {t(item.ten)}
                          </Select.Option>
                        ))}
                      </Select>
                    </Col>
                    <Col span={9}>
                      <span>{t("editor.capKy")}</span>
                    </Col>
                    <Col span={15}>
                      <InputNumber
                        type="number"
                        size={"small"}
                        min={1}
                        value={state.capKyTruongKhoa}
                        onChange={onChangeValue("capKyTruongKhoa")}
                      ></InputNumber>
                    </Col>

                    <Col span={9}>
                      <span>{t("editor.hienThiNguoiKy")}:</span>
                    </Col>
                    <Col span={15}>
                      <Checkbox
                        checked={state.showPatientSignTruongKhoa}
                        onChange={onChangeCheckBox("showPatientSignTruongKhoa")}
                      />
                    </Col>
                    <Col span={9}>
                      <span>{t("editor.hienThiTenTruongKhoa")}:</span>
                    </Col>
                    <Col span={15}>
                      <Checkbox
                        checked={state.hienThiTenTruongKhoa}
                        onChange={onChangeCheckBox("hienThiTenTruongKhoa")}
                      />
                    </Col>
                    {state.showPatientSignTruongKhoa && (
                      <>
                        <Col span={9}>
                          <span>{t("editor.viTriNguoiKy")}</span>
                        </Col>
                        <Col span={15}>
                          <Select
                            showSearch
                            size={"small"}
                            style={{ width: "100%" }}
                            value={state.viTriCaTruongKhoa}
                            onSelect={onChangeValue("viTriCaTruongKhoa")}
                          >
                            {VI_TRI_CA.map((item, index) => (
                              <Select.Option key={index} value={item.id}>
                                {t(item.ten)}
                              </Select.Option>
                            ))}
                          </Select>
                        </Col>
                        <Col span={9}>
                          <span>{t("editor.mauChuNguoiKy")}</span>
                        </Col>
                        <Col span={15}>
                          <PickColor
                            iconComponent={FontColorsOutlined}
                            title={t("editor.chonMauChu")}
                            dataColor={state.contentColorTruongKhoa || "black"}
                            changeColor={onChangeValue(
                              "contentColorTruongKhoa"
                            )}
                          />
                        </Col>
                        <Col span={9}>
                          <span>{t("editor.coChuNguoiKy")}</span>
                        </Col>
                        <Col span={15}>
                          <FontSizeConfig
                            changeFont={onChangeValue("fontSizeCaTruongKhoa")}
                            fontSize={state.fontSizeCaTruongKhoa}
                          />
                        </Col>
                      </>
                    )}
                    <Col span={9}>
                      <span>{t("editor.hienThiCA")}:</span>
                    </Col>
                    <Col span={15}>
                      <Checkbox
                        checked={state.showCaTruongKhoa}
                        onChange={onChangeCheckBox("showCaTruongKhoa")}
                      />
                    </Col>
                    {state.showCaTruongKhoa && (
                      <>
                        <Col span={9}>
                          <span>{t("editor.viTriCA")}</span>
                        </Col>
                        <Col span={15}>
                          <Select
                            showSearch
                            size={"small"}
                            style={{ width: "100%" }}
                            value={state.viTriAnhCaTruongKhoa}
                            onSelect={onChangeValue("viTriAnhCaTruongKhoa")}
                          >
                            {VI_TRI_CA.map((item, index) => (
                              <Select.Option key={index} value={item.id}>
                                {t(item.ten)}
                              </Select.Option>
                            ))}
                          </Select>
                        </Col>
                        <Col span={9}>
                          <span>{t("editor.doRongCA")}</span>
                        </Col>
                        <Col span={15}>
                          <InputNumber
                            size={"small"}
                            value={state.widthCaTruongKhoa}
                            onChange={onChangeValue("widthCaTruongKhoa")}
                          />
                        </Col>
                      </>
                    )}
                  </Row>
                </Panel>
              </Collapse>
            </Col>
          )}
          <Col span={24}>
            <Collapse>
              <Panel
                header={
                  <div>
                    <div>{t("editor.chanKyBacSi")}</div>
                  </div>
                }
                key={"chanKyBacSi"}
              >
                <Row gutter={[12, 12]}>
                  <Col span={8}>
                    <span>{t("editor.loaiKy")}</span>
                  </Col>
                  <Col span={16}>
                    <Select
                      showSearch
                      size={"small"}
                      style={{ width: "100%" }}
                      value={state.loaiKy}
                      onSelect={onChangeValue("loaiKy")}
                    >
                      {DS_LOAI_KY.map((item, index) => (
                        <Select.Option key={index} value={item.id}>
                          {t(item.ten)}
                        </Select.Option>
                      ))}
                    </Select>
                  </Col>
                  <Col span={8}>
                    <span>{t("editor.capKy")}</span>
                  </Col>
                  <Col span={16}>
                    <InputNumber
                      type="number"
                      size={"small"}
                      min={1}
                      value={state.capKy}
                      onChange={onChangeValue("capKy")}
                    ></InputNumber>
                  </Col>
                  <Col span={8}>
                    <span>{t("editor.hienThiNguoiKy")}:</span>
                  </Col>
                  <Col span={16}>
                    <Checkbox
                      checked={state.showPatientSign}
                      onChange={onChangeCheckBox("showPatientSign")}
                    />
                  </Col>
                  {state.showPatientSign && (
                    <>
                      <Col span={8}>
                        <span>{t("editor.viTriNguoiKy")}</span>
                      </Col>
                      <Col span={16}>
                        <Select
                          showSearch
                          size={"small"}
                          style={{ width: "100%" }}
                          value={state.viTriCa}
                          onSelect={onChangeValue("viTriCa")}
                        >
                          {VI_TRI_CA.map((item, index) => (
                            <Select.Option key={index} value={item.id}>
                              {t(item.ten)}
                            </Select.Option>
                          ))}
                        </Select>
                      </Col>
                      <Col span={8}>
                        <span>{t("editor.mauChuNguoiKy")}</span>
                      </Col>
                      <Col span={16}>
                        <PickColor
                          iconComponent={FontColorsOutlined}
                          title={t("editor.chonMauChu")}
                          dataColor={state.contentColor || "black"}
                          changeColor={onChangeValue("contentColor")}
                        />
                      </Col>
                      <Col span={8}>
                        <span>{t("editor.coChuNguoiKy")}</span>
                      </Col>
                      <Col span={16}>
                        <FontSizeConfig
                          changeFont={onChangeValue("fontSizeCa")}
                          fontSize={state.fontSizeCa}
                        />
                      </Col>
                    </>
                  )}

                  <Col span={8}>
                    <span>{t("editor.hienThiCA")}:</span>
                  </Col>
                  <Col span={16}>
                    <Checkbox
                      checked={state.showCa}
                      onChange={onChangeCheckBox("showCa")}
                    />
                  </Col>
                  {state.showCa && (
                    <>
                      <Col span={8}>
                        <span>{t("editor.viTriCA")}</span>
                      </Col>
                      <Col span={16}>
                        <Select
                          showSearch
                          size={"small"}
                          style={{ width: "100%" }}
                          value={state.viTriAnhCa}
                          onSelect={onChangeValue("viTriAnhCa")}
                        >
                          {VI_TRI_CA.map((item, index) => (
                            <Select.Option key={index} value={item.id}>
                              {t(item.ten)}
                            </Select.Option>
                          ))}
                        </Select>
                      </Col>
                      <Col span={8}>
                        <span>{t("editor.doRongCA")}</span>
                      </Col>
                      <Col span={16}>
                        <InputNumber
                          size={"small"}
                          value={state.widthCa}
                          onChange={onChangeValue("widthCa")}
                        />
                      </Col>
                    </>
                  )}
                </Row>
              </Panel>
            </Collapse>
          </Col>
          <Col span={8}>{t("editor.viTriChanKyBacSi")}:</Col>
          <Col span={16}>
            <Select
              fit={true}
              value={state.viTriChanKyBacSi}
              onChange={onChangeValue("viTriChanKyBacSi")}
              options={[
                { label: t("editor.cotYLenh"), value: 2 },
                { label: t("editor.cotDienBien"), value: 1 },
              ]}
            ></Select>
          </Col>
          <Col span={8}>
            <span>{t("editor.tieuDeCuaPhieu")}:</span>
          </Col>
          <Col span={16}>
            <Input
              onChange={onChangeInput("tieuDeCuaPhieu")}
              value={state.tieuDeCuaPhieu}
            ></Input>
          </Col>

          <Col span={8}>
            <span>{t("editor.tieuDeThoiGian")}:</span>
          </Col>
          <Col span={16}>
            <Input
              onChange={onChangeInput("tieuDeThoiGian")}
              value={state.tieuDeThoiGian}
            ></Input>
          </Col>

          <Col span={8}>
            <span>{t("editor.tieuDeDienBienBenh")}:</span>
          </Col>
          <Col span={16}>
            <Input
              onChange={onChangeInput("tieuDeDienBienBenh")}
              value={state.tieuDeDienBienBenh}
            ></Input>
          </Col>

          <Col span={8}>
            <span>{t("editor.tieuDeChiDinh")}:</span>
          </Col>
          <Col span={16}>
            <Input
              onChange={onChangeInput("tieuDeChiDinh")}
              value={state.tieuDeChiDinh}
            ></Input>
          </Col>

          <Col span={8}>
            <span>{t("editor.tieuDeDanDoHLDD")}:</span>
          </Col>
          <Col span={16}>
            <Input
              onChange={onChangeInput("tieuDeDanDoHLDD")}
              value={state.tieuDeDanDoHLDD}
            ></Input>
          </Col>
          <Col span={24} style={{ display: "flex", alignItems: "center" }}>
            {t("editor.bangThongSo")}
            <Button type="success" onClick={onThemMoiThongSo}>
              {t("editor.themMoi")}
            </Button>
          </Col>
          <Col span={24}>
            <TableWrapper
              dataSource={state.defaultThongSo}
              columns={columnsThongSo}
              scroll={{ x: 300 }}
              onRow={onRow}
              rowKey={(record) => record.key}
            />
          </Col>
          {/* Checkbox */}
          <Col span={16}>{t("editor.hienCotHL/DD")}</Col>
          <Col span={8}>
            <Checkbox
              onChange={onChangeCheckBox("showHLDD")}
              checked={state.showHLDD}
            />
          </Col>
          <Col span={16}>{t("editor.hienDVThuThuat")}:</Col>
          <Col span={8}>
            <Checkbox
              onChange={onChangeCheckBox("hideDvTT")}
              checked={state.hideDvTT}
            />
          </Col>
          <Col span={16}>{t("editor.hienĐuongDungThuoc")}:</Col>
          <Col span={8}>
            <Checkbox
              onChange={onChangeCheckBox("showDuongDungThuoc")}
              checked={state.showDuongDungThuoc}
            />
          </Col>
          <Col span={16}>{t("editor.hienSLĐVT")}:</Col>
          <Col span={8}>
            <Checkbox
              onChange={onChangeCheckBox("hienSoLuongDvt")}
              checked={state.hienSoLuongDvt}
            />
          </Col>
          <Col span={16}>{t("editor.hienThiThuocNhaThuoc")}:</Col>
          <Col span={8}>
            <Checkbox
              onChange={onChangeCheckBox("hienThuocNhaThuoc")}
              checked={state.hienThuocNhaThuoc}
            />
          </Col>
          <Col span={16}>{t("editor.hienThiThuocKeNgoai")}:</Col>
          <Col span={8}>
            <Checkbox
              onChange={onChangeCheckBox("hienThuocKeNgoai")}
              checked={state.hienThuocKeNgoai}
            />
          </Col>
          <Col span={16}>{t("editor.hienThiThuocDonTuVan")}:</Col>
          <Col span={8}>
            <Checkbox
              onChange={onChangeCheckBox("hienThuocTuVan")}
              checked={state.hienThuocTuVan}
            />
          </Col>
          <Col span={16}>{t("editor.hienThiBSDieuTri")}</Col>
          <Col span={8}>
            <Checkbox
              onChange={onChangeCheckBox("hienBSDieuTri")}
              checked={state.hienBSDieuTri}
            />
          </Col>
          <Col span={16}>{t("editor.hienThiLoaiChiDinh")}</Col>
          <Col span={8}>
            <Checkbox
              onChange={onChangeCheckBox("hienLoaiChiDinh")}
              checked={state.hienLoaiChiDinh}
            />
          </Col>
          <Col span={16}>{t("editor.hienThiSLBuoi")}</Col>
          <Col span={8}>
            <Checkbox
              onChange={onChangeCheckBox("hienThiSLBuoi")}
              checked={state.hienThiSLBuoi}
            />
          </Col>
          <Col span={16}>{t("editor.hienThiTocDoTruyen")}</Col>
          <Col span={8}>
            <Checkbox
              onChange={onChangeCheckBox("hienThiTocDoTruyen")}
              checked={state.hienThiTocDoTruyen}
            />
          </Col>
          <Col span={16}>{t("editor.hienThiDonViTocDoTruyen")}</Col>
          <Col span={8}>
            <Checkbox
              onChange={onChangeCheckBox("hienThiDonViTocDoTruyen")}
              checked={state.hienThiDonViTocDoTruyen}
            />
          </Col>
          <Col span={16}>{t("editor.gopDv")}</Col>
          <Col span={8}>
            <Checkbox
              onChange={onChangeCheckBox("gopDv")}
              checked={state.gopDv}
            />
          </Col>
          <Col span={16}>{t("editor.themXoaToDieuTri")}</Col>
          <Col span={8}>
            <Checkbox
              onChange={onChangeCheckBox("themXoaToDieuTri")}
              checked={state.themXoaToDieuTri}
            />
          </Col>
          <Col span={16}>{t("editor.hienThiNam")}</Col>
          <Col span={8}>
            <Checkbox
              onChange={onChangeCheckBox("hienThiNam")}
              checked={state.hienThiNam}
            />
          </Col>
          <Col span={16}>{t("editor.hienThiGio")}</Col>
          <Col span={8}>
            <Checkbox
              onChange={onChangeCheckBox("hienThiGio")}
              checked={state.hienThiGio}
            />
          </Col>
          <Col span={16}>{t("editor.gopToDieuTriNhieuNgay")}</Col>
          <Col span={8}>
            <Checkbox
              onChange={onChangeCheckBox("gopToDieuTriNhieuNgay")}
              checked={state.gopToDieuTriNhieuNgay}
            />
          </Col>
          <Col span={16}>{t("editor.hienThiDichVuKham")}</Col>
          <Col span={8}>
            <Checkbox
              onChange={onChangeCheckBox("hienThiDichVuKham")}
              checked={state.hienThiDichVuKham}
            />
          </Col>
          {/* End of Checkbox */}
          <Col span={8}>
            <span>{t("editor.kyTuThuocNhaThuoc")}:</span>
          </Col>
          <Col span={16}>
            <Input
              onChange={onChangeInput("kyTuThuocNhaThuoc")}
              value={state.kyTuThuocNhaThuoc}
            ></Input>
          </Col>
          <Col span={8}>
            <span>{t("editor.kyTuCon")}:</span>
          </Col>
          <Col span={16}>
            <Input
              onChange={onChangeInput("kyTuCon")}
              value={state.kyTuCon}
            ></Input>
          </Col>
          <Col span={8}>
            <span>{t("editor.kyTuThuocKeNgoai")}:</span>
          </Col>
          <Col span={16}>
            <Input
              onChange={onChangeInput("kyTuThuocKeNgoai")}
              value={state.kyTuThuocKeNgoai}
            ></Input>
          </Col>
          <Col span={16}>{t("editor.gopToDieuTriKhiIn")}</Col>
          <Col span={8}>
            <Checkbox
              onChange={onChangeCheckBox("gopToDieuTriKhiIn")}
              checked={state.gopToDieuTriKhiIn}
            />
          </Col>
          <Col span={24}>{t("editor.caiDatHienThiTieuDeTrai")}</Col>
          <Col span={24}>
            <div className="item">
              <Checkbox
                onChange={onChangeCheckBox("showLogo")}
                checked={state.showLogo}
              >
                {t("editor.hienLogo")}
              </Checkbox>
            </div>
          </Col>
          <Col span={24}>
            <div className="item">
              <Checkbox
                onChange={onChangeCheckBox("showTitleLeft1")}
                checked={state.showTitleLeft1}
              >
                {t("editor.hienTieuDeTrai1")}
              </Checkbox>
            </div>
          </Col>
          <Col span={24}>
            <div className="item">
              <Checkbox
                onChange={onChangeCheckBox("showTitleLeft2")}
                checked={state.showTitleLeft2}
              >
                {t("editor.hienTieuDeTrai2")}
              </Checkbox>
            </div>
          </Col>
          <Col span={24}></Col>
          <Col span={24}>{t("editor.sapXepTieuDePhai")}</Col>
          <Col span={24}>
            <DragDropContext onDragEnd={onDragEnd("headerRight")}>
              <Droppable droppableId="droppable">
                {(provided, snapshot) => (
                  <div {...provided.droppableProps} ref={provided.innerRef}>
                    {state.positionHeaderRight.map((item, index) => (
                      <Draggable
                        key={item.fieldName}
                        draggableId={item.fieldName}
                        index={index}
                      >
                        {(provided, snapshot) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            className="item"
                          >
                            <Checkbox
                              checked={item.show}
                              onChange={onChangeHeaderRight(item.fieldName)}
                            >
                              {item.ten.includes("editor.")
                                ? t(item.ten)
                                : item.ten}
                            </Checkbox>
                            <SVG.IcDrag className="ic-drag" />
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          </Col>
          <Col span={24}>{t("editor.sapXepThongTinChung")}</Col>
          <Col span={24}>
            <DragDropContext onDragEnd={onDragEnd("thongTinChung")}>
              <Droppable droppableId="droppable">
                {(provided, snapshot) => (
                  <div {...provided.droppableProps} ref={provided.innerRef}>
                    {state.listSapXepThongTinChung.map((item, index) => (
                      <Draggable
                        key={item.fieldName}
                        draggableId={item.fieldName}
                        index={index}
                      >
                        {(provided, snapshot) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            className="item"
                          >
                            <Checkbox
                              checked={item.show}
                              onChange={onChangeSapXepThongTinChung(
                                item.fieldName,
                                "show"
                              )}
                            >
                              {t(`${item.ten}`)}
                            </Checkbox>
                            <div>
                              <span>Tên hiển thị:</span>
                              <Input
                                value={item.tenHienThi}
                                onChange={onChangeSapXepThongTinChung(
                                  item.fieldName,
                                  "tenHienThi"
                                )}
                              ></Input>
                            </div>

                            <SVG.IcDrag className="ic-drag ic-drag-2" />
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          </Col>
          <Col span={24}>{t("editor.sapXepChiSoSong")}</Col>
          <Col span={24}>
            <DragDropContext onDragEnd={onDragEnd("Css")}>
              <Droppable droppableId="droppable">
                {(provided, snapshot) => (
                  <div {...provided.droppableProps} ref={provided.innerRef}>
                    {state.listCss.map((item, index) => (
                      <Draggable
                        key={item.fieldName}
                        draggableId={item.fieldName}
                        index={index}
                      >
                        {(provided, snapshot) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            className="item flex justify-between align-items-center"
                          >
                            <Checkbox
                              checked={item.show}
                              onChange={onChangeCss(item.fieldName)}
                            >
                              {t(item.ten)}
                            </Checkbox>
                            <SVG.IcDrag className="ic-drag" />
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          </Col>
          <Col span={24}>{t("editor.sapXepCotYLenh")}</Col>
          <Col span={24}>
            <DragDropContext onDragEnd={onDragEnd("yLenh")}>
              <Droppable droppableId="droppable">
                {(provided, snapshot) => (
                  <div {...provided.droppableProps} ref={provided.innerRef}>
                    {state.listSapXepYLenh.map((item, index) => (
                      <Draggable
                        key={item.fieldName}
                        draggableId={item.fieldName}
                        index={index}
                      >
                        {(provided, snapshot) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            className="item flex justify-between align-items-center"
                          >
                            <Checkbox
                              checked={item.show}
                              onChange={onChangeSapXepYLenh(item.fieldName)}
                            >
                              {t(`editor.${item.fieldName}`)}
                            </Checkbox>
                            <div>
                              {item.fieldName === "dichVuCls" && (
                                <Dropdown
                                  overlayClassName="dropdown-them-thong-tin"
                                  menu={{
                                    items: [
                                      {
                                        key: "tenNhomDichVuCap2",
                                        label: (
                                          <div className="item">
                                            <div className="label">
                                              {t(
                                                "editor.hienThiTenNhomDichVuCap2"
                                              )}
                                            </div>
                                            <Checkbox
                                              checked={
                                                state.tuyChinhTruongSapXepYLenh.find(
                                                  (item) =>
                                                    item.parentFieldName ===
                                                      "dichVuCls" &&
                                                    item.fieldName ===
                                                      "tenNhomDichVuCap2"
                                                )?.show ?? true
                                              }
                                              onClick={(e) => {
                                                e.stopPropagation();
                                              }}
                                              onChange={(e) => {
                                                const index =
                                                  state.tuyChinhTruongSapXepYLenh.findIndex(
                                                    (item) =>
                                                      item.parentFieldName ===
                                                        "dichVuCls" &&
                                                      item.fieldName ===
                                                        "tenNhomDichVuCap2"
                                                  );
                                                if (index !== -1) {
                                                  state.tuyChinhTruongSapXepYLenh[
                                                    index
                                                  ] = {
                                                    ...state
                                                      .tuyChinhTruongSapXepYLenh[
                                                      index
                                                    ],
                                                    show: e.target.checked,
                                                  };
                                                }
                                                setState({
                                                  tuyChinhTruongSapXepYLenh:
                                                    state.tuyChinhTruongSapXepYLenh,
                                                });
                                              }}
                                            />
                                          </div>
                                        ),
                                      },
                                    ],
                                  }}
                                  placement="bottomRight"
                                  trigger={["click"]}
                                >
                                  <SVG.IcEdit className="ic-edit cursor-pointer" />
                                </Dropdown>
                              )}
                              <SVG.IcDrag className="ic-drag" />
                            </div>
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          </Col>
          <Col span={24}>{t("editor.sapXepCotDienBienBenh")}</Col>
          <Col span={24}>
            <DragDropContext onDragEnd={onDragEnd("dienBienBenh")}>
              <Droppable droppableId="droppable">
                {(provided, snapshot) => (
                  <div {...provided.droppableProps} ref={provided.innerRef}>
                    {state.listSapXepDienBienBenh.map((item, index) => (
                      <Draggable
                        key={item.fieldName}
                        draggableId={item.fieldName}
                        index={index}
                      >
                        {(provided, snapshot) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            className="item flex justify-between align-items-center"
                          >
                            <Checkbox
                              checked={item.show}
                              onChange={onChangeSapXepDienBienBenh(
                                item.fieldName
                              )}
                            >
                              {t(`${item.ten}`)}
                            </Checkbox>
                            <SVG.IcDrag className="ic-drag" />
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          </Col>
        </Row>
        <ModalThongSo ref={refThongSo} />
      </Main>
    </>
  );
});

BangTheoDoiBenhNhanHSTCProperties.defaultProps = {
  state: {},
};

BangTheoDoiBenhNhanHSTCProperties.propTypes = {
  state: T.shape({}),
};

export default BangTheoDoiBenhNhanHSTCProperties;
