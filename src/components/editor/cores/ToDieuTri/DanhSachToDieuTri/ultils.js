import React, { memo, useEffect, useRef, useState } from "react";
import { DeboundInput } from "components/editor/config";
import { <PERSON><PERSON>, <PERSON>, Menu, Row } from "antd";
import { BangThongSoStyled } from "./styled";
import { SettingOutlined } from "@ant-design/icons";
import { cloneDeep, get, groupBy, orderBy } from "lodash";
import stringUtils from "mainam-react-native-string-utils";
import { numberToString, docSoViet } from "utils";
import DropDownList from "../../DropDownList";
import { DragDropContext, Draggable, Droppable } from "react-beautiful-dnd";
import { useDraggableInPortal, useThietLap } from "hooks";
import { SVG } from "assets";
import TextField from "components/TextField";
import { Dropdown } from "components";
import { t } from "i18next";
import { GIOI_TINH_BY_VALUE, THIET_LAP_CHUNG } from "constants/index";
import { gopThuoc<PERSON>hacLo } from "utils/chi-dinh-thuoc-utils";

export const LIST_CHI_SO_SONG = [
  {
    ten: t("editor.mach"),
    donViTinh: t("editor.l/phut"),
    fieldName: "mach",
    show: true,
  },
  {
    ten: t("editor.nhietDo"),
    donViTinh: "°C",
    fieldName: "nhietDo",
    show: true,
  },
  {
    ten: t("editor.bMI"),
    donViTinh: "",
    fieldName: "bmi",
    show: true,
  },
  {
    ten: t("editor.hA"),
    donViTinh: "mmHg",
    fieldName: "huyetAp",
    show: true,
  },
  {
    ten: t("editor.canNang"),
    donViTinh: "kg",
    fieldName: "canNang",
    show: true,
  },
  {
    ten: t("editor.nhipTho"),
    donViTinh: t("editor.l/phut"),
    fieldName: "nhipTho",
    show: true,
  },
  {
    ten: t("editor.chieuCao"),
    donViTinh: "cm",
    fieldName: "chieuCao",
    show: true,
  },
  {
    ten: t("editor.nhomMau"),
    donViTinh: "",
    fieldName: "nhomMau",
    show: true,
  },
  {
    ten: "Sp02",
    donViTinh: "",
    fieldName: "spo2",
    show: true,
  },
];

export const LIST_SAP_XEP_Y_LENH = [
  {
    ten: t("editor.thongTinBoSung"),
    fieldName: "boSung",
    show: true,
  },
  {
    ten: t("editor.bangThongSo"),
    fieldName: "dsThongSoMay",
    show: true,
  },
  {
    ten: t("editor.thuoc"),
    fieldName: "thuoc",
    show: true,
  },
  {
    ten: t("editor.ghiChu"),
    fieldName: "ghiChu",
    show: true,
  },
  {
    ten: t("editor.cheDoAn"),
    fieldName: "cheDoAn",
    show: true,
  },
  {
    ten: t("editor.cheDoChamSoc"),
    fieldName: "cheDoChamSoc",
    show: true,
  },
  {
    ten: t("editor.cheDoTheoDoi"),
    fieldName: "cheDoTheoDoi",
    show: true,
  },
  {
    ten: t("editor.dichVuCLS"),
    fieldName: "dichVuCls",
    show: true,
  },
  {
    ten: t("editor.mau"),
    fieldName: "mau",
    show: true,
  },
  {
    ten: t("editor.phanLoaiPHCN"),
    fieldName: "phanLoaiPHCN",
    show: true,
  },
];

export const LIST_SAP_XEP_DIEN_BIEN_BENH = [
  {
    ten: "khamBenh.chanDoan.chanDoanSoBo",
    fieldName: "cdSoBo",
    show: false,
  },
  {
    ten: "common.chanDoan",
    fieldName: "dsCdChinhId",
    show: false,
  },
  {
    ten: "khamBenh.chanDoan.chanDoanKemTheo",
    fieldName: "dsCdKemTheoId",
    show: false,
  },
  {
    ten: "khamBenh.chanDoan.moTaChiTiet",
    fieldName: "moTa",
    show: false,
  },
  {
    ten: "khamBenh.chanDoan.theBenh",
    fieldName: "theBenhLao",
    show: false,
  },
  {
    ten: "khamBenh.hoiBenh.quaTrinhBenhLy",
    fieldName: "quaTrinhBenhLy",
    show: false,
  },
  {
    ten: "khamBenh.hoiBenh.banThan",
    fieldName: "tienSuBanThan",
    show: false,
  },
  {
    ten: "khamBenh.hoiBenh.giaDinh",
    fieldName: "tienSuGiaDinh",
    show: false,
  },
  {
    ten: "khamBenh.hoiBenh.diUngThuoc",
    fieldName: "diUngThuoc",
    show: false,
  },
  {
    ten: "khamBenh.khamXet.toanThan",
    fieldName: "toanThan",
    show: false,
  },
  {
    ten: "khamBenh.khamXet.cacBoPhan",
    fieldName: "cacBoPhan",
    show: false,
  },
  {
    ten: "khamBenh.khamXet.luuY",
    fieldName: "ghiChu",
    show: false,
  },
  {
    ten: "khamBenh.khamXet.dienBien",
    fieldName: "dienBienBenh",
    show: true,
  },
  {
    ten: "hoiChan.giaiDoanBenh",
    fieldName: "giaiDoanBenh",
    show: false,
  },
  {
    ten: "tenTruong.ketQuaCls",
    fieldName: "nbTomTatCls",
    show: false,
  },
];

export const LIST_SAP_XEP_THONG_TIN_CHUNG = [
  {
    ten: "editor.toDieuTri.hoTenNB",
    fieldName: "form.tenNb",
    show: true,
    tenHienThi: "Họ tên NB",
  },
  {
    ten: "editor.toDieuTri.tuoi",
    fieldName: "form.tuoi2",
    show: true,
    tenHienThi: "Tuổi",
  },
  {
    ten: "editor.toDieuTri.gioiTinh",
    fieldName: "form.gioiTinh",
    show: true,
    tenHienThi: "Giới tính",
  },
  {
    ten: "editor.toDieuTri.khoa",
    fieldName: "toDieuTri.tenKhoaChiDinh",
    show: true,
    tenHienThi: "Khoa",
  },
  {
    ten: "editor.toDieuTri.phong",
    fieldName: "toDieuTri.tenPhong",
    show: true,
    tenHienThi: "Phòng",
  },
  {
    ten: "editor.toDieuTri.soGiuong",
    fieldName: "toDieuTri.soHieuGiuong",
    show: true,
    tenHienThi: "Số giường",
  },
  {
    ten: "editor.toDieuTri.diaChi",
    fieldName: "form.diaChi",
    show: true,
    tenHienThi: "Địa chỉ",
  },
  {
    ten: "editor.toDieuTri.chanDoan",
    fieldName: "toDieuTri.cdChinh",
    show: true,
    tenHienThi: "Chẩn đoán",
  },
  {
    ten: "editor.toDieuTri.chanDoanKemTheo",
    fieldName: "toDieuTri.cdKemTheo",
    show: true,
    tenHienThi: "Chẩn đoán kèm theo",
  },
  {
    ten: "editor.toDieuTri.moTa",
    fieldName: "toDieuTri.moTa",
    show: true,
    tenHienThi: "Chẩn đoán (mô tả chi tiết)",
  },
  {
    ten: "editor.toDieuTri.chanDoanPhanBiet",
    fieldName: "toDieuTri.cdPhanBiet",
    show: true,
    tenHienThi: "Chẩn đoán phân biệt",
  },
];

export const DS_TIEU_DE_PHAI = [
  {
    ten: "Barcode",
    fieldName: "barcode",
    show: false,
  },
  {
    ten: t("editor.mauSo"),
    fieldName: "maSoBm",
    show: true,
    defaultValue: "39/BV-01",
  },
  { ten: t("editor.soVaoVien"), fieldName: "maHoSo", show: true },
  { ten: t("editor.maNb"), fieldName: "maNb", show: true },
  { ten: t("editor.maBa"), fieldName: "maBenhAn", show: true },
];
export const TEN_TIEU_DE_TRAI_BY_FIELDNAME = {
  maSoBm: "Mẫu số",
  maHoSo: "Số vào viện",
  maNb: "Mã NB",
  maBenhAn: "Mã BA",
};
export const TEN_TIEU_DE_TRAI_BY_FIELDNAME_SAN_HN = {
  maSoBm: "Mẫu số",
  maHoSo: "Mã HS",
  maNb: "Mã NB",
  maBenhAn: "Mã BA",
};

export const LIST_TUY_CHINH_TRUONG_SAP_XEP_Y_LENH = [
  {
    ten: "editor.hienThiTenNhomDichVuCap2",
    parentFieldName: "dichVuCls",
    fieldName: "tenNhomDichVuCap2",
    show: true,
  },
];

export const renderThongSoChung = ({
  chiSoSong = {},
  onChangeChiSoSong = () => {},
  onChangeChiSoSongKhac = () => {},
  isDisable = false,
  ngayDieuTri,
  listCss = [],
  dataNhomMau,
  index: idx,
  toDieuTriId,
  isEditDienBien,
}) => {
  return (
    <div className="thong-so-chung">
      <div className="text-center title">
        <b>{t("editor.thongSoChung")}</b>
      </div>
      <Row>
        {[0, 1].map((item) => (
          <Col span={12} className={!item ? "left" : "right"}>
            {listCss
              .filter((el, index) =>
                item ? index >= listCss.length / 2 : index < listCss.length / 2
              )
              .map((chiSo, index) => {
                //chỉ số sống khác
                let _value = chiSoSong[chiSo.fieldName] || "";
                let _findIndex = -1;
                if (chiSo?.id) {
                  _findIndex = (chiSoSong.dsChiSoSongKhac || []).findIndex(
                    (x) => x.chiSoSongId === chiSo?.id
                  );
                  if (_findIndex > -1) {
                    _value = chiSoSong.dsChiSoSongKhac[_findIndex].giaTri;
                  }
                }
                return (
                  <div className="flex">
                    <div className="flex">
                      {chiSo.fieldName === "nhomMau" ? (
                        <div style={{ width: "80px" }}>{t(chiSo.ten)}: </div>
                      ) : (
                        <div style={{ marginRight: 2 }}>{t(chiSo.ten)}: </div>
                      )}
                      <div style={{ minWidth: "30px" }}>
                        {chiSo.fieldName === "nhomMau" ? (
                          <DropDownList
                            component={{
                              props: {
                                checkList: dataNhomMau,
                                fieldName: "value",
                                listType: 2,
                                noLabel: true,
                                minHeight: "10",
                                fontSize: "12",
                                lineHeight: "1.5",
                                type: "onlyOne",
                                readOnly: isDisable,
                                markSpanRow: false,
                              },
                            }}
                            blockWidth="50px"
                            className={"drop-list"}
                            form={{
                              value: chiSoSong[chiSo.fieldName],
                            }}
                            formChange={{
                              value: (value) => {
                                onChangeChiSoSong(
                                  chiSoSong,
                                  chiSo.fieldName,
                                  ngayDieuTri,
                                  idx,
                                  toDieuTriId,
                                  isEditDienBien
                                )(value);
                              },
                            }}
                          />
                        ) : (
                          <DeboundInput
                            rows={1}
                            value={_value}
                            inputNumber={
                              chiSo.fieldName !== "huyetAp" ? true : false
                            }
                            onChange={
                              chiSo?.id
                                ? onChangeChiSoSongKhac(
                                    chiSoSong,
                                    _findIndex,
                                    chiSo,
                                    ngayDieuTri,
                                    toDieuTriId,
                                    isEditDienBien
                                  )
                                : onChangeChiSoSong(
                                    chiSoSong,
                                    chiSo.fieldName,
                                    ngayDieuTri,
                                    idx,
                                    toDieuTriId,
                                    isEditDienBien
                                  )
                            }
                            typeNumber={
                              chiSo.fieldName === "nhipTho" ? "int" : "float"
                            }
                            type="multipleline"
                            lineHeightText={1.15}
                            fontSize={12}
                            minHeight={12 + 6}
                            readOnly={isDisable}
                            disabled={isDisable}
                          ></DeboundInput>
                        )}
                      </div>
                    </div>
                    <div>{t(chiSo.donViTinh)}</div>
                  </div>
                );
              })}
          </Col>
        ))}
      </Row>
    </div>
  );
};

export const renderThongTinChung = ({
  form,
  itemProps,
  onChange,
  isDisable,
  maBaoCao,
}) => {
  const listShow = (
    itemProps.listSapXepThongTinChung || LIST_SAP_XEP_THONG_TIN_CHUNG
  ).filter((item) => item.show);
  const firtsRow = new Array(6)
    .fill({})
    .map((item, index) => listShow[index] || {});
  const secondRow =
    listShow.length > 5 ? listShow.slice(6, listShow.length) : [];
  const listSpanFirstRow = { 0: 12, 1: 7, 2: 5, 3: 24, 4: 12, 5: 12 };

  return (
    <>
      <Row>
        {firtsRow.map((item, index) => {
          return (
            <Col span={listSpanFirstRow[index]} key={index}>
              <b>{item.tenHienThi ? item.tenHienThi + ": " : ""}</b>
              {item.fieldName === "form.gioiTinh" ? (
                get(form, item.fieldName) ? (
                  GIOI_TINH_BY_VALUE[get(form, item.fieldName) || 1]
                ) : (
                  ""
                )
              ) : item.fieldName === "toDieuTri.tenKhoaChiDinh" ? (
                get(
                  form,
                  maBaoCao === "EMR_BA077.2" ? "form.tenKhoaNb" : item.fieldName
                )
              ) : item.fieldName === "form.tenNb" ? (
                <b>{get(form, item.fieldName)}</b>
              ) : (
                get(form, item.fieldName)
              )}
            </Col>
          );
        })}

        {secondRow.length
          ? secondRow.map((item, index) => {
              let _fieldName = (get(form, item.fieldName, "") || "") + "";
              let tenKhoa =
                item.fieldName === "toDieuTri.tenKhoaChiDinh" &&
                maBaoCao === "EMR_BA077.2"
                  ? (get(form, "toDieuTri.tenKhoaNb", "") || "") + ""
                  : _fieldName;

              return (
                <Col span={24} key={item.fieldName}>
                  <b>{item.tenHienThi}: </b>
                  {item.fieldName === "form.gioiTinh" ? (
                    get(form, item.fieldName) ? (
                      GIOI_TINH_BY_VALUE[get(form, item.fieldName) || 1]
                    ) : (
                      ""
                    )
                  ) : item.fieldName === "toDieuTri.tenKhoaChiDinh" ? (
                    <b>{tenKhoa.replaceAll(";", "/")}</b>
                  ) : (
                    _fieldName.replaceAll(";", "/")
                  )}
                </Col>
              );
            })
          : null}
      </Row>
    </>
  );
};

export const formatText = (value) => {
  let text = (value || "")?.replaceAll("\n", "<br>");
  let textReplace = text?.replaceAll(" null", "");
  return textReplace;
};

export const parseTextToHtml = (html) => {
  if (!html) return "";
  let doc = new DOMParser().parseFromString(html, "text/html");
  return doc?.body?.textContent?.trim() || "";
};

export const RenderBangThongSo = memo(
  ({
    item,
    isDisable,
    onChangeThongSo,
    index,
    onRemoveThongSo,
    data,
    onMergeColumns,
    onTachCot,
    ngayDieuTri,
    indexToDieuTri,
  }) => {
    const content = (index, data) => (
      <Menu
        items={[
          {
            key: 0,
            label: (
              <a
                href={() => false}
                onClick={() => {
                  onMergeColumns(index, data, ngayDieuTri, indexToDieuTri)();
                }}
              >
                {t("editor.toDieuTri.gopCot")}
              </a>
            ),
          },
          {
            key: 1,
            label: (
              <a
                href={() => false}
                onClick={() => {
                  onTachCot(index, data, ngayDieuTri, indexToDieuTri)();
                }}
              >
                {t("editor.toDieuTri.tachCot")}
              </a>
            ),
          },
          {
            key: 2,
            label: (
              <a
                href={() => false}
                onClick={() => {
                  onRemoveThongSo(index, data, ngayDieuTri, indexToDieuTri)();
                }}
              >
                {t("editor.toDieuTri.xoaHang")}
              </a>
            ),
          },
        ]}
      />
    );
    return (
      <BangThongSoStyled>
        <td
          width={"50%"}
          colSpan={item.hienThi ? 2 : 1}
          {...(item.hienThi === 2 ? { style: { display: "none" } } : {})}
        >
          <Dropdown trigger="click" overlay={content(index, data)}>
            <Button
              size="small"
              className="icon-delete"
              icon={<SettingOutlined />}
            ></Button>
          </Dropdown>
          <TextField
            onChange={onChangeThongSo(
              "ma",
              index,
              data,
              ngayDieuTri,
              indexToDieuTri
            )}
            html={parseTextToHtml(item?.ma)}
            style={{ height: "100%" }}
          />
        </td>
        <td
          width={"50%"}
          colSpan={item.hienThi ? 2 : 1}
          {...(item.hienThi === 1 ? { style: { display: "none" } } : {})}
        >
          {item.hienThi === 2 && (
            <Dropdown trigger="click" overlay={content(index, data)}>
              <Button
                size="small"
                className="icon-delete"
                icon={<SettingOutlined />}
              ></Button>
            </Dropdown>
          )}

          <TextField
            onChange={onChangeThongSo(
              "giaTri",
              index,
              data,
              ngayDieuTri,
              indexToDieuTri
            )}
            html={parseTextToHtml(item?.giaTri)}
            style={{ height: "100%" }}
          />
        </td>
      </BangThongSoStyled>
    );
  }
);

export const sortThuocDuongDung = (thuocs) => {
  const thuocsSortDuongDung = thuocs.filter((thuoc) => thuoc.sttDuongDung);
  const groupBySttDuongDung = groupBy(thuocsSortDuongDung, "sttDuongDung");
  const sttDuongDung = Object.keys(groupBySttDuongDung)
    .map((item) => ({
      tenDuongDung: item,
      sttDuongDung: groupBySttDuongDung[item][0].sttDuongDung,
    }))
    .sort((a, b) => a.sttDuongDung - b.sttDuongDung);
  Object.keys(groupBySttDuongDung).forEach((thuoc) => {
    const dsThuoc = groupBySttDuongDung[thuoc].sort(
      (a, b) =>
        new Date(a.thoiGianChiDinh).getTime() -
        new Date(b.thoiGianChiDinh).getTime()
    );
    groupBySttDuongDung[thuoc] = dsThuoc;
  });
  let listThuocsSortDuongDung = [];
  sttDuongDung.forEach((item) => {
    listThuocsSortDuongDung.push(...groupBySttDuongDung[item.tenDuongDung]);
  });
  return listThuocsSortDuongDung;
};

export const sortThuoc = (thuocs) => {
  const listThuocsSortDuongDung = sortThuocDuongDung(thuocs);
  const thuocNotSttDuongDung = thuocs
    .filter((thuoc) => !thuoc.sttDuongDung)
    .sort(
      (a, b) =>
        new Date(a.thoiGianChiDinh).getTime() -
        new Date(b.thoiGianChiDinh).getTime()
    );
  return [...listThuocsSortDuongDung, ...thuocNotSttDuongDung];
};

export const renderDichVu = (data, gopDv) => {
  if (gopDv) {
    const listData = groupBy(data, (item) => `${item.dichVuId}-${item.ghiChu}`);
    return Object.keys(listData)
      .map((item, index) => {
        const dichVu = listData[item][0];
        return `${dichVu.tenDichVu || ""} ${
          dichVu?.ghiChu ? ` (${dichVu.ghiChu})` : ""
        } ngày 1 lần x ${listData[item]?.length} ngày `;
      })
      .join(", ");
  } else {
    return (data || [])
      .map((item, index) => {
        return `${item?.tenDichVu}${
          item.soLuong != 1 ? ` (${item.soLuong} ${item.tenDonViTinh})` : ""
        }${item?.ghiChu ? ` (${item.ghiChu})` : ""}`;
      })
      .join(", ");
  }
};

export const renderListDv = (
  ngayDieuTri,
  gopDv,
  showTenNhomDichVuCap2 = true,
  hienThiDichVuKham
) => {
  const dvXetNghiem = groupBy(
    ngayDieuTri?.dsXetNghiem || [],
    "nhomDichVuCap2Id"
  );

  const dsCdhaTdcn = groupBy(ngayDieuTri?.dsCdhaTdcn || [], "nhomDichVuCap2Id");
  const dsPttt = groupBy(ngayDieuTri?.dsPtTt, "tenNhomDichVuCap2");
  const dsKham = groupBy(ngayDieuTri?.dsKham, "tenNhomDichVuCap2");

  return (
    <>
      {(!!Object.keys(dvXetNghiem).length ||
        !!Object.keys(dsCdhaTdcn).length ||
        !!Object.keys(dsPttt).length ||
        !!Object.keys(dsKham).length) && (
        <div className="fs16 b">
          {gopDv
            ? t("editor.toDieuTri.chiDinh")
            : t("editor.chiDinhCanLamSang")}
          :
        </div>
      )}
      {(Object.keys(dvXetNghiem) || []).map((xetNghiem, index) => {
        return (
          <div className="ds-xet-nghiem" key={index}>
            {showTenNhomDichVuCap2 && (
              <span className="b">
                {" "}
                {`${dvXetNghiem[xetNghiem][0].tenNhomDichVuCap2 || ""}`}:{" "}
              </span>
            )}

            {renderDichVu(dvXetNghiem[xetNghiem], gopDv)}
          </div>
        );
      })}
      {(Object.keys(dsCdhaTdcn) || []).map((dichVu, index) => {
        return (
          <div className="ds-xet-nghiem" key={index}>
            {showTenNhomDichVuCap2 && (
              <span className="b">
                {" "}
                {`${dsCdhaTdcn[dichVu][0].tenNhomDichVuCap2 || ""}`}:{" "}
              </span>
            )}
            {renderDichVu(dsCdhaTdcn[dichVu], gopDv)}
          </div>
        );
      })}
      {(Object.keys(dsPttt) || []).map((item, index) => {
        return (
          <div className="ds-xet-nghiem" key={index}>
            <span className="b">{`${item || ""}`}: </span>
            {renderDichVu(dsPttt[item], gopDv)}
          </div>
        );
      })}
      {hienThiDichVuKham && (Object.keys(dsKham) || []).map((item, index) => {
        return (
          <div className="ds-xet-nghiem" key={index}>
            <span className="b">{`${item || ""}`}: </span>
            {renderDichVu(dsKham[item], gopDv)}
          </div>
        );
      })}
    </>
  );
};

export const renderListDvNgoaiTru = (listDvkt) => {
  const dsDvKt = groupBy(listDvkt || [], "nhomDichVuCap2Id");
  return (
    <>
      {!!Object.keys(dsDvKt).length && (
        <div className="fs16 b">{t("editor.chiDinhCanLamSang")}:</div>
      )}

      {(Object.keys(dsDvKt) || []).map((dvkt, index) => {
        return (
          <div className="ds-xet-nghiem" key={index}>
            <span className="b">
              {" "}
              {`${dsDvKt[dvkt][0].tenNhomDichVuCap2 || ""}`}:{" "}
            </span>
            {renderDichVu(dsDvKt[dvkt])}
          </div>
        );
      })}
    </>
  );
};

export const renderMau = ({ ngayDieuTri, listNhomMau }) => {
  let dsChePhamMau = groupBy(ngayDieuTri.dsChePhamMau || [], (item) => {
    return `${item.tenDichVu}-${item.nhomMau}-${item.ghiChu}-${item.cachDung}`;
  });
  dsChePhamMau = Object.keys(dsChePhamMau).map((item) => {
    return {
      tenDichVu: dsChePhamMau[item][0]?.tenDichVu,
      soLuong: dsChePhamMau[item].reduce((a, b) => a + b.soLuong, 0),
      nhomMau: dsChePhamMau[item][0]?.nhomMau,
      ghiChu: dsChePhamMau[item][0]?.ghiChu,
      tenDonViTinh: dsChePhamMau[item][0]?.tenDonViTinh,
      cachDung: dsChePhamMau[item][0]?.cachDung,
    };
  });
  return (dsChePhamMau || []).map((data) => {
    return (
      <div>
        <span className="b">{`${data.tenDichVu} `}</span>
        <span>
          {data.soLuong != 1 ? `(${data.soLuong} ${data?.tenDonViTinh})` : ""}
          {data.ghiChu ? ` (${data.ghiChu})` : ""}
        </span>
        <div className="i">{data.cachDung ? ` ${data.cachDung}` : ""}</div>
        <div className="i">
          {t("editor.nhomMau")}:{" "}
          {listNhomMau.find((mau) => mau.id === data.nhomMau)?.ten}
        </div>
      </div>
    );
  });
};

export const RenderThuocs = memo(
  ({
    item,
    isSub,
    listDonViTocDoTruyen,
    showDuongDungThuoc = true,
    hienSoLuongDvt = true,
    showIconDrag = true,
    listLoaiChiDinh = [],
    hienLoaiChiDinh = false,
    hienThiSLBuoi = false,
    hienThiTocDoTruyen = false,
    hienThiDonViTocDoTruyen = false,
    maBaoCao,
    itemProps = {},
  }) => {
    const [TO_DIEU_TRI_NOI_TRU_CHI_HIEN_THI_CACH_DUNG_THUOC_CHA] = useThietLap(
      THIET_LAP_CHUNG.TO_DIEU_TRI_NOI_TRU_CHI_HIEN_THI_CACH_DUNG_THUOC_CHA
    );
    const [PHAN_LOAI_THUOC_HUONG_THAN] = useThietLap(
      THIET_LAP_CHUNG.PHAN_LOAI_THUOC_HUONG_THAN
    );
    const [PHAN_LOAI_THUOC_GAY_NGHIEN] = useThietLap(
      THIET_LAP_CHUNG.PHAN_LOAI_THUOC_GAY_NGHIEN
    );
    const [HIEN_THI_KY_HIEU_THUOC_THEO_DOI] = useThietLap(
      THIET_LAP_CHUNG.HIEN_THI_KY_HIEU_THUOC_THEO_DOI
    );
    const thuocHuongThan = PHAN_LOAI_THUOC_HUONG_THAN?.split(",");
    const thuocGayNghien = PHAN_LOAI_THUOC_GAY_NGHIEN?.split(",");
    const buoiDung = [];
    if (item.slSang)
      buoiDung.push({ ten: t("editor.sang"), soLuong: item.slSang });
    if (item.slChieu)
      buoiDung.push({ ten: t("editor.chieu"), soLuong: item.slChieu });
    if (item.slToi)
      buoiDung.push({ ten: t("editor.toi"), soLuong: item.slToi });
    if (item.slDem)
      buoiDung.push({ ten: t("editor.dem"), soLuong: item.slDem });

    const donViTocDoTruyen = (listDonViTocDoTruyen || []).find(
      (el) => el.id === item.donViTocDoTruyen
    );
    const tenDuongDung = showDuongDungThuoc ? item?.tenDuongDung || "" : "";
    const _tocDoTruyenTxt =
      hienThiTocDoTruyen && item.tocDoTruyen ? `, ${item.tocDoTruyen}` : "";
    const _donViTocDoTruyenTxt =
      hienThiDonViTocDoTruyen && donViTocDoTruyen
        ? `(${donViTocDoTruyen?.ten})`
        : "";
    const _isHideBuoiDung =
      !hienThiSLBuoi && !hienThiTocDoTruyen && !hienThiDonViTocDoTruyen;

    let textBuoiDung = hienThiSLBuoi
      ? buoiDung
          .map((buoi) => {
            const _buoiTxt = buoi.ten ? ` ${buoi.ten}` : "";
            const _slBuoiTxt = buoi.soLuong ? ` ${buoi.soLuong}` : "";

            return `${tenDuongDung}${_buoiTxt}${_slBuoiTxt}${_tocDoTruyenTxt}${_donViTocDoTruyenTxt}`;
          })
          .join(", ")
      : "";

    if (
      (!item.slSang && !item.slToi && !item.slDem && !item.slChieu) ||
      (!hienThiSLBuoi && (hienThiDonViTocDoTruyen || hienThiTocDoTruyen))
    ) {
      textBuoiDung = `${tenDuongDung}${_tocDoTruyenTxt}${_donViTocDoTruyenTxt}`;
    }
    if (_isHideBuoiDung) {
      textBuoiDung = null;
    }

    const tenHoatChat_HamLuong = () => {
      let textCustom = "";
      if (item.tenHoatChat && !item.hamLuong) {
        textCustom = ` (${item?.tenHoatChat})`;
      } else if (!item.tenHoatChat && item.hamLuong) {
        textCustom = ` (${item?.hamLuong})`;
      } else if (item.tenHoatChat && item.hamLuong) {
        textCustom = ` (${item?.tenHoatChat} - ${item?.hamLuong})`;
      }
      return textCustom;
    };
    const soLuong_Dvt = () => {
      if (!hienSoLuongDvt) return null;

      const isDocSo =
        item.loaiDonThuoc === 20 ||
        thuocGayNghien?.includes(item.maPhanLoaiDvKho);

      return (
        <>
          {" x "}
          {isDocSo ? (
            <span className="so-luong" style={{ margin: `0 5px` }}>
              {docSoViet(item.soLuong)}
            </span>
          ) : (
            item.soLuong
          )}
          {item.tenDonViTinh ? ` (${item.tenDonViTinh})` : ""}{" "}
        </>
      );
    };

    const classThuoc = HIEN_THI_KY_HIEU_THUOC_THEO_DOI?.eval()
      ? thuocGayNghien?.includes(item.maPhanLoaiDvKho)
        ? "rounded"
        : thuocHuongThan?.includes(item.maPhanLoaiDvKho)
        ? "square"
        : item.kyHieu
        ? "rhombus"
        : "circle"
      : "circle";

    return (
      <div
        className={`item-drug  ${item?.childs?.length ? "drug" : ""} `}
        key={stringUtils.guid()}
        style={{ position: "relative" }}
      >
        <div style={{ position: "relative" }}>
          {!isSub && showIconDrag && <SVG.IcDrag className="ic-drag" />}
          <div
            key={item.id}
            className={` ml5 ${isSub ? "child" : ""}`}
            style={{ position: "relative" }}
          >
            <div className={`${!isSub ? "b" : ""} flex`}>
              {item?.sttNgaySuDung ? (
                <span
                  className={`${classThuoc} b ${
                    !isSub ? "circle-thuoc-chinh" : "circle-thuoc-di-kem"
                  }`}
                >
                  <div className="stt-ngay-su-dung">{item?.sttNgaySuDung}</div>
                </span>
              ) : null}
              <span>
                {isSub ? "-" : ""} {item.tenDichVu}
                {tenHoatChat_HamLuong()}
                {item.boSung ? `(${t("editor.boSung")})` : ""}{" "}
                {item.thuocDaKe ? "(ĐK)" : ""}
                {item.dungChoCon ? itemProps.kyTuCon || `(CON)` : ""}
                {[50, 55].includes(item.loai)
                  ? itemProps.kyTuThuocNhaThuoc || `(NT)`
                  : item.thuocKeNgoai
                  ? itemProps.kyTuThuocKeNgoai || `(TT)`
                  : " "}
                {soLuong_Dvt()}
                <i style={{ fontWeight: "normal", marginLeft: 2 }}>
                  {item.soLuongHuy
                    ? `  (${t("common.huy")} ${item.soLuongHuy} ${
                        item.tenDonViTinh || ""
                      }${item.lyDoHuy ? `, ${item.lyDoHuy}` : ""})`
                    : ""}
                </i>
                {hienLoaiChiDinh && item.loaiChiDinh
                  ? `(${
                      listLoaiChiDinh.find((x) => x.id === item.loaiChiDinh)
                        ?.ten || ""
                    })`
                  : ""}
              </span>
            </div>
            {(!TO_DIEU_TRI_NOI_TRU_CHI_HIEN_THI_CACH_DUNG_THUOC_CHA?.eval() ||
              maBaoCao !== "EMR_BA077") && (
              <div className="i ">
                <div>
                  {`${item.tenLieuDung || ""} ${
                    item.cachDung ? `${item.cachDung}` : ""
                  } ${
                    textBuoiDung
                      ? `(${textBuoiDung}${
                          item.thoiDiem ? ` ${item.thoiDiem}` : ""
                        })`
                      : ""
                  }`}
                  <span>
                    {!!item.ghiChu && `.${t("editor.ghiChu")}: ${item.ghiChu}`}
                  </span>
                </div>
              </div>
            )}
          </div>
          {item?.childs?.length ? (
            <>
              {item.childs.map((e) => (
                <RenderThuocs
                  key={stringUtils.guid()}
                  item={e}
                  isSub={true}
                  listDonViTocDoTruyen={listDonViTocDoTruyen}
                  showDuongDungThuoc={showDuongDungThuoc}
                  hienSoLuongDvt={hienSoLuongDvt}
                  maBaoCao={maBaoCao}
                  itemProps={itemProps}
                ></RenderThuocs>
              ))}
            </>
          ) : null}
          {TO_DIEU_TRI_NOI_TRU_CHI_HIEN_THI_CACH_DUNG_THUOC_CHA?.eval() &&
            maBaoCao === "EMR_BA077" &&
            !isSub && (
              <div className="i ">
                <div>
                  {`${item.tenLieuDung || ""} ${
                    item.cachDung ? `${item.cachDung}` : ""
                  } ${
                    textBuoiDung
                      ? `(${textBuoiDung}${
                          item.thoiDiem ? ` ${item.thoiDiem}` : ""
                        })`
                      : ""
                  }`}
                  <span>
                    {!!item.ghiChu && `.${t("editor.ghiChu")}: ${item.ghiChu}`}
                  </span>
                </div>
              </div>
            )}
        </div>
      </div>
    );
  }
);

export const RenderPhaChung = memo(
  ({
    item,
    isSub,
    listDonViTocDoTruyen,
    showDuongDungThuoc = true,
    hienSoLuongDvt = true,
    showIconDrag = true,
    listLoaiChiDinh = [],
    hienLoaiChiDinh = false,
    onDragEnd = () => {},
    hienThiSLBuoi,
    hienThiTocDoTruyen,
    hienThiDonViTocDoTruyen,
    maBaoCao,
    itemProps,
  }) => {
    const renderDraggable = useDraggableInPortal(
      "to-dieu-tri to-dieu-tri-drag"
    );
    return (
      <div
        key={stringUtils.guid()}
        className={`overlay item-pha-chung ${
          item?.phaChungChilds?.length > 1 ? "drug" : ""
        }`}
        style={{ position: "relative" }}
      >
        <div style={{ position: "relative" }}>
          {(item.phaChungChilds || []).length > 1 && showIconDrag && (
            <SVG.IcDrag className="ic-drag-pha-chung" />
          )}

          <DragDropContext onDragEnd={onDragEnd}>
            <Droppable droppableId={"droppable2"}>
              {(provided, snapshot) => (
                <div {...provided.droppableProps} ref={provided.innerRef}>
                  {(item.phaChungChilds || []).map((item2, index2) => (
                    <Draggable
                      key={item2.id + ""}
                      draggableId={item2.id + ""}
                      index={index2}
                    >
                      {renderDraggable((provided) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          {...provided.dragHandleProps}
                        >
                          <RenderThuocs
                            item={item2}
                            listDonViTocDoTruyen={listDonViTocDoTruyen}
                            showDuongDungThuoc={showDuongDungThuoc}
                            hienSoLuongDvt={hienSoLuongDvt}
                            listLoaiChiDinh={listLoaiChiDinh}
                            hienLoaiChiDinh={hienLoaiChiDinh}
                            hienThiSLBuoi={hienThiSLBuoi}
                            hienThiTocDoTruyen={hienThiTocDoTruyen}
                            hienThiDonViTocDoTruyen={hienThiDonViTocDoTruyen}
                            maBaoCao={maBaoCao}
                            itemProps={itemProps}
                          />
                        </div>
                      ))}
                    </Draggable>
                  ))}

                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
        </div>
      </div>
    );
  }
);

export const YLenh = ({
  data,
  disable,
  listDonViTocDoTruyen = [],
  onFormChange,
  showDuongDungThuoc,
  hienSoLuongDvt,
  hienThuocNhaThuoc,
  hienThuocKeNgoai,
  hienThuocTuVan,
  listLoaiChiDinh = [],
  hienLoaiChiDinh,
  hienThiSLBuoi,
  hienThiTocDoTruyen,
  hienThiDonViTocDoTruyen,
  maBaoCao,
  itemProps,
}) => {
  const renderDraggable = useDraggableInPortal("to-dieu-tri to-dieu-tri-drag");
  const [listThuoc, setListThuoc] = useState([]);
  const refThuTu = useRef();
  useEffect(() => {
    const ngayDieuTri = data ? cloneDeep(data) : {};
    let thuocs = [];
    (ngayDieuTri.dsThuocDaChiDinh || []).forEach((thuoc) => {
      thuoc.thuocDaKe = true;
      thuoc.thuocKeNgoai = false;
    });

    (ngayDieuTri.dsThuoc || []).forEach((thuoc) => {
      thuoc.thuocDaKe = false;
      thuoc.thuocKeNgoai = false;
    });
    (ngayDieuTri.dsThuocChiDinhNgoai || []).forEach((thuoc) => {
      thuoc.thuocDaKe = false;
      thuoc.thuocKeNgoai = true;
      thuoc.tenDichVu = thuoc.tenThuocChiDinhNgoai;
    });
    (ngayDieuTri.dsVacXin || []).forEach((thuoc) => {
      thuoc.thuocKeNgoai = false;
    });
    thuocs = [
      ...(ngayDieuTri.dsThuocDaChiDinh || []),
      ...(ngayDieuTri.dsThuoc || []),
      ...(ngayDieuTri.dsThuocChiDinhNgoai || []),
      ...(ngayDieuTri.dsVacXin || []),
    ];
    thuocs = gopThuocKhacLo(thuocs);

    for (let index = 0; index < thuocs.length; index++) {
      const item = thuocs[index];
      const listParent = thuocs.filter((item1) => {
        return (
          item1.id == item.dungKemId ||
          (item1.dsThuocGopId || []).includes(item.dungKemId)
        );
      });
      if (listParent.length) {
        listParent.forEach((item2) => {
          if (!item2.childs) item2.childs = [];
          item2.childs.push(item);
        });
        thuocs.splice(index, 1);
        index--;
      }
    }
    const listThuocsSortDuongDung = sortThuocDuongDung(thuocs);
    const thuocNotSttDuongDung = thuocs
      .filter((thuoc) => !thuoc.sttDuongDung)
      .sort(
        (a, b) =>
          new Date(a.thoiGianChiDinh).getTime() -
          new Date(b.thoiGianChiDinh).getTime()
      );
    thuocs = [...listThuocsSortDuongDung, ...thuocNotSttDuongDung];
    const thuocDaKe = sortThuoc(thuocs.filter((item) => item.thuocDaKe));
    const thuocTrongKho = sortThuoc(
      thuocs.filter(
        (item) =>
          !item.thuocKeNgoai && !item.thuocDaKe && ![50, 55].includes(item.loai)
      )
    );
    const thuocNhaThuoc = sortThuoc(
      thuocs.filter(
        (item) =>
          !item.thuocKeNgoai && !item.thuocDaKe && [50, 55].includes(item.loai)
      )
    );
    const thuocKeNgoai = sortThuoc(thuocs.filter((item) => item.thuocKeNgoai));
    thuocs = [
      ...thuocDaKe,
      ...thuocTrongKho,
      ...(hienThuocNhaThuoc ? thuocNhaThuoc : []),
      ...(hienThuocKeNgoai ? thuocKeNgoai : []),
    ];

    if (!hienThuocTuVan) {
      //lọc ẩn thuốc đơn tư vấn (40)
      thuocs = thuocs.filter((item) => item.loaiDonThuoc !== 40);
    }
    thuocs = thuocs.sort((a, b) => {
      return a.dungChoCon === b.dungChoCon ? 0 : a.dungChoCon ? 1 : -1;
    });
    if (thuocs.some((thuoc) => thuoc.sttHienThi)) {
      thuocs = thuocs.sort((a, b) => a.sttHienThi - b.sttHienThi);
    }
    setListThuoc(thuocs);
  }, [data]);

  if (listThuoc.length) {
    const listThuocWithIndex = listThuoc.map((item, index) => ({
      ...item,
      index,
    }));
    let listThuocGroupByPhaChung = listThuocWithIndex
      .filter((item) => item.dsPhaChungId)
      .map((item) => ({ ...item, phaChungChilds: [item] }));
    listThuocWithIndex
      .filter((item) => !item.dsPhaChungId)
      .forEach((element) => {
        const _findIdx = listThuocGroupByPhaChung.findIndex((item) =>
          (item.dsPhaChungId || []).includes(element.id)
        );

        if (_findIdx > -1) {
          listThuocGroupByPhaChung[_findIdx].phaChungChilds = [
            ...(listThuocGroupByPhaChung[_findIdx].phaChungChilds || []),
            element,
          ];
        } else {
          listThuocGroupByPhaChung.push({
            ...element,
            phaChungChilds: [element],
          });
        }
      });
    listThuocGroupByPhaChung = orderBy(
      listThuocGroupByPhaChung,
      ["index"],
      ["asc"]
    );

    const onDragEnd = (result) => {
      try {
        if (!result.destination) {
          return;
        }

        const items = reorder(
          listThuocGroupByPhaChung,
          result.source.index,
          result.destination.index
        );

        let thuocItems = [];

        items.forEach((item) => {
          if (item.phaChungChilds.length > 1) {
            item.phaChungChilds.forEach((item2) => {
              thuocItems.push(item2);
            });
          } else {
            thuocItems.push(item);
          }
        });

        refThuTu.current = thuocItems.map((item, index) => ({
          id: item.id,
          sttHienThi: index,
          thuoChiDinhNgoai: item.thuocChiDinhNgoaiId ? true : false,
        }));
        onFormChange("sttHienThi", data.idGuid)(refThuTu.current);
        setListThuoc(thuocItems);
      } catch (error) {
        console.log(error);
      }
    };
    const onDragEndPhaChung = (index) => (result) => {
      try {
        if (!result.destination) {
          return;
        }

        const items = reorderPhaChung(
          listThuoc,
          result.source.index + index,
          result.destination.index + index
        );

        refThuTu.current = items.map((item, index) => ({
          id: item.id,
          sttHienThi: index,
          thuoChiDinhNgoai: item.thuocChiDinhNgoaiId ? true : false,
        }));
        onFormChange("sttHienThi", data.idGuid)(refThuTu.current);
        setListThuoc(items);
      } catch (error) {
        console.log(error);
      }
    };
    const reorder = (list, startIndex, endIndex) => {
      const result = Array.from(list);
      const [removed] = result.splice(startIndex, 1);
      result.splice(endIndex, 0, removed);
      return result;
    };
    const reorderPhaChung = (list, startIndex, endIndex) => {
      const result = Array.from(list);
      const [removed] = result.splice(startIndex, 1);
      result.splice(endIndex, 0, removed);
      return result;
    };

    return (
      <div className="overlay">
        <DragDropContext onDragEnd={onDragEnd}>
          <Droppable droppableId="droppable">
            {(provided, snapshot) => (
              <div {...provided.droppableProps} ref={provided.innerRef}>
                {listThuocGroupByPhaChung.map((item, index) => (
                  <Draggable
                    key={item.id + ""}
                    draggableId={item.id + ""}
                    index={index}
                  >
                    {renderDraggable((provided) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        {...provided.dragHandleProps}
                      >
                        <RenderPhaChung
                          item={item}
                          listDonViTocDoTruyen={listDonViTocDoTruyen}
                          showDuongDungThuoc={showDuongDungThuoc}
                          hienSoLuongDvt={hienSoLuongDvt}
                          listLoaiChiDinh={listLoaiChiDinh}
                          hienLoaiChiDinh={hienLoaiChiDinh}
                          onDragEnd={onDragEndPhaChung(index)}
                          hienThiSLBuoi={hienThiSLBuoi}
                          hienThiTocDoTruyen={hienThiTocDoTruyen}
                          hienThiDonViTocDoTruyen={hienThiDonViTocDoTruyen}
                          maBaoCao={maBaoCao}
                          itemProps={itemProps}
                        />
                      </div>
                    ))}
                  </Draggable>
                ))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      </div>
    );
  } else {
    return <></>;
  }
};

export const findClosestChiSoSong = (
  targetDate,
  dateArray,
  timeField = "thoiGianThucHien"
) => {
  if (dateArray.length === 0) return {};

  const target = new Date(targetDate);

  return dateArray.reduce((closest, current) => {
    const currentDate = new Date(current[timeField]);
    const closestDate = new Date(closest[timeField]);

    return Math.abs(currentDate - target) < Math.abs(closestDate - target)
      ? current
      : closest;
  });
};

export const renderThuocNgoaiTru = ({ dsThuoc }) => {};
