import React, { useEffect, useMemo, useState } from "react";
import { TextField, SelectLargeData, Select } from "components";
import { SelectGroup } from "pages/khamBenh/KhamCoBan/styled";
import { t } from "i18next";
import { DeboundInput } from "components/editor/config";
import DropDownList from "../../DropDownList";
import { useListAll } from "hooks";
const { SelectChanDoan } = SelectLargeData;

const ChanDoanComponent = ({
  field,
  ngayDieuTri,
  onChange,
  isDisable = false,
}) => {
  const [value, setValue] = useState(null);
  const [listAllMaBenh] = useListAll("maBenh", {}, true);
  const listData = useMemo(() => {
    return listAllMaBenh.map((item) => {
      return {
        label: item.ten,
        value: item.id,
      };
    });
  }, [listAllMaBenh]);
  useEffect(() => {
    if (ngayDieuTri[field]) {
      setValue(ngayDieuTri[field]);
    }
  }, [ngayDieuTri?.[field]]);

  return (
    <DropDownList
      component={{
        props: {
          dataIsArray: true,
          type: field === "dsCdChinhId" ? "onlyOne" : "multiple",
          checkList: listData,
          fieldName: field,
          listType: 2,
          minHeight: "10",
          fontSize: "12",
          lineHeight: "1.5",
          markSpanRow: false,
          readOnly: isDisable,
          label:
            field === "dsCdChinhId"
              ? t("common.chanDoan") + ": &nbsp"
              : t("khamBenh.chanDoan.chanDoanKemTheo") + ": &nbsp",
        },
      }}
      blockWidth="150px"
      className={"drop-list"}
      form={{
        [field]: value,
      }}
      formChange={{
        [field]: (value) => {
          onChange(field, ngayDieuTri)(value);
        },
      }}
    />
  );
};

const renderDienBienNgoaiTru = ({
  key,
  ngayDieuTri,
  onChange,
  listTheBenhLao = [],
  isDisable = false,
  isReadOnly,
  index,
  toDieuTriId,
  isEditDienBien,
}) => {
  switch (key) {
    case "cdSoBo":
      return (
        <DeboundInput
          label={`${t("khamBenh.chanDoan.chanDoanSoBo")}: `}
          value={ngayDieuTri.cdSoBo}
          onChange={onChange(
            "cdSoBo",
            ngayDieuTri,
            index,
            toDieuTriId,
            isEditDienBien
          )}
          type="multipleline"
          lineHeightText={1.5}
          fontSize={9}
          minHeight={9 + 6}
          size={2000}
          readOnly={isDisable || isReadOnly}
          disabled={isDisable || isReadOnly}
        />
      );
    case "dsCdChinhId":
    case "dsCdKemTheoId":
      return (
        <ChanDoanComponent
          field={key}
          ngayDieuTri={ngayDieuTri}
          onChange={onChange}
          isDisable={isDisable || isReadOnly}
        />
      );
    case "moTa":
      return (
        <DeboundInput
          label={`${t("khamBenh.chanDoan.moTaChiTiet")}: `}
          value={ngayDieuTri.moTa}
          onChange={onChange(
            "moTa",
            ngayDieuTri,
            index,
            toDieuTriId,
            isEditDienBien
          )}
          type="multipleline"
          lineHeightText={1.5}
          fontSize={9}
          minHeight={9 + 6}
          size={2000}
          readOnly={isDisable || isReadOnly}
          disabled={isDisable || isReadOnly}
        />
      );
    case "theBenhLao":
      return (
        <DropDownList
          component={{
            props: {
              type: "onlyOne",
              checkList: listTheBenhLao.map((item) => ({
                label: item.ten,
                value: item.id,
              })),
              fieldName: "listTheBenhLao",
              listType: 2,
              minHeight: "10",
              fontSize: "12",
              lineHeight: "1.5",
              markSpanRow: false,
              label: `${t("khamBenh.chanDoan.theBenh")}: &nbsp`,
              labelValue: `${t("khamBenh.chanDoan.theBenh")}: &nbsp`,
              readOnly: isDisable || isReadOnly,
            },
          }}
          blockWidth="150px"
          className={"drop-list"}
          form={{
            listTheBenhLao: ngayDieuTri?.theBenhLao,
          }}
          formChange={{
            listTheBenhLao: (value) => {
              onChange(
                "theBenhLao",
                ngayDieuTri,
                index,
                toDieuTriId,
                isEditDienBien
              )(value);
            },
          }}
        />
      );
    case "quaTrinhBenhLy":
      return (
        <DeboundInput
          label={`${t("khamBenh.hoiBenh.quaTrinhBenhLy")}: `}
          value={ngayDieuTri?.quaTrinhBenhLy}
          onChange={onChange(
            "quaTrinhBenhLy",
            ngayDieuTri,
            index,
            toDieuTriId,
            isEditDienBien
          )}
          type="multipleline"
          lineHeightText={1.5}
          fontSize={9}
          minHeight={9 + 6}
          size={2000}
          readOnly={isDisable || isReadOnly}
          disabled={isDisable || isReadOnly}
        />
      );
    case "tienSuBanThan":
      return (
        <DeboundInput
          label={`${t("khamBenh.hoiBenh.banThan")}: `}
          value={ngayDieuTri?.tienSuBanThan}
          onChange={onChange(
            "tienSuBanThan",
            ngayDieuTri,
            index,
            toDieuTriId,
            isEditDienBien
          )}
          type="multipleline"
          lineHeightText={1.5}
          fontSize={9}
          minHeight={9 + 6}
          size={2000}
          readOnly={isDisable || isReadOnly}
          disabled={isDisable || isReadOnly}
        />
      );
    case "tienSuGiaDinh":
      return (
        <DeboundInput
          label={`${t("khamBenh.hoiBenh.giaDinh")}: `}
          value={ngayDieuTri?.tienSuGiaDinh}
          onChange={onChange(
            "tienSuGiaDinh",
            ngayDieuTri,
            index,
            toDieuTriId,
            isEditDienBien
          )}
          type="multipleline"
          lineHeightText={1.5}
          fontSize={9}
          minHeight={9 + 6}
          size={2000}
          readOnly={isDisable || isReadOnly}
          disabled={isDisable || isReadOnly}
        />
      );
    case "diUngThuoc":
      return (
        // <TextField
        //   label={`${t("khamBenh.hoiBenh.diUngThuoc")}`}
        //   maxLength={2000}
        //   html={ngayDieuTri?.diUngThuoc}
        //   onChange={onChange("diUngThuoc", ngayDieuTri)}
        // />
        <DeboundInput
          label={`${t("khamBenh.hoiBenh.diUngThuoc")}: `}
          value={ngayDieuTri?.diUngThuoc}
          onChange={onChange(
            "diUngThuoc",
            ngayDieuTri,
            index,
            toDieuTriId,
            isEditDienBien
          )}
          type="multipleline"
          lineHeightText={1.5}
          fontSize={9}
          minHeight={9 + 6}
          size={2000}
          readOnly={isDisable || isReadOnly}
          disabled={isDisable || isReadOnly}
        />
      );
    case "toanThan":
      return (
        <DeboundInput
          label={`${t("khamBenh.khamXet.toanThan")}: `}
          value={ngayDieuTri?.toanThan}
          onChange={onChange(
            "toanThan",
            ngayDieuTri,
            index,
            toDieuTriId,
            isEditDienBien
          )}
          type="multipleline"
          lineHeightText={1.5}
          fontSize={9}
          minHeight={9 + 6}
          size={2000}
          readOnly={isDisable || isReadOnly}
          disabled={isDisable || isReadOnly}
        />
      );
    case "cacBoPhan":
      return (
        <DeboundInput
          label={`${t("khamBenh.khamXet.cacBoPhan")}: `}
          value={ngayDieuTri?.cacBoPhan}
          onChange={onChange(
            "cacBoPhan",
            ngayDieuTri,
            index,
            toDieuTriId,
            isEditDienBien
          )}
          type="multipleline"
          lineHeightText={1.5}
          fontSize={9}
          minHeight={9 + 6}
          size={2000}
          readOnly={isDisable || isReadOnly}
          disabled={isDisable || isReadOnly}
        />
      );
    case "ghiChu":
      return (
        <DeboundInput
          label={`${t("khamBenh.khamXet.luuY")}: `}
          value={ngayDieuTri?.ghiChu}
          onChange={onChange(
            "ghiChu",
            ngayDieuTri,
            index,
            toDieuTriId,
            isEditDienBien
          )}
          type="multipleline"
          lineHeightText={1.5}
          fontSize={9}
          minHeight={9 + 6}
          size={2000}
          readOnly={isDisable || isReadOnly}
          disabled={isDisable || isReadOnly}
        />
      );
    case "dienBienBenh":
      return (
        <DeboundInput
          label={`${t("khamBenh.khamXet.dienBien")}: `}
          value={ngayDieuTri?.dienBienBenh}
          onChange={onChange(
            "dienBienBenh",
            ngayDieuTri,
            index,
            toDieuTriId,
            isEditDienBien
          )}
          type="multipleline"
          lineHeightText={1.5}
          fontSize={9}
          minHeight={9 + 6}
          size={4000}
          readOnly={isDisable}
          disabled={isDisable}
        />
      );
    case "giaiDoanBenh":
      return (
        <DeboundInput
          label={`${t("hoiChan.giaiDoanBenh")}: `}
          value={ngayDieuTri?.giaiDoanBenh}
          onChange={onChange(
            "giaiDoanBenh",
            ngayDieuTri,
            index,
            toDieuTriId,
            isEditDienBien
          )}
          type="multipleline"
          lineHeightText={1.5}
          fontSize={9}
          minHeight={9 + 6}
          size={2000}
          readOnly={isDisable || isReadOnly}
          disabled={isDisable || isReadOnly}
        />
      );
    case "nbTomTatCls":
      return (
        <TomTatKetQuaCls
          nbDvKhamTomTatCls={ngayDieuTri?.nbDvKhamTomTatCls}
          isDisable={true} // chỉ xem
        />
      );

    default:
      break;
  }
};

const TomTatKetQuaCls = ({ nbDvKhamTomTatCls, onChange, isDisable }) => {
  return (
    <>
      <b>{t("tenTruong.ketQuaCls")}:</b>
      <div className="label">{`1. ${t("khamBenh.dauTrang.xetNghiem")}:`}</div>
      <DeboundInput
        label={`- ${t("khamBenh.khamSucKhoe.congThucMau")}: `}
        value={nbDvKhamTomTatCls?.congThucMau}
        type="multipleline"
        lineHeightText={1.5}
        fontSize={9}
        minHeight={9 + 6}
        size={2000}
        readOnly={isDisable}
        disabled={isDisable}
      />
      <DeboundInput
        label={`- ${t("khamBenh.sinhHoaMau")}: `}
        value={nbDvKhamTomTatCls?.sinhHoaMau}
        type="multipleline"
        lineHeightText={1.5}
        fontSize={9}
        minHeight={9 + 6}
        size={2000}
        readOnly={isDisable}
        disabled={isDisable}
      />
      <DeboundInput
        label={`- ${t("baoCao.viSinh")}: `}
        value={nbDvKhamTomTatCls?.viSinh}
        type="multipleline"
        lineHeightText={1.5}
        fontSize={9}
        minHeight={9 + 6}
        size={2000}
        readOnly={isDisable}
        disabled={isDisable}
      />
      <DeboundInput
        label={`- ${t("khamBenh.xetNghiemKhac")}: `}
        value={nbDvKhamTomTatCls?.xetNghiemKhac}
        type="multipleline"
        lineHeightText={1.5}
        fontSize={9}
        minHeight={9 + 6}
        size={2000}
        readOnly={isDisable}
        disabled={isDisable}
      />
      <DeboundInput
        label={`- ${t("xetNghiem.giaiPhauBenh")}: `}
        value={nbDvKhamTomTatCls?.giaiPhauBenh}
        type="multipleline"
        lineHeightText={1.5}
        fontSize={9}
        minHeight={9 + 6}
        size={2000}
        readOnly={isDisable}
        disabled={isDisable}
      />
      <div className="label">{`2. ${t(
        "khamBenh.chanDoanHinhAnhSieuAmXquang"
      )}: `}</div>
      <DeboundInput
        label={""}
        value={nbDvKhamTomTatCls?.cdhaTdcn}
        type="multipleline"
        lineHeightText={1.5}
        fontSize={9}
        minHeight={9 + 6}
        size={2000}
        readOnly={isDisable}
        disabled={isDisable}
      />
      <DeboundInput
        label={`3. ${t("khamBenh.khamXet.khac")}: `}
        value={nbDvKhamTomTatCls?.khac}
        type="multipleline"
        lineHeightText={1.5}
        fontSize={9}
        minHeight={9 + 6}
        size={2000}
        readOnly={isDisable}
        disabled={isDisable}
      />
    </>
  );
};

export { renderDienBienNgoaiTru };
