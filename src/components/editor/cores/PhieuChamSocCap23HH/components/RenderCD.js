import React, { Fragment, useEffect, useRef, useState } from "react";
import { DeboundInput } from "components/editor/config";
import { cloneDeep, get } from "lodash";
import { SVG } from "assets";
import { DS_VAN_DE_MUC_TIEU, LIST_VAN_DE_MUC_TIEU } from "../constants";
import { containText, updateObject } from "utils";
import PopoverSelect from "../../Components/PopoverSelect";
import { ARR } from "./RenderTable";
import { Input, Popover } from "antd";
import { PopoverStyled } from "../styled";

const STATUS = {
  ACTIVE: 1,
  INACTIVE: 0,
};

const RenderCD = ({ item, refValue, formChangeValue, data }) => {
  const [value, setValue] = useState([{}]);
  const [listTr, setListTr] = useState(DS_VAN_DE_MUC_TIEU);
  const [listData, setListData] = useState(DS_VAN_DE_MUC_TIEU);
  const [state, _setState] = useState({
    value: [],
    searchText: "",
    open: false,
    curIndex: 0,
  });
  const setState = (data = {}) => {
    _setState((state) => ({ ...state, ...data }));
  };
  useEffect(() => {
    if (Object.keys(data || {}).length) {
      let dataItem = get(data, `dsChiTiet[0].khungChanDoanDd`) || [];
      setValue(dataItem);
    }
    const listData = DS_VAN_DE_MUC_TIEU.map((el) => {
      return {
        ...el,
        status: (refValue.current?.vanDe || []).includes(el.stt)
          ? STATUS.ACTIVE
          : STATUS.INACTIVE,
      };
    });
    setListTr(listData);
  }, [data]);

  const onChangeInput = (key, idx, indexKhung) => (value) => {
    updateObject(
      refValue.current.dsChiTiet[idx]["khungChanDoanDd"][indexKhung],
      key,
      value
    );
    formChangeValue();
  };

  const onClick = (index, data) => () => {
    if (listTr[index]?.status) {
      listTr[index].status = STATUS.INACTIVE;
      (refValue.current.dsChiTiet || []).forEach((el) => {
        el["khungChanDoanDd"] = el["khungChanDoanDd"].filter(
          (item, idx) => item.ctVanDe !== data.vanDe
        );
      });
      setValue(refValue.current.dsChiTiet[0]["khungChanDoanDd"]);
    } else {
      listTr[index].status = STATUS.ACTIVE;
      (refValue.current.dsChiTiet || []).forEach((el) => {
        el["khungChanDoanDd"] = [
          ...(el["khungChanDoanDd"] || []),
          { ctVanDe: data.vanDe, ctMucTieu: data.mucTieu },
        ];
      });
    }
    const listData = listTr.filter((el) => el.status === STATUS.ACTIVE);
    const vanDe = listData.map((el) => el.stt);
    refValue.current["vanDe"] = vanDe;

    setValue(refValue.current.dsChiTiet[0]["khungChanDoanDd"]);
    formChangeValue();
    setListTr(cloneDeep(listTr));
  };
  const handleChangeSearchText = (e) => {
    setState({
      searchText: e.target.value,
      curIndex: 0,
    });
  };

  useEffect(() => {
    if (state.searchText) {
      setListData(
        DS_VAN_DE_MUC_TIEU.filter((item) =>
          containText(item.vanDe, state.searchText)
        )
      );
    } else {
      setListData(DS_VAN_DE_MUC_TIEU);
    }
  }, [state.searchText]);

  return (
    <>
      <tr>
        <td colSpan={3}>
          <b style={{ display: "flex", alignItems: "center" }}>
            {item.label1}
            <Popover
              title={"Danh sách chẩn đoán"}
              content={
                <PopoverStyled>
                  <Input
                    placeholder={"Tìm kiếm"}
                    value={state.searchText}
                    onChange={handleChangeSearchText}
                  />

                  {listData.map((item, index) => {
                    const isActive = listTr.find(
                      (el) => el.stt === item.stt && el.status
                    );
                    return (
                      <div
                        key={item.key}
                        onClick={onClick(index, item)}
                        className={`item ${isActive ? "active" : ""}`}
                      >
                        <div
                          style={{ flex: 1 }}
                        >{`${item.stt} - ${item.vanDe}`}</div>
                        {isActive && <SVG.IcDelete className={"icon-remove"} />}
                      </div>
                    );
                  })}
                </PopoverStyled>
              }
              trigger="click"
            >
              <SVG.IcAdd className="icon-add hide-print-btn" />
            </Popover>
          </b>
        </td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
      </tr>
      {value.map((trItem, indexKhung) => {
        return (
          <Fragment key={indexKhung}>
            <tr>
              <td colSpan={3} style={{ position: "relative" }}>
                <label>
                  CĐĐD/ VẤN ĐỀ:{" "}
                  {get(
                    refValue.current,
                    `dsChiTiet[0].khungChanDoanDd[${indexKhung}].ctVanDe`
                  )}
                </label>
              </td>
              {ARR.map((el, idx) => (
                <td key={idx}>
                  <PopoverSelect
                    data={LIST_VAN_DE_MUC_TIEU}
                    value={get(
                      refValue.current,
                      `dsChiTiet[${idx}].khungChanDoanDd[${indexKhung}].vanDe`
                    )}
                    onChangeValue={(e) => {
                      onChangeInput("vanDe", idx, indexKhung)([e]);
                    }}
                    isMultiple={false}
                    trigger="click"
                    labelByKey={"label"}
                  ></PopoverSelect>
                </td>
              ))}
            </tr>
            <tr>
              <td colSpan={3}>
                <label>
                  MỤC TIÊU:{" "}
                  {get(
                    refValue.current,
                    `dsChiTiet[0].khungChanDoanDd[${indexKhung}].ctMucTieu`
                  )}
                </label>
              </td>
              {ARR.map((el, idx) => (
                <td key={idx}>
                  <PopoverSelect
                    data={LIST_VAN_DE_MUC_TIEU}
                    value={get(
                      refValue.current,
                      `dsChiTiet[${idx}].khungChanDoanDd[${indexKhung}].mucTieu`
                    )}
                    onChangeValue={(e) => {
                      onChangeInput("mucTieu", idx, indexKhung)([e]);
                    }}
                    isMultiple={false}
                    trigger="click"
                    labelByKey={"label"}
                  ></PopoverSelect>
                </td>
              ))}
            </tr>
          </Fragment>
        );
      })}
    </>
  );
};

RenderCD.propTypes = {};

export default RenderCD;
