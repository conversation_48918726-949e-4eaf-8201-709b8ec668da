import React, { useEffect, useState } from "react";
import { SVG } from "assets";
import AppDatePicker from "../../DatePicker";
import PopoverSelect from "../../Components/PopoverSelect";
import { DeboundInput } from "components/editor/config";
import { get } from "lodash";
import ImageSign from "../../ImageSign";
import { combineFields } from "utils/editor-utils";
import CopyPasteCol from "../../Components/CopyPasteCol";
import { ARR, NUM_OF_COLS } from "./RenderTable";

const RenderTr = ({
  item,
  index,
  mode,
  data,
  onChangeInput,
  tableIndex,
  isShowAdd,
  isShowRemove,
  onAddKhung,
  onRemoveKhung,
  onRemove,
  indexKhung,
  isTrMultiple,
  itemProps,
  form,
  hanldePaste,
  handleCopy,
  handleDelete,
  dataCopy,
  onSelectSinhHieu,
  refValue,
  formChangeValue,
  dataKhung,
}) => {
  const [value, setValue] = useState({});
  useEffect(() => {
    setValue(data);
  }, [data]);

  const keySplit = (item?.key || "").split(".");
  let key1, key2;
  key1 = keySplit[0];
  if (keySplit.length > 1) {
    key2 = keySplit[1];
  }

  const onChangeValueChung = (key) => (value) => {
    refValue.current[key] = value;
    formChangeValue();
  };

  return (
    <tr key={`${item.key}_${index}`}>
      {item.label1 && (
        <td
          rowSpan={item.rowSpan1 || 1}
          colSpan={item.colSpan1 || 1}
          style={{
            position: "relative",
            width: 40,
            maxWidth: 40,
            minWidth: 40,
            textAlignc: "center",
          }}
        >
          {(tableIndex && !index) || isShowRemove ? (
            <div>
              <SVG.IcDelete
                className="ic-remove ic-remove-khung"
                onClick={isShowRemove ? onRemoveKhung : onRemove}
              />
            </div>
          ) : null}
          {isShowAdd && (
            <SVG.IcAdd
              onClick={onAddKhung(index)}
              className="btn-add btn-add-khung"
            />
          )}
          <b style={{ display: "flex", alignItems: "center" }}>
            {item.label1}{" "}
            {item.key === "chanDoan" ? (
              <SVG.IcAdd className="icon-add hide-print-btn" />
            ) : (
              ""
            )}
          </b>
        </td>
      )}

      {item.label2 && (
        <td
          rowSpan={item.rowSpan2 || 1}
          colSpan={item.colSpan2 || 1}
          style={{
            width: 100,
            maxWidth: 100,
            minWidth: 100,
          }}
        >
          {item.label2}
        </td>
      )}

      {item.hint && (
        <td rowSpan={item.rowSpan3 || 1} colSpan={item.colSpan3 || 1}>
          <div
            className={`hint ${item.numberItemHint ? "hint-with-number" : ""}`}
          >
            {(item.hint || []).map((item, index) => (
              <span key={index}>{item}</span>
            ))}
          </div>
        </td>
      )}

      {item.hintKhac && (
        <td {...(item.key === "banGiao" ? { colSpan: 3 } : {})}>
          <DeboundInput
            readOnly={false}
            value={get(dataKhung, `${item.hintKhac}`)}
            onChange={onChangeValueChung(item.hintKhac)}
            type="multipleline"
            lineHeightText={1.5}
            fontSize={9}
            minHeight={9 + 6}
            markSpanRow={false}
            contentAlign="center"
          />
        </td>
      )}

      {ARR.map((el, idx) => {
        return item.disabled ? (
          <td key={`${item.key}_${index}_${idx}`}></td>
        ) : item.type == "time" ? (
          <td
            key={`${item.key}_${index}_${idx}`}
            style={{
              width: 70,
              minWidth: 70,
              maxWidth: 70,
              position: "relative",
            }}
            className="col-element"
          >
            {item.key === "thoiGianThucHien" && (
              <CopyPasteCol
                colIndex={idx}
                handlePaste={hanldePaste(idx)}
                handleCopy={handleCopy(idx)}
                dataCopy={dataCopy}
                handleDelete={handleDelete(idx)}
                showSinhHieu={true}
                onSelectSinhHieu={onSelectSinhHieu(idx)}
              />
            )}
            <AppDatePicker
              component={{
                props: {
                  contentAlign: "center",
                  dateTimeFormat:
                    item.key === "thoiGianThucHien" ? "HH:mm D/M/Y" : "D/M/Y",
                  fieldName: "value",
                  // disableOnblur: true,
                  fontSize: 8,
                  allowClear: true,
                },
              }}
              form={{
                value: get(
                  value,
                  isTrMultiple
                    ? `[${idx}][${key1}][${indexKhung}][${key2}]`
                    : `dsChiTiet[${idx}][${key1}]${key2 ? `${key2}` : ""}`
                ),
              }}
              mode={mode}
              formChange={{
                value: (e) => {
                  try {
                    onChangeInput(item.key, idx, indexKhung)(e);
                  } catch (error) {}
                },
              }}
            />
          </td>
        ) : item.type === "droplist" ? (
          <td>
            <PopoverSelect
              data={item.data}
              value={get(
                value,
                isTrMultiple
                  ? `[${idx}][${key1}][${indexKhung}][${key2}]`
                  : `dsChiTiet[${idx}][${key1}]${key2 ? `[${key2}]` : ""}`
              )}
              onChangeValue={(e) => {
                onChangeInput(
                  item.key,
                  idx,
                  indexKhung
                )(item.isArr && item.mode === "onlyOne" ? [e] : e);
              }}
              isMultiple={!(item.mode == "onlyOne")}
              trigger="click"
              labelByKey={item.labelByKey || "value"}
            />
          </td>
        ) : item.type == "sign" ? (
          <td key={`${item.key}_${index}_${idx}`}>
            <ImageSign
              component={{
                props: {
                  ...itemProps,
                  isMultipleSign: true,
                  viTri:
                    (tableIndex * 10 + item.signIndex) * NUM_OF_COLS + idx + 1,
                  customText: "Ký",
                  width: 60,
                  height: 40,
                  dataSign: {
                    id: form.id,
                    soPhieu: form?.lichSuKy?.soPhieu,
                    lichSuKyId: form?.lichSuKy?.id,
                  },
                },
              }}
              form={{
                ...combineFields(form),
              }}
            />
          </td>
        ) : (
          <td key={`${item.key}_${index}_${idx}`}>
            <DeboundInput
              readOnly={false}
              value={get(
                value,
                isTrMultiple
                  ? `[${idx}][${key1}][${indexKhung}][${key2}]`
                  : `dsChiTiet[${idx}][${key1}]${key2 ? `[${key2}]` : ""}`
              )}
              onChange={onChangeInput(item.key, idx, indexKhung)}
              type="multipleline"
              lineHeightText={1.5}
              fontSize={9}
              minHeight={9 + 6}
              markSpanRow={false}
              contentAlign="center"
            />
          </td>
        );
      })}
    </tr>
  );
};

RenderTr.propTypes = {};

export default RenderTr;
