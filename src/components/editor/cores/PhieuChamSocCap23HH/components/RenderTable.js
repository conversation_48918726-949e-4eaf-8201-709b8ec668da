import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { TR } from "../constants";
import { cloneDeep, get } from "lodash";
import { updateObject } from "utils";
import { message } from "antd";
import RenderTr from "./RenderTr";
import RenderCD from "./RenderCD";
import ListTrMultiple from "./ListTrMultiple";
import moment from "moment";
import HeaderFormChamSocC2CR from "../../Components/HeaderFormChamSocC2CR";
import { refConfirm } from "app";
import { useQueryString } from "hooks";
import { useTranslation } from "react-i18next";
import nbChiSoSongProvider from "data-access/nb-chi-so-song-provider";

export const NUM_OF_COLS = 5;
export const ARR = new Array(NUM_OF_COLS).fill({});

const RenderTable = ({
  item,
  mode,
  tableIndex,
  refValueForm,
  formChange,
  onRemove,
  form,
  itemProps,
  dataCopy,
  setDataCopy,
  refModalSinhHieu,
}) => {
  const { t } = useTranslation();
  const [nbDotDieuTriId] = useQueryString("nbDotDieuTriId");
  const [khoaChiDinhId] = useQueryString("khoaChiDinhId");

  const [state, _setState] = useState({
    value: {},
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const refValue = useRef();
  useEffect(() => {
    refValue.current = item;
    setState({
      value: item || {},
    });
  }, [item]);

  const onChangeHuyetAp = (index) => (value) => {
    if (!refValue.current.dsChiTiet[index]["chiSoSong"])
      refValue.current.dsChiTiet[index]["chiSoSong"] = {};
    if (!value) {
      updateObject(
        refValue.current.dsChiTiet[index]["chiSoSong"],
        `huyetApTamThu`,
        ""
      );
      updateObject(
        refValue.current.dsChiTiet[index]["chiSoSong"],
        `huyetApTamTruong`,
        ""
      );
    } else {
      if (value.split("/").length - 1 !== 1) {
        message.error("Nhập sai quy tắc. Nhập đúng ví dụ: 120/90 ");
        return;
      }
      const arr = value.split("/");
      const huyetApTamThu = +arr[0];
      const huyetApTamTruong = +arr[1];
      if (huyetApTamThu < huyetApTamTruong) {
        message.error("Huyết áp tâm thu cần lớn hơn huyết áp tâm trương");
      } else {
        updateObject(
          refValue.current.dsChiTiet[index]["chiSoSong"],
          `huyetApTamThu`,
          huyetApTamThu
        );
        updateObject(
          refValue.current.dsChiTiet[index]["chiSoSong"],
          `huyetApTamTruong`,
          huyetApTamTruong
        );
      }
    }
  };

  const onChangeVE = (index) => (value) => {
    const arr = value.split("/");
    const ve = +arr[0];

    updateObject(refValue.current["dsChiTiet"][index], `ve`, ve);
    if (arr.length > 1) {
      const vh = +arr[1];
      updateObject(refValue.current["dsChiTiet"][index], `vh`, vh);
    }
  };

  const onChangeInput = useCallback(
    (key, idx) => (value) => {
      const keySplit = (key || "").split(".");
      let key1, key2;
      key1 = keySplit[0];
      if (keySplit.length > 1) {
        key2 = keySplit[1];
      }
      if (
        !key.includes("thoiGianThucHien") &&
        !refValue.current.dsChiTiet[idx]?.thoiGianThucHien
      ) {
        message.error("Vui lòng nhập thời gian thực hiện!");
      }
      if (key == "huyetApTrungBinh") {
        updateObject(refValue.current.dsChiTiet[idx], key1, value);
      } else if (key.includes("huyetAp")) {
        onChangeHuyetAp(idx)(value);
      } else {
        if (!key2) {
          updateObject(refValue.current.dsChiTiet[idx], key1, value);
        } else {
          if (!refValue.current.dsChiTiet[idx][key1]) {
            if (key1.includes("chiSoSong")) {
              refValue.current.dsChiTiet[idx][key1] = {
                chiDinhTuLoaiDichVu: 201,
                khoaChiDinhId: form.khoaChiDinhId,
                nbDotDieuTriId: form.nbDotDieuTriId,
              };
            } else {
              refValue.current.dsChiTiet[idx][key1] = {};
            }
          }
          if (get(refValue.current.dsChiTiet[idx], `${key1}`)) {
            updateObject(
              refValue.current.dsChiTiet[idx][key1],
              `${key2}`,
              value
            );
          } else {
            refValue.current.dsChiTiet[idx][key1] = {};
            updateObject(
              refValue.current.dsChiTiet[idx][key1],
              `${key2}`,
              value
            );
          }
        }
      }
      if (key === "thoiGianThucHien" || key === "") {
        setState({
          value: cloneDeep(refValue.current),
        });
      }
      if (
        key === "khChamSocDieuDuong" &&
        value.includes(5) &&
        !refValue.current.dsChiTiet[idx]["ngayDatOngThong"]
      ) {
        refValue.current.dsChiTiet[idx]["ngayDatOngThong"] = moment().format();
        setState({
          value: cloneDeep(refValue.current),
        });
      }

      if (key === "khChamSocDieuDuong" && value.includes(20)) {
        refValue.current.dsChiTiet[idx]["ngayRutOngThong"] = moment().format();
        setState({
          value: cloneDeep(refValue.current),
        });
      }
      // if (["chiSoSong.canNang", "chiSoSong.chieuCao"].includes(key)) {
      //   const { canNang, chieuCao } = get(
      //     refValue.current,
      //     `dsChiTiet[${idx}].chiSoSong`,
      //     {}
      //   );
      //   if (canNang && chieuCao) {
      //     const bmi = canNang / ((chieuCao / 100) * (chieuCao / 100));
      //     refValue.current["dsChiTiet"][idx]["chiSoSong"]["bmi"] =
      //       bmi.toFixed(2);
      //     refBmi.current.setValue(refValue.current);
      //   }
      // }

      formChangeValue();
    },
    [refValueForm, form, Object.keys(formChange || {}).length]
  );

  const formChangeValue = () => {
    refValueForm[tableIndex] = refValue.current;
    formChange["dsTheoDoi"] && formChange["dsTheoDoi"](refValueForm);
  };

  const onChangeValueChung = useCallback(
    (key) => (value) => {
      formChange[key](value);
    },
    [Object.keys(formChange || {}).length]
  );

  const handleCopy = (idx) => () => {
    const dataCopy = cloneDeep(refValue.current.dsChiTiet[idx]);
    if (dataCopy.chiSoSong) {
      delete dataCopy.chiSoSong.id;
      delete dataCopy.chiSoSong.createdAt;
      delete dataCopy.chiSoSong.createdBy;
      delete dataCopy.chiSoSong.nguoiThucHienId;
      delete dataCopy.chiSoSong.updatedAt;
      delete dataCopy.chiSoSong.updatedBy;
    }
    setDataCopy({
      col: idx,
      data: dataCopy,
    });
    message.success("Copy dữ liệu thành công!");
  };

  const hanldePaste = (idx) => () => {
    refValue.current.dsChiTiet[idx] = cloneDeep(dataCopy.data);
    setState({
      value: cloneDeep(refValue.current),
    });
    formChangeValue();
  };

  const handleDelete = (idx) => () => {
    refConfirm.current &&
      refConfirm.current.show(
        {
          title: t("common.thongBao"),
          content: `${t("common.banCoChacMuonXoa")} cột ${idx + 1}`,
          cancelText: t("common.huy"),
          okText: t("common.dongY"),
          classNameOkText: "button-warning",
          showBtnOk: true,
          typeModal: "warning",
        },
        async () => {
          try {
            if (refValue.current.dsChiTiet[idx]?.chiSoSong?.id) {
              await nbChiSoSongProvider.onDelete(
                refValue.current.dsChiTiet[idx]?.chiSoSong?.id
              );
            }
            refValue.current.dsChiTiet[idx] = {};
            setState({
              value: cloneDeep(refValue.current),
            });
            formChangeValue();
          } catch (error) {
            message.error(error?.message || t("kiosk.coLoiXayRaVuiLongThuLai"));
          }
        }
      );
  };

  const onSelectSinhHieu = (idx) => () => {
    refModalSinhHieu?.current &&
      refModalSinhHieu?.current.show(
        {
          nbDotDieuTriId,
          khoaChiDinhId,
        },
        (sinhHieu) => {
          const ngayThucHien = sinhHieu.thoiGianThucHien
            ? moment(sinhHieu.thoiGianThucHien).format("YYYY-MM-DD")
            : "";
          const thoiGian = sinhHieu.thoiGianThucHien
            ? moment(sinhHieu.thoiGianThucHien).format("HH:mm:ss")
            : "";
          const allChiTiet = refValueForm
            .map((el, index) => {
              return el.dsChiTiet;
            })
            .flat(Infinity);
          let chiTietOld = allChiTiet.find(
            (el) =>
              el.chiSoSong?.thoiGianThucHien &&
              moment(el.chiSoSong?.thoiGianThucHien).format(
                "DD/MM/YYYY HH:mm"
              ) === moment(sinhHieu.thoiGianThucHien).format("DD/MM/YYYY HH:mm")
          );
          if (chiTietOld) {
            message.error(
              t("editor.nbDaTonTaiSinhHieu", {
                time: moment(chiTietOld.chiSoSong.thoiGianThucHien).format(
                  "DD/MM/YYYY HH:mm:ss"
                ),
              })
            );
          } else {
            refValue.current.dsChiTiet[idx]["chiSoSong"] = sinhHieu;
            refValue.current.dsChiTiet[idx].ngayThucHien = ngayThucHien;
            refValue.current.dsChiTiet[idx].thoiGian = thoiGian;
            refValue.current.dsChiTiet[idx].thoiGianThucHien = moment(
              sinhHieu.thoiGianThucHien
            ).format("YYYY-MM-DD HH:mm:00");
            setState({
              value: cloneDeep(refValue.current),
            });
            formChange["dsTheoDoi"] && formChange["dsTheoDoi"](refValueForm);
          }
        }
      );
  };

  const render = useMemo(() => {
    return (
      <>
        <HeaderFormChamSocC2CR
          form={form}
          mode={mode}
          formChange={formChange}
          tableIndex={tableIndex}
        />
        <table style={{ marginTop: 20 }}>
          <tbody>
            {TR.map((item, index) => {
              if (
                [
                  "khungDau",
                  "khungVetThuong",
                  "khungDanLuu",
                  "khungOngThong",
                ].includes(item.key)
              ) {
                return (
                  <ListTrMultiple
                    item={item}
                    index={index}
                    mode={mode}
                    onChangeInput={onChangeInput}
                    key={index}
                    value={state.value}
                    tableIndex={tableIndex}
                    listTr={item.listTr}
                    refValue={refValue}
                    formChangeValue={formChangeValue}
                  />
                );
              } else if (item.key == "chanDoanDieuDuong") {
                return (
                  <RenderCD
                    item={item}
                    index={index}
                    mode={mode}
                    onChangeInput={onChangeInput}
                    key={index}
                    data={state.value}
                    tableIndex={tableIndex}
                    listTr={item.listTr}
                    refValue={refValue}
                    formChangeValue={formChangeValue}
                  />
                );
              } else {
                return (
                  <RenderTr
                    item={item}
                    index={index}
                    mode={mode}
                    onChangeInput={onChangeInput}
                    key={index}
                    data={state.value}
                    tableIndex={tableIndex}
                    onRemove={onRemove(tableIndex)}
                    itemProps={itemProps}
                    form={form}
                    hanldePaste={hanldePaste}
                    handleCopy={handleCopy}
                    handleDelete={handleDelete}
                    dataCopy={dataCopy}
                    onSelectSinhHieu={onSelectSinhHieu}
                    refValue={refValue}
                    formChangeValue={formChangeValue}
                    dataKhung={state.value}
                  />
                );
              }
            })}
          </tbody>
        </table>
      </>
    );
  }, [state.value, onChangeValueChung, refValueForm, dataCopy]);
  return render;
};

export default RenderTable;
