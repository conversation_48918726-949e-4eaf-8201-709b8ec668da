import React, { useCallback, useEffect, useRef, useState } from "react";
import RenderTr from "./RenderTr";
import { cloneDeep, get } from "lodash";
import { refConfirm } from "app";
import { updateObject } from "utils";
import { t } from "i18next";

const ListTrMultiple = ({
  listTr,
  item,
  mode,
  value,
  refTr,
  tableIndex,
  refValue,
  formChangeValue,
}) => {
  const refData = useRef();
  const [data, setData] = useState([{}]);

  useEffect(() => {
    if (Object.keys(value || {}).length) {
      const dataItem = get(value, `dsChiTiet[0]${item.key}`) || [cloneDeep({})];
      setData(dataItem);
      refData.current = dataItem;
    }
  }, [value]);

  const onAddKhung = useCallback(
    () => () => {
      refValue.current.dsChiTiet.forEach((el) => {
        el[item.key] = [...(el[item.key] || []), cloneDeep({})];
      });
      setData(refValue.current.dsChiTiet[0][item.key]);
      formChangeValue();
    },
    [formChangeValue]
  );

  const onRemoveKhung = useCallback(
    (indexKhung) => () => {
      refConfirm.current &&
        refConfirm.current.show(
          {
            title: t("common.thongBao"),
            content: `${t("common.banCoChacMuonXoa")}`,
            cancelText: t("common.huy"),
            okText: t("common.dongY"),
            classNameOkText: "button-warning",
            showBtnOk: true,
            typeModal: "warning",
          },
          () => {
            refValue.current.dsChiTiet.forEach((el) => {
              el[item.key] = el[item.key].filter(
                (e, idx) => idx !== indexKhung
              );
            });
            setData(refValue.current.dsChiTiet[0][item.key]);
            formChangeValue();
          }
        );
    },
    [data]
  );

  const onChange = (key, idx, khungIndex) => (value) => {
    const keySplit = (key || "").split(".");
    let key1, key2;
    key1 = keySplit[0];
    if (keySplit.length > 1) {
      key2 = keySplit[1];
    }
    if (!refValue.current.dsChiTiet[idx][item.key]) {
      refValue.current.dsChiTiet[idx][item.key] = [cloneDeep({})];
    }
    updateObject(
      refValue.current.dsChiTiet[idx][item.key][khungIndex],
      key2,
      value
    );
    formChangeValue();
  };

  return data.map((trItem, indexKhung) => {
    return listTr.map((el, idx) => {
      return (
        <RenderTr
          refTr={refTr}
          item={el}
          index={idx}
          mode={mode}
          onChangeInput={onChange}
          key={idx}
          data={get(refValue.current, `dsChiTiet`)}
          tableIndex={tableIndex}
          isShowAdd={indexKhung === data.length - 1}
          isShowRemove={indexKhung}
          onAddKhung={onAddKhung}
          onRemoveKhung={onRemoveKhung(indexKhung)}
          indexKhung={indexKhung}
          isTrMultiple={true}
          refValue={refValue}
          formChangeValue={formChangeValue}
          dataKhung={value}
        />
      );
    });
  });
};

ListTrMultiple.propTypes = {};

export default ListTrMultiple;
