import React from "react";

export const LIST_VAN_DE_MUC_TIEU = [
  { label: "(1) Bắt đầu", value: 1 },
  { label: "(2) Tiếp tục", value: 2 },
  { label: "(3) <PERSON><PERSON>t thúc", value: 3 },
];

const LIST_CO_KHONG = [
  { label: "(+) Có", value: 1, hint: [] },
  { label: "(-) Không", value: 2, hint: [] },
];

export const DS_VAN_DE_MUC_TIEU = [
  { stt: 1, vanDe: "Mệt mỏi liên quan đến thiếu máu", mucTieu: "BN hết mệt" },
  {
    stt: 2,
    vanDe:
      "<PERSON><PERSON> (vị trí…) liên quan đến bệnh/tác dụng phụ của thuốc/thâm nhiễm/tổn thương/nhiễm  trùng….",
    mucTieu: "<PERSON><PERSON><PERSON> đau(vị trí….)",
  },
  {
    stt: 3,
    vanDe: "<PERSON>au xương nhiều liên quan đến bệnh",
    mucTieu: "Giảm đau xương cho NB",
  },
  {
    stt: 4,
    vanDe: "Đau, nhức liên quan đến chảy máu trong cơ, khớp….",
    mucTieu: "Người bệnh cảm thấy giảm đau",
  },
  {
    stt: 5,
    vanDe: "Ho(tính chất…) liên quan đến nhiễm khuẩn đường hô hấp/dị ứng/…..",
    mucTieu: "Hết  ho",
  },
  {
    stt: 6,
    vanDe: "Nguy cơ xảy ra tình trạng suy hô hấp",
    mucTieu: "Không xảy ra tình trạng suy hô hấp",
  },
  {
    stt: 7,
    vanDe:
      "Rối loạn giấc ngủ liên quan đến các triệu chứng của bệnh/tâm lý lo lắng của NB/nhức đầu/ Tác dụng sau dùng thuốc/ Thay đổi môi trường/….",
    mucTieu: "Hết rối loạn giấc ngủ",
  },
  {
    stt: 8,
    vanDe:
      "Buồn nôn/Nôn liên quan đến tác dụng phụ của thuốc/Bệnh lý tiêu hóa/bệnh lý….",
    mucTieu: "Hết nôn, buồn nôn.",
  },
  {
    stt: 9,
    vanDe:
      "Khó thở/Tăng tiết/ ứ đọng đờm dãi liên quan đến bệnh…./ tăng tiết đờm dãi/đau/ Thiếu máu/Viêm phổi/ ứ đọng đờm dãi/tổn thương/ chèn ép….",
    mucTieu: "Hết khó thở/Tăng tiết/ ứ đọng đờm dãi",
  },
  {
    stt: 10,
    vanDe:
      "Sốt liên quan đến tình trạng viêm họng/ viêm phổi/ nhiễm vi khuẩn/ nhiễm virus/  giảm bạch cầu trung tính/  mọc mảnh ghép/chấn thương/Rối loạn điện giải/…",
    mucTieu: "Hết sốt",
  },
  {
    stt: 11,
    vanDe:
      "Nguy cơ rối loạn nước và điện giải liên quan đến tình trạng sốt kéo dài",
    mucTieu: "Không xảy ra rối loạn nước và điện giải",
  },
  {
    stt: 12,
    vanDe:
      "Ăn kém liên quan đến tình trạng cơ thể mệt mỏi/khẩu phần ăn không phù hợp/viêm loét miệng…",
    mucTieu: "Ăn tốt hơn",
  },
  {
    stt: 13,
    vanDe: "Nguy cơ thiếu hụt dinh dưỡng liên quan đến bệnh/chế độ dinh dưỡng…",
    mucTieu: "Không thiếu hụt dinh dưỡng",
  },
  {
    stt: 14,
    vanDe:
      "Rối loạn tiêu hóa liên quan đến tác dụng phụ của thuốc/nhiễm khuẩn đường tiêu hóa/chế độ ăn",
    mucTieu: "Hết rối loạn tiêu hóa",
  },
  {
    stt: 15,
    vanDe:
      "Mệt mỏi liên quan đến bệnh/quá trình điều trị/ăn không ngon miệng/thiếu máu/…",
    mucTieu: "Hết mệt mỏi cho NB",
  },
  {
    stt: 16,
    vanDe: "Thiếu máu liên quan đến bệnh/tác dụng phụ khi điều trị hóa chất",
    mucTieu: "Hết thiếu máu",
  },
  {
    stt: 17,
    vanDe:
      "Nguy cơ thiếu máu liên quan đến bệnh/tác dụng phụ khi điều trị hóa chất…",
    mucTieu: "Không thiếu máu",
  },
  {
    stt: 18,
    vanDe:
      "Chảy máu/xuất huyết dưới da( vị trí….) liên quan đến rối loạn đông máu/rối loạn chức năng tiểu cầu/ bệnh lý đường tiêu hoá/chấn thương….",
    mucTieu: "Hết chảy máu/hết xuất huyết",
  },
  {
    stt: 19,
    vanDe:
      "Nguy cơ Chảy máu/xuất huyết dưới da( vị trí….) liên quan đến rối loạn đông máu/rối loạn chức năng tiểu cầu/ bệnh lý đường tiêu hoá/chấn thương….",
    mucTieu: "Không chảy máu, xuất huyết",
  },
  {
    stt: 20,
    vanDe:
      "Nguy cơ loét  - tỳ đè liên quan đến hạn chế vận động/thiếu hụt dinh dưỡng/ bệnh lý (kèm theo…)/ biến chứng….",
    mucTieu: "Không xảy ra loét/không có loét tỷ đè",
  },
  {
    stt: 21,
    vanDe:
      "Loét tỳ đè liên quan đến hạn chế vận động/thiếu hụt dinh dưỡng/bệnh lý (kèm theo…)/biến chứng…",
    mucTieu: "Hết loét",
  },
  {
    stt: 22,
    vanDe:
      "Tiêu chảy liên quan đến tác dụng phụ của thuốc/nhiễm khuẩn đường tiêu hóa/chế độ sinh hoạt/chế độ ăn uống/…",
    mucTieu: "Hết tiêu chảy",
  },
  {
    stt: 23,
    vanDe:
      "Táo bón liên quan đến tác dụng phụ của thuốc/giảm nhu động ruột/do hạn chế vận động/chế độ sinh hoạt/chế độ ăn uống…",
    mucTieu: "Hết táo bón",
  },
  {
    stt: 24,
    vanDe:
      "Nguy cơ táo bón liên quan đến tác dụng phụ của thuốc/giảm nhu động ruột/do hạn chế vận động/chế độ sinh hoạt/chế độ ăn uống…",
    mucTieu: "Không táo bón",
  },
  {
    stt: 25,
    vanDe:
      "Nguy cơ tắc ruột liên quan đến bệnh lý đường tiêu hóa/chế độ sinh hoạt/chế độ ăn uống/…",
    mucTieu: "Không xảy ra tắc ruột",
  },
  {
    stt: 26,
    vanDe:
      "Nguy cơ tiêu chảy liên quan đến tác dụng phụ của thuốc/nhiễm khuẩn đường tiêu hóa/chế độ sinh hoạt/chế độ ăn uống/…",
    mucTieu: "Không tiêu chảy",
  },
  {
    stt: 27,
    vanDe: "Gia đình thiếu thông tin về chăm sóc sức khỏe",
    mucTieu: "Gia đình được cung cấp đủ thông tin về CSSK",
  },
  {
    stt: 28,
    vanDe: "Lo lắng/Buồn chán về bệnh tật",
    mucTieu: "Hết lo lắng/Buồn chán",
  },
  {
    stt: 29,
    vanDe:
      "Nguy cơ xảy ra biến chứng… liên quan đến không tuân thủ chăm sóc, điều trị",
    mucTieu: "Không xảy ra biến chứng",
  },
  {
    stt: 30,
    vanDe: "Hạn chế vận động liên quan đến sưng/đau các khớp",
    mucTieu: "Người bệnh đỡ sưng đau các khớp, có thể tự vận động",
  },
  {
    stt: 31,
    vanDe:
      "Nguy cơ teo cơ cứng khớp liên quan đến hạn chế vận động/bệnh lý cơ xương khớp…",
    mucTieu: "Không xảy ra teo cơ cứng khớp",
  },
  {
    stt: 32,
    vanDe: "Nguy cơ ngã liên quan đến thiếu máu/bệnh lý cơ xương khớp…",
    mucTieu: "Không bị ngã",
  },
  {
    stt: 33,
    vanDe:
      "Mất khả năng vận động, phải nằm lâu liên quan đến tổn thương thần kinh/chấn thương…",
    mucTieu: "Vận động được trở lại",
  },
  {
    stt: 34,
    vanDe: "Nguy cơ nhiễm trùng liên quan đến việc giảm bạch cầu",
    mucTieu: "Không xảy ra nhiễm trùng",
  },
  {
    stt: 35,
    vanDe: "Đái tháo đường liên quan đến biến chứng/biến chứng quá tải sắt",
    mucTieu: "Đường huyết về mức an toàn",
  },
  {
    stt: 36,
    vanDe: "Đường huyết tăng cao liên quan đến biến chứng quá tải sắt",
    mucTieu: "Đường huyết về mức an toàn",
  },
  {
    stt: 37,
    vanDe:
      "Tiểu ít liên quan đến suy giảm chức năng thận/tràn dịch đa màng/bù dịch không đủ…",
    mucTieu: "Số lượng nước tiểu trong giới hạn bình thường",
  },
  {
    stt: 38,
    vanDe:
      "Mẩn ngứa/Ngứa liên quan đến phản ứng truyền chế phẩm/thuốc/bệnh lý ngoài da/…",
    mucTieu: "Hết mẩn ngứa/ngứa",
  },
  {
    stt: 39,
    vanDe:
      "Nguy cơ dị ứng/phản vệ liên quan đến thuốc và dịch truyền/Máu/chế phẩm/môi trường( áp dụng với các  NB có tiền sử dị ứng/ phản ứng truyền máu/thuốc/dịch trước đây)",
    mucTieu: "Không xảy ra phản ứng phản vệ/dị ứng",
  },
  {
    stt: 40,
    vanDe:
      "Nguy cơ hạ đường huyết liên quan đến sử dụng insulin/ liên quan đến bệnh/ tác dụng phụ của thuốc/chế độ ăn uống….",
    mucTieu: "Không xảy ra hạ đường huyết",
  },
  {
    stt: 41,
    vanDe:
      "Nguy cơ huyết khối (tay/ chân/ổ bụng…) liên quan đến biến chứng sau mổ cắt lách",
    mucTieu: "Không có huyết khối",
  },
  {
    stt: 42,
    vanDe:
      "Nguy cơ phù( tay/chân/…)  liên quan đến ứ chệ tuần hoàn/ bệnh lý tim mạch…",
    mucTieu: "Không phù",
  },
  {
    stt: 43,
    vanDe:
      "Phù (tay/ chân…) liên quan đến ứ chệ tuần hoàn/dinh dưỡng/bệnh lý tim mạch….",
    mucTieu: "Hết phù",
  },
  {
    stt: 44,
    vanDe:
      "Nguy cơ suy tim liên quan đến tình trạng quá tải tuần hoàn kéo dài/thiếu máu/bệnh lý tim mạch….",
    mucTieu: "Không xảy ra suy tim",
  },
  {
    stt: 45,
    vanDe:
      "Tắc mạch (vị trí….) liên quan đến tình trạng bạch cầu tăng cao/ tiểu cầu tăng cao/tăng độ quánh máu/tăng đông/ bệnh lý mạch máu…",
    mucTieu: "Hết tắc mạch",
  },
  {
    stt: 46,
    vanDe:
      "Nguy cơ tắc mạch liên quan đến tình trạng bạch cầu tăng cao/ tiểu cầu tăng cao/tăng độ quánh máu/tăng đông/ bệnh lý mạch máu…",
    mucTieu: "Không xảy ra tắc mạch",
  },
  {
    stt: 47,
    vanDe: "Tăng huyết áp",
    mucTieu: "Huyết áp trong giới hạn bình thường",
  },
  {
    stt: 48,
    vanDe:
      "Nguy cơ tăng huyết áp liên quan đến bệnh…/tác dụng phụ của thuốc/tăng HST/chế độ sinh hoạt…",
    mucTieu: "Không xảy ra tăng huyết áp",
  },
  {
    stt: 49,
    vanDe: "Viêm tĩnh mạch liên quan đến nhiễm khuẩn đường truyền/ thoát mạch/…",
    mucTieu: "Hết viêm tĩnh mạch",
  },
];

export const LIST_THEO_DOI_KHAC = [
  {
    label1: "Đau",
    rowSpan1: 2, // Adjusted rowspan to 6 (Header + Đau + 2 sub-items + 3 droplists + Khác)
    label2: "Vị trí đau", // Added based on OCR
    key: "khungDau.viTri", // Kept existing key for header logic
    hintKhac: "dauViTri",
  },

  {
    label2: "Mức độ (điểm đau)", // Added based on OCR, child of Đau logically
    key: "khungDau.mucDoDau", // Added key
    hint: [""],
  },
];

export const LIST_VET_THUONG = [
  {
    label1: "Vết thương",
    rowSpan1: 9, // Updated rowspan to 10 (Header + 9 items)
    label2: "Vị trí", // Added based on OCR
    key: "khungVetThuong.viTri", // Added key
    hintKhac: "vetThuongViTri",
    // No hint in OCR image for droplist options
  },
  {
    label2: "Loại vết thương", // Added based on OCR
    key: "khungVetThuong.loaiVetThuong", // Added key
    hint: ["(1) Sạch", "(2) Nhiễm", "(3) Vô khuẩn"], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Sạch", value: 1 },
      { label: "(2) Nhiễm", value: 2 },
      { label: "(3) Vô khuẩn", value: 3 },
    ],
    mode: "onlyOne",
    isArr: true,
  },
  {
    label2: "Kích thước (cm)", // Added based on OCR
    key: "khungVetThuong.kichThuoc",
    hint: [], // Added key
    // No hint in OCR image for droplist options
  },
  {
    label2: "Phân độ loét (đối với VT là loét tỳ đè)", // Added based on OCR
    key: "khungVetThuong.phanDoLoet", // Added key
    hint: [
      "(1) Độ I",
      "(2) Độ II",
      "(3) Độ III",
      "(4) Độ IV",
      "(5) Nghi ngờ tổn thương mô sâu",
      "(6) Không xếp loại được",
    ], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Độ I", value: 1 },
      { label: "(2) Độ II", value: 2 },
      { label: "(3) Độ III", value: 3 },
      { label: "(4) Độ IV", value: 4 },
      { label: "(5) Nghi ngờ tổn thương mô sâu", value: 5 },
      { label: "(6) Không xếp loại được", value: 6 },
    ],
    mode: "onlyOne",
    isArr: true,
  },
  {
    label2: "Số lượng dịch tiết", // Added based on OCR
    key: "khungVetThuong.soLuongDichTiet", // Added key
    hint: ["(1) Không có", "(2) Ít", "(3) Trung bình", "(4) Nhiều"], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Không có", value: 1 },
      { label: "(2) Ít", value: 2 },
      { label: "(3) Trung bình", value: 3 },
      { label: "(4) Nhiều", value: 4 },
    ],
    mode: "onlyOne",
    isArr: true,
  },
  {
    label2: "Tính chất dịch tiết", // Added based on OCR
    key: "khungVetThuong.tinhChatDichTiet", // Added key
    hint: ["(1) Trong", "(2) Máu", "(3) Mủ"], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Trong", value: 1 },
      { label: "(2) Máu", value: 2 },
      { label: "(3) Mủ", value: 3 },
    ],
    mode: "multiple",
    isArr: true,
  },
  {
    label2: "Mùi vết thương", // Added based on OCR
    key: "khungVetThuong.muiVetThuong", // Added key
    hint: ["(1) Không mùi", "(2) Ít mùi", "(3) Nặng mùi"], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Không mùi", value: 1 },
      { label: "(2) Ít mùi", value: 2 },
      { label: "(3) Nặng mùi", value: 3 },
    ],
    mode: "onlyOne",
    isArr: true,
  },
  {
    label2: "Mô, nền vết thương", // Added based on OCR
    key: "khungVetThuong.moNenVetThuong", // Added key
    hint: ["(1) Mô hạt", "(2) Mảng mục", "(3) Hoại tử"], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Mô hạt", value: 1 },
      { label: "(2) Mảng mục", value: 2 },
      { label: "(3) Hoại tử", value: 3 },
    ],
    mode: "multiple",
    isArr: true,
  },
  {
    label2: "Khác", // Added based on OCR
    key: "khungVetThuong.vetThuongKhac",
    hintKhac: "vetThuongKhac",
    // No hint in OCR image for droplist options
  },
];

export const LIST_DAN_LUU = [
  {
    label1: "Dẫn lưu / lỗ mở ra da",
    rowSpan1: 10, // Updated rowspan to 11 (Header + 10 items)
    label2: "Vị trí", // Added based on OCR
    key: "khungDanLuu.viTri", // Added key
    hint: ["(1) Cổ", "(2) Ngực", "(3) Bụng", "(4) Hố thận"], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Cổ", value: 1 },
      { label: "(2) Ngực", value: 2 },
      { label: "(3) Bụng", value: 3 },
      { label: "(4) Hố thận", value: 4 },
    ],
    mode: "onlyOne",
    isArr: true,
  },
  {
    label2: "Vị trí khác", // Added based on OCR
    key: "khungDanLuu.viTriKhac", // Added key
    hintKhac: "danLuuLoMoRaDaViTriKhac",
  },
  {
    label2: "Tình trạng hoạt động", // Added based on OCR
    key: "khungDanLuu.tinhTrangHoatDong", // Added key
    hint: ["(1) Thông tốt", "(2) Bán tắc", "(3) Tắc"], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Thông tốt", value: 1 },
      { label: "(2) Bán tắc", value: 2 },
      { label: "(3) Tắc", value: 3 },
    ],
    mode: "onlyOne",
    isArr: true,
  },
  {
    label2: "Chân dẫn lưu", // Added based on OCR
    key: "khungDanLuu.chanDanLuu", // Added key
    hint: ["(1) Khô sạch", "(2) Đỏ", "(3) Tiết dịch"], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Khô sạch", value: 1 },
      { label: "(2) Đỏ", value: 2 },
      { label: "(3) Tiết dịch", value: 3 },
    ],
  },
  {
    label2: "Số lượng (ml)", // Added based on OCR
    key: "khungDanLuu.soLuong",
    hint: [], // Added key
    // No hint in OCR image for droplist options
  },
  {
    label2: "Màu sắc dịch", // Added based on OCR
    key: "khungDanLuu.mauSacDich", // Added key
    hint: ["(1) Đỏ", "(2) Hồng", "(3) Vàng", "(4) Xanh"], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Đỏ", value: 1 },
      { label: "(2) Hồng", value: 2 },
      { label: "(3) Vàng", value: 3 },
      { label: "(4) Xanh", value: 4 },
    ],
    mode: "multiple",
    isArr: true,
  },
  {
    label2: "Tính chất dịch", // Added based on OCR
    key: "khungDanLuu.tinhChatDich", // Added key
    hint: ["(1) Trong", "(2) Lợn cợn"], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Trong", value: 1 },
      { label: "(2) Lợn cợn", value: 2 },
    ],
    mode: "onlyOne",
    isArr: true,
  },
  {
    label2: "Ngày đặt", // Added based on OCR
    key: "khungDanLuu.ngayDat", // Added key
    hint: [],
    type: "time",
  },
  {
    label2: "Ngày rút", // Added based on OCR
    key: "khungDanLuu.ngayRut", // Added key
    hint: [],
    type: "time",
  },
  {
    label2: "Khác", // Added based on OCR
    key: "khungDanLuu.khac", // Added key
    hintKhac: "danLuuLoMoRaDaKhac",
  },
];

export const LIST_ONG_THONG_MACH = [
  {
    label1: "Ống thông mạch máu",
    rowSpan1: 10, // Updated rowspan to 10 (Header + 9 items)
    label2: "Tên ống thông", // Added based on OCR
    key: "khungOngThong.ongThong", // Added key
    hint: [
      "(1) Ống thông tĩnh mạch ngoại biên",
      "(2) Ống thông tĩnh mạch trung tâm",
      "(3) Ống thông khác",
    ], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Ống thông tĩnh mạch ngoại biên", value: 1 },
      { label: "(2) Ống thông tĩnh mạch trung tâm", value: 2 },
      { label: "(3) Ống thông khác", value: 3 },
    ],
    mode: "onlyOne",
    isArr: true,
  },
  {
    label2: "Tên khác", // Added based on OCR
    key: "khungOngThong.tenKhac", // Added key
    hintKhac: "ongThongMachMauTenKhac",
    // No hint in OCR image for droplist options
  },
  {
    label2: "Vị trí", // Added based on OCR
    key: "khungOngThong.viTri", // Added key
    hint: ["(1) Cánh tay", "(2) Cẳng tay", "(3) Mu bàn tay", "(4) Dưới đòn"], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Cánh tay", value: 1 },
      { label: "(2) Cẳng tay", value: 2 },
      { label: "(3) Mu bàn tay", value: 3 },
      { label: "(4) Dưới đòn", value: 4 },
    ],
    mode: "onlyOne",
    isArr: true,
  },
  {
    label2: "Vị trí khác", // Added based on OCR
    key: "khungOngThong.viTriKhac", // Added key
    hintKhac: "ongThongMachMauViTriKhac",
  },
  {
    label2: "Tình trạng hoạt động", // Added based on OCR
    key: "khungOngThong.tinhTrangHoatDong", // Added key
    hint: ["(1) Thông tốt", "(2) Bán tắc", "(3) Tắc"], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Thông tốt", value: 1 },
      { label: "(2) Bán tắc", value: 2 },
      { label: "(3) Tắc", value: 3 },
    ],
    mode: "onlyOne",
    isArr: true,
  },
  {
    label2:
      "Điểm đánh giá mức độ viêm TM (VIP score) đối với ống thông TM ngoại biên", // Added based on OCR
    key: "khungOngThong.mucDoViem", // Added key
    hint: [
      "(0) Cấp 0",
      "(1) Cấp 1",
      "(2) Cấp 2",
      "(3) Cấp 3",
      "(4) Cấp 4",
      "(5) Cấp 5",
    ], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(0) Cấp 0", value: 0 },
      { label: "(1) Cấp 1", value: 1 },
      { label: "(2) Cấp 2", value: 2 },
      { label: "(3) Cấp 3", value: 3 },
      { label: "(4) Cấp 4", value: 4 },
      { label: "(5) Cấp 5", value: 5 },
    ],
    mode: "onlyOne",
    isArr: true,
  },
  {
    label2: "Tình trạng da nơi đặt", // Added based on OCR
    key: "khungOngThong.tinhTrangDaNoiDat", // Added key
    hint: ["(1) Khô sạch", "(2) Đỏ", "(3) Tiết dịch"], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Khô sạch", value: 1 },
      { label: "(2) Đỏ", value: 2 },
      { label: "(3) Tiết dịch", value: 3 },
    ],
    mode: "onlyOne",
    isArr: true,
  },
  {
    label2: "Ngày đặt", // Added based on OCR
    key: "khungOngThong.ngayDat", // Added key
    hint: [],
    type: "time",
  },
  {
    label2: "Ngày rút", // Added based on OCR
    key: "khungOngThong.ngayRut", // Added key
    hint: [],
    type: "time",
  },
  {
    label2: "Khác", // Added based on OCR
    key: "khungOngThong.khac", // Added key
    hintKhac: "ongThongMachMauKhac",
  },
];

export const LIST_LOAI_THUOC = [
  {
    label: "(1) Thuốc uống",
    value: 1,
    hint: [],
  },
  {
    label: "(2) Thuốc tiêm",
    value: 2,
    hint: [],
  },
  {
    label: "(3) Thuốc truyền",
    value: 3,
    hint: [],
  },
  {
    label: "(4) Phun khí dung",
    value: 4,
    hint: [],
  },
  {
    label: "(5) Thuốc đặt",
    value: 5,
    hint: [],
  },
  {
    label: "(6) Truyền máu và chế phẩm máu",
    value: 6,
    hint: [],
  },
];

export const LIST_THUC_HIEN_CAN_LAM_SANG = [
  { label: "(1) XN máu", value: 1, hint: [] },
  { label: "(2) XN nước tiểu", value: 2, hint: [] },
  { label: "(3) XN đàm", value: 3, hint: [] },
  { label: "(4) XN phân", value: 4, hint: [] },
  { label: "(5) XN dịch", value: 5, hint: [] },
  { label: "(6) Điện tim", value: 6, hint: [] },
  { label: "(7) Siêu âm bụng", value: 7, hint: [] },
  { label: "(8) Siêu âm tim", value: 8, hint: [] },
  { label: "(9) X-Quang", value: 9, hint: [] },
  { label: "(10) CT", value: 10, hint: [] },
  { label: "(11) MRI", value: 11, hint: [] },
  { label: "(12) Nội soi dạ dày", value: 12, hint: [] },
  { label: "(13) Nội soi đại tràng", value: 13, hint: [] },
  { label: "(14) Nội soi phế quản", value: 14, hint: [] },
  { label: "(15) Chọc tủy xương", value: 15, hint: [] },
  { label: "(16) ST tủy xương", value: 16, hint: [] },
  { label: "(17) Chọc dịch/Tiêm tủy sống", value: 17, hint: [] },
];

export const LIST_CHAM_SOC_DIEU_DUONG = [
  { label: "(1) Thở oxy", value: 1, hint: [] },
  { label: "(2) Vỗ rung", value: 2, hint: [] },
  { label: "(3) Ăn qua sonde", value: 3, hint: [] },
  { label: "(4) Đặt sonde dạ dày", value: 4, hint: [] },
  { label: "(5) Đặt sonde tiểu", value: 5, hint: [] },
  { label: "(6) Đặt catheter TM", value: 6, hint: [] },
  { label: "(7) TB cắt chỉ", value: 7, hint: [] },
  { label: "(8) TB - RVT", value: 8, hint: [] },
  { label: "(9) TB-RVT-CL", value: 9, hint: [] },
  { label: "(10) Thụt tháo", value: 10, hint: [] },
  { label: "(11) Phòng ngừa loét", value: 11, hint: [] },
  { label: "(12) Rút dẫn lưu", value: 12, hint: [] },
  { label: "(13) Gội đầu", value: 13, hint: [] },
  { label: "(14) Tắm", value: 14, hint: [] },
  { label: "(15) Rút meches", value: 15, hint: [] },
  { label: "(16) PHCN", value: 16, hint: [] },
  { label: "(17) Hạ sốt", value: 17, hint: [] },
  { label: "(18) Vệ sinh răng miệng", value: 18, hint: [] },
  { label: "(19) Rút sonde dạ dày", value: 19, hint: [] },
  { label: "(20) Rút sonde tiểu", value: 20, hint: [] },
  { label: "(21) Rút catheter TM", value: 21, hint: [] },
  { label: "(22) Hút đờm", value: 22, hint: [] },
  { label: "(23) Cho người bệnh nằm đầu cao", value: 23, hint: [] },
];

const LIST_THEO_DOI_NGUOI_BENH = [
  { label: "(1) DHST", value: 1, hint: [] },
  { label: "(2) Cân nặng", value: 2, hint: [] },
  { label: "(3) Dinh dưỡng", value: 3, hint: [] },
  { label: "(4) ĐHMM", value: 4, hint: [] },
  { label: "(5) Hô hấp", value: 5, hint: [] },
  { label: "(6) Nước tiểu", value: 6, hint: [] },
  { label: "(7) Vệ sinh", value: 7, hint: [] },
  { label: "(8) Vết mổ", value: 8, hint: [] },
  { label: "(9) Vận động", value: 9, hint: [] },
  { label: "(10) Lọc máu", value: 10, hint: [] },
  { label: "(11) Dịch dẫn lưu", value: 11, hint: [] },
  { label: "(12) Dấu hiệu khản tiếng", value: 12, hint: [] },
  { label: "(13) Theo dõi tri giác", value: 13, hint: [] },
];

export const TR = [
  { label1: "I. NHẬN ĐỊNH NGƯỜI BỆNH", colSpan1: 3, disabled: true },
  {
    label1: (
      <div style={{ display: "flex" }}>
        <div style={{ flex: 1, borderRight: "1px solid #000" }}>
          Ghi số tương ứng với vấn đề nhận định trên NB Nếu không có đánh dấu -
        </div>
        <div style={{ width: 150, textAlign: "right" }}>
          <div>
            <b>Giờ:</b>
          </div>
          <div>
            <b>Ngày /tháng /năm</b>
          </div>
        </div>
      </div>
    ),
    colSpan1: 3,
    key: "thoiGianThucHien",
    type: "time",
  },

  {
    label1: "I. Phân cấp chăm sóc",
    colSpan1: 2,
    key: "phanCapChamSoc",
    // disable: true,
    hint: ["(2) CS cấp 2", "(3) CS cấp 3"],
  },
  {
    label1: "Chỉ số sinh trắc",
    rowSpan1: 8,
    label2: "Cân nặng (kg):",
    key: "chiSoSong.canNang",
    hint: ["Chỉ số cân nặng hàng ngày (kg)"],
  },
  {
    label2: "BMI",
    key: "chiSoSong.bmi",
    hint: ["Chỉ số khối cơ thể (kg/m2)"],
  },
  {
    label2: "Nhiệt độ",
    key: "chiSoSong.nhietDo",
    hint: ["Chỉ số nhiệt độ (°C)"], // No droplist as hint is a description
  },
  {
    label2: "Huyết áp",
    key: "chiSoSong.huyetAp",
    hint: ["Chỉ số huyết áp (mmHg)"], // No droplist as hint is a description
  },
  {
    label2: "Huyết áp TB (mmHg)",
    key: "huyetApTrungBinh",
    hint: [""], // No droplist as hint is a description
  },
  {
    label2: "SpO2 (%)", // Moved up based on OCR
    key: "chiSoSong.spo2",
    hint: ["Chỉ số SpO2 (%)"], // No droplist as hint is a description
  },
  {
    label2: "Mạch", // Moved up based on OCR
    key: "chiSoSong.mach",
    hint: ["Chỉ số mạch (lần/phút)"], // No droplist as hint is a description
  },
  {
    label2: "Nhịp thở", // Moved up based on OCR
    key: "chiSoSong.nhipTho",
    hint: ["Chỉ số nhịp thở(lần/phút)"], // No droplist as hint is a description
  },
  {
    label1: "Toàn thân",
    rowSpan1: 11,
    label2: "Tri giác (ACVPU)",
    hint: [
      "(A) Tỉnh táo",
      "(C) Lơ mơ",
      "(V) Đáp ứng với âm thanh",
      "(P) Đáp ứng với kích thích đau",
      "(U) Không đáp ứng",
    ],
    type: "droplist",
    labelByKey: "valueDisplay",
    data: [
      {
        label: "(A) Tỉnh táo",
        value: "1",
        valueDisplay: "A",
      },
      {
        label: "(C) Lơ mơ",
        value: 2,
        valueDisplay: "C",
        labelByKey: "valueDisplay",
      },
      {
        label: "(V) Đáp ứng với âm thanh",
        value: 3,
        valueDisplay: "V",
        labelByKey: "valueDisplay",
      },
      {
        label: "(P) Đáp ứng với kích thích đau",
        value: 4,
        valueDisplay: "P",
        labelByKey: "valueDisplay",
      },
      {
        label: "(U) Không đáp ứng",
        value: 5,
        valueDisplay: "U",
        labelByKey: "valueDisplay",
      },
    ],
    key: "triGiac",
    mode: "onlyOne",
  },

  {
    label2: "Glassgow",
    key: "glassgow",
    hint: [],
  },
  {
    label2: "Tinh thần",
    key: "tinhThan2",
    hint: ["(1) Bình thường", "(2) Lo lắng", "(3) Ủ rũ", "(4) Kích động"],
    type: "droplist",
    data: [
      { label: "(1) Bình thường", value: 1 },
      { label: "(2) Ủ rũ", value: 2 },
      { label: "(3) Lo lắng", value: 3 },
      { label: "(4) Kích động", value: 4 },
    ],
  },
  {
    label2: "Màu sắc niêm",
    key: "daNiemMac",
    hint: ["(1) Hồng", "(2) Nhợt nhạt", "(3) Vàng"],
    type: "droplist",
    data: [
      { label: "(1) Hồng", value: 1 },
      { label: "(2) Nhợt nhạt", value: 2 },
      { label: "(3) Vàng", value: 3 },
    ],
    mode: "onlyOne",
  },
  {
    label2: "Màu sắc da",
    key: "mauSacDa",
    hint: ["(1) Bình thường", "(2) Vàng", "(3) Xanh xao", "(4) Tím tái"],
    type: "droplist",
    data: [
      { label: "(1) Bình thường", value: 1 },
      { label: "(2) Vàng", value: 2 },
      { label: "(3) Xanh xao", value: 3 },
      { label: "(4) Tím tái", value: 4 },
    ],
    mode: "onlyOne",
    isArr: true,
  },
  {
    label2: "Tính chất da",
    key: "tinhChatDa",
    hint: ["(1) Ấm", "(2) Nóng", "(3) Lạnh"],
    type: "droplist",
    data: [
      { label: "(1) Ấm", value: 1 },
      { label: "(2) Nóng", value: 2 },
      { label: "(3) Lạnh", value: 3 },
    ],
    mode: "onlyOne",
    isArr: true,
  },
  {
    label2: "Tình trạng da",
    key: "tinhTrangDa",
    hint: [
      "(1) Bình thường",
      "(2) Viêm tại chỗ",
      "(3) Rôm lở",
      "(4) Nổi mẩn",
      "(5) Khối tụ máu",
      "(6) Chấm/mảng xuất huyết",
    ],
    type: "droplist",
    rowSpan2: 2,
    data: [
      { label: "(1) Bình thường", value: 1 },
      { label: "(2) Viêm tại chỗ", value: 2 },
      { label: "(3) Rôm lở", value: 3 },
      { label: "(4) Nổi mẩn", value: 4 },
      { label: "(5) Khối tụ máu", value: 5 },
      { label: "(6) Chấm/mảng xuất huyết", value: 6 },
    ],
  },
  {
    hint: ["Vị trí, kích thước (cm x cm)"],
    key: "viTriKichThuoc",
  },

  {
    label2: "Tràn khí dưới da/ Mô cơ",
    key: "tranKhiDuoiDa",
    hint: ["(1) Không", "(2) Có"],
    type: "droplist",
    data: [
      { label: "(1) Không", value: 1 },
      { label: "(2) Có", value: 2 },
    ],
    mode: "onlyOne",
    isArr: true,
  },
  {
    label2: "Tràn khí dưới da/ Mô cơ (Mức độ)",
    key: "mucDoTranKhi",
    hint: ["(1) Rất ít", "(2) Ít", "(3) Nhiều"],
    type: "droplist",
    data: [
      { label: "(1) Rất ít", value: 1 },
      { label: "(2) Ít", value: 2 },
      { label: "(3) Nhiều", value: 3 },
    ],
    mode: "onlyOne",
    isArr: true,
  },
  // ... existing code ...
  {
    label2: "Khác",
    key: "toanThanKhac",
    hintKhac: "toanThanKhac",
  },

  // -------------- Hô Hấp -------------
  {
    label1: "Hô hấp",
    rowSpan1: 8, // Updated rowspan to 10 based on the image
    label2: "Kiểu thở",
    key: "kieuTho",
    type: "droplist", // Added type droplist
    hint: [
      "(1) Đều êm",
      "(2) Thở nông", // Updated hint based on OCR
      "(3) Khò khè", // Updated hint based on OCR
      "(4) Khó thở",
      "(5) Co kéo cơ hô hấp phụ", // Updated hint based on OCR
    ],
    data: [
      // Added data based on hint
      { label: "(1) Đều êm", value: 1 },
      { label: "(2) Thở nông", value: 2 },
      { label: "(3) Khò khè", value: 3 },
      { label: "(4) Khó thở", value: 4 },
      { label: "(5) Co kéo cơ hô hấp phụ", value: 5 },
    ],
    mode: "onlyOne",
  },
  {
    label2: "Ho", // Updated label based on OCR
    key: "ho",
    hint: ["(1) Không", "(2) Ho khan", "(3) Ho đờm", "(4) Ho máu"], // Updated hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Không", value: 1 },
      { label: "(2) Ho khan", value: 2 },
      { label: "(3) Ho đờm", value: 3 },
      { label: "(4) Ho máu", value: 4 },
    ],
    mode: "onlyOne",
  },
  {
    label2: "SL đờm (ml)", // Added based on OCR
    key: "soLuongDom",
    hint: [],
  },
  {
    label2: "Màu sắc đờm", // Added based on OCR
    key: "mauSacSom",
    hint: [
      "(a) Trắng đục",
      "(b) Vàng",
      "(c) Xanh",
      "(d) Trắng trong",
      "(e) Đỏ",
      "(f) Nâu",
      "(g) Hồng",
      "(h) Đen",
    ], // Added hint based on OCR
    type: "droplist", // Added type droplist
    labelByKey: "valueDisplay",
    data: [
      // Added data based on hint (using letters as values)
      {
        label: "(a) Trắng đục",
        value: 1,
        valueDisplay: "a",
        labelByKey: "valueDisplay",
      },
      {
        label: "(b) Vàng",
        value: 2,
        valueDisplay: "b",
        labelByKey: "valueDisplay",
      },
      {
        label: "(c) Xanh",
        value: 3,
        valueDisplay: "c",
        labelByKey: "valueDisplay",
      },
      {
        label: "(d) Trắng trong",
        value: 4,
        valueDisplay: "d",
        labelByKey: "valueDisplay",
      },
      {
        label: "(e) Đỏ",
        value: 5,
        valueDisplay: "e",
        labelByKey: "valueDisplay",
      },
      {
        label: "(f) Nâu",
        value: 6,
        valueDisplay: "f",
        labelByKey: "valueDisplay",
      },
      {
        label: "(g) Hồng",
        value: 7,
        valueDisplay: "g",
        labelByKey: "valueDisplay",
      },
      {
        label: "(h) Đen",
        value: 8,
        valueDisplay: "h",
        labelByKey: "valueDisplay",
      },
    ],
    mode: "onlyOne",
  },
  {
    label2: "Tính chất đờm", // Added based on OCR
    key: "tinhChatDom",
    hint: ["(a) Loãng", "(b) Đặc", "(c) Lẫn máu"], // Added hint based on OCR
    labelByKey: "valueDisplay",
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint (using letters as values)
      {
        label: "(a) Loãng",
        value: 1,
        valueDisplay: "a",
        labelByKey: "valueDisplay",
      },
      {
        label: "(b) Đặc",
        value: 2,
        valueDisplay: "b",
        labelByKey: "valueDisplay",
      },
      {
        label: "(c) Lẫn máu",
        value: 3,
        valueDisplay: "c",
        labelByKey: "valueDisplay",
      },
    ],
  },
  {
    label2: "Liệu pháp oxy", // Added based on OCR
    key: "lieuPhapOxy",
    hint: [
      "(1) Không",
      "(2) Oxy gọng kính",
      "(3) Oxy mặt nạ có túi không hít lại",
    ], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Không", value: 1 },
      { label: "(2) Oxy gọng kính", value: 2 },
      { label: "(3) Oxy mặt nạ có túi không hít lại", value: 3 },
    ],
    rowSpan2: 2,
    mode: "onlyOne",
    isArr: true,
  },
  {
    hint: ["Lưu lượng oxy (lít/phút)"], // Added based on OCR, child of Liệu pháp oxy logically
    key: "luuLuongOxy",
  },
  {
    label2: "Khác",
    key: "hoHapKhac", // Changed key to be more specific
    hintKhac: "hoHapKhac",
  },

  // ---------- Tuần hoàn

  {
    label1: "Tuần hoàn",
    rowSpan1: 5, // Updated rowspan based on the image (Header + 7 items)
    label2: "Tính chất mạch", // Updated label based on OCR
    key: "tinhChatMach",
    hint: [
      "(1) Đều, rõ",
      "(2) Không đều",
      "(3) Rời rạc",
      "(4) Khó bắt",
      "(5) Không rõ mạch",
    ], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Đều, rõ", value: 1 },
      { label: "(2) Không đều", value: 2 },
      { label: "(3) Rời rạc", value: 3 },
      { label: "(4) Khó bắt", value: 4 },
      { label: "(5) Không rõ mạch", value: 5 },
    ],
  },
  {
    label2: "Phù",
    key: "phu",
    hint: ["(1) Độ 1", "(2) Độ 2", "(3) Độ 3", "(4) Độ 4"],
    type: "droplist", // Ensure type is droplist
    data: [
      // Ensure data is correct based on hint
      { label: "(1) Độ 1", value: 1 },
      { label: "(2) Độ 2", value: 2 },
      { label: "(3) Độ 3", value: 3 },
      { label: "(4) Độ 4", value: 4 },
    ],
    mode: "onlyOne",
  },
  {
    label2: "Vị trí phù", // Added based on OCR, child of Phù
    key: "viTriPhu2",
    hint: ["(1) Chi trên", "(2) Chi dưới", "(3) Toàn thân"], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Chi trên", value: 1 },
      { label: "(2) Chi dưới", value: 2 },
      { label: "(3) Toàn thân", value: 3 },
    ],
  },
  {
    label2: "Tính chất phù", // Added based on OCR, child of Phù
    key: "tinhChatPhu",
    hint: ["(1) Phù mềm ấn lõm", "(2) Phù cứng"], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Phù mềm ấn lõm", value: 1 },
      { label: "(2) Phù cứng", value: 2 },
    ],
    mode: "onlyOne",
    isArr: true,
  },
  {
    label2: "Khác",
    key: "tuanHoanKhac",
    hintKhac: "tuanHoanKhac",
  },

  // Tiêu hóa dinh dưỡng
  {
    label1: "Tiêu hóa Dinh dưỡng", // Updated label to match OCR
    rowSpan1: 14, // Updated rowspan based on the image (Header + 8 items)
    label2: "Tình trạng bụng", // Added based on OCR
    key: "tinhTrangBung", // Added key
    hint: ["(1) Bụng mềm", "(2) Bụng báng", "(3) Bụng chướng"],
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Bụng mềm", value: 1 },
      { label: "(2) Bụng báng", value: 2 },
      { label: "(3) Bụng chướng", value: 3 },
    ],
    mode: "onlyOne",
  },
  {
    label2: "Tình trạng bụng khác",
    hint: [],
    key: "ctTinhTrangBung",
  },
  {
    label2: "Nhu động ruột", // Added based on OCR
    key: "nhuDongRuot", // Added key
    hint: ["(1) Có", "(2) Không"], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Có", value: 1 },
      { label: "(2) Không", value: 2 },
    ],
    mode: "onlyOne",
  },
  {
    label2: "Nhu động ruột khác",
    hint: [],
    key: "ctNhuDongRuot",
  },
  {
    label2: "Tình trạng tiêu hóa", // Added based on OCR
    key: "tinhTrangTieuHoa", // Added key
    hint: [
      "(1) Chưa ghi nhận bất thường",
      "(2) Ợ chua",
      "(3) Đầy bụng/ khó tiêu",
      "(4) Nôn ói",
      "(5) Táo bón",
      "(6) Tiêu chảy",
    ], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Chưa ghi nhận bất thường", value: 1 },
      { label: "(2) Ợ chua", value: 2 },
      { label: "(3) Đầy bụng/ khó tiêu", value: 3 },
      { label: "(4) Nôn ói", value: 4 },
      { label: "(5) Táo bón", value: 5 },
      { label: "(6) Tiêu chảy", value: 6 },
    ],
  },
  {
    label2: "Số lượng dịch nôn ói/ tiêu chảy, ... (ml)", // Added based on OCR, child of Tình trạng tiêu hóa logically
    key: "slDich", // Added key
    colSpan2: 2,
  },
  {
    label2: "Màu sắc", // Added based on OCR, child of Tình trạng tiêu hóa logically
    key: "mauSacDich", // Added key
    hintKhac: "mauSac",
  },
  {
    label2: "Tính chất", // Added based on OCR, child of Tình trạng tiêu hóa logically
    key: "tinhChatOi", // Added key
    hintKhac: "tinhChat",
  },
  {
    label2: "Ăn qua đường miệng", // Added based on OCR
    key: "anQuaDuongMieng", // Added key
    hint: ["Ăn được ......% suất ăn"],
  },
  {
    label2: "Ăn qua ống thông", // Added based on OCR
    key: "anQuaOngThong", // Added key for the type of tube
    hint: [
      "(1) Ống thông dạ dày",
      "(2) Hồng tràng ra da",
      "(3) Mở dạ dày ra da",
    ], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Ống thông dạ dày", value: 1 },
      { label: "(2) Hỗng tràng ra da", value: 2 },
      { label: "(3) Mở dạ dày ra da", value: 3 },
    ],
    rowSpan2: 2, // Set rowSpan2 to cover the sub-item (Số lượng thức ăn)
    mode: "onlyOne",
  },
  {
    hint: ["Số lượng thức ăn (ml) x số lần/ 24 giờ"], // Added based on OCR, child of Ăn qua ống thông
    key: "slThucAn", // Added key
    colSpan2: 2,
  },
  {
    label2: "Chế độ ăn, uống", // Added based on OCR
    key: "cheDoAn", // Added key
    hintKhac: "cheDoAnUong",
  },
  {
    label2: "Dinh dưỡng qua tĩnh mạch", // Added based on OCR
    key: "dinhDuongTinhMach", // Added key
    hint: ["(1) Không", "(2) Có"], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Không", value: 1 },
      { label: "(2) Có", value: 2 },
    ],
    mode: "onlyOne",
    isArr: true,
  },
  {
    label2: "Khác",
    key: "tieuHoaKhac",
    hintKhac: "tieuHoaDinhDuongKhac",
    // Changed key for consistency
    // No hint in OCR image for droplist options
  },

  // Tiết niệu

  {
    label1: "Tiết niệu",
    rowSpan1: 9, // Updated rowspan to 8 based on the image (Header + 7 items)
    label2: "Bài tiết", // Added based on OCR
    key: "tinhTrangTietNieu", // Added key
    hint: [
      "(1) Tiểu tự chủ",
      "(2) Tiểu không tự chủ",
      "(3) Tiểu qua ống thông",
    ], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Tiểu tự chủ", value: 1 },
      { label: "(2) Tiểu không tự chủ", value: 2 },
      { label: "(3) Tiểu qua ống thông", value: 3 },
    ],
    mode: "onlyOne",
    isArr: true,
  },
  {
    label2: "Tình trạng ống thông", // Added based on OCR, child of Bài tiết logically
    key: "tinhTrangOngThong", // Added key
    hint: ["(1) Thông tốt", "(2) Bán tắc", "(3) Tắc"], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Thông tốt", value: 1 },
      { label: "(2) Bán tắc", value: 2 },
      { label: "(3) Tắc", value: 3 },
    ],
    rowSpan2: 3,
    mode: "onlyOne",
    isArr: true,
  },
  {
    hint: ["Ngày đặt"], // Added based on OCR, child of Tình trạng ống thông
    key: "ngayDatOngThong", // Added key
    type: "time",
  },
  {
    hint: ["Ngày rút"], // Added based on OCR, child of Tình trạng ống thông
    key: "ngayRutOngThong", // Added key
    type: "time",
  },
  {
    label2: "Số lượng nước tiểu/ thời gian (...ml/..... giờ)", // Updated label based on OCR
    key: "soLuongNuocTieu", // Kept existing key
    colSpan2: 2,
    type: "string",
    // No hint in OCR image for droplist options
  },
  {
    label2: "Màu sắc nước tiểu", // Updated label based on OCR
    key: "mauSacNuocTieu", // Kept existing key
    hint: [
      "(1) Vàng nhạt",
      "(2) Vàng sẫm",
      "(3) Hồng",
      "(4) Đỏ tươi",
      "(5) Xanh",
      "(6) Xá xị",
      "(7) Đỏ nhạt",
    ], // Updated hint based on OCR
    type: "droplist", // Ensure type is droplist
    data: [
      // Updated data based on hint
      { label: "(1) Vàng nhạt", value: 1 },
      { label: "(2) Vàng sẫm", value: 2 },
      { label: "(3) Hồng", value: 3 },
      { label: "(4) Đỏ tươi", value: 4 },
      { label: "(5) Xá xị", value: 5 },
      { label: "(6) Xanh", value: 6 },
      { label: "(7) Đỏ nhạt", value: 7 },
    ],
    mode: "onlyOne",
  },
  {
    label2: "Tính chất", // Added based on OCR
    key: "tinhChatNuocTieu", // Added key
    hint: ["(1) Trong", "(2) Đục", "(3) Lợn cợn"], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Trong", value: 1 },
      { label: "(2) Đục", value: 2 },
      { label: "(3) Lợn cợn", value: 3 },
    ],
    mode: "onlyOne",
    isArr: true,
  },
  {
    label2: "Tình trạng tiểu", // Added based on OCR
    key: "tinhTrangTieu", // Added key
    hint: [
      "(1) Bình thường",
      "(2) Bí tiểu",
      "(3) Tiểu rắt/ buốt",
      "(4) Tiểu máu",
    ], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Bình thường", value: 1 },
      { label: "(2) Bí tiểu", value: 2 },
      { label: "(3) Tiểu rắt/ buốt", value: 3 },
      { label: "(4) Tiểu máu", value: 4 },
    ],
  },
  {
    label2: "Khác",
    key: "tietNieuKhac",
    hintKhac: "tietNieuKhac",
    // Kept existing key
    // No hint in OCR image for droplist options
  },

  // Giấc ngủ

  {
    label1: "Giấc ngủ, nghỉ ngơi", // Kept label
    rowSpan1: 5, // Updated rowspan to 5 based on the image (Header + 4 items)
    label2: "Số giờ ngủ được", // Added based on OCR
    key: "soGioNgu", // Added key
    hint: ["......giờ"],
    type: "int",
  },
  {
    label2: "Tình trạng giấc ngủ", // Updated label based on OCR
    key: "tinhTrangGiacNgu", // Kept existing key
    hint: [
      "(1) Bình thường",
      "(2) Khó ngủ", // Updated hint based on OCR
      "(3) Rối loạn giấc ngủ", // Updated hint based on OCR
      "(4) Mất ngủ", // Updated hint based on OCR
    ], // Updated hint based on OCR
    type: "droplist", // Ensure type is droplist
    data: [
      // Updated data based on hint
      { label: "(1) Bình thường", value: 1 },
      { label: "(2) Khó ngủ", value: 2 },
      { label: "(3) Rối loạn giấc ngủ", value: 3 },
      { label: "(4) Mất ngủ", value: 4 },
    ],
    mode: "onlyOne",
  },
  {
    label2: "Sử dụng thuốc ngủ", // Updated label based on OCR
    key: "suDungThuocNgu", // Changed key slightly for clarity
    hint: ["(1) Không", "(2) Có"], // Updated hint based on OCR
    type: "droplist", // Ensure type is droplist
    data: [
      // Updated data based on hint
      { label: "(1) Không", value: 1 },
      { label: "(2) Có", value: 2 },
    ],
    mode: "onlyOne",
    isArr: true,
  },
  {
    label2: "Nghỉ ngơi", // Added based on OCR
    key: "nghiNgoi", // Added key
    hint: [
      "(1) Nghỉ tại giường",
      "(2) Đi lại nhẹ nhàng quanh giường",
      "(3) Hạn chế gắng sức",
    ], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Nghỉ tại giường", value: 1 },
      { label: "(2) Đi lại nhẹ nhàng quanh giường", value: 2 },
      { label: "(3) Hạn chế gắng sức", value: 3 },
    ],
    mode: "multiple",
    isArr: true,
  },
  {
    label2: "Khác",
    key: "giacNguKhac",
    hintKhac: "giacNguNghiNgoiKhac",
    // No hint in OCR image for droplist options
  },

  // Vệ sinh cá nhân

  {
    label1: "Vệ sinh cá nhân", // Kept label
    rowSpan1: 5, // Updated rowspan to 5 based on the image (Header + 4 items)
    label2: "Khả năng tự chăm sóc", // Updated label based on OCR
    key: "tinhTrangVeSinh", // Added key
    hint: ["(1) Độc lập", "(2) Phụ thuộc 1 phần", "(3) Phụ thuộc hoàn toàn"], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Độc lập", value: 1 },
      { label: "(2) Phụ thuộc 1 phần", value: 2 },
      { label: "(3) Phụ thuộc hoàn toàn", value: 3 },
    ],
    mode: "onlyOne",
    isArr: true,
  },
  {
    label2: "Răng miệng", // Added based on OCR
    key: "rangMieng", // Added key
    hint: ["(1) Sạch", "(2) Không sạch", "(3) Răng giả"], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Sạch", value: 1 },
      { label: "(2) Không sạch", value: 2 },
      { label: "(3) Răng giả", value: 3 },
    ],
  },
  {
    label2: "Thân thể", // Added based on OCR
    key: "thanThe", // Added key
    hint: ["(1) Sạch", "(2) Không sạch"], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Sạch", value: 1 },
      { label: "(2) Không sạch", value: 2 },
    ],
    mode: "onlyOne",
    isArr: true,
  },
  {
    label2: "Tóc", // Added based on OCR
    key: "toc", // Added key
    hint: ["(1) Sạch", "(2) Không sạch"], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Sạch", value: 1 },
      { label: "(2) Không sạch", value: 2 },
    ],
    mode: "onlyOne",
    isArr: true,
  },
  {
    label2: "Khác",
    key: "veSinhCaNhanKhac",
    hintKhac: "veSinhCaNhanKhac",
    // No hint in OCR image for droplist options
  },
  // Thần kinh

  {
    label1: "Thần kinh", // Kept label
    rowSpan1: 6, // Updated rowspan to 6 based on the image (Header + 5 items)
    label2: "Lời nói", // Updated label based on OCR
    key: "loiNoi", // Added key
    hint: ["(1) Bình thường", "(2) Nói khàn", "(3) Mất tiếng"], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Bình thường", value: 1 },
      { label: "(2) Nói khàn", value: 2 },
      { label: "(3) Mất tiếng", value: 3 },
    ],
    mode: "onlyOne",
    isArr: true,
  },
  {
    label2: "Tình trạng yếu liệt", // Updated label based on OCR
    key: "tinhTrangThanKinh", // Added key
    hint: [
      "(1) Không yếu liệt",
      "(2) YL toàn thân",
      "(3) YL ½ người trái", // Corrected hint based on OCR
      "(4) YL ½ người phải",
      "(5) YL hai chi dưới",
    ], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Không yếu liệt", value: 1 },
      { label: "(2) YL toàn thân", value: 2 },
      { label: "(3) YL ½ người trái", value: 3 },
      { label: "(4) YL ½ người phải", value: 4 },
      { label: "(5) YL hai chi dưới", value: 5 },
    ],
    mode: "onlyOne",
    isArr: true,
  },
  {
    label2: "Rối loạn cảm giác (tay chân)", // Updated label based on OCR
    key: "roiLoanCamGiac", // Added key
    hint: ["(1) Không", "(2) Tê bì", "(3) Giảm cảm giác", "(4) Mất cảm giác"], // Updated hint based on OCR
    type: "droplist", // Ensure type is droplist
    data: [
      // Updated data based on hint
      { label: "(1) Không", value: 1 },
      { label: "(2) Tê bì", value: 2 },
      { label: "(3) Giảm cảm giác", value: 3 },
      { label: "(4) Mất cảm giác", value: 4 },
    ],
    mode: "onlyOne",
    isArr: true,
  },
  {
    label2: "Dấu hiệu khác",
    key: "thanKinhKhac", // Added key
    hint: [
      "(1) Run",
      "(2) Co giật",
      "(3) Múa giật",
      "(4) Loạn động",
      "(5) Động kinh",
      "(6) Chóng mặt",
    ], // Updated hint based on OCR
    type: "droplist", // Ensure type is droplist
    data: [
      // Updated data based on hint
      { label: "(1) Run", value: 1 },
      { label: "(2) Co giật", value: 2 },
      { label: "(3) Múa giật", value: 3 },
      { label: "(4) Loạn động", value: 4 },
      { label: "(5) Động kinh", value: 5 },
      { label: "(6) Chóng mặt", value: 6 },
    ],
    rowSpan2: 2, // Set rowSpan2 to cover the sub-item (Thời gian kéo dài)
    mode: "onlyOne",
  },
  {
    hint: ["Thời gian kéo dài (giây, phút, giờ)"], // Added based on OCR, child of Dấu hiệu khác
    key: "thoiGianKeoDai", // Added key
    type: "time",
  },
  {
    label2: "Khác",
    key: "ctThanKinh",
    hintKhac: "thanKinhKhac",
    // No hint in OCR image for droplist options
  },

  // Xương khớp

  {
    label1: "Xương khớp, Vận động, PHCN", // Kept label
    rowSpan1: 4, // Updated rowspan to 4 based on the image (Header + 3 items)
    label2: "Tầm vận động", // Updated label based on OCR
    key: "tamVanDong", // Kept existing key
    hint: ["(1) Không hạn chế", "(2) Có hạn chế"], // Kept existing hint
    type: "droplist", // Ensure type is droplist
    data: [
      // Ensure data is correct based on hint
      { label: "(1) Không hạn chế", value: 1 },
      { label: "(2) Có hạn chế", value: 2 },
    ],
    mode: "onlyOne",
  },
  {
    label2: "Xương khớp", // Updated label based on OCR (for condition)
    key: "xuongKhop", // Added new key for consistency
    hint: [
      "(1) Bình thường",
      "(2) Sưng/viêm",
      "(3) Biến dạng",
      "(4) Cứng khớp",
    ], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Bình thường", value: 1 },
      { label: "(2) Sưng/viêm", value: 2 },
      { label: "(3) Biến dạng", value: 3 },
      { label: "(4) Cứng khớp", value: 4 },
    ],
  },
  {
    label2: "Vị trí", // Updated label based on OCR (for position)
    key: "viTriTonThuongXuongKhop",
    hintKhac: "xuongKhopViTri",
  },
  {
    label2: "Khác",
    key: "ctXuongKhop", // Changed key for consistency
    hintKhac: "xuongKhopKhac",
  },
  {
    label1: "Theo dõi khác",
    key: "khungDau", // Changed key for consistency
    listTr: LIST_THEO_DOI_KHAC,
    colSpan1: 3,
  },
  {
    label1: "Theo dõi khác",
    rowSpan1: 3,
    label2: "Nguy cơ té ngã (Morse)", // Added based on OCR
    key: "nguyCoTeNgaMorse", // Added key
    hint: ["(1) Không có nguy cơ", "(2) Thấp", "(3) Trung bình", "(4) Cao"], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Không có nguy cơ", value: 1 },
      { label: "(2) Thấp", value: 2 },
      { label: "(3) Trung bình", value: 3 },
      { label: "(4) Cao", value: 4 },
    ],
    mode: "onlyOne",
    isArr: true,
  },
  {
    label2: "Nguy cơ loét do tì đè (Braden)", // Added based on OCR
    key: "nguyCoLoetBraden", // Added key
    hint: [
      "(1) Không có nguy cơ",
      "(2) Thấp",
      "(3) Trung bình",
      "(4) Cao",
      "(5) Rất cao",
    ], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Không có nguy cơ", value: 1 },
      { label: "(2) Thấp", value: 2 },
      { label: "(3) Trung bình", value: 3 },
      { label: "(4) Cao", value: 4 },
      { label: "(5) Rất cao", value: 5 },
    ],
    mode: "onlyOne",
    isArr: true,
  },
  {
    label2: "Cảnh báo sớm (NEWS2)", // Added based on OCR
    key: "canhBaoSom2", // Kept existing key
    hint: ["(1) Thấp", "(2) Trung bình - thấp", "(3) Trung bình", "(4) Cao"], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Thấp", value: 1 },
      { label: "(2) Trung bình - thấp", value: 2 },
      { label: "(3) Trung bình", value: 3 },
      { label: "(4) Cao", value: 4 },
    ],
    mode: "onlyOne",
    isArr: true,
  },
  {
    label1: "Vết thương",
    key: "khungVetThuong", // Changed key for consistency
    listTr: LIST_VET_THUONG,
    colSpan1: 3,
  },
  {
    label1: "Dẫn lưu / lỗ mở ra",
    key: "khungDanLuu", // Changed key for consistency
    listTr: LIST_DAN_LUU,
    colSpan1: 3,
  },
  {
    label1: "Ống thông mạch máu",
    key: "khungOngThong", // Changed key for consistency\
    listTr: LIST_ONG_THONG_MACH,
    colSpan1: 3,
  },

  {
    label1: "Mở khí quản",
    rowSpan1: 5, // Updated rowspan to 5 based on the image (Header + 4 items)
    label2: "Hoạt động", // Added based on OCR
    key: "hoatDong", // Added key
    hint: ["(1) Thông tốt", "(2) Bán tắc", "(3) Tắc"], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Thông tốt", value: 1 },
      { label: "(2) Bán tắc", value: 2 },
      { label: "(3) Tắc", value: 3 },
    ],
    mode: "onlyOne",
    isArr: true,
  },
  {
    label2: "Chân MKQ", // Added based on OCR
    key: "chanMkq", // Added key
    hint: ["(1) Khô sạch", "(2) Đỏ", "(3) Tiết dịch"], // Added hint based on OCR
    type: "droplist", // Added type droplist
    data: [
      // Added data based on hint
      { label: "(1) Khô sạch", value: 1 },
      { label: "(2) Đỏ", value: 2 },
      { label: "(3) Tiết dịch", value: 3 },
    ],
  },
  {
    label2: "Ngày đặt", // Added based on OCR
    key: "ngayDatKhiQuan", // Added key
    hint: [],
    type: "time",
  },
  {
    label2: "Ngày rút", // Added based on OCR
    key: "ngayRutKhiQuan", // Added key
    hint: [],
    type: "time",
  },
  {
    label2: "Khác",
    key: "ctKhiQuan", // Added key
    hintKhac: "moKhiQuanKhac",
  },
  {
    label1: "Ghi chú",
    key: "ghiChu", // Added key
    colSpan1: 2,
    colSpan2: 2,
    hintKhac: "ghiChu",
  },
  {
    label1: "II. CHẨN ĐOÁN ĐIỀU DƯỠNG/ VẤN ĐỀ NGƯỜI BỆNH",
    key: "chanDoanDieuDuong", // Added key
    colSpan1: 3,
    disabled: true,
  },

  {
    label1: "III. LẬP KẾ HOẠCH VÀ CAN THIỆP ĐIỀU DƯỠNG",
    key: "ghiChu", // Added key
    colSpan1: 3,
    disabled: true,
  },
  {
    label1: "(A) Thực hiện thuốc",
    colSpan1: 3,
    disabled: true,
  },

  {
    key: "thucHienThuoc", // Added a key for this field
    hint: LIST_LOAI_THUOC.map((item) => item.label), // Use labels from LIST_LOAI_THUOC as hints
    colSpan3: 3,
    numberItemHint: 4,
  },
  {
    label2: "Khác",
    colSpan2: 2,
    key: "ctThucHienThuoc",
    hintKhac: "thucHienThuocKhac",
  },
  {
    label1: "(B) Thực hiện cận lâm sàng",
    colSpan1: 3,
    disabled: true,
  },
  {
    colSpan3: 3,
    hint: LIST_THUC_HIEN_CAN_LAM_SANG.map((item) => item.label),
    key: "thucHienCls",
    numberItemHint: 4,
  },
  {
    label2: "Khác",
    colSpan2: 2,
    key: "ctThucHienCls",
    hintKhac: "thucHienCanLamSangKhac",
  },
  {
    label1: "(C) Chăm sóc điều dưỡng",
    colSpan1: 3,
    disabled: true,
  },
  {
    hint: LIST_CHAM_SOC_DIEU_DUONG.map((item) => item.label),
    colSpan3: 3,
    key: "chamSocDieuDuong",
    numberItemHint: 4,
  },
  {
    label2: "Khác",
    colSpan2: 2,
    key: "ctChamSocDieuDuong",
    hintKhac: "chamSocDieuDuongKhac",
  },
  {
    label1: "IV. THEO DÕI NGƯỜI BỆNH",
    colSpan1: 3,
    disabled: true,
  },
  {
    hint: LIST_THEO_DOI_NGUOI_BENH.map((item) => item.label), // Header for Patient Monitoring
    colSpan3: 3, // Span across 3 columns
    key: "theoDoiNguoiBenh", // Added key for consistency
    type: "droplist",
    data: LIST_THEO_DOI_NGUOI_BENH,
    numberItemHint: 4,
  },
  {
    label2: "Khác",
    colSpan2: 2,
    key: "ctTheoDoiNguoiBenh",
    hintKhac: "theoDoiNguoiBenhKhac",
  },
  {
    label1: "V. Tư vấn - giáo dục sức khỏe",
    key: "tuVanGiaoDuc",
    colSpan1: 3,
    type: "droplist",
    data: LIST_CO_KHONG,
    labelByKey: "label",
  },
  {
    label1: "VI. Bàn giao",
    colSpan1: 3,
    disabled: true,
  },
  {
    label1: "Theo dõi tình trạng người bệnh",
    colSpan1: 3,
    key: "theoDoiTinhTrang",
    type: "droplist",
    data: LIST_CO_KHONG,
    labelByKey: "label",
  },
  {
    label1: "Các y lệnh điều trị và chăm sóc",
    colSpan1: 3,
    key: "ylenhDieuTriChamSoc",
    type: "droplist",
    data: LIST_CO_KHONG,
    labelByKey: "label",
  },
  {
    label1: "Khác",
    colSpan1: 3,
    key: "banGiao",
  },
  {
    label1: "Điều dưỡng thực hiện",
    colSpan1: 3,
    type: "sign",
    signIndex: 1,
  },
  {
    label1: "Điều dưỡng nhận bàn giao",
    colSpan1: 3,
    type: "sign",
    signIndex: 0,
  },
];
