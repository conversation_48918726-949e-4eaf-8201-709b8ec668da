import React, { useCallback, useEffect, useRef, useState } from "react";
import { Main } from "./styled";
import { useTranslation } from "react-i18next";
import { MODE } from "utils/editor-utils";
import RenderTable from "./components/RenderTable";
import { useDispatch } from "react-redux";
import { SettingOutlined } from "@ant-design/icons";
import { Button } from "antd";
import { get } from "lodash";
import { PlusOutlined } from "@ant-design/icons";
import { refConfirm } from "app";
import ModalSelectSinhHieu from "../Components/ModalSelectSinhHieu";

const PhieuChamSocCap23CR = (props) => {
  const { t } = useTranslation();
  const [state, _setState] = useState({
    dsTheoDoi: [{}],
  });

  const [dataCopy, setDataCopy] = useState();

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const refValueForm = useRef();
  const refModalSinhHieu = useRef();

  const { component, mode, form = {}, formChange } = props;
  const itemProps = component.props || {};
  const init = useDispatch().component.init;

  const handleFocus = () => {
    if (mode === MODE.config) {
      init(component);
    }
  };

  useEffect(() => {
    if (form) {
      refValueForm.current = form.dsTheoDoi || [{}];
      refValueForm.current.forEach((item) => {
        item.dsChiTiet = [{}, {}, {}, {}, {}].map((el, index) => {
          const element = get(item, `dsChiTiet[${index}]`, {});

          if (element?.chiSoSong) {
            element.chiSoSong.huyetAp =
              element.chiSoSong.huyetApTamThu &&
              element.chiSoSong.huyetApTamTruong
                ? `${element.chiSoSong.huyetApTamThu}/${element.chiSoSong.huyetApTamTruong}`
                : "";
          }

          if (element?.chiSoSong?.thoiGianThucHien) {
            element.thoiGianThucHien = element?.chiSoSong?.thoiGianThucHien;
          }

          //set khoa chỉ định cho sinh hiệu mặc định
          if (element?.chiSoSong && !element?.chiSoSong?.khoaChiDinhId) {
            element.chiSoSong.khoaChiDinhId = form?.khoaChiDinhId;
          }

          element.veh = `${element.ve || ""}/${element.vh || ""}`;
          return element;
        });
      });
      setState({
        dsTheoDoi: refValueForm.current,
      });
    }
  }, [JSON.stringify(form || {})]);

  const handleAdd = useCallback(() => {
    refValueForm.current = [
      ...refValueForm.current,
      {
        dsChiTiet: [{}, {}, {}, {}, {}],
      },
    ];
    setState({
      dsTheoDoi: refValueForm.current,
    });
    formChange["dsTheoDoi"](refValueForm.current);
  }, [state.dsTheoDoi, formChange]);

  const handleRemove = useCallback(
    (index) => () => {
      refConfirm.current &&
        refConfirm.current.show(
          {
            title: t("common.thongBao"),
            content: `${t("common.banCoChacMuonXoa")}`,
            cancelText: t("common.huy"),
            okText: t("common.dongY"),
            classNameOkText: "button-warning",
            showBtnOk: true,
            typeModal: "warning",
          },
          () => {
            const dsTheoDoi = refValueForm.current.filter(
              (item, idx) => idx !== index
            );
            refValueForm.current = dsTheoDoi;
            setState({
              dsTheoDoi,
            });
            formChange["dsTheoDoi"](dsTheoDoi);
          }
        );
    },
    [state.dsTheoDoi, formChange]
  );

  return (
    <Main
      className="phieu-cham-soc-cap23"
      mode={mode}
      itemProps={itemProps}
      data-type="phieu-cham-soc-cap23"
    >
      {mode === MODE.config && (
        <>
          <div className="table-config">
            <Button
              icon={<SettingOutlined />}
              onClick={handleFocus}
              size={"small"}
            />
          </div>
        </>
      )}
      {state.dsTheoDoi.map((item, index) => {
        return (
          <div
            className="form"
            style={{
              pageBreakAfter:
                index === state.dsTheoDoi.length - 1 ? "avoid" : "always",
            }}
            key={index}
          >
            <RenderTable
              key={index}
              mode={mode}
              tableIndex={index}
              refValueForm={refValueForm.current}
              item={item}
              formChange={formChange}
              onRemove={handleRemove}
              itemProps={itemProps}
              form={form}
              setDataCopy={setDataCopy}
              dataCopy={dataCopy}
              refModalSinhHieu={refModalSinhHieu}
            />
          </div>
        );
      })}
      {mode !== MODE.config && (
        <Button
          className="btn-add"
          icon={<PlusOutlined />}
          onClick={handleAdd}
          size={"small"}
        />
      )}
      <ModalSelectSinhHieu ref={refModalSinhHieu} />
    </Main>
  );
};

PhieuChamSocCap23CR.propTypes = {};

export default PhieuChamSocCap23CR;
