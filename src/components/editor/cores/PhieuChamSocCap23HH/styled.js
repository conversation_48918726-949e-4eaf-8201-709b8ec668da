import styled, { createGlobalStyle } from "styled-components";
export const GlobalStyle = createGlobalStyle``;
export const Main = styled.div`
  .form {
    @media screen {
      margin-top: 20px;
    }
  }
  table {
    width: 100%;
    margin-top: 50px;

    td {
      .hint {
        /* width: 200px;
        min-width: 200px;
        max-width: 200px; */
        text-align: center;

        > span {
          margin-right: 10px;
          display: inline-block;
          white-space: nowrap;
        }
      }
      .hint-with-number {
        width: unset;
        max-width: unset;
        display: flex;
        flex-wrap: wrap;
        text-align: left;
        span {
          width: calc(25%);
          margin-right: 0px;
        }
      }
    }
  }
  table,
  td,
  th {
    border: 1px solid;
    border-collapse: collapse;
  }
  & table {
    page-break-inside: auto;
  }
  & tr {
    page-break-inside: avoid;
    page-break-after: auto;
    font-size: 12px;
  }
  .copy-paste-element {
    display: none;
  }
  .col-element:hover {
    .copy-paste-element {
      display: block !important;
    }
  }
  .bold {
    font-weight: 700;
  }
  .flex {
    display: flex;
  }
  .box {
    width: 16px;
    height: 16px;
    border: 1px solid #000;
    margin-right: 5px;
  }
  .flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .al-center {
    align-items: center;
  }
  .title-center {
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
    font-size: 20px;
    font-weight: 700;
    align-items: center;
  }
  .ic-remove {
    position: absolute;
    right: 0px;
    top: 0px;
    :hover {
      cursor: pointer;
    }
    @media print {
      display: none;
    }
  }
  .ic-remove-khung {
    left: -20px;
    top: 0px;
  }
  .btn-add-khung {
    position: absolute;
    left: -20px;
    top: 20px;
    width: 20px;
    height: 20px;
  }
  .center {
    text-align: center;
  }
  @media print {
    .btn-add {
      display: none;
    }
  }
`;
export const ModalModalSelectStyled = styled.div`
  padding: 20px;
  .ant-select {
    width: 100%;
  }
  .label {
    margin-top: 10px;
  }
`;
export const PopoverStyled = styled.div`
  height: 400px;
  overflow: auto;
  .active {
    background: #7b92ac;
    color: #fff;
  }
  .item {
    min-width: 100px;
    border-radius: 8px;
    text-align: left;
    margin: 5px;
    display: flex;
    padding: 2px 5px;
    :hover {
      cursor: pointer;
    }
  }
`;
