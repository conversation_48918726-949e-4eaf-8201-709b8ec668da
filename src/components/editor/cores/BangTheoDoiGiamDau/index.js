import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { Main } from "./styled";
import { useTranslation } from "react-i18next";
import { MODE } from "utils/editor-utils";
import RenderTable from "./components/RenderTable";
import { useDispatch } from "react-redux";
import { SettingOutlined } from "@ant-design/icons";
import { Button, message, Pagination } from "antd";
import { cloneDeep, get } from "lodash";
import { refConfirm } from "app";
import { SVG } from "assets";

// import ModalSelectSinhHieu from "../Components/ModalSelectSinhHieu";

const LIST_KEY_CHANGE = {
  duLieu1_TocDoMau: "tocDo",
  duLieu1_tocDoPCA: "tocDo",
  duLieu1_LieuTuDong: "lieuBoSungTuDong",
  duLieu1_ThoiGianNgatQuang: "thoiGian<PERSON>gat<PERSON><PERSON>",
  duLieu1_PCAbs: "pcaLieuBoSung",
  duLieu1_GiamDauPCA: ["pcaLieuBoSung", "duLieu1_PCAbs"],
  duLieu1_PCAThoiGianKhoa: "pcaThoiGianKhoa",
  duLieu1_thoiGianKhoa: ["pcaThoiGianKhoa", "duLieu1_PCAThoiGianKhoa"],
  duLieu1_PCAGioiHan: "gioiHan",
  duLieu1_gioiHan: ["gioiHan", "duLieu1_PCAGioiHan"],
};
const COL = 7;

const BangTheoDoiGiamDau = (props) => {
  const { t } = useTranslation();
  const [state, _setState] = useState({
    dsTheoDoi: [{}],
    currentPage: 1, // Thêm state cho trang hiện tại
  });
  const [dataCopy, setDataCopy] = useState();

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const {
    danhMucThuoc: { onSearchAll: onSearchAllThuoc },
  } = useDispatch();

  const refValueForm = useRef();

  const { component, mode, form = {}, formChange } = props;
  const itemProps = component.props || {};
  const init = useDispatch().component.init;

  const handleFocus = () => {
    if (mode === MODE.config) {
      init(component);
    }
  };

  useEffect(() => {
    const getData = () => {
      refValueForm.current = cloneDeep(
        form?.duLieu1?.dsTheoDoi || [
          {
            dsChiTiet: new Array(COL).fill(cloneDeep({})).map((el, index) => ({
              stt: index + 1,
            })),
            tableIndex: 0,
          },
        ]
      );
      const objFirst = refValueForm.current[0]?.dsChiTiet?.[0] || {};
      const {
        tocDoMau,
        tocDoPCA,
        LieuTuDong,
        ThoiGianNgatQuang,
        PCAbs,
        GiamDauPCA,
        PCAThoiGianKhoa,
        thoiGianKhoa,
        PCAGioiHan,
        gioiHan,
      } = form?.duLieu1 || {};
      objFirst.tocDo = tocDoMau || tocDoPCA || "";
      objFirst.lieuBoSungTuDong = LieuTuDong || "";
      objFirst.thoiGianNgatQuang = ThoiGianNgatQuang || "";
      objFirst.pcaLieuBoSung = PCAbs || GiamDauPCA || "";
      objFirst.pcaThoiGianKhoa = PCAThoiGianKhoa || thoiGianKhoa || "";
      objFirst.gioiHan = PCAGioiHan || gioiHan || "";

      setState({
        dsTheoDoi: refValueForm.current,
      });
    };
    getData();
    if (formChange) {
      formChange["duLieu1_dsTheoDoi"] &&
        formChange["duLieu1_dsTheoDoi"](refValueForm.current);
    }
  }, [JSON.stringify(form || {}), itemProps, formChange]);

  useEffect(() => {
    const eventMessage = (event = {}) => {
      if (event.data?.TYPE === "CHANGE_VALUE_EDITOR" && event.data.value?.key) {
        const key = event.data.value?.key;
        const value = event.data.value?.value;
        if (LIST_KEY_CHANGE[key]) {
          refValueForm.current = cloneDeep(refValueForm.current);
          let keyObj = LIST_KEY_CHANGE[key];
          let keyCheckValue = null;
          if (Array.isArray(keyObj)) {
            keyObj = keyObj[0];
            keyCheckValue = keyObj[1];
          }
          const objFirst = refValueForm.current[0]?.dsChiTiet?.[0] || {};
          if (keyCheckValue && form.duLieu1[keyCheckValue]) return;
          objFirst[keyObj] = value;
          if (formChange) {
            formChange["duLieu1_dsTheoDoi"] &&
              formChange["duLieu1_dsTheoDoi"](refValueForm.current);
          }
          setState({
            dsTheoDoi: refValueForm.current,
          });
        }
      }
    };
    window.addEventListener("message", eventMessage, false);
    return () => {
      window.removeEventListener("message", eventMessage);
    };
  }, [formChange, form, refValueForm.current]);
  useEffect(() => {
    onSearchAllThuoc({ page: "", size: "", dsLoaiDichVu: "90,120" });
  }, []);
  const onAdd = () => {
    const maxStt =
      Math.max(
        ...refValueForm.current
          .map((el) => el)
          .map((el) => el.dsChiTiet.map((e) => e.stt))
          .flat(Infinity)
      ) || 1;
    const maxSttBang =
      Math.max(...refValueForm.current.map((el) => el.stt)) || 0;

    refValueForm.current = [
      ...refValueForm.current,
      {
        dsChiTiet: new Array(COL)
          .fill({})
          .map((_, idx) => ({ stt: maxStt + idx + 1 })),
        tableIndex: maxSttBang + 1,
      },
    ];
    const dsTheoDoi = refValueForm.current;
    setState({
      dsTheoDoi,
    });
    formChange["duLieu1_dsTheoDoi"](refValueForm.current);
  };
  const checkSign = (position) => {
    let dsViTriKy = [];
    const chanKyBatDat = position * COL;
    const chanKyKetThuc = chanKyBatDat + (COL - 1);
    for (let index = chanKyBatDat; index <= chanKyKetThuc; index++) {
      dsViTriKy.push(index);
    }
    return (form.lichSuKy?.dsChuKy || []).some((el) => {
      return dsViTriKy.includes(el.viTri);
    });
  };
  const handleRemove = (index) => () => {
    if (checkSign(index)) {
      message.error("Bảng đã được ký xin vui lòng hủy ký trước khi xóa bảng!");
      return;
    }
    refConfirm.current &&
      refConfirm.current.show(
        {
          title: t("common.thongBao"),
          content: `${t("common.banCoChacMuonXoa")}`,
          cancelText: t("common.huy"),
          okText: t("common.dongY"),
          classNameOkText: "button-warning",
          showBtnOk: true,
          typeModal: "warning",
        },
        () => {
          refValueForm.current = refValueForm.current.filter(
            (item, idx) => item.tableIndex !== index
          );
          const dsTheoDoi = refValueForm.current.slice(0, state.size);
          setState({
            dsTheoDoi,
          });
          formChange["duLieu1_dsTheoDoi"](refValueForm.current);
        }
      );
  };
  const onChangeForm = () => {
    formChange["duLieu1_dsTheoDoi"] &&
      formChange["duLieu1_dsTheoDoi"](refValueForm.current);
  };

  return (
    <Main
      className="phieu-theo-doi-va-cham-soc-nb"
      mode={mode}
      itemProps={itemProps}
      data-type="phieu-theo-doi-va-cham-soc-nb"
    >
      {mode === MODE.config && (
        <>
          <div className="table-config">
            <Button
              icon={<SettingOutlined />}
              onClick={handleFocus}
              size={"small"}
            />
          </div>
        </>
      )}
      {(state.dsTheoDoi || []).map((item, index) => (
        <RenderTable
          key={item.tableIndex}
          mode={mode}
          tableIndex={item.tableIndex}
          refValueForm={refValueForm.current}
          item={item}
          formChange={formChange}
          onRemove={handleRemove}
          itemProps={itemProps}
          form={form}
          onChangeForm={onChangeForm}
          COL={COL}
          dataCopy={dataCopy}
          setDataCopy={setDataCopy}
        ></RenderTable>
      ))}

      <SVG.IcAdd className="icon-add hide-print-btn" onClick={onAdd} />
    </Main>
  );
};

BangTheoDoiGiamDau.propTypes = {};

export default BangTheoDoiGiamDau;
