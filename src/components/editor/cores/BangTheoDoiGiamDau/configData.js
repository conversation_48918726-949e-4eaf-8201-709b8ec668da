import React from "react";

export const DATA_TABLE = [
  // Nhóm: Thời gian
  { label1: "Thời gian", key: "thoiGian<PERSON>hucH<PERSON>", colSpan1: 2, type: "time" },

  // Nhóm: Thông tin thuốc giảm đau
  {
    label1: "Thông tin thuốc giảm đau",
    key: "tocDo",
    rowSpan1: 6,
    label2: "Tốc độ (ml/h)",
    type: "number",
    colSpan1: 1,
  },
  {
    label2: "Liều tự động (ml/lần)",
    key: "lieuBoSungTuDong",
    type: "number",
    colSpan2: 1,
  },
  {
    label2: "Thời gian ngắt quãng (p)",
    key: "thoiGian<PERSON>gatQuang",
    type: "number",
    colSpan2: 1,
  },
  {
    label2: "PCA liề<PERSON> b<PERSON> (ml/lần)",
    key: "pcaLieuBoSung",
    type: "number",
    colSpan2: 1,
  },
  {
    label2: "PCA thời gian khóa (p)",
    key: "pcaThoiGianKhoa",
    type: "number",
    colSpan2: 1,
  },
  { label2: "Giới hạn (ml/h)", key: "gioiHan", type: "number", colSpan2: 1 },

  // Các mục đơn lẻ
  { label1: "Mạch (lần/phút)", key: "mach", type: "number", colSpan1: 2 },
  { label1: "Huyết áp (mmHg)", key: "huyetAp", type: "text", colSpan1: 2 },
  {
    label1: "Nhịp thở (lần/phút)",
    key: "nhipTho",
    type: "number",
    colSpan1: 2,
  },
  { label1: "SpO2 (%)", key: "spo2", type: "number", colSpan1: 2 },

  // Nhóm: Điểm đau VAS
  {
    label1: "Điểm đau VAS",
    key: "vasNghi",
    rowSpan1: 2,
    label2: "Khi nghỉ",
    colSpan1: 1,
    valueDefault: "< 4",
  },
  {
    label2: "Khi vận động",
    key: "vasVanDong",
    colSpan1: 1,
    valueDefault: "< 4",
  },

  // Các mục đơn lẻ
  {
    label1: "Điểm vận động Bromage",
    key: "diemVanDongBromage",
    type: "droplist",
    data: [
      { label: "0", value: 1 },
      { label: "1", value: 2 },
      { label: "2", value: 3 },
      { label: "3", value: 4 },
    ],
    mode: "onlyOne",
    valueDefault: "1",
  },
  {
    label1: "Điểm an thần",
    key: "diemAnThan",
    type: "string",
    valueDefault: "Không",
  },

  // Nhóm: Tác dụng không mong muốn
  {
    label1: "Tác dụng không mong muốn",
    key: "biTieu",
    rowSpan1: 4,
    label2: "Bí tiểu",
    colSpan1: 1,
    valueDefault: "Không",
  },
  {
    label2: "Rối loạn cảm giác",
    key: "roiLoanCamGiac",
    type: "text",
    colSpan2: 1,
    valueDefault: "Không",
  },
  {
    label2: "Nôn hoặc buồn nôn",
    key: "nonBuonNon",
    type: "text",
    colSpan1: 1,
    valueDefault: "Không",
  },
  {
    label2: "Khác",
    key: "khac",
    type: "text",
    colSpan1: 1,
    valueDefault: "Không",
  },

  // Các mục đơn lẻ
  {
    label1: "Điều dưỡng theo dõi",
    key: "dieuDuongTheoDoiId",
    type: "sign",
    keyData: "listDieuDuong",
  },
  {
    label1: "Bác sĩ theo dõi",
    key: "bacSiTheoDoiId",
    type: "sign",
    keyData: "listAllNhanVien",
  },
];
