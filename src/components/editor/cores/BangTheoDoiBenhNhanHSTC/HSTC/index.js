import React, {
  useState,
  useEffect,
  useRef,
  useMemo,
  useCallback,
} from "react";
import { cloneDeep, get, isEmpty } from "lodash";
import { message } from "antd";
import { SIZE } from "../VitalSigns/utils/vital-signs/constants";
import VitalSigns from "components/editor/cores/BangTheoDoiBenhNhanGMHS/GMHS/VitalSigns";
import { NHIETS, MACHS, BLOOD_PRESSURE } from "utils/vital-signs/constants";
import { handleBloodPressure } from "utils/vital-signs/canvas-utils";
import { Main } from "./styled";

import DropDownList from "../DropDownList";
import ToggleValue from "../ToggleValue";
import { DeboundInput } from "components/editor/config";
import { useTranslation } from "react-i18next";
import { updateObject } from "utils";
import { Select } from "components";
import { useListAll, useQueryString } from "hooks";
import ModalChangeDate from "../ModalChangeDate";
import moment from "moment";
import ImageSign from "../../ImageSign";
import { SVG } from "assets";
import { MODE } from "utils/editor-utils";
import { LOAI_DICH_VU } from "constants/index";

const COL_NUM = 1000;

const HSTC = (
  {
    itemProps,
    form,
    formChange,
    block,
    mode,
    khungIndex,
    onChangeForm,
    handleRemove,
    dataForm,
    ...props
  },
  refs
) => {
  const [chiDinhTuLoaiDichVu] = useQueryString("chiDinhTuLoaiDichVu", "");
  const [dsChiDinhTuLoaiDichVu] = useQueryString("dsChiDinhTuLoaiDichVu", "");
  const { t } = useTranslation();
  const [listAllNhanVien] = useListAll("nhanVien", {}, true);
  const DOM_MAUS = [
    { id: 1, ten: t("editor.xanh") },
    { id: 2, ten: t("editor.trang") },
    { id: 3, ten: t("editor.duc") },
    { id: 4, ten: t("editor.mau") },
    { id: 5, ten: t("editor.vang") },
    { id: 5, ten: t("editor.hong") },
  ];
  const DOM_SOLUONGS = [
    { id: 1, ten: t("editor.nhieu") },
    { id: 2, ten: t("editor.vua") },
    { id: 3, ten: t("editor.it") },
  ];
  const PHAN_MAUS = [
    { id: 1, ten: t("editor.den") },
    { id: 2, ten: t("editor.vang") },
    { id: 3, ten: t("editor.xanh") },
    { id: 4, ten: t("editor.song") },
    { id: 5, ten: t("editor.mau") },
  ];
  const PHAN_TINH_CHAT = [
    { id: 1, ten: t("editor.ran") },
    { id: 2, ten: t("editor.long") },
    { id: 3, ten: t("editor.bot") },
    { id: 4, ten: t("editor.binhThuong") },
  ];
  const TU_THE = [
    { id: 1, ten: t("editor.phai") },
    { id: 2, ten: t("editor.trai") },
    { id: 3, ten: t("editor.ngua") },
    { id: 4, ten: t("editor.sap") },
    { id: 5, ten: t("editor.ngoiDay") },
  ];
  const LEFT_VITAL_SIGNS = 220;
  const TOTAL_COL = 12;
  const LEFT_COLUMN_ITEM_MRLEFT = 30;
  const refState = useRef({
    values: [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}],
    rangeBloodPressure: BLOOD_PRESSURE[0].listShow,
  });
  const [state, _setState] = useState(refState.current);
  const setState = (data = {}) => {
    _setState((state) => {
      refState.current = { ...refState.current, ...data };
      return refState.current;
    });
  };

  const refMain = useRef(null);
  const addDefaultCSS = (dataCSS) => {
    let _addFields = {};
    if (!dataCSS.khoaChiDinhId) {
      _addFields.khoaChiDinhId = dataForm?.khoaChiDinhId;
    }
    if (!dataCSS.nbDotDieuTriId) {
      _addFields.nbDotDieuTriId = dataForm?.nbDotDieuTriId;
    }
    if (!dataCSS.chiDinhTuLoaiDichVu) {
      _addFields.chiDinhTuLoaiDichVu =
        chiDinhTuLoaiDichVu ||
        dsChiDinhTuLoaiDichVu.split(",")[0] ||
        LOAI_DICH_VU.NOI_TRU;
    }

    return _addFields;
  };

  useEffect(() => {
    if (form) {
      let values = form;
      values.forEach((item) => {
        item.tongVao = (item.dich || 0) + (item.an || 0) + (item.vaoKhac || 0);
        item.tongRa = (item.tieu || 0) + (item.raKhac || 0);
        item.vaoTruRa = item.tongVao - item.tongRa;

        if (item?.chiSoSong?.id) {
          item.chiSoSong = {
            ...item.chiSoSong,
            ...addDefaultCSS(item.chiSoSong),
          };
        }
      });

      setState({
        values,
        rangeBloodPressure: getRangeBloodPressure(values),
      });
    }
  }, [form]);

  const canvasWidth = useMemo(() => {
    const tableWidth = block?.width || refMain.current?.clientWidth;
    return tableWidth - LEFT_VITAL_SIGNS;
  }, [block, refMain.current]);

  const columnWidth = useMemo(() => {
    return (canvasWidth - 12) / TOTAL_COL - 1;
  }, [block, refMain.current]);

  const canvasHeight = useMemo(() => {
    return SIZE.rowHeight * 80 + SIZE.headerHeight;
  }, []);
  const refTimeout = useRef();
  const onChangeHuyetApp = (item, index) => (value) => {
    const keyHATamThu = `chiSoSong.huyetApTamThu`;
    const keyHATamTruong = `chiSoSong.huyetApTamTruong`;
    if (!value) {
      onChangeValue(index, keyHATamThu)(null);
      onChangeValue(index, keyHATamTruong)(null);
      return;
    }
    if (refTimeout.current) clearTimeout(refTimeout.current);
    refTimeout.current = setTimeout(
      (e) => {
        if (e.split("/").length - 1 !== 1) {
          message.error("Nhập sai quy tắc. Nhập đúng ví dụ: 120/90 ");
          return;
        }
        const arr = e.split("/");
        const huyetApTamThu = +arr[0];
        const huyetApTamTruong = +arr[1];
        if (huyetApTamThu < huyetApTamTruong) {
          message.error("Huyết áp tâm thu cần lớn hơn huyết áp tâm trương");
        } else {
          onChangeValue(index, keyHATamThu)(huyetApTamThu);
          onChangeValue(index, keyHATamTruong)(huyetApTamTruong);
        }
      },
      700,
      value
    );
  };

  const onChangeValue =
    (index, type) =>
    (value = "") => {
      if (!formChange) return;

      if (
        type.indexOf("chiSoSong.") >= 0 &&
        !state.values[index].chiSoSong?.id
      ) {
        if (!state.values[index].chiSoSong) {
          state.values[index].chiSoSong = {};
        }
        const _addFields = addDefaultCSS(state.values[index].chiSoSong);

        state.values[index].chiSoSong = {
          ...state.values[index].chiSoSong,
          ..._addFields,
        };

        Object.keys(_addFields).forEach((key) => {
          updateObject(state.values[index], key, _addFields[key]);
        });
      }

      updateObject(state.values[index], type, value);
      // formChange["dsTheoDoi"] && formChange["dsTheoDoi"](state.values);
      onChangeForm(khungIndex, state.values);
      if (
        [
          "dich",
          "an",
          "vaoKhac",
          "tieu",
          "raKhac",
          "raGhiChu",
          "chiSoSong.mach",
          "chiSoSong.huyetApTamThu",
          "chiSoSong.huyetApTamThu",
          "chiSoSong.nhietDo",
        ].includes(type)
      ) {
        state.values.forEach((item) => {
          item.tongVao =
            (item.dich || 0) + (item.an || 0) + (item.vaoKhac || 0);
          item.tongRa = (item.tieu || 0) + (item.raKhac || 0);
          item.vaoTruRa = item.tongVao - item.tongRa;
        });

        setState({
          values: cloneDeep(state.values),
        });
      }
    };

  const getRangeBloodPressure = (values) => {
    const cloneValues = cloneDeep(values);
    const indexOfLastItemHasValue =
      cloneValues.length -
      1 -
      cloneValues.reverse().findIndex((item) => !!item.huyetAp);
    const newValue = handleBloodPressure(
      values[indexOfLastItemHasValue] && values[indexOfLastItemHasValue].huyetAp
    );

    const listShow =
      BLOOD_PRESSURE.find(
        (item) => item.min <= newValue.systolic && newValue.systolic <= item.max
      ) &&
      BLOOD_PRESSURE.find(
        (item) => item.min <= newValue.systolic && newValue.systolic <= item.max
      ).listShow;
    if (!isEmpty(listShow)) {
      return listShow || [];
    } else {
      return BLOOD_PRESSURE[0].listShow;
    }
  };

  const isDisable = (index) => {
    return false;
  };

  const renderSignature = useMemo(() => {
    return Array(TOTAL_COL)
      .fill({})
      .map((item, index) => {
        return (
          <td className="col-lv4" key={index} rowSpan={3}>
            <ImageSign
              mode={mode}
              component={{
                props: {
                  hideIconSign: true,
                  fontSize: 12,
                  capKy: 1,
                  loaiKy: 1,
                  width: 45,
                  height: 40,
                  showCa: false,
                  isMultipleSign: true,
                  showPatientSign: false,
                  viTri: khungIndex * 12 + index + 1,
                  customText: "Ký",
                  allowReset: true,
                  contentAlign: "center",
                  dataSign: {
                    id: dataForm.soPhieu,
                    soPhieu: dataForm.soPhieu,
                    lichSuKyId: dataForm?.lichSuKy?.id,
                  },
                },
              }}
              form={{
                ...form,
                lichSuKy: dataForm.lichSuKy,
              }}
            />
          </td>
        );
      });
  }, [form, formChange, itemProps]);

  const onChangeDieuDuong = (key) => (value) => {
    formChange[key] && formChange[key](value);
  };
  const renderCaDieuDuong = (label, index) => {
    return (
      <div className={"flex acenter"}>
        {label}
        <Select
          data={listAllNhanVien}
          onChange={onChangeDieuDuong(`dieuDuong${index}Id`)}
          value={get(form, `dieuDuong${index}Id`)}
        ></Select>
      </div>
    );
  };

  const onChangeDate = (index, key) => (value) => {
    const timecurrent = state.values[index].thoiGianThucHien
      ? moment(state.values[index].thoiGianThucHien)
      : moment();

    if (value) {
      const hours = timecurrent.hours();
      const minutes = timecurrent.minutes();
      const date = timecurrent.date();
      const month = timecurrent.month();
      const year = timecurrent.year();
      if (key === "gioThucHien") {
        value.set({
          year,
          months: month,
          date: date,
        });
      } else {
        value.set({
          hours,
          minutes,
        });
      }
      onChangeValue(index, "thoiGianThucHien")(value.format());
    } else {
      onChangeValue(index, "thoiGianThucHien")(null);
    }
  };

  const calculateTabIndex = (idx, rowNo) => (idx + 1) * COL_NUM + rowNo;

  return (
    <Main
      ref={refMain}
      leftColumnWidth={LEFT_VITAL_SIGNS}
      columnWidth={columnWidth}
    >
      <table className={khungIndex ? "tables" : ""}>
        <tbody>
          <tr>
            <td
              colSpan={4}
              className="bold center"
              style={{ position: "relative" }}
            >
              <div className="leftColumnWidth">{t("editor.gio")}</div>
              {mode !== MODE.config && khungIndex ? (
                <SVG.IcDelete
                  className="ic-remove"
                  onClick={handleRemove(khungIndex)}
                ></SVG.IcDelete>
              ) : null}
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4 center" key={index}>
                  <ModalChangeDate
                    value={item.thoiGianThucHien}
                    onChange={onChangeDate(index, "gioThucHien")}
                    format="HH:mm"
                  ></ModalChangeDate>
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={4} rowSpan={2} className="pr">
              <table className="table-left-vital-signs">
                <tbody>
                  <tr className="left-column-row-height">
                    <td className="bold center w75">{t("editor.hUYETAP")}</td>
                    <td className="bold center w75">{t("editor.mACH")}</td>
                    <td className="bold center w75">{t("editor.nHIETĐO")}</td>
                  </tr>
                  <tr style={{ height: "329px" }}>
                    <td className="pr">
                      {state.rangeBloodPressure?.map((item, index) => {
                        return (
                          <div
                            key={index}
                            className="pa"
                            style={{
                              top: 5 + index * SIZE.rowHeight * 10,
                              left: LEFT_COLUMN_ITEM_MRLEFT,
                            }}
                          >
                            {item}
                          </div>
                        );
                      })}
                    </td>
                    <td className="pr">
                      {MACHS.map((item, index) => {
                        return (
                          <div
                            key={index}
                            className="pa"
                            style={{
                              top: 5 + index * SIZE.rowHeight * 10,
                              left: LEFT_COLUMN_ITEM_MRLEFT,
                            }}
                          >
                            {item}
                          </div>
                        );
                      })}
                    </td>
                    <td className="pr">
                      {NHIETS.map((item, index) => {
                        return (
                          <div
                            key={index}
                            className="pa"
                            style={{
                              top: 5 + index * SIZE.rowHeight * 10,
                              left: LEFT_COLUMN_ITEM_MRLEFT,
                            }}
                          >
                            {item}
                          </div>
                        );
                      })}
                    </td>
                  </tr>
                </tbody>
              </table>
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4 center" key={index}>
                  <ModalChangeDate
                    value={item.thoiGianThucHien}
                    onChange={onChangeDate(index, "ngayThucHien")}
                    format="DD/MM"
                  ></ModalChangeDate>
                </td>
              );
            })}
          </tr>
          <tr>
            <td
              style={{
                position: "relative",
                height: `${canvasHeight - 3}px`,
              }}
            >
              <VitalSigns
                canvasWidth={canvasWidth}
                canvasHeight={canvasHeight}
                columnWidth={columnWidth}
                rangeBloodPressure={state.rangeBloodPressure || []}
                values={(state.values || []).map(
                  (item) => item.chiSoSong || {}
                )}
                bonusSize={1.2}
              />
            </td>

            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
          </tr>
          <tr>
            <td
              colSpan={1}
              rowSpan={4}
              className="col-lv0 no-border-right"
            ></td>
            <td colSpan={1} rowSpan={4} className="col-lv1 no-border-left"></td>
            <td colSpan={2} className="col-lv2 bold">
              {t("editor.nhietDo")}
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DeboundInput
                    tabIndex={calculateTabIndex(index, 1)}
                    readOnly={isDisable(index)}
                    size={"small"}
                    step={1}
                    min={34}
                    max={42}
                    value={item.chiSoSong?.nhietDo}
                    onChange={onChangeValue(index, "chiSoSong.nhietDo")}
                    type="number"
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={2} className="col-lv2 bold">
              {t("editor.mach")}
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DeboundInput
                    tabIndex={calculateTabIndex(index, 2)}
                    readOnly={isDisable(index)}
                    size={"small"}
                    step={1}
                    min={20}
                    max={180}
                    value={item.chiSoSong?.mach}
                    onChange={onChangeValue(index, "chiSoSong.mach")}
                    type="number"
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={2} className="col-lv2 bold">
              {t("editor.huyetAp")}
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <div style={{ display: "flex", alignItems: "center" }}>
                    <DeboundInput
                      tabIndex={calculateTabIndex(index, 3)}
                      onChange={onChangeValue(index, "chiSoSong.huyetApTamThu")}
                      size={"small"}
                      value={item?.chiSoSong?.huyetApTamThu}
                      styleMain={{ width: 20 }}
                    />
                    /
                    <DeboundInput
                      label="/"
                      tabIndex={calculateTabIndex(index, 3)}
                      onChange={onChangeValue(
                        index,
                        "chiSoSong.huyetApTamTruong"
                      )}
                      size={"small"}
                      value={item?.chiSoSong?.huyetApTamTruong}
                      styleMain={{ width: 20 }}
                    />
                  </div>
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={2} className="col-lv2 bold red">
              {t("editor.ghiChu")}
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4 red" key={index}>
                  <DeboundInput
                    tabIndex={calculateTabIndex(index, 4)}
                    rows={2}
                    className="red"
                    readOnly={isDisable(index)}
                    value={item.ghiChu}
                    size={"small"}
                    onChange={onChangeValue(index, "ghiChu")}
                    type="textarea"
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={2} rowSpan={3}></td>
            <td colSpan={2} className="col-lv2">
              SPO2
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DeboundInput
                    tabIndex={calculateTabIndex(index, 5)}
                    readOnly={isDisable(index)}
                    size={"small"}
                    value={item.chiSoSong?.spo2}
                    onChange={onChangeValue(index, "chiSoSong.spo2", false)}
                    type="number"
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={2} className="col-lv2">
              {t("editor.nHIPTHO")}
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DeboundInput
                    tabIndex={calculateTabIndex(index, 6)}
                    onChange={onChangeValue(index, "chiSoSong.nhipTho")}
                    size={"small"}
                    value={item.chiSoSong?.nhipTho}
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={2} className="col-lv2">
              GLASSGOW
            </td>

            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DeboundInput
                    tabIndex={calculateTabIndex(index, 7)}
                    readOnly={isDisable(index)}
                    size={"small"}
                    value={item.glassgow}
                    onChange={onChangeValue(index, "glassgow", false)}
                    type="number"
                  />
                </td>
              );
            })}
          </tr>
          {/* -------------------------- */}
          <tr>
            <td colSpan={2} rowSpan={7} className="col-lv0 center bold vamid">
              {t("editor.tHOMAY")}
            </td>
            <td colSpan={2} className="col-lv2">
              MODE
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DeboundInput
                    tabIndex={calculateTabIndex(index, 8)}
                    readOnly={isDisable(index)}
                    size={"small"}
                    value={item.mode}
                    onChange={onChangeValue(index, "mode", false)}
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={2} className="col-lv2">
              TV
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DeboundInput
                    tabIndex={calculateTabIndex(index, 9)}
                    readOnly={isDisable(index)}
                    size={"small"}
                    max={999}
                    value={item.tv}
                    onChange={onChangeValue(index, "tv", false)}
                    type="number"
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={2} className="col-lv2">
              Tần số
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DeboundInput
                    tabIndex={calculateTabIndex(index, 10)}
                    readOnly={isDisable(index)}
                    size={"small"}
                    max={99}
                    value={item.tanSo}
                    onChange={onChangeValue(index, "tanSo", false)}
                    type="number"
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={2} className="col-lv2">
              I/E - I TIME - PEAK LOW
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DeboundInput
                    tabIndex={calculateTabIndex(index, 11)}
                    readOnly={isDisable(index)}
                    size={"small"}
                    value={item.peakLow}
                    onChange={onChangeValue(index, "peakLow", false)}
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={2} className="col-lv2">
              FIO2 (%)
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DeboundInput
                    tabIndex={calculateTabIndex(index, 12)}
                    type="number"
                    readOnly={isDisable(index)}
                    max={100}
                    min={21}
                    value={item.fiO2}
                    size={"small"}
                    onChange={onChangeValue(index, "fiO2", false)}
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={2} className="col-lv2">
              PEEP/CPAP/EPAP
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DeboundInput
                    tabIndex={calculateTabIndex(index, 13)}
                    type="number"
                    readOnly={isDisable(index)}
                    max={99}
                    value={item.epap}
                    size={"small"}
                    onChange={onChangeValue(index, "epap", false)}
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={2} className="col-lv2">
              PS/IPAP/FLOW
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DeboundInput
                    tabIndex={calculateTabIndex(index, 14)}
                    type="number"
                    readOnly={isDisable(index)}
                    max={99}
                    value={item.flow}
                    size={"small"}
                    onChange={onChangeValue(index, "flow", false)}
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={2} rowSpan={3} className="col-lv0 center bold vamid">
              Thở oxy
            </td>
            <td colSpan={2} className="col-lv2">
              Gọng kính(1/p)
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DeboundInput
                    tabIndex={calculateTabIndex(index, 15)}
                    readOnly={isDisable(index)}
                    size={"small"}
                    max={99}
                    value={item.gongKinh}
                    onChange={onChangeValue(index, "gongKinh", false)}
                    type="number"
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={2} className="col-lv2">
              Mask(1/p)
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DeboundInput
                    tabIndex={calculateTabIndex(index, 16)}
                    readOnly={isDisable(index)}
                    size={"small"}
                    max={99}
                    value={item.mask}
                    onChange={onChangeValue(index, "mask", false)}
                    type="number"
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={2} className="col-lv2">
              Mask túi(1/p)
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DeboundInput
                    tabIndex={calculateTabIndex(index, 17)}
                    readOnly={isDisable(index)}
                    size={"small"}
                    max={99}
                    value={item.maskTui}
                    onChange={onChangeValue(index, "maskTui", false)}
                    type="number"
                  />
                </td>
              );
            })}
          </tr>

          <tr>
            <td colSpan={2} rowSpan={9} className="col-lv0 bold center vamid">
              Dịch
            </td>
            <td rowSpan={4} className="col-lv2 bold center lh15">
              Dịch vào
            </td>
            <td className="col-lv3">Dịch thuốc</td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DeboundInput
                    tabIndex={calculateTabIndex(index, 18)}
                    type="number"
                    readOnly={isDisable(index)}
                    value={item.dich}
                    size={"small"}
                    onChange={onChangeValue(index, "dich", false)}
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td className="col-lv3">{"Ăn"}</td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DeboundInput
                    tabIndex={calculateTabIndex(index, 19)}
                    type="number"
                    readOnly={isDisable(index)}
                    value={item.an}
                    size={"small"}
                    onChange={onChangeValue(index, "an", false)}
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td className="col-lv3">{"Khác"}</td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DeboundInput
                    tabIndex={calculateTabIndex(index, 20)}
                    type="number"
                    readOnly={isDisable(index)}
                    value={item.vaoKhac}
                    size={"small"}
                    onChange={onChangeValue(index, "vaoKhac", false)}
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td className="col-lv3 center bold">
              <div
                className="flex"
                style={{
                  justifyContent: "space-between",
                }}
              >
                <div>Tổng vào cũ: {form?.tongVaoCu}</div>
                <div>Tổng</div>
              </div>
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4 center bold" key={index}>
                  {item.tongVao || ""}
                </td>
              );
            })}
          </tr>
          <tr>
            <td rowSpan={4} className="col-lv2 bold center lh15">
              Dịch ra
            </td>
            <td className="col-lv3">Tiểu</td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DeboundInput
                    tabIndex={calculateTabIndex(index, 21)}
                    type="number"
                    readOnly={isDisable(index)}
                    value={item.tieu}
                    size={"small"}
                    onChange={onChangeValue(index, "tieu", false)}
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td className="col-lv3">{"Khác"}</td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DeboundInput
                    tabIndex={calculateTabIndex(index, 22)}
                    type="number"
                    readOnly={isDisable(index)}
                    value={item.raKhac}
                    size={"small"}
                    onChange={onChangeValue(index, "raKhac", false)}
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td className="col-lv3">{t("editor.ghiChu")}</td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DeboundInput
                    tabIndex={calculateTabIndex(index, 23)}
                    readOnly={isDisable(index)}
                    value={item.raGhiChu}
                    size={"small"}
                    onChange={onChangeValue(index, "raGhiChu", false)}
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td className="col-lv3 center bold">
              <div
                className="flex"
                style={{
                  justifyContent: "space-between",
                }}
              >
                <div>Tổng ra cũ: {form?.tongRaCu}</div>
                <div>Tổng</div>
              </div>
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4 center bold" key={index}>
                  {item.tongRa || ""}
                </td>
              );
            })}
          </tr>
          {/* <tr>
            <td className="col-lv3 center bold">
              <div>Tổng ra</div>
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4 center bold" key={index}>
                  <DeboundInput
                    type="number"
                    className="old-value"
                    value={state.tongRa}
                    onChange={onChangeValue(index, "tongRa", false)}
                  />
                </td>
              );
            })}
          </tr> */}
          <tr>
            <td colSpan={2} className="col-lv2 bold center">
              Dịch vào - Dịch ra
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4 center bold" key={index}>
                  {item.vaoTruRa || ""}
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={2} rowSpan={2} className="col-lv0 center bold vamid">
              Đờm
            </td>
            <td colSpan={2} className="col-lv2">
              {t("editor.mauu")} ({t("editor.xanh")} - {t("editor.trang")} -{" "}
              {t("editor.duc")} - {t("editor.mau")})
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DropDownList
                    tabIndex={calculateTabIndex(index, 24)}
                    disabled={isDisable(index)}
                    title="Chọn màu đờm"
                    className={"drop-list"}
                    dataSource={DOM_MAUS}
                    value={item.domMau}
                    onChange={(values) => {
                      onChangeValue(index, "domMau", false)(values);
                    }}
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={2} className="col-lv2">
              {t("editor.soLuong")}
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DropDownList
                    tabIndex={calculateTabIndex(index, 25)}
                    disabled={isDisable(index)}
                    title="Chọn số lượng đờm"
                    className={"drop-list"}
                    dataSource={DOM_SOLUONGS}
                    value={item.domTinhChat}
                    onChange={(values) => {
                      onChangeValue(index, "domTinhChat", false)(values);
                    }}
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={2} rowSpan={3} className="col-lv0 center bold vamid">
              Phân
            </td>
            <td colSpan={2} className="col-lv2">
              Số lượng
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DeboundInput
                    tabIndex={calculateTabIndex(index, 26)}
                    type="number"
                    max={9999}
                    readOnly={isDisable(index)}
                    value={item.phanSoLuong}
                    size={"small"}
                    onChange={onChangeValue(index, "phanSoLuong", false)}
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={2} className="col-lv2">
              {t("editor.mau")}
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DropDownList
                    tabIndex={calculateTabIndex(index, 27)}
                    disabled={isDisable(index)}
                    title="Chọn màu phân"
                    className={"drop-list"}
                    dataSource={PHAN_MAUS}
                    value={item.phanMau}
                    onChange={(values) => {
                      onChangeValue(index, "phanMau", false)(values);
                    }}
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={2} className="col-lv2">
              Tính chất
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DropDownList
                    tabIndex={calculateTabIndex(index, 28)}
                    disabled={isDisable(index)}
                    title="Chọn tính chất phân"
                    className={"drop-list"}
                    dataSource={PHAN_TINH_CHAT}
                    value={item.phanTinhChat}
                    onChange={(values) => {
                      onChangeValue(index, "phanTinhChat", false)(values);
                    }}
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={2} rowSpan={13} className="col-lv0 bold center vamid">
              <div> {t("editor.cHAMSOCDIEUDUONG")}</div>
            </td>
            <td colSpan={2} className="col-lv2">
              {t("editor.dichTonDuDaDay")}
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DeboundInput
                    tabIndex={calculateTabIndex(index, 29)}
                    type="number"
                    readOnly={isDisable(index)}
                    max={999}
                    value={item.tonDaDay}
                    size={"small"}
                    onChange={onChangeValue(index, "tonDaDay", false)}
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={2} className="col-lv2">
              {t("editor.duongMauMaoMach")}……{t("editor.gio/lan")}
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DeboundInput
                    tabIndex={calculateTabIndex(index, 30)}
                    readOnly={isDisable(index)}
                    value={item.duongMau}
                    size={"small"}
                    onChange={onChangeValue(index, "duongMau", false)}
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={2} className="col-lv2">
              {t("editor.hutDom")}
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <ToggleValue
                    tabIndex={calculateTabIndex(index, 31)}
                    valueToggle={"x"}
                    value={item.hutDom}
                    disabled={isDisable(index)}
                    onChange={onChangeValue(index, "hutDom", false)}
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={2} className="col-lv2">
              Xoa Sanyrene
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <ToggleValue
                    tabIndex={calculateTabIndex(index, 32)}
                    valueToggle={"x"}
                    value={item.xoaSanyrene}
                    disabled={isDisable(index)}
                    onChange={onChangeValue(index, "xoaSanyrene", false)}
                  />
                </td>
              );
            })}
          </tr>

          <tr>
            <td colSpan={2} className="col-lv2">
              Vỗ rung
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <ToggleValue
                    tabIndex={calculateTabIndex(index, 33)}
                    valueToggle={"x"}
                    value={item.voRung}
                    disabled={isDisable(index)}
                    onChange={onChangeValue(index, "voRung", false)}
                  />
                </td>
              );
            })}
          </tr>

          <tr>
            <td colSpan={2} className="col-lv2">
              {t("editor.tuThe")}
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DropDownList
                    tabIndex={calculateTabIndex(index, 34)}
                    disabled={isDisable(index)}
                    title="Chọn thư thế"
                    className={"drop-list"}
                    dataSource={TU_THE}
                    value={item.tuThe}
                    onChange={(values) => {
                      onChangeValue(index, "tuThe", false)(values);
                    }}
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={2} className="col-lv2">
              {t("editor.tam")}
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <ToggleValue
                    tabIndex={calculateTabIndex(index, 35)}
                    valueToggle={"x"}
                    value={item.tam}
                    disabled={isDisable(index)}
                    onChange={onChangeValue(index, "tam", false)}
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={2} className="col-lv2">
              {t("editor.goiDau")}
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <ToggleValue
                    tabIndex={calculateTabIndex(index, 36)}
                    valueToggle={"x"}
                    value={item.goiDau}
                    disabled={isDisable(index)}
                    onChange={onChangeValue(index, "goiDau", false)}
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={2} className="col-lv2">
              {t("editor.thayBang")}
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <ToggleValue
                    tabIndex={calculateTabIndex(index, 37)}
                    valueToggle={"x"}
                    value={item.thayBang}
                    disabled={isDisable(index)}
                    onChange={onChangeValue(index, "thayBang", false)}
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={2} className="col-lv2">
              {t("editor.veSinhRangMiengMui")}
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <ToggleValue
                    tabIndex={calculateTabIndex(index, 38)}
                    valueToggle={"x"}
                    value={item.veSinhRang}
                    disabled={isDisable(index)}
                    onChange={onChangeValue(index, "veSinhRang", false)}
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={2} className="col-lv2">
              {t("editor.viTriNKQ")}(cm)
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DeboundInput
                    tabIndex={calculateTabIndex(index, 39)}
                    type="number"
                    readOnly={isDisable(index)}
                    max={99}
                    value={item.viTriNkq}
                    size={"small"}
                    onChange={onChangeValue(index, "viTriNkq", false)}
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={2} className="col-lv2">
              {t("editor.soNgayDatNKQ/MKQ")}
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DeboundInput
                    tabIndex={calculateTabIndex(index, 40)}
                    type="number"
                    readOnly={isDisable(index)}
                    max={999}
                    value={item.soNgayDatNkq}
                    size={"small"}
                    onChange={onChangeValue(index, "soNgayDatNkq", false)}
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={2} className="col-lv2">
              {t("editor.apLucCuff")}
            </td>
            {state.values.map((item, index) => {
              return (
                <td className="col-lv4" key={index}>
                  <DeboundInput
                    tabIndex={calculateTabIndex(index, 41)}
                    type="number"
                    readOnly={isDisable(index)}
                    max={999}
                    value={item.apLucCuff}
                    size={"small"}
                    onChange={onChangeValue(index, "apLucCuff", false)}
                  />
                </td>
              );
            })}
          </tr>

          <tr>
            <td className="col-lv0 vertical bold" rowSpan={3}>
              <svg width="20" height="40">
                <text x="32" y="45" transform="rotate(-90, 20, 50)">
                  {t("editor.tenDD")}
                </text>
              </svg>
            </td>
            <td colSpan={3} className="col-lv1">
              {renderCaDieuDuong("Ca1: ", 1)}
            </td>
            {renderSignature}
          </tr>
          <tr>
            <td colSpan={3} className="col-lv1">
              {renderCaDieuDuong("Ca2: ", 2)}
            </td>
          </tr>
          <tr>
            <td colSpan={3} className="col-lv1">
              {renderCaDieuDuong("Ca3: ", 3)}
            </td>
          </tr>
        </tbody>
      </table>
    </Main>
  );
};

export default HSTC;
