import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { Main } from "./styled";
import { useTranslation } from "react-i18next";
import { MODE } from "utils/editor-utils";
import RenderTable from "./components/RenderTable";
import { useDispatch } from "react-redux";
import { SettingOutlined } from "@ant-design/icons";
import { Button, Pagination } from "antd";
import { get } from "lodash";
import { PlusOutlined } from "@ant-design/icons";
import { refConfirm } from "app";
import ModalSelectSinhHieu from "../Components/ModalSelectSinhHieu";

const PAGE_SIZE = 5; // Số khung mỗi trang, có thể cho chỉnh nếu muốn

const PhieuChamSocCap23CR = (props) => {
  const { t } = useTranslation();
  const [state, _setState] = useState({
    dsTheoDoi: [{}],
    size: PAGE_SIZE,
    currentPage: 1, // Thêm state cho trang hiện tại
  });

  const [dataCopy, setDataCopy] = useState();

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const refValueForm = useRef();
  const refModalSinhHieu = useRef();
  const refAllKhung = useRef([]);

  const { component, mode, form = {}, formChange } = props;
  const itemProps = component.props || {};
  const init = useDispatch().component.init;

  const handleFocus = () => {
    if (mode === MODE.config) {
      init(component);
    }
  };

  useEffect(() => {
    if (form) {
      refValueForm.current = form.dsTheoDoi || [{}];
      refValueForm.current.forEach((item) => {
        item.dsChiTiet = [{}, {}, {}, {}, {}].map((el, index) => {
          const element = get(item, `dsChiTiet[${index}]`, {});

          if (element?.chiSoSong) {
            element.chiSoSong.huyetAp =
              element.chiSoSong.huyetApTamThu &&
              element.chiSoSong.huyetApTamTruong
                ? `${element.chiSoSong.huyetApTamThu}/${element.chiSoSong.huyetApTamTruong}`
                : "";
          }

          if (element?.chiSoSong?.thoiGianThucHien) {
            element.thoiGianThucHien = element?.chiSoSong?.thoiGianThucHien;
          }

          //set khoa chỉ định cho sinh hiệu mặc định
          if (element?.chiSoSong && !element?.chiSoSong?.khoaChiDinhId) {
            element.chiSoSong.khoaChiDinhId = form?.khoaChiDinhId;
          }

          element.veh = `${element.ve || ""}/${element.vh || ""}`;
          return element;
        });
      });

      refAllKhung.current = refValueForm.current;
      const dsTheoDoi = refAllKhung.current.slice(0, state.size);
      setState({
        dsTheoDoi: dsTheoDoi,
      });
    }
  }, [JSON.stringify(form || {})]);

  const handleAdd = useCallback(() => {
    refValueForm.current = [
      ...refValueForm.current,
      {
        dsChiTiet: [{}, {}, {}, {}, {}],
      },
    ];
    setState({
      dsTheoDoi: refValueForm.current,
      currentPage: 1, // Chuyển về page đầu tiên
    });
    refAllKhung.current = refValueForm.current;

    formChange["dsTheoDoi"](refValueForm.current);
  }, [state.dsTheoDoi, formChange]);

  const handleRemove = useCallback(
    (index) => () => {
      refConfirm.current &&
        refConfirm.current.show(
          {
            title: t("common.thongBao"),
            content: `${t("common.banCoChacMuonXoa")}`,
            cancelText: t("common.huy"),
            okText: t("common.dongY"),
            classNameOkText: "button-warning",
            showBtnOk: true,
            typeModal: "warning",
          },
          () => {
            refAllKhung.current = refAllKhung.current.filter(
              (item, idx) => idx !== index
            );
            refValueForm.current = refAllKhung.current;
            const dsTheoDoi = refValueForm.current.slice(0, state.size);

            setState({
              dsTheoDoi,
            });
            formChange["dsTheoDoi"](dsTheoDoi);
          }
        );
    },
    [state.dsTheoDoi, formChange]
  );

  // Tính toán các khung sẽ hiển thị dựa trên trang hiện tại
  const pagedTheoDoi = useMemo(() => {
    const start = (state.currentPage - 1) * state.size;
    const end = start + state.size;
    return refAllKhung.current.slice(start, end);
  }, [state.currentPage, state.size, refAllKhung.current, state.dsTheoDoi]);

  // Khi thay đổi trang
  const handlePageChange = (page) => {
    setState({ currentPage: page });
  };

  const handleSizeChange = (page, size) => {
    setState({ size });
  };

  return (
    <Main
      className="phieu-cham-soc-cap23"
      mode={mode}
      itemProps={itemProps}
      data-type="phieu-cham-soc-cap23"
    >
      {mode === MODE.config && (
        <>
          <div className="table-config">
            <Button
              icon={<SettingOutlined />}
              onClick={handleFocus}
              size={"small"}
            />
          </div>
        </>
      )}
      {pagedTheoDoi.map((item, index) => {
        return (
          <div
            className="form"
            style={{
              pageBreakAfter:
                index === state.dsTheoDoi.length - 1 ? "avoid" : "always",
            }}
            key={index + (state.currentPage - 1) * state.size}
          >
            <RenderTable
              key={index + (state.currentPage - 1) * state.size}
              mode={mode}
              tableIndex={index + (state.currentPage - 1) * state.size}
              showHeader={index == 0}
              refValueForm={refValueForm.current}
              item={item}
              formChange={formChange}
              onRemove={handleRemove}
              itemProps={itemProps}
              form={form}
              setDataCopy={setDataCopy}
              dataCopy={dataCopy}
              refModalSinhHieu={refModalSinhHieu}
            />
          </div>
        );
      })}
      <div className="controls-container">
        <Pagination
          listData={refAllKhung.current}
          current={state.currentPage}
          pageSize={state.size}
          total={refAllKhung.current.length}
          onChange={handlePageChange}
          onShowSizeChange={handleSizeChange}
          showSizeChanger={true}
          showQuickJumper
          showTotal={(total, range) =>
            `${range[0]}-${range[1]} của ${total} bảng`
          }
          pageSizeOptions={[5, 10, 20, 50, 100]}
        />
      </div>
      {mode !== MODE.config && (
        <Button
          className="btn-add"
          icon={<PlusOutlined />}
          onClick={handleAdd}
          size={"small"}
        />
      )}
      <ModalSelectSinhHieu ref={refModalSinhHieu} />
    </Main>
  );
};

PhieuChamSocCap23CR.propTypes = {};

export default PhieuChamSocCap23CR;
