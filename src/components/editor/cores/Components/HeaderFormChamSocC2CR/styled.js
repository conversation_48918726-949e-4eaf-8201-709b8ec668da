import styled, { createGlobalStyle } from "styled-components";
export const GlobalStyle = createGlobalStyle``;
export const Main = styled.div`
  display: ${(props) => {
    if (props.showHeader) {
      return "block";
    }
    return props.tableIndex ? "none" : "block";
  }};
  @media print {
    margin-top: 0 important;
    display: block;
  }

  .title {
    font-size: 20px;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    flex-direction: column;
    font-weight: 700;
    text-transform: uppercase;
  }
  .header-right {
    padding-left: 10px;
  }
  .header-left {
    display: flex;
    .logo {
    }
    .left-content {
      flex: 1;
      text-align: left;
    }
  }
  .flex {
    display: flex;
  }
  .flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .flex-1 {
    flex: 1;
  }
  .column {
    flex-direction: column;
  }
  .flex-bw {
    display: flex;
    justify-content: space-between;
  }
  .align-center {
    align-items: center;
  }
  .left {
    text-align: left;
  }
  .bold {
    font-weight: 700;
  }
  .center {
    text-align: center;
  }
  .flex-center {
    display: flex;
    align-items: center;
  }
  .bold {
    font-weight: bold;
  }
  .green {
    color: green;
  }
  .right {
    text-align: right;
  }
  .blue {
    color: blue;
  }
  & .barcode-label {
    font-size: 16px;
  }
`;
