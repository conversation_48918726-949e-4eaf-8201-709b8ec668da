import React, { useMemo } from "react";
import { Col, Row } from "antd";
import { DeboundInput } from "module_html_editor/Components";
import CheckGroups from "../../CheckGroups";
import Barcode from "../../Barcode";
import { Main } from "./styled";
import { MODE } from "utils/editor-utils";
import Image from "./../../Image";
import { useListAll } from "hooks";

const HeaderFormChamSocC2 = ({
  form,
  mode,
  formChange,
  tableIndex,
  loaiPhieu = 1,
  showHeader,
}) => {
  const [listAllKhoa] = useListAll("khoa", {}, true);

  const tenKhoaChiDinh = useMemo(() => {
    return listAllKhoa.find((x) => x.id == form?.khoaChiDinhId)?.ten || "";
  }, [listAllKhoa, form?.khoaChiDinhId]);

  return (
    <Main tableIndex={tableIndex} showHeader={showHeader}>
      <Row className="form-header flex">
        <Col className="header-left" span={10}>
          <div className="logo">
            <Image
              component={{
                props: {
                  isLogo: true,
                  width: 60,
                  height: 60,
                },
              }}
              mode={mode}
              form={form}
            />
          </div>
          <div className="left-content">
            <div className="left">
              {mode === MODE.config ? "Tiêu đề trái 1" : form.tieuDeTrai1}
            </div>
            <div className="left bold green">
              <b>
                {mode === MODE.config ? "Tiêu đề trái 2" : form.tieuDeTrai2}
              </b>
            </div>
            <div className="header-level-1 blue">
              <b>
                {mode === MODE.config ? "Tên khoa người bệnh" : tenKhoaChiDinh}
              </b>
            </div>
          </div>
        </Col>
        <Col span={8} className="title">
          <Barcode
            component={{
              props: {
                label: "Mã HS",
                width: 200,
                height: 45,
                contentAlign: "left",
                fieldName: "maHoSo",
              },
            }}
            mode={mode}
            form={form}
          />
        </Col>
        <Col className="header-right" span={6}>
          <div>Mã BA: {form.maBenhAn}</div>
          <div>Mã NB: {form.maNb}</div>
        </Col>
      </Row>
      <Row>
        <Col span={24} className="green right">
          <i> ​(Theo MS: 38/BV02, Thông tư số 32/2023/TT-BYT)</i>
        </Col>
      </Row>
      <Row>
        <Col span={24} className="flex-center column bold title">
          <div>
            PHIẾU THEO DÕI VÀ CHĂM SÓC{" "}
            {loaiPhieu == 2 ? "(Cấp 1)" : "(Cấp 2 - 3)"}{" "}
          </div>
        </Col>
      </Row>
      <Row>
        <Col span={24} className="flex-center">
          Tờ số: {tableIndex + 1}
        </Col>
      </Row>

      <Row>
        <Col span={12}>
          Họ và tên NB: <b className="blue">{form.tenNb}</b>
        </Col>
        <Col span={6}>
          Tuổi: <span className="blue">{form.tuoi || form.tuoi2}</span>
        </Col>
        <Col span={6}>
          <CheckGroups
            component={{
              props: {
                direction: "rtl",
                type: "onlyOne",
                checkList: [
                  {
                    label: "Giới tính: Nam",
                    value: 1,
                  },
                  {
                    label: "Nữ",
                    value: 2,
                  },
                ],
                fieldName: "gioiTinh",
                readOnly: true,
              },
            }}
            mode={mode}
            form={form}
            formChange={formChange}
          />
        </Col>
      </Row>
      <Row>
        <Col span={12}>
          Phòng: <span className="blue">{form?.tenPhong}</span>
        </Col>
        <Col span={12}>
          Giường: <span className="blue">{form.tenDvGiuong}</span>
        </Col>
      </Row>
      <Row>
        <Col span={24}>
          Chẩn đoán: <span className="blue">{form?.cdChinh}</span>
        </Col>
      </Row>
      <Row>
        <Col span={24} className="flex">
          <div>
            <span>Tiền sử dị ứng:&nbsp;</span>
          </div>
          <CheckGroups
            component={{
              props: {
                direction: "ltr",
                type: "onlyOne",
                checkList: [
                  {
                    label: "Chưa ghi nhận &nbsp;",
                    value: 1,
                  },
                  {
                    label: "Có,",
                    value: 2,
                  },
                ],
                fieldName: "tienSuDiUng",
              },
            }}
            mode={mode}
            form={form}
            formChange={formChange}
          />
          <DeboundInput
            //   disabled={disabled}
            size={"small"}
            value={form?.ghiRo || ""}
            onChange={(value) => formChange["ghiRo"](value)}
            type="multipleline"
            lineHeightText={1.5}
            fontSize={12}
            minHeight={24}
            styleMain={{ flex: 1 }}
            label="&nbsp;ghi rõ:&nbsp;"
            contentColor="blue"
          />
        </Col>
      </Row>
    </Main>
  );
};

HeaderFormChamSocC2.propTypes = {};

export default HeaderFormChamSocC2;
