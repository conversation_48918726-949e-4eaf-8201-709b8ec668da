import React, { useMemo } from "react";
import { Col, Row } from "antd";
import { DeboundInput } from "module_html_editor/Components";
import CheckGroups from "../../CheckGroups";
import Barcode from "../../Barcode";
import { Main } from "./styled";
import { MODE } from "utils/editor-utils";
import Image from "../../Image";
import { useListAll } from "hooks";

const HeaderFormChamSocC2PSHN = ({
  form,
  mode,
  formChange,
  tableIndex,
  itemProps,
  isCscap1,
  toSo,
  titleCap2,
}) => {
  const [listAllKhoa] = useListAll("khoa", {}, true);

  const tenKhoaChiDinh = useMemo(() => {
    return listAllKhoa.find((x) => x.id == form?.khoaChiDinhId)?.ten || "";
  }, [listAllKhoa, form?.khoaChiDinhId]);

  return (
    <Main tableIndex={tableIndex}>
      <Row className="form-header flex">
        <Col className="header-left " span={18}>
          <div style={{ display: "flex", lineHeight: 1.35 }}>
            <Image
              component={{
                props: {
                  isLogo: true,
                  width: 60,
                  height: 60,
                },
              }}
              mode={mode}
              form={form}
            ></Image>
            <div style={{ flex: 1, marginLeft: 10 }}>
              <div className="left">
                {mode === MODE.config ? "Tiêu đề trái 1" : form.tieuDeTrai1}
              </div>
              <div className="left bold green">
                <b>
                  {mode === MODE.config ? "Tiêu đề trái 2" : form.tieuDeTrai2}
                </b>
              </div>
              <div className="header-level-1 blue">
                <b>
                  {mode === MODE.config
                    ? "Tên khoa người bệnh"
                    : tenKhoaChiDinh}
                </b>
              </div>
            </div>
          </div>

          <div className="green left">
            <i> (Theo MS: 38/BV2, Công văn số 65/KCB-QLCL&CĐT)</i>
          </div>
        </Col>
        <Col className="header-right" span={6}>
          <Barcode
            component={{
              props: {
                label: "Mã HS",
                width: 170,
                height: 35,
                contentAlign: "left",
                fieldName: "maHoSo",
                noLabel: true,
              },
            }}
            mode={mode}
            form={form}
          />
          <div>Mã Hồ sơ: {form.maHoSo}</div>
          <div>Mã NB: {form.maNb}</div>
          <div>Mã BA: {form.maBenhAn}</div>
        </Col>
      </Row>
      <Row></Row>
      <Row>
        <Col span={24} className="flex-center column bold title">
          <div>
            {itemProps?.titleForm || "​PHIẾU THEO DÕI VÀ CHĂM SÓC PHCN"}
          </div>
          <div>({`${titleCap2 ? titleCap2 : "CẤP 2-3"}`})</div>
        </Col>
      </Row>
      <Row>
        <Col span={24} className="flex-center">
          Tờ số: {toSo || tableIndex + 1}
        </Col>
      </Row>

      <Row>
        <Col span={14}>
          Họ và tên NB: <b className="blue">{form.tenNb}</b>
        </Col>
        <Col span={4}>
          Tuổi: <span className="blue">{form.tuoi || form.tuoi2}</span>
        </Col>
        <Col span={6}>
          <CheckGroups
            component={{
              props: {
                direction: "rtl",
                type: "onlyOne",
                checkList: [
                  {
                    label: "Giới tính: Nam",
                    value: 1,
                  },
                  {
                    label: "Nữ",
                    value: 2,
                  },
                ],
                fieldName: "gioiTinh",
                readOnly: true,
              },
            }}
            mode={mode}
            form={form}
            formChange={formChange}
          />
        </Col>
      </Row>
      <Row>
        <Col span={14}>
          Phòng: <span className="blue">{form?.tenPhong}</span>
        </Col>
        <Col span={10}>
          Giường: <span className="blue">{form.tenDvGiuong}</span>
        </Col>
      </Row>
      <Row>
        <Col span={24}>
          Chẩn đoán: <span className="blue">{form?.cdChinh}</span>
        </Col>
      </Row>
      <Row>
        <Col span={24} className="flex">
          <CheckGroups
            component={{
              props: {
                direction: "rtl",
                type: "onlyOne",
                checkList: [
                  {
                    label: "Tiền sử dị ứng: ",
                    value: 1,
                  },
                  {
                    label: "Chưa ghi nhận",
                    value: 2,
                  },
                ],
                fieldName: "tienSuDiUng",
              },
            }}
            mode={mode}
            form={form}
            formChange={formChange}
          />
          <DeboundInput
            size={"small"}
            value={form?.ghiRo || ""}
            onChange={(value) => formChange["ghiRo"](value)}
            type="multipleline"
            lineHeightText={1.5}
            fontSize={12}
            minHeight={24}
            styleMain={{ flex: 1 }}
            label="Có, ghi rõ: "
            contentColor="blue"
          />
        </Col>
      </Row>
    </Main>
  );
};

HeaderFormChamSocC2PSHN.propTypes = {};

export default HeaderFormChamSocC2PSHN;
