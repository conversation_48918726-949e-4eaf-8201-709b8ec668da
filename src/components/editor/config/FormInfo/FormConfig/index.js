import React, { useState, forwardRef, useEffect } from "react";
import T from "prop-types";
import { Row, Col, Input, InputNumber, Radio, Checkbox, Button } from "antd";
import { PlusOutlined, DeleteOutlined } from "@ant-design/icons";
import { Main } from "./styled";
import { FontSizeConfig } from "components/editor/config";
import { useDispatch, useSelector } from "react-redux";
import { PAGE_TYPE } from "constants/index";
import { useTranslation } from "react-i18next";
import { EditorTool } from "components/editor/config";
const { FieldName } = EditorTool;
const ComponentProps = forwardRef((props, ref) => {
  const [state, _setState] = useState({
    defaultData: [],
    listKeyRequireData: [],
  });
  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };
  const { apiFields } = props;
  const { formInfo = {} } = useSelector((state) => state.config);
  const { updateData, updateFormProps } = useDispatch().config;
  const { t } = useTranslation();

  useEffect(() => {
    setState({
      listKeyRequireData: formInfo?.listKeyRequireData || [],
    });
  }, [formInfo]);

  const changeInput = (target) => (e) => {
    const newData = { ...formInfo, [target]: e.target.value };
    updateData({ formInfo: newData });
  };
  const changeValue = (target) => (e) => {
    let value = "";
    if (target == "fontSize") {
      updateFormProps({
        fontSize: e,
      });
    }
    if (
      target == "notEditHtml" ||
      target == "choPhepXoa" ||
      target == "hienThiDuLieuTaiThoiDiemKy" ||
      target == "suDungApiKySoRieng"
    ) {
      value = e.target.checked;
    } else {
      value = e;
    }
    const newData = { ...formInfo, [target]: value };
    updateData({ formInfo: newData });
  };

  const onRemoveKeyRequireData = (index) => () => {
    const data = state.listKeyRequireData.filter((item, idx) => idx !== index);
    setState({
      listKeyRequireData: data,
    });
    const newData = { ...formInfo, listKeyRequireData: data };
    updateData({ formInfo: newData });
  };

  const onChangeKeyRequireData = (key, index) => (e) => {
    let data = e;
    if (e?.target) {
      data = e.target.value;
    }
    state.listKeyRequireData[index][key] = data;
    setState({
      listKeyRequireData: state.listKeyRequireData,
    });
    formInfo.listKeyRequireData = state.listKeyRequireData;
  };

  const onAddListKeyRequireValue = () => {
    const newData = [
      ...state.listKeyRequireData,
      {
        key: "",
        ten: "",
      },
    ];
    setState({
      listKeyRequireData: newData,
    });
  };

  return (
    <Main>
      <Row gutter={[12, 12]}>
        <Col span={8}>
          <span>{t("editor.coChu")}</span>
        </Col>

        <Col span={16} style={{ display: "flex" }}>
          <FontSizeConfig
            onChange={changeValue("fontSize")}
            value={formInfo?.fontSize}
          />
        </Col>

        <Col span={8}>
          <span>{t("editor.inMacDinh")}</span>
        </Col>
        <Col span={16} style={{ display: "flex" }}>
          <Checkbox
            checked={formInfo?.notEditHtml}
            onChange={changeValue("notEditHtml")}
          ></Checkbox>
        </Col>
        <Col span={8}>
          <span>{t("editor.loaiGiay")}</span>
        </Col>
        <Col span={16} style={{ display: "flex" }}>
          <Radio.Group
            value={formInfo?.pageType}
            onChange={changeInput("pageType")}
          >
            <Radio value={PAGE_TYPE.FORM_A4}>A4</Radio>
            <Radio value={PAGE_TYPE.FORM_A3}>A3</Radio>
          </Radio.Group>
        </Col>
        <Col span={8}>
          <span>{t("editor.khoGiay")}</span>
        </Col>

        <Col span={16} style={{ display: "flex" }}>
          <Radio.Group
            value={formInfo?.layoutType}
            onChange={changeInput("layoutType")}
          >
            <Radio value="horizontal">{t("editor.ngang")}</Radio>
            <Radio value="default">{t("editor.doc")}</Radio>
          </Radio.Group>
        </Col>

        <Col span={16}>
          <span>{t("editor.choPhepXoaBanGhi")}</span>
        </Col>
        <Col span={8} style={{ display: "flex" }}>
          <Checkbox
            checked={formInfo?.choPhepXoa}
            onChange={changeValue("choPhepXoa")}
          ></Checkbox>
        </Col>

        <Col span={16}>
          <span>{t("editor.hienThiDuLieuTaiThoiDiemKy")}</span>
        </Col>
        <Col span={8} style={{ display: "flex" }}>
          <Checkbox
            checked={formInfo?.hienThiDuLieuTaiThoiDiemKy}
            onChange={changeValue("hienThiDuLieuTaiThoiDiemKy")}
          ></Checkbox>
        </Col>
        <Col span={16}>
          <span>{t("editor.suDungApiNvLichSuKy")}</span>
        </Col>
        <Col span={8} style={{ display: "flex" }}>
          <Checkbox
            checked={formInfo?.suDungApiKySoRieng}
            onChange={changeValue("suDungApiKySoRieng")}
          ></Checkbox>
        </Col>
      </Row>
      <Row gutter={[12, 12]}>
        <Col span={24}>{t("editor.truongDuLieuBatBuoc")}</Col>
        <Col span={24}>
          {state.listKeyRequireData.map((item, index) => {
            return (
              <Row type="flex" gutter={[12, 12]} align="bottom">
                <Col span={10}>
                  <div>{t("editor.truongDuLieu")}</div>
                  <FieldName
                    style={{ width: "100%" }}
                    onSelect={onChangeKeyRequireData("key", index)}
                    value={item.key}
                    apiFields={apiFields}
                  />
                </Col>
                <Col span={10}>
                  <div>{t("editor.tenTruongDuLieu")}</div>
                  <Input
                    size="small"
                    value={item.ten}
                    onChange={onChangeKeyRequireData("ten", index)}
                  ></Input>
                </Col>
                <Col span={2}>
                  <Button
                    size="small"
                    icon={<DeleteOutlined></DeleteOutlined>}
                    onClick={onRemoveKeyRequireData(index)}
                  ></Button>
                </Col>
              </Row>
            );
          })}
        </Col>
        <Col span={24}>
          <Button
            className={"add-btn"}
            icon={<PlusOutlined />}
            size={"small"}
            onClick={onAddListKeyRequireValue}
          />
        </Col>
      </Row>
    </Main>
  );
});

export default ComponentProps;
