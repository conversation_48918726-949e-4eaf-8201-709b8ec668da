import React, { useEffect, useLayoutEffect, useRef, useState } from "react";
import { connect, useDispatch, useSelector } from "react-redux";
import { Main } from "./styled";
import Page from "./Page";
import { Spin } from "antd";
import { useListAll, useLoading, useThietLap } from "hooks";
import { A4, THIET_LAP_CHUNG } from "constants/index";
import { debounce, isEmpty, uniq } from "lodash";
import { checkValidBody, isArray } from "utils/index";
import useListChiSoSong from "./hooks/useListChiSoSong";

const VitalSignsPrint = (props) => {
  const [dataTIEU_DE_TRAI_1] = useThietLap(THIET_LAP_CHUNG.TIEU_DE_TRAI_1);
  const [dataTIEU_DE_TRAI_2] = useThietLap(THIET_LAP_CHUNG.TIEU_DE_TRAI_2);
  const [dataMA_CSS_VONG_CANH_TAY, loadFinish] = useThietLap(
    THIET_LAP_CHUNG.MA_CSS_VONG_CANH_TAY
  );
  const [dataHIEN_THI_SINH_HIEU_TU_TIEP_DON_VA_KHAM_BENH_VAO_NOI_TRU] =
    useThietLap(
      THIET_LAP_CHUNG.HIEN_THI_SINH_HIEU_TU_TIEP_DON_VA_KHAM_BENH_VAO_NOI_TRU,
      ""
    );
  const [khoaChiDinhId, setKhoaChiDinhId] = useState(null);
  const { hideLoading } = useLoading();
  const {
    vitalSigns: { getDataToPrint },
    chiSoSong: { getListAllChiSoSong },
    pttt: { onSearch: onSearchPttt },
    danhSachNguoiBenhNoiTru: { getNbNoiTruById },
  } = useDispatch();
  const refMain = useRef(null);
  const refIsObserving = useRef(null);
  const [listAllKhoa] = useListAll("khoa", {}, true);
  const { listAllChiSoSong, listChiSoSongMacDinh, listChiSoSongKhac } =
    useListChiSoSong({
      isNoiTru: true,
    });

  useEffect(() => {
    const fetchData = async () => {
      let nbDotDieuTriId = props.match?.params?.nbDotDieuTriId;
      const queryString = window.location.search;
      const urlParams = new URLSearchParams(queryString);
      let params = {
        khoaChiDinhId: urlParams.get("khoaChiDinhId"),
        tuThoiGian: urlParams.get("tuThoiGian"),
        denThoiGian: urlParams.get("denThoiGian"),
      };
      params = checkValidBody(params);
      if (params.khoaChiDinhId) {
        setKhoaChiDinhId(+params.khoaChiDinhId);
      }

      await getListAllChiSoSong({
        page: "",
        size: "",
        active: true,
        saveCache: false,
        isForceCall: true,
      });
      const res = await getNbNoiTruById(nbDotDieuTriId);
      if (res?.id) {
        const dsKhoa =
          dataHIEN_THI_SINH_HIEU_TU_TIEP_DON_VA_KHAM_BENH_VAO_NOI_TRU
            ?.split(",")
            .map((x) => x.trim())
            .filter(Boolean);

        const allowShowTiepDonKham = dsKhoa?.includes(res.maKhoaNb);

        getDataToPrint({
          nbDotDieuTriId,
          ...(!isEmpty(params) && params),
          allowShowTiepDonKham,
        });
      }
      onSearchPttt({
        dataSearch: { nbDotDieuTriId: nbDotDieuTriId },
        page: 0,
        size: 100,
      });
    };

    fetchData();
  }, []);

  const callback = debounce((observer) => {
    hideLoading();
    refMain.current.classList.add("editor-loaded-data");
    var iframe = window.frameElement;
    if (iframe) {
      const iframeId = iframe.getAttribute("data-id");
      const height = iframe.contentDocument.body.scrollHeight;
      window.parent?.postMessage(
        {
          TYPE: "IFRAME-EDITOR-HEIGHT",
          DATA: { iframeId, height: height, width: A4.width + 5 },
        },
        window.location.origin
      );
    }

    observer.disconnect();
    refIsObserving.current = false;
  }, 1000);

  useLayoutEffect(() => {
    if (props.patient && props.values) {
      setTimeout(() => {
        window.print();
        window.close();
      }, 500);
      if (refIsObserving.current) return;
      refIsObserving.current = true;
      let observer = new MutationObserver((mutationList, observer) => {
        callback(observer);
      });
      const targetNode = document.getElementById("root");
      const config = { attributes: true, childList: true, subtree: true };
      observer.observe(targetNode, config);
    }
  }, [props.values, props.patient]);

  const allFooter = uniq([
    "huyetap",
    "nhipTho",
    "canNang",
    ...(dataMA_CSS_VONG_CANH_TAY?.eval() ? ["bmiVct"] : []),
    ...listChiSoSongMacDinh.map((i) => i.id),
    ...props.moreValueIds.map((i) => i),
  ]);

  const chunkArray = (arr, size) => {
    const result = [];
    for (let i = 0; i < arr.length; i += size) {
      result.push(arr.slice(i, i + size));
    }
    return result;
  };

  // 1. Chia values: nhóm 16 phần tử (nếu rỗng vẫn tạo 1 mảng)
  const values = props.values?.length ? props.values : [];
  const valueChunks = chunkArray(values, 16);
  if (valueChunks.length === 0) valueChunks.push([]); // đảm bảo luôn có ít nhất 1 group

  // 2. Chia footer: nhóm 7 phần tử (ở đây chia 6 vì mặc định show phần ký điều dưỡng)
  const footerChunks = chunkArray(allFooter, 6);

  // 3. Kết hợp từng nhóm values với từng nhóm footer
  const paginatedValues = [];

  valueChunks.forEach((valueChunk) => {
    footerChunks.forEach((footerChunk, footerIndex) => {
      const footerWithIndex = footerChunk.map((id, i) => ({
        id,
        index: footerIndex * 6 + i + 1, // STT toàn cục
      }));

      paginatedValues.push({
        values: valueChunk,
        footer: footerWithIndex,
        pageIndex: paginatedValues.length + 1,
      });
    });
  });

  if (!props.patient) return null;
  return (
    <Main ref={refMain}>
      <Spin spinning={props.isLoadingPrint || !loadFinish}>
        {loadFinish &&
          paginatedValues.map(({ footer, values } = {}, index) => {
            return (
              <Page
                listAllKhoa={listAllKhoa}
                khoaChiDinhId={khoaChiDinhId}
                tieuDeTrai1={dataTIEU_DE_TRAI_1}
                tieuDeTrai2={dataTIEU_DE_TRAI_2}
                moreValueIds={props.moreValueIds}
                vitalSignsCategories={listAllChiSoSong}
                key={index}
                data={props.patient}
                values={values}
                style={{}}
                footer={footer}
                dataMA_CSS_VONG_CANH_TAY={dataMA_CSS_VONG_CANH_TAY}
              />
            );
          })}
      </Spin>
    </Main>
  );
};

const mapState = (state) => {
  let dataPrint = state.vitalSigns.dataPrint || {};
  return {
    isLoadingPrint: state.vitalSigns.isLoadingPrint,
    values: dataPrint.values || [],
    patient: dataPrint.patient,
    moreValueIds: dataPrint.moreValueIds || [],
  };
};

const mapDispatch = ({ vitalSigns: { getDataToPrint } }) => ({
  getDataToPrint,
});

export default connect(mapState, mapDispatch)(VitalSignsPrint);
