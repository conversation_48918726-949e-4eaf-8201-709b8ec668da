import React, { useCallback, useEffect, useMemo, useRef } from "react";
import { BLOOD_PRESSURE_1 } from "utils/vital-signs/constants";
import { cloneDeep, isEmpty } from "lodash";
import {
  drawLine,
  drawDate,
  handleBloodPressure,
  drawValueFooter,
  drawValueBody,
  drawBodyBloodPressure,
  drawLeftColumnFooter,
  drawLeftColumnBloodPressure,
  drawLeftColumnBackground,
} from "utils/vital-signs/canvas-utils";
import { Main } from "./styled";
import { useTranslation } from "react-i18next";
import { Col, Row } from "antd";
import Barcode from "components/editor/cores/Barcode";
import Image from "components/editor/cores/Image";
import { isNumber, isArray } from "utils/index";
import useListChiSoSong from "../hooks/useListChiSoSong";
import { useThietLap } from "hooks";
import { THIET_LAP_CHUNG } from "constants";

const SIZE = {
  columnWidth: 40,
  leftColumnWidth: 140,
  headerHeight: 110,
  rowHeight: 6,
  bottomHeight: 180,
};

export default function Page({
  moreValueIds = [],
  values = [],
  data = {},
  tieuDeTrai1,
  tieuDeTrai2,
  dataMA_CSS_VONG_CANH_TAY,
  listAllKhoa = [],
  khoaChiDinhId,
  footer,
}) {
  const [dataMAU_HEADER_01] = useThietLap(THIET_LAP_CHUNG.MAU_HEADER_01);
  const { t } = useTranslation();
  const { leftColumnWidth, columnWidth, headerHeight, rowHeight } = SIZE;
  const refCanvas = useRef(null);
  const showSurgery = process.env.REACT_APP_SHOW_SURGERY === "true";

  const { listChiSoSongKhac, listAllChiSoSong, listChiSoSongMacDinh } =
    useListChiSoSong({
      isNoiTru: true,
    });

  let moreValues = [...moreValueIds, ...listChiSoSongMacDinh];

  const bottomHeight = footer
    ? footer.length * 45 + 45
    : (SIZE.bottomHeight + dataMA_CSS_VONG_CANH_TAY?.eval() ? 45 : 0) +
      moreValues.length * 45;

  const canvasHeight = rowHeight * 75 + headerHeight + bottomHeight;
  let canvasWidth = Math.max(
    leftColumnWidth + values.length * columnWidth,
    710
  );
  const sizeLeftItem = leftColumnWidth / 3;

  let rangeBloodPressure = [];
  if (!isEmpty(values)) {
    const cloneValues = cloneDeep(values);
    const indexOfLastItemHasValue =
      cloneValues.length -
      1 -
      cloneValues.reverse().findIndex((item) => isNumber(item.huyetApTamThu));

    const newValue = handleBloodPressure(
      values[indexOfLastItemHasValue] && values[indexOfLastItemHasValue].huyetap
    );
    rangeBloodPressure =
      BLOOD_PRESSURE_1.find(
        (item) =>
          item.min <= newValue.huyetApTamTruong &&
          newValue.huyetApTamTruong <= item.max
      ) &&
      BLOOD_PRESSURE_1.find(
        (item) =>
          item.min <= newValue.huyetApTamTruong &&
          newValue.huyetApTamTruong <= item.max
      ).listShow;

    if (!isArray(rangeBloodPressure, 1)) {
      rangeBloodPressure = BLOOD_PRESSURE_1[0].listShow;
    }
  }
  if (!showSurgery) {
    values.map((item) => (item.ptTtId = null));
  }

  const adjustSize = useCallback(() => {
    if (refCanvas.current) {
      const rect = refCanvas.current.getBoundingClientRect();
      const ctx = refCanvas.current.getContext("2d");
      const scale = window.devicePixelRatio || 1;
      refCanvas.current.width = rect.width * scale;
      refCanvas.current.height = rect.height * scale;
      ctx.scale(scale, scale);
      refCanvas.current.style.width = `${rect.width}px`;
      refCanvas.current.style.height = `${rect.height}px`;
      draw();
    }
  }, [refCanvas.current]);

  useEffect(() => {
    adjustSize();
  }, [adjustSize]);

  useEffect(() => {
    window.addEventListener("resize", adjustSize);
    return () => {
      window.removeEventListener("resize", adjustSize);
    };
  }, []);

  useEffect(() => {
    window.addEventListener("beforeprint", adjustSize);
    return () => {
      window.removeEventListener("beforeprint", adjustSize);
    };
  }, []);

  const drawLineRow = (ctx) => {
    for (
      let i = 0;
      i < canvasHeight - bottomHeight - headerHeight;
      i = i + rowHeight
    ) {
      if (i % (rowHeight * 10) === 0) {
        drawLine(
          ctx,
          { x: leftColumnWidth, y: i + headerHeight },
          { x: canvasWidth, y: i + headerHeight },
          1.5
        );
      } else {
        drawLine(
          ctx,
          { x: leftColumnWidth, y: i + headerHeight },
          { x: canvasWidth, y: i + headerHeight },
          0.5,
          [10, 2]
        );
      }
    }
  };
  const drawLineColumn = (ctx) => {
    for (let i = 0; i < values.length + 1; i++) {
      drawLine(
        ctx,
        { x: i * columnWidth + leftColumnWidth, y: 0 },
        {
          x: i * columnWidth + leftColumnWidth,
          y: canvasHeight,
        },
        0.5
      );
    }
  };
  const drawText = (ctx, text, from, fontSize = 14) => {
    ctx.font = `${fontSize}px Times New Roman`;
    ctx.fillStyle = "black";
    ctx.fillText(text, from.x, from.y);
  };

  const drawHeader = (ctx) => {
    drawText(ctx, t("quanLyNoiTru.chiSoSong.ngayThang"), { x: 40, y: 20 }, 12);
    drawText(ctx, t("quanLyNoiTru.chiSoSong.huyet"), { x: 10, y: 50 }, 12);
    drawText(ctx, t("quanLyNoiTru.chiSoSong.ap"), { x: 17, y: 70 }, 12);
    drawText(
      ctx,
      t("quanLyNoiTru.chiSoSong.mach"),
      {
        x: sizeLeftItem + 5,
        y: 50,
      },
      12
    );
    drawText(
      ctx,
      t("quanLyNoiTru.chiSoSong.lPh"),
      {
        x: sizeLeftItem + 5,
        y: 70,
      },
      12
    );
    drawText(
      ctx,
      t("quanLyNoiTru.chiSoSong.nhiet"),
      {
        x: sizeLeftItem * 2 + 5,
        y: 50,
      },
      12
    );
    drawText(
      ctx,
      t("sinhHieu.doC"),
      {
        x: sizeLeftItem * 2 + 5,
        y: 70,
      },
      12
    );
    drawDate(ctx, values, SIZE);
  };

  const drawValues = (ctx, values) => {
    if (isEmpty(values)) return;
    values.forEach((item, index) => {
      try {
        drawBodyBloodPressure({
          ctx,
          item,
          huyetAp: item.huyetap,
          index,
          canvasHeight,
          bottomHeight,
          headerHeight,
          rangeBloodPressure,
          columnWidth: SIZE.columnWidth,
          bloodPressureHeight: 150,
          bottomRangeBloodPressure: 10,
          rowHeight: SIZE.rowHeight,
          totalRow: 75,
          leftColumnWidth: SIZE.leftColumnWidth,
        });
        drawValueBody({
          ctx,
          item,
          values,
          index,
          columnWidth: SIZE.columnWidth,
          leftColumnWidth: SIZE.leftColumnWidth,
          headerHeight: SIZE.headerHeight,
          bottomHeight,
          totalRow: 75,
          rowHeight: SIZE.rowHeight,
          startValueMach: 30,
          startValueNhiet: 34.5,
          SIZE,
          isPrint: true,
        });
        drawValueFooter({
          marginTop: canvasHeight - bottomHeight,
          ctxFooter: ctx,
          index,
          item,
          values,
          moreValueIds,
          SIZE,
          isPrint: true,
          dataMA_CSS_VONG_CANH_TAY,
          listAllChiSoSong: listAllChiSoSong,
          listChiSoSongMacDinh,
          listChiSoSongKhac,
          footer,
        });
      } catch (error) {}
    });
  };
  const drawBackground = (ctx) => {
    if (!ctx) {
      return;
    }
    drawLineRow(ctx);
    drawLineColumn(ctx);
    drawLeftColumnBackground({
      ctx,
      canvasWidth,
      canvasHeight,
      sizeLeftItem,
      bottomHeight,
      SIZE,
    });
    drawHeader(ctx);
    drawLeftColumnFooter({
      ctx,
      moreValueIds: moreValueIds,
      vitalSignsCategories: listChiSoSongKhac,
      canvasWidth: canvasWidth,
      canvasHeight: canvasHeight,
      bottomHeight: bottomHeight,
      isPrint: true,
      dataMA_CSS_VONG_CANH_TAY,
      listChiSoSongMacDinh,
      footer,
    });
    drawLeftColumnBloodPressure(ctx, rangeBloodPressure, SIZE);
  };
  const draw = () => {
    if (refCanvas.current) {
      const ctx = refCanvas.current.getContext("2d");
      drawBackground(ctx);
      drawValues(ctx, values);
    }
  };
  const renderChanDoan = useMemo(() => {
    const chiDinhChinh = (data.dsCdChinh || []).map((item) => item.ten).join();
    const chiDinhKemTheo = (data.dsCdKemTheo || [])
      .map((item) => item.ten)
      .join();
    return `${chiDinhChinh}  ${chiDinhKemTheo ? `, ${chiDinhKemTheo}` : ""}`;
  }, [data]);

  const formatTuoi = (text) => {
    const tuoi = text.replace(/(\d+)/, "<strong>$1</strong>");
    return tuoi;
  };

  if (!data) return null;

  let tenKhoa =
    data?.tenKhoaChiDinh ??
    listAllKhoa.find((i) => i.id === khoaChiDinhId)?.ten;

  return (
    <Main>
      {dataMAU_HEADER_01.eval() ? (
        <div className="flex justify-between gap-8">
          <div style={{ textAlign: "left", flex: 1, display: "flex" }}>
            <Image
              component={{
                props: {
                  isLogo: true,
                  width: 60,
                  height: 60,
                },
              }}
              mode={"editting"}
              form={data}
            ></Image>
            <div style={{ marginLeft: 10 }}>
              <div>{(tieuDeTrai1 || "").toUpperCase()}</div>
              <div style={{ fontWeight: "bold" }}>
                {(tieuDeTrai2 || "").toUpperCase()}
              </div>
              {tenKhoa ? (
                <div style={{ textAlign: "left" }}>
                  <span style={{ fontWeight: "bold" }}>{tenKhoa}</span>
                </div>
              ) : null}
            </div>
          </div>
          <div style={{ marginRight: 50, lineHeight: 1 }}>
            <Barcode
              component={{
                props: {
                  fieldName: "maHoSo",
                  width: 150,
                  height: 30,
                  noLabel: true,
                  contentAlign: "left",
                },
              }}
              form={{
                maHoSo: data?.maHoSo,
              }}
            />
            <div>Mã HS: {data.maHoSo}</div>
            <div>
              {data.maNb ? (
                <div>
                  Mã NB:<span> {data.maNb}</span>
                </div>
              ) : null}
            </div>
            <div>
              {data.maBenhAn ? (
                <div>
                  Mã BA:<span> {data.maBenhAn}</span>
                </div>
              ) : null}
            </div>
          </div>
        </div>
      ) : (
        <div className="flex justify-between gap-8">
          <div style={{ textAlign: "center", maxWidth: 350 }}>
            <div>{(tieuDeTrai1 || "").toUpperCase()}</div>
            <div style={{ fontWeight: "bold" }}>
              {(tieuDeTrai2 || "").toUpperCase()}
            </div>
            {tenKhoa ? (
              <div style={{ textAlign: "center" }}>
                {`${t("common.khoa")}: `}
                <span style={{ fontWeight: "bold" }}>{tenKhoa}</span>
              </div>
            ) : null}
          </div>
          <div>
            <Barcode
              component={{
                props: {
                  fieldName: "maHoSo",
                  width: 170,
                  height: 40,
                },
              }}
              form={{
                maHoSo: data?.maHoSo,
              }}
            />
          </div>
          <div style={{ marginRight: 50 }}>
            <div>{`${t("common.maHoSo")}: ${data.maHoSo}`}</div>
            <div>
              {data.maBenhAn ? (
                <div>
                  {t("common.maBenhAn")}:<span> {data.maBenhAn}</span>
                </div>
              ) : null}
            </div>
            <div>
              {data.maNb ? (
                <div>
                  {t("common.maNb")}:<span> {data.maNb}</span>
                </div>
              ) : null}
            </div>
          </div>
        </div>
      )}

      <div className="text-header flex flex-center">
        {t("quanLyNoiTru.chiSoSong.phieuTheoDoiChucNangSong").toUpperCase()}
      </div>
      <div>
        <Row>
          <Col span={12}>
            {`${t("quanLyNoiTru.chiSoSong.hoTenBenhNhan")}: `}
            <span
              style={{
                fontWeight: "bold",
                display: "inline-block",
                marginRight: 50,
              }}
            >
              {data.tenNb}
            </span>
          </Col>
          <Col span={6}>
            {`${t("common.tuoi").upperCaseFirstLetter()}: `}
            <span
              dangerouslySetInnerHTML={{ __html: formatTuoi(data.tuoi2) }}
              style={{ marginRight: 50 }}
            />{" "}
          </Col>
          <Col span={6}>
            {`${t("common.gioiTinh")}: `}
            <span style={{ fontWeight: "bold", marginRight: 50 }}>
              {data.gioiTinh === 1 ? t("common.nam") : t("common.nu")}
            </span>
          </Col>
        </Row>
        <Row>
          <Col span={12}>{`${t("common.phong")}: ${data.tenPhong || ""}`}</Col>
          <Col span={12}>
            <div style={{ marginRight: 50 }}>{`${t("quanLyNoiTru.giuong")}: ${
              data.soHieuGiuong || ""
            }`}</div>
          </Col>
        </Row>
        <div>
          <div style={{ marginRight: 50 }}>
            {data.dsCdChinh?.length || data.dsCdKemTheo?.length ? (
              <div>{`${t("common.chanDoan")}: ${renderChanDoan}`}</div>
            ) : null}
          </div>
        </div>
      </div>

      <div style={{ flexDirection: "row" }}>
        <div style={{ display: "flex", flexDirection: "row" }}>
          <div
            style={{
              color: "#E74C3C",
              fontWeight: "bold",
              fontSize: 18,
              lineHeight: 1,
            }}
          >
            x
          </div>
          <span style={{ marginLeft: 5, float: "left" }}>
            {t("quanLyNoiTru.chiSoSong.nhipMach")}
          </span>

          <img
            src={require("../images/bg_blue.png")}
            style={{
              width: 10,
              height: 10,
              background: "#5498DB",
              float: "left",
              marginTop: 5,
              marginLeft: 30,
              borderRadius: "50%",
            }}
            alt=""
          />
          <span style={{ marginLeft: 5, float: "left" }}>
            {t("quanLyNoiTru.chiSoSong.nhietDo")}
          </span>
          <img
            src={require("../images/huyet_ap.png")}
            style={{
              width: 10,
              height: 10,
              float: "left",
              marginTop: 5,
              marginLeft: 30,
            }}
            alt=""
          />
          <span style={{ marginLeft: 5, float: "left" }}>
            {t("quanLyNoiTru.chiSoSong.huyetAp")}
          </span>
        </div>
      </div>
      <div>
        <canvas
          ref={refCanvas}
          id="canvas"
          width={canvasWidth}
          height={canvasHeight}
        />
      </div>
      <ul className="list-surgery">
        {values.map((item, index) => {
          if (item.ptTtId) {
            return (
              <li key={index}>
                {`${item.thoiGianThucHien.format("dd/MM/yyyy HH:mm")} - ${t(
                  "tiepDon.bacSi"
                )}: `}
                {item.tenBacSiPtTt} - {item.phuongPhapPtTt}
              </li>
            );
          }
          return null;
        })}
      </ul>
    </Main>
  );
}
