import React, {
  forwardRef,
  memo,
  useEffect,
  useImperative<PERSON>andle,
  useRef,
  useState,
} from "react";
import { useSelector, useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { Form, Input } from "antd";
import { cloneDeep, isEmpty } from "lodash";
import classNames from "classnames";

import logUtils from "lib-utils/log-utils";
import { useEnum, useLoading, useStore } from "hooks";

import { Button, htmlToImage, ModalTemplate, Select } from "components";
import { Main } from "./styled";
import { ENUM } from "constants/index";
import UploadFile from "./components/UploadFile";
import fileUtils from "utils/file-utils";
import fileProvider from "data-access/file-provider";
import {
  isArray,
  copyToClipboard,
  getAndConvertAuthDataToString,
} from "utils/index";
import UploadForm from "components/UploadForm";
import { SVG } from "assets";
import { LOAI_YEU_CAU } from "constants/index";
import { createTextContent } from "./utils";

let acceptTypes = ".html,.pdf,.xlsx,.docx,.png,.jpg,.jpeg,.json,.txt,.log";

const ModalSupport = (props, ref) => {
  const { t } = useTranslation();
  const [state, _setState] = useState({
    loading: true,
    data: {},
    loadFinish: false,
    initCompleted: false, // Flag để tránh chạy initState nhiều lần
  });
  const refModal = useRef(null);
  const [listLoaiYeuCau] = useEnum(ENUM.LOAI_YEU_CAU, [], "/api/crm/v1");
  const auth = useStore("auth.auth");
  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan", null);
  const { branchCode, db, trangThaiDb } = useSelector((state) => state.utils);
  const { mayTinhId, deviceInfo, browser } = useSelector(
    (state) => state.application
  );
  const { showLoading, hideLoading } = useLoading();

  const [form] = Form.useForm();

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const {
    quanLyYeuCau: { guiYeuCau },
    utils: { getDb, getBranch, getHealth },
  } = useDispatch();

  useImperativeHandle(ref, () => ({
    show: async ({ data } = {}) => {
      try {
        setState({
          show: true,
          loading: true,
          loadFinish: false,
          data,
          initCompleted: false, // Reset flag khi mở modal mới
        });
        form.resetFields();
        if (!data?.id) {
          await getDb();
          await getBranch();
          await getHealth();
        }
      } catch (error) {
        console.error(error);
      } finally {
        setState({ loadFinish: true });
      }
    },
  }));

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  useEffect(() => {
    if (
      state.show &&
      !state.data?.id &&
      state.loadFinish &&
      !state.initCompleted
    ) {
      const initState = async () => {
        let dsDinhKem = [];
        try {
          setState({ loading: true });

          // --- file log ---
          const dataLog = await logUtils.getAllLogs();
          const file2 = fileUtils.textToFile(
            JSON.stringify(dataLog, null, 2),
            "sakura-data-log.json"
          );
          file2.url = URL.createObjectURL(file2);
          dsDinhKem.push(file2);

          // --- device info ---
          let device = cloneDeep(deviceInfo || {});
          if (typeof device !== "object") device = {};
          device.browser = browser;
          const file3 = fileUtils.textToFile(
            JSON.stringify(device, null, 2),
            "device-info.json"
          );
          file3.url = URL.createObjectURL(file3);
          dsDinhKem.push(file3);

          // --- account info ---
          const authData = getAndConvertAuthDataToString();
          if (authData) {
            const file4 = fileUtils.textToFile(
              authData,
              "thong-tin-tai-khoan.txt",
              "text/plain"
            );
            file4.url = URL.createObjectURL(file4);
            dsDinhKem.push(file4);
          }

          // --- software info ---
          const { fileText } = createTextContent({
            auth,
            dataUtils: { branchCode, db, trangThaiDb },
            mayTinhId,
            t,
          });
          const file5 = fileUtils.textToFile(
            fileText,
            "thong-tin-phan-mem.txt",
            "text/plain"
          );
          file5.url = URL.createObjectURL(file5);
          dsDinhKem.push(file5);

          // --- Screenshot (chạy bất đồng bộ sau khi UI đã sẵn sàng) ---
          setTimeout(async () => {
            try {
              const bodyEl = document.body;

              // Tối ưu filter để loại bỏ nhiều element không cần thiết hơn
              const filterNode = (node) => {
                if (node.classList) {
                  if (
                    node.classList.contains("modal-support") ||
                    node.classList.contains("ant-modal-mask") ||
                    node.classList.contains("popover-header-main")
                  ) {
                    return false;
                  }
                }
                if (
                  node.style?.display === "none" ||
                  node.style?.visibility === "hidden"
                )
                  return false;
                if (node.tagName === "SCRIPT" || node.tagName === "STYLE")
                  return false;
                return true;
              };

              const screenshotOptions = {
                filter: filterNode,
                quality: 0.8,
                pixelRatio: 1,
                cacheBust: false,
                width: Math.min(bodyEl.scrollWidth || 1920, 1920),
                height: Math.min(bodyEl.scrollHeight || 1080, 3000), // Giới hạn chiều cao
              };

              const timeoutPromise = new Promise((_, reject) =>
                setTimeout(
                  () => reject(new Error("html-to-image timeout")),
                  90000
                )
              );

              let htmlImg;
              try {
                htmlImg = await Promise.race([
                  htmlToImage.toPng(bodyEl, screenshotOptions),
                  timeoutPromise,
                ]);
              } catch (optionsError) {
                console.warn(
                  "html-to-image failed or timeout, trying minimal options:",
                  optionsError
                );
                try {
                  // Fallback với options tối thiểu
                  htmlImg = await Promise.race([
                    htmlToImage.toPng(bodyEl, {
                      ...screenshotOptions,
                      pixelRatio: 0.5,
                      quality: 0.6,
                      width: Math.min(screenshotOptions.width || 1280, 1280),
                      height: Math.min(screenshotOptions.height || 720, 2000),
                    }),
                    new Promise((_, reject) =>
                      setTimeout(
                        () => reject(new Error("Final timeout")),
                        30000
                      )
                    ),
                  ]);
                } catch (fallbackError) {
                  console.warn(
                    "html-to-image completely failed:",
                    fallbackError
                  );
                }
              }

              // Validate và thêm screenshot vào danh sách
              if (
                htmlImg &&
                htmlImg.length > 50 &&
                (htmlImg.startsWith("data:image/") ||
                  htmlImg.startsWith("data:"))
              ) {
                const file = fileUtils.base64ToFile(
                  htmlImg,
                  "anh-chup-man-hinh.png"
                );
                file.url = URL.createObjectURL(file);
                dsDinhKem.unshift(file);
              }
            } catch (error) {
              console.warn("Screenshot process failed:", error);
            } finally {
              // Cập nhật form với danh sách file sau khi screenshot hoàn thành
              form.setFieldsValue({ dsDinhKem });
              // Kết thúc loading sau khi screenshot hoàn thành (thành công hoặc thất bại)
              setState({ loading: false, initCompleted: true });
            }
          }, 100); // Delay ngắn để UI render xong
        } catch (error) {
          console.error(error?.message || error);
          // Nếu có lỗi trong quá trình tạo file khác, vẫn cập nhật form với các file đã tạo
          form.setFieldsValue({ dsDinhKem });
          setState({ loading: false, initCompleted: true });
        }
      };

      // ModalTemplate nếu có async action sẽ bị delay khi show nên sẽ đợi show rồi mới xử lý async action
      setTimeout(initState, 300);
    }
  }, [state.show, state.data?.id, state.loadFinish, state.initCompleted]);

  useEffect(() => {
    if (state.show) {
      if (state.data?.id) {
        form.setFieldsValue({
          url: state.data.url,
          loai: state.data.loai,
          maHoSo: state.data.maHoSo,
          noiDung: state.data.noiDung,
          tieuDe: state.data.tieuDe,
          dsDinhKem: state.data.dsDinhKem,
        });
      } else if (state.loadFinish) {
        // set data form default
        const { noiDungText } = createTextContent({
          auth,
          dataUtils: { branchCode, db, trangThaiDb },
          mayTinhId,
          t,
        });
        form.setFieldsValue({
          url: window.location.href,
          loai: LOAI_YEU_CAU.LOI,
          noiDung: noiDungText,
          maHoSo: !isEmpty(thongTinCoBan)
            ? `maHoSo: ${thongTinCoBan.maHoSo}, maNb: ${thongTinCoBan.maNb}, nbDotDieuTriId: ${thongTinCoBan.id}`
            : "",
        });
      }
    }
  }, [
    state.show,
    auth,
    mayTinhId,
    thongTinCoBan,
    state.data,
    branchCode,
    db,
    trangThaiDb,
    state.loadFinish,
  ]);

  const onOk = (isOk) => () => {
    if (isOk) {
      form.submit();
    } else {
      setState({ show: false });
    }
  };

  const onSaoChepDuongDan = (ma) => {
    let url = `${window.location.origin}/quan-ly-yeu-cau/danh-sach-yeu-cau?ma=${ma}`;

    copyToClipboard(url);
  };

  const onSaoChep = (ma) => {
    navigator.permissions.query({ name: "clipboard-write" }).then((result) => {
      if (result.state === "granted" || result.state === "prompt") {
        onSaoChepDuongDan(ma);
      } else {
        console.warn("Clipboard access denied");
        setState({ show: false });
      }
    });
  };

  const onHandleSubmit = async () => {
    try {
      showLoading();
      const values = await form.validateFields();
      const {
        tieuDe,
        loai,
        url,
        maHoSo,
        noiDung,
        dsDinhKem: listFiles,
      } = values || {};
      let dsDinhKem = [];
      if (isArray(listFiles, true)) {
        let files = [];
        for (const item of listFiles) {
          const fileObj = item.originFileObj || item;

          const cleanFileObj = new File([fileObj], fileObj.name, {
            type: fileObj.type,
            lastModified: fileObj.lastModified,
          });

          files.push(cleanFileObj);
        }
        const s = await fileProvider.upload(null, "dinhKem", null, files);
        if (isArray(s?.data, true)) {
          dsDinhKem = [...dsDinhKem, ...s.data];
        }
      }
      const res = await guiYeuCau({
        tieuDe,
        loai,
        url,
        maHoSo,
        noiDung,
        dsDinhKem: isArray(dsDinhKem, true) ? dsDinhKem : null,
      });
      onSaoChep(res.ma);
      setState({ show: false });
    } catch (error) {
      console.error(error?.message || error);
    } finally {
      hideLoading();
    }
  };

  const handleUpLoad = (data) => {
    form.setFieldsValue({ dsDinhKem: data });
  };

  return (
    <ModalTemplate
      ref={refModal}
      wrapClassName="modal-support"
      title={t("common.guiYeuCauHoTro")}
      width={"80vw"}
      onCancel={onOk(false)}
      actionRight={
        !state.data?.id ? (
          <>
            <Button minWidth={100} iconHeight={15} onClick={onOk(false)}>
              {t("common.huy")}
            </Button>
            <Button
              type="primary"
              minWidth={100}
              iconHeight={15}
              onClick={!state.loading ? onOk(true) : () => {}}
            >
              {t("common.xacNhan")}
            </Button>
          </>
        ) : (
          <Button
            type="primary"
            minWidth={100}
            iconHeight={15}
            rightIcon={<SVG.IcSaoChep />}
            onClick={() => onSaoChepDuongDan(state.data?.ma)}
          >
            {t("common.saoChep")}
          </Button>
        )
      }
      isShowButtonSupport={false}
      destroyOnClose
    >
      <Main>
        <Form
          form={form}
          style={{ width: "100%" }}
          className="form-custom"
          onFinish={onHandleSubmit}
          layout="horizontal"
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 18 }}
          disabled={!!state.data?.id}
        >
          <Form.Item
            label={t("common.tieuDe")}
            name="tieuDe"
            rules={[
              {
                required: true,
                message: t("quanLyYeuCau.vuiLongNhapTieuDe"),
              },
            ]}
          >
            <Input placeholder={t("common.nhapTieuDe")} />
          </Form.Item>
          <Form.Item
            label={t("common.loaiYeuCau")}
            name="loai"
            rules={[
              {
                required: true,
                message: t("quanLyYeuCau.vuiLongChonLoaiYeuCau"),
              },
            ]}
          >
            <Select
              data={listLoaiYeuCau}
              placeholder={t("common.chonLoaiYeuCau")}
              className={classNames({ "select-disabled": !!state.data?.id })}
            />
          </Form.Item>
          <Form.Item label={t("common.urlManHinh")} name="url">
            <Input disabled />
          </Form.Item>
          <Form.Item label={t("common.maHoSo")} name="maHoSo">
            <Input placeholder={t("common.nhapMaHoSo")} />
          </Form.Item>
          <Form.Item label={t("common.noiDung")} name="noiDung">
            <Input.TextArea autoSize style={{ whiteSpace: "pre-wrap" }} />
          </Form.Item>
          <Form.Item label={t("common.fileDinhKem")} name="dsDinhKem">
            {state.data?.id ? (
              <UploadForm
                multiple
                textBtn=""
                accept={acceptTypes}
                provider="dinhKem"
                pathApi="/api/crm/v1"
                showPreviewImg
                disabled={!!state.data?.id}
              />
            ) : (
              <UploadFile
                multiple
                accept={acceptTypes}
                provider="dinhKem"
                isLoading={state.loading}
                afterUpload={handleUpLoad}
                pathApi="/api/crm/v1"
                showPreviewImg
              />
            )}
          </Form.Item>
        </Form>
      </Main>
    </ModalTemplate>
  );
};

export default memo(forwardRef(ModalSupport));
