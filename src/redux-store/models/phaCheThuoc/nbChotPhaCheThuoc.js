import { PAGE_SIZE, PAGE_DEFAULT } from "constants/index";
import baseStore from "../base-store";
import nbChotPhaCheThuocProvider from "data-access/phaCheThuoc/nb-chot-pha-che-thuoc-provider";
import { combineSort } from "utils";
import { message } from "antd";
import printProvider from "data-access/print-provider";
import { t } from "i18next";

export default {
  ...baseStore({
    fetchProvider: nbChotPhaCheThuocProvider,
    storeName: "nbChotPhaCheThuoc",
    initState: {
      isLoading: false,
      listData: [],
      dataTongHopTheoId: {},
      totalElements: null,
      page: PAGE_DEFAULT,
      size: PAGE_SIZE,
      dataSearch: {},
      dataSortColumn: {},
    },

    customEffect: ({ dispatch }) => ({
      onSearchTongHop: ({ page = 0, ...payload }, state) => {
        return new Promise((resolve, reject) => {
          dispatch.nbChotPhaCheThuoc.updateData({ isLoading: true, page });
          let size = payload.size || state.nbChotPhaCheThuoc.size || 10;
          const sort = combineSort(
            payload.dataSortColumn ||
              state.nbChotPhaCheThuoc.dataSortColumn ||
              {}
          );
          const dataSearch =
            payload.dataSearch || state.nbChotPhaCheThuoc.dataSearch || {};
          nbChotPhaCheThuocProvider
            .searchTongHop({ ...dataSearch, page, size, sort, active: true })
            .then((s) => {
              resolve(s?.data);
              dispatch.nbChotPhaCheThuoc.updateData({
                listData: (s?.data || []).map((item, index) => {
                  item.index = page * size + index + 1;
                  return item;
                }),
                isLoading: false,
                totalElements: s?.totalElements || 0,
                page,
              });
            })
            .catch((e) => {
              message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
              reject(e);
              dispatch.nbChotPhaCheThuoc.updateData({
                listData: [],
                isLoading: false,
              });
            });
        });
      },
      onChangeInputSearch: ({ page, size = 10, ...payload }, state) => {
        return new Promise((resolve) => {
          const dataSearch = {
            ...(state.nbChotPhaCheThuoc.dataSearch || {}),
            ...payload,
          };
          dispatch.nbChotPhaCheThuoc.updateData({
            page,
            size,
            dataSearch,
          });
          dispatch.nbChotPhaCheThuoc.onSearchTongHop({
            page,
            size,
            dataSearch,
          });
        });
      },
      onSizeChange: ({ dataSearch, ...rest }, state) => {
        dispatch.nbChotPhaCheThuoc.updateData({
          ...rest,
        });
        dispatch.nbChotPhaCheThuoc.onSearchTongHop({ page: 0, ...rest });
      },
      onSortChange: ({ ...payload }, state) => {
        const dataSortColumn = {
          ...state.nbChotPhaCheThuoc.dataSortColumn,
          ...payload,
        };
        dispatch.nbChotPhaCheThuoc.updateData({
          dataSortColumn,
        });
        dispatch.nbChotPhaCheThuoc.onSearchTongHop({
          page: 0,
          dataSortColumn,
        });
      },
      inPhieuXuatPhaChe: (id, state) => {
        nbChotPhaCheThuocProvider.getPhieuXuatPhaChe(id).then((s) => {
          printProvider.printPdf(s.data);
        });
      },
      inPhieuXuatKho: (id, state) => {
        nbChotPhaCheThuocProvider.getPhieuXuatKho(id).then((s) => {
          printProvider.printPdf(s.data);
        });
      },
      phieuBanGiaoThanhPhamTPN: (id, state) => {
        nbChotPhaCheThuocProvider.getPhieuBanGiaoThanhPhamTPN(id).then((s) => {
          printProvider.printPdf(s.data);
        });
      },
      themMoiPhieuXuatPhaChe: async (payload, state) => {
        return new Promise((resolve, reject) => {
          nbChotPhaCheThuocProvider
            .post(payload)
            .then((res) => {
              if (res?.code === 0) {
                message.success(t("common.themMoiThanhCongDuLieu"));
                resolve(res.data);
              } else {
                reject(res);
                message.error(
                  res?.message || t("common.xayRaLoiVuiLongThuLaiSau")
                );
              }
            })
            .catch((err) => {
              message.error(
                err?.message || t("common.xayRaLoiVuiLongThuLaiSau")
              );
              reject(err);
            });
        });
      },
      capNhatPhieuXuatPhaChe: async (payload, state) => {
        return new Promise((resolve, reject) => {
          nbChotPhaCheThuocProvider
            .put(payload)
            .then((response) => {
              message.success(t("common.capNhatThanhCong"));
              resolve(response?.data);
            })
            .catch((err) => {
              message.error(
                err?.message || t("common.xayRaLoiVuiLongThuLaiSau")
              );
              reject(err);
            });
        });
      },
      onSearchTongHopTheoId: async (id, state) => {
        return new Promise((resolve, reject) => {
          try {
            nbChotPhaCheThuocProvider
              .getTongHopTheoId(id)
              .then((s) => {
                dispatch.nbChotPhaCheThuoc.updateData({
                  dataTongHopTheoId: s?.data,
                });
                resolve(s?.data);
              })
              .catch((e) => {
                message.error(
                  e?.message || t("common.xayRaLoiVuiLongThuLaiSau")
                );
                dispatch.nbChotPhaCheThuoc.updateData({
                  dataTongHopTheoId: {},
                });
                reject(err);
              });
          } catch (err) {
            message.error(err.message.toString());
            reject(err);
          }
        });
      },

      deletePhieuXuatPhaChe: (id, state) => {
        return new Promise((resolve, reject) => {
          nbChotPhaCheThuocProvider
            .delete(id)
            .then((s) => {
              resolve(s);
              message.success(t("common.xoaDuLieuThanhCong"));
            })
            .catch((e) => {
              reject(e);
              message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            });
        });
      },
      hoanThanhPhieuXuatPhaChe: (id, state) => {
        return new Promise((resolve, reject) => {
          nbChotPhaCheThuocProvider
            .hoanThanhPhieuXuatPhaChe(id)
            .then((s) => {
              resolve(s);
              message.success(t("common.capNhatThanhCong"));
            })
            .catch((e) => {
              reject({ id, ...e });
              message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            });
        });
      },
      huyHoanThanhPhieuXuatPhaChe: (payload, state) => {
        return new Promise((resolve, reject) => {
          nbChotPhaCheThuocProvider
            .huyHoanThanhPhieuXuatPhaChe(payload)
            .then((s) => {
              message.success(t("common.capNhatThanhCong"));
              resolve(s);
            })
            .catch((e) => {
              reject({ id: payload.id, ...e });
              message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            });
        });
      },
      getnbChotPhaCheThuocById: (id, state) => {
        return new Promise((resolve, reject) => {
          nbChotPhaCheThuocProvider
            .getById(id)
            .then((res) => {
              if (res && res.code === 0) {
                resolve(res.data);
                dispatch.nbChotPhaCheThuoc.updateData({
                  nbChotPhaCheThuoc: res.data,
                });
              }
              reject(res);
            })
            .catch((err) => {
              message.error(err.message.toString());
              reject(err);
            });
        });
      },
    }),
  }),
};
