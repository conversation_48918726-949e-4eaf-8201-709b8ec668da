import { message } from "antd";
import nbDichVuKyThuatProvider from "data-access/nb-dich-vu-ky-thuat-provider";
import { t } from "i18next";
import { toSafePromise } from "lib-utils";
import { cloneDeep } from "lodash";
import { getState } from "redux-store/stores";
import retryFetch from "utils/retryFetch";

const initialState = {
  dataSearch: {},
  listData: [],
  loading: false,
  pageNumber: 0,
  totalPages: 0,
  totalElements: null,
};

export default {
  state: cloneDeep(initialState),
  reducers: {
    updateData(state, payload = {}) {
      return { ...state, ...payload };
    },
    clearData(state, payload = {}) {
      return { ...cloneDeep(initialState), ...payload };
    },
  },
  effects: (dispatch) => ({
    getList: retryFetch(
      (payload, state) => {
        return new Promise((resolve, reject) => {
          const page = payload.page ?? state.nbDichVuKyThuat.pageNumber ?? 0;
          const size = payload.size ?? state.nbDichVuKyThuat.size ?? 10;
          const dataSearch = {
            ...state.nbDichVuKyThuat.dataSearch,
            ...payload,
            page,
            size,
          };
          dispatch.nbDichVuKyThuat.updateData({ loading: true });
          nbDichVuKyThuatProvider
            .search(dataSearch)
            .then((res) => {
              dispatch.nbDichVuKyThuat.updateData({
                listData: res.data.map((i, index) => {
                  i.index = index + 1 + page * size;
                  return i;
                }),
                loading: false,
                pageNumber: res.pageNumber,
                totalPages: res.totalPages,
                totalElements: res.totalElements,
              });
              resolve(res);
            })
            .catch((err) => {
              if (err?.code !== "ERR_CANCELED") {
                dispatch.nbDichVuKyThuat.updateData({
                  loading: false,
                });
              }
              reject(err);
            });
        });
      },
      3,
      0
    ),
    onSearch: (payload, state) => {
      return nbDichVuKyThuatProvider.search(payload);
    },
    onChangeInputSearch: ({ page = 0, size, ...payload }, state) => {
      const dataSearch = {
        ...state.nbDichVuKyThuat.dataSearch,
        ...payload,
      };
      dispatch.nbDichVuKyThuat.updateData({ dataSearch });
      return dispatch.nbDichVuKyThuat.getList({ page: page, size });
    },
    tomTatKqCls: (payload, state) => {
      return new Promise((resolve, reject) => {
        nbDichVuKyThuatProvider
          .tomTatKqCls(payload)
          .then((res) => {
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    thongBaoCls: (payload, state) => {
      return new Promise((resolve, reject) => {
        nbDichVuKyThuatProvider
          .thongBaoCls(payload)
          .then((res) => {
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    thongBaoDv: (payload, state) => {
      return new Promise((resolve, reject) => {
        nbDichVuKyThuatProvider
          .thongBaoDv(payload)
          .then((res) => {
            if (res.code === 0) {
              message.success(t("common.daLuuDuLieu"));
              resolve(res);
            }
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    kiemTraTrungNguoiThucHien: ({
      dichVuId,
      nguoiThucHienId,
      thoiGianThucHien,
      thoiGianCoKetQua,
    }, state) => {
      return new Promise((resolve, reject) => {
        nbDichVuKyThuatProvider
          .kiemTraDvktTrungNguoiThucHien({
            dichVuId,
            nguoiThucHienId,
            thoiGianThucHien,
            thoiGianCoKetQua,
          })
          .then((res) => {
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    xacNhanTrungNguoiThucHien: ({
      dichVuId,
      thoiGianCoKetQua,
      nguoiThucHienId,
      thoiGianThucHien,
      duKienKetQua,
      hideLoading,
      showLoading,
      showAsyncConfirm,
    }, state) => {
      return new Promise(async (resolve, reject) => {
        // Kiểm tra trùng người thực hiện
        let resultTrungNguoiThucHien = null;
        const [err, overlapCheckResult] = await toSafePromise(
          dispatch.nbDichVuKyThuat.kiemTraTrungNguoiThucHien({
            dichVuId: dichVuId,
            nguoiThucHienId: nguoiThucHienId || getState().auth.auth.nhanVienId,
            thoiGianThucHien: thoiGianThucHien,
            thoiGianCoKetQua: thoiGianCoKetQua,
          })
        );
        // if err throw error end function
        if (err) {
          throw new Error(err);
        }

        // Nếu có kết quả trùng người thực hiện
        if (overlapCheckResult?.data && overlapCheckResult?.code === 0) {
          const {
            tenNguoiThucHien,
            tenDichVu,
            thoiGianThucHien,
            thoiGianCoKetQua,
          } = overlapCheckResult.data;

          const startTime = moment(thoiGianThucHien).format("HH:mm DD/MM/YYYY");
          const endTime = moment(thoiGianCoKetQua).format("HH:mm DD/MM/YYYY");
          hideLoading?.();
          resultTrungNguoiThucHien = await showAsyncConfirm({
            title: t("common.canhBao"),
            content: t("cdha.canhBaoTrungNguoiThucHien", {
              tenNguoiThucHien: tenNguoiThucHien,
              tenDichVu: tenDichVu,
              startTime: startTime,
              endTime: endTime,
            }),
            showBtnOk: true,
            classNameOkText: "button-warning",
          });

          if (resultTrungNguoiThucHien.action !== "ok") {
            return; // Hủy thao tác nếu user không xác nhận
          }
        }

        // Kiểm tra thời gian thực hiện thực tế vs thời gian dự kiến
        if (thoiGianThucHien) {
          const thoiGianThucHienThucTe = moment(thoiGianCoKetQua).diff(
            moment(thoiGianThucHien),
            "minutes"
          );

          // Kiểm tra nếu có thời gian dự kiến có kết quả
          const thoiGianDuKien = duKienKetQua;
          if (thoiGianDuKien) {
            if (thoiGianThucHienThucTe < thoiGianDuKien) {
              hideLoading?.();
              const resultThoiGian = await showAsyncConfirm({
                title: t("common.canhBao"),
                content: t("cdha.canhBaoThoiGianThucHien", {
                  thoiGianThucHienThucTe: thoiGianThucHienThucTe,
                  thoiGianDuKien: thoiGianDuKien,
                }),
                showBtnOk: true,
                classNameOkText: "button-warning",
              });
              if (resultThoiGian.action !== "ok") {
                reject(false);
              }
            }
          }
        }
        resolve(true);
      });
    },
  }),
};
