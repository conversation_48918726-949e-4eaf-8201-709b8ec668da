import baseStore from "redux-store/models/base-store";
import nbTheNbProvider from "data-access/nb-the-nb-provider";
import { message } from "antd";
import { t } from "i18next";

export default {
  ...baseStore({
    fetchProvider: nbTheNbProvider.initHuy,
    storeName: "nbTheNbHuy",
    customEffect: ({ dispatch }) => ({
      huyTheAna: (payload) => {
        return new Promise((resolve, reject) => {
          nbTheNbProvider
            .huyTheAna(payload)
            .then((s) => {
              resolve(s);
              message.success(t("tiepDon.baoMatTheThanhCong"));
            })
            .catch((e) => {
              message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
              reject(e);
            });
        });
      },
    }),
  }),
};
