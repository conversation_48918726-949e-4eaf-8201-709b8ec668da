import nbDotDieuTriProvider from "data-access/nb-dot-dieu-tri-provider";
import printProvider from "data-access/print-provider";
import { cloneDeep, flatten, groupBy, isArray, isEmpty, orderBy } from "lodash";
import stringUtils from "mainam-react-native-string-utils";
import { message } from "antd";
import { t } from "i18next";
import {
  LOAI_BIEU_MAU,
  LOAI_IN_BANG_KE_CHI_PHI,
  MA_BIEU_MAU_EDITOR,
  THIET_LAP_CHUNG,
  LIST_PHIEU_IN_EDITOR,
  LIST_PHIEU_IN_EDITOR_ALL,
  LIST_PHIEU_IN_POPUP,
  LIST_PHIEU_IN_EDITOR_THEO_SO_PHIEU,
  LIST_PHIEU_IN_WORD_THEO_SO_PHIEU,
  LIST_PHIEU_IN_BANG_KE,
  LIST_PHIEU_KQ_XN_CDHA,
  LIST_PHIEU_KQ_XN_KHONG_CALL_API,
} from "constants/index";
import danhSachPhieuChoKyProvider from "data-access/kySo/danh-sach-phieu-cho-ky-provider";
import editorUtils from "utils/editor-utils";
import { combineUrlParams } from "utils";
import nbTuVongProvider from "data-access/nb-tu-vong-provider";
import nbKhamKSKLaiXeProvider from "data-access/nb-kham-ksk-lai-xe-provider";
import { getState } from "redux-store/stores";
import isofhToolProvider from "data-access/isofh-tool-provider";
import { showError } from "utils/message-utils";
import fileUtils from "utils/file-utils";
import env from "module_env/ENV";

const initState = {
  isLoadingListPhieu: false,
  listPhieu: [],
  listPhieuTemp: [],
  refreshListPhieu: 0,
  dsPhieuKy: [],
  phieuChiDinhKhamBenh: null,
  phieuChiDinhCDHA: null,
  phieuTongHopKhamBenh: null,
  phieuKqCls: null,
};
export default {
  state: {
    ...cloneDeep(initState),
  },
  reducers: {
    updateData(state, payload = {}) {
      return { ...state, ...payload };
    },
    clearData(state, payload = {}) {
      return { ...cloneDeep(initState), ...payload };
    },
  },
  effects: (dispatch) => ({
    getListPhieu: ({
      nbDotDieuTriId,
      nbThongTinId,
      chiDinhTuLoaiDichVu,
      dsChiDinhTuLoaiDichVu,
      chiDinhTuDichVuId,
      isInHsba = false,
      ignoreLoadPhieu = false,
      ...payload
    }) => {
      return new Promise(async (resolve, reject) => {
        try {
          let listPhieu =
            (
              await nbDotDieuTriProvider.getPhieuIn({
                nbDotDieuTriId,
                chiDinhTuLoaiDichVu,
                dsChiDinhTuLoaiDichVu,
                chiDinhTuDichVuId,
                ...payload,
              })
            ).data || [];

          if (
            ["03501", "03601", "00606"].includes(payload.maViTri) &&
            chiDinhTuDichVuId
          ) {
            listPhieu = listPhieu.map((phieu) => ({
              ...phieu,
              dsSoPhieu: (phieu.dsSoPhieu || []).filter(
                (item) => item.soPhieu == chiDinhTuDichVuId
              ),
            }));
          }

          const thietLapBangKeTongHopExcel =
            await dispatch.thietLap.getThietLap({
              ma: THIET_LAP_CHUNG.BANG_KE_TONG_HOP_EXCEL,
            });
          let listPhieuInEditor = isInHsba
            ? LIST_PHIEU_IN_EDITOR
            : LIST_PHIEU_IN_EDITOR_ALL;
          if (thietLapBangKeTongHopExcel?.eval()) {
            listPhieuInEditor = listPhieuInEditor.filter((phieu) =>
              ["P062", "P554", "P678"].every((el) => el !== phieu)
            );
          }
          //kiểm tra xem có tồn tại mã P040 không
          const index = listPhieu.findIndex((item) => item.ma == "P040");
          // nếu tồn tại
          if (index != -1) {
            if (nbThongTinId) {
              const item = listPhieu[index];
              //thì lấy ds gói dv của người bệnh
              const listNbGoiDv = await dispatch.nbGoiDv.getByNbThongTinId({
                nbThongTinId,
              });
              // thêm vào mỗi gói dv tương ứng với 1 phiếu
              listPhieu.splice(
                index,
                1,
                ...listNbGoiDv.map((nbGoiDv) => {
                  const item2 = cloneDeep(item);
                  item2.nbGoiDvId = nbGoiDv.id;
                  item2.key += nbGoiDv.id;
                  item2.ten += " - " + nbGoiDv.id;
                  return item2;
                })
              );
            }
          }

          let returnListPhieu = [];
          let dsNbChuyenKhoa = [];
          if (listPhieu.some((item) => item.ma === "P253")) {
            dsNbChuyenKhoa = await dispatch.nbChuyenKhoa.getNbChuyenKhoa({
              nbDotDieuTriId,
            });
          }
          listPhieu.forEach((item) => {
            let mhParams = {};
            if (item.kySo) {
              mhParams = {
                nbDotDieuTriId,
                chiDinhTuLoaiDichVu,
                dsChiDinhTuLoaiDichVu,
                chiDinhTuDichVuId,
                ...payload,
                kySo: true,
                maPhieuKy: item.ma,
              };
            }

            //get link editor của các phiếu. Nếu ko phải editor thì linkEditor = ""=> ko silent loading
            item.linkEditor = ignoreLoadPhieu
              ? ""
              : editorUtils.getLinkHeadless({
                  nbDotDieuTriId,
                  nbThongTinId,
                  soPhieu: item.dsSoPhieu?.[0]?.soPhieu,
                  ...payload,
                  ma: item.ma,
                  nbGoiDvId: item.nbGoiDvId,
                  mhParams,
                  baoCaoId: item.baoCaoId,
                  loaiBieuMau: item.loaiBieuMau,
                  maBaoCao: item.maBaoCao,
                });

            //ở màn hsba với những phiếu editor có nhiều số phiếu và get link editor theo số phiếu
            if (
              LIST_PHIEU_IN_EDITOR_THEO_SO_PHIEU.includes(item.ma) &&
              isInHsba
            ) {
              item.dsSoPhieu.forEach((sp, idx) => {
                let newItem = cloneDeep(item);
                newItem.dsSoPhieu = [sp];
                if (item.ma === "P253") {
                  payload.khoaChiDinhId = dsNbChuyenKhoa.find(
                    (el) => el.id == sp.soPhieu
                  )?.khoaId;
                }
                let _soPhieu = sp.soPhieu;
                if (item.ma === "P266") _soPhieu = sp.phieuXuatId;
                if (item.ma === "P368") {
                  _soPhieu = sp.chiDinhTuDichVuId;
                }
                if (item.ma === "P421" || item.ma === "P719") {
                  payload.khoaChiDinhId = sp?.khoaChiDinhId;
                }
                if (["P231", "P791"].includes(item.ma)) {
                  _soPhieu = sp.ten1;
                  payload.conThu = sp?.ten2;
                }
                newItem.linkEditor = ignoreLoadPhieu
                  ? ""
                  : editorUtils.getLinkHeadless({
                      nbDotDieuTriId,
                      nbThongTinId,
                      ...payload,
                      ma: item.ma,
                      thoiGianThucHien: sp?.thoiGianThucHien,
                      nbGoiDvId: item.nbGoiDvId,
                      mhParams,
                      soPhieu: _soPhieu,
                      baoCaoId: item.baoCaoId,
                      tuThoiGian: sp?.tuThoiGian,
                      denThoiGian: sp?.denThoiGian,
                      loaiBieuMau: item.loaiBieuMau,
                      ...(item.ma === "P487"
                        ? {
                            chiDinhTuDichVuId: sp?.chiDinhTuDichVuId,
                            chiDinhTuLoaiDichVu: sp?.chiDinhTuLoaiDichVu,
                          }
                        : {}),
                    });

                if (
                  ["P290", "P208", "P207", "P871"].includes(item.ma) &&
                  item.loaiBieuMau === LOAI_BIEU_MAU.BIEU_MAU_CHI_XEM
                ) {
                  newItem.linkEditor = null;
                }

                newItem.key = stringUtils.guid();

                returnListPhieu.push(newItem);
              });
            } else {
              if (
                item.ma === "P207" &&
                item.loaiBieuMau === LOAI_BIEU_MAU.BIEU_MAU_CHI_XEM
              ) {
                item.linkEditor = null;
              }
              if (item.ma === "P253") {
                let khoaChiDinhId = null;
                khoaChiDinhId = dsNbChuyenKhoa.find(
                  (el) => (el.id = payload.soPhieu)
                )?.khoaId;
                item.linkEditor = ignoreLoadPhieu
                  ? ""
                  : editorUtils.getLinkHeadless({
                      nbDotDieuTriId,
                      nbThongTinId,
                      ...payload,
                      khoaChiDinhId,
                      ma: item.ma,
                      nbGoiDvId: item.nbGoiDvId,
                      mhParams,
                      loaiBieuMau: item.loaiBieuMau,
                    });
              }

              if (
                ["P062", "P678", "P554", "P677"].includes(item.ma) &&
                thietLapBangKeTongHopExcel?.toLowerCase() === "true"
              ) {
                item.linkEditor = null;
              }

              item.key = stringUtils.guid();
              //Trong màn HSBA => theo constant
              if (isInHsba) {
                if (listPhieuInEditor.includes(item.ma)) {
                  item.type = "editor";
                }
              } else {
                //Trong các màn khác => nếu là bảng kê => check thiết lập BANG_KE_TONG_HOP_EXCEL
                // = true => in pdf
                // = false => in editor
                // còn lại thì lấy theo loại biểu mẫu
                if (LIST_PHIEU_IN_BANG_KE.includes(item.ma)) {
                  if (!thietLapBangKeTongHopExcel?.eval()) {
                    item.type = "editor";
                  }
                } else {
                  if (item.loaiBieuMau === LOAI_BIEU_MAU.BIEU_MAU_CHINH_SUA) {
                    item.type = "editor";
                  }
                }
              }

              if (LIST_PHIEU_IN_POPUP.includes(item.ma)) {
                item.type = "popup";
              }

              if (
                isInHsba &&
                LIST_PHIEU_KQ_XN_KHONG_CALL_API.includes(item.ma)
              ) {
                //Với các phiếu kết quả XN + CDHA => lọc lấy các phiếu có trả về đường dẫn file pdf để xử lý ko call api
                if ((item.dsSoPhieu || []).some((x) => x.dsDuongDan)) {
                  item.dsSoPhieu = item.dsSoPhieu.filter((x) => x.dsDuongDan);

                  returnListPhieu.push(item);
                }
              } else {
                returnListPhieu.push(item);
              }
            }
          });

          resolve(returnListPhieu);
        } catch (error) {
          console.log(error);
          resolve([]);
        }
      });
    },
    getThongTinPhieu: (
      {
        phieu: item,
        nbDotDieuTriId,
        chiDinhTuLoaiDichVu,
        dsChiDinhTuLoaiDichVu,
        chiDinhTuDichVuId,
        showError,
        bienBanHoiChanId,
        ...payload
      },
      state
    ) => {
      return new Promise(async (resolve, reject) => {
        try {
          const thietLapBangKeTongHopExcel =
            await dispatch.thietLap.getThietLap({
              ma: THIET_LAP_CHUNG.BANG_KE_TONG_HOP_EXCEL,
            });
          const s = await nbDotDieuTriProvider.getThongTinPhieu({
            phieu: item,
            nbDotDieuTriId,
            chiDinhTuLoaiDichVu,
            dsChiDinhTuLoaiDichVu,
            chiDinhTuDichVuId,
            showError,
            thietLapBangKeTongHopExcel,
            bienBanHoiChanId,
            ...payload,
          });
          resolve(s);
        } catch (error) {
          reject(error);
        }
      });
    },
    getPhieuIn: (
      {
        nbDotDieuTriId,
        nbThongTinId,
        chiDinhTuLoaiDichVu,
        dsChiDinhTuLoaiDichVu,
        chiDinhTuDichVuId,
        isThuNgan,
        noScrollEl = false,
        ignoreLoadPhieu = false,
        fromToolDongGoi = false,
        ...payload
      } = {},
      state
    ) => {
      return new Promise(async (resolve, reject) => {
        try {
          dispatch.phieuIn.updateData({
            isLoadingListPhieu: true,
            nbDotDieuTriId,
          });
          const data = await dispatch.phieuIn.getListPhieu({
            nbDotDieuTriId,
            nbThongTinId,
            chiDinhTuLoaiDichVu,
            dsChiDinhTuLoaiDichVu,
            chiDinhTuDichVuId,
            ignoreLoadPhieu,
            ...payload,
          });
          let listPhieu = cloneDeep(data).filter(
            (item) => item.type != "editor" && item.type != "popup"
            // (item) => item.type != "popup"
          );
          const list = [];
          for (let i = 0; i < listPhieu.length; i++) {
            const item = listPhieu[i];
            //nếu là phiếu scan thì load tất cả các số phiếu vào danh sách
            if (item.loaiBieuMau === LOAI_BIEU_MAU.BIEU_MAU_SCAN) {
              item.dsSoPhieu.forEach((x) => {
                list.push({
                  ...item,
                  key: stringUtils.guid(),
                  dsSoPhieu: [x],
                });
              });
            } else {
              if (item.ma == "P033") {
                item.dsSoPhieu = item.dsSoPhieu.filter(
                  (x) => x.soPhieu == payload?.phieuThuId
                );
              }
              //nếu là phiếu chi thì hiển thị tất cả phiếu chi
              if (item.ma == "P034") {
                item.dsSoPhieu.forEach((x) => {
                  list.push({
                    ...item,
                    key: stringUtils.guid(),
                    dsSoPhieu: [x],
                  });
                });
              }

              //get đơn theo số phiếu => tách về dạng tree
              if (LIST_PHIEU_IN_WORD_THEO_SO_PHIEU.includes(item.ma)) {
                item.dsSoPhieu.forEach((x) => {
                  let newItem = {
                    ...item,
                    key: stringUtils.guid(),
                    dsSoPhieu: [x],
                  };
                  if (x.lichSuKyId) {
                    //đánh dấu phiếu đã ký
                    newItem.isPhieuDaKy = true;
                  }

                  list.push(newItem);
                });
              } else {
                //kiểm tra phiếu đã ký => load phiếu đã ký
                if (
                  item.loaiBieuMau !== LOAI_BIEU_MAU.BIEU_MAU_CHINH_SUA &&
                  item?.dsSoPhieu?.[0].lichSuKyId
                ) {
                  //đánh dấu phiếu đã ký
                  item.isPhieuDaKy = true;
                }
                list.push(item);
              }
            }
          }
          const _group = groupBy(list || [], "ma");
          //logic sắp xếp chuyển từ commit 04e24ab843b0208e6789be8817248765bbf3c728
          //[SAKURA-54595] FE(NDTP.0350) Hồ sơ bệnh án - Sắp xếp giấy tờ có level 2 có thời gian thưc hiện: sắp xếp từ bé đến lớn
          listPhieu = flatten(
            Object.keys(_group).map((key) => {
              return orderBy(
                _group[key],
                (x) => x.dsSoPhieu[0]?.thoiGianThucHien,
                "asc"
              );
            })
          );

          if (fromToolDongGoi) {
            resolve(listPhieu);
            return;
          }

          //nếu có phiếu
          if (listPhieu.length) {
            //thì mặc định load phiếu đầu tiên để hiển thị ra trước
            if (!listPhieu[0].linkEditor && !ignoreLoadPhieu) {
              // kiem tra xem phiếu đầu tiên có phải là phiếu editor không
              const phieu = await dispatch.phieuIn.getThongTinPhieu({
                phieu: listPhieu[0],
                nbDotDieuTriId,
                nbThongTinId,
                chiDinhTuLoaiDichVu,
                dsChiDinhTuLoaiDichVu,
                chiDinhTuDichVuId,
                ...payload,
              });
              if (phieu && !phieu.code) {
                //nếu load thành công thì gắn thông tin vào field data của phiếu
                listPhieu[0].data = phieu;
                if (phieu.filePdf) {
                  //sau đó tải file về
                  const urlFileLocal =
                    await dispatch.phieuIn.getDataDanhSachPhieu({
                      dsFile: phieu.filePdf,
                    });
                  phieu.urlFileLocal = urlFileLocal;
                }
              }
            }
          }
          const listSelected = listPhieu.map((item) => item.key);

          if (!noScrollEl) {
            dispatch.phieuIn.updateData({
              elementScrollingPdfKey: listSelected[0],
              // selectedIds: [...flatten(listSelected)],
              listPhieu: listPhieu,
            });
          } else {
            dispatch.phieuIn.updateData({
              listPhieuTemp: listPhieu,
            });
          }

          //nếu tải lại phiếu sau khi ký ở màn hsba => ko xử lý silent load
          if (!payload?.onRefreshPhieuKy && !ignoreLoadPhieu) {
            //sau đó tải ngầm những phiếu còn lại
            dispatch.phieuIn.silentLoadingFile({
              listPhieu,
              data: {
                nbDotDieuTriId,
                nbThongTinId,
                chiDinhTuLoaiDichVu,
                dsChiDinhTuLoaiDichVu,
                chiDinhTuDichVuId,
                ...payload,
              },
            });

            resolve(data);
          } else {
            // lấy thông tin key mới sinh ra để thực hiện reload phiếu mới ký
            const newKey = listPhieu.find(
              (x) => x.ma === payload?.maPhieu
            )?.key;
            resolve(newKey);
          }
        } catch (error) {
          console.log(error);
          resolve([]);
        } finally {
          dispatch.phieuIn.updateData({
            isLoadingListPhieu: false,
          });
        }
      });
    },
    getPhieuInVaKy: (
      {
        phieuKy = null,
        nbDotDieuTriId,
        nbThongTinId,
        chiDinhTuLoaiDichVu,
        dsChiDinhTuLoaiDichVu,
        chiDinhTuDichVuId,
        noScrollEl = false,
        isGetListPhieu = false,
        paramsTheoMaPhieu = {},
        ...payload
      },
      state
    ) => {
      return new Promise(async (resolve, reject) => {
        try {
          dispatch.phieuIn.updateData({
            isLoadingListPhieu: true,
          });

          let listPhieu = [];

          //trường hợp mở popup in và get ds phiếu mới
          if (isGetListPhieu) {
            const data = await dispatch.phieuIn.getListPhieu({
              nbDotDieuTriId,
              nbThongTinId,
              chiDinhTuLoaiDichVu,
              dsChiDinhTuLoaiDichVu,
              chiDinhTuDichVuId,
              ...payload,
              maManHinh: payload?.maManHinh2,
              maViTri: payload?.maViTri2,
            });

            const dsPhieuIn = cloneDeep(data).filter(
              (item) => item.type != "editor" && item.type != "popup"
            );

            dsPhieuIn.forEach((phieu) => {
              if (phieu.dsSoPhieu?.length) {
                //với tính năng in phiếu chỉ định chưa in
                if (payload.inPhieuChiDinh === false) {
                  //phiếu chỉ định chưa in thường được trả về trong api phiếu in với soPhieu=0
                  //nếu in rồi thì sẽ có số phiếu
                  //mục đích là api phiếu in đang trả về tất cả các phiếu đã in gom theo số phiếu.
                  //chỉ in phiếu chưa in thì lọc ra số phiếu = 0
                  const soPhieu0 = phieu.dsSoPhieu.filter(
                    (item) => item.soPhieu == 0
                  );
                  listPhieu.push({
                    ...phieu,
                    dsSoPhieu: [soPhieu0],
                    key: stringUtils.guid(),
                  });
                } else
                  phieu.dsSoPhieu.forEach((soPhieu) => {
                    let returnSoPhieu = {
                      ...phieu,
                      dsSoPhieu: [soPhieu],
                      key: stringUtils.guid(),
                    };

                    if (soPhieu?.lichSuKyId) {
                      //đánh dấu phiếu đã ký
                      returnSoPhieu.isPhieuDaKy = true;
                    }

                    //bỏ đi những phiếu có số phiếu bị trùng
                    if (
                      listPhieu.findIndex(
                        (x) =>
                          x.ma == phieu.ma &&
                          x?.dsSoPhieu?.[0].soPhieu == soPhieu?.soPhieu
                      ) == -1 ||
                      phieu.ma === "P801"
                    ) {
                      if (
                        !(
                          phieu.ma === "P801" &&
                          (soPhieu.soPhieu === "null" ||
                            soPhieu?.soPhieu != chiDinhTuDichVuId)
                        )
                      ) {
                        listPhieu.push(returnSoPhieu);
                      }
                    }
                  });

                //đối với trường hợp api phieu-in trả ra dsSoPhieu ( P801 ) không chứa soPhieu khớp với id đang khám, FE gọi api in đơn thuốc theo soPhieu đầu tiên
                if (
                  phieu.ma === "P801" &&
                  listPhieu.findIndex((x) => x.ma === "P801") === -1
                ) {
                  listPhieu.push({
                    ...phieu,
                    dsSoPhieu: [phieu.dsSoPhieu[0]],
                    key: stringUtils.guid(),
                    isPhieuDaKy: !!phieu.dsSoPhieu[0]?.lichSuKyId,
                  });
                }
              }
            });
          } else if (isArray(phieuKy)) {
            listPhieu = phieuKy.map((item) => {
              item.key = stringUtils.guid();
              if (item?.dsSoPhieu?.[0].lichSuKyId) {
                //đánh dấu phiếu đã ký
                item.isPhieuDaKy = true;
              }

              return item;
            });
          } else {
            if (LIST_PHIEU_IN_WORD_THEO_SO_PHIEU.includes(phieuKy.ma)) {
              let _dsSoPhieu = (phieuKy.dsSoPhieu || []).map(
                (item) => item.soPhieu
              );
              //trường hợp in và ký phiếu theo mã
              listPhieu = (phieuKy.dsSoPhieu || [])
                .filter((item, index) => {
                  if (
                    _dsSoPhieu.indexOf(item?.soPhieu) !== index &&
                    phieuKy.ma != "P801"
                  ) {
                    return false;
                  }
                  if (
                    phieuKy.ma === "P801" &&
                    (item?.soPhieu === "null" ||
                      item?.soPhieu != chiDinhTuDichVuId)
                  ) {
                    return false;
                  }

                  return true;
                })
                .map((item) => {
                  let returnItem = {
                    ...phieuKy,
                    dsSoPhieu: [item],
                    key: stringUtils.guid(),
                  };

                  if (item?.lichSuKyId) {
                    //đánh dấu phiếu đã ký
                    returnItem.isPhieuDaKy = true;
                  }

                  return returnItem;
                });

              //đối với trường hợp api phieu-in trả ra dsSoPhieu ( P801 ) không chứa soPhieu khớp với id đang khám, FE gọi api in đơn thuốc theo soPhieu đầu tiên
              if (
                phieuKy.ma === "P801" &&
                listPhieu.length === 0 &&
                (phieuKy.dsSoPhieu || []).length > 0
              ) {
                listPhieu = [(phieuKy.dsSoPhieu || [])[0]].map((item) => {
                  let returnItem = {
                    ...phieuKy,
                    dsSoPhieu: [item],
                    key: stringUtils.guid(),
                  };

                  if (item?.lichSuKyId) {
                    //đánh dấu phiếu đã ký
                    returnItem.isPhieuDaKy = true;
                  }

                  return returnItem;
                });
              }
            } else {
              //kiểm tra phiếu đã ký => load phiếu đã ký
              if (
                phieuKy.loaiBieuMau !== LOAI_BIEU_MAU.BIEU_MAU_CHINH_SUA &&
                phieuKy?.dsSoPhieu?.[0].lichSuKyId
              ) {
                //đánh dấu phiếu đã ký
                phieuKy.isPhieuDaKy = true;
              }

              phieuKy.key = stringUtils.guid();
              listPhieu.push(phieuKy);
            }
          }

          //ds phiếu load lỗi
          const listErrPhieuKeys = [];
          const listErrPhieuMessages = {};

          //nếu có phiếu => load hết phiếu để có thể có thông tin ký/ hủy ký
          if (listPhieu.length) {
            await Promise.all(
              listPhieu.map(async (phieuItem) => {
                let phieu;
                const params = paramsTheoMaPhieu[phieuItem.ma];

                phieu = await dispatch.phieuIn.getThongTinPhieu({
                  phieu: phieuItem,
                  nbDotDieuTriId,
                  nbThongTinId,
                  chiDinhTuLoaiDichVu,
                  dsChiDinhTuLoaiDichVu,
                  chiDinhTuDichVuId,
                  ...payload,
                  ...params,
                });

                if (phieu && !phieu.code) {
                  //nếu load thành công thì gắn thông tin vào field data của phiếu
                  phieuItem.data = phieu;
                  if (phieu.filePdf) {
                    //sau đó tải file về
                    const urlFileLocal =
                      await dispatch.phieuIn.getDataDanhSachPhieu({
                        dsFile: phieu.filePdf,
                      });
                    phieu.urlFileLocal = urlFileLocal;
                  }
                } else {
                  listErrPhieuKeys.push(phieuItem.key);
                  listErrPhieuMessages[phieuItem.ma] = phieu?.data?.message;
                }
              })
            );
          }

          //Với phiếu chỉ định MH khám bệnh
          // => Lọc bỏ những phiếu load lỗi hoặc do ko có file trả về
          if (
            (isGetListPhieu && payload?.maViTri2 == "00304") ||
            phieuKy.ma == "P801"
          ) {
            listPhieu = listPhieu.filter(
              (phieu) => !listErrPhieuKeys.includes(phieu.key)
            );
          }

          //Nếu ko có phiếu nào thì trả ra lỗi
          if (!listPhieu.length) {
            const dataKHONG_IN_DON_THUOC_KHI_THIEU_TIEN_TAM_UNG =
              await dispatch.thietLap.getThietLap({
                ma: THIET_LAP_CHUNG.KHONG_IN_DON_THUOC_KHI_THIEU_TIEN_TAM_UNG,
              });
            if (
              phieuKy.ma == "P801" &&
              dataKHONG_IN_DON_THUOC_KHI_THIEU_TIEN_TAM_UNG?.eval()
            ) {
              reject({
                code: -1,
                message:
                  listErrPhieuMessages[phieuKy.ma] ||
                  t("phieuIn.khongTonTaiPhieuIn"),
              });
            } else {
              reject({ code: -1, message: t("phieuIn.khongTonTaiPhieuIn") });
            }
          }

          const listSelected = listPhieu.map((item) => item.key);

          if (!noScrollEl) {
            dispatch.phieuIn.updateData({
              elementScrollingPdfKey: listSelected[0],
              // selectedIds: [...flatten(listSelected)],
              listPhieu: listPhieu,
            });
          } else {
            dispatch.phieuIn.updateData({
              listPhieuTemp: listPhieu,
            });
          }

          resolve(listPhieu);
        } catch (error) {
          console.log(error);
          resolve([]);
        } finally {
          dispatch.phieuIn.updateData({
            isLoadingListPhieu: false,
          });
        }
      });
    },
    clearSilentJob: () => {
      if (window.listTimeout && isArray(window.listTimeout)) {
        window.listTimeout.forEach((item) => {
          clearTimeout(item);
        });
      }
    },
    silentLoadingFile: (payload, state) => {
      return new Promise(async (resolve, reject) => {
        const data = payload.data || {};
        const listPhieu = (payload.listPhieu || []).filter((item, index) => {
          return !item.data && index != 0;
        });
        dispatch.phieuIn.clearSilentJob();
        let numberEditor = 0;
        window.listTimeout = listPhieu.map((phieu, index) => {
          // if (!phieu.linkEditor)
          return setTimeout(
            (dispatch, index, phieu, data, nbDotDieuTriId) => {
              dispatch.phieuIn
                .getThongTinPhieu({
                  phieu: phieu,
                  ...data,
                })
                .then(async (data) => {
                  const { listPhieu, nbDotDieuTriId: currentNbDotDieuTriId } =
                    getState().phieuIn;
                  if (nbDotDieuTriId == currentNbDotDieuTriId) {
                    if (data && !data.code) {
                      //nếu load thành công thì gắn thông tin vào field data của phiếu
                      const index = listPhieu.findIndex(
                        (item) => item.key == phieu.key
                      );
                      if (index == -1) return;

                      listPhieu[index].data = data;
                      if (data.filePdf) {
                        //sau đó tải file về
                        const urlFileLocal =
                          await dispatch.phieuIn.getDataDanhSachPhieu({
                            dsFile: data.filePdf,
                          });
                        data.urlFileLocal = urlFileLocal;
                      }
                      listPhieu[index] = { ...listPhieu[index] };
                    }
                    // dispatch.phieuIn.updateData({
                    //   listPhieu: [...state.phieuIn.listPhieu],
                    // });
                  }
                });
            },
            !phieu.linkEditor ? index * 1000 : ++numberEditor * 15000, //nếu là file editor thì chờ lâu hơn 1 chút
            dispatch,
            index,
            phieu,
            data,
            data.nbDotDieuTriId
          );
          // return null;
        });
      });
    },
    getDataDanhSachPhieu: ({ dsFile, mode, ...payload }, state) => {
      return new Promise((resolve, reject) => {
        printProvider
          .getMergePdf(dsFile)
          .then((s) => {
            console.info("Print success");
            resolve(s);
          })
          .catch((err) => {
            console.error("Print fail", err);
            resolve(null);
          });
      });
    },
    updatePhieu: ({ key, data }, state) => {
      return new Promise((resolve, reject) => {
        const listPhieu = cloneDeep(state.phieuIn.listPhieu || []);
        const phieu = listPhieu.find((item) => item.key == key);
        if (phieu) {
          phieu.data = data;
        }
        dispatch.phieuIn.updateData({
          listPhieu: [...listPhieu],
        });
      });
    },
    updateDataFileLoaded: ({ key, data }, state) => {
      return new Promise((resolve, reject) => {
        const fileLoaded = cloneDeep(state.phieuIn.fileLoaded || {});
        if (fileLoaded) {
          fileLoaded[key] = data;
        }
        dispatch.phieuIn.updateData({
          fileLoaded: { ...fileLoaded },
        });
        resolve();
      });
    },
    showFileEditor: async (
      {
        phieu,
        mhParams = {},
        ngonNguParams = {},
        inKemParams = [],
        ...payload
      },
      state
    ) => {
      const _newMhParams = { ...mhParams, baoCaoId: phieu.baoCaoId };

      if (inKemParams && Array.isArray(inKemParams) && inKemParams.length > 0) {
        inKemParams.forEach((phieuKem) => {
          dispatch.phieuIn.showFileEditor({
            phieu: phieuKem,
            ...payload,
            id: null, //bỏ id của phiếu cha
          });
        });
      }

      switch (phieu.ma) {
        case "P037":
        case "P056":
        case "P079":
        case "P090":
        case "P095":
        case "P106":
        case "P105":
        case "P113":
        case "P117":
        case "P118":
        case "P119":
        case "P120":
        case "P122":
        case "P123":
        case "P124":
        case "P125":
        case "P129":
        case "P131":
        case "P132":
        case "P146":
        case "P150":
        case "P154":
        case "P156":
        case "P157":
        case "P162":
        case "P163":
        case "P172":
        case "P174":
        case "P175":
        case "P179":
        case "P180":
        case "P213":
        case "P217":
        case "P218":
        case "P229":
        case "P230":
        case "P231":
        case "P232":
        case "P240":
        case "P251":
        case "P283":
        case "P284":
        case "P285":
        case "P286":
        case "P295":
        case "P296":
        case "P297":
        case "P299":
        case "P310":
        case "P312":
        case "P313":
        case "P314":
        case "P315":
        case "P316":
        case "P317":
        case "P318":
        case "P323":
        case "P324":
        case "P356":
        case "P357":
        case "P369":
        case "P375":
        case "P376":
        case "P377":
        case "P386":
        case "P387":
        case "P388":
        case "P398":
        case "P399":
        case "P401":
        case "P404":
        case "P405":
        case "P411":
        case "P412":
        case "P414":
        case "P432":
        case "P433":
        case "P434":
        case "P435":
        case "P436":
        case "P437":
        case "P429":
        case "P440":
        case "P450":
        case "P449":
        case "P446":
        case "P447":
        case "P456":
        case "P455":
        case "P469":
        case "P470":
        case "P473":
        case "P474":
        case "P471":
        case "P472":
        case "P475":
        case "P476":
        case "P478":
        case "P480":
        case "P503":
        case "P504":
        case "P515":
        case "P522":
        case "P516":
        case "P517":
        case "P520":
        case "P521":
        case "P523":
        case "P524":
        case "P481":
        case "P542":
        case "P543":
        case "P547":
        case "P548":
        case "P567":
        case "P569":
        case "P573":
        case "P574":
        case "P575":
        case "P580":
        case "P581":
        case "P582":
        case "P587":
        case "P588":
        case "P589":
        case "P590":
        case "P596":
        case "P597":
        case "P598":
        case "P627":
        case "P628":
        case "P635":
        case "P636":
        case "P605":
        case "P633":
        case "P634":
        case "P591":
        case "P640":
        case "P641":
        case "P642":
        case "P643":
        case "P644":
        case "P645":
        case "P658":
        case "P659":
        case "P660":
        case "P661":
        case "P662":
        case "P673":
        case "P674":
        case "P646":
        case "P647":
        case "P683":
        case "P706":
        case "P708":
        case "P709":
        case "P710":
        case "P713":
        case "P714":
        case "P720":
        case "P721":
        case "P722":
        case "P723":
        case "P724":
        case "P725":
        case "P736":
        case "P737":
        case "P751":
        case "P752":
        case "P766":
        case "P767":
        case "P768":
        case "P773":
        case "P777":
        case "P795":
        case "P797":
        case "P798":
        case "P800":
        case "P804":
        case "P807":
        case "P809":
        case "P811":
        case "P813":
        case "P816":
        case "P820":
        case "P821":
        case "P822":
        case "P824":
        case "P826":
        case "P828":
        case "P837":
        case "P839":
        case "P840":
        case "P841":
        case "P843":
        case "P847":
        case "P850":
        case "P851":
        case "P854":
        case "P855":
        case "P858":
        case "P859":
        case "P861":
        case "P862":
        case "P875":
        case "P881":
        case "P904":
        case "P906":
        case "P907":
        case "P920":
        case "P921":
        case "P874":
        case "P961":
        case "P1086":
        case "P1114":
        case "P1115":
        case "P888":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload?.nbDotDieuTriId
              }`,
              {
                ..._newMhParams,
                ...(phieu.ma == "P044"
                  ? { chiDinhTuLoaiDichVu: payload.chiDinhTuLoaiDichVu }
                  : {}),
                ...(["P713", "P714"].includes(phieu.ma)
                  ? {
                      chiDinhTuLoaiDichVu: payload.chiDinhTuLoaiDichVu,
                      khoaChiDinhId: payload.khoaChiDinhId,
                    }
                  : {}),
                ...(["P231", "P217"].includes(phieu.ma)
                  ? { conThu: payload.conThu }
                  : {}),
              }
            )
          );
          break;
        case "P121":
        case "P497":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload?.nbDotDieuTriId
              }`,
              {
                id: payload?.nbDotDieuTriId,
                ..._newMhParams,
              }
            )
          );
          break;
        case "P898":
        case "P916":
        case "P917":
        case "P864":
        case "P817":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload?.id
              }`,
              {
                ..._newMhParams,
                nbDotDieuTriId: payload?.nbDotDieuTriId,
              }
            )
          );
          break;
        case "P899":
        case "P965":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/`,
              {
                ..._newMhParams,
                nbDvCdhaTdcnPtTtId: payload?.id,
                nbDotDieuTriId: payload?.nbDotDieuTriId,
              }
            )
          );
          break;
        case "P900":
        case "P951":
        case "P1067":
        case "P1003":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/`,
              {
                ..._newMhParams,
                nbDvCdhaTdcnPtTtId: payload?.id,
                nbDotDieuTriId: payload?.nbDotDieuTriId,
                id: undefined,
              }
            )
          );
          break;
        case "P887":
        case "P892":
        case "P911":
        case "P932":
        case "P937":
        case "P926":
        case "P936":
        case "P1030":
        case "P1059":
        case "P1032":
        case "P1069":
        case "P1036":
        case "P1034":
        case "P1118":
        case "P1093":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}`,
              {
                ..._newMhParams,
                nbDotDieuTriId: payload?.nbDotDieuTriId,
              }
            )
          );
          break;
        case "P1043":
        case "P1044":
        case "P1045":
        case "P1047":
        case "P1049":
        case "P1050":
        case "P1051":
        case "P1052":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}`,
              {
                ..._newMhParams,
                nbDotDieuTriId: payload?.nbDotDieuTriId,
                nbDvChePhamMauId: payload?.id,
              }
            )
          );
          break;
        case "P390":
        case "P393":
        case "P454":
        case "P1075":
        case "P1076":

        case "P1078":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload?.bienBanHoiChanId || payload?.chiDinhTuDichVuId
              }`,
              _newMhParams
            )
          );
          break;
        case "P136":
        case "P185":
        case "P239":
        case "P370":
        case "P395":
        case "P396":
        case "P397":
        case "P406":
        case "P422":
        case "P423":
        case "P438":
        case "P439":
        case "P464":
        case "P462":
        case "P486":
        case "P484":
        case "P491":
        case "P498":
        case "P499":
        case "P489":
        case "P490":
        case "P513":
        case "P514":
        case "P556":
        case "P557":
        case "P558":
        case "P562":
        case "P571":
        case "P599":
        case "P609":
        case "P632":
        case "P651":
        case "P712":
        case "P792":
        case "P794":
        case "P834":
        case "P829":
        case "P866":
        case "P905":
        case "P922":
        case "P950":
        case "P956":
        case "P1010":
        case "P1097":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload?.id || ""
              }`,
              {
                nbDotDieuTriId: payload.nbDotDieuTriId,
                khoaChiDinhId: payload.khoaChiDinhId,
                thoiGianThucHien: payload.thoiGianThucHien,
                baoCaoId: phieu.baoCaoId,
                chiDinhTuLoaiDichVu: payload.chiDinhTuLoaiDichVu,
                ...(phieu.ma == "P922" ? { boSung: payload.boSung } : {}),
                ..._newMhParams,
              }
            )
          );
          break;
        case "P988":
        case "P990":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload?.id || ""
              }`,
              {
                nbDotDieuTriId: payload.nbDotDieuTriId,
                khoaChiDinhId: payload.khoaChiDinhId,
                thoiGianThucHien: payload.thoiGianThucHien,
                baoCaoId: phieu.baoCaoId,
                denKhoaId: payload.denKhoaId,
                ..._newMhParams,
              }
            )
          );
          break;
        case "P506":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload?.id || ""
              }`,
              {
                nbDotDieuTriId: payload.nbDotDieuTriId,
                khoaChiDinhId: payload.khoaChiDinhId,
                thoiGianThucHien: payload.thoiGianThucHien,
                baoCaoId: phieu.baoCaoId,
                dsTrangThaiHoan: [0, 10],
                ..._newMhParams,
              }
            )
          );
          break;
        case "P116":
        case "P153": // Phiếu tóm tắt BA
        case "P155":
        case "P311":
        case "P127":
        case "P173":
        case "P126":
        case "P748":
        case "P749":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.nbDotDieuTriId
              }`,
              {
                baoCaoId: phieu.baoCaoId,
                ..._newMhParams,
                ...(ngonNguParams || {}),
              }
            )
          );
          break;
        case "P032":
          window.open(
            combineUrlParams(`/print-file/bang-ke/${payload.nbDotDieuTriId}`, {
              ma: payload.ma,
              maBaoCao: payload.maBaoCao,
              loai: LOAI_IN_BANG_KE_CHI_PHI.BAO_HIEM,
              notPrint: !!payload.notPrint,
              khongDongTab: payload.khongDongTab,
              nbDvKhamId: payload?.nbDvKhamId,
              ..._newMhParams,
            })
          );
          break;
        case "P178":
          window.open(
            combineUrlParams(`/print-file/bang-ke/${payload.nbDotDieuTriId}`, {
              ma: payload.ma,
              maBaoCao: payload.maBaoCao,
              loai: LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_BHYT,
              notPrint: !!payload.notPrint,
              khongDongTab: payload.khongDongTab,
              nbDvKhamId: payload?.nbDvKhamId,
              ..._newMhParams,
            })
          );
          break;
        case "P035":
        case "P553":
        case "P625":
        case "P730":
        case "P733":
        case "P739":
        case "P741":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.id
              }`,
              _newMhParams
            )
          );
          break;
        case "P038":
        case "P039":
        case "P254":
        case "P282":
        case "P281":
        case "P681":
        case "P682":
        case "P685":
        case "P686":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.nbDvKhamId
              }`,
              _newMhParams
            )
          );
          break;
        case "P040":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.goiDvId
              }`,
              _newMhParams
            )
          );

          break;
        case "P041":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.nbThongTinId
              }`,
              _newMhParams
            )
          );

          break;
        case "P042":
        case "P043":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.nbDvKhamId
              }`,
              _newMhParams
            )
          );

          break;
        case "P871":
        case "P054":
        case "P208":
        case "P207":
        case "P865":
        case "P1071":
          let _id;
          if (Array.isArray(phieu?.dsSoPhieu) && phieu?.dsSoPhieu.length > 0) {
            _id = phieu?.dsSoPhieu[0]?.soPhieu;
          } else {
            _id = payload.chiDinhTuDichVuId;
          }
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${_id}`,
              {
                ..._newMhParams,
                nbDotDieuTriId: payload.nbDotDieuTriId,
              }
            )
          );
          break;
        case "P210":
        case "P212":
        case "P483":
        case "P479":
        case "P500":
        case "P501":
        case "P502":
        case "P488":
        case "P631":
        case "P711":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                phieu?.dsSoPhieu[0]?.soPhieu
              }`,
              {
                ..._newMhParams,
                nbDotDieuTriId: payload.nbDotDieuTriId,
              }
            )
          );
          break;
        case "P877":
        case "P878":
        case "P879":
        case "P880":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.hoaDonId ?? phieu?.dsSoPhieu[0]?.soPhieu
              }`,
              {
                ..._newMhParams,
                nbDotDieuTriId: payload.nbDotDieuTriId,
              }
            )
          );
          break;
        case "P726":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                phieu?.dsSoPhieu[0]?.soPhieu
              }`,
              {
                ..._newMhParams,
                nbDotDieuTriId: payload.nbDotDieuTriId,
                capCuu: true,
              }
            )
          );
          break;
        case "P062":
        case "P554":
          window.open(
            combineUrlParams(`/print-file/bang-ke/${payload.id}`, {
              ma: payload.ma,
              maBaoCao: payload.maBaoCao,
              loai: LOAI_IN_BANG_KE_CHI_PHI.TONG_HOP,
              notPrint: !!payload.notPrint,
              khongDongTab: payload.khongDongTab,
              ..._newMhParams,
            })
          );
          break;
        case "P678":
          window.open(
            combineUrlParams(`/print-file/bang-ke/${payload.id}`, {
              ma: payload.ma,
              maBaoCao: payload.maBaoCao,
              loai: LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_DICH_VU,
              notPrint: !!payload.notPrint,
              khongDongTab: payload.khongDongTab,
              ..._newMhParams,
            })
          );
          break;
        case "P677":
          window.open(
            combineUrlParams(`/print-file/bang-ke/${payload.id}`, {
              ma: payload.ma,
              maBaoCao: payload.maBaoCao,
              loai: LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_DICH_VU_BH,
              notPrint: !!payload.notPrint,
              khongDongTab: payload.khongDongTab,
              ..._newMhParams,
            })
          );
          break;
        case "P746":
        case "P747":
          window.open(
            combineUrlParams(`/print-file/bang-ke/${payload.id}`, {
              ma: payload.ma,
              maBaoCao: payload.maBaoCao,
              loai: LOAI_IN_BANG_KE_CHI_PHI.KCB_NOI_TRU,
              notPrint: !!payload.notPrint,
              khongDongTab: payload.khongDongTab,
              ..._newMhParams,
            })
          );
          break;
        case "P073":
        case "P074":
        case "P268":
        case "P273":
        case "P077":
        case "P407":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.id
              }`,
              _newMhParams
            )
          );

          break;
        case "P086":
        case "P233":
        case "P466":
        case "P467":
        case "P606":
        case "P688":
          const param = {
            nbDotDieuTriId: payload.nbDotDieuTriId,
            khoaChiDinhId: payload.khoaChiDinhId,
            tuThoiGian: payload.tuThoiGian,
            denThoiGian: payload.denThoiGian,
            ..._newMhParams,
          };

          const link = combineUrlParams(
            `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
              payload?.id || ""
            }`,
            param
          );
          window.open(link);
          break;
        case "P044":
        case "P151":
        case "P537":
        case "P774":
        case "P781":
        case "P784":
        case "P941":
        case "P963":
        case "P1072":
        case "P1073":
        case "P1090":
        case "P1132":
        case "P1134":
        case "P1210":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload?.id || ""
              }`,
              {
                nbDotDieuTriId: payload.nbDotDieuTriId,
                khoaChiDinhId: payload.khoaChiDinhId,
                thoiGianThucHien: payload.thoiGianThucHien,
                chiDinhTuDichVuId: payload.chiDinhTuDichVuId,
                chiDinhTuLoaiDichVu: payload.chiDinhTuLoaiDichVu,
                ..._newMhParams,
              }
            )
          );
          break;
        case "P361":
          window.open(
            combineUrlParams(
              ` ${
                ["choray"].includes(env.HOSPITAL)
                  ? "/quan-ly-noi-tru/dieu-duong/phieu-cham-soc-cap-2/"
                  : "/editor/bao-cao/"
              }${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}${
                payload?.id ? "/" + payload.id : ""
              }`,
              {
                nbDotDieuTriId: payload.nbDotDieuTriId,
                khoaChiDinhId: payload.khoaChiDinhId,
                thoiGianThucHien: payload.thoiGianThucHien,
                chiDinhTuDichVuId: payload.chiDinhTuDichVuId,
                chiDinhTuLoaiDichVu: payload.chiDinhTuLoaiDichVu,
                ..._newMhParams,
              }
            )
          );
          break;
        case "P366":
          window.open(
            combineUrlParams(
              ` ${
                ["choray"].includes(env.HOSPITAL)
                  ? "/quan-ly-noi-tru/dieu-duong/phieu-cham-soc-cap-1/"
                  : "/editor/bao-cao/"
              }${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}${
                payload?.id ? "/" + payload.id : ""
              }`,
              {
                nbDotDieuTriId: payload.nbDotDieuTriId,
                khoaChiDinhId: payload.khoaChiDinhId,
                thoiGianThucHien: payload.thoiGianThucHien,
                ..._newMhParams,
              }
            )
          );
          break;
        case "P088":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.id
              }`,
              {
                ..._newMhParams,
                soPhieu: payload.id,
                nbDotDieuTriId: payload.nbDotDieuTriId,
              }
            )
          );
          break;
        case "P091":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.id
              }`,
              _newMhParams
            )
          );

          break;
        case "P092":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload?.id || ""
              }`,
              {
                nbDotDieuTriId: payload.nbDotDieuTriId,
                dsNhomDichVuCap1Id: payload.dsNhomDichVuCap1Id,
                ...(!payload?.id
                  ? {
                      tuThoiGian: payload.tuThoiGian,
                      denThoiGian: payload.denThoiGian,
                      dsLoaiDichVu: payload.dsLoaiDichVu,
                      khoaChiDinhId: payload.khoaChiDinhId,
                      dsThoiGian: payload.dsThoiGian,
                    }
                  : {}),
                ..._newMhParams,
              }
            )
          );
          break;
        case "P093":
        case "P570":
        case "P572":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.id
              }`,
              _newMhParams
            )
          );
          //to dieu tri Id
          break;
        case "P097":
        case "P098":
        case "P099":
        case "P100":
        case "P101":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.nbDvKhamId
              }`,
              _newMhParams
            )
          );

          break;
        case "P096":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.nbDvKhamId
              }`,
              {
                nbDotDieuTriId: payload.nbDotDieuTriId,
                ..._newMhParams,
              }
            )
          );

          break;
        case "P102":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.id
              }`,
              _newMhParams
            )
          );

          break;
        case "P107":
          window.open(
            combineUrlParams(`/print-file/bang-ke/${payload.id}`, {
              ma: payload.ma,
              maBaoCao: payload.maBaoCao,
              loai: LOAI_IN_BANG_KE_CHI_PHI.NOI_TRU,
              notPrint: !!payload.notPrint,
              khongDongTab: payload.khongDongTab,
              ..._newMhParams,
            })
          );
          break;
        case "P108":
        case "P383":
        case "P664":
        case "P969":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/`,
              {
                nbDotDieuTriId: payload.nbDotDieuTriId,
                khoaChiDinhId: payload.khoaChiDinhId,
                ..._newMhParams,
              }
            )
          );
          break;
        case "P109":
        case "P279":
        case "P277":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.id
              }`,
              {
                nbDotDieuTriId: payload?.nbDotDieuTriId,
                ..._newMhParams,
              }
            )
          );
          break;
        case "P111":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.id
              }`,
              {
                nbDotDieuTriId: payload?.nbDotDieuTriId,
                ..._newMhParams,
              }
            )
          );
          break;
        case "P115":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.id
              }`,
              _newMhParams
            )
          );

          break;
        case "P135":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.id
              }`,
              {
                nbDotDieuTriId: payload.nbDotDieuTriId,
                chiDinhTuDichVuId: payload.chiDinhTuDichVuId,
                chiDinhTuLoaiDichVu: payload.chiDinhTuLoaiDichVu,
                ..._newMhParams,
              }
            )
          );
          break;
        case "P139":
        case "P322":
        case "P409":
        case "P410":
        case "P694":
        case "P699":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.id
              }`,
              {
                nbDotDieuTriId: payload.nbDotDieuTriId,
                ..._newMhParams,
              }
            )
          );

          break;
        case "P144":
        case "P372":
        case "P680":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.nbDvXetNgiemId
              }`,
              _newMhParams
            )
          );

          break;
        case "P148":
        case "P378":
        case "P382":
        case "P381":
        case "P385":
        case "P380":
        case "P384":
        case "P427":
        case "P419":
        case "P428":
        case "P420":
        case "P425":
        case "P418":
        case "P426":
        case "P424":
        case "P509":
        case "P510":
        case "P511":
        case "P512":
        case "P772":
        case "P901":
        case "P902":
        case "P894":
        case "P896":
        case "P909":
        case "P910":
        case "P948":
        case "P959":
        case "P1011":
        case "P1084":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}`,
              {
                nbDotDieuTriId: payload.nbDotDieuTriId,
                khoaChiDinhId: payload.khoaChiDinhId,
                ..._newMhParams,
              }
            )
          );
          break;
        case "P885":
        case "P886":
        case "P996":
        case "P995":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}`,
              {
                nbDotDieuTriId: payload.nbDotDieuTriId,
                ..._newMhParams,
              }
            )
          );
          break;
        case "P750":
        case "P753":
        case "P757":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}`,
              {
                nbDotDieuTriId: payload.nbDotDieuTriId,
                khoaChiDinhId: payload.khoaLamViecId,
                ..._newMhParams,
              }
            )
          );
          break;
        case "P718":
        case "P998":
        case "P1187":
        case "P1188":
        case "P1190":
        case "P1189":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}`,
              {
                nbDotDieuTriId: payload.nbDotDieuTriId,
                khoaChiDinhId: payload.khoaChiDinhId,
                thoiGianThucHien: payload.thoiGianThucHien,
                ..._newMhParams,
              }
            )
          );
          break;
        case "P149":
          let url = combineUrlParams(
            `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
              payload?.id || ""
            }`,
            {
              nbDotDieuTriId: payload.nbDotDieuTriId,
              khoaChiDinhId: payload.khoaChiDinhId,
              tuThoiGian: payload?.tuThoiGian,
              denThoiGian: payload?.denThoiGian,
              ..._newMhParams,
            }
          );
          url = url + `&dsPhieuLinhId=${payload.dsPhieuLinhId}`;
          window.open(url);
          break;
        case "P253":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}`,
              {
                nbChuyenKhoaId: payload.nbChuyenKhoaId,
                nbDotDieuTriId: payload.nbDotDieuTriId,
                khoaChiDinhId: payload.khoaChiDinhId,
                ..._newMhParams,
              }
            )
          );
          break;
        case "P158":
        case "P519":
        case "P518":
        case "P550":
        case "P551":
        case "P137":
        case "P138":
        case "P577":
        case "P576":
        case "P579":
        case "P578":
        case "P586":
        case "P583":
        case "P594":
        case "P595":
        case "P611":
        case "P700":
        case "P702":
        case "P704":
        case "P758":
        case "P759":
        case "P760":
        case "P762":
        case "P763":
        case "P764":
        case "P765":
        case "P770":
        case "P771":
        case "P779":
        case "P780":
        case "P1062":
        case "P1061":
        case "P1063":
        case "P1060":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.id
              }`,
              { ..._newMhParams, nbDotDieuTriId: payload.nbDotDieuTriId }
            )
          );
          break;
        case "P161":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.id
              }`,
              {
                ..._newMhParams,
                nbDotDieuTriId: payload.nbDotDieuTriId,
              }
            )
          );
          break;
        case "P276":
        case "P237":
        case "P280":
        case "P164":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.id
              }`,
              {
                nbDotDieuTriId: payload.nbDotDieuTriId,
                ..._newMhParams,
              }
            )
          );
          break;
        case "P274":
        case "P275":
          mhParams.nbDotDieuTriId = payload.nbDotDieuTriId;
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.id
              }`,
              _newMhParams
            )
          );
          break;
        case "P167":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.id
              }`,
              mhParams
            )
          );

          break;
        case "P168":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.id
              }`,
              _newMhParams
            )
          );

          break;
        case "P241":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.id
              }`,
              _newMhParams
            )
          );
          break;
        case "P181":
        case "P252":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}`,
              {
                nbDotDieuTriId: payload.nbDotDieuTriId,
                khoaChiDinhId: payload.khoaChiDinhId,
                ngayThucHien: payload.thoiGianYLenh,
                thoiGianThucHien: payload.thoiGianThucHien,
                ..._newMhParams,
              }
            )
          );
          break;
        case "P182":
        case "P539":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/`,
              {
                nbDotDieuTriId: payload.nbDotDieuTriId,
                khoaChiDinhId: payload.khoaChiDinhId,
                ngayYLenh: payload.thoiGianYLenh,
                thoiGianThucHien: payload.thoiGianThucHien,
                ..._newMhParams,
              }
            )
          );
          break;
        case "P183":
        case "P249":
        case "P531":
        case "P532":
        case "P533":
        case "P534":
        case "P535":
        case "P536":

        case "P666":
        case "P669":
        case "P690":
        case "P691":
        case "P692":
        case "P693":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload?.id || ""
              }`,
              {
                nbDotDieuTriId: payload?.nbDotDieuTriId,
                khoaChiDinhId: payload?.khoaChiDinhId,
                tuThoiGian: payload?.tuThoiGian,
                denThoiGian: payload?.denThoiGian,
                baoCaoId: phieu.baoCaoId,
                dsLoaiChiDinh: payload.dsLoaiChiDinh,
                ..._newMhParams,
              }
            )
          );
          break;
        case "P637":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload?.id || ""
              }`,
              {
                nbDotDieuTriId: payload?.nbDotDieuTriId,
                khoaId: payload?.khoaId,
                tuNgay: payload?.tuNgay,
                denNgay: payload?.denNgay,
                baoCaoId: phieu.baoCaoId,
                dsLoaiChiDinh: payload.dsLoaiChiDinh,
                ..._newMhParams,
              }
            )
          );
          break;
        case "P164":
        case "P165":
        case "P186":
        case "P270":
        case "P271":
        case "P272":
        case "P413":
        case "P452":
        case "P458":
        case "P453":
        case "P457":
        case "P451":
        case "P459":
        case "P482":
        case "P621":
        case "P744":
        case "P745":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.id
              }`,
              {
                nbDotDieuTriId: payload?.nbDotDieuTriId,
                ..._newMhParams,
              }
            )
          );
          break;
        case "P188":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.id
              }`,
              _newMhParams
            )
          );
          break;
        case "P191":
        case "P248":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}`,
              {
                nbDotDieuTriId: payload?.nbDotDieuTriId,
                khoaChiDinhId: payload?.khoaChiDinhId,
                ngayYLenh: payload?.thoiGianYLenh,
                ..._newMhParams,
              }
            )
          );
          break;
        case "P196":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.nbDvKhamId
              }`,
              _newMhParams
            )
          );
          break;
        case "P197":
        case "P250":
        case "P290":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload?.id || ""
              }`,
              {
                nbDotDieuTriId: payload?.nbDotDieuTriId,
                khoaChiDinhId: payload?.khoaChiDinhId,
                tuThoiGian: payload?.tuThoiGian,
                denThoiGian: payload?.denThoiGian,
                ..._newMhParams,
              }
            )
          );
          break;
        case "P087":
        case "P228":
        case "P487":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}`,
              {
                ..._newMhParams,
                baoCaoId: phieu.baoCaoId,
                nbDotDieuTriId: payload?.nbDotDieuTriId,
                chiDinhTuDichVuId: payload.chiDinhTuDichVuId,
                chiDinhTuLoaiDichVu: payload.chiDinhTuLoaiDichVu,
              }
            )
          );
          break;
        case "P258":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.id
              }`,
              _newMhParams
            )
          );
          break;
        case "P141":
        case "P389":
        case "P442":
        case "P563":
        case "P564":
          let params = {
            nbDotDieuTriId: payload?.nbDotDieuTriId,
            tuThoiGian: payload?.tuThoiGian,
            denThoiGian: payload?.denThoiGian,
            loai: payload?.loai,
            ..._newMhParams,
          };
          if (phieu.ma === "P389") {
            params.khoaChiDinhId = payload.khoaChiDinhId;
          }

          if (phieu.ma === "P563") {
            params.loai = 10;
          }
          if (phieu.ma === "P564") {
            params.loai = 20;
          }

          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}`,
              params
            )
          );
          break;
        case "P143":
        case "P833":
        case "P846":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload?.id || ""
              }`,
              {
                nbDotDieuTriId: payload.nbDotDieuTriId,
                khoaChiDinhId: payload.khoaChiDinhId,
                thoiGianThucHien: payload.thoiGianThucHien,
                ..._newMhParams,
              }
            )
          );
          break;
        case "P819":
          const dataPHIEU_KHAM_TIEN_ME_KHONG_PTTT =
            await dispatch.thietLap.getThietLap({
              ma: THIET_LAP_CHUNG.PHIEU_KHAM_TIEN_ME_KHONG_PTTT,
            });

          if (dataPHIEU_KHAM_TIEN_ME_KHONG_PTTT?.eval()) {
            window.open(
              combineUrlParams(
                `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                  payload?.id || ""
                }`,
                {
                  nbDotDieuTriId: payload.nbDotDieuTriId,
                  khoaChiDinhId: payload.khoaChiDinhId,
                  thoiGianThucHien: payload.thoiGianThucHien,
                  ..._newMhParams,
                }
              )
            );
          } else {
            window.open(
              combineUrlParams(
                `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/`,
                {
                  nbDotDieuTriId: payload.nbDotDieuTriId,
                  khoaChiDinhId: payload.khoaChiDinhId,
                  ..._newMhParams,
                }
              )
            );
            break;
          }
          break;
        case "P508":
        case "P507":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/`,
              {
                nbDotDieuTriId: payload.nbDotDieuTriId,
                khoaChiDinhId: payload.khoaChiDinhId,
                tuThoiGian: payload.tuThoiGian,
                denThoiGian: payload.denThoiGian,
                chiDinhTuLoaiDichVu: payload.chiDinhTuLoaiDichVu,
                ..._newMhParams,
              }
            )
          );
          break;
        case "P541":
          window.open(
            combineUrlParams(`/print-file/bang-ke/${payload.nbDotDieuTriId}`, {
              ma: payload.ma,
              maBaoCao: payload.maBaoCao,
              loai: LOAI_IN_BANG_KE_CHI_PHI.MIEN_PHI,
              notPrint: !!payload.notPrint,
              khongDongTab: payload.khongDongTab,
              ..._newMhParams,
            })
          );
          break;
        case "P616":
        case "P617":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/`,
              {
                nbDotDieuTriId: payload.nbDotDieuTriId,
                khoaId: payload.khoaId,
                tuThoiGian: payload.tuThoiGian,
                denThoiGian: payload.denThoiGian,
                baoCaoId: phieu.baoCaoId,
                ..._newMhParams,
              }
            )
          );
          break;
        case "P607":
        case "P603":
        case "P953":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}${
                payload.id ? `/${payload.id}` : ""
              }`,
              {
                nbDotDieuTriId: payload.nbDotDieuTriId,
                khoaChuyenDiId: payload.khoaChuyenDiId,
                thoiGianBanGiao: payload.thoiGianBanGiao,
                khoaChuyenDenId: payload.khoaChuyenDenId,
                baoCaoId: phieu.baoCaoId,
                ..._newMhParams,
              }
            )
          );
          break;
        case "P620":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload?.id || ""
              }`,
              {
                nbDotDieuTriId: payload.nbDotDieuTriId,
                khoaId: payload.khoaId,
                ngayTheoDoi: payload.ngayTheoDoi,
                loaiTheoDoi: payload.loaiTheoDoi,
                ..._newMhParams,
              }
            )
          );
          break;
        case "P615":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload.nbDvKhamId || phieu?.dsSoPhieu[0]?.soPhieu
              }`,
              {
                nbDotDieuTriId: payload.nbDotDieuTriId,
                ..._newMhParams,
              }
            )
          );
          break;
        case "P600":
        case "P608":
        case "P884":
        case "P940":
        case "P979":
        case "P981":
        case "P978":
        case "P987":
        case "P973":
        case "P933":
        case "P957":
        case "P983":
        case "P954":
          debugger;
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}`,
              {
                nbDotDieuTriId: payload.nbDotDieuTriId,
                baoCaoId: phieu.baoCaoId,
                tuanThai: payload.tuanThai,
                ...(["P940"].includes(phieu.ma)
                  ? { conThu: payload.conThu }
                  : {}),
                ..._newMhParams,
              }
            )
          );
          break;
        case "P943":
        case "P944":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}`,
              {
                nbDotDieuTriId: payload.nbDotDieuTriId,
                baoCaoId: phieu.baoCaoId,
                nbDvXetNghiemId: payload.nbDvXetNgiemId,
                ..._newMhParams,
              }
            )
          );
          break;
        case "P619":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}/${
                payload?.id || ""
              }`,
              {
                nbDotDieuTriId: payload.nbDotDieuTriId,
                khoaChiDinhId: payload.khoaChiDinhId,
                thoiGianThucHien: payload.thoiGianThucHien,
                baoCaoId: phieu.baoCaoId,
                toDieuTriId: payload.toDieuTriId,
                ..._newMhParams,
              }
            )
          );
          break;
        case "P803":
          window.open(
            combineUrlParams(`/print-file/bang-ke/${payload.id}`, {
              ma: payload.ma,
              maBaoCao: payload.maBaoCao,
              loai: LOAI_IN_BANG_KE_CHI_PHI.THU_NGOAI,
              notPrint: !!payload.notPrint,
              khongDongTab: payload.khongDongTab,
              ..._newMhParams,
            })
          );
          break;
        case "P930":
        case "P929":
          const { id, ...res } = _newMhParams;
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}`,
              {
                nbDvKhamId: payload.id,
                nbDotDieuTriId: payload.nbDotDieuTriId,
                ...res,
              }
            )
          );
          break;
        case "P992":
        case "P993":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}`,
              {
                ..._newMhParams,
                nbDotDieuTriId: payload?.nbDotDieuTriId,
                khoaChiDinhId: payload?.khoaChiDinhId,
                denKhoaId: payload?.denKhoaId,
                thoiGianThucHien: payload?.thoiGianThucHien,
              }
            )
          );
          break;
        case "P994":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}`,
              {
                nbDvKhamId: payload.nbDvKhamId,
                nbDotDieuTriId: payload.nbDotDieuTriId,
                ..._newMhParams,
              }
            )
          );
          break;
        case "P1183":
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${MA_BIEU_MAU_EDITOR[phieu.ma].maBaoCao}`,
              {
                thoiGianThucHien: payload.thoiGianThucHien,
                khoaChiDinhId: payload.khoaChiDinhId,
                ..._newMhParams,
              }
            )
          );
          break;
        default:
          window.open(
            combineUrlParams(
              `/editor/bao-cao/${
                MA_BIEU_MAU_EDITOR[phieu.ma]?.maBaoCao || phieu?.maBaoCao
              }${payload.id ? `/${payload.id}` : ""}`,
              {
                ..._newMhParams,
                nbDotDieuTriId: payload?.nbDotDieuTriId,
                ...(payload?.nbDvKhamId
                  ? { nbDvKhamId: payload.nbDvKhamId }
                  : payload?.nbDvCdhaTdcnPtTtId
                  ? { nbDvCdhaTdcnPtTtId: payload.nbDvCdhaTdcnPtTtId }
                  : {}),
                khoaChiDinhId: payload?.khoaChiDinhId,
                denKhoaId: payload?.denKhoaId,
                thoiGianThucHien: payload?.thoiGianThucHien,
              }
            )
          );
          break;
      }
    },
    getFilePhieuIn: (
      { listPhieus, selectedIds, isPhieuTemp, ...payload },
      state
    ) => {
      return new Promise(async (resolve, reject) => {
        try {
          let dsPhieu = listPhieus;
          if (!dsPhieu) {
            const data = isPhieuTemp
              ? state.phieuIn.listPhieuTemp
              : state.phieuIn.listPhieu || [];
            dsPhieu = (selectedIds || [])
              .map((id) => {
                const phieu = data?.find((item) => item.key == id);
                return phieu;
              })
              .filter((item) => item);
          }
          if (dsPhieu?.length) {
            let _dsPhieu = [];

            dsPhieu.forEach((element) => {
              if (
                (payload.isInHsba && element.ma == "P059") ||
                element.ma == "P024"
              ) {
                //nếu ở MH HSBA thì in tất cả các phiếu phẫu thuật + thủ thuật
                (element.dsSoPhieu || []).forEach((element2) => {
                  _dsPhieu.push({
                    ...element,
                    key: stringUtils.guid(),
                    dsSoPhieu: [element2],
                  });
                });
              } else {
                _dsPhieu.push(element);
              }
            });
            dsPhieu = _dsPhieu;

            let promise = dsPhieu.map((phieu) => {
              //Với phiếu chỉ định đã in không thực hiện gọi lại, lấy luôn data cũ để thực hiện in
              if (payload.inPhieuChiDinh === false && phieu.ma == "P075") {
                return { ...phieu, code: 0 };
              }
              const params = payload?.paramsTheoMaPhieu?.[phieu.ma];

              return dispatch.phieuIn.getThongTinPhieu({
                phieu,
                ...payload,
                ...params,
              });
            });

            dsPhieu = (await Promise.all(promise)).filter(
              (item) => item && !item.code
            );
            promise = dsPhieu.map((phieu) => {
              return dispatch.phieuIn.getDataDanhSachPhieu({
                dsFile: flatten(phieu.filePdf),
                mode: 0,
              });
            });
            let values = (await Promise.all(promise)).filter((item) => item);
            if (!values?.length) {
              reject({ message: t("phieuIn.khongTonTaiPhieuIn") });
              return;
            }
            let file = null;
            file = await printProvider.getMergePdf(values);
            resolve({ finalFile: file, dsPhieu });
          } else {
            message.error(t("phieuIn.khongTonTaiPhieuIn"));
            reject({ message: t("phieuIn.khongTonTaiPhieuIn") });
          }
        } catch (error) {
          console.log(error);
          reject(error);
        }
      });
    },
    kyPhieu: (
      { isBangKe = false, isEditor, suDungApiKySoRieng, ...payload },
      state
    ) => {
      return new Promise(async (resolve, reject) => {
        try {
          let s = null;
          if (suDungApiKySoRieng) {
            let { loaiKy, api, idPhieu, ...res } = payload;
            s = await danhSachPhieuChoKyProvider.post(res);
          } else {
            if ((payload.loaiKy && !isBangKe) || (isEditor && !isBangKe)) {
              s = await danhSachPhieuChoKyProvider.kyDienTu(payload);
            } else {
              let { loaiKy, api, idPhieu, ...res } = payload;
              s = await danhSachPhieuChoKyProvider.post(res);
            }
          }

          if (!s.data.duLieu) {
            message.success(s?.message || t("kySo.kyPhieuThanhCong"));
            resolve(s);
          } else {
            const { theChuKy, fileTruocKy, chuKySo, viTri, id } = s.data.duLieu;
            const base64 = await isofhToolProvider.kyXml({
              xmlBase64String: fileTruocKy,
              theChuKy,
            });
            if (base64) {
              const file = fileUtils.base64ToFile(
                base64,
                stringUtils.guid() + ".xml",
                "application/xml"
              );
              await danhSachPhieuChoKyProvider.token({
                file,
                viTri,
                chuKySo,
                id,
              });
            }
            message.success(t("kySo.kyPhieuThanhCong"));
            resolve(base64);
          }
        } catch (error) {
          showError(error?.message);
          reject(error);
        }
      });
    },

    kyPhieuTheoAPI: (payload, state) => {
      return new Promise((resolve, reject) => {
        danhSachPhieuChoKyProvider
          .signCustom(payload)
          .then(async (s) => {
            if (!s.data.duLieu) {
              message.success(s?.message || t("kySo.kyPhieuThanhCong"));
              resolve(s);
            } else {
              const { theChuKy, fileTruocKy, chuKySo, viTri, id } =
                s.data.duLieu;
              const base64 = await isofhToolProvider.kyXml({
                xmlBase64String: fileTruocKy,
                theChuKy,
              });
              if (base64) {
                const file = fileUtils.base64ToFile(
                  base64,
                  stringUtils.guid() + ".xml",
                  "application/xml"
                );
                await danhSachPhieuChoKyProvider.token({
                  file,
                  viTri,
                  chuKySo,
                  id,
                });
              }
              message.success(t("kySo.kyPhieuThanhCong"));
              resolve(base64);
            }
          })
          .catch((e) => {
            showError(e?.message);
            reject(e);
          });
      });
    },

    kyPhieuTheoMa: ({ ma, ...rest }, state) => {
      return new Promise((resolve, reject) => {
        let api;
        let payload;

        switch (ma) {
          //Giấy đẩy cổng - Giấy chứng tử
          case "P209":
            api = nbTuVongProvider.ky;
            payload = {
              id: rest?.id,
              body: {
                id: rest?.idBody,
                chuKySo: rest?.chuKySo,
                ...(rest?.anhKy ? { anhKy: rest?.anhKy } : {}),
              },
            };
            break;

          //Giấy đẩy cổng - Giấy KSK lái xe
          case "P196":
          case "P254":
            api = nbKhamKSKLaiXeProvider.ky;
            payload = rest?.id;
            break;
          default:
            api = null;
            message.error("Phiếu chưa được cấu hình");
            break;
        }

        if (api) {
          api(payload)
            .then(async (s) => {
              if (!s.data.duLieu) {
                message.success(s?.message || t("kySo.kyPhieuThanhCong"));
                resolve(s);
              } else {
                const { theChuKy, fileTruocKy, chuKySo, viTri, id } =
                  s.data.duLieu;
                const base64 = await isofhToolProvider.kyXml({
                  xmlBase64String: fileTruocKy,
                  theChuKy,
                });
                if (base64) {
                  const file = fileUtils.base64ToFile(
                    base64,
                    stringUtils.guid() + ".xml",
                    "application/xml"
                  );
                  await danhSachPhieuChoKyProvider.token({
                    file,
                    viTri,
                    chuKySo,
                    id,
                  });
                }
                message.success(t("kySo.kyPhieuThanhCong"));
                resolve(base64);
              }
            })
            .catch((err) => {
              showError(err?.message);
              reject(err);
            });
        }
      });
    },

    huyKyPhieu: (payload, state) => {
      return new Promise((resolve, reject) => {
        danhSachPhieuChoKyProvider
          .huyKy(payload)
          .then((s) => {
            message.success(s?.message || "Hủy ký phiếu thành công!");
            resolve(s);
          })
          .catch((err) => {
            message.error(err?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(err);
          });
      });
    },

    trinhKy: (payload, state) => {
      return new Promise((resolve, reject) => {
        let { loaiKy, api, ...res } = payload;
        danhSachPhieuChoKyProvider
          .trinhKySo(res)
          .then((s) => {
            message.success(s?.message || t("editor.trinhKyPhieuThanhCong"));
            resolve(s);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },

    trinhKyPhieu: (payload, state) => {
      return new Promise((resolve, reject) => {
        danhSachPhieuChoKyProvider
          .trinhKySo(payload)
          .then((s) => {
            message.success(s?.message || t("editor.trinhKyPhieuThanhCong"));
            resolve(s);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },

    tuChoiKyPhieu: (payload, state) => {
      return new Promise((resolve, reject) => {
        danhSachPhieuChoKyProvider
          .tuChoiKy(payload)
          .then((s) => {
            message.success(s?.message || t("phieuIn.tuChoiKyPhieuThanhCong"));
            resolve(s);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },

    kyBieuMauScan: (payload, state) => {
      return new Promise((resolve, reject) => {
        danhSachPhieuChoKyProvider
          .post(payload)
          .then(async (s) => {
            if (!s.data.duLieu) {
              message.success(s?.message || t("kySo.kyPhieuThanhCong"));
              resolve(s);
            } else {
              const { theChuKy, fileTruocKy, chuKySo, viTri, id } =
                s.data.duLieu;
              const base64 = await isofhToolProvider.kyXml({
                xmlBase64String: fileTruocKy,
                theChuKy,
              });
              if (base64) {
                const file = fileUtils.base64ToFile(
                  base64,
                  stringUtils.guid() + ".xml",
                  "application/xml"
                );
                await danhSachPhieuChoKyProvider.token({
                  file,
                  viTri,
                  chuKySo,
                  id,
                });
              }
              message.success(t("kySo.kyPhieuThanhCong"));
              resolve(base64);
            }
          })
          .catch((e) => {
            showError(e?.message);
            reject(e);
          });
      });
    },
    getPhieuInTheoMa: ({ maPhieu, maManHinh, maViTri, ...payload }, state) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .getPhieuIn({ maManHinh, maViTri, ...payload })
          .then(async (s) => {
            if (s.code === 0) {
              let phieu = s?.data.find((x) => x.ma == maPhieu);
              if (!phieu) {
                message.error(t("phieuIn.khongTonTaiMaPhieu", { maPhieu }));
                reject(s);
              } else {
                resolve(phieu);
              }
            } else {
              reject(s);
              message.error(s.message);
            }
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    getPhieuInTheoDsMa: (
      { dsMaPhieu = [], maManHinh, maViTri, ...payload },
      state
    ) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .getPhieuIn({ maManHinh, maViTri, ...payload })
          .then(async (s) => {
            if (s.code === 0) {
              let dsPhieu = s?.data.filter((x) => dsMaPhieu.includes(x.ma));
              resolve(dsPhieu);
            } else {
              reject(s);
              message.error(s.message);
            }
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
  }),
};
