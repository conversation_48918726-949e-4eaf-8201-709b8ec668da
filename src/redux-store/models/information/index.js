import nbThongTinProvider from "data-access/nb-thong-tin-provider";
import { message } from "antd";
import { PAGE_SIZE, PAGE_DEFAULT } from "constants/index";
import { combineSort } from "utils";
import moment from "moment";
import apiBase from "../../../data-access/api-base";

export default {
  state: {
    listThongTinNguoiBenh: [],
    totalElements: null,
    page: PAGE_DEFAULT,
    size: PAGE_SIZE,
    dataEditDefault: {},
    dataSortColumn: {},
    dataSearch: {},
  },
  reducers: {
    updateData(state, payload = {}) {
      return { ...state, ...payload };
    },
  },
  effects: (dispatch) => ({
    ...apiBase.initReduxGetListAll({
      dispatch,
      api: nbThongTinProvider.search,
      KEY_CACHE: "DATA_ALL_DANH_SACH_NB",
      model: "information",
      fieldName: "DsNb",
    }),
    onCheckTrungThongTin: (payload = {}, state) => {
      return new Promise((resolve, reject) => {
        const { variables, value, khoaId } = payload;
        let {
          tenNb,
          gioiTinh,
          ngaySinh,
          soDienThoai,
          quocTichId,
          danTocId,
          nbDiaChi,
          nbGiayToTuyThan,
          nbNguoiBaoLanh,
          nbTheBaoHiem,
          quocGiaId,
          chiNamSinh,
        } = state.tiepDon;
        ngaySinh =
          ngaySinh?.date instanceof moment
            ? ngaySinh?.date.format("YYYY/MM/DD HH:mm:ss")
            : ngaySinh?.date instanceof Date
            ? ngaySinh?.date.format("yyyy/MM/dd HH:mm:ss")
            : ngaySinh?.date;
        let sdtNguoiBaoLanh = nbNguoiBaoLanh?.soDienThoai;
        let maSoGiayToTuyThan = nbGiayToTuyThan?.maSo;
        let maTheBhyt = nbTheBaoHiem?.maThe;
        // let tuNgay = nbTheBaoHiem?.tuNgay;
        // let denNgay = nbTheBaoHiem?.denNgay;
        let { tinhThanhPhoId, quanHuyenId, xaPhuongId, soNha } = nbDiaChi || {};
        let newValue = {};
        if (variables === "diaChi") {
          newValue = {
            xaPhuongId: value?.xaPhuongId,
            quanHuyenId: value?.quanHuyenId,
            tinhThanhPhoId: value?.tinhThanhPhoId,
          };
        } else if (variables == "tenNb") {
          tenNb = value;
          newValue = {
            [variables]: value,
          };
        } else if (variables == "ngaySinh") {
          newValue = {
            [variables]: ngaySinh,
          };
        } else if (variables == "maThe") {
          maTheBhyt = value;
          newValue = {
            maTheBhyt: value,
          };
        } else if (variables == "maSo") {
          maSoGiayToTuyThan = value;
          newValue = {
            maSoGiayToTuyThan: value,
          };
        } else {
          newValue = {
            [variables]: value,
          };
        }
        const giamDinhBaoHiem = (s) => {
          if (
            [
              "tenNb",
              "ngaySinh",
              "gioiTinh",
              "maThe",
              "tuNgay",
              "denNgay",
            ].includes(variables) &&
            tenNb &&
            maTheBhyt &&
            ngaySinh
          ) {
            resolve({
              type: 2,
              data: {
                hoTen: tenNb,
                maThe: maTheBhyt,
                ngaySinh,
              },
            });
          } else {
            resolve({
              type: 0,
              data: s,
            });
          }
        };
        const removeSpaces = (obj) => {
          const newObj = {};
          for (const key in obj) {
            const value = obj[key];
            if (typeof value === "string") {
              newObj[key] = value.trim().replace(/\s+$/g, "");
            } else {
              newObj[key] = value;
            }
          }
          return newObj;
        };
        if (
          (variables === "tenNb" && value && ngaySinh) ||
          (variables === "ngaySinh" && value && tenNb) ||
          (variables === "gioiTinh" && value && ngaySinh && tenNb) ||
          (variables === "soDienThoai" && value) ||
          (variables === "sdtNguoiBaoLanh" && value) ||
          (variables === "maSo" && value) ||
          (variables === "maThe" && value) ||
          (variables === "diaChi" && value && tenNb)
        ) {
          //check trung thông tin bệnh nhân
          let payloadCheckTrung = {};
          if (maTheBhyt) {
            payloadCheckTrung = { maTheBhyt: maTheBhyt, khoaId };
          } else {
            payloadCheckTrung = {
              tenNb: tenNb?.toUpperCase(),
              soDienThoai: soDienThoai?.replaceAll(" ", ""),
              gioiTinh,
              sdtNguoiBaoLanh,
              maSoGiayToTuyThan,
              maTheBhyt,
              quocGiaId,
              tinhThanhPhoId,
              quanHuyenId,
              xaPhuongId,
              soNha,
              chiNamSinh,
              ...newValue,
              ngaySinh,
              khoaId,
            };
          }
          payloadCheckTrung = removeSpaces(payloadCheckTrung);
          dispatch.information
            .checkTrungThongTin(payloadCheckTrung)
            .then((s) => {
              if (s?.data.length) {
                resolve({
                  type: 1,
                  data: s.data,
                });
              } else {
                giamDinhBaoHiem(s);
                //không tìm thấy bệnh nhân
              }
            })
            .catch((e) => {
              giamDinhBaoHiem(e);
              //Lỗi tìm kiếm bệnh nhân
            });
        } else {
          giamDinhBaoHiem(newValue);
          //Khôgn đủ điều kiện tìm kiếm bệnh nhân
        }
        return;
        // if (s?.code === 0 && s?.data?.length) showTrungThongTin(s?.data);
        // else if (tenNb?.length && maTheBhyt?.length && ngaySinh) {
        //   onCheckCardInsurance(
        //     {
        //       hoTen: tenNb,
        //       maThe: maTheBhyt,
        //       ngaySinh,
        //     },
        //     { tenNb }
        //   );
        // }
      });
    },
    checkTrungThongTin: ({ isOnlyResolve, ...payload }, state) => {
      return new Promise((resolve, reject) => {
        nbThongTinProvider
          .searchTrungThongTin(payload)
          .then((s) => {
            resolve(s);
          })
          .catch((e) => {
            if (isOnlyResolve) {
              resolve({});
            } else {
              reject(e);
            }
          });
      });
    },
    onSizeChange: ({ dataSearch, ...rest }, state) => {
      dispatch.information.updateData({
        page: 0,
        dataSearch,
        ...rest,
      });
      dispatch.information.onSearch({ ...rest });
    },
    onChangeInputSearch: ({ page = 0, ...payload }, state) => {
      const obj = {
        dataSearch: {
          ...(state.information.dataSearch || {}),
          ...payload,
        },
      };
      dispatch.information.updateData({
        page: 0,
        ...obj,
      });
      dispatch.information.onSearch({ ...obj });
    },
    onSortChange: ({ ...payload }, state) => {
      const dataSortColumn = {
        ...state.information.dataSortColumn,
        ...payload,
      };
      dispatch.information.updateData({
        page: 0,
        dataSortColumn,
      });
      dispatch.information.onSearch({
        page: 0,
        dataSortColumn,
      });
    },
    onSearch: ({ page = 0, ...payload }, state) => {
      return new Promise((resolve, reject) => {
        let newState = { isLoading: true, page };
        dispatch.information.updateData(newState);
        let size = payload.size || state.information.size || 10;
        const sort = combineSort(
          payload.dataSortColumn || state.information.dataSortColumn || {}
        );
        const dataSearch =
          payload.dataSearch || state.information.dataSearch || {};

        nbThongTinProvider
          .search({ page, size, sort, ...dataSearch })
          .then((s) => {
            dispatch.information.updateData({
              listThongTinNguoiBenh: (s?.data || []).map((item, index) => {
                item.index = page * size + index + 1;
                return item;
              }),
              isLoading: false,
              totalElements: s?.totalElements || 0,
              page,
              size,
            });

            resolve(s?.data || []);
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || "Xảy ra lỗi vui lòng thử lại");
            dispatch.information.updateData({
              listThongTinNguoiBenh: [],
              isLoading: false,
            });
          });
      });
    },
  }),
};
