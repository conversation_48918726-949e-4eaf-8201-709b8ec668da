import { cloneDeep } from "lodash";
import nbGiayNghiDuongThaiProvider from "data-access/nb-giay-nghi-duong-thai-provider";
import { message } from "antd";
import { combineSort } from "utils";
import { t } from "i18next";

const initData = {
  listData: [],
  totalElements: 0,
  page: 0,
  dataSearch: {},
  dataSortColumn: {},
};

export default {
  state: cloneDeep(initData),
  reducers: {
    updateData(state, payload = {}) {
      return { ...state, ...payload };
    },
    clearData(state, payload = {}) {
      return { ...cloneDeep(initData), ...payload };
    },
  },
  effects: (dispatch) => ({
    onSizeChange: ({ dataSearch, ...rest }) => {
      dispatch.nbGiayNghiDuongThai.updateData({
        page: 0,
        ...rest,
      });
      dispatch.nbGiayNghiDuongThai.onSearch({ ...rest });
    },

    searchNbGiayNghiDuongThaiByParams: ({ page = 0, ...payload }, state) => {
      const obj = {
        dataSearch: {
          ...state.nbGiayNghiDuongThai.dataSearch,
          ...payload,
        },
      };

      dispatch.nbGiayNghiDuongThai.updateData({
        page: 0,
        ...obj,
      });
      dispatch.nbGiayNghiDuongThai.onSearch({ ...obj });
    },

    onSortChange: ({ ...payload }, state) => {
      const dataSortColumn = {
        ...state.nbGiayNghiDuongThai.dataSortColumn,
        ...payload,
      };
      dispatch.nbGiayNghiDuongThai.updateData({
        page: 0,
        dataSortColumn,
      });
      dispatch.nbGiayNghiDuongThai.onSearch({
        page: 0,
        dataSortColumn,
      });
    },

    onSearch: ({ page = 0, ...payload }, state) => {
      let newState = { isLoading: true, page };
      dispatch.nbGiayNghiDuongThai.updateData(newState);
      let size = payload.size || state.nbGiayNghiDuongThai.size || 10;
      const sort = combineSort(
        payload.dataSortColumn || state.nbGiayNghiDuongThai.dataSortColumn || {}
      );
      const dataSearch =
        payload.dataSearch || state.nbGiayNghiDuongThai.dataSearch || {};

      nbGiayNghiDuongThaiProvider
        .searchAll({ page, size, sort, ...dataSearch })
        .then((s) => {
          dispatch.nbGiayNghiDuongThai.updateData({
            listData: (s?.data || []).map((item, index) => {
              item.index = page * size + index + 1;
              return item;
            }),
            isLoading: false,
            totalElements: s?.totalElements || 0,
            page,
          });
        })
        .catch((e) => {
          message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          dispatch.nbGiayNghiDuongThai.updateData({
            listData: [],
            isLoading: false,
          });
        });
    },
    dayGiayNghiDuongThai: (payload, state) => {
      return new Promise((resolve, reject) => {
        nbGiayNghiDuongThaiProvider
          .dayGiayNghiDuongThaiHangLoat(payload)
          .then((s) => {
            message.success(
              t("giayDayCong.message.dayGiayNghiDuongThaiThanhCong")
            );
            resolve(s.data);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
  }),
};
