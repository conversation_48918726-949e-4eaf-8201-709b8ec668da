import nbDvKTProvider from "data-access/nb-dv-ky-thuat-provider";
import nbDvXNProvider from "data-access/nb-dv-xet-nghiem-provider";
import nbDvKhamProvider from "data-access/nb-dv-kham-provider";
import nbDvNgoaiDieuTriProvider from "data-access/nb-dv-ngoai-dieu-tri-provider";
import nbBoChiDinhProvider from "data-access/nb-bo-chi-dinh-provider";
import dmBoChiDinhProvider from "data-access/categories/dm-bo-chi-dinh-provider";
import nbDvCLSProvider from "data-access/nb-dv-cdha-tdcn-pt-tt-provider";
import nbGoiDvProvider from "data-access/nb-goi-dv-provider";
import nbGoiDvChiTietProvider from "data-access/nb-goi-dv-chi-tiet-provider";
import nbDvThuocProvider from "data-access/nb-dv-thuoc-provider";
import { message } from "antd";
// import cacheUtils from "lib-utils/cache-utils";
import printProvider from "data-access/print-provider";
import dichVuKyThuatProvider from "data-access/categories/dm-dv-ky-thuat-provider";
import { t } from "i18next";
import { LOAI_DICH_VU, LOAI_IN } from "constants/index.js";
import { flatten, groupBy } from "lodash";
import { isArray, locPhieuLisPacs, openInNewTab } from "utils";
import cloneDeep from "lodash/cloneDeep";
import nbDotDieuTriProvider from "data-access/nb-dot-dieu-tri-provider";
import nbDichVuProvider from "data-access/nb-dich-vu-provider";

export default {
  state: {
    configData: null,
    listDvKham: [],
    loaiDichVu: null,
    listLoaiDichVu: [],
    dsDichVuChiDinhXN: [],
    dsDichVuChiDinhKham: [],
    dsDichVuNgoaiDieuTri: [],
    dsDichVuChiDinhCls: [],
    dataPhieu: {},
    neededUpdateRecord: [],
    listGoiDv: [],
    collapseDichVuTemp: [],
    detailDvKyThuat: {},
    listDvTiepDon: [],
  },
  reducers: {
    updateData(state, payload = {}) {
      return { ...state, ...payload };
    },
    updateConfigData(state, payload = {}) {
      if (
        payload?.configData?.nbDotDieuTriId != state.configData?.nbDotDieuTriId
      ) {
        return {
          ...state,
          ...payload,
          dsDichVuChiDinhXN: [],
          dsDichVuChiDinhKham: [],
          dsDichVuNgoaiDieuTri: [],
          dsDichVuChiDinhCls: [],
        };
      }

      return { ...state, ...payload };
    },
  },

  effects: (dispatch) => ({
    searchDvKSKTachPhong: async ({ bacSiChiDinhId, ...payload }, state) => {
      return new Promise((resolve, reject) => {
        dichVuKyThuatProvider
          .searchDMDichVuTachPhong({ ...payload, dsDoiTuongSuDung: 40 })
          .then((s) => {
            if (s?.code === 0) {
              let data = s?.data || [];
              dispatch.chiDinhKhamBenh.updateData({
                listDvKham: data,
              });
              resolve(s);
            } else {
              reject(s);
              message.error(s?.message);
            }
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    onSearchDichVu: async (
      { bacSiChiDinhId, dsDoiTuongSuDung, ...payload },
      state
    ) => {
      return new Promise((resolve, reject) => {
        dichVuKyThuatProvider
          .searchAll({ ...payload, dsDoiTuongSuDung })
          .then((s) => {
            let data = s?.data || [];
            dispatch.chiDinhKhamBenh.updateData({
              listDvKham: data,
              page: s.pageNumber || 0,
              size: s.pageSize || data?.length || 0,
              totalElements: s.totalElements || data?.length || 0,
            });
            resolve(s);
          })
          .catch((e) => {
            dispatch.chiDinhKhamBenh.updateData({
              listDvKham: [],
              page: 0,
              size: 0,
              totalElements: 0,
            });
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    onSearchChiTietDichVu: async ({ dichVuId, ...payload }, state) => {
      return new Promise((resolve, reject) => {
        dichVuKyThuatProvider
          .searchAll({ ...payload, dichVuId })
          .then((s) => {
            let data = s?.data || [];
            dispatch.chiDinhKhamBenh.updateData({
              detailDvKyThuat: data,
            });
            resolve(s);
          })
          .catch((e) => {
            dispatch.chiDinhKhamBenh.updateData({
              detailDvKyThuat: {},
            });
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    onSearchGoiDichVu: async (
      { dsDoiTuongSuDung = 20, dsLoaiDichVu = [10, 20, 30, 40], ...payload },
      state
    ) => {
      try {
        const s = await dmBoChiDinhProvider.searchTongHop({
          ...payload,
          dsDoiTuongSuDung,
          dsLoaiDichVu,
        });
        let data = (s?.data || []).map((item) => ({
          ...item,
          dichVuId: item.id,
        }));

        dispatch.chiDinhKhamBenh.updateData({
          listGoiDv: data,
          pageGoiDv: s.pageNumber,
          sizeGoiDv: s.pageSize,
          totalElementsGoiDv: s.totalElements,
        });
      } catch (error) {
        dispatch.chiDinhKhamBenh.updateData({
          listGoiDv: [],
          pageGoiDv: 0,
          sizeGoiDv: 0,
          totalElementsGoiDv: 0,
        });
        message.error(error?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
      }
    },
    tamTinhTien: (
      {
        khoaChiDinhId,
        chiDinhTuDichVuId,
        showChiDinhTuDichVuId = false,
        nbDotDieuTriId,
        chiDinhTuLoaiDichVu = 10,
        dsDichVu = [],
        ...payload
      },
      state
    ) => {
      let { listLoaiDichVu } = state.chiDinhKhamBenh;
      let listUpdatedLoaiDichVu = dsDichVu.map(
        (item) => item.nbDichVu?.loaiDichVu
      );
      listUpdatedLoaiDichVu = [
        ...new Set([...listLoaiDichVu, ...listUpdatedLoaiDichVu]),
      ];

      if (!listUpdatedLoaiDichVu.length) return;
      dispatch.chiDinhKhamBenh.updateData({
        listLoaiDichVu: listUpdatedLoaiDichVu,
      });

      const tamTinhTienDVKham = new Promise((resolve, reject) => {
        const body = dsDichVu.filter(
          (item) => item.nbDichVu.loaiDichVu === LOAI_DICH_VU.KHAM
        );
        if (body.length)
          return nbDvKhamProvider
            .tamTinhTienDVKham(body)
            .then((s) => {
              if (s?.code === 0) {
                resolve(s);
              } else {
                reject(s);
                message.error(s?.message);
              }
            })
            .catch((e) => {
              reject(e);
              message.error(e?.message);
            });
        else resolve(0);
      });

      const tamTinhTienDVXN = new Promise((resolve, reject) => {
        const body = dsDichVu.filter(
          (item) => item.nbDichVu.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM
        );
        if (body.length)
          return nbDvXNProvider
            .tamTinhTienDVXN(body)
            .then((s) => {
              if (s?.code === 0) {
                resolve(s);
              } else {
                reject(s);
                message.error(s?.message);
              }
            })
            .catch((e) => {
              reject(e);
              message.error(e?.message);
            });
        else resolve(0);
      });

      const tamTinhTienDVCLS = new Promise((resolve, reject) => {
        const body = dsDichVu.filter(
          (item) =>
            item.nbDichVu.loaiDichVu === LOAI_DICH_VU.CDHA ||
            item.nbDichVu.loaiDichVu === LOAI_DICH_VU.PHAU_THUAT_THU_THUAT
        );
        if (body.length)
          return nbDvCLSProvider
            .tamTinhTienDVCLS(body)
            .then((s) => {
              if (s?.code === 0) {
                resolve(s);
              } else {
                reject(s);
                message.error(s?.message);
              }
            })
            .catch((e) => {
              reject(e);
              message.error(e?.message);
            });
        else resolve(0);
      });

      const tamTinhTienDVNgoaiDieuTri = new Promise((resolve, reject) => {
        const body = dsDichVu.filter(
          (item) => item.nbDichVu.loaiDichVu === LOAI_DICH_VU.NGOAI_DIEU_TRI
        );
        if (body.length)
          return nbDvNgoaiDieuTriProvider
            .tamTinhTienDVNgoaiDieuTri(body)
            .then((s) => {
              if (s?.code === 0) {
                resolve(s);
              } else {
                reject(s);
                message.error(s?.message);
              }
            })
            .catch((e) => {
              reject(e);
              message.error(e?.message);
            });
        else resolve(0);
      });

      return Promise.all([
        tamTinhTienDVKham,
        tamTinhTienDVXN,
        tamTinhTienDVCLS,
        tamTinhTienDVNgoaiDieuTri,
      ])
        .then((response) => {
          let dataTamTinhTien = [];
          response.forEach((res) => {
            if (res === 0) return;
            const tinhTien = res.data.map((item) => {
              const _findDv = dsDichVu.find(
                (x) => x.nbDichVu?.dichVuId === item.nbDichVu?.dichVuId
              );
              const _loaiDichVu =
                item.nbDichVu?.loaiDichVu || _findDv.nbDichVu?.loaiDichVu;

              return {
                code: item.code,
                message: item.message,
                nbDotDieuTriId: nbDotDieuTriId,
                nbDichVu: {
                  dichVu: item.nbDichVu?.dichVu,
                  dichVuId: item.nbDichVu?.dichVuId,
                  soLuong: item.nbDichVu?.soLuong,
                  chiDinhTuDichVuId: showChiDinhTuDichVuId
                    ? item.nbDichVu?.chiDinhTuDichVuId
                    : chiDinhTuDichVuId,
                  chiDinhTuLoaiDichVu: chiDinhTuLoaiDichVu,
                  khoaChiDinhId: item.nbDichVu?.khoaChiDinhId || khoaChiDinhId,
                  loaiDichVu: _loaiDichVu,
                  thanhTien: item.nbDichVu?.thanhTien,
                  nbGoiDvId: item.nbDichVu?.nbGoiDvId || undefined,
                  nbGoiDvChiTietId:
                    item.nbDichVu?.nbGoiDvChiTietId || undefined,
                  loaiHinhThanhToanId: item?.nbDichVu?.loaiHinhThanhToanId,
                  tyLeTtDv: _findDv?.nbDichVu?.tyLeTtDv,
                  tuTra: item.nbDichVu?.tuTra,
                  khongTinhTien: item.nbDichVu?.khongTinhTien,
                  giaBaoHiem: item.nbDichVu.giaBaoHiem,
                  giaKhongBaoHiem: item.nbDichVu.giaKhongBaoHiem,
                  ghiChu: item?.nbDichVu?.ghiChu,
                  nguonKhacId: item?.nbDichVu?.nguonKhacId,
                  thoiGianThucHien: item?.nbDichVu?.thoiGianThucHien,
                },
                nbDvKyThuat: {
                  phongThucHienId: item.nbDvKyThuat?.phongThucHienId,
                  tuVanVienId: item.nbDvKyThuat?.tuVanVienId,
                  capCuu: _findDv.nbDvKyThuat.capCuu,
                },
                nbChanDoan: {
                  cdSoBo: "",
                },
                boChiDinhId: _findDv ? _findDv.boChiDinhId : undefined,
                dsPhongThucHien: _findDv?.dsPhongThucHien,
                dsLoaiHinhThanhToan: _findDv?.dsLoaiHinhThanhToan,
                benhPhamId: _findDv?.benhPhamId,
                ...(_loaiDichVu === LOAI_DICH_VU.XET_NGHIEM
                  ? { phongLayMauId: item.phongLayMauId }
                  : {}),
                ...(item.nbDichVu.loaiDichVu === LOAI_DICH_VU.KHAM
                  ? { bacSiKhamId: item.bacSiKhamId }
                  : { nguoiThucHienId: item.nguoiThucHienId }),
                ...([
                  LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
                  LOAI_DICH_VU.CDHA,
                ].includes(_loaiDichVu)
                  ? {
                      ptTtNguoiThucHien: _findDv?.ptTtNguoiThucHien,
                      phanLoaiPtTt: item?.phanLoaiPtTt,
                      nguoiTiepNhanId: _findDv?.nguoiTiepNhanId,
                      dieuDuongId: _findDv?.dieuDuongId,
                    }
                  : {}),
                ...(_loaiDichVu == LOAI_DICH_VU.XET_NGHIEM
                  ? {
                      phuThucHien1Id: _findDv?.phuThucHien1Id,
                      phuThucHien2Id: _findDv?.phuThucHien2Id,
                      phuThucHien3Id: _findDv?.phuThucHien3Id,
                      thanhVienKhacId: _findDv?.thanhVienKhacId,
                      nguoiThucHien2Id: _findDv?.nguoiThucHien2Id,
                      phanTangNguyCoId: _findDv?.phanTangNguyCoId,
                    }
                  : {}),
              };
            });
            dataTamTinhTien = [...dataTamTinhTien, ...tinhTien];
          });
          dispatch.chiDinhKhamBenh.updateData({
            dataTamTinhTien,
          });
          return dataTamTinhTien;
        })
        .catch((e) => {
          message.error(e?.message);
          return e;
        });
    },
    chiDinhDichVu: async (payload = {}, state) => {
      return new Promise((resolve, reject) => {
        const { listLoaiDichVu } = state.chiDinhKhamBenh;

        let {
          dsDichVuCanBoSung = [],
          dataTamTinhTien,
          isShowDvTiepDon = false,
        } = payload;

        const chiDinhGoiDV = new Promise((resolve, reject) => {
          const body = dataTamTinhTien.filter((item) => item.boChiDinhId);
          const groupedBody = groupBy(body, "boChiDinhId");
          let sendBody = [];
          Object.keys(groupedBody).forEach((element) => {
            sendBody.push({
              nbDotDieuTriId: groupedBody[element][0].nbDotDieuTriId,
              boChiDinhId: element,
              dsKham:
                groupedBody[element].filter(
                  (item) => item.nbDichVu.loaiDichVu === LOAI_DICH_VU.KHAM
                ) || null,
              dsXetNghiem:
                groupedBody[element].filter(
                  (item) => item.nbDichVu.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM
                ) || null,
              dsCdhaTdcnPtTt:
                groupedBody[element].filter(
                  (item) =>
                    item.nbDichVu.loaiDichVu === LOAI_DICH_VU.CDHA ||
                    item.nbDichVu.loaiDichVu ===
                      LOAI_DICH_VU.PHAU_THUAT_THU_THUAT
                ) || null,
              dsNgoaiDieuTri:
                groupedBody[element].filter(
                  (item) =>
                    item.nbDichVu.loaiDichVu === LOAI_DICH_VU.NGOAI_DIEU_TRI
                ) || null,
              dsThuoc:
                groupedBody[element].filter(
                  (item) => item.nbDichVu.loaiDichVu === LOAI_DICH_VU.THUOC
                ) || null,
              dsVatTu:
                groupedBody[element].filter(
                  (item) => item.nbDichVu.loaiDichVu === LOAI_DICH_VU.VAT_TU
                ) || null,
              dsHoaChat:
                groupedBody[element].filter(
                  (item) => item.nbDichVu.loaiDichVu === LOAI_DICH_VU.HOA_CHAT
                ) || null,
              dsChePhamMau:
                groupedBody[element].filter(
                  (item) =>
                    item.nbDichVu.loaiDichVu === LOAI_DICH_VU.CHE_PHAM_MAU
                ) || null,
            });
          });

          if (sendBody.length)
            return nbBoChiDinhProvider
              .chiDinhGoiDV(sendBody)
              .then((s) => {
                if (s.code === 0) {
                  resolve(s);
                } else {
                  reject(s);
                }
              })
              .catch((e) => {
                reject(e);
              });
          else resolve(0);
        });

        const chiDinhDVKham = new Promise((resolve, reject) => {
          const body = dataTamTinhTien.filter(
            (item) =>
              item.nbDichVu.loaiDichVu === LOAI_DICH_VU.KHAM &&
              !item.boChiDinhId
          );
          if (body.length)
            return nbDvKhamProvider
              .chiDinhDVKham(body)
              .then((s) => {
                if (s.code === 0) {
                  resolve(s);
                } else {
                  reject(s);
                }
              })
              .catch((e) => {
                reject(e);
              });
          else resolve(0);
        });

        const chiDinhDVXN = new Promise((resolve, reject) => {
          const body = dataTamTinhTien.filter(
            (item) =>
              item.nbDichVu.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM &&
              !item.boChiDinhId
          );
          if (body.length)
            return nbDvXNProvider
              .chiDinhXN(body)
              .then((s) => {
                if (s.code === 0) {
                  resolve(s);
                } else {
                  reject(s);
                }
              })
              .catch((e) => {
                reject(e);
              });
          else resolve(0);
        });

        const chiDinhDVCLS = new Promise((resolve, reject) => {
          const body = dataTamTinhTien.filter(
            (item) =>
              (item.nbDichVu.loaiDichVu === LOAI_DICH_VU.CDHA ||
                item.nbDichVu.loaiDichVu ===
                  LOAI_DICH_VU.PHAU_THUAT_THU_THUAT) &&
              !item.boChiDinhId
          );
          if (body.length)
            nbDvCLSProvider
              .chiDinhCLS(body)
              .then((s) => {
                if (s.code === 0) {
                  resolve(s);
                } else {
                  reject(s);
                }
              })
              .catch((e) => {
                reject(e);
              });
          else resolve(0);
        });

        const chiDinhNgoaiDieuTri = new Promise((resolve, reject) => {
          const body = dataTamTinhTien
            .filter(
              (item) =>
                item.nbDichVu.loaiDichVu === LOAI_DICH_VU.NGOAI_DIEU_TRI &&
                !item.boChiDinhId
            )
            .map((item) => {
              const newItem = cloneDeep(item);
              newItem.phongThucHienId = item.nbDvKyThuat?.phongThucHienId;
              if (newItem.nbDichVu) {
                delete newItem.nbDichVu.giaKhongBaoHiem;
                delete newItem.nbDichVu.giaBaoHiem;
              }
              return newItem;
            });
          if (body.length)
            nbDvNgoaiDieuTriProvider
              .chiDinhNgoaiDieuTri(body)
              .then((s) => {
                if (s.code === 0) {
                  resolve(s);
                } else {
                  reject(s);
                }
              })
              .catch((e) => {
                reject(e);
              });
          else resolve(0);
        });

        Promise.all([
          chiDinhDVKham,
          chiDinhDVXN,
          chiDinhDVCLS,
          chiDinhGoiDV,
          chiDinhNgoaiDieuTri,
        ])
          .then((response) => {
            let errMessage = [];
            response.forEach((res) => {
              if (res === 0) return;
              const updatingRecord = res.data.filter(
                (item) =>
                  item.code && ![0, 8724, 7603, 7830, 403]?.includes(item.code)
              );
              //7830: "Người bệnh chưa sàng lọc dinh dưỡng trong 36h, vui lòng thực hiện sàng lọc dinh dưỡng cho NB"

              //cập nhật lại ds phòng thực hiện cho các dịch vụ bị lỗi
              updatingRecord.forEach((item) => {
                const _findDv = (dataTamTinhTien || []).find(
                  (x) => x?.nbDichVu?.dichVuId === item?.nbDichVu?.dichVuId
                );
                if (_findDv) {
                  item.dsPhongThucHien = _findDv.dsPhongThucHien;
                  item.dsLoaiHinhThanhToan = _findDv.dsLoaiHinhThanhToan;
                  if (!item.nbDichVu?.loaiDichVu) {
                    if (!item.nbDichVu) {
                      item.nbDichVu = {};
                    }
                    item.nbDichVu.loaiDichVu = _findDv.nbDichVu?.loaiDichVu;
                  }
                }
              });

              //DS lỗi với thuốc chính
              const listMessages = res.data
                .filter((item) => item.code && item.code !== 0)
                .map(
                  (item2) =>
                    `(${item2?.nbDichVu?.dichVu?.ten} - ${item2.message})`
                );
              //DS lỗi với thuốc dùng kèm
              const listDungKemMessages = flatten(
                res.data.map((item) => item.dsDvKemTheo || [])
              )
                .filter((item) => item.code && item.code !== 0)
                .map(
                  (item2) =>
                    `(${item2?.nbDichVu?.dichVu?.ten} - ${item2.message})`
                );
              errMessage = [
                ...errMessage,
                ...listMessages,
                ...listDungKemMessages,
              ];
              dsDichVuCanBoSung = [...dsDichVuCanBoSung, ...updatingRecord];
            });

            errMessage = [...new Set(errMessage)];
            if (errMessage.length) {
              message.error(errMessage.join());
            } else {
              message.success(t("common.capNhatThanhCong"));
            }
            resolve({
              code: 0,
              dsDichVuCanBoSung,
              response,
              errMessage,
            });
            let listDichVu = [];
            response.forEach((item) => {
              if (item?.data) {
                listDichVu = [...listDichVu, ...item?.data];
              }
            });
            dispatch.chiDinhKhamBenh.getDsDichVuChiDinh({
              listLoaiDichVu,
              listDichVu,
              dsDichVuCanBoSung,
              isShowDvTiepDon,
            });

            //refresh lại list phiếu
            dispatch.phieuIn.updateData({
              refreshListPhieu: state.phieuIn.refreshListPhieu + 1,
            });
          })
          .catch((e) => {
            message.error(e?.message);
            reject(e);
          });
      });
    },
    getDsDichVuChiDinhXN: (params, state) => {
      return new Promise((resolve, reject) => {
        const {
          chiDinhTuDichVuId,
          nbDotDieuTriId,
          chiDinhTuLoaiDichVu,
          thongTinNguoiBenh,
        } = state.chiDinhKhamBenh.configData || {};
        const isKsk =
          thongTinNguoiBenh?.khamSucKhoe || thongTinNguoiBenh?.loaiDoiTuongKsk;
        const { isShowDvTiepDon = false, sort, ...rest } = params || {};
        let payload = {
          nbDotDieuTriId: nbDotDieuTriId,
          dsTrangThaiHoan: [0, 10, 20],
          page: "",
          size: "",
          sort,
        };
        if (isShowDvTiepDon) {
          payload = {
            ...payload,
            ...rest,
          };
        } else {
          payload = {
            ...payload,
            dsChiDinhTuDichVuId: isKsk ? undefined : chiDinhTuDichVuId,
            chiDinhTuLoaiDichVu: isKsk ? undefined : chiDinhTuLoaiDichVu,
          };
        }
        nbDvXNProvider
          .searchTongHop(payload)
          .then((s) => {
            if (s.code === 0) {
              dispatch.chiDinhKhamBenh.updateData({
                dsDichVuChiDinhXN: (s.data || []).map((item) => ({
                  ...item,
                  loaiDichVuXoa: LOAI_DICH_VU.XET_NGHIEM,
                })),
              });
              resolve(s);
            } else {
              reject(s);
              message.error(s.message);
            }
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    getDsDichVuChiDinh: (payload, state) => {
      return new Promise(async (resolve, reject) => {
        const {
          listLoaiDichVu,
          listDichVu = [],
          dsDichVuCanBoSung = [],
          isShowDvTiepDon = false,
        } = payload; //dsDichVuCanBoSung là các dịch vụ lỗi, dịch vụ chưa được kê
        //danh sách các mã phiếu đang active
        let activeKey = [...(state.chiDinhKhamBenh.collapseDichVuTemp || [])];
        let listPhieuIds = null;

        const { nbDotDieuTriId, chiDinhTuDichVuId, chiDinhTuLoaiDichVu } =
          state.chiDinhKhamBenh.configData || {};
        let addParams = {
          dsChiDinhTuDichVuId: [nbDotDieuTriId, chiDinhTuDichVuId],
        };

        if (listLoaiDichVu.includes(LOAI_DICH_VU.KHAM)) {
          let res = await dispatch.chiDinhKhamBenh.getDsDichVuChiDinhKham({
            isShowDvTiepDon,
            dsChiDinhTuLoaiDichVu: [chiDinhTuLoaiDichVu, LOAI_DICH_VU.TIEP_DON],
            ...addParams,
          });
          listPhieuIds = res.data
            .filter((item) => listDichVu.some((item1) => item1.id === item.id))
            .map(
              (itemLast) =>
                `${itemLast.soPhieu}-${itemLast.tenPhieuChiDinh}-${itemLast.diaDiemPhongThucHien}`
            );
          //gọi api để check case kê cùng phòng
          dispatch.chiDinhKhamBenh.getDsDichVuKham({
            nbDotDieuTriId,
          });
          activeKey = [...activeKey, ...listPhieuIds];
        }
        if (listLoaiDichVu.includes(LOAI_DICH_VU.XET_NGHIEM)) {
          let res = await dispatch.chiDinhKhamBenh.getDsDichVuChiDinhXN({
            isShowDvTiepDon,
            dsChiDinhTuLoaiDichVu: [chiDinhTuLoaiDichVu, LOAI_DICH_VU.TIEP_DON],
            ...addParams,
          });
          listPhieuIds = res.data
            .filter((item) => listDichVu.some((item1) => item1.id === item.id))
            .map(
              (itemLast) =>
                `${itemLast.soPhieu}-${itemLast.tenPhieuChiDinh}-${itemLast.diaDiemPhongThucHien}`
            );
          activeKey = [...activeKey, ...listPhieuIds];
        }
        if (
          listLoaiDichVu.includes(LOAI_DICH_VU.CDHA) ||
          listLoaiDichVu.includes(LOAI_DICH_VU.PHAU_THUAT_THU_THUAT)
        ) {
          let res = await dispatch.chiDinhKhamBenh.getDsDichVuChiDinhCLS({
            isShowDvTiepDon,
            dsChiDinhTuLoaiDichVu: [chiDinhTuLoaiDichVu, LOAI_DICH_VU.TIEP_DON],
            ...addParams,
          });
          listPhieuIds = res.data
            .filter((item) => listDichVu.some((item1) => item1.id === item.id))
            .map(
              (itemLast) =>
                `${itemLast.soPhieu}-${itemLast.tenPhieuChiDinh}-${itemLast.diaDiemPhongThucHien}`
            );
          activeKey = [...activeKey, ...listPhieuIds];
        }
        if (listLoaiDichVu.includes(LOAI_DICH_VU.NGOAI_DIEU_TRI)) {
          let res = await dispatch.chiDinhKhamBenh.getDsDichVuNgoaiDieuTri({
            isShowDvTiepDon,
            dsChiDinhTuLoaiDichVu: [chiDinhTuLoaiDichVu, LOAI_DICH_VU.TIEP_DON],
            ...addParams,
          });
          listPhieuIds = res.data
            .filter((item) => listDichVu.some((item1) => item1.id === item.id))
            .map(
              (itemLast) =>
                `${itemLast.soPhieu}-${itemLast.tenPhieuChiDinh}-${itemLast.diaDiemPhongThucHien}`
            );
          activeKey = [...activeKey, ...listPhieuIds];
        }
        dispatch.chiDinhKhamBenh.updateData({
          collapseDichVu: activeKey, //set activeKey vào collapseDichVu
          collapseDichVuTemp: dsDichVuCanBoSung?.length ? activeKey : [], //khi đang còn dịch vụ pending thì vẫn giữ lại danh sách các activeKey
        });
      });
    },
    getDsDichVuChiDinhKham: (params, state) => {
      return new Promise((resolve, reject) => {
        const {
          chiDinhTuDichVuId,
          nbDotDieuTriId,
          chiDinhTuLoaiDichVu,
          thongTinNguoiBenh,
        } = state.chiDinhKhamBenh.configData || {};
        const { isShowDvTiepDon = false, sort, ...rest } = params || {};
        const isKsk =
          thongTinNguoiBenh?.khamSucKhoe || thongTinNguoiBenh?.loaiDoiTuongKsk;
        let payload = {
          nbDotDieuTriId: nbDotDieuTriId,
          dsTrangThaiHoan: [0, 10, 20],
          sort,
        };
        if (isShowDvTiepDon) {
          payload = {
            ...payload,
            ...rest,
          };
        } else {
          payload = {
            ...payload,
            dsChiDinhTuDichVuId: isKsk ? undefined : chiDinhTuDichVuId,
            chiDinhTuLoaiDichVu: isKsk ? undefined : chiDinhTuLoaiDichVu,
          };
        }
        nbDvKhamProvider
          .getDsDichVuChiDinhKham(payload)
          .then((s) => {
            if (s.code === 0) {
              //lọc bỏ dịch vụ đang khám
              dispatch.chiDinhKhamBenh.updateData({
                dsDichVuChiDinhKham: (s.data || [])
                  .filter((x) => x.id != chiDinhTuDichVuId)
                  .map((item) => ({
                    ...item,
                    loaiDichVuXoa: LOAI_DICH_VU.KHAM,
                  })),
              });
              resolve(s);
            } else {
              reject(s);
              message.error(s.message);
            }
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    getDsDichVuChiDinhCLS: (params, state) => {
      return new Promise((resolve, reject) => {
        const {
          chiDinhTuDichVuId,
          nbDotDieuTriId,
          chiDinhTuLoaiDichVu,
          thongTinNguoiBenh,
        } = state.chiDinhKhamBenh.configData || {};
        const { isShowDvTiepDon = false, sort, ...rest } = params || {};
        let payload = {
          nbDotDieuTriId: nbDotDieuTriId,
          dsTrangThaiHoan: [0, 10, 20, 40],
          sort,
        };
        if (isShowDvTiepDon) {
          payload = {
            ...payload,
            ...rest,
          };
        } else {
          payload = {
            ...payload,
            dsChiDinhTuDichVuId: thongTinNguoiBenh?.khamSucKhoe
              ? undefined
              : chiDinhTuDichVuId,
            chiDinhTuLoaiDichVu: thongTinNguoiBenh?.khamSucKhoe
              ? undefined
              : chiDinhTuLoaiDichVu,
          };
        }
        nbDvCLSProvider
          .getDsDichVuChiDinhCLS(payload)
          .then((s) => {
            if (s.code === 0) {
              dispatch.chiDinhKhamBenh.updateData({
                dsDichVuChiDinhCls: (s.data || []).map((item) => ({
                  ...item,
                  loaiDichVuXoa: LOAI_DICH_VU.CDHA,
                })),
              });
              resolve(s);
            } else {
              reject(s);
              message.error(s.message);
            }
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    getDsDichVuNgoaiDieuTri: (params, state) => {
      return new Promise((resolve, reject) => {
        const {
          chiDinhTuDichVuId,
          nbDotDieuTriId,
          chiDinhTuLoaiDichVu,
          dsChiDinhTuLoaiDichVu,
        } = state.chiDinhKhamBenh.configData || {};
        const { isShowDvTiepDon = false, sort, ...rest } = params || {};
        let payload = {
          nbDotDieuTriId: nbDotDieuTriId,
          dsTrangThaiHoan: [0, 10, 20],
          page: "",
          size: "",
          sort,
        };
        if (isShowDvTiepDon) {
          payload = {
            ...payload,
            ...rest,
          };
        } else {
          payload = {
            ...payload,
            dsChiDinhTuDichVuId: chiDinhTuDichVuId,
            chiDinhTuLoaiDichVu: chiDinhTuLoaiDichVu,
            dsChiDinhTuLoaiDichVu: dsChiDinhTuLoaiDichVu,
          };
        }
        nbDvNgoaiDieuTriProvider
          .searchTongHop(payload)
          .then((s) => {
            if (s.code === 0) {
              dispatch.chiDinhKhamBenh.updateData({
                dsDichVuNgoaiDieuTri: (s.data || []).map((item) => ({
                  ...item,
                  loaiDichVuXoa: LOAI_DICH_VU.NGOAI_DIEU_TRI,
                })),
              });
              resolve(s);
            } else {
              reject(s);
              message.error(s.message);
            }
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    getNBSoPhieuCLS: ({ page, loaiDichVu }, state) => {
      return new Promise((resolve, reject) => {
        const { nbDotDieuTriId } = state.chiDinhKhamBenh.configData || {};
        const data = {
          nbDotDieuTriId: nbDotDieuTriId,
          page,
          loaiDichVu,
        };
        nbDvKTProvider
          .getNBSoPhieuCLS(data)
          .then((s) => {
            if (s.code === 0) {
              dispatch.chiDinhKhamBenh.updateData({
                soPhieuCls: s.data.map((item) => ({
                  id: item.id,
                  ten: item.soPhieu,
                })),
              });
              resolve(s);
            } else {
              reject(s);
              message.error(s.message);
            }
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    getDsSoPhieuCLS: ({ page, loaiDichVu, nbDotDieuTriId }, state) => {
      return new Promise((resolve, reject) => {
        const data = {
          nbDotDieuTriId,
          page,
          loaiDichVu,
        };
        nbDvKTProvider
          .getNBSoPhieuCLS(data)
          .then((s) => {
            if (s.code === 0) {
              dispatch.chiDinhKhamBenh.updateData({
                soPhieuCls: s.data.map((item) => ({
                  id: item.id,
                  ten: item.soPhieu,
                })),
              });
              resolve(s);
            } else {
              reject(s);
              message.error(s.message);
            }
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    onDeleteDichVu: ({ id, loaiDichVu, listDeletingId }, state) => {
      return new Promise((resolve, reject) => {
        let provider = null;
        switch (loaiDichVu) {
          case LOAI_DICH_VU.KHAM: //kham
            provider = nbDvKhamProvider;
            break;
          case LOAI_DICH_VU.XET_NGHIEM: //xet nghiem
            provider = nbDvXNProvider;
            break;
          case LOAI_DICH_VU.CDHA:
          case LOAI_DICH_VU.PHAU_THUAT_THU_THUAT:
            provider = nbDvCLSProvider;
            break;
          case LOAI_DICH_VU.NGOAI_DIEU_TRI:
            provider = nbDvNgoaiDieuTriProvider;
            break;
          default:
            break;
        }
        if (provider && provider.onDeleteDichVu) {
          provider
            .onDeleteDichVu({ id, listDeletingId })
            .then((s) => {
              if (s.code === 0) {
                const resultError =
                  s?.data?.filter((item) => item.code !== 0) || [];
                resultError.forEach((item) =>
                  message.error(
                    item?.message || t("common.xayRaLoiVuiLongThuLaiSau")
                  )
                );
                if (resultError.length) {
                } else {
                  message.success(
                    t("khamBenh.xoaThanhCongDuLieuChiDinhDichVu")
                  );
                }
                resolve(s);
              } else {
                reject(s);
                message.error(s.message);
              }
            })
            .catch((e) => {
              reject(e);
              message.error(e?.message);
            });
        }
      });
    },
    huyTiepNhan: ({ loaiDichVu, listId }, state) => {
      return new Promise((resolve, reject) => {
        let provider = null;
        switch (loaiDichVu) {
          case LOAI_DICH_VU.KHAM: //kham
            provider = nbDvKhamProvider;
            break;
          case LOAI_DICH_VU.XET_NGHIEM: //xet nghiem
            provider = nbDvXNProvider;
            break;
          case LOAI_DICH_VU.CDHA:
          case LOAI_DICH_VU.PHAU_THUAT_THU_THUAT:
            provider = nbDvCLSProvider;
            break;
          default:
            break;
        }
        if (provider && provider.huyTiepNhan) {
          provider
            .huyTiepNhan(listId)
            .then((s) => {
              if (s.code === 0) {
                message.success(
                  t("khamBenh.huyTiepNhanThanhCongDuLieuChiDinhDichVu")
                );
                resolve(s);
              } else {
                reject(s);
                message.error(s.message);
              }
            })
            .catch((e) => {
              reject(e);
              message.error(e?.message);
            });
        }
      });
    },
    coKetQua: ({ loaiDichVu, payload }, state) => {
      return new Promise((resolve, reject) => {
        let provider = null;
        switch (loaiDichVu) {
          case LOAI_DICH_VU.KHAM: //kham
            provider = nbDvKhamProvider;
            break;
          case LOAI_DICH_VU.XET_NGHIEM: //xet nghiem
            provider = nbDvXNProvider;
            break;
          case LOAI_DICH_VU.CDHA:
          case LOAI_DICH_VU.PHAU_THUAT_THU_THUAT:
            provider = nbDvCLSProvider;
            break;
          default:
            break;
        }
        if (provider && provider.coKetQua) {
          provider
            .coKetQua(payload)
            .then((s) => {
              if (s.code === 0) {
                message.success(t("common.traKetQuaDvThanhCong"));
                resolve(s);
              } else {
                reject(s);
                message.error(s.message);
              }
            })
            .catch((e) => {
              reject(e);
              message.error(e?.message);
            });
        }
      });
    },
    huyKetQua: ({ loaiDichVu, payload }, state) => {
      return new Promise((resolve, reject) => {
        let provider = null;
        switch (loaiDichVu) {
          case LOAI_DICH_VU.KHAM: //kham
            provider = nbDvKhamProvider;
            break;
          case LOAI_DICH_VU.XET_NGHIEM: //xet nghiem
            provider = nbDvXNProvider;
            break;
          case LOAI_DICH_VU.CDHA:
          case LOAI_DICH_VU.PHAU_THUAT_THU_THUAT:
            provider = nbDvCLSProvider;
            break;
          default:
            break;
        }
        if (provider && provider.huyKetQua) {
          provider
            .huyKetQua(payload)
            .then((s) => {
              if (s.code === 0) {
                message.success(t("cdha.huyKetQuaDVThanhCong"));
                resolve(s);
              } else {
                reject(s);
                message.error(s.message);
              }
            })
            .catch((e) => {
              reject(e);
              message.error(e?.message);
            });
        }
      });
    },
    xemKetQua: ({ loaiDichVu, payload }, state) => {
      return new Promise((resolve, reject) => {
        let provider = null;
        switch (loaiDichVu) {
          case LOAI_DICH_VU.KHAM: //kham
            provider = nbDvKhamProvider;
            break;
          case LOAI_DICH_VU.XET_NGHIEM: //xet nghiem
            provider = nbDvXNProvider;
            break;
          case LOAI_DICH_VU.CDHA:
          case LOAI_DICH_VU.PHAU_THUAT_THU_THUAT:
            provider = nbDvCLSProvider;
            break;
          default:
            break;
        }
        if (provider && provider.coKetQua) {
          provider
            .xemKetQua(payload)
            .then((s) => {
              if (s.code === 0) {
                message.success(t("common.traKetQuaDvThanhCong"));
                resolve(s);
              } else {
                reject(s);
                message.error(s.message);
              }
            })
            .catch((e) => {
              reject(e);
              message.error(e?.message);
            });
        }
      });
    },
    huyXemKetQua: ({ loaiDichVu, payload }, state) => {
      return new Promise((resolve, reject) => {
        let provider = null;
        switch (loaiDichVu) {
          case LOAI_DICH_VU.KHAM: //kham
            provider = nbDvKhamProvider;
            break;
          case LOAI_DICH_VU.XET_NGHIEM: //xet nghiem
            provider = nbDvXNProvider;
            break;
          case LOAI_DICH_VU.CDHA:
          case LOAI_DICH_VU.PHAU_THUAT_THU_THUAT:
            provider = nbDvCLSProvider;
            break;
          default:
            break;
        }
        if (provider && provider.huyKetQua) {
          provider
            .huyXemKetQua(payload)
            .then((s) => {
              if (s.code === 0) {
                message.success(t("cdha.huyKetQuaDVThanhCong"));
                resolve(s);
              } else {
                reject(s);
                message.error(s.message);
              }
            })
            .catch((e) => {
              reject(e);
              message.error(e?.message);
            });
        }
      });
    },
    themThongTinDV: ({ body, id, loaiDichVu }, state) => {
      return new Promise((resolve, reject) => {
        let api = null;
        if (loaiDichVu === LOAI_DICH_VU.KHAM)
          api = nbDvKhamProvider.themThongTinDV;
        else if (loaiDichVu === LOAI_DICH_VU.XET_NGHIEM)
          api = nbDvXNProvider.themThongTinDV;
        else if (loaiDichVu === LOAI_DICH_VU.CDHA)
          api = nbDvCLSProvider.themThongTinDV;
        else if (loaiDichVu === LOAI_DICH_VU.PHAU_THUAT_THU_THUAT)
          api = nbDvCLSProvider.themThongTinDV;
        else if (loaiDichVu === LOAI_DICH_VU.NGOAI_DIEU_TRI)
          api = nbDvNgoaiDieuTriProvider.themThongTinDV;
        else if (loaiDichVu === LOAI_DICH_VU.THUOC)
          api = nbDvThuocProvider.themThongTin;
        if (api)
          api(body, id)
            .then((s) => {
              if (s.code === 0) {
                message.success(t("common.capNhatThanhCong"));
                resolve(s);
              } else {
                reject(s);
                message.error(s.message);
              }
            })
            .catch((e) => {
              reject(e);
              message.error(e?.message);
            });
        else reject();
      });
    },
    themThongTinPhieu: ({ body, id, loaiDichVu }, state) => {
      return new Promise((resolve, reject) => {
        let api = null;
        if (loaiDichVu === LOAI_DICH_VU.KHAM)
          api = nbDvKhamProvider.themThongTinPhieu;
        else if (loaiDichVu === LOAI_DICH_VU.XET_NGHIEM)
          api = nbDvXNProvider.themThongTinPhieu;
        else if (loaiDichVu === LOAI_DICH_VU.CDHA)
          api = nbDvCLSProvider.themThongTinPhieu;
        if (api) {
          api(body, id)
            .then((s) => {
              if (s.code === 0) {
                message.success(t("common.capNhatThanhCong"));
                resolve(s);
              } else {
                reject(s);
                message.error(s.message);
              }
            })
            .catch((e) => {
              reject(e);
              message.error(e?.message);
            });
        } else {
          reject();
        }
      });
    },

    inPhieu: (
      {
        loaiDichVu,
        nbDotDieuTriId,
        soPhieuId,
        dsSoPhieuId,
        phieuChiDinhId,
        chiDinhTuLoaiDichVu,
        chiDinhTuDichVuId,
        dsChiDinhTuLoaiDichVu,
        dsNbDichVuId,
        loai,
      },
      state
    ) => {
      return new Promise(async (resolve, reject) => {
        let api = "";
        switch (loaiDichVu) {
          case 10:
            api = nbDvKhamProvider;
            break;
          case 20:
            api = nbDvXNProvider;
            break;
          case 30:
          case 40:
            api = nbDvCLSProvider;
            break;
          case 60:
            api = nbDvNgoaiDieuTriProvider;
            break;
          default:
            break;
        }
        try {
          if (!api) reject();
          const s = await api.getPhieuChiDinh({
            nbDotDieuTriId,
            soPhieuId,
            dsSoPhieuId,
            phieuChiDinhId,
            ...(loaiDichVu !== LOAI_DICH_VU.XET_NGHIEM
              ? { chiDinhTuLoaiDichVu }
              : {}),
            dsChiDinhTuLoaiDichVu,
            chiDinhTuDichVuId,
            dsNbDichVuId,
            loai,
          });

          if (s?.code == 0) {
            await printProvider.printPdf(s.data);
            resolve();
          } else {
            message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(s);
          }
        } catch (error) {
          message.error(error?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          reject(error);
        }
      });
    },
    inPhieuKetQua: (
      {
        loaiDichVu,
        nbDotDieuTriId,
        soPhieuId,
        dsSoPhieuId,
        phieuChiDinhId,
        chiDinhTuLoaiDichVu,
        chiDinhTuDichVuId,
        dsSoKetNoi,
        nbDichVuId,
        returnData,
      },
      state
    ) => {
      return new Promise(async (resolve, reject) => {
        let api = "",
          isLocPhieu = false,
          isXetNghiem = false;
        switch (loaiDichVu) {
          case LOAI_DICH_VU.XET_NGHIEM:
            isLocPhieu = true;
            isXetNghiem = true;
            api = nbDvXNProvider;
            break;
          case LOAI_DICH_VU.CDHA:
          case LOAI_DICH_VU.PHAU_THUAT_THU_THUAT:
            api = nbDvCLSProvider;
            isLocPhieu = true;
            isXetNghiem = false;
            break;
          default:
            break;
        }
        if (!api) {
          reject();
          return;
        }
        try {
          const s = await api.getPhieuKetQua({
            nbDotDieuTriId,
            soPhieuId,
            dsSoPhieuId,
            phieuChiDinhId,
            chiDinhTuLoaiDichVu,
            chiDinhTuDichVuId,
            dsSoKetNoi,
            nbDichVuId,
          });
          const { dsPhieuHis, dsPhieuLis, dsPhieuPacs } = s?.data || {};
          const dsPhieu = locPhieuLisPacs(
            { dsPhieuHis, dsPhieuLis, dsPhieuPacs },
            { allData: false, isLocPhieu, isXetNghiem }
          );
          const dsPhieuFull = locPhieuLisPacs(
            { dsPhieuHis, dsPhieuLis, dsPhieuPacs },
            { allData: true, isLocPhieu, isXetNghiem }
          );

          if (returnData) {
            resolve(dsPhieuFull);
          } else {
            if (
              isArray(dsPhieuFull, true) &&
              dsPhieuFull.every((x) => x?.loaiIn == LOAI_IN.MO_TAB)
            ) {
              const finalFile = await printProvider.getMergePdf(dsPhieu);
              openInNewTab(finalFile);
            } else {
              await printProvider.printPdf(dsPhieuFull);
            }
          }

          console.info("Print success");
          resolve();
        } catch (e) {
          message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          reject();
        }
      });
    },
    inPhieuKhamChuyenKhoa: (payload, state) => {
      return new Promise(async (resolve, reject) => {
        try {
          const s = await nbDvKhamProvider.getPhieuKhamChuyenKhoa(payload);
          await printProvider.printPdf(s.data);
          console.info("Print success");
          resolve();
        } catch (error) {
          message.error(error?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          reject(error);
        }
      });
    },

    luuLoiDan: (
      { body, id, loaiDichVu, notUpdateThongTinKham = false },
      state
    ) => {
      return new Promise(async (resolve, reject) => {
        try {
          const s = await nbDvKhamProvider.loiDan(body, id);
          //sau khi lưu xong lời dăn, ghi chú thì cập nhật lại thông tin chi tiết dv khám
          // vì 2 thông tin lời dặn, ghi chú nằm trong nbKetLuan và nbKhamXet
          if (!notUpdateThongTinKham) {
            const res = await nbDvKhamProvider.getById(id);
            dispatch.khamBenh.updateData({
              thongTinChiTiet: res?.data || {},
              thongTinChiTietBanDau: cloneDeep(res?.data || {}),
            });
          }
          // message.success("Cập nhật thành công dữ liệu dịch vụ!");
          resolve(s);
        } catch (error) {
          message.error(error?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          reject(error);
        }
      });
    },

    getDsGoiDvChiTiet: (params) => {
      return new Promise((resolve, reject) => {
        nbGoiDvChiTietProvider
          .search(params)
          .then((s) => {
            if (s?.code === 0) {
              let data = (s?.data || []).map((x) => ({
                ...x,
                ten: x.tenDichVu,
                ma: x.maDichVu,
                id: undefined,
                nbGoiDvChiTietId: x.id,
              }));
              dispatch.chiDinhKhamBenh.updateData({
                listDvKham: data,
                page: s.pageNumber || 0,
                size: s.pageSize || data?.length || 0,
                totalElements: s.totalElements || data?.length || 0,
              });
              resolve(s);
            } else {
              reject(s);
            }
          })
          .catch((e) => {
            reject(e);
          });
      });
    },

    postNbGoiDv: (params) => {
      return new Promise((resolve, reject) => {
        nbGoiDvProvider
          .post(params)
          .then((s) => {
            if (s?.code === 0) {
              message.success(
                t("khamBenh.themMoiThanhCongGoiDichVuChoNguoiBenh")
              );
              resolve(s);
            } else {
              reject(s);
            }
          })
          .catch((e) => {
            reject(e);
          });
      });
    },

    getChiTietDichVuKham: (id, state) => {
      return new Promise(async (resolve, reject) => {
        try {
          const res = await nbDvKhamProvider.getById(id);
          resolve(res?.data || {});
        } catch (error) {
          message.error(error?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          reject(error);
        }
      });
    },

    getChiTietNguoiBenh: (nbDotDieuTriId, state) => {
      return new Promise(async (resolve, reject) => {
        try {
          const res = await nbDotDieuTriProvider.getByIdTongHop(nbDotDieuTriId);
          resolve(res?.data || {});
        } catch (error) {
          message.error(error?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          reject(error);
        }
      });
    },

    getNbKhamChuyenKhoaNgoai: (id, state) => {
      return new Promise(async (resolve, reject) => {
        try {
          const res = await nbDvKhamProvider.getNbKhamChuyenKhoaKhamNgoai(id);
          resolve(res?.data || {});
        } catch (error) {
          message.error(error?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          reject(error);
        }
      });
    },

    getDsDichVuTiepDon: (payload, state) => {
      return new Promise(async (resolve, reject) => {
        try {
          const res = await nbDichVuProvider.searchAll({
            page: "",
            size: "",
            ...payload,
          });
          const data = res?.data || [];

          dispatch.chiDinhKhamBenh.updateData({
            listDvTiepDon: data,
          });
          resolve(data);
        } catch (error) {
          message.error(error?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          reject(error);
        }
      });
    },
    getDsDichVuKham: (payload, state) => {
      return new Promise((resolve, reject) => {
        nbDvKhamProvider
          .getDsDichVuChiDinhKham(payload)
          .then((s) => {
            if (s.code === 0) {
              dispatch.chiDinhKhamBenh.updateData({
                dsDichVuKham: s.data,
              });
              resolve(s);
            } else {
              reject(s);
              message.error(s.message);
            }
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
  }),
};
