import { combineFields } from "utils/editor-utils";
import { message } from "antd";
import editorProvider from "data-access/editor-provider";
import dmBaoCaoProvider from "data-access/categories/dm-bao-cao-provider";
import cacheUtils from "lib-utils/cache-utils";
import { t } from "i18next";
import stringUtils from "mainam-react-native-string-utils";
import dmBaoCaoChanKyProvider from "data-access/categories/dm-bao-cao-chan-ky-provider";
import { isEmpty } from "lodash";
import danhSachPhieuChoKyProvider from "data-access/kySo/danh-sach-phieu-cho-ky-provider";

let timeout = null;

export const objectKey = [
  { fieldName: "tenNb" },
  { fieldName: "tuoi2" },
  { fieldName: "gioiTinh" },
  { fieldName: "tenKhoaChiDinh" },
  { fieldName: "tenPhong" },
  { fieldName: "soHieu<PERSON>iuong" },
  { fieldName: "diaChi" },
  { fieldName: "cdChinh" },
  { fieldName: "cdKemTheo" },
  { fieldName: "moTa" },
  { fieldName: "cdPhanBiet" },
  { fieldName: "dsThuoc" },
  { fieldName: "boSung" },
  { fieldName: "cacBoPhan" },
  { fieldName: "cdChinhMoTa" },
  { fieldName: "cdSauPt" },
  { fieldName: "cdSoBo" },
  { fieldName: "cdChinhMoTa" },
  { fieldName: "dienBienBenh" },
  { fieldName: "dsDienBienBenh" },
  { fieldName: "dsDvKt" },
  { fieldName: "dsVacXin" },
  { fieldName: "dsCdChinhId" },
  { fieldName: "dsCdKemTheoId" },
  { fieldName: "theBenhLao" },
  { fieldName: "quaTrinhBenhLy" },
  { fieldName: "tienSuBanThan" },
  { fieldName: "tienSuGiaDinh" },
  { fieldName: "diUngThuoc" },
  { fieldName: "toanThan" },
  { fieldName: "giaiDoanBenh" },
  { fieldName: "thoiGianDangKy" },
  { fieldName: "thoiGianYLenh" },
  { fieldName: "baoCaoId" },
  { fieldName: "nbDotDieuTriId" },
  { fieldName: "ghiChuNoiTru" },
  { fieldName: "lichSuKy" },
  { fieldName: "thoiGianThucHien" },
  { fieldName: "nbDvKhamTomTatCls" },
];

export default {
  state: {
    isFormDataLoading: false,
    isFileLoading: false,
    isSaveFormLoading: false,
    list: [],
    apiFields: [],
    file: {},
    apiFields: [], //list apiFields json template api
    fileData: {},
    fileDataHIS: {},
    listForm: [],
    fileTemplate: {},
    fileDataTemplate: {},
  }, // initial state
  reducers: {
    updateData(state, payload = {}) {
      return { ...state, ...payload };
    },
  },
  effects: (dispatch) => ({
    updateEditor: (editorId, state, payload) => {
      const editorData = state.files.editorData || {};
      const data = { ...(editorData[editorId] || {}), ...payload };
      editorData[editorId] = data;
      dispatch.files.updateData({
        editorData: { ...editorData },
      });
    },
    getBaoCaoByMaBaoCao: (
      editorId,
      state,
      { maBaoCao, id, queries: _queries, isGetMultiFile = false }
    ) => {
      return new Promise(async (resolve, reject) => {
        try {
          const baoCaos = await dmBaoCaoProvider.getMauBaoCao({
            ma: maBaoCao,
            ngonNgu: _queries?.ngonNgu,
          });
          if (baoCaos.data?.components) {
            const chanKyBaoCao = await dispatch.files.getListChanKy(
              baoCaos.data.id
            );
            const baoCao = baoCaos.data;
            dispatch.files.updateEditor(editorId, {
              file: baoCao,
              chanKyBaoCao,
            });
            let queries = _queries;
            if (maBaoCao === "EMR_BA224" && queries.loai == 10) {
              delete queries.loai;
            }
            if (
              [
                "EMR_BA224",
                "EMR_HSDD092",
                "EMR_HSDD015.4",
                "EMR_HSDD015.3",
                "EMR_BA083",
                "EMR_BA284",
                "EMR_BA224.1",
                "EMR_BA250",
                "EMR_HSDD098.1",
                "EMR_HSDD098.2",
                "EMR_HSDD098.3",
                "EMR_HSDD098.4",
                "EMR_HSDD098.5",
                "EMR_HSDD098.6",
                "EMR_BA319",
                "EMR_BA343",
                "EMR_BA353",
                "EMR_BA362",
                "EMR_BA376",
                "EMR_BA377",
                "EMR_BA363",
              ].includes(maBaoCao) &&
              queries.tuThoiGian &&
              queries.denThoiGian
            ) {
              queries.tuThoiGian = decodeURIComponent(queries.tuThoiGian);
              queries.denThoiGian = decodeURIComponent(queries.denThoiGian);
            }
            if (["EMR_BA343", "EMR_BA346"].includes(maBaoCao)) {
              queries.thoiGianBanGiao = decodeURIComponent(
                queries.thoiGianBanGiao
              );
            }
            if (["EMR_BA307"].includes(maBaoCao)) {
              queries.tuThoiGian = decodeURIComponent(queries.tuThoiGian);
              queries.denThoiGian = decodeURIComponent(queries.denThoiGian);
            }

            //phiếu có truyền param thời gian thực hiện thì format lại định dạng truyền lên api
            if (queries.thoiGianThucHien) {
              queries.thoiGianThucHien = decodeURIComponent(
                queries.thoiGianThucHien
              );
            }
            if (maBaoCao == "EMR_HSDD015.4" && queries.dsLoaiChiDinh) {
              queries.dsLoaiChiDinh = decodeURIComponent(queries.dsLoaiChiDinh);
            }
            if (maBaoCao == "EMR_BA083" && queries.dsLoaiDichVu) {
              queries.dsLoaiDichVu = decodeURIComponent(queries.dsLoaiDichVu);
            }

            if (
              ["EMR_BA077", "EMR_BA077.1", "EMR_BA503", "EMR_BA503.1"].includes(
                maBaoCao
              ) &&
              queries.tuThoiGianThucHien &&
              queries.denThoiGianThucHien
            ) {
              queries.tuThoiGianThucHien = decodeURIComponent(
                queries.tuThoiGianThucHien
              );
              queries.denThoiGianThucHien = decodeURIComponent(
                queries.denThoiGianThucHien
              );
            }
            if (maBaoCao == "EMR_BA111") {
              queries.baoCaoId = baoCaos.data.id;
            }
            if (["EMR_HSDD015.2", "EMR_BA111"].includes(maBaoCao)) {
              queries.dsTrangThaiHoan = [0, 10, 20];
            }
            if (maBaoCao == "EMR_BA077.2" && queries.dsTrangThaiHoan) {
              queries.dsTrangThaiHoan = decodeURIComponent(
                queries.dsTrangThaiHoan
              );
            }

            const fileData = await dispatch.files.getFormData(editorId, {
              file: baoCao,
              id,
              queries,
              updateState: false,
            });
            if (["EMR_BA077", "EMR_BA503.1"]?.includes(maBaoCao)) {
              if (fileData?.dsToDieuTri?.length) {
                fileData.dsToDieuTri.forEach((item) => {
                  const sttHienThi = [
                    ...(item?.dsThuoc || []),
                    ...(item.dsThuocChiDinhNgoai || []),
                  ].map((stt) => ({
                    id: stt.id,
                    sttHienThi: stt.sttHienThi,
                    thuocChiDinhNgoai: stt.thuocChiDinhNgoaiId ? true : false,
                  }));
                  item.idGuid = stringUtils.guid();
                  item.sttHienThi = sttHienThi;
                });
              } else {
                message.error(
                  t("editor.toDieuTri.khongTimThayThongTinToDieuTri")
                );
              }
            }

            if (!isGetMultiFile) {
              dispatch.files.updateEditor(editorId, {
                fileData,
              });
            }
            const template = await dispatch.files.getTemplateAPI(editorId, {
              api: baoCao.apiTemplate || baoCao.api,
              updateState: false,
            });
            if (!isGetMultiFile) {
              dispatch.files.updateEditor(editorId, {
                ...template,
              });
              resolve(baoCao);
            } else {
              const data = {
                baoCao,
                template,
                fileData,
              };
              resolve(data);
            }
          } else {
            reject(baoCaos);
          }
        } catch (error) {
          message.error(error?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          reject(error);
        }
      });
    },
    getBaoCaoBaoCaoPreview: (editorId, state, { baoCaoId, templateId }) => {
      return new Promise(async (resolve, reject) => {
        try {
          let baoCao = await dmBaoCaoProvider.getById(baoCaoId);
          baoCao = baoCao.data;
          const template = await dispatch.files.getTemplateAPI(editorId, {
            api: baoCao.apiTemplate || baoCao.api,
            updateState: false,
          });
          dispatch.files.updateEditor(editorId, {
            ...template,
            file: baoCao,
            fileData: {},
          });
          resolve(baoCao);
        } catch (error) {
          message.error(error?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          reject(error);
        }
      });
    },

    //get danh sách trường dữ liệu
    getTemplateAPI: (editorId, state, { api, updateState = true }) => {
      return new Promise(async (resolve, reject) => {
        let template = await cacheUtils.read(
          "",
          "EDITOR_TEMPLATE_" + api,
          {},
          false
        );
        dispatch.files.updateEditor(editorId, template);
        editorProvider
          .getTemplateAPI({ api })
          .then((s) => {
            const apiFields = combineFields(s?.data, {}, null, true);
            let template = {
              fileTemplate: s?.data,
              apiFields: Object.keys(apiFields),
            };
            if (updateState) dispatch.files.updateEditor(editorId, template);
            resolve(template);
            cacheUtils.save("", "EDITOR_TEMPLATE_" + api, template, false);
          })
          .catch((e) => {
            reject(e);
          });
      });
    },

    getFormData: (
      editorId,
      state,
      { file, id, queries = "", updateState = true, ...payload }
    ) => {
      return new Promise((resolve, reject) => {
        // dispatch.files.updateData({
        //   // isFormDataLoading: true,
        //   fileData: {},
        // });
        if (file.ma == "EMR_HSDD015.3") {
          //Tìm component phieu-truyen-dich để check config form
          let phieuTruyenDichComp = (file.components || []).find(
            (x) => x.type == "table" && x.props.type == "phieu-truyen-dich"
          );
          if (phieuTruyenDichComp) {
            if (phieuTruyenDichComp.props?.hienThiThuocKeNgoai) {
              queries.loaiThuoc = 60;
            }
          }
        }

        editorProvider
          .getFormDataEMR({
            api: file.api,
            id,
            queries,
          })
          .then(async (value) => {
            try {
              let fileData = {};
              if (file.ma == "EMR_BA077.1" && queries.ngoaiTru == "true") {
                const fieldNames = objectKey.map((o) => o.fieldName);
                if (Array.isArray(value?.data) && value?.data.length > 0) {
                  const filteredArray = (value?.data || []).map((item) =>
                    Object.fromEntries(
                      Object.entries(item).filter(([key]) =>
                        fieldNames.includes(key)
                      )
                    )
                  );
                  fileData = combineFields({
                    ...value?.data[0],
                    dsToDieuTri: filteredArray,
                  });
                }
              } else if (file.ma === "EMR_BA077.2") {
                if (!isEmpty(value?.data) && value?.data) {
                  fileData = combineFields({
                    ...value?.data,
                    dsToDieuTri: [value?.data],
                  });
                }
              } else if (file.ma === "EMR_BA503") {
                if (Array.isArray(value?.data) && value?.data.length > 0) {
                  fileData = combineFields({
                    ...value?.data[0],
                    dsToDieuTri: value?.data,
                  });
                } else if (!isEmpty(value?.data) && value?.data) {
                  fileData = combineFields({
                    ...value?.data,
                    dsToDieuTri: [value?.data],
                  });
                }
              } else {
                //lấy dữ liệu của phiếu tại thời điểm ký
                if (
                  file?.cauHinh?.hienThiDuLieuTaiThoiDiemKy &&
                  queries.lichSuKyId
                ) {
                  const duLieuKy =
                    await danhSachPhieuChoKyProvider.getDuLieuTheoLichSuKyId({
                      lichSuKyId: queries.lichSuKyId,
                      chuKySo: queries.chuKySo,
                    });

                  if (
                    [
                      "EMR_BA077",
                      "EMR_BA077.1",
                      "EMR_BA077.2",
                      "EMR_BA503",
                      "EMR_BA503.1",
                    ].includes(file.ma)
                  ) {
                    const _dsToDieuTriIds = (
                      value?.data?.dsToDieuTri || []
                    ).map((item) => item.id);
                    fileData = combineFields({
                      ...duLieuKy?.data,
                      dsToDieuTri: (duLieuKy?.data?.dsToDieuTri || [])
                        .filter((item) => _dsToDieuTriIds.includes(item.id))
                        .map((item) => {
                          const _lichSuKy =
                            (value?.data?.dsToDieuTri || []).find(
                              (x) => x.id === item.id
                            )?.lichSuKy || null;
                          const toTieuTri = value?.data?.dsToDieuTri?.find(
                            (x) => x.id === item.id
                          );
                          debugger;
                          return {
                            ...item,
                            moTaToDieuTri: toTieuTri.moTa,
                            lichSuKy: _lichSuKy,
                          };
                        }),
                    });
                  } else {
                    fileData = combineFields({
                      ...(duLieuKy?.data ? duLieuKy.data : value?.data),
                      lichSuKy: value?.data?.lichSuKy,
                    });
                  }
                } else {
                  fileData = combineFields({
                    ...value?.data,
                    //Chỉ thêm field này trong phiếu tờ điều trị,
                    //ko thêm vào các phiếu khác tránh sinh ra dữ liệu rỗng (ví dụ như bảng kê)
                    ...(file.ma === "EMR_BA077"
                      ? {
                          dsToDieuTri: (value?.data?.dsToDieuTri || []).map(
                            (item) => {
                              return {
                                ...item,
                                moTaToDieuTri: item.moTa,
                              };
                            }
                          ),
                        }
                      : {}),
                  });
                }
              }

              if (updateState)
                dispatch.files.updateEditor(editorId, {
                  // isFormDataLoading: false,
                  fileData,
                });
              resolve(fileData);
            } catch (error) {
              console.log(error);
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            if (updateState)
              dispatch.files.updateEditor(editorId, {
                // isFormDataLoading: false,
                fileData: null,
              });
            reject(e);
          });
      });
    },

    // getDataMultiForm: (payload = {}, state) => {
    //   let userId = state.auth.auth?.id;
    //   return new Promise(async (resolve, reject) => {
    //     const files = payload.files;
    //     const patientDocument = payload.patientDocument;
    //     const allPromise = files.map((file) => {
    //       if (file)
    //         if (file.type == "emr")
    //           return new Promise(async (resolve, reject) => {
    //             const allPromise2 = [];
    //             allPromise2.push(
    //               new Promise(async (resolve, reject) => {
    //                 let configFile = await cacheUtils.read(
    //                   userId,
    //                   "EDITOR_FILE_" + file.formId,
    //                   null,
    //                   false
    //                 );
    //                 editorProvider
    //                   .getConfigForm(file.formId)
    //                   .then((s) => {
    //                     cacheUtils.save(
    //                       userId,
    //                       "EDITOR_FILE_" + file.formId,
    //                       s?.data,
    //                       false
    //                     );
    //                     if (!configFile) resolve(s.data);
    //                   })
    //                   .catch((e) => {
    //                     if (!configFile) resolve(null);
    //                   });
    //                 if (configFile) resolve(configFile);
    //               })
    //             );
    //             allPromise2.push(
    //               new Promise((resolve, reject) => {
    //                 let promise = [
    //                   editorProvider.getFormDataEMR({
    //                     api: file.api,
    //                     nbHsBaId: file.nbHoSoBaId,
    //                     patientDocument: patientDocument,
    //                   }),
    //                   editorProvider.getFormDataHIS({
    //                     api: file.api,
    //                     patientDocument: patientDocument,
    //                     recordId: file.recordId,
    //                   }),
    //                 ];
    //                 Promise.all(promise)
    //                   .then((values) => {
    //                     resolve({
    //                       fileData: combineFields(values[0]?.data),
    //                       fileDataHIS: combineFields(values[1]?.data),
    //                     });
    //                   })
    //                   .catch((e) => {
    //                     resolve({
    //                       fileData: null,
    //                       fileDataHIS: null,
    //                     });
    //                   });
    //               })
    //             );
    //             allPromise2.push(
    //               new Promise(async (resolve, reject) => {
    //                 let template = await cacheUtils.read(
    //                   userId,
    //                   "EDITOR_TEMPLATE_" + file.api,
    //                   {},
    //                   false
    //                 );
    //                 editorProvider
    //                   .getTemplateAPI({ api: file.api })
    //                   .then((s) => {
    //                     const apiFields = combineFields(s?.data);
    //                     let template2 = {
    //                       apiFields: Object.keys(apiFields),
    //                       fileTemplate: s?.data,
    //                     };
    //                     cacheUtils.save(
    //                       userId,
    //                       "EDITOR_TEMPLATE_" + file.api,
    //                       template2,
    //                       false
    //                     );
    //                     if (!template) {
    //                       resolve(template2);
    //                     }
    //                   })
    //                   .catch((e) => {
    //                     if (!template) resolve(null);
    //                   });
    //                 if (template) {
    //                   resolve(template);
    //                 }
    //               })
    //             );
    //             Promise.all(allPromise2)
    //               .then((s) => {
    //                 resolve({
    //                   file,
    //                   config: s[0],
    //                   data: s[1],
    //                   template: s[2],
    //                 });
    //               })
    //               .catch((e) => {
    //                 resolve({
    //                   file,
    //                 });
    //               });
    //           });
    //         else {
    //           return new Promise((resolve) => {
    //             resolve({ file });
    //           });
    //         }
    //     });
    //     Promise.all(allPromise)
    //       .then((s) => {
    //         resolve(s);
    //       })
    //       .catch((e) => {
    //         reject(e);
    //       });
    //   });
    // },

    // getConfigForm: (payload, state) => {
    //   return new Promise(async (resolve, reject) => {
    //     dispatch.files.updateData({
    //       isFileLoading: true,
    //       file: {
    //         components: [],
    //       },
    //       signStatus: {},
    //     });
    //     let userId = state.auth.auth?.id;
    //     let file = await cacheUtils.read(
    //       userId,
    //       "EDITOR_FILE_" + payload,
    //       null,
    //       false
    //     );
    //     let list = state.files.list || [];
    //     if (file) {
    //       list.push(file);
    //       dispatch.files.updateData({
    //         file,
    //         list: [...list],
    //         isFileLoading: false,
    //       });
    //     }
    //     editorProvider
    //       .getConfigForm(payload)
    //       .then((s) => {
    //         cacheUtils.save(userId, "EDITOR_FILE_" + payload, s?.data, false);
    //         list.push(s?.data);
    //         list = list.filter((item, index, self) => {
    //           return self.findIndex((item2) => item2.id === item.id) === index;
    //         });
    //         dispatch.files.updateData({
    //           isFileLoading: false,
    //           file: s?.data,
    //           list: [...list],
    //         });
    //       })
    //       .catch((e) => {
    //         message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
    //         dispatch.files.updateData({
    //           isFileLoading: false,
    //         });
    //       });
    //   });
    // },s
    onSaveFormPhieuTruyenMau: (editorId, state, payload) => {
      return new Promise(async (resolve, reject) => {
        try {
          const prosmise = [];
          payload.forEach((file) => {
            prosmise.push(editorProvider.onSaveFormPhieuTruyenMau(file));
          });
          let fileData = {
            dsPhieu: [],
          };
          let res = await Promise.all(prosmise);
          let phieuLoi = [];
          let phieuThanhCong = [];
          res.forEach((item) => {
            if (item.code !== 0) {
              phieuLoi.push(item);
            } else {
              phieuThanhCong.push(item.data);
            }
          });
          const dsPhieu =
            state.files.editorData[editorId]?.fileData.dsPhieu || [];
          let newData = dsPhieu.map((item) => {
            const phieu = phieuThanhCong.find((item2) => {
              return item2.id === item.id;
            });

            //xử lý giữ lại số phiếu
            if (item.soPhieu && phieu.soPhieu != item.soPhieu) {
              phieu.soPhieu = item.soPhieu;
            }
            return phieu || item;
          });
          fileData.dsPhieu = newData;
          message.success(t("common.capNhatThanhCong"));
          dispatch.files.updateEditor(editorId, {
            isSaveFormLoading: false,
            fileData: combineFields(fileData),
          });
          if (phieuLoi.length) {
            message.error(`${phieuLoi.map((item) => item.message).join(",")}`);
          }
          resolve(newData);
        } catch (error) {
          console.log(error);
        }
      });
    },
    onSaveForm: (editorId, state, payload) => {
      return new Promise(async (resolve, reject) => {
        const { file, data, noUpdateRedux = false } = payload || {};
        if (!noUpdateRedux) {
          dispatch.files.updateEditor(editorId, { isSaveFormLoading: true });
        }
        await new Promise((resolve) => setTimeout(resolve, 1000));
        editorProvider
          .onSaveForm({ file, data })
          .then((s) => {
            message.success(s?.message || t("common.capNhatThanhCong"));
            if (!noUpdateRedux) {
              // dispatch.documents.getFiles({ maHoSo: payload.patientDocument });
              // sử dụng key để update lại form nếu cần
              const uniqKey = stringUtils.guid();

              dispatch.files.updateEditor(editorId, {
                // savedId: s?.data?.id, //lưu lại id phiếu vừa lưu
                isSaveFormLoading: false,
                fileData: { ...combineFields(s?.data), uniqKey },
              });
            }
            resolve(s?.data);
          })
          .catch((e) => {
            if (!noUpdateRedux) {
              dispatch.files.updateEditor(editorId, {
                isSaveFormLoading: false,
              });
            }
            message.error(e?.message || t("common.capNhatKhongThanhCong"));
            reject(e);
          });
      });
    },

    // getAllCriterials: ({ api, ...payload }, state) => {
    //   return new Promise((resolve, reject) => {
    //     if (!api) {
    //       message.error("Chưa khai báo api danh sách tiêu chí");
    //       return;
    //     }
    //     dispatch.files.updateData({
    //       isLoadingCriterial: true,
    //       criterials: [],
    //     });
    //     editorProvider
    //       .getAllCriterials({ api, ...payload })
    //       .then((s) => {
    //         dispatch.files.updateData({
    //           isLoadingCriterial: false,
    //           criterials: s?.data || [],
    //         });
    //       })
    //       .catch((e) => {
    //         dispatch.files.updateData({
    //           isLoadingCriterial: false,
    //         });
    //       });
    //   });
    // },
    updateFileSignStatus: (
      editorId,
      state,
      { maHoSo, formId, nbHoSoBaId, trangThai }
    ) => {
      return new Promise((resolve, reject) => {
        editorProvider
          .updateFileSignStatus({ formId, nbHoSoBaId, trangThai })
          .then((s) => {
            dispatch.documents.getFiles(editorId, { maHoSo });
          })
          .catch((e) => {
            message.error(e.message || t("editor.khongTheCapNhatTrangThaiKy"));
          });
      });
    },
    setSignStatus: (
      editorId,
      state,
      {
        componentId,
        block = false,
        levelSign,
        chuKy,
        currentLevel,
        props,
        ...payload
      }
    ) => {
      return new Promise((resolve, reject) => {
        if (state.application.viewMultipleFile) return;
        if (timeout != null) {
          clearTimeout(timeout);
        }

        let status2 = {};
        status2.block = block;
        status2.levelSign = levelSign;
        status2.currentLevel = currentLevel;
        status2.chuKy = chuKy;
        if (!window.signStatus) {
          window.signStatus = {};
        }
        if (!window.signStatus[editorId]) {
          window.signStatus[editorId] = {};
        }
        if (!window.signStatus[editorId][componentId]) {
          window.signStatus[editorId][componentId] = {};
        }
        window.signStatus[editorId][componentId] = status2;
        timeout = setTimeout(
          (signStatus = {}) => {
            dispatch.files.updateEditor(editorId, {
              signStatus: { ...window.signStatus[editorId] },
            });
            timeout = null;
          },
          500,
          signStatus
        );
        resolve();
      });
    },

    getTemplateBieuMau: (editorId, state, payload) => {
      return new Promise(async (resolve, reject) => {
        editorProvider
          .getTemplateBieuMau(payload)
          .then((s) => {
            if (payload.id) {
              const _fileDataTemplate = s.data.find(
                (item) => item.id == payload.id
              );

              dispatch.files.updateEditor(editorId, {
                templateBieuMau: s.data,
                fileDataTemplate: _fileDataTemplate
                  ? combineFields(_fileDataTemplate)
                  : _fileDataTemplate,
              });
            } else {
              dispatch.files.updateEditor(editorId, {
                templateBieuMau: s.data,
              });
              if (!s.data?.length) {
                reject(s);
                return;
              }
            }
            resolve(s);
          })
          .catch((e) => {
            message.error(e?.message);
            reject(e);
          });
      });
    },
    createTemplateBieuMau: (editorId, state, payload) => {
      return new Promise(async (resolve, reject) => {
        editorProvider
          .createTemplateBieuMau(payload)
          .then((s) => {
            dispatch.files.updateEditor(editorId, {
              tenplateBieuMau: s,
            });
            message.success(t("editor.taoDuLieuMauMauThanhCong"));
            resolve(s);
          })
          .catch((e) => {
            message.error(e?.message);
            reject(e);
          });
      });
    },
    updateTemplateBieuMau: (editorId, state, payload) => {
      return new Promise(async (resolve, reject) => {
        editorProvider
          .updateTemplateBieuMau(payload)
          .then((s) => {
            message.success(t("common.capNhatThanhCong"));
            resolve(s);
          })
          .catch((e) => {
            message.error(e?.message || t("common.capNhatKhongThanhCong"));
            reject(e);
          });
      });
    },
    deleteTemplateBieuMau: (editorId, state, payload) => {
      return new Promise(async (resolve, reject) => {
        editorProvider
          .deleteTemplateBieuMau(payload)
          .then((s) => {
            const templateBieuMau = (
              state.files.editorData[editorId]?.templateBieuMau || []
            ).filter((item) => item.id !== payload.id);
            dispatch.files.updateEditor(editorId, {
              templateBieuMau,
            });
            message.success(t("common.xoaDuLieuThanhCong"));
            resolve(s);
          })
          .catch((e) => {
            message.error(e?.message);
            reject(e);
          });
      });
    },
    getDataMultiForm: async (editorId, state, { files }) => {
      try {
        const promise = files.map(({ maBaoCao, id, ...payload }) => {
          return dispatch.files.getBaoCaoByMaBaoCao(editorId, {
            maBaoCao: maBaoCao,
            id: id,
            isGetMultiFile: true,
            queries: payload,
          });
        });
        const data = await Promise.all(promise);
        const newData = data.map((item, index) => ({
          ...item,
          key: files[index]?.key,
        }));
        return newData;
      } catch (error) {
        console.log("error", error);
      }
    },
    getImageSign: async (payload) => {
      return new Promise((resolve, reject) => {
        danhSachPhieuChoKyProvider
          .getImageSign(payload)
          .then((s) => {
            dispatch.files.updateData({
              thongTinKy: s,
            });
            resolve(s);
          })
          .catch((e) => {
            reject(e);
          });
      });
    },
    getInfoBaoCao: (maBaoCao) => {
      return new Promise(async (resolve, reject) => {
        try {
          const baoCao = await dmBaoCaoProvider.getMauBaoCao({
            ma: maBaoCao,
          });
          if (baoCao.code === 0) {
            resolve(baoCao.data.id);
          }
        } catch (error) {
          reject(error);
        }
      });
    },

    getListChanKy: (payload) => {
      return new Promise(async (resolve, reject) => {
        dmBaoCaoChanKyProvider
          .getById(payload)
          .then((s) => {
            resolve(s?.data);
          })
          .catch((e) => {
            resolve(null);
          });
      });
    },

    getMauBaoCaoByMa: ({ editorId, maBaoCao }) => {
      return new Promise(async (resolve, reject) => {
        try {
          const baoCao = await dmBaoCaoProvider.getMauBaoCao({
            ma: maBaoCao,
          });
          if (baoCao.code === 0) {
            if (baoCao?.data?.id) {
              const chanKyBaoCao = await dispatch.files.getListChanKy(
                baoCao.data.id
              );
              dispatch.files.updateEditor(editorId, {
                chanKyBaoCao,
                file: baoCao?.data,
              });
            }
            dispatch.files.updateData({
              currentBaoCao: baoCao.data,
            });
            resolve(baoCao.data);
          } else {
            reject();
          }
        } catch (error) {
          reject(error);
        }
      });
    },

    deletePhieuEditor: (payload) => {
      return new Promise(async (resolve, reject) => {
        const { api, id } = payload || {};

        editorProvider
          .onDeleteForm({ api, id })
          .then((s) => {
            message.success(t("common.xoaDuLieuThanhCong"));
            resolve(s);
          })
          .catch((e) => {
            message.error(e?.message);
            reject(e);
          });
      });
    },

    getDuLieuTheoLichSuKyId: ({ lichSuKyId, ...rest }) => {
      return new Promise((resolve, reject) => {
        danhSachPhieuChoKyProvider
          .getDuLieuTheoLichSuKyId({ lichSuKyId, ...rest })
          .then((s) => {
            resolve(s?.data);
          })
          .catch((e) => {
            reject(e);
          });
      });
    },

    onSaoChepForm: ({ api, data }, state) => {
      return new Promise(async (resolve, reject) => {
        editorProvider
          .onSaoChepForm({ api, data })
          .then((s) => {
            message.success(s?.message || t("editor.saoChepPhieuThanhCong"));
            resolve(s?.data);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
  }),
};
