import nbDotDieuTriProvider from "data-access/nb-dot-dieu-tri-provider";
import nbToDieuTriProvider from "data-access/nb-to-dieu-tri-provider";
import { message } from "antd";
import { combineSort } from "utils";
import fileUtils from "utils/file-utils";
import {
  PAGE_SIZE,
  PAGE_DEFAULT,
  LOAI_DICH_VU,
  TRANG_THAI_NB,
  TRANG_THAI_TAI_KHOA,
} from "constants/index";
import { t } from "i18next";
import nbMuonNbProvider from "data-access/nb-muon-nb-provider";
import nbDvCdhaTdcnPtTtProvider from "data-access/nb-dv-cdha-tdcn-pt-tt-provider";
import nbDvXetNghiemProvider from "data-access/nb-dv-xet-nghiem-provider";
import nbDvThuocProvider from "data-access/nb-dv-thuoc-provider";
import nbDvKhamProvider from "data-access/nb-dv-kham-provider";
import nbDvVatTuProvider from "data-access/nb-dv-vat-tu-provider";
import nbDvChePhamMauProvider from "data-access/nb-dv-che-pham-mau-provider";
import { cloneDeep, uniq } from "lodash";
import nbDanhSachLichHenProvider from "data-access/nb-danh-sach-lich-hen-provider";
import { refConfirm } from "app";
import isArray from "lodash/isArray";
import nbPhieuThuProvider from "data-access/nb-phieu-thu-provider";
import moment from "moment";
import nbDvHoaChatProvider from "data-access/nb-dv-hoa-chat-provider";

const initialState = {
  listNbLapBenhAn: [],
  totalElements: null,
  page: PAGE_DEFAULT,
  size: 50,
  dataEditDefault: {},
  dataSearch: {
    tuThoiGianVaoVien: moment()
      .subtract(3, "month")
      .startOf("day")
      .format("YYYY-MM-DD HH:mm:ss"),
    denThoiGianVaoVien: moment().endOf("day").format("YYYY-MM-DD HH:mm:ss"),
    dsTrangThai: [
      TRANG_THAI_NB.CHO_TIEP_NHAN_VAO_KHOA,
      TRANG_THAI_NB.DANG_DIEU_TRI,
      TRANG_THAI_NB.DANG_CHUYEN_KHOA,
      TRANG_THAI_NB.CHO_HOAN_TAT_THU_TUC_RA_VIEN,
    ],
  },
  listData: [],
  dataSortColumn: {},
  thongTinBenhNhan: [],
  listNbTiepTheo: [],
  nbLapBenhAn: {},
  thongTinNb: {},
  listDsMuonNb: [],
  isLoading: false,
};

export const centralizeSearch = (dataSearch) => {
  const newDataSearch = cloneDeep(dataSearch);
  if (!newDataSearch.trangThaiTaiKhoa) {
    newDataSearch.dsKhoaNbId = null;
  } else if (
    newDataSearch.trangThaiTaiKhoa ==
    TRANG_THAI_TAI_KHOA.NB_CHO_TIEP_DON_THEO_HEN
  ) {
    newDataSearch.dsTrangThai = null;
  } else if (
    newDataSearch.trangThaiTaiKhoa !== TRANG_THAI_TAI_KHOA.NB_DANG_KY_PHCN
  ) {
    newDataSearch.dsTrangThaiPhcn = null;
  } else if (
    newDataSearch.trangThaiTaiKhoa == TRANG_THAI_TAI_KHOA.NB_DANG_KY_PHCN
  ) {
    newDataSearch.dsTrangThaiPhcn = [10, 30];
  }
  if (newDataSearch.dsKhoaNbId && !isArray(newDataSearch.dsKhoaNbId))
    newDataSearch.dsKhoaNbId = [newDataSearch.dsKhoaNbId];

  if (newDataSearch.trangThaiTaiKhoa === "DANG_CHUYEN_KHOA") {
    newDataSearch.trangThaiTaiKhoa = 20;
    newDataSearch.dsTrangThai = uniq([
      ...(newDataSearch.dsTrangThai || []),
      40,
    ]);
  }

  return newDataSearch;
};

export default {
  state: cloneDeep(initialState),
  reducers: {
    updateData(state, payload = {}) {
      return { ...state, ...payload };
    },
    clearData(state, payload = {}) {
      // lưu lại tuThoiGianVaoVien và denThoiGianVaoVien
      const tuThoiGianVaoVien = state.dataSearch.tuThoiGianVaoVien;
      const denThoiGianVaoVien = state.dataSearch.denThoiGianVaoVien;
      const dsTrangThai = state.dataSearch.dsTrangThai || [];
      return {
        ...cloneDeep(initialState),
        ...payload,
        dataSearch: {
          ...payload.dataSearch,
          tuThoiGianVaoVien,
          denThoiGianVaoVien,
          dsTrangThai,
        },
      };
    },
  },
  effects: (dispatch) => ({
    onSizeChange: ({ size }, state) => {
      dispatch.danhSachNguoiBenhNoiTru.updateData({
        page: 0,
        size,
      });

      dispatch.danhSachNguoiBenhNoiTru.onSearch({ size });
    },
    onSearch: ({ signal, ...payload }, state) => {
      return new Promise((resolve, reject) => {
        let newState = { isLoading: true };
        dispatch.danhSachNguoiBenhNoiTru.updateData(newState);
        const page = payload.page ?? state.danhSachNguoiBenhNoiTru.page ?? 0;
        let size = payload.size ?? state.danhSachNguoiBenhNoiTru.size ?? 50;
        const sort = combineSort(
          payload.dataSortColumn ||
            state.danhSachNguoiBenhNoiTru.dataSortColumn ||
            {}
        );
        const dataSearch =
          payload.dataSearch || state.danhSachNguoiBenhNoiTru.dataSearch || {};
        const { isInNoiTruTraDV = false, ...restDataSearch } = dataSearch || {};

        const newDataSearch = !isInNoiTruTraDV
          ? centralizeSearch(dataSearch)
          : restDataSearch;

        nbDotDieuTriProvider
          .getNbNoiTru({
            page,
            size,
            sort,
            signal,
            ...newDataSearch,
          })
          .then((s) => {
            dispatch.danhSachNguoiBenhNoiTru.updateData({
              listNbLapBenhAn: (s?.data || []).map((item, index) => {
                item.index = page * size + index + 1;
                return item;
              }),
              isLoading: false,
              totalElements: s?.totalElements || 0,
              page,
              first: s?.first,
              last: s?.last,
            });

            resolve(s?.data);
          })
          .catch((e) => {
            if (e?.code !== "ERR_CANCELED") {
              message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            }
            dispatch.danhSachNguoiBenhNoiTru.updateData({
              listData: [],
              isLoading: false,
            });
            reject(e);
          });
      });
    },
    onSortChange: ({ ...payload }, state) => {
      const dataSortColumn = {
        ...state.danhSachNguoiBenhNoiTru.dataSortColumn,
        ...payload,
      };
      dispatch.danhSachNguoiBenhNoiTru.updateData({
        page: 0,
        dataSortColumn,
      });
      dispatch.danhSachNguoiBenhNoiTru.onSearch({
        page: 0,
        dataSortColumn,
      });
    },
    onChangeInputSearch: (
      { size, page, onlyMaHoSo, onlyTenNb, signal, ...payload },
      state
    ) => {
      console.log("payload", payload);

      const newSize = size || state.danhSachNguoiBenhNoiTru.size || 50;

      const dataSearch = {
        ...(state.danhSachNguoiBenhNoiTru.dataSearch || {}),
        ...payload,
      };
      dispatch.danhSachNguoiBenhNoiTru.updateData({
        page: 0,
        size: newSize,
        dataSearch,
      });

      const obj = { ...dataSearch };
      // mục đích để ko lưu maHoSo và tenNb vào dataSearch
      if (onlyMaHoSo) obj.maHoSo = onlyMaHoSo;
      if (onlyTenNb) obj.tenNb = onlyTenNb;

      dispatch.danhSachNguoiBenhNoiTru.onSearch({
        page: 0,
        size: newSize,
        dataSearch: obj,
        signal,
      });
    },
    getNbNoiTruById: (id) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider.getNbNoiTruById(id).then((s) => {
          if (s?.code === 0) {
            dispatch.danhSachNguoiBenhNoiTru.updateData({
              chiTietNguoiBenhNoiTru: s?.data,
            });
            resolve(s?.data);
          } else {
            dispatch.danhSachNguoiBenhNoiTru.updateData({
              chiTietNguoiBenhNoiTru: {},
            });
          }
        });
      });
    },
    postLapBenhAn: (payload) => {
      const id = payload.id;
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .postLapBenhAn(id, { ...payload, id: null })
          .then((s) => {
            if (s?.code === 0) {
              resolve(s);
              message.success(t("quanLyNoiTru.lapBenhAnThanhCong"));
            } else {
              reject(s);
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    postNbToDieuTri: (payload) => {
      const id = payload.id;
      return new Promise((resolve, reject) => {
        nbToDieuTriProvider
          .postNbToDieuTri(payload)
          .then((s) => {
            if (s?.code === 0) {
              resolve(s);
              message.success(t("quyetToanBhyt.taoThanhCong"));
            } else {
              reject(s);
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    tiepNhanVaoKhoa: (payload) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .tiepNhanVaoKhoa(payload)
          .then((s) => {
            if (s?.code === 0) {
              resolve(s);
            } else {
              reject(s);
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    tuChoiVaoKhoa: (payload) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .tuChoiVaoKhoa(payload)
          .then((s) => {
            if (s?.code === 0) {
              resolve(s);
            } else {
              reject(s);
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    huyTiepNhanVaoKhoa: (payload) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .huyTiepNhanVaoKhoa(payload)
          .then((s) => {
            if (s?.code === 0) {
              resolve(s);
            } else {
              reject(s);
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },

    choVaoVienLai: ({ trangThai, ...payload }) => {
      return new Promise((resolve, reject) => {
        const api =
          trangThai == TRANG_THAI_NB.CHO_HOAN_TAT_THU_TUC_RA_VIEN
            ? nbDotDieuTriProvider.huyDuKienRaVien
            : nbDotDieuTriProvider.choVaoVienLai;

        api(payload)
          .then((s) => {
            if (s?.code === 0) {
              message.success(t("quanLyNoiTru.choVaoVienLaiThanhCong"));
              resolve(s);
            } else {
              reject(s);
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    ketThucDieuTri: (payload, state, options = {}) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .ketThucDieuTri(payload)
          .then((s) => {
            message.success(t("quanLyNoiTru.ketThucDieuTriThanhCong"));
            resolve(s);
          })
          .catch((e) => {
            if (e?.code === 7987) {
              message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
              reject({ ...payload, showKiemTraHs: true });
              return;
            }

            if (options?.showPopupErr) {
              refConfirm.current &&
                refConfirm.current.show({
                  title: t("common.canhBao"),
                  content: `${
                    e?.message || t("common.xayRaLoiVuiLongThuLaiSau")
                  }`,
                  okText: t("common.dong"),
                  classNameOkText: "button-error",
                  showBtnOk: true,
                  showBtnCancel: false,
                  typeModal: "error",
                });
            } else if (e?.code !== 1024) {
              message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            }
            reject(e);
          });
      });
    },
    duKienRaVien: (payload) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .duKienRaVien(payload)
          .then((s) => {
            if (s?.code === 0) {
              resolve(s);
              message.success(s?.message || t("common.capNhatThanhCong"));
            } else {
              reject(s);
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    nbMuonNb: (payload) => {
      return new Promise((resolve, reject) => {
        nbMuonNbProvider
          .post(payload)
          .then((s) => {
            if (s?.code === 0) {
              resolve(s);
              message.success(s?.message || t("common.capNhatThanhCong"));
            } else {
              reject(s);
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    getDanhSachMuonNb: ({ ...payload }) => {
      nbMuonNbProvider
        .search(payload)
        .then((s) => {
          if (s?.code === 0) {
            if (Array.isArray(s?.data)) {
              dispatch.danhSachNguoiBenhNoiTru.updateData({
                listDsMuonNb: s?.data,
              });
            } else {
              dispatch.danhSachNguoiBenhNoiTru.updateData({
                nbDaChuyenKhoa: s?.data,
              });
            }
          } else {
            message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          }
        })
        .catch((e) => {
          message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
        });
    },
    duyetYeuCauMuonNb: (payload) => {
      return new Promise((resolve, reject) => {
        nbMuonNbProvider
          .duyetYeuCauMuonNb(payload)
          .then((s) => {
            if (s?.code === 0) {
              resolve(s);
              message.success(s?.message || t("common.capNhatThanhCong"));
            } else {
              reject(s);
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    ngatDieuTri: (payload) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .ngatDieuTri(payload)
          .then((s) => {
            if (s?.code === 0) {
              resolve(s);
            } else {
              reject(s);
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            }
          })
          .catch((e) => {
            if (e?.code === 7987) {
              reject({ ...payload, showKiemTraHs: true });
              return;
            }
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    deleteDichVu: (payload) => {
      return new Promise(async (resolve, reject) => {
        try {
          let resAll = [];

          const dsKham = payload.filter(
            (x) => x.loaiDichVu === LOAI_DICH_VU.KHAM
          );

          if (dsKham && dsKham.length > 0) {
            const resKham = await nbDvKhamProvider.onDeleteDichVu({
              listDeletingId: dsKham.map((x) => x.id),
            });

            resAll = [...resAll, ...resKham.data];
          }

          const dsCdhaTdcnPtTt = payload.filter(
            (x) =>
              x.loaiDichVu === LOAI_DICH_VU.CDHA ||
              x.loaiDichVu === LOAI_DICH_VU.PHAU_THUAT_THU_THUAT
          );

          if (dsCdhaTdcnPtTt && dsCdhaTdcnPtTt.length > 0) {
            const resCdha = await nbDvCdhaTdcnPtTtProvider.onDeleteDichVu({
              listDeletingId: dsCdhaTdcnPtTt.map((x) => x.id),
            });

            resAll = [...resAll, ...resCdha.data];
          }

          const dsXetNghiem = payload.filter(
            (x) => x.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM
          );

          if (dsXetNghiem && dsXetNghiem.length > 0) {
            const resXetNghiem = await nbDvXetNghiemProvider.onDeleteDichVu({
              listDeletingId: dsXetNghiem.map((x) => x.id),
            });
            resAll = [...resAll, ...resXetNghiem.data];
          }

          const dsThuoc = payload.filter(
            (x) => x.loaiDichVu === LOAI_DICH_VU.THUOC
          );

          if (dsThuoc && dsThuoc.length > 0) {
            const resThuoc = await nbDvThuocProvider.onDeleteMultiDichVu({
              listDeletingId: dsThuoc.map((x) => x.id),
            });
            resAll = [...resAll, ...resThuoc.data];
          }

          const dsVatTu = payload.filter(
            (x) => x.loaiDichVu === LOAI_DICH_VU.VAT_TU
          );

          if (dsVatTu && dsVatTu.length > 0) {
            const resVatTu = await nbDvVatTuProvider.onDeleteMultiDichVu({
              listDeletingId: dsVatTu.map((x) => x.id),
            });
            resAll = [...resAll, ...resVatTu.data];
          }

          const dsMau = payload.filter(
            (x) => x.loaiDichVu === LOAI_DICH_VU.CHE_PHAM_MAU
          );

          if (dsMau && dsMau.length > 0) {
            const resMau = await nbDvChePhamMauProvider.deleteMultiple(
              dsMau.map((x) => x.id)
            );
            resAll = [...resAll, ...resMau.data];
          }

          if (resAll.every((x) => x.code == 0)) {
            resolve();
          } else {
            reject(resAll);
          }
        } catch (e) {
          message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          reject();
        }
      });
    },

    traDichVu: (payload) => {
      return new Promise(async (resolve, reject) => {
        try {
          let resAll = [];

          const dsThuoc = payload.filter(
            (x) => x.loaiDichVu === LOAI_DICH_VU.THUOC
          );

          if (dsThuoc && dsThuoc.length > 0) {
            const resThuoc = await nbDvThuocProvider.deleteDsDvThuocTraKho(
              dsThuoc.map((x) => x.id)
            );
            resAll = [...resAll, ...(resThuoc.data || [])];
          }

          const dsVatTu = payload.filter(
            (x) => x.loaiDichVu === LOAI_DICH_VU.VAT_TU
          );

          if (dsVatTu && dsVatTu.length > 0) {
            const resVatTu = await nbDvVatTuProvider.deleteDsDvVatTuTraKho(
              dsVatTu.map((x) => x.id)
            );
            resAll = [...resAll, ...(resVatTu.data || [])];
          }

          if (resAll.every((x) => x.code == 0)) {
            resolve();
          } else {
            reject(resAll);
          }
        } catch (e) {
          message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          reject();
        }
      });
    },

    deleteToDieuTri: (id) => {
      return new Promise(async (resolve, reject) => {
        nbToDieuTriProvider
          .delete(id)
          .then((s) => {
            if (s?.code === 0) {
              message.success(t("quanLyNoiTru.xoaThanhCongToDieuTri"));
              resolve(s);
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject();
          });
      });
    },

    themThongTin: (payload) => {
      return new Promise(async (resolve, reject) => {
        try {
          let resAll = [];

          const dsKham = payload.filter(
            (x) => x.loaiDichVu === LOAI_DICH_VU.KHAM
          );

          if (dsKham && dsKham.length > 0) {
            const resKham = await nbDvKhamProvider.themThongTin(
              dsKham.map((item) => ({
                id: item?.id,
                nbDichVu: {
                  mucDichId: item.mucDichId,
                  thoiGianThucHien: item.thoiGianThucHien,
                },
              }))
            );
            resAll = [...resAll, { ...resKham }];
          }

          const dsCdhaTdcnPtTt = payload.filter(
            (x) =>
              x.loaiDichVu === LOAI_DICH_VU.CDHA ||
              x.loaiDichVu === LOAI_DICH_VU.PHAU_THUAT_THU_THUAT
          );

          if (dsCdhaTdcnPtTt && dsCdhaTdcnPtTt.length > 0) {
            const resCdha = await nbDvCdhaTdcnPtTtProvider.themThongTin(
              dsCdhaTdcnPtTt.map((item) => ({
                id: item?.id,
                nbDichVu: {
                  mucDichId: item.mucDichId,
                  thoiGianThucHien: item.thoiGianThucHien,
                },
              }))
            );
            resAll = [...resAll, { ...resCdha }];
          }

          const dsXetNghiem = payload.filter(
            (x) => x.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM
          );

          if (dsXetNghiem && dsXetNghiem.length > 0) {
            const resXetNghiem = await nbDvXetNghiemProvider.themThongTin(
              dsXetNghiem.map((item) => ({
                id: item?.id,
                nbDichVu: {
                  mucDichId: item.mucDichId,
                  thoiGianThucHien: item.thoiGianThucHien,
                },
              }))
            );
            resAll = [...resAll, { ...resXetNghiem }];
          }

          const dsThuoc = payload.filter(
            (x) => x.loaiDichVu === LOAI_DICH_VU.THUOC
          );

          if (dsThuoc && dsThuoc.length > 0) {
            const resThuoc = await nbDvThuocProvider.themThongTin(
              dsThuoc.map((item) => ({
                id: item?.id,
                nbDichVu: {
                  mucDichId: item.mucDichId,
                  thoiGianThucHien: item.thoiGianThucHien,
                },
              }))
            );
            resAll = [...resAll, { ...resThuoc }];
          }

          const dsVatTu = payload.filter(
            (x) => x.loaiDichVu === LOAI_DICH_VU.VAT_TU
          );

          if (dsVatTu && dsVatTu.length > 0) {
            const resVatTu = await nbDvVatTuProvider.themThongTin(
              dsVatTu.map((item) => ({
                id: item?.id,
                nbDichVu: {
                  mucDichId: item.mucDichId,
                  thoiGianThucHien: item.thoiGianThucHien,
                },
              }))
            );
            resAll = [...resAll, { ...resVatTu }];
          }

          if (resAll.every((x) => x.code == 0)) {
            resolve();
          } else {
            reject(resAll);
          }
        } catch (e) {
          message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          reject();
        }
      });
    },

    getDsDv: (payload) => {
      return new Promise((resolve, reject) => {
        nbDvThuocProvider
          .searchTongHop({ ...payload, size: "" })
          .then((s) => {
            if (s?.code === 0) {
              let data = s?.data || [];
              dispatch.chiDinhDichVuThuoc.updateData({
                listDvThuoc: data,
              });
              resolve(s);
            } else {
              reject(s);
              message.error(s?.message);
            }
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },

    deleteDsDvThuocPhieuLinh: (payload) => {
      return new Promise((resolve, reject) => {
        nbDvThuocProvider
          .deleteDsDvThuocPhieuLinh(payload)
          .then((s) => {
            if (s?.code === 0) {
              resolve(s);
              message.success(s?.message || t("common.xoaDuLieuThanhCong"));
            } else {
              reject(s);
              message.error(s?.message);
            }
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    deleteDsDvVatTuPhieuLinh: (payload) => {
      return new Promise((resolve, reject) => {
        nbDvVatTuProvider
          .deleteDsDvVatTuPhieuLinh(payload)
          .then((s) => {
            if (s?.code === 0) {
              resolve(s);
              message.success(s?.message || t("common.xoaDuLieuThanhCong"));
            } else {
              reject(s);
              message.error(s?.message);
            }
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    deleteDsDvHoaChatPhieuLinh: (payload) => {
      return new Promise((resolve, reject) => {
        nbDvHoaChatProvider
          .deleteDsDvHoaChatPhieuLinh(payload)
          .then((s) => {
            if (s?.code === 0) {
              resolve(s);
              message.success(s?.message || t("common.xoaDuLieuThanhCong"));
            } else {
              reject(s);
              message.error(s?.message);
            }
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },

    updateThoiGianYLenhToDieuTri: (payload) => {
      return new Promise(async (resolve, reject) => {
        try {
          const resAll = await Promise.all(
            (payload || []).map(async (toDieuTri) => {
              return await nbToDieuTriProvider.patch({
                id: toDieuTri.id,
                thoiGianYLenh: toDieuTri.thoiGianYLenh,
              });
            })
          );

          if (resAll.every((x) => x.code == 0)) {
            resolve();
          } else {
            reject(resAll);
          }
        } catch (e) {
          message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          reject();
        }
      });
    },

    tiepDonHenDieuTri: (payload) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .tiepDonTheoHen(payload)
          .then((s) => {
            resolve(s);
            message.success(
              s?.message || t("quanLyNoiTru.tiepDonHenDieuTriThanhCong")
            );
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },

    getDsPhieuThuByMaHS: (payload) => {
      return new Promise((resolve, reject) => {
        nbPhieuThuProvider
          .searchTongHop({ ...payload, page: "", size: "" })
          .then((s) => {
            resolve(s?.data || []);
          })
          .catch((e) => {
            reject(e);
          });
      });
    },
    guiDuyetChiPhi: (payload) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .guiDuyetChiPhi(payload)
          .then((s) => {
            if (s?.code === 0) {
              resolve(s);
              message.success(t("quanLyNoiTru.guiDuyetChiPhiThanhCong"));
            } else {
              reject(s);
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    duyetChiPhi: (nbDotDieuTriId) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .duyetChiPhi(nbDotDieuTriId)
          .then((s) => {
            if (s?.code === 0) {
              resolve(s);
              message.success(t("thuNgan.duyetChiPhiThanhCong"));
            } else {
              reject(s);
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    tuChoiDuyetChiPhi: (payload) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .tuChoiDuyetChiPhi(payload)
          .then((s) => {
            if (s?.code === 0) {
              resolve(s);
              message.success(t("thuNgan.tuChoiDuyetChiPhiThanhCong"));
            } else {
              reject(s);
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    huyDuyetChiPhi: (nbDotDieuTriId) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .huyDuyetChiPhi(nbDotDieuTriId)
          .then((s) => {
            if (s?.code === 0) {
              resolve(s);
              message.success(t("thuNgan.huyDuyetChiPhiThanhCong"));
            } else {
              reject(s);
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    henDieuTriNgoaiTru: (payload) => {
      return new Promise((resolve, reject) => {
        nbDanhSachLichHenProvider
          .henDieuTriNgoaiTru(payload)
          .then((s) => {
            if (s?.code === 0) {
              message.success(t("quanLyNoiTru.henDieuTriNgoaiTruThanhCong"));
              resolve(s);
            } else {
              reject(s);
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    exportExcel: (payload, state) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .exportExcel(payload)
          .then((s) => {
            if (s?.code == 0 && s?.data) {
              fileUtils.downloadFile(
                s?.data?.file?.doc,
                `${t("quanLyNoiTru.danhSachNguoiBenhNoiTru")}.xlsx`
              );
              resolve(s);
            } else {
              reject(s);
            }
          })
          .catch((e) => {
            reject(e);
          });
      });
    },
    huyTiepDonTheoHen: (payload) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .huyTiepDonTheoHen(payload)
          .then((s) => {
            if (s?.code === 0) {
              message.success(t("quanLyNoiTru.huyTiepDonTheoHenThanhCong"));
              resolve(s);
            } else {
              reject(s);
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
  }),
};
