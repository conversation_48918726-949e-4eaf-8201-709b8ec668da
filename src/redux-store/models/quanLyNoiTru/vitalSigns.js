import { cloneDeep } from "lodash";
import cacheUtils from "lib-utils/cache-utils";
import { t } from "i18next";
import { message } from "antd";
import moment from "moment";

import { formatName, splitName } from "utils/vital-signs/helpers";
import nbChiSoSongProvider from "data-access/nb-chi-so-song-provider";
import { client, dataPath } from "client/request";
import { NB_CHI_SO_SONG, NB_DOT_DIEU_TRI } from "client/api";
import { CACHE_KEY, LOAI_DICH_VU } from "constants/index";
import { combineUrlParams, isArray, isNumber } from "utils/index";

const convertDataCreate = (item, state, payload, configData) => {
  const huyetap = ((item.huyetap ?? "") + "").split("/");
  const nhipTho = ((item.nhipTho ?? "") + "").split("/");
  const data = {
    nhietDo: isNumber(item.nhietDo) ? Math.round(item.nhietDo * 10) / 10 : null,
    mach: isNumber(item.mach) ? Math.round(item.mach) : null,
    canNang: item.canNang ?? null,
    huyetApTamThu: huyetap[0] ?? null,
    huyetApTamTruong: huyetap[1] ?? null,
    nhipTho: nhipTho[0] ?? null,
    nguoiThucHienId: state.auth.auth?.nhanVienId || "",
    thoiGianThucHien: item.thoiGianThucHien,
    nbDotDieuTriId: payload.nbDotDieuTriId,
    dsChiSoSongKhac: item.dsChiSoSongKhac || [],
    ptTtId: null,
    spo2: item.spo2 ?? null,
    nhomMau: item.nhomMau || null,
    chieuCao: item.chieuCao ?? null,
    troTho: item.troTho,
    chiDinhTuDichVuId: payload.tiepDon ? null : configData?.chiDinhTuDichVuId,
    chiDinhTuLoaiDichVu: payload.tiepDon
      ? LOAI_DICH_VU.TIEP_DON
      : configData?.chiDinhTuLoaiDichVu,
    khoaChiDinhId: item.khoaChiDinhId,
  };
  return data;
};

const sortValuesIds = (data, vitalSignsCategories) => {
  const idsWithStt = data.filter((id) => {
    const category = vitalSignsCategories.find((cat) => cat.id === id);
    return category?.stt !== undefined && category?.stt !== null;
  });
  const idsWithoutStt = data.filter((id) => {
    const category = vitalSignsCategories.find((cat) => cat.id === id);
    return category?.stt === undefined || category?.stt === null;
  });
  idsWithStt.sort((a, b) => {
    const categoryA = vitalSignsCategories.find((cat) => cat.id === a);
    const categoryB = vitalSignsCategories.find((cat) => cat.id === b);
    return categoryA.stt - categoryB.stt;
  });
  idsWithoutStt.sort((a, b) => data.indexOf(a) - data.indexOf(b));
  return [...idsWithStt, ...idsWithoutStt];
};

export default {
  state: {
    moreValueIds: [],
    configData: {},
    actionLoading: false,
    dataSearch: {
      all: true,
      tuThoiGian: null,
      denThoiGian: null,
    },
  },
  reducers: {
    updateData(state, payload = {}) {
      return { ...state, ...payload };
    },
  },
  effects: (dispatch) => ({
    getDataVitalSigns: async (
      { nbDotDieuTriId, khoaChiDinhId, isLog, refreshData = false, ...payload },
      state
    ) => {
      return new Promise((resolve, reject) => {
        dispatch.vitalSigns.updateData({
          ...(!refreshData && { isLoading: true }),
          nbDotDieuTriId,
          refreshData,
        });
        let configData = state.vitalSigns.configData;
        nbChiSoSongProvider
          .getDataVitalSigns({
            nbDotDieuTriId,
            chiDinhTuDichVuId: null,
            dsChiDinhTuLoaiDichVu: configData?.dsChiDinhTuLoaiDichVu,
            khoaChiDinhId: configData?.hienThiTheoKhoa ? khoaChiDinhId : null,
            page: 0,
            size: 500,
            ...payload,
          })
          .then(async (s) => {
            let data = s?.data || [];
            data.sort(
              (a, b) =>
                new Date(a?.thoiGianThucHien).getTime() -
                new Date(b?.thoiGianThucHien).getTime()
            );
            let values = [];
            let moreValueIds = [];
            let vitalSignsCategories = state.chiSoSong.listAllChiSoSong || [];

            const isNoiTru = (configData?.dsChiDinhTuLoaiDichVu || []).includes(
              LOAI_DICH_VU.NOI_TRU
            );

            if (isNoiTru) {
              vitalSignsCategories = vitalSignsCategories.filter(
                (item) => !item.macDinh
              );
            }
            data.map((item) => {
              return (moreValueIds = [
                ...moreValueIds,
                ...(item.dsChiSoSongKhac || []).filter((t) => {
                  if (vitalSignsCategories && vitalSignsCategories.length) {
                    if (
                      !vitalSignsCategories.find((y) => y.id === t.chiSoSongId)
                    )
                      return false;
                  }
                  return t.giaTri !== "" && t.giaTri !== undefined;
                }),
              ]);
            });
            moreValueIds = moreValueIds
              .map((item) => item.chiSoSongId)
              .filter((item, index, self) => {
                return self.indexOf(item) === index;
              });

            // Sort moreValueIds theo stt bên trong vitalSignsCategories
            moreValueIds = sortValuesIds(moreValueIds, vitalSignsCategories);
            // const currentTime = new Date();
            values = (data || []).map((item = {}, index) => {
              let item2 = {
                ...item,
                thoiGianThucHien:
                  item.thoiGianThucHien && new Date(item.thoiGianThucHien),
                huyetap: !isNumber(item.huyetApTamThu)
                  ? ""
                  : item.huyetApTamThu +
                  (isNumber(item.huyetApTamTruong)
                    ? "/" + item.huyetApTamTruong
                    : ""),
                nhipTho: item.nhipTho,
                tenNguoiThucHien: formatName(item.tenNguoiThucHien || ""),
                ...splitName(item.tenNguoiThucHien || ""),
                isLoading: false,
              };
              return item2;
            });

            if (!isLog) {
              let obj = {
                thoiGianThucHien: null,
                huyetap: null,
                canNang: null,
                nhipTho: null,
                dsChiSoSongKhac: [],
                khoaChiDinhId: khoaChiDinhId || null,
              };
              values.push(obj);
            }
            const newValues = await dispatch.vitalSigns.convertData({ values });
            dispatch.vitalSigns.updateData({
              resState: data,
              values: newValues,
              moreValueIds: moreValueIds || [],
              typeValue: 1,
              isLoading: false,
              currentCol: refreshData ? newValues.length - 1 : -1,
              refreshData: false,
            });
            resolve(newValues);
          })
          .catch((e) => {
            dispatch.vitalSigns.updateData({
              isLoading: false,
              refreshData: false,
            });
            reject(e);
            throw e;
          });
      });
    },

    getAllVitalSignsCategory: async () => {
      let cache = await cacheUtils.read(
        "",
        CACHE_KEY.DATA_CATEGORIES_VITALSIGNS,
        [],
        false
      );
      dispatch.vitalSigns.updateData({
        vitalSignsCategories: cache || [],
      });
      nbChiSoSongProvider
        .searchCategory({ size: 1000, active: true })
        .then((s) => {
          let data = (s.data || []).map((item) => ({
            ten: item.ten,
            id: item.id,
            donVi: item.donVi,
            giaTriLonNhat: item.giaTriLonNhat,
            giaTriNhoNhat: item.giaTriNhoNhat,
          }));
          cacheUtils.save(
            null,
            CACHE_KEY.DATA_CATEGORIES_VITALSIGNS,
            data,
            false
          );
          dispatch.vitalSigns.updateData({
            vitalSignsCategories: data,
          });
        });
    },
    onCreateOrEditCategory: async ({ isSaveToDieuTri, ...payload }, state) => {
      if (payload.id) {
        nbChiSoSongProvider
          .onUpdate(payload)
          .then((s) => {
            if (isSaveToDieuTri) {
              const values = state.vitalSigns.values.map((item) => {
                if (item.id === s.data.id) {
                  return s.data;
                } else {
                  return item;
                }
              });
              dispatch.vitalSigns.updateData({
                values: values,
              });
            }

            const categories = (state.vitalSigns.categories || []).map(
              (item) => {
                if (item.id == s.data?.id) return s.data;
                return item;
              }
            );
          })
          .catch((e) => {
            message.error(
              e?.message || t("quanLyNoiTru.capNhatThongTinChiSoKhongThanhCong")
            );
          });
      } else {
        nbChiSoSongProvider
          .onCreate(payload)
          .then((s) => {
            if (isSaveToDieuTri) {
              const values = [...(state.vitalSigns.values || []), s.data];
              dispatch.vitalSigns.updateData({
                values: values,
              });
            }
          })
          .catch((e) => {
            message.error(
              e?.message || t("quanLyNoiTru.taoThongTinChiSoKhongThanhCong")
            );
          });
      }
    },
    onDeleteCategory: async (payload) => {
      nbChiSoSongProvider
        .onDeleteCategory(payload)
        .then((s) => {
          message.success(
            s?.message || t("quanLyNoiTru.xoaChiSoSongThanhCong")
          );
        })
        .catch((e) => {
          message.error(
            e?.message || t("quanLyNoiTru.xoaChiSoSongKhongThanhCong")
          );
        });
    },
    onUpdate: ({ ...payload } = {}, state) => {
      return new Promise(async (resolve, reject) => {
        dispatch.vitalSigns.updateData({
          isLoading: true,
          actionLoading: true,
        });
        const values = state.vitalSigns.values.map((item) => {
          if (item?.auToAddMach) {
            item.mach = null;
          }
          if (item?.auToAddNhietDo) {
            item.nhietDo = null;
          }
          return item;
        });
        let index =
          typeof payload.currentCol === "number"
            ? payload.currentCol
            : state.vitalSigns.currentCol;
        let huyetap = (values[index].huyetap || "").split("/");
        let id = state.vitalSigns.resState[index]?.id;
        const splitValues = values[index].nhipTho
          ? (values[index].nhipTho + "").split("/")
          : [];
        let nhietDo = values[index].nhietDo
          ? Math.round(values[index].nhietDo * 10) / 10
          : null;
        let mach = values[index].mach ? Math.round(values[index].mach) : null;
        if (state.vitalSigns.isDeleting) {
          if (state.vitalSigns.typeValue === 1) {
            mach = 0;
            values[index].mach = 0;
          }
          if (state.vitalSigns.typeValue === 2) {
            nhietDo = 0;
            values[index].nhietDo = 0;
          }
        }

        const _payload = {
          ...values[index],
          id,
          nhietDo,
          mach,
          canNang: values[index].canNang,
          huyetApTamTruong: huyetap[1] || null,
          huyetApTamThu: huyetap[0] || null,
          nhipTho: splitValues[0] || null,
          // bopBong: !!(splitValues[1] || ""),
          thoiGianThucHien: values[index].thoiGianThucHien,
          nguoiThucHienId: state.auth.auth?.nhanVienId || "",
          dsChiSoSongKhac: values[index].dsChiSoSongKhac || [],
        };

        delete payload.bmiVct;

        const _thoiGianThucHienPayload = moment(
          _payload.thoiGianThucHien
        ).format("DD/MM/YYYY HH:mm:ss");
        const _filterArr = values.filter(
          (x) =>
            moment(x.thoiGianThucHien).format("DD/MM/YYYY HH:mm:ss") ===
            _thoiGianThucHienPayload
        );
        //kiểm tra bản ghi chỉnh sửa có thời gian đã tồn tại hay chưa
        if (_filterArr.length > 1) {
          dispatch.vitalSigns.updateData({
            isLoading: false,
            actionLoading: false,
          });
          message.error(
            t("sinhHieu.nguoiBenhDaCoSinhHieuTaiThoiDiem", {
              time: _thoiGianThucHienPayload,
            })
          );

          reject();
          return;
        }

        nbChiSoSongProvider
          .onUpdate(_payload)
          .then(async (s) => {
            const newValues = await dispatch.vitalSigns.convertData({
              values,
              payload: s?.data,
            });
            dispatch.vitalSigns.updateData({
              isLoading: false,
              values: newValues,
              actionLoading: false,
            });
            message.success(t("quyetToanBhyt.luuThanhCong"));
            resolve(newValues);
          })
          .catch((e) => {
            dispatch.vitalSigns.updateData({
              isLoading: false,
              actionLoading: false,
            });
            message.error(e?.message || t("common.luuKhongThanhCong"));
            reject();
          });
      });
    },
    onDelete: (payload, state) => {
      return new Promise(async (resolve, reject) => {
        dispatch.vitalSigns.updateData({
          isLoading: true,
          actionLoading: true,
        });
        let index = state.vitalSigns.currentCol; //lấy ra vị trí hiện tại
        let id = state.vitalSigns.resState[index]?.id; //lấy ra id của item hiện tại
        const values = cloneDeep(state.vitalSigns.values)
          .filter((item) => item.id != id)
          .map((item) => {
            if (item?.auToAddMach) {
              item.mach = null;
            }
            if (item?.auToAddNhietDo) {
              item.nhietDo = null;
            }
            return item;
          });
        const resState = cloneDeep(state.vitalSigns.resState).filter(
          (item) => item.id != id
        );
        const newValues = await dispatch.vitalSigns.convertData({ values });
        if (!id) {
          message.error(t("sinhHieu.xoaKhongThanhCongDoChuaLuuSinhHieu"));
          reject();
          return;
        }
        nbChiSoSongProvider
          .onDelete(id)
          .then((s) => {
            dispatch.vitalSigns.updateData({
              isLoading: false,
              actionLoading: false,
              values: newValues,
              resState,
            });
            message.success(t("quyetToanBhyt.luuThanhCong"));
            resolve(newValues);
          })
          .catch((e) => {
            dispatch.vitalSigns.updateData({
              isLoading: false,
              actionLoading: false,
            });
            message.error(e?.message || t("common.luuKhongThanhCong"));
            reject();
          });
      });
    },
    onCreate: async ({ listData, ...payload }, state) => {
      return new Promise((resolve, reject) => {
        dispatch.vitalSigns.updateData({
          isLoading: true,
          actionLoading: true,
        });
        let configData = state.vitalSigns.configData;

        if (isArray(listData, true)) {
          const _listData = listData.map((el) =>
            convertDataCreate(el, state, payload, configData)
          );

          nbChiSoSongProvider
            .onCreateBatch(_listData)
            .then((s) => {
              message.success(t("quyetToanBhyt.luuThanhCong"));
              dispatch.vitalSigns.updateData({
                isLoading: false,
                actionLoading: false,
              });
              resolve(s);
            })
            .catch((e) => {
              dispatch.vitalSigns.updateData({
                isLoading: false,
                actionLoading: false,
              });
              message.error(e?.message || t("common.luuKhongThanhCong"));
              reject(false);
            });
        } else {
          let values = cloneDeep(state.vitalSigns.values);
          let item = values[values.length - 1];
          const data = convertDataCreate(item, state, payload, configData);
          const validCreate = !isArray(item.dsChiSoSongKhac, true)
            ? item.huyetap ||
            item.canNang ||
            item.nhietDo ||
            item.nhipTho ||
            item.mach ||
            item.chieuCao ||
            item.spo2 ||
            item.nhomMau
            : true;

          //kiểm tra bản ghi thêm mới có thời gian thực hiện đã tồn tại hay chưa
          const _thoiGianThucHienItem = moment(item.thoiGianThucHien).format(
            "DD/MM/YYYY HH:mm:ss"
          );
          const _finxIdx = values.findIndex(
            (x) =>
              moment(x.thoiGianThucHien).format("DD/MM/YYYY HH:mm:ss") ===
              _thoiGianThucHienItem
          );
          if (_finxIdx > -1 && _finxIdx < values.length - 1) {
            dispatch.vitalSigns.updateData({
              isLoading: false,
              actionLoading: false,
            });
            message.error(
              t("sinhHieu.nguoiBenhDaCoSinhHieuTaiThoiDiem", {
                time: _thoiGianThucHienItem,
              })
            );

            reject(false);
            return;
          }

          if (validCreate) {
            nbChiSoSongProvider
              .onCreate({ ...data })
              .then(async (s) => {
                dispatch.vitalSigns
                  .getDataVitalSigns({
                    nbDotDieuTriId: payload.nbDotDieuTriId,
                  })
                  .then((res) => {
                    item.id = s?.data?.id;
                    item.nguoiThucHienId = s?.data?.nguoiThucHienId;
                    let resState = [...res];
                    values = [...res];
                    dispatch.vitalSigns.updateData({
                      isLoading: false,
                      actionLoading: false,
                      resState,
                      values,
                      currentCol: values.length - 1,
                    });
                    message.success(t("quyetToanBhyt.luuThanhCong"));
                    resolve(values);
                  })
                  .catch((e) => {
                    console.log("e", e);
                  });
              })
              .catch((e) => {
                dispatch.vitalSigns.updateData({
                  isLoading: false,
                  actionLoading: false,
                });
                message.error(e?.message || t("common.luuKhongThanhCong"));
                reject(false);
              });
          } else {
            dispatch.vitalSigns.updateData({
              isLoading: false,
              actionLoading: false,
            });
            message.error(t("common.vuiLongDienItNhatMotThongTin"));
            reject(false);
          }
        }
      });
    },
    onCancel: (payload, state) => {
      dispatch.vitalSigns.updateData({
        values: [...state.vitalSigns.preValues],
        idxColEdit: undefined,
        modeAdd: false,
        modeEdit: false,
        isSaveSucces: true,
      });
    },
    getAllDoctor: async () => {
      // let cache = await cacheUtils.read("", "DATA-DOCTORS", [], false);
      // dispatch.vitalSigns.updateData({
      //   doctors: cache || [],
      // });
      // userProvider
      //   .search({
      //     page: "0",
      //     size: 2000,
      //     active: "true",
      //     sort: "fullName",
      //     doctor: "true",
      //   })
      //   .then((s) => {
      //     let data = s.data
      //       .map((item) => ({
      //         id: item.id,
      //         username: item.username,
      //         value: item.value,
      //         fullName: item.fullName + " - " + item.value,
      //         name: item.fullName,
      //         qualificationName: item.qualification?.name,
      //         departmentId: item.departmentId,
      //         departmentName: item.department?.name,
      //       }))
      //       .filter((item, index, self) => {
      //         return self.findIndex((t) => t.id === item.id) === index;
      //       });
      //     cacheUtils.save(null, "DATA-DOCTORS", data, false);
      //     dispatch.vitalSigns.updateData({
      //       doctors: data || [],
      //     });
      //   });
    },
    onCreateSurgery: (payload, state) => {
      return new Promise((resolve, reject) => {
        nbChiSoSongProvider
          .createSurgery(payload)
          .then((s) => {
            if (s.code == 0) {
              let values = state.vitalSigns.values;
              const { vitalSignsId } = payload;
              let item = values.find((i) => i.id === vitalSignsId);
              if (item) {
                item.nbPhauThuat = s.data;
                dispatch.vitalSigns.updateData({
                  values: [...values],
                });
                message.success(
                  t("quanLyNoiTru.themThongTinPhauThuatThanhCong")
                );
                resolve(values);
              }
            } else {
              message.error(`${s.message}`);
              resolve(null);
            }
          })
          .catch((e) => {
            message.error(
              t("quanLyNoiTru.taoThongTinPhauThuatKhongThanhCongHoacDaTonTai")
            );
            reject(null);
          });
      });
    },
    onRemoveSurgery: (vitalSignsId, state) => {
      return new Promise((resolve, reject) => {
        let values = state.vitalSigns.values;
        let item = values.find((i) => i.id === vitalSignsId);
        if (item && item.nbPhauThuat) {
          nbChiSoSongProvider
            .deleteSurgery(item.nbPhauThuat.id)
            .then((s) => {
              item.nbPhauThuat = null;
              dispatch.vitalSigns.updateData({
                values: [...values],
              });
              message.success(t("pttt.xoaThanhCong"));
              resolve(values);
            })
            .catch((e) => {
              message.error(
                e?.message ||
                t("quanLyNoiTru.xoaThongTinPhauThuatKhongThanhCong"),
                "danger"
              );
              reject(e);
            });
        } else {
          message.error(t("quanLyNoiTru.khongTonTaiThongTinPhauThuat"));
          reject();
        }
      });
    },
    onUpdateSurgery: ({ id, bacSy, phuongPhapPhauThuat }) => {
      nbChiSoSongProvider
        .updateSurgery({ id, bacSy, phuongPhapPhauThuat })
        .then((s) => {
          message.success(
            s?.message || t("quanLyNoiTru.capNhatThongTinPhauThuatThanhCong")
          );
        })
        .catch((e) => {
          message.error(
            e?.message ||
            t("quanLyNoiTru.capNhatThongTinPhauThuatKhongThanhCong")
          );
        });
    },
    getDataToPrint: (
      { nbDotDieuTriId, allowShowTiepDonKham, ...rest },
      state
    ) => {
      dispatch.vitalSigns.updateData({
        isLoadingPrint: true,
        dataPrint: null,
      });

      let dsChiDinhTuLoaiDichVu = [
        LOAI_DICH_VU.NOI_TRU,
        LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
      ];
      if (allowShowTiepDonKham) {
        dsChiDinhTuLoaiDichVu = [
          ...dsChiDinhTuLoaiDichVu,
          LOAI_DICH_VU.KHAM,
          LOAI_DICH_VU.TIEP_DON,
        ];
      }

      let promises = [];
      //detail patient
      promises.push(
        new Promise(async (resolve, reject) => {
          client
            .get(
              combineUrlParams(
                `${dataPath}${NB_DOT_DIEU_TRI}/noi-tru/${nbDotDieuTriId}`
              )
            )
            .then((res) => {
              if (res?.data?.code === 0 && res?.data?.data) {
                resolve(res?.data?.data || {});
              } else {
                message.error(
                  res?.data?.message ||
                  t("danhMuc.khongTimThayThongTinBenhNhan")
                );
                reject(null);
              }
            })
            .catch((e) => {
              message.error(
                e?.message || t("danhMuc.khongTimThayThongTinBenhNhan")
              );
              resolve(null);
            });
        })
      );
      promises.push(
        new Promise((resolve, reject) => {
          client
            .get(
              combineUrlParams(`${dataPath}${NB_CHI_SO_SONG}`, {
                nbDotDieuTriId,
                dsChiDinhTuLoaiDichVu,
                ...rest,
              })
            )
            .then((res) => {
              if (res?.data?.code === 0) {
                resolve(res.data.data || []);
              } else {
                message.error(
                  res?.data?.message ||
                  t("quanLyNoiTru.taiDuLieuChucNangSongKhongThanhCong")
                );
                reject();
              }
            })
            .catch((e) => {
              resolve([]);
            });
        })
      );
      Promise.all(promises)
        .then((_values) => {
          let patient = {
            ..._values[0],
            ...(rest?.khoaChiDinhId && {
              tenKhoaChiDinh: _values[1][0]?.tenKhoaChiDinh,
            }),
          };
          let values = [];
          let moreValueIds = [];
          let vitalSignsCategories = state.chiSoSong.listAllChiSoSong || [];
          vitalSignsCategories = vitalSignsCategories.filter(
            (item) => !item.macDinh
          );

          _values[1].map((item) => {
            return (moreValueIds = [
              ...moreValueIds,
              ...(item.dsChiSoSongKhac || []).filter((t) => {
                if (vitalSignsCategories && vitalSignsCategories.length) {
                  if (!vitalSignsCategories.find((y) => y.id === t.chiSoSongId))
                    return false;
                }
                return t.giaTri !== "" && t.giaTri !== undefined;
              }),
            ]);
          });
          moreValueIds = moreValueIds
            .map((item) => item.chiSoSongId)
            .filter((item, index, self) => {
              return self.indexOf(item) === index;
            });

          // Sort moreValueIds theo stt bên trong vitalSignsCategories
          moreValueIds = sortValuesIds(moreValueIds, vitalSignsCategories);

          values = (_values[1] || [])
            .map((item, index) => {
              let item2 = {
                ...item,
                tenNguoiThucHien: formatName(item.tenNguoiThucHien || ""),
                ...splitName(item.tenNguoiThucHien || ""),
                id: item.id,
                thoiGianThucHien: new Date(item.thoiGianThucHien),
                huyetap:
                  item.huyetApTamThu === 0
                    ? ""
                    : item.huyetApTamThu +
                    (item.huyetApTamTruong
                      ? "/" + item.huyetApTamTruong
                      : ""),
                canNang: item.canNang,
                nhietDo: item.nhietDo,
                nhipTho: item.nhipTho,
                mach: item.mach,
                isLoading: false,
              };
              return item2;
            })
            .sort(
              (a, b) =>
                new Date(a?.thoiGianThucHien).getTime() -
                new Date(b?.thoiGianThucHien).getTime()
            );
          dispatch.vitalSigns.updateData({
            isLoadingPrint: false,
            dataPrint: {
              patient: patient,
              values: values,
              moreValueIds: moreValueIds || [],
            },
          });
        })
        .catch((e) => { });
    },

    onSizeChange: ({ size = 10, ten = "", active }) => {
      dispatch.vitalSigns.updateData({
        size,
        page: 0,
        categories: [],
      });
      dispatch.vitalSigns.onSearch({
        page: 0,
        ten,
        active,
        size,
      });
    },
    onSearch: ({ page, ten = "", active }, state) => {
      let newState = { isLoadingCategory: true, categories: [] };
      dispatch.vitalSigns.updateData(newState);
      let size = state.vitalSigns.size || 10;
      nbChiSoSongProvider
        .searchCategory({ page: page + "", size, ten, active })
        .then((s) => {
          dispatch.vitalSigns.updateData({
            categories: s?.data || [],
            isLoadingCategory: false,
            total: s?.totalElements || 0,
            page,
          });
        })
        .catch((e) => {
          message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          dispatch.vitalSigns.updateData({
            categories: [],
            isLoadingCategory: false,
          });
        });
    },

    convertData: ({ values, payload } = {}) => {
      return new Promise((resolve, reject) => {
        const data = (values || []).map((item, index) => {
          let newValues = {
            auToAddMach: false,
            auToAddNhietDo: false,
          };
          if (!isNumber(item?.mach)) {
            if (values[index - 1]?.mach && values[index + 1]?.mach) {
              let valueMach =
                (+values[index - 1]?.mach + +values[index + 1]?.mach) / 2;
              newValues.mach = valueMach;
              newValues.auToAddMach = true;
            }
          }
          if (!isNumber(item?.nhietDo)) {
            if (values[index - 1]?.nhietDo && values[index + 1]?.nhietDo) {
              let valueNhietDo =
                (+values[index - 1]?.nhietDo + +values[index + 1]?.nhietDo) / 2;
              newValues.nhietDo = valueNhietDo;
              newValues.auToAddNhietDo = true;
            }
          }
          return {
            ...item,
            ...newValues,
            ...(item.id === payload?.id && {
              tenKhoaChiDinh: payload?.khoaChiDinh?.ten || item.tenKhoaChiDinh,
            }),
          };
        });
        resolve(data);
      });
    },
  }),
};
