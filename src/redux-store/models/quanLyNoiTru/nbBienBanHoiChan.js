import nbBienBanHoiChanProvider from "data-access/nb-bien-ban-hoi-chan-provider";
import nbBienBanHoiChanTuVanProvider from "data-access/nb-bien-ban-hoi-chan-tu-van-provider";
import nbDvXNProvider from "data-access/nb-dv-xet-nghiem-provider";
import nbDvKhamProvider from "data-access/nb-dv-kham-provider";
import nbDvNgoaiDieuTriProvider from "data-access/nb-dv-ngoai-dieu-tri-provider";
import nbDvCLSProvider from "data-access/nb-dv-cdha-tdcn-pt-tt-provider";
import { message } from "antd";
import { combineSort } from "utils";
import { PAGE_SIZE, PAGE_DEFAULT, LOAI_DICH_VU } from "constants/index";
import { t } from "i18next";
import dmPhieuInProvider from "data-access/categories/dm-phieu-in-provider";
import nbDvThuocProvider from "data-access/nb-dv-thuoc-provider";
import nbDvVatTuProvider from "data-access/nb-dv-vat-tu-provider";
import nbDvChePhamDdProvider from "data-access/nbDichVu/nb-dv-che-pham-dd-provider";
import nbDvSuatAnProvider from "data-access/nb-dv-suat-an-provider";
import fileUtils from "utils/file-utils";

export default {
  state: {
    isLoading: false,
    listDsBienBanHoiChan: [],
    totalElements: null,
    page: PAGE_DEFAULT,
    size: PAGE_SIZE,
    dataEditDefault: {},
    dataSearch: {},
    listData: [],
    dataSortColumn: {},
    listDsBienBanHoiChanTuVan: [],
    listDsBienBanHoiChanLaoKhangThuoc: [],
    currentItem: {},
    isBatBuocHoiChanThuong: false,
  },
  reducers: {
    updateData(state, payload = {}) {
      return { ...state, ...payload };
    },
  },
  effects: (dispatch) => ({
    onSizeChange: ({ all, dataSearch, ...rest }, state) => {
      dispatch.nbBienBanHoiChan.updateData({
        page: 0,
        ...rest,
      });
      if (all) {
        dispatch.nbBienBanHoiChan.onSearchAll({ rest });
      } else {
        dispatch.nbBienBanHoiChan.onSearch({ rest });
      }
    },
    searchDsHoiChanByParams: ({ page = 0, size = 10, ...payload }, state) => {
      const obj = {
        dataSearch: {
          ...state.nbBienBanHoiChan.dataSearch,
          ...payload,
        },
      };

      dispatch.nbBienBanHoiChan.updateData({
        page,
        size,
        ...obj,
      });
      dispatch.nbBienBanHoiChan.onSearchAll({ page, size, ...obj });
    },
    onSearchAll: ({ page = 0, ...payload }, state) => {
      let newState = { isLoading: true, page };
      dispatch.nbBienBanHoiChan.updateData(newState);
      let size = payload.size || state.nbBienBanHoiChan.size || 10;
      const sort = combineSort(
        payload.dataSortColumn || state.nbBienBanHoiChan.dataSortColumn || {}
      );
      const dataSearch =
        payload.dataSearch || state.nbBienBanHoiChan.dataSearch || {};

      nbBienBanHoiChanProvider
        .searchAll({
          page,
          size,
          sort,
          ...dataSearch,
        })
        .then((s) => {
          dispatch.nbBienBanHoiChan.updateData({
            listData: (s?.data || []).map((item, index) => {
              item.index = page * size + index + 1;
              return item;
            }),
            isLoading: false,
            totalElements: s?.totalElements || 0,
            page,
          });
        })
        .catch((e) => {
          message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          dispatch.nbBienBanHoiChan.updateData({
            listData: [],
            isLoading: false,
          });
        });
    },
    onSearch: ({ page = 0, ...payload }, state) => {
      let newState = { isLoading: true, page };
      dispatch.nbBienBanHoiChan.updateData(newState);
      let size = payload.size || state.nbBienBanHoiChan.size || 10;
      const sort = combineSort(
        payload.dataSortColumn || state.nbBienBanHoiChan.dataSortColumn || {}
      );
      const dataSearch =
        payload.dataSearch || state.nbBienBanHoiChan.dataSearch || {};

      nbBienBanHoiChanProvider
        .search({
          page,
          size,
          sort,
          ...dataSearch,
        })
        .then((s) => {
          dispatch.nbBienBanHoiChan.updateData({
            listDsBienBanHoiChan: (s?.data || []).map((item, index) => {
              item.index = page * size + index + 1;
              return item;
            }),
            isLoading: false,
            totalElements: s?.totalElements || 0,
            page,
          });
        })
        .catch((e) => {
          message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          dispatch.nbBienBanHoiChan.updateData({
            listDsBienBanHoiChan: [],
            isLoading: false,
          });
        });
    },
    onSortChange: ({ all, ...payload }, state) => {
      const dataSortColumn = {
        ...state.nbBienBanHoiChan.dataSortColumn,
        ...payload,
      };
      dispatch.nbBienBanHoiChan.updateData({
        page: 0,
        dataSortColumn,
      });
      if (all) {
        dispatch.nbBienBanHoiChan.onSearchAll({
          page: 0,
          dataSortColumn,
        });
      } else {
        dispatch.nbBienBanHoiChan.onSearch({
          page: 0,
          dataSortColumn,
        });
      }
    },
    onChangeInputSearch: ({ ...payload }, state) => {
      const dataSearch = {
        ...(state.nbBienBanHoiChan.dataSearch || {}),
        ...payload,
      };
      dispatch.nbBienBanHoiChan.updateData({
        page: 0,
        dataSearch,
      });
      dispatch.nbBienBanHoiChan.onSearch({
        page: 0,
        dataSearch,
      });
    },
    getById: (id, state) => {
      return new Promise((resolve, reject) => {
        nbBienBanHoiChanProvider
          .getById(id)
          .then((s) => {
            dispatch.nbBienBanHoiChan.updateData({ nbBienBanHoiChan: s.data });
            resolve(s);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    createOrEdit: (payload = {}, state) => {
      return new Promise((resolve, reject) => {
        const refreshList = () => {
          const isHoiChan = window.location.pathname.includes("/hoi-chan");
          if (isHoiChan) {
            dispatch.nbBienBanHoiChan.searchDsHoiChanByParams({
              page: state.nbBienBanHoiChan.page || 0,
              size: state.nbBienBanHoiChan.size || 10,
            });
          } else {
            dispatch.nbBienBanHoiChan.onSearch({
              page: 0,
            });
          }
        };

        try {
          if (payload.id) {
            nbBienBanHoiChanProvider
              .patch(payload)
              .then((s) => {
                message.success(t("common.capNhatThanhCong"));
                refreshList();
                resolve(s?.data);
              })
              .catch((e) => {
                message.error(
                  e?.message || t("common.xayRaLoiVuiLongThuLaiSau")
                );
                reject();
              });
          } else {
            nbBienBanHoiChanProvider
              .post(payload)
              .then((s) => {
                message.success(t("common.themMoiThanhCongDuLieu"));
                refreshList();
                resolve(s?.data);
              })
              .catch((e) => {
                message.error(
                  e?.message || t("common.xayRaLoiVuiLongThuLaiSau")
                );
                reject();
              });
          }
        } catch (err) {
          message.error(err?.message.toString());
          return Promise.reject(err);
        }
      });
    },
    onDelete: (id) => {
      return new Promise((resolve, reject) => {
        nbBienBanHoiChanProvider
          .delete(id)
          .then((s) => {
            resolve(s);
            message.success(t("common.xoaDuLieuThanhCong"));
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    macDinh: ({ ...payload }) => {
      return new Promise((resolve, reject) => {
        nbBienBanHoiChanProvider
          .macDinh({ payload })
          .then((s) => {
            dispatch.nbBienBanHoiChan.updateData({
              nbBienBanHoiChanMacDinh: s.data,
            });
            resolve(s);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    getPhieu: ({ id, ...payload }) => {
      return new Promise((resolve, reject) => {
        let api = nbBienBanHoiChanProvider.getPhieu;
        if (payload?.path == "/trich-bien-ban") {
          api = nbBienBanHoiChanProvider.getPhieuTrichBienBanHoiChan;
        } else if (payload?.path == "/bien-ban") {
          api = nbBienBanHoiChanProvider.getPhieuBienBanHoiChan;
        }

        api({ id, ...payload })
          .then((s) => {
            if (s.code === 0) {
              resolve(s.data);
            } else {
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
              reject();
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    tuVanDichVu: (payload) => {
      return new Promise((resolve, reject) => {
        nbBienBanHoiChanTuVanProvider
          .batch(payload)
          .then((s) => {
            if (s.code === 0) {
              resolve(s);
              message.success(t("common.themMoiThanhCongDuLieu"));
            } else {
              message.error(
                s?.data?.message || t("common.xayRaLoiVuiLongThuLaiSau")
              );
              reject();
            }
          })
          .catch((e) => {
            message.error(e?.message);
            reject(e);
          });
      });
    },
    getBienBanHoiChanTuVan: ({ ...payload }) => {
      return new Promise((resolve, reject) => {
        nbBienBanHoiChanTuVanProvider
          .search(payload)
          .then((s) => {
            dispatch.nbBienBanHoiChan.updateData({
              listDsBienBanHoiChanTuVan: s?.data,
            });

            resolve(s?.data || []);
          })
          .catch((e) => {
            dispatch.nbBienBanHoiChan.updateData({
              listDsBienBanHoiChanTuVan: [],
            });

            reject(e);
          });
      });
    },
    updateBienBanHoiChanTuVan: (payload, state, options) => {
      return new Promise((resolve, reject) => {
        nbBienBanHoiChanTuVanProvider.patch(payload).then((s) => {
          if (s.code === 0) {
            resolve(s);
            if (!options?.ignoreMessage) {
              message.success(t("common.capNhatThanhCong"));
            }
          } else {
            message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject();
          }
        });
      });
    },
    onDeleteTuVan: ({ id, deleteIds }) => {
      return new Promise((resolve, reject) => {
        nbBienBanHoiChanTuVanProvider
          .delete({ id, deleteIds })
          .then((s) => {
            resolve(s);
            if (!deleteIds) message.success(t("common.xoaDuLieuThanhCong"));
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    chiDinhDichVu: (payload) => {
      return new Promise((resolve, reject) => {
        let provider = null;
        switch (payload?.nbDichVu?.loaiDichVu) {
          case LOAI_DICH_VU.KHAM: //kham
            provider = nbDvKhamProvider.chiDinhDVKham([payload]);
            break;
          case LOAI_DICH_VU.XET_NGHIEM: //xet nghiem
            provider = nbDvXNProvider.chiDinhXN([payload]);
            break;
          case LOAI_DICH_VU.CDHA:
          case LOAI_DICH_VU.PHAU_THUAT_THU_THUAT:
            provider = nbDvCLSProvider.chiDinhCLS([payload]);
            break;
          case LOAI_DICH_VU.NGOAI_DIEU_TRI:
            provider = nbDvNgoaiDieuTriProvider.chiDinhNgoaiDieuTri([payload]);
            break;
          case LOAI_DICH_VU.THUOC:
            provider = nbDvThuocProvider.chiDinhThuoc([payload]);
            break;
          case LOAI_DICH_VU.VAT_TU:
            provider = nbDvVatTuProvider.post([payload]);
            break;
          case LOAI_DICH_VU.SUAT_AN:
            provider = nbDvSuatAnProvider.chiDinhDichVu([payload]);
            break;
          case LOAI_DICH_VU.CHE_PHAM_DINH_DUONG:
            provider = nbDvChePhamDdProvider.post([payload]);
            break;
          default:
            break;
        }
        if (provider) {
          provider
            .then((s) => {
              if (s.code === 0) {
                if (s.data.some((item) => item.code)) {
                  resolve(s?.data || []);

                  message.error(
                    s.message ||
                      (s.data && s.data[0]?.message) ||
                      t("common.xayRaLoiVuiLongThuLaiSau")
                  );
                } else {
                  resolve(s?.data || []);
                  message.success(t("common.capNhatThanhCong"));
                }
              } else {
                resolve(s?.data || []);
                message.error(
                  s.message ||
                    (s.data && s.data[0]?.message) ||
                    t("common.xayRaLoiVuiLongThuLaiSau")
                );
              }
            })
            .catch((e) => {
              reject(e);
              message.error(e?.message);
            });
        }
      });
    },

    getListPhieu: (payload, state) => {
      return new Promise(async (resolve, reject) => {
        dmPhieuInProvider
          .searchTongHop(payload)
          .then((s) => {
            resolve(s?.data);
          })
          .catch((e) => {
            reject(e);
          });
      });
    },
    getBienBanHoiChanLaoKhangThuoc: (id) => {
      return new Promise((resolve, reject) => {
        nbBienBanHoiChanProvider
          .getBienBanHoiChanLaoKhangThuoc(id)
          .then((s) => {
            if (s?.code === 0) {
              dispatch.nbBienBanHoiChan.updateData({
                listDsBienBanHoiChanLaoKhangThuoc: s?.data,
              });
              resolve(s?.data);
            } else {
              dispatch.nbBienBanHoiChan.updateData({
                listDsBienBanHoiChanLaoKhangThuoc: {},
              });
            }
          });
      });
    },
    hoanThanh: (id) => {
      return new Promise((resolve, reject) => {
        nbBienBanHoiChanProvider.hoanThanh(id).then((s) => {
          if (s?.code === 0) {
            resolve(s);
            message.success(s?.message || t("hoiChan.hoanThanhBienBanHoiChan"));
          } else {
            message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject();
          }
        });
      });
    },
    huyHoanThanh: (id) => {
      return new Promise((resolve, reject) => {
        nbBienBanHoiChanProvider.huyHoanThanh(id).then((s) => {
          if (s?.code === 0) {
            resolve(s);
            message.success(
              s?.message || t("hoiChan.huyHoanThanhBienBanHoiChan")
            );
          } else {
            message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject();
          }
        });
      });
    },
    exportExcel: (payload, state) => {
      return new Promise((resolve, reject) => {
        nbBienBanHoiChanProvider
          .exportPut(payload)
          .then((s) => {
            if (s?.code == 0 && s?.data) {
              fileUtils.downloadFile(
                s?.data?.file?.doc,
                `${s?.data?.maBaoCao}.xlsx`
              );
              resolve(s);
            } else {
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
              reject(s);
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
  }),
};
