import nbChuyenKhoaProvider from "data-access/noiTru/nb-chuyen-khoa-provider";
import { message } from "antd";
import nbDvCdhaTdcnPtTtProvider from "data-access/nb-dv-cdha-tdcn-pt-tt-provider";
import { t } from "i18next";
export default {
  state: {
    dsNbChuyenKhoa: [],
  },
  reducers: {
    updateData(state, payload = {}) {
      return { ...state, ...payload };
    },
  },
  effects: (dispatch) => ({
    chuyenKhoa: (payload) => {
      return new Promise((resolve, reject) => {
        nbChuyenKhoaProvider
          .chuyenKhoa(payload)
          .then((s) => {
            if (s?.code === 0) {
              resolve(s);
              message.success(t("quanLyNoiTru.chuyenKhoaThanhCong"));
            } else {
              if (s?.code !== 1024) {
                message.error(
                  s?.message || t("common.xayRaLoiVuiLongThuLaiSau")
                );
              }
              reject(s);
            }
          })
          .catch((e) => {
            if (e?.code !== 1024) {
              message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            }
            reject(e);
          });
      });
    },

    getDsPhongGiuong: ({ page = 0, ...payload }, state) => {
      return new Promise((resolve, reject) => {
        nbChuyenKhoaProvider
          .searchPhongGiuong(payload)
          .then((s) => {
            if (s?.code === 0) {
              dispatch.nbChuyenKhoa.updateData({
                dsNbChuyenKhoa: s?.data || [],
              });
              resolve(s?.data);
            } else {
              reject(s);
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },

    getDsPttt: (payload, state) => {
      return new Promise((resolve, reject) => {
        nbDvCdhaTdcnPtTtProvider
          .getDsPttt(payload)
          .then((s) => {
            resolve((s?.data || []).filter((x) => !x.khongThucHien));
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },

    getNbChuyenKhoa: (payload) => {
      return new Promise((resolve, reject) => {
        nbChuyenKhoaProvider
          .search(payload)
          .then((s) => {
            if (s.code === 0) {
              resolve(s.data);
            } else {
              reject([]);
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject([]);
          });
      });
    },
  }),
};
