import { message } from "antd";
import { PAGE_SIZE, PAGE_DEFAULT, SORT_DEFAULT } from "constants/index";
import fileUtils from "utils/file-utils";
import apiBase from "data-access/api-base";
import dmProt<PERSON><PERSON><PERSON><PERSON>ider from "data-access/categories/dm-protocol-provider";
import dmMauDu<PERSON><PERSON>Provider from "data-access/dm-mau-du-lieu-provider";
import { t } from "i18next";

export default {
  state: {
    listProtocol: [],
    listAllProtocol: [],
    totalElements: null,
    pageProtocol: PAGE_DEFAULT,
    sizeProtocol: PAGE_SIZE,
    dataEditDefault: {},
    dataSearch: {},
    dataSortProtocol: SORT_DEFAULT,
  },
  reducers: {
    updateData(state, payload = {}) {
      return { ...state, ...payload };
    },
  },
  effects: (dispatch) => ({
    ...apiBase.initReduxGetListAll({
      dispatch,
      api: dmProtocolProvider.searchAll,
      KEY_CACHE: "DATA_ALL_PROTOCOL",
      model: "protocol",
      fieldName: "Protocol",
    }),
    searchProtocol: async (payload = {}, state) => {
      try {
        const { pageProtocol: page, sizeProtocol: size, ...rest } = payload;
        payload = { page, size, ...rest };
        const response = await dmProtocolProvider.search(payload);
        let {
          data: listProtocol,
          totalElements: totalProtocol,
          pageNumber: pageProtocol,
          pageSize: sizeProtocol,
          numberOfElements,
        } = response;

        if (pageProtocol > 0 && numberOfElements === 0) {
          return dispatch.protocol.searchProtocol({
            ...payload,
            pageProtocol: pageProtocol - 1,
            sizeProtocol,
          });
        }

        return dispatch.protocol.updateData({
          listProtocol,
          totalProtocol,
          pageProtocol,
          sizeProtocol,
        });
      } catch (err) {
        message.error(err.message.toString());
        return Promise.reject(err);
      }
    },
    createOrEditProtocol: async (payload = {}, state) => {
      let response = {};
      try {
        if (payload.id) {
          response = await dmProtocolProvider.put(payload);
          dispatch.protocol.updateData({
            dataEditDefault: response.data,
          });
          message.success(t("common.capNhatThanhCong"));
        } else {
          response = await dmProtocolProvider.post(payload);
          message.success(t("common.themMoiThanhCongDuLieu"));
        }
        return response?.data;
      } catch (err) {
        message.error(err.message.toString());
        return Promise.reject();
      }
    },
    onImport: async (payload, state) => {
      apiBase.onImport(payload, dmProtocolProvider.import).then((res) => {
        dispatch.protocol.searchProtocol({});
      });
    },
    onExport: () => {
      return new Promise((resolve, reject) => {
        dmMauDuLieuProvider
          .get({ dsBang: "dm_protocol" })
          .then((res) => {
            if (res && res.code === 0) {
              fileUtils.downloadFile(res.data?.data, "dm_protocol.xlsx");
            }
            resolve(res);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
  }),
};
