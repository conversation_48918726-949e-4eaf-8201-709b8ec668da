import apiBase from "data-access/api-base";
import baseStore from "redux-store/models/base-store";
import dmProtocolChiTietProvider from "data-access/categories/dm-protocol-chi-tiet-provider";
import { combineSort } from "utils";
import { message } from "antd";
import { t } from "i18next";

export default {
  ...baseStore({
    fetchProvider: dmProtocolChiTietProvider,
    storeName: "protocolChiTiet",
    title: "danhMuc.protocolChiTiet",
    customEffect: ({ dispatch }) => ({
      ...apiBase.initReduxGetListAll({
        dispatch,
        api: dmProtocolChiTietProvider.searchAll,
        KEY_CACHE: "DATA_ALL_PROTOCOL_CHI_TIET",
        model: "protocolChiTiet",
        fieldName: "protocolChiTiet",
      }),
      getData: ({ protocolId, page = 0, size = 10, ...payload }, state) => {
        dispatch.protocolChiTiet.updateData({
          page: 0,
          size,
          protocolId,
        });
        dispatch.protocolChiTiet.onSearch({
          page: 0,
          size,
          protocolId,
        });
      },
      onSearch: ({ page = 0, ...payload }, state) => {
        let newState = { isLoading: true, page };
        dispatch.protocolChiTiet.updateData(newState);
        let size = payload.size || state.protocolChiTiet.size || 10;
        const sort = combineSort(
          payload.dataSortColumn || state.protocolChiTiet.dataSortColumn || {}
        );
        const dataSearch =
          payload.dataSearch || state.protocolChiTiet.dataSearch || {};

        const protocolId = payload.hasOwnProperty("protocolId")
          ? payload.protocolId
          : state.protocolChiTiet.protocolId;

        dmProtocolChiTietProvider
          .search({
            page,
            size,
            sort,
            protocolId,
            ...dataSearch,
          })
          .then((s) => {
            dispatch.protocolChiTiet.updateData({
              listData: (s?.data || []).map((item, index) => {
                item.index = page * size + index + 1;
                return item;
              }),
              isLoading: false,
              totalElements: s?.totalElements || 0,
              page,
            });
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            dispatch.protocolChiTiet.updateData({
              listData: [],
              isLoading: false,
            });
          });
      },
      createOrEdit: ({ ...payload }, state) => {
        return new Promise((resolve, reject) => {
          try {
            if (payload.id) {
              dmProtocolChiTietProvider
                .put(payload)
                .then((s) => {
                  message.success(t("common.capNhatThanhCong"));
                  let data = (state.protocolChiTiet.listData || []).map(
                    (item) => {
                      if (item.id == s.data?.id) {
                        s.data.index = item.index;
                        return s.data;
                      }
                      return item;
                    }
                  );
                  dispatch.protocolChiTiet.updateData({
                    currentItem: null,
                    listData: data.sort((a, b) => b.active - a.active),
                  });
                  resolve(s.data);
                })
                .catch((e) => {
                  message.error(
                    e?.message || t("common.xayRaLoiVuiLongThuLaiSau")
                  );
                  reject(e);
                });
            } else {
              dmProtocolChiTietProvider
                .post(payload)
                .then((s) => {
                  message.success(t("common.themMoiThanhCongDuLieu"));
                  dispatch.protocolChiTiet.updateData({ currentItem: null });
                  dispatch.protocolChiTiet.onSearch({
                    page: 0,
                  });
                  resolve(s?.data);
                })
                .catch((e) => {
                  message.error(
                    e?.message || t("common.xayRaLoiVuiLongThuLaiSau")
                  );
                  reject(e);
                });
            }
          } catch (err) {
            message.error(err?.message.toString());
            return Promise.reject(err);
          }
        });
      },
      onSizeChange: ({ size }, state) => {
        dispatch.protocolChiTiet.updateData({
          size,
          page: 0,
        });
        dispatch.protocolChiTiet.onSearch({
          page: 0,
          size,
        });
      },
      onSortChange: ({ ...payload }, state) => {
        const dataSortColumn = {
          ...state.protocolChiTiet.dataSortColumn,
          ...payload,
        };
        dispatch.protocolChiTiet.updateData({
          page: 0,
          dataSortColumn,
        });
        dispatch.protocolChiTiet.onSearch({
          page: 0,
          dataSortColumn,
        });
      },
      onChangeInputSearch: ({ ...payload }, state) => {
        const dataSearch = {
          ...(state.protocolChiTiet.dataSearch || {}),
          ...payload,
        };
        dispatch.protocolChiTiet.updateData({
          page: 0,
          dataSearch,
        });
        dispatch.protocolChiTiet.onSearch({
          page: 0,
          dataSearch,
        });
      },
    }),
  }),
};
