import { message } from "antd";
import { PAGE_SIZE, PAGE_DEFAULT, SORT_DEFAULT } from "constants/index";
import fileUtils from "utils/file-utils";
import apiBase from "data-access/api-base";
import dmProtocolTenTruongProvider from "data-access/categories/dm-protocol-ten-truong-provider";
import dmMauDuLieuProvider from "data-access/dm-mau-du-lieu-provider";
import { t } from "i18next";

export default {
  state: {
    listProtocolTenTruong: [],
    listAllProtocolTenTruong: [],
    totalElements: null,
    pageProtocolTenTruong: PAGE_DEFAULT,
    sizeProtocolTenTruong: PAGE_SIZE,
    dataEditDefault: {},
    dataSearch: {},
    dataSortProtocolTenTruong: SORT_DEFAULT,
  },
  reducers: {
    updateData(state, payload = {}) {
      return { ...state, ...payload };
    },
  },
  effects: (dispatch) => ({
    ...apiBase.initReduxGetListAll({
      dispatch,
      api: dmProtocolTenTruongProvider.searchAll,
      KEY_CACHE: "DATA_ALL_PROTOCOL_TEN_TRUONG",
      model: "protocolTenTruong",
      fieldName: "ProtocolTenTruong",
    }),
    searchProtocolTenTruong: async (payload = {}, state) => {
      try {
        const {
          pageProtocolTenTruong: page,
          sizeProtocolTenTruong: size,
          ...rest
        } = payload;
        payload = { page, size, ...rest };
        const response = await dmProtocolTenTruongProvider.search(payload);
        let {
          data: listProtocolTenTruong,
          totalElements: totalProtocolTenTruong,
          pageNumber: pageProtocolTenTruong,
          pageSize: sizeProtocolTenTruong,
          numberOfElements,
        } = response;

        if (pageProtocolTenTruong > 0 && numberOfElements === 0) {
          return dispatch.protocolTenTruong.searchProtocolTenTruong({
            ...payload,
            pageProtocolTenTruong: pageProtocolTenTruong - 1,
            sizeProtocolTenTruong,
          });
        }

        return dispatch.protocolTenTruong.updateData({
          listProtocolTenTruong,
          totalProtocolTenTruong,
          pageProtocolTenTruong,
          sizeProtocolTenTruong,
        });
      } catch (err) {
        message.error(err.message.toString());
        return Promise.reject(err);
      }
    },
    searchTongHopProtocolTenTruong: async (payload = {}, state) => {
      try {
        const {
          pageProtocolTenTruong: page,
          sizeProtocolTenTruong: size,
          ...rest
        } = payload;
        payload = { page, size, ...rest };
        const response = await dmProtocolTenTruongProvider.searchTongHop(
          payload
        );
        let {
          data: listProtocolTenTruong,
          totalElements: totalProtocolTenTruong,
          pageNumber: pageProtocolTenTruong,
          pageSize: sizeProtocolTenTruong,
          numberOfElements,
        } = response;

        if (pageProtocolTenTruong > 0 && numberOfElements === 0) {
          return dispatch.protocolTenTruong.searchTongHopProtocolTenTruong({
            ...payload,
            pageProtocolTenTruong: pageProtocolTenTruong - 1,
            sizeProtocolTenTruong,
          });
        }

        return dispatch.protocolTenTruong.updateData({
          listProtocolTenTruong,
          totalProtocolTenTruong,
          pageProtocolTenTruong,
          sizeProtocolTenTruong,
        });
      } catch (err) {
        message.error(err.message.toString());
        return Promise.reject(err);
      }
    },
    createOrEditProtocolTenTruong: async (payload = {}, state) => {
      let response = {};
      try {
        if (payload.id) {
          response = await dmProtocolTenTruongProvider.put(payload);
          dispatch.protocolTenTruong.updateData({
            dataEditDefault: response.data,
          });
          message.success(t("common.capNhatThanhCong"));
        } else {
          response = await dmProtocolTenTruongProvider.post(payload);
          message.success(t("common.themMoiThanhCongDuLieu"));
        }
        return response?.data;
      } catch (err) {
        message.error(err.message.toString());
        return Promise.reject();
      }
    },
    onImport: async (payload, state) => {
      apiBase
        .onImport(payload, dmProtocolTenTruongProvider.import)
        .then((res) => {
          dispatch.protocolTenTruong.searchProtocolTenTruong({});
        });
    },
    onExport: () => {
      return new Promise((resolve, reject) => {
        dmMauDuLieuProvider
          .get({ dsBang: "dm_protocol_ten_truong" })
          .then((res) => {
            if (res && res.code === 0) {
              fileUtils.downloadFile(
                res.data?.data,
                "dm_protocol_ten_truong.xlsx"
              );
            }
            resolve(res);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
  }),
};
