import React, { lazy, Suspense } from "react";
import BaseIcon from "./BaseIcon";
import withRotate from "./withRotate";

const createSvg = (importer) => {
  const Component = lazy(() =>
    importer().catch(() => {
      return {
        default: (p) => <img width={20} height={20} src="" alt="" {...p} />,
      };
    })
  );

  return function SVGComponent(props) {
    return (
      <Suspense fallback={<div></div>}>
        <BaseIcon {...props} component={Component} />
      </Suspense>
    );
  };
};

const IcListFeature = createSvg(() => import("assets/svg/ic-list-feature.svg"));
const IcPrint = createSvg(() => import("assets/svg/ic-print.svg"));
const IcEdit = createSvg(() => import("assets/svg/ic-edit.svg"));
const IcEye = createSvg(() => import("assets/svg/ic-eye.svg"));
const IcSave = createSvg(() => import("assets/svg/ic-save.svg"));
const IcCall = createSvg(() => import("assets/svg/ic-call.svg"));
const IcDelete = createSvg(() => import("assets/svg/ic-delete.svg"));
const IcSearch = createSvg(() => import("assets/svg/ic-search.svg"));
const IcSetting = createSvg(() => import("assets/svg/ic-setting.svg"));
const IcTiem = createSvg(() => import("assets/svg/ic-tiem.svg"));
const IcVacxin = createSvg(() => import("assets/svg/ic-vacin.svg"));
const IcCalendar = createSvg(() => import("assets/svg/ic-calendar.svg"));
const IcSangLoc = createSvg(() => import("assets/svg/ic-sang-loc.svg"));
const IcThucHienTiem = createSvg(() =>
  import("assets/svg/ic-thuc-hien-tiem.svg")
);
const IcMaVach = createSvg(() => import("assets/svg/ic-ma-vach.svg"));
const IcTiemChung = createSvg(() => import("assets/svg/ic-tiem-chung.svg"));
const IcAdd = createSvg(() => import("assets/svg/ic-add.svg"));
const IcAddFilled = createSvg(() => import("assets/svg/ic-add-fill.svg"));
const IcFullScreenFilled = createSvg(() =>
  import("assets/svg/ic-full-screen-fill.svg")
);
const IcMinusFilled = createSvg(() => import("assets/svg/ic-minus-fill.svg"));
const IcSuccess = createSvg(() =>
  import("assets/svg/ic-check-circle-white.svg")
);
const IcArrowLeft = createSvg(() => import("assets/svg/ic-arrow-left.svg"));
const IcHDHD = createSvg(() => import("assets/svg/ic-hdhd.svg"));
const IcLichSu = createSvg(() => import("assets/svg/ic-lich-su.svg"));
const IcHsba = createSvg(() => import("assets/svg/ic-hsba.svg"));
const IcBaoCao = createSvg(() => import("assets/svg/ic-bao-cao.svg"));
const IcCDHATDCN = createSvg(() => import("assets/svg/ic-cdha-tdcn.svg"));
const IcDanhMuc = createSvg(() => import("assets/svg/ic-danh-muc.svg"));
const IcDanhSachGiayDayCong = createSvg(() =>
  import("assets/svg/ic-danh-sach-giay-day-cong.svg")
);
const IcDashboard = createSvg(() => import("assets/svg/ic-dashboard.svg"));
const IcGoiDichVu = createSvg(() => import("assets/svg/ic-goi-dich-vu.svg"));
const IcHoSoBenhAn = createSvg(() => import("assets/svg/ic-ho-so-benh-an.svg"));
const IcKeHoachTongHop = createSvg(() =>
  import("assets/svg/ic-ke-hoach-tong-hop.svg")
);
const IcKhamBenh = createSvg(() => import("assets/svg/ic-kham-benh.svg"));
const IcKhamSucKhoeHopDong = createSvg(() =>
  import("assets/svg/ic-kham-suc-khoe-hop-dong.svg")
);
const IcKhoMau = createSvg(() => import("assets/svg/ic-kho-mau.svg"));
const IcKySo = createSvg(() => import("assets/svg/ic-ky-so.svg"));
const IcNhaThuoc = createSvg(() => import("assets/svg/ic-nha-thuoc.svg"));
const IcDonThuoc = createSvg(() => import("assets/svg/ic-don-thuoc.svg"));
const IcPhauThuatThuThuat = createSvg(() =>
  import("assets/svg/ic-phau-thuat-thu-thuat.svg")
);
const IcPhucHoiChucNang = createSvg(() =>
  import("assets/svg/ic-phuc-hoi-chuc-nang.svg")
);
const IcQuanLyKho = createSvg(() => import("assets/svg/ic-quan-ly-kho.svg"));
const IcQuanLyNoiTru = createSvg(() =>
  import("assets/svg/ic-quan-ly-noi-tru.svg")
);
const IcQuanLyThongBao = createSvg(() =>
  import("assets/svg/ic-quan-ly-thong-bao.svg")
);
const IcQuanTriHeThong = createSvg(() =>
  import("assets/svg/ic-quan-tri-he-thong.svg")
);
const IcQuyetToanBhyt = createSvg(() =>
  import("assets/svg/ic-quyet-toan-bhyt.svg")
);
const IcQuanLyDinhDuong = createSvg(() =>
  import("assets/svg/ic-quan-ly-dinh-duong.svg")
);
const IcPhaCheThuoc = createSvg(() =>
  import("assets/svg/ic-pha-che-thuoc.svg")
);
const IcPhacDoDieuTri = createSvg(() =>
  import("assets/svg/ic-phac-do-dieu-tri.svg")
);
const IcPhacDoDieuTri1 = createSvg(() =>
  import("assets/svg/ic-phac-do-dieu-tri-1.svg")
);
const IcPhacDoDieuTri2 = createSvg(() =>
  import("assets/svg/ic-phac-do-dieu-tri-2.svg")
);
const IcSinhHieu = createSvg(() => import("assets/svg/ic-sinh-hieu.svg"));
const IcTheoDoiDieuTri = createSvg(() =>
  import("assets/svg/ic-theo-doi-dieu-tri.svg")
);
const IcThietLap = createSvg(() => import("assets/svg/ic-thiet-lap.svg"));
const IcThuNgan = createSvg(() => import("assets/svg/ic-thu-ngan.svg"));
const IcTiepDon = createSvg(() => import("assets/svg/ic-tiep-don.svg"));
const IcXetNghiem = createSvg(() => import("assets/svg/ic-xet-nghiem.svg"));
const IcLienThongThuocDienTu = createSvg(() =>
  import("assets/svg/ic-lien-thong-thuoc-dien-tu.svg")
);
const IcPhone = createSvg(() => import("assets/svg/ic-phone.svg"));
const IcXemThongKe2 = createSvg(() => import("assets/svg/ic-xem-thong-ke.svg"));
const IcDeNghiTamTung = createSvg(() =>
  import("assets/svg/ic-de-nghi-tam-ung.svg")
);
const IcHuyTamUng = createSvg(() => import("assets/svg/ic-huy-tam-ung.svg"));
const IcHoanTamUng = createSvg(() => import("assets/svg/ic-hoan-tam-ung.svg"));
const IcThuTamUng = createSvg(() => import("assets/svg/ic-thu-tam-ung.svg"));
const IcKetLuan = createSvg(() =>
  import("assets/images/khamBenh/icKetLuan.svg")
);
const IcDrag = createSvg(() => import("assets/svg/ic-drag.svg"));
const IcGrid = createSvg(() => import("assets/svg/ic-grid.svg"));
const IcNguoiBenhNam = createSvg(() => import("assets/svg/ic-nguoi-benh.svg"));
const IcNguoiBenhNu = createSvg(() => import("assets/svg/ic-nb-nu.svg"));
const IcNguoiBenhLgbt = createSvg(() => import("assets/svg/ic-nb-lgbt.svg"));
const IcGiuongTrong = createSvg(() => import("assets/svg/ic-giuong-trong.svg"));
const IcNam = createSvg(() => import("assets/svg/ic-male.svg"));
const IcNu = createSvg(() => import("assets/svg/ic-female.svg"));
const IcLgbt = createSvg(() => import("assets/svg/ic-lgbt.svg"));
const IcGridOutlined = createSvg(() =>
  import("assets/svg/ic-grid-outlined.svg")
);
const IcLogout = createSvg(() => import("assets/svg/ic-logout.svg"));
const IcLogin = createSvg(() => import("assets/svg/ic-login.svg"));
const IcChangePassword = createSvg(() =>
  import("assets/svg/ic-change-password.svg")
);
const IcShareLogin = createSvg(() => import("assets/svg/ic-share-login.svg"));
const IcLanguage = createSvg(() => import("assets/svg/ic-lang.svg"));
const IconHelp = createSvg(() => import("assets/svg/ic-help.svg"));
const IconVideo = createSvg(() => import("assets/svg/ic-video.svg"));
const IcDocument = createSvg(() => import("assets/svg/ic-document.svg"));
const IcQuestion2 = createSvg(() => import("assets/svg/ic-question2.svg"));
const IcQuestion = createSvg(() => import("assets/svg/ic-question.svg"));
const IcRefresh = createSvg(() => import("assets/svg/ic-refresh.svg"));
const IcNotification = createSvg(() =>
  import("assets/svg/ic-notification.svg")
);
const IcNotificationOutlined = createSvg(() =>
  import("assets/svg/ic-notification-outlined.svg")
);
const IcNotificationIdleOutlined = createSvg(() =>
  import("assets/svg/ic-notification-idle-outlined.svg")
);
const IconAvatar = createSvg(() => import("assets/svg/ic-avatar.svg"));
const IcApp = createSvg(() => import("assets/svg/ic-app.svg"));
const IcTick = createSvg(() => import("assets/svg/ic-tick.svg"));
const IcTick2 = createSvg(() => import("assets/svg/ic-tick2.svg"));
const IcTakePicture = createSvg(() => import("assets/svg/ic-take-picture.svg"));
const IcTakeAvatar = createSvg(() => import("assets/svg/ic-take-avatar.svg"));
const IcCamera = createSvg(() => import("assets/svg/ic-camera.svg"));
const IcUp = createSvg(() => import("assets/svg/ic-arrow-up.svg"));
const IcUpload = createSvg(() => import("assets/svg/ic-upload.svg"));
const IcArrowDown = createSvg(() => import("assets/svg/ic-arrow-down.svg"));
const IcTiemChung1 = createSvg(() => import("assets/svg/ic-tiem-chung-1.svg"));
const IcDieuTriDaiHan = createSvg(() =>
  import("assets/svg/ic-dieu-tri-dai-han.svg")
);
const IcTuyChinhTiepDon = createSvg(() =>
  import("assets/svg/ic-tuy-chinh-tiep-don.svg")
);
const IcQrCode = createSvg(() => import("assets/svg/ic-qrcode.svg"));
const IcUpdateStatusQr = createSvg(() =>
  import("assets/svg/ic-update-status-qr.svg")
);
const IcQrCodeOff = createSvg(() => import("assets/svg/ic-qrcode-off.svg"));
const IcBaoCao2 = createSvg(() => import("assets/svg/ic-bao-cao2.svg"));
const IcDanhSachNbDaTiepDon = createSvg(() =>
  import("assets/svg/ic-ds-da-tiep-don.svg")
);
const IcNoData = createSvg(() => import("assets/svg/ic-no-data.svg"));
const IcMail = createSvg(() => import("assets/svg/ic-mail.svg"));
const IcOption = createSvg(() => import("assets/svg/ic-option.svg"));
const IcEmptyBox = createSvg(() => import("assets/svg/ic-empty-box.svg"));
const IcHoanDv = createSvg(() => import("assets/svg/ic-hoandv.svg"));
const IcHuyHoanDv = createSvg(() => import("assets/svg/ic-huy-hoandv.svg"));
const IcViewImagePacs = createSvg(() => import("assets/svg/ic-view-pasc.svg"));
const IcPdf = createSvg(() => import("assets/svg/ic-pdf.svg"));
const IcSaoChep = createSvg(() => import("assets/svg/ic-sao-chep.svg"));
const IcReload = createSvg(() => import("assets/svg/ic-reload.svg"));
const IcCancel = createSvg(() => import("assets/svg/ic-cancel.svg"));
const IcChePhamDD = createSvg(() => import("assets/svg/ic-che-pham-dd.svg"));
import {
  MoreOutlined,
  FolderAddOutlined,
  FileAddOutlined,
  BugOutlined,
  PlusSquareOutlined,
  MinusSquareOutlined,
  CalculatorOutlined,
  ExceptionOutlined,
  SignatureOutlined,
  FundViewOutlined,
} from "@ant-design/icons";
const IcDownload = createSvg(() => import("assets/svg/ic-download.svg"));
const IcGiamDinh = createSvg(() => import("assets/svg/ic-giam-dinh.svg"));
const IcXoaHoSo = createSvg(() => import("assets/svg/ic-xoa-ho-so.svg"));
const IcChiDinhDichVu = createSvg(() =>
  import("assets/svg/ic-chi-dinh-dich-vu.svg")
);
const IcVatTu = createSvg(() => import("assets/svg/ic-vat-tu.svg"));
const IcSuatAn = createSvg(() => import("assets/svg/ic-suat-an.svg"));
const IcDoiDoiTuong = createSvg(() =>
  import("assets/svg/ic-doi-doi-tuong.svg")
);
const IcMoBenhAn = createSvg(() => import("assets/svg/ic-mo-benh-an.svg"));

const IcChuyenDichVu = createSvg(() =>
  import("assets/svg/ic-chuyen-dich-vu.svg")
);
const IcInfoFill = createSvg(() => import("assets/svg/ic-info-fill.svg"));
const IcInfo = createSvg(() => import("assets/svg/ic-info.svg"));
const IcThongTinPttt = createSvg(() =>
  import("assets/svg/ic-thong-tin-pttt.svg")
);
const IcThongTinNguoiThucHien = createSvg(() =>
  import("assets/svg/ic-thong-tin-nguoi-thuc-hien.svg")
);
const IcMau = createSvg(() => import("assets/svg/ic-mau.svg"));
const IcHoaChat = createSvg(() => import("assets/svg/ic-hoa-chat.svg"));
const IcDichVu = createSvg(() => import("assets/svg/ic-dich-vu.svg"));
const IcPhongGiuong = createSvg(() => import("assets/svg/ic-phong-giuong.svg"));
const IcList = createSvg(() => import("assets/svg/ic-list.svg"));
const IcListNoBorder = createSvg(() =>
  import("assets/svg/ic-list-no-border.svg")
);
const IcMoRong = createSvg(() => import("assets/svg/ic-mo-rong.svg"));
const IcNgungSuDungThuoc = createSvg(() =>
  import("assets/svg/ic-ngung-su-dung-thuoc.svg")
);
const IcNgungThuoc = createSvg(() => import("assets/svg/ic-ngung-thuoc.svg"));
const IcToDieuTri = createSvg(() => import("assets/svg/ic-to-dieu-tri.svg"));
const IcTiepTuc = createSvg(() => import("assets/svg/ic-tiep-tuc.svg"));
const IcTamDung = createSvg(() => import("assets/svg/ic-tam-dung.svg"));
const IcKetThuc = createSvg(() => import("assets/svg/ic-ket-thuc.svg"));
const IcMoLai = createSvg(() => import("assets/svg/ic-mo-lai.svg"));
const IcLaySo = createSvg(() => import("assets/svg/ic-lay-so.svg"));
const IcLichSuTiemChung = createSvg(() =>
  import("assets/svg/ic-lich-su-tiem-chung.svg")
);
const IcDanhSachHoaDon = createSvg(() =>
  import("assets/svg/ic-ds-hoa-don.svg")
);
const IcSend = createSvg(() => import("assets/svg/ic-send.svg"));
const IcGuiCt = createSvg(() => import("assets/svg/ic-gui-ct.svg"));
const IcCloseCircle = createSvg(() => import("assets/svg/ic-close-circle.svg"));
const IcCloseCircleFill = createSvg(() =>
  import("assets/svg/ic-close-circle-fill.svg")
);
const IcLocation = createSvg(() => import("assets/svg/ic-location.svg"));
const IcKiosk = createSvg(() => import("assets/svg/ic-kiosk.svg"));
const IcHoiChan1 = createSvg(() => import("assets/svg/ic-hoi-chan-1.svg"));
const IcQms = createSvg(() => import("assets/svg/ic-qms.svg"));
const IcScanBieuMau = createSvg(() =>
  import("assets/svg/ic-bieu-mau-scan.svg")
);
const IcChietKhau = createSvg(() => import("assets/svg/ic-chiet-khau.svg"));
const IcChiaPhieuThu = createSvg(() =>
  import("assets/svg/ic-chia-phieu-thu.svg")
);
const IcMienGiam = createSvg(() => import("assets/svg/ic-mien-giam.svg"));
const IcConnected = createSvg(() => import("assets/svg/ic-connected.svg"));
const IcDisconnect = createSvg(() => import("assets/svg/ic-disconnect.svg"));
const IcHoiChan = createSvg(() => import("assets/svg/ic-hoi-chan.svg"));
const IcChiSoSong = createSvg(() => import("assets/svg/ic-chi-so-song.svg"));
const IcThongTinChung = createSvg(() =>
  import("assets/svg/ic-thong-tin-chung.svg")
);
const IcUploadFile = createSvg(() => import("assets/svg/ic-upload-file.svg"));
const IcFullScreen = createSvg(() => import("assets/svg/ic-full-screen.svg"));
const IcTiepDonThuNgan = createSvg(() =>
  import("assets/svg/Ic-tiep-don-thu-ngan.svg")
);
const IcPending = createSvg(() => import("assets/svg/ic-pending.svg"));
const IcWaiting = createSvg(() => import("assets/svg/ic-waiting.svg"));
const IcYeuCauHoan = createSvg(() => import("assets/svg/ic-yeu-cau-hoan.svg"));
const IcBanGiaoThuoc = createSvg(() =>
  import("assets/svg/ic-ban-giao-thuoc.svg")
);
const IcFilter = createSvg(() => import("assets/svg/ic-filter.svg"));
const IcThanhToan = createSvg(() => import("assets/svg/ic-thanh-toan.svg"));
const IcHuyThanhToan = createSvg(() =>
  import("assets/svg/ic-huy-thanh-toan.svg")
);
const IcShowThuNho = createSvg(() => import("assets/svg/ic-thu-nho.svg"));
const IcShowFull = createSvg(() => import("assets/svg/ic-show-full.svg"));
const IcExtend = createSvg(() => import("assets/svg/ic-extend.svg"));
const IcCollapse = createSvg(() => import("assets/svg/ic-collapse.svg"));
const IcExport = createSvg(() => import("assets/svg/ic-export.svg"));
const IcShowLog = createSvg(() => import("assets/svg/ic-show-log.svg"));
const IcPhieuSoKet = createSvg(() => import("assets/svg/ic-phieu-so-ket.svg"));
const IcQuaHan3Ngay = createSvg(() =>
  import("assets/svg/ic-qua-han-3-ngay.svg")
);
const IcQuaHan7Ngay = createSvg(() =>
  import("assets/svg/ic-qua-han-7-ngay.svg")
);
const IcQmsThuNgan = createSvg(() => import("assets/svg/ic-qms-thu-ngan.svg"));
const IcXuatHoaDon = createSvg(() => import("assets/svg/ic-xuat-hoa-don.svg"));
const IcDeleteCircle = createSvg(() =>
  import("assets/svg/ic-delete-circle.svg")
);
const IcTiemChungBg = createSvg(() =>
  import("assets/svg/ic-tiem-chung-bg.svg")
);
const IcChoHoan = createSvg(() => import("assets/svg/ic-cho-hoan.svg"));
const IcTiepNhan = createSvg(() => import("assets/svg/ic-tiep-nhan.svg"));
const IcTime = createSvg(() => import("assets/svg/ic-time.svg"));
const IcDaHoan = createSvg(() => import("assets/svg/ic-da-hoan.svg"));
const IcFolder = createSvg(() => import("assets/svg/ic-folder.svg"));
const IcImage = createSvg(() => import("assets/svg/ic-image.svg"));
const IcExpandDown = createSvg(() => import("assets/svg/ic-expand-down.svg"));
const IcDone = createSvg(() => import("assets/svg/ic-done.svg"));
const IcTuVan = createSvg(() => import("assets/svg/ic-tu-van.svg"));
const IcGuiVaDuyet = createSvg(() =>
  import("assets/images/kho/ic-send-approve.svg")
);
const IcLoaiPhieu = createSvg(() =>
  import("assets/images/kho/ic-loai-phieu-xuat.svg")
);
const IcMayTinh = createSvg(() => import("assets/svg/ic-may-tinh.svg"));
const IcCapCuu = createSvg(() => import("assets/svg/ic-capcuu.svg"));
const IcPhatHanhThe = createSvg(() =>
  import("assets/svg/ic-phat-hanh-the.svg")
);
const IcFolderUpload = createSvg(() =>
  import("assets/svg/Ic-folder-upload.svg")
);
const IcSwitchCamera = createSvg(() =>
  import("assets/svg/ic-switch-camera.svg")
);
const IcWarning = createSvg(() => import("assets/svg/ic-warning.svg"));
const IcError = createSvg(() => import("assets/svg/ic-error.svg"));
const IcCheckHsba = createSvg(() => import("assets/svg/ic-check.svg"));
const IcArrowSwap = createSvg(() => import("assets/svg/ic-arrow-swap.svg"));
const IcTheBaoHiem = createSvg(() => import("assets/svg/ic-the-bao-hiem.svg"));
const IcNhiemKhuan = createSvg(() => import("assets/svg/ic-nhiem-khuan.svg"));
const IcDVKT = createSvg(() => import("assets/svg/ic-dvkt.svg"));
const IcPass = createSvg(() => import("assets/svg/ic-pass.svg"));

const IcChart = createSvg(() => import("assets/svg/ic-chart.svg"));
const IcXuatHangLoat = createSvg(() =>
  import("assets/svg/ic-xuat-hang-loat.svg")
);
const IcPerson = createSvg(() => import("assets/svg/ic-person.svg"));
const IcPersonMultiple = createSvg(() =>
  import("assets/svg/ic-person-multiple.svg")
);
const IcPencel = createSvg(() => import("assets/svg/ic-pencel.svg"));
const IcCircleBack = createSvg(() => import("assets/svg/ic-back-circle.svg"));
//Icon man hinh cap 2

/* Quản lý kho */
const IcThietLapKhoChiDinh = createSvg(() =>
  import("assets/svg/ic-thiet-lap-kho-chi-dinh.svg")
);
const IcQuanTriKho = createSvg(() => import("assets/svg/ic-quan-tri-kho.svg"));
const IcQuanLyThau = createSvg(() => import("assets/svg/ic-quan-ly-thau.svg"));
const IcNhapKho = createSvg(() => import("assets/svg/ic-nhap-kho.svg"));
const IcXuatKho = createSvg(() => import("assets/svg/ic-xuat-kho.svg"));
const IcDanhSachTonKho = createSvg(() =>
  import("assets/svg/ic-danh-sach-ton-kho.svg")
);
const IcPhatThuocNgoaiTruRaVien = createSvg(() =>
  import("assets/svg/ic-phat-thuoc-ngoai-tru-ra-vien.svg")
);
const IcVatTuKyGui = createSvg(() => import("assets/svg/ic-vat-tu-ky-gui.svg"));
const IcDanhSachDuyetDuocLamSang = createSvg(() =>
  import("assets/svg/ic-danh-sach-duyet-duoc-lam-sang.svg")
);
const IcDanhSachNguoiBenhChuaHoanThanhTraHangHoa = createSvg(() =>
  import("assets/svg/ic-danh-sach-nb-chua-hoan-thanh-tra-hang-hoa.svg")
);
const IcDanhSachSuatAnChuaLinhDuyetTra = createSvg(() =>
  import("assets/svg/ic-danh-sach-suat-an-chua-linh-duyet-tra.svg")
);
const IcDSTuVanThuoc = createSvg(() =>
  import("assets/svg/ic-ds-tu-van-thuoc.svg")
);
const IcDieuTriLaoNoiTru = createSvg(() =>
  import("assets/svg/ic-dieu-tri-lao.svg")
);
const IcThongTinConNoiTru = createSvg(() =>
  import("assets/svg/ic-thong-tin-con.svg")
);
const IcTongKetKhoaDeNoiTru = createSvg(() =>
  import("assets/svg/ic-tong-ket-khoa-de.svg")
);

/* Xét nghiệm */
const IcLayMauBenhPham = createSvg(() =>
  import("assets/svg/ic-lay-mau-benh-pham.svg")
);
const IcThucHienSinhHoaHuyetHoc = createSvg(() =>
  import("assets/svg/ic-thuc-hien-sinh-hoa-huyet-hoc.svg")
);
const IcThucHienGiaiPhauBenhViSinh = createSvg(() =>
  import("assets/svg/ic-thuc-hien-giai-phau-benh-vi-sinh.svg")
);

/* Kế hoạch tổng hợp */
const IcDanhSachLuuTruBenhAn = createSvg(() =>
  import("assets/svg/ic-danh-sach-luu-tru-benh-an.svg")
);
const IcDanhSachDuyetBaoHiem = createSvg(() =>
  import("assets/svg/ic-danh-sach-duyet-bao-hiem.svg")
);

/* Quyết toán bảo hiểm y tế */
const IcDanhSachNbChoTaoHoSoQtBhyt = createSvg(() =>
  import("assets/svg/ic-danh-sach-nb-cho-tao-ho-so-qtbhyt.svg")
);
const IcDanhSachHoSoBaoHiemTheoQD4210 = createSvg(() =>
  import("assets/svg/ic-danh-sach-ho-so-bao-hiem-qd4210.svg")
);
const IcDanhSachHoSoBaoHiemTheoQD130 = createSvg(() =>
  import("assets/svg/ic-danh-sach-ho-so-bao-hiem-qd130.svg")
);
const IcDanhSachHoSoDichVuTheoQD130 = createSvg(() =>
  import("assets/svg/ic-danh-sach-ho-so-dich-vu-qd130.svg")
);
const IcDanhSachHoSoBaoHiemDaXoa = createSvg(() =>
  import("assets/svg/ic-danh-sach-ho-so-bao-hiem-da-xoa.svg")
);

/* Pha chế thuốc */
const IcKhaiBaoCongThucPhaChe = createSvg(() =>
  import("assets/svg/ic-khai-bao-cong-thuc-pha-che.svg")
);
const IcDanhSachPhieuPhaCheThuoc = createSvg(() =>
  import("assets/svg/ic-danh-sach-phieu-pha-che-thuoc.svg")
);
const IcDanhSachPhieuXuatPhaCheThuoc = createSvg(() =>
  import("assets/svg/ic-danh-sach-phieu-xuat-pha-che-thuoc.svg")
);

/* Tiếp đón */
const IcTiepDon1 = createSvg(() => import("assets/svg/ic-tiep-don-1.svg"));
const IcDanhSachNbDaTiepDon1 = createSvg(() =>
  import("assets/svg/ic-danh-sach-nb-da-tiep-don.svg")
);
const IcDanhSachNbHuyTiepDon = createSvg(() =>
  import("assets/svg/ic-danh-sach-nb-da-huy-tiep-don.svg")
);
const IcDanhSachLichHen = createSvg(() =>
  import("assets/svg/ic-danh-sach-lich-hen.svg")
);

/* Quản lý nội trú */
const IcDanhSachLapBenhAn = createSvg(() =>
  import("assets/svg/ic-danh-sach-lap-benh-an.svg")
);
const IcDanhSachNbNoiTru = createSvg(() =>
  import("assets/svg/ic-danh-sach-nb-noi-tru.svg")
);
const IcGiaHanTheChuyenDoiTuong = createSvg(() =>
  import("assets/svg/ic-gia-han-the-chuyen-doi-tuong.svg")
);
const IcQuanLyDieuDuongPhuTrachPhongGiuong = createSvg(() =>
  import("assets/svg/ic-quan-ly-dieu-duong-phu-trach-phong-giuong.svg")
);

/* CDHA-TDCN */
const IcChoTiepDon = createSvg(() => import("assets/svg/ic-cho-tiep-don.svg"));
const IcTiepNhan1 = createSvg(() => import("assets/svg/ic-tiep-nhan-1.svg"));
const IcBieuDoThongKe = createSvg(() =>
  import("assets/svg/ic-bieu-do-thong-ke.svg")
);

/* Ký số */
const IcThietLapQuyenKy = createSvg(() =>
  import("assets/svg/ic-thiet-lap-quyen-ky.svg")
);
const IcDanhSachPhieuChoKy = createSvg(() =>
  import("assets/svg/ic-danh-sach-phieu-cho-ky.svg")
);
const IcLichSuKy = createSvg(() => import("assets/svg/ic-lich-su-ky.svg"));
const IcTrinhKy = createSvg(() => import("assets/svg/ic-trinh-ky.svg"));
const IcTuChoiKy = createSvg(() => import("assets/svg/ic-tu-choi-ky.svg"));

/* Quản trị hệ thống */
const IcDanhMucVaiTro = createSvg(() =>
  import("assets/svg/ic-danh-muc-vai-tro.svg")
);
const IcQuanLyTaiKhoan = createSvg(() =>
  import("assets/svg/ic-quan-ly-tai-khoan.svg")
);
const IcDanhMucNhanVien = createSvg(() =>
  import("assets/svg/ic-danh-muc-nhan-vien.svg")
);
const IcDanhMucQuyen = createSvg(() =>
  import("assets/svg/ic-danh-muc-quyen.svg")
);
const IcDanhMucNhomTinhNang = createSvg(() =>
  import("assets/svg/ic-danh-muc-nhom-tinh-nang.svg")
);
const IcTuyChinhGiaoDienPhanMem = createSvg(() =>
  import("assets/svg/ic-tuy-chinh-giao-dien-phan-mem.svg")
);

/* Gói dịch vụ */
const IcDanhSachNbSuDungGoi = createSvg(() =>
  import("assets/svg/ic-danh-sach-nb-su-dung-goi.svg")
);
const IcDanhMucGoiDichVu = createSvg(() =>
  import("assets/svg/ic-danh-muc-goi-dich-vu.svg")
);

/* Dashboard */
const IcTongQuanBenhVien = createSvg(() =>
  import("assets/svg/ic-tong-quan-benh-vien.svg")
);
const IcTongQuanNgoaiTru = createSvg(() =>
  import("assets/svg/ic-tong-quan-ngoai-tru.svg")
);
const IcTongQuanDoanhThu = createSvg(() =>
  import("assets/svg/ic-tong-quan-doanh-thu.svg")
);
const IcTongQuanGiuongPhong = createSvg(() =>
  import("assets/svg/ic-tong-quan-giuong-phong.svg")
);
const IcThoiGianChoKham = createSvg(() =>
  import("assets/svg/ic-thoi-gian-cho-kham.svg")
);

/* Thiết lập */
const IcThietLapChung = createSvg(() =>
  import("assets/svg/ic-thiet-lap-chung.svg")
);
const IcTachGopPhieuXetNghiem = createSvg(() =>
  import("assets/svg/ic-tach-gop-phieu-xet-nghiem.svg")
);
const IcTachGopPhieuChiDinhDvkt = createSvg(() =>
  import("assets/svg/ic-tach-gop-phieu-chi-dinh-dvkt.svg")
);
const IcThietLapTichDiem = createSvg(() =>
  import("assets/svg/ic-thiet-lap-tich-diem.svg")
);
const IcThietLapThongSoHangDoi = createSvg(() =>
  import("assets/svg/ic-thiet-lap-thong-so-hang-doi.svg")
);
const IcThietLapPhieuTaiCacManHinh = createSvg(() =>
  import("assets/svg/ic-thiet-lap-phieu-tai-cac-man-hinh.svg")
);
const IcThietLapPhieuLinhTra = createSvg(() =>
  import("assets/svg/ic-thiet-lap-phieu-linh-tra.svg")
);
const IcThietLapChonGiuong = createSvg(() =>
  import("assets/svg/ic-thiet-lap-chon-giuong.svg")
);

const IcThietLapNhungLink = createSvg(() =>
  import("assets/svg/ic-thiet-lap-nhung-link.svg")
);
const IcThietLapDieuKienChuyenKhoaRaVien = createSvg(() =>
  import("assets/svg/ic-thiet-lap-dieu-kien-chuyen-khoa-ra-vien.svg")
);
const IcThietLapLuuTruBenhAn = createSvg(() =>
  import("assets/svg/ic-thiet-lap-luu-tru-benh-an.svg")
);
const IcThietLapDoiMaNguoiBenh = createSvg(() =>
  import("assets/svg/ic-thiet-lap-doi-ma-nguoi-benh.svg")
);
const IcThietLapSoLienIn = createSvg(() =>
  import("assets/svg/ic-thiet-lap-so-lien-in.svg")
);
const IcThietLapGiaTriCSS = createSvg(() =>
  import("assets/svg/ic-thiet-lap-gia-tri-css.svg")
);

/* Tiêm chủng */
const IcTiepDonTiemChung = createSvg(() =>
  import("assets/svg/ic-tiep-don-tiem-chung.svg")
);
const IcDanhSachTiepDonTiemChung = createSvg(() =>
  import("assets/svg/ic-danh-sach-tiep-don-tiem-chung.svg")
);
const IcDanhSachKhamSangLoc = createSvg(() =>
  import("assets/svg/ic-danh-sach-kham-sang-loc.svg")
);
const IcDanhSachTiem = createSvg(() =>
  import("assets/svg/ic-danh-sach-tiem.svg")
);
const IcDanhSachTheoDoiSauTiem = createSvg(() =>
  import("assets/svg/ic-danh-sach-theo-doi-sau-tiem.svg")
);

const IcDanhSachVaccinDayCongTcqg = createSvg(() =>
  import("assets/svg/ic-danh-sach-vaccin-day-cong-tcqg.svg")
);

/* Nhà thuốc */
const IcDanhSachDonThuoc = createSvg(() =>
  import("assets/svg/ic-danh-sach-don-thuoc.svg")
);
const IcLienThongGpp = createSvg(() =>
  import("assets/svg/ic-lien-thong-gpp.svg")
);

/* Kho máu */
const IcNhapKhoMau = createSvg(() => import("assets/svg/ic-nhap-kho-mau.svg"));
const IcDanhSachTonKhoMau = createSvg(() =>
  import("assets/svg/ic-danh-sach-ton-kho-mau.svg")
);
const IcTruyenPhatMau = createSvg(() =>
  import("assets/svg/ic-truyen-phat-mau.svg")
);
const IcXuatKhoMau = createSvg(() => import("assets/svg/ic-xuat-kho-mau.svg"));

/* Kiosk */
const IcKioskLaySoThuNgan = createSvg(() =>
  import("assets/svg/ic-kiosk-lay-so-thu-ngan.svg")
);
const IcKioskQuetMaQr = createSvg(() =>
  import("assets/svg/ic-kiosk-quet-ma-qr.svg")
);
const IcKioskQuetVaGoiNbVaoQuay = createSvg(() =>
  import("assets/svg/ic-kiosk-quet-va-goi-nb-vao-quay.svg")
);

/* Khám sức khỏe hợp đồng */
const IcDanhSachPhieuBaoGia = createSvg(() =>
  import("assets/svg/ic-danh-sach-phieu-bao-gia.svg")
);
const IcDanhSachHopDong = createSvg(() =>
  import("assets/svg/ic-danh-sach-hop-dong.svg")
);
const IcDongBoGia = createSvg(() => import("assets/svg/ic-dong-bo-gia.svg"));

/* Thu ngân */
const IcDanhSachPhieuThu = createSvg(() =>
  import("assets/svg/ic-danh-sach-phieu-thu.svg")
);
const IcDanhSachPhieuYeuCauHoan = createSvg(() =>
  import("assets/svg/ic-danh-sach-phieu-yeu-cau-hoan.svg")
);
const IcDanhSachHoaDonDienTu = createSvg(() =>
  import("assets/svg/ic-danh-sach-hoa-don-dien-tu.svg")
);
const IcQuanLyTamUng = createSvg(() =>
  import("assets/svg/ic-quan-ly-tam-ung.svg")
);
const IcLogoSakura = createSvg(() => import("assets/svg/ic-logo-sakura.svg"));

/* Danh sách giấy đẩy cổng */
const IcDanhSachGiayNghiHuong = createSvg(() =>
  import("assets/svg/ic-danh-sach-giay-nghi-huong.svg")
);
const IcDanhSachNbTuVong = createSvg(() =>
  import("assets/svg/ic-danh-sach-nb-tu-vong.svg")
);
const IcDanhSachNbRaVien = createSvg(() =>
  import("assets/svg/ic-danh-sach-nb-ra-vien.svg")
);
const IcDanhSachPhieuTomTatBenhAn = createSvg(() =>
  import("assets/svg/ic-danh-sach-phieu-tom-tat-benh-an.svg")
);
const IcDanhSachPhieuChungSinh = createSvg(() =>
  import("assets/svg/ic-danh-sach-phieu-chung-sinh.svg")
);
const IcDanhSachGiayKskLaiXe = createSvg(() =>
  import("assets/svg/ic-danh-sach-giay-ksk-lai-xe.svg")
);
const IcDanhSachGiayChungNhanNghiDuongThai = createSvg(() =>
  import("assets/svg/ic-danh-sach-giay-chung-nhan-nghi-duong-thai.svg")
);

/* Quản lý dinh dưỡng */
const IcDanhSachPhieuLinhSuatAn = createSvg(() =>
  import("assets/svg/ic-danh-sach-phieu-linh-suat-an.svg")
);
const IcDanhSachPhieuTraSuatAn = createSvg(() =>
  import("assets/svg/ic-danh-sach-phieu-tra-suat-an.svg")
);
const IcDanhSachNbQuanLyDinhDuong = createSvg(() =>
  import("assets/svg/ic-danh-sach-nb-quan-ly-dinh-duong.svg")
);
const IcKhaiBaoChiSoDinhDuongTreEm = createSvg(() =>
  import("assets/svg/ic-khai-bao-chi-so-dinh-duong-tre-em.svg")
);

// Kiểm soát nhiễm khuẩn
const IcTiepNhanDieuTra = createSvg(() =>
  import("assets/svg/ic-tiep-nhan-dieu-tra.svg")
);
const IcHuyCapNhat = createSvg(() => import("assets/svg/ic-huy-cap-nhat.svg"));

/* Sinh hiệu */
const IcDoSinhHieuGut = createSvg(() =>
  import("assets/svg/ic-do-sinh-hieu-gut.svg")
);
const IcNhapChiSo = createSvg(() => import("assets/svg/ic-nhap-chi-so.svg"));
const IcCanhBaoCss = createSvg(() => import("assets/svg/ic-canh-bao-css.svg"));

// Đo thị lực
const IcDoThiLuc = createSvg(() => import("assets/svg/ic-do-thi-luc.svg"));

// Báo cáo ADR
const IcBaoCaoAdr = createSvg(() => import("assets/svg/ic-bao-cao-adr.svg"));

// KPIs
const IcKPIs = createSvg(() => import("assets/svg/ic-kpis.svg"));

// Quản lý điều trị lao
const IcDangKyThuocLao = createSvg(() =>
  import("assets/svg/ic-dang-ky-thuoc-lao.svg")
);
const IcDieuTriLao = createSvg(() => import("assets/svg/ic-dieu-tri-lao.svg"));
const IcNbDieuTriLao = createSvg(() =>
  import("assets/svg/ic-nb-dieu-tri-lao.svg")
);

// Quản lý nhân lực
const IcQuanLyNhanLuc = createSvg(() =>
  import("assets/svg/ic-quan-ly-nhan-luc.svg")
);

const IcQuanLyYeuCau = createSvg(() =>
  import("assets/svg/ic-quan-ly-yeu-cau.svg")
);

const IcRectangular = createSvg(() => import("assets/svg/ic-rectangular.svg"));

const IcMedicalKit = createSvg(() => import("assets/svg/ic-medical-kit.svg"));
const IcChePhamMau = createSvg(() => import("assets/svg/ic-che-pham-mau.svg"));
const IcChePhamMauHuy = createSvg(() =>
  import("assets/svg/ic-che-pham-mau-huy.svg")
);
const IcTaoPhienBanMau = createSvg(() =>
  import("assets/svg/ic-tao-phien-ban-mau.svg")
);

const IcExpand = createSvg(() => import("assets/svg/ic-expand.svg"));
const IcPlus = createSvg(() => import("assets/svg/ic-plus.svg"));
const IcSkip = createSvg(() => import("assets/svg/ic-skip.svg"));
const IcArrowBack = createSvg(() => import("assets/svg/ic-arrow-back.svg"));
const IcCheckCircleGreen = createSvg(() =>
  import("assets/svg/ic-check-circle-green.svg")
);
const IcYearCalendar = createSvg(() =>
  import("assets/svg/ic-calendar-year.svg")
);
const IcTuVanThuoc = createSvg(() => import("assets/svg/ic-tu-van-thuoc.svg"));

// Hẹn nội soi
const IcModuleHenNoiSoi = createSvg(() =>
  import("assets/svg/ic-module-hen-noi-soi.svg")
);
const IcThoiGianHenNoiSoi = createSvg(() =>
  import("assets/svg/ic-thoi-gian-hen-noi-soi.svg")
);

const IcDsHenNoiSoiSinhThiet = createSvg(() =>
  import("assets/svg/ic-ds-hen-noi-soi-sinh-thiet.svg")
);
const IcVoiceToText = createSvg(() =>
  import("assets/svg/ic-voice-to-text.svg")
);
const IcCommand = createSvg(() => import("assets/svg/ic-command.svg"));
const IcUnread = createSvg(() => import("assets/svg/ic-unread.svg"));
const IcDsPhieuThu = createSvg(() => import("assets/svg/ic-ds-phieu-thu.svg"));
const IcRotateRight = createSvg(() => import("assets/svg/ic-rotate-right.svg"));
const IcRotateLeft = createSvg(() => import("assets/svg/ic-rotate-left.svg"));
const IcIsofh = createSvg(() => import("assets/svg/ic-isofh-1.svg"));
const IcCom = createSvg(() => import("assets/svg/ic-com.svg"));
const IcLasa = createSvg(() => import("assets/svg/ic-lasa.svg"));
const IcNguyCoCao = createSvg(() => import("assets/svg/ic-nguy-co-cao.svg"));
const IcProtect = createSvg(() => import("assets/svg/ic-protect.svg"));
const IcContact = createSvg(() => import("assets/svg/ic-contact.svg"));
const IcNguoiBenhMoi = createSvg(() =>
  import("assets/svg/ic-nguoi-benh-moi.svg")
);
const IcFaceId = createSvg(() => import("assets/svg/ic-face-id.svg"));
const IcKhamThuong = createSvg(() => import("assets/svg/ic-kham-thuong.svg"));
const IcKhamBHYT = createSvg(() => import("assets/svg/ic-kham-bhyt.svg"));
const IcTap = createSvg(() => import("assets/svg/ic-tap.svg"));
const IcPlay = createSvg(() => import("assets/svg/ic-play.svg"));
const IcDanhMuc1 = createSvg(() => import("assets/svg/ic-danh-muc-1.svg"));
const IcNbTiepTheo = createSvg(() => import("assets/svg/ic-nb-tiep-theo.svg"));
const IcO = createSvg(() => import("assets/svg/ic-o.svg"));
const IcX = createSvg(() => import("assets/svg/ic-x.svg"));
const IcDongGoiHSBA = createSvg(() =>
  import("assets/svg/ic-dong-goi-hsba.svg")
);
const IcLichSuKCB = createSvg(() => import("assets/svg/ic-lich-su-kcb.svg"));
const IcFingerPrint = createSvg(() => import("assets/svg/ic-fingerprint.svg"));

const IcDanhSachPhieuLinh = createSvg(() =>
  import("assets/svg/ic-danh-sach-phieu-linh.svg")
);

const IcDescription = createSvg(() => import("assets/svg/ic-description.svg"));
const IcMemo = createSvg(() => import("assets/svg/ic-memo.svg"));
const IcHoSoDuSinh = createSvg(() => import("assets/svg/ic-ho-so-du-sinh.svg"));
const IcNbTrungTen = createSvg(() => import("assets/svg/ic-nb-trung-ten.svg"));

//png
import Isofh from "assets/images/his-core/logo-isofh.png";
import ImgCcccMatSau from "assets/images/welcome/Matsau.png";
import ImgCcccMatTruoc from "assets/images/welcome/Mattruoc.png";
import DefaultImage from "assets/images/default-image.jpg";
import BgTrangChuLeft from "assets/images/trangChu/bg-trang-chu-left.png";
import BgTrangChuRight from "assets/images/trangChu/bg-trang-chu-right.png";
import ImgSuccessBlue from "assets/images/thuNgan/mhPhuSuccessBlue.png";
import ImageBorder from "assets/images/qms/border-image.png";
import BgDvDieuDuong from "assets/images/trangChu/bg-dv-dieu-duong.png";
import LogoDvDieuDuong from "assets/images/trangChu/logo-dv-dieu-duong.png";
import LogoDvDieuDuongDetail from "assets/images/trangChu/logo-dv-dieu-duong-detail.png";
import MucDoDau from "assets/images/editor/nopain.png";
export const SVG = {
  IcDanhSachPhieuLinh,
  IcO,
  IcX,
  IcPlay,
  IcNbTiepTheo,
  IcCom,
  IcListFeature,
  IcPending,
  IcWaiting,
  IcPrint,
  IcEdit,
  IcEye,
  IcSave,
  IcDelete,
  IcDeleteCircle,
  IcSearch,
  IcSetting,
  IcTiem,
  IcVacxin,
  IcCalendar,
  IcSangLoc,
  IcAdd,
  IcAddFilled,
  IcMinusFilled,
  IcPlusSquareOutlined: PlusSquareOutlined,
  IcMinusSquareOutlined: MinusSquareOutlined,
  IcCalculatorOutlined: CalculatorOutlined,
  IcFullScreenFilled,
  IcProtect,
  IcContact,
  IcNguoiBenhMoi,
  IcFaceId,
  IcKhamThuong,
  IcKhamBHYT,
  IcCall,
  IcLog: ExceptionOutlined,
  IcThucHienTiem,
  IcSuccess,
  IcHDHD,
  IcLichSu,
  IcMaVach,
  IcTiemChung,
  IcCDHATDCN,
  IcDanhMuc,
  IcDanhMuc1,
  IcDanhSachGiayDayCong,
  IcDashboard,
  IcGoiDichVu,
  IcHoSoBenhAn,
  IcHsba,
  IcKeHoachTongHop,
  IcKhamBenh,
  IcKhamSucKhoeHopDong,
  IcKhoMau,
  IcKySo,
  IcNhaThuoc,
  IcDonThuoc,
  IcPhauThuatThuThuat,
  IcPhucHoiChucNang,
  IcQuanLyKho,
  IcQuanLyNoiTru,
  IcQuanLyThongBao,
  IcQuanTriHeThong,
  IcQuyetToanBhyt,
  IcQuanLyDinhDuong,
  IcPhaCheThuoc,
  IcPhacDoDieuTri,
  IcPhacDoDieuTri1,
  IcPhacDoDieuTri2,
  IcSinhHieu,
  IcTheoDoiDieuTri,
  IcThietLap,
  IcThuNgan,
  IcTiepDon,
  IcXetNghiem,
  IcPhone,
  IcXemThongKe2,
  IcDeNghiTamTung,
  IcHuyTamUng,
  IcHoanTamUng,
  IcThuTamUng,
  IcKetLuan,
  IcDrag,
  IcLogout,
  IcLogin,
  IcChangePassword,
  IcShareLogin,
  IcLanguage,
  IconHelp,
  IconVideo,
  IcDocument,
  IcQuestion2,
  IcQuestion,
  IcNotification,
  IcNotificationOutlined,
  IcNotificationIdleOutlined,
  IconAvatar,
  IcApp,
  IcTick,
  IcTick2: IcTick2,
  IcTakePicture,
  IcTakeAvatar,
  IcCamera,
  IcArrowLeft,
  IcUp,
  IcDown: withRotate(IcUp, 180),
  IcLeft: withRotate(IcUp, 270),
  IcRight: withRotate(IcUp, 90),
  IcArrowUp: withRotate(IcArrowDown, 180),
  IcArrowDown,
  IcExpandDown,
  IcExpandRight: withRotate(IcExpandDown, -90),
  IcExpandUp: withRotate(IcExpandDown, -180),
  IcExpandLeft: withRotate(IcExpandDown, 90),
  IcSkip,
  IcArrowBack,
  IcUpload,
  IcDownload,
  IcUploadFile,
  IcXuatFile: IcGuiVaDuyet,
  IcTiemChung1,
  IcDieuTriDaiHan,
  IcReload,
  IcRefresh,
  IcTuyChinhTiepDon,
  IcQrCode,
  IcQrCodeOff,
  IcUpdateStatusQr,
  IcBaoCao,
  IcDanhSachNbDaTiepDon,
  IcBaoCao2,
  IcMail,
  IcOption,
  IcEmptyBox,
  IcHoanDv,
  IcHuyHoanDv,
  IcViewImagePacs,
  IcPdf,
  IcSaoChep,
  IcMore: MoreOutlined,
  IcGiamDinh,
  IcXoaHoSo,
  IcNoData,
  IcChiDinhDichVu,
  IcVatTu,
  IcSuatAn,
  IcDoiDoiTuong,
  IcMoBenhAn,
  IcChuyenDichVu,
  IcInfoFill,
  IcInfo,
  IcThongTinPttt,
  IcThongTinNguoiThucHien,
  IcMau,
  IcHoaChat,
  IcDichVu,
  IcPhongGiuong,
  IcHoiChan,
  IcChiSoSong,
  IcThongTinChung,
  IcList,
  IcListNoBorder,
  IcMoRong,
  IcTiepTuc,
  IcTamDung,
  IcKetThuc,
  IcMoLai,
  IcLaySo,
  IcFullScreen,
  IcShowThuNho,
  IcPlus,
  IcLichSuTiemChung,
  IcExtend,
  IcCollapse,
  IcDanhSachHoaDon,
  IcSend,
  IcGuiCt,
  IcCloseCircle,
  IcCloseCircleFill,
  IcCancel,
  IcLocation,
  IcDieuTriDaiHan,
  IcKiosk,
  IcQms,
  IcScanBieuMau,
  IcChietKhau,
  IcTiepDonThuNgan,
  IcChiaPhieuThu,
  IcMienGiam,
  IcYeuCauHoan,
  IcBanGiaoThuoc,
  IcNgungSuDungThuoc,
  IcNgungThuoc,
  IcToDieuTri,
  IcDieuTriLaoNoiTru,
  IcThongTinConNoiTru,
  IcTongKetKhoaDeNoiTru,
  IcFilter,
  IcConnected,
  IcDisconnect,
  IcThanhToan,
  IcHuyThanhToan,
  IcGrid,
  IcNguoiBenhLgbt,
  IcNguoiBenhNam,
  IcNguoiBenhNu,
  IcGiuongTrong,
  IcNam,
  IcNu,
  IcLgbt,
  IcGridOutlined,
  IcShowFull,
  IcExport,
  IcShowLog,
  IcPhieuSoKet,
  IcQmsThuNgan,
  IcXuatHoaDon,
  IcTiemChungBg,
  IcChoHoan,
  IcDaHoan,
  IcTiepNhan,
  IcTime,
  IcFolder,
  IcFolderUpload,
  IcAddFolder: FolderAddOutlined,
  IcAddFile: FileAddOutlined,
  IcImage,
  IcDone,
  IcTuVan,
  IcGuiVaDuyet,
  IcLoaiPhieu,
  IcMayTinh,
  IcCapCuu,
  IcPhatHanhThe,
  IcSwitchCamera,
  IcWarning,
  IcError,
  IcCheckHsba,
  IcRectangular,
  IcArrowSwap,
  IcTheBaoHiem,
  IcNhiemKhuan,
  IcDVKT,
  IcThietLapKhoChiDinh,
  IcQuanTriKho,
  IcQuanLyThau,
  IcNhapKho,
  IcXuatKho,
  IcDanhSachTonKho,
  IcPhatThuocNgoaiTruRaVien,
  IcVatTuKyGui,
  IcDanhSachDuyetDuocLamSang,
  IcDanhSachNguoiBenhChuaHoanThanhTraHangHoa,
  IcDanhSachSuatAnChuaLinhDuyetTra,
  IcLayMauBenhPham,
  IcThucHienSinhHoaHuyetHoc,
  IcThucHienGiaiPhauBenhViSinh,
  IcDanhSachLuuTruBenhAn,
  IcDanhSachDuyetBaoHiem,
  IcDanhSachNbChoTaoHoSoQtBhyt,
  IcDanhSachHoSoBaoHiemTheoQD4210,
  IcDanhSachHoSoBaoHiemTheoQD130,
  IcDanhSachHoSoDichVuTheoQD130,
  IcDanhSachHoSoBaoHiemDaXoa,
  IcKhaiBaoCongThucPhaChe,
  IcDanhSachPhieuPhaCheThuoc,
  IcDanhSachPhieuXuatPhaCheThuoc,
  IcTiepDon1,
  IcDanhSachNbDaTiepDon1,
  IcDanhSachNbHuyTiepDon,
  IcDanhSachLichHen,
  IcDanhSachLapBenhAn,
  IcDanhSachNbNoiTru,
  IcGiaHanTheChuyenDoiTuong,
  IcQuanLyDieuDuongPhuTrachPhongGiuong,
  IcChoTiepDon,
  IcTiepNhan1,
  IcBieuDoThongKe,
  IcThietLapQuyenKy,
  IcDanhSachPhieuChoKy,
  IcLichSuKy,
  IcDanhMucVaiTro,
  IcQuanLyTaiKhoan,
  IcDanhMucNhanVien,
  IcDanhMucQuyen,
  IcDanhMucNhomTinhNang,
  IcTuyChinhGiaoDienPhanMem,
  IcDanhSachNbSuDungGoi,
  IcDanhMucGoiDichVu,
  IcTongQuanBenhVien,
  IcTongQuanNgoaiTru,
  IcTongQuanDoanhThu,
  IcTongQuanGiuongPhong,
  IcThoiGianChoKham,
  IcThietLapChung,
  IcTachGopPhieuXetNghiem,
  IcTachGopPhieuChiDinhDvkt,
  IcThietLapTichDiem,
  IcThietLapThongSoHangDoi,
  IcThietLapPhieuTaiCacManHinh,
  IcThietLapPhieuLinhTra,
  IcThietLapChonGiuong,
  IcThietLapNhungLink,
  IcThietLapDieuKienChuyenKhoaRaVien,
  IcThietLapLuuTruBenhAn,
  IcThietLapDoiMaNguoiBenh,
  IcTiepDonTiemChung,
  IcDanhSachTiepDonTiemChung,
  IcDanhSachKhamSangLoc,
  IcDanhSachTiem,
  IcDanhSachTheoDoiSauTiem,
  IcDanhSachVaccinDayCongTcqg,
  IcDanhSachDonThuoc,
  IcLienThongGpp,
  IcNhapKhoMau,
  IcDanhSachTonKhoMau,
  IcTruyenPhatMau,
  IcXuatKhoMau,
  IcKioskLaySoThuNgan,
  IcKioskQuetMaQr,
  IcKioskQuetVaGoiNbVaoQuay,
  IcDanhSachPhieuBaoGia,
  IcDanhSachHopDong,
  IcDanhSachPhieuThu,
  IcDanhSachPhieuYeuCauHoan,
  IcDanhSachHoaDonDienTu,
  IcQuanLyTamUng,
  IcDanhSachGiayNghiHuong,
  IcDanhSachNbTuVong,
  IcDanhSachNbRaVien,
  IcDanhSachPhieuTomTatBenhAn,
  IcDanhSachPhieuChungSinh,
  IcDanhSachGiayKskLaiXe,
  IcDanhSachPhieuLinhSuatAn,
  IcDanhSachPhieuTraSuatAn,
  IcDanhSachNbQuanLyDinhDuong,
  IcKhaiBaoChiSoDinhDuongTreEm,
  IcDanhSachGiayChungNhanNghiDuongThai,
  IcLienThongThuocDienTu,
  IcHoiChan1,
  IcMedicalKit,
  IcChePhamMau,
  IcChePhamMauHuy,
  IcPass,
  IcChart,
  IcTaoPhienBanMau,
  IcExpand,
  IcXuatHangLoat,
  IcPerson,
  IcPersonMultiple,
  IcPencel,
  IcHuyCapNhat,
  IcTiepNhanDieuTra,
  IcDoSinhHieuGut,
  IcLogoSakura,
  IcTrinhKy,
  IcTuChoiKy,
  IcNhapChiSo,
  IcCanhBaoCss,
  IcDoThiLuc,
  IcBaoCaoAdr,
  IcCheckCircleGreen,
  IcCircleBack,
  IcYearCalendar,
  IcDongBoGia,
  IcDSTuVanThuoc,
  IcKPIs,
  IcDangKyThuocLao,
  IcDieuTriLao,
  IcTuVanThuoc,
  IcNbDieuTriLao,
  IcModuleHenNoiSoi,
  IcThoiGianHenNoiSoi,
  IcDsHenNoiSoiSinhThiet,
  IcChePhamDD,
  IcThietLapSoLienIn,
  IcVoiceToText,
  IcUnread,
  IcThietLapGiaTriCSS,
  IcDsPhieuThu,
  IcBug: BugOutlined,
  IcRotateRight,
  IcRotateLeft,
  IcQuanLyNhanLuc,
  IcIsofh,
  IcLasa,
  IcNguyCoCao,
  IcQuaHan3Ngay,
  IcQuaHan7Ngay,
  IcQuanLyYeuCau,
  IcTap,
  IcDongGoiHSBA,
  IcCommand,
  IcLichSuKCB,
  IcFingerPrint,
  IcSign: SignatureOutlined,
  IcDescription,
  IcMemo,
  IcHoSoDuSinh,
  IcVitalSign: FundViewOutlined,
  IcNbTrungTen,
};
export const IMG = {
  Isofh,
  ImgCcccMatSau,
  ImgCcccMatTruoc,
  DefaultImage,
  BgTrangChuLeft,
  BgTrangChuRight,
  ImgSuccessBlue,
  ImageBorder,
  BgDvDieuDuong,
  LogoDvDieuDuong,
  LogoDvDieuDuongDetail,
  MucDoDau,
};
export default {
  SVG,
  IMG,
};
