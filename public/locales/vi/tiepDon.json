{"tiepDon": "<PERSON><PERSON><PERSON><PERSON>", "nbTiepTheo": "NB tiếp theo", "vuiLongChonQuayTiepDonTruocKhiGoiSo": "<PERSON>ui lòng chọn quầy trư<PERSON><PERSON> ghi gọi số!", "danhSachNguoiBenhDaTiepDon": "<PERSON><PERSON> s<PERSON>ch ngư<PERSON><PERSON> bệnh đã tiếp đón", "danhSachNguoiBenhHuyTiepDon": "<PERSON><PERSON> s<PERSON>ch người bệnh huỷ tiếp đón", "maDinhDanh": "<PERSON><PERSON> đ<PERSON>nh danh", "quetQRCCCDBHYTmaNBtheRFID": "Quét QR CCCD, BHYT, mã NB, thẻ RFID", "quetMaQR": "Quét mã QR", "lyDoDenKham": "<PERSON><PERSON> do đến khám", "uuTien": "Ưu tiên", "capCuu": "<PERSON><PERSON><PERSON>", "khamSKTQ": "Khám SKTQ", "laUuTien": "<PERSON><PERSON> <PERSON>u tiên", "laCapCuu": "<PERSON><PERSON> c<PERSON><PERSON> c<PERSON>", "loaiDoiTuong": "<PERSON><PERSON><PERSON> đối t<PERSON>", "chonLoaiDoiTuong": "<PERSON><PERSON><PERSON> lo<PERSON>i đối tư<PERSON>", "vuiLongChonLoaiDoiTuong": "<PERSON><PERSON> lòng chọn loại đối tượng", "giuTheBHYT": "Giữ thẻ BHYT", "nbCovid": "NB Covid", "taoTheTam": "Tạo thẻ tạm", "taoTheTamBHYT": "Tạo thẻ tạm BHYT", "chonQuay": "<PERSON><PERSON><PERSON>", "dongQuay": "<PERSON><PERSON><PERSON>", "xemThongKe": "<PERSON><PERSON> th<PERSON> kê", "nhapSTTTiepDon": "Nhập STT tiếp đón", "thongKeNguoiBenh": "THỐNG KÊ NGƯỜI BỆNH", "thuGon": "<PERSON><PERSON>", "hoTenTuoi": "<PERSON><PERSON> tên - tu<PERSON>i", "danhSachGoiNho": "<PERSON><PERSON> s<PERSON>ch g<PERSON>i nhỡ", "maPhong": "Mã phòng", "tenPhong": "<PERSON><PERSON><PERSON> ph<PERSON>ng", "phong": "Phòng", "choKham": "Chờ khám", "dangKham": "<PERSON><PERSON>", "daKL": "Đã KL", "soLuongNguoiBenhTheoPhongKham": "<PERSON><PERSON> l<PERSON> ng<PERSON><PERSON><PERSON> bệnh theo phòng khám", "timMaPhongTenPhong": "T<PERSON>m mã phòng, tên phòng", "timTenNguoiBenh": "<PERSON><PERSON><PERSON> tên ng<PERSON><PERSON> b<PERSON>nh", "ngayDangKy": "<PERSON><PERSON><PERSON> ký", "thoiGianDangKy": "<PERSON>h<PERSON><PERSON> gian đ<PERSON>ng ký", "hoTenNguoiBenh": "<PERSON><PERSON> tên ng<PERSON><PERSON> b<PERSON>nh", "nhapTenNguoiBenh": "<PERSON><PERSON><PERSON><PERSON> tên ng<PERSON><PERSON> b<PERSON>nh", "diaChiNguoiBenh": "<PERSON><PERSON><PERSON> chỉ người bệnh", "nhapDiaChiNguoiBenh": "<PERSON><PERSON><PERSON><PERSON> chỉ người bệnh", "soBHYT": "Số thẻ BHYT", "boQuaCheckThe": "Bỏ qua check thẻ", "tenCongTy": "<PERSON><PERSON>n công ty", "nhapTenCongTy": "<PERSON><PERSON><PERSON><PERSON> tên công ty", "timKiemNguoiBenh": "<PERSON><PERSON><PERSON> k<PERSON> ng<PERSON><PERSON> b<PERSON>nh", "nguoiBenhKSK": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> KSK", "trangThaiKSK": "Trạng thái KSK", "tiepNhanNBKSK": "<PERSON><PERSON><PERSON><PERSON> nhận nhiều NB KSK", "chonNgayDangKy": "<PERSON><PERSON><PERSON> ngày đ<PERSON>ng ký", "timHoTenNguoiBenh": "<PERSON><PERSON><PERSON> họ tên <PERSON> b<PERSON>nh", "timSoDienThoai": "<PERSON><PERSON><PERSON> số điện thoại", "timMaHoSo": "<PERSON><PERSON><PERSON> mã hồ sơ", "timTenCongTy": "T<PERSON>m tên công ty", "timHoTenQrMaNbMahsTheRfid": "<PERSON><PERSON><PERSON> h<PERSON>ê<PERSON>, <PERSON><PERSON>, mã NB, mã HS, Thẻ RFID", "timHoTenQrMaNbMahs": "<PERSON><PERSON><PERSON> h<PERSON>ê<PERSON>, <PERSON><PERSON>, mã NB, mã HS", "nhanVienTiepDon": "<PERSON><PERSON><PERSON> viên tiếp đ<PERSON>", "doiTuongNguoiBenh": "<PERSON><PERSON><PERSON> t<PERSON> ng<PERSON><PERSON><PERSON> b<PERSON>nh", "soTheBaoHiem": "<PERSON><PERSON> thẻ b<PERSON>o hiểm", "suaThongTin": "<PERSON><PERSON><PERSON> thông tin", "giaTriThe": "<PERSON><PERSON><PERSON> trị thẻ", "ttThanhToan": "TT thanh toán", "ttHoan": "TT Hoàn", "khoaChiDinh": "Khoa chỉ định", "khongTinhTien": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h tiền", "chonTinhTien": "<PERSON><PERSON><PERSON> t<PERSON> ti<PERSON>n", "thanhToanSau": "<PERSON>h toán sau", "chiTietNguoiBenhDaTiepDon": "<PERSON> tiết ngư<PERSON>i bệnh đã tiếp đón", "nguoiBenhKSK2": "NB Khám sức khỏe hợp đồng", "chucVuPhongBan": "<PERSON><PERSON><PERSON> vụ - <PERSON><PERSON><PERSON> ban", "hopDong": "<PERSON><PERSON><PERSON>", "inGiayTo": "In giấy tờ", "inXacNhanThucHienTheoDv": "In xác nhận thực hiện theo dịch vụ", "themDichVu": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "tiepNhanNguoiBenhKSK": "T<PERSON>ếp nhận NB KSK", "nhapMaNb": "Nhập mã NB", "hoanTacHuyTiepDon": "<PERSON><PERSON><PERSON> t<PERSON>c hủy tiếp đón", "diaChiHanhChinhKhongHopLe": "Địa chỉ hành ch<PERSON>h không có trong hệ thống. Chọn địa chỉ từ gợi ý của hệ thống", "thongTinCaNhan": "Thông tin cá nhân", "thongTinCaNhanBoSung": "<PERSON>h<PERSON><PERSON> tin cá nhân bổ sung", "nhanDeThemThongTin": "để thêm thông tin cá nhân", "vuiLongNhapTenNguoiBenh": "<PERSON>ui lòng nhập tên ngư<PERSON>i bệnh!", "soDienThoaiSaiDinhDang": "S<PERSON> điện thoại sai định dạng!", "ngayThangNamSinh": "<PERSON><PERSON><PERSON> th<PERSON>g n<PERSON>m sinh", "nhapNgayThangNamSinh": "<PERSON><PERSON><PERSON><PERSON> ngày tháng năm sinh", "ngaySinhSaiDinhDang": "<PERSON><PERSON><PERSON> sinh sai định dạng!", "vuiLongNhapNgaySinh": "<PERSON>ui lòng nhập ngày sinh!", "tuoi": "<PERSON><PERSON><PERSON>", "soNhaThonXom": "Số nhà/ Thôn/ Xóm", "vuiLongNhapSoNhaThonXom": "<PERSON><PERSON> lòng nhập <PERSON> nhà/ Thôn/ Xóm", "snThonXom": "SN/ Thôn/ Xóm", "phuongXaTinhThanh": "Phường/Xã, Tỉnh/TP", "vuiLongNhapDiaChi": "<PERSON><PERSON> lòng nhập địa chỉ!", "vuiLongNhapDiaChiCap1": "<PERSON>ui lòng nhập Tỉnh/Thành Phố!", "vuiLongNhapDiaChiCap2": "<PERSON>ui lòng nhập Tỉnh/Thành Phố!", "vuiLongNhapDiaChiCap3": "<PERSON><PERSON> lòng nhập Phường/Xã, Tỉnh/Thành Phố!", "loaiGiayToTuyThan": "Loại gi<PERSON>y tờ tuỳ thân", "chonLoaiGiayToTuyThan": "<PERSON><PERSON><PERSON> lo<PERSON>i gi<PERSON>y tờ tùy thân", "maSoGiayToTuyThan": "Mã số giấy tờ tùy thân", "nhapMaSoGiayToTuyThan": "<PERSON><PERSON><PERSON><PERSON> mã số giấy tờ tùy thân", "thongTinBHYT": "Thông tin BHYT", "soBaoHiem": "<PERSON><PERSON> b<PERSON>o <PERSON>", "vuiLongNhapSoBaoHiem": "<PERSON>ui lòng nhâp số bảo hiểm!", "mucHuong": "<PERSON><PERSON><PERSON> hưởng", "baoHiemTuNgay": "<PERSON><PERSON><PERSON> từ ngày", "tuNgaySaiDinhDang": "Từ ngày sai định dạng!", "vuiLongChonTuNgay": "<PERSON>ui lòng chọn từ ngày!", "baoHiemDenNgay": "<PERSON><PERSON><PERSON> hi<PERSON> đến ng<PERSON>y", "denNgaySaiDinhDang": "<PERSON><PERSON>n ngày sai định dạng!", "vuiLongChonDenNgay": "<PERSON>ui lòng chọn đến ngày!", "nhanDeThemThongTinBHYT": "để thêm thông tin BHYT ", "nhanDeThemThongTinKhac": "để thêm thông tin kh<PERSON>c ", "chanDoanNoiGioiThieu": "<PERSON><PERSON><PERSON> đo<PERSON> nơi giới thiệu", "nhapNoiDung": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> dung", "noiDangKy": "<PERSON><PERSON><PERSON> đ<PERSON>ng ký", "chonNoiDangKy": "<PERSON><PERSON><PERSON> n<PERSON>i đăng ký", "vuiLongChonNoiDangKy": "<PERSON>ui lòng chọn nơi đăng ký!", "noiGioiThieu": "<PERSON><PERSON><PERSON> gi<PERSON>i thiệu", "chonNoiGioiThieu": "<PERSON><PERSON><PERSON> n<PERSON>i giới thiệu", "vuiLongChonNoiGioiThieu": "<PERSON>ui lòng chọn nơi giới thiệu!", "thoiGianDu5NamLienTuc": "TG đủ 5 năm liên tục", "thoiGianMienCungChiTra": "<PERSON><PERSON><PERSON><PERSON> gian miễn cùng chi trả", "mienCungChiTra": "<PERSON><PERSON><PERSON> c<PERSON>ng chi trả", "chuyenTuyen": "chuy<PERSON><PERSON> tuyến", "taiLenGiayChuyenTuyen": "<PERSON><PERSON><PERSON> lên g<PERSON><PERSON><PERSON> chuyển tuyến", "taiLenGiayMienCungChiTra": "<PERSON><PERSON><PERSON> lên g<PERSON><PERSON><PERSON>", "coLichHenKhamLai": "<PERSON><PERSON> l<PERSON>ch hẹn khám lại", "taiTepLen": "<PERSON><PERSON><PERSON> lên", "taiLenGiay": "<PERSON><PERSON><PERSON> l<PERSON>n <PERSON>", "nhanVaoIconMayAnhDeChupAnh": "Nhấn vào icon m<PERSON><PERSON> <PERSON>nh để tải ảnh chụp trự<PERSON> tiếp", "nhanVaoIconDeTaiTepTuMay": "<PERSON>hấn vào hình đám mây để tải tệp từ máy", "nhanVaoDauXDeXoaDuLieu": "Nhấn biểu tượng x ở góc ảnh để xóa dữ liệu đã tải lên", "henKhamLai": "hẹn khám lại", "thongTinBoSung": "<PERSON><PERSON><PERSON><PERSON> tin bổ sung", "hoTenNguoiBaoLanh": "<PERSON><PERSON> tên ng<PERSON><PERSON><PERSON> b<PERSON><PERSON> l<PERSON>nh", "nhapHoTenNguoiBaoLanh": "<PERSON><PERSON><PERSON><PERSON> họ tên ng<PERSON><PERSON><PERSON> b<PERSON>o lãnh", "vuiLongNhapHoTenNguoiBaoLanh": "<PERSON><PERSON> lòng nhập họ tên ngư<PERSON>i bảo lãnh!", "moiQhVoiNguoiBenh": "Mối qh với NB", "chonMoiQlVoiNguoiBenh": "<PERSON><PERSON>n mối qh với NB", "vuiLongChonMoiQuanHeVoiNguoiBenh": "Vui lòng chọn mỗi qh với NB!", "cmndNguoiBaoLanh": "CMND ngư<PERSON><PERSON> b<PERSON><PERSON> l<PERSON>", "nhapCMNDNguoiBaoLanh": "Nhập CMND ngư<PERSON><PERSON> b<PERSON>o lãnh", "nguonNguoiBenh": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON><PERSON> b<PERSON>nh", "chonNguonNguoiBenh": "<PERSON><PERSON><PERSON> nguồn ng<PERSON><PERSON><PERSON> b<PERSON>nh", "nguoiGioiThieu": "<PERSON><PERSON><PERSON><PERSON> giới thiệu", "chonNguoiGioiThieu": "<PERSON><PERSON><PERSON> ng<PERSON>ời giới thiệu", "vuiLongChonNguoiGioiThieu": "<PERSON>ui lòng chọn người giới thiệu!", "nhapQuaSoKyTuChoPhep": "<PERSON><PERSON>ậ<PERSON> quá số ký tự cho phép!", "loaiMienGiam": "<PERSON><PERSON><PERSON> mi<PERSON>", "chonLoaiMienGiam": "<PERSON><PERSON><PERSON> lo<PERSON>i miễn g<PERSON>", "nguoiDuyetMienPhi": "<PERSON><PERSON><PERSON><PERSON> du<PERSON> miễn phí", "phanLoai": "<PERSON><PERSON> lo<PERSON>", "chonPhanLoai": "<PERSON><PERSON>n phân lo<PERSON>i", "chonNguoiDuyetMienPhi": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> du<PERSON>t miễn phí", "diaChiTaiNuocNgoai": "Địa chỉ tại nước ngoài", "nhapDiaChiTaiNuocNgoai": "<PERSON><PERSON><PERSON><PERSON> địa chỉ ở nước ngoài", "maSoBHXH": "Mã số bảo hiểm xã hội", "nhapMaSoBHXH": "Nhậ<PERSON> mã số BHXH", "daXacThucThongTin": "<PERSON><PERSON> xác thực thông tin", "loaiCapCuu": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> c<PERSON>u", "chonLoaiCapCuu": "<PERSON><PERSON><PERSON> lo<PERSON> c<PERSON><PERSON> c<PERSON>u", "vuiLongChonLoaiCapCuu": "<PERSON>ui lòng chọn lo<PERSON>i cấp cứu!", "nguyenNhanNhapVien": "<PERSON><PERSON><PERSON><PERSON> nhân nhập viện", "chonNguyenNhanNhapVien": "<PERSON><PERSON><PERSON> nguyên nhân nhập viện", "nguyenNhanTaiNanThuongTich": "<PERSON><PERSON><PERSON><PERSON> nhân tai nạn thương tích", "chonNguyenNhanTaiNanThuongTich": "<PERSON><PERSON><PERSON> nguyên nhân tai nạn thương tích", "viTriChanThuong": "<PERSON><PERSON> trí chấn th<PERSON>", "chonViTriChanThuong": "<PERSON><PERSON><PERSON> vị trí chấn thư<PERSON>ng", "thoiGianCapCuu": "<PERSON><PERSON><PERSON><PERSON> gian c<PERSON><PERSON> c<PERSON>u", "chonThoiGianCapCuu": "<PERSON><PERSON><PERSON> th<PERSON>i gian cấ<PERSON> c<PERSON>u", "khongCoNguoiThanDiKem": "<PERSON><PERSON><PERSON><PERSON> có người thân đi kèm", "donVi": "Đơn vị", "chonDonVi": "<PERSON><PERSON>n đơn vị", "vuiLongChonLoaiDonVi": "<PERSON>ui lòng chọn loại đơn vị!", "chucVu": "<PERSON><PERSON><PERSON> v<PERSON>", "chonChucVu": "<PERSON><PERSON><PERSON> ch<PERSON> v<PERSON>", "vuiLongChonChucVu": "Vui lòng chọn loại chức vụ!", "quanHam": "<PERSON><PERSON><PERSON> h<PERSON>m", "chonQuanHam": "<PERSON><PERSON><PERSON> quân hàm", "vuiLongChonQuanHam": "Vui lòng chọn quân hàm!", "nguoiDaiDien": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "chonNguoiDaiDien": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> đại <PERSON>n", "boKiemTraThe": "Bỏ kiểm tra thẻ", "banCoChacChanMuonBoKiemTraThe": "Bạn có chắc chắn muốn bỏ qua kiểm tra thẻ không?", "anhHuongKhiBoKiemTraThe": "Bỏ qua kiểm tra thẻ với cổng giám định có thể dẫn đến các dịch vụ<br /> của người bệnh không được cơ quan BHYT quyết toán!", "thongTinTheChinhXac": "Thông tin thẻ chính xác", "suDungThongTinThe": "Sử dụng thông tin thẻ", "thongTinDung": "<PERSON>h<PERSON><PERSON> tin đúng", "maThe": "Mã thẻ", "diaChiThe": "Địa chỉ thẻ", "noiDangKyKCBBD": "Nơi ĐK KCBBD", "ngay5NamLT": "Ngày 5 năm LT", "maTheMoi": "Mã thẻ mới", "giaTriTheMoi": "G<PERSON><PERSON> trị thẻ mới", "maSoBHXH2": "Mã số BHXH", "maKhuVuc": "Mã khu vực", "chonMaKhuVuc": "<PERSON><PERSON>n mã khu vực", "ketQuaDieuTri": "<PERSON><PERSON><PERSON> quả điều trị", "danhSachNguoiBenhTrungThongTinHanhChinh": "<PERSON><PERSON> s<PERSON>ch <PERSON> bệnh trùng - khớp thông tin hành ch<PERSON>h", "timSDT": "Tìm SĐT", "timMaNb": "<PERSON><PERSON><PERSON> mã NB", "tenNb": "Tên NB", "timTenNb": "<PERSON><PERSON><PERSON> tên N<PERSON>", "timSdtNguoiBaoLanh": "Tìm SĐT người bảo l<PERSON>nh", "cmtHc": "CMT/HC", "timCmtHc": "Tìm CMT/HC", "timDiaChi": "<PERSON><PERSON><PERSON> đ<PERSON> chỉ", "thoiGianChuaBenhGanNhat": "<PERSON>h<PERSON><PERSON> gian kh<PERSON>m chữa b<PERSON>nh gần nhất", "saiThongTin": "<PERSON> thông tin ", "vuiLongDienHoVaTen": "Vui lòng điền Họ và tên!", "vuiLongDienSoBaoHiem": "<PERSON>ui lòng điền Số bảo hiểm!", "huyTiepDon": "<PERSON><PERSON><PERSON> ti<PERSON><PERSON>", "huyTiepDonThanhCong": "<PERSON><PERSON> hoàn tác việc hủy tiếp đón. Bệnh nhân trở lại trạng thái tiếp đón ban đầu", "xacNhanHuyTiepDon": "<PERSON><PERSON><PERSON><PERSON> thay đổi dữ liệu sẽ không đượ<PERSON> lưu,<br /> bạn có chắc chắn muốn hủy tiếp đón?", "luuVaDangKyKhamCapCuu": "<PERSON><PERSON><PERSON> và đăng ký khám cấp c<PERSON>u", "trungDichVu": "<PERSON><PERSON><PERSON><PERSON> d<PERSON> v<PERSON>", "xacNhanKeTrungDichVu": "<PERSON><PERSON><PERSON> vụ {0} đã tồn tại. <br> Chắc chắn muốn chỉ định trùng dịch vụ?", "chiDinhDichVu": "Chỉ đ<PERSON>nh dịch vụ", "chonNhomDichVu": "<PERSON><PERSON><PERSON> n<PERSON> d<PERSON>ch vụ", "timTenDichVuPhongThucHien": "<PERSON><PERSON><PERSON> tên d<PERSON> v<PERSON>, ph<PERSON><PERSON> thực hiện", "maDV": "Mã DV", "donGiaKhongBh": "Đơn gi<PERSON> không BH", "donGiaBH": "Đơn giá BH", "sl": "SL", "donGia": "Đơn giá", "daTT": "Đã TT", "doiPhong": "Đổ<PERSON> phòng", "xoaDichVu": "<PERSON>ó<PERSON> v<PERSON>", "dichVuDaChon": "<PERSON><PERSON><PERSON> vụ đã chọn", "dangKiemTraTheBHYT": "<PERSON>ang kiểm tra thông tin thẻ BHYT với Cổng giám định bảo hiểm. Vui lòng đợi trong giây lát!", "diaChiHanhChinhKhongCoTrongHeThong": "Địa chỉ hành ch<PERSON>h không có trong hệ thống", "khongTimThayThongTinBenhNhan": "Không tìm thấy thông tin bệnh nhân!", "layThongTinBaoHiemThatBai": "<PERSON><PERSON><PERSON> thông tin thẻ bảo hiểm thất bại", "nhapDayDuThongTinDoiTuongQuanNhan": "<PERSON><PERSON> lòng nhập đầy đủ thông tin dành cho đối tượng Quân nhân!", "nhapDayDuThongTinDoiTuongCapCuu": "<PERSON><PERSON> lòng nhập đầy đủ thông tin dành cho đối tượng cấp cứu!", "banCoMuonBoQuaKiemTraThe": "bạn có muốn bỏ qua kiểm tra thẻ không?", "banCoMuonTiepTucTiepDonNguoiBenhCapCuu": "Bạn có muốn tiếp tục tiếp đón người bệnh cấp cứu không?", "loiValidateDuLieu": "Lỗi validate d<PERSON> liệu", "themMoiThanhCongDuLieuDichVu": "Thêm mới thành công dữ liệu dịch vụ!", "xoaBanGhiThanhCong": "<PERSON><PERSON><PERSON> bản ghi thành công", "vuiLongChonQuayTiepDon": "<PERSON>ui lòng chọn quầy tiếp đón!", "quayTuChon": "<PERSON><PERSON><PERSON><PERSON> tự chọn", "danhSachDichVuDaChiDinh": "<PERSON><PERSON> s<PERSON>ch dịch vụ đã chỉ định", "keThemDichVu": "<PERSON><PERSON> thêm <PERSON>V", "vuiLongChonNguoiBenh": "<PERSON>ui lòng chọn ng<PERSON>ời bệnh!", "dvTrongHopDongKSK": "DV trong hợp đồng KSK", "soTienChinhXacDuocXacDinhTaiQuayThuNganSauApDungChinhSachGiamGia": "Số tiền chính xác đư<PERSON>c xác định tại quầy thu ngân sau khi áp dụng các chính sách giảm giá nếu có.", "nguoiBenhCungChiTra": "NB cùng chi trả", "phuThu": "<PERSON><PERSON> thu", "tienBHChiTra": "Tiền BH chi trả", "tienNbTuTra": "Tiền NB tự trả", "chuaThanhToan": "<PERSON><PERSON><PERSON> to<PERSON>", "mienGiamTuNgay": "Miễn CCT từ ngày", "mienGiamDenNgay": "Miễn CCT đ<PERSON><PERSON> ng<PERSON>y", "xoaAnh": "<PERSON><PERSON><PERSON>", "doiTuongBaoHiemChiDuocChon1DvKham": "<PERSON><PERSON><PERSON> tượng bảo hiểm chỉ được chọn 1 DV khám!", "khongTheSuaThongTinNbDaCoPhieuThuDaThanhToan": "<PERSON><PERSON><PERSON>ng thể sửa thông tin, Nb đã có phiếu thu đã thanh toán", "diaChiTheBHYT": "Địa chỉ thẻ BHYT", "nhapDiaChiBHYT": "<PERSON><PERSON><PERSON><PERSON> địa chỉ BHYT", "chinhSuaThongTin": "Chỉnh sửa thông tin", "chiTietThongTinNb": "<PERSON> tiết thông tin ngư<PERSON>i bệnh", "chuaChonQuayTiepDon": "<PERSON><PERSON><PERSON> chọn qu<PERSON>y tiếp đón", "xacNhanDoiTenNguoiBenh": "<PERSON><PERSON><PERSON> nhận đổi tên nb \"{0}\" thành \"{1}\"", "thoiGianThucHien": "<PERSON><PERSON><PERSON><PERSON> gian th<PERSON> hi<PERSON>n", "thoiGianChiDinh": "<PERSON>h<PERSON>i gian chỉ định", "taiGiayLenThanhCong": "<PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON> lên thành công", "capNhatThanhCong": "<PERSON><PERSON><PERSON> nh<PERSON>t thành công", "timMaThanhToanHDKSK": "<PERSON><PERSON><PERSON> mã thanh toán HĐ KSK", "baoHiemApDungTuNgay": "<PERSON><PERSON><PERSON> dụng từ ngày", "baoHiemApDungDenNgay": "<PERSON><PERSON><PERSON> hi<PERSON> áp dụng đến ngày", "kiemTraTheBh": "<PERSON><PERSON><PERSON> tra thẻ BH", "quanLyTiepDon": "<PERSON><PERSON><PERSON><PERSON> lý tiếp đ<PERSON>", "cacDichVuPhaiChonPhong": "<PERSON><PERSON><PERSON> vụ phải chọn phòng", "nguoiBenhChuaXacNhanDanhTinh": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> chưa xác nhận danh t<PERSON>h", "bacSi": "<PERSON><PERSON><PERSON>", "banCoMuonXoaDichVu": "Bạn có muốn xóa dịch vụ", "dsNbDaTiepDon": "DS NB đã tiếp đón", "baoCaoTiepDon": "<PERSON><PERSON><PERSON> c<PERSON>o tiếp đón", "thongKeTiepDon": "<PERSON><PERSON><PERSON><PERSON> kê tiếp đón", "phatHanhTheRFID": "<PERSON><PERSON><PERSON> h<PERSON>nh thẻ RFID", "huyTheRFID": "Huỷ thẻ RFID", "xacNhanHuyTheRFID": "Bạn có chắc chắn huỷ thẻ RFID của người bệnh {0} không?", "huyTheRFIDThanhCong": "Huỷ thẻ RFID thành công!", "huyTheRFIDThatBai": "Huỷ thẻ RFID thất bại!", "phatHanhTheRFIDThanhCong": "<PERSON><PERSON><PERSON> hành thẻ RFID thành công!", "phatHanhTheRFIDThatBai": "<PERSON><PERSON><PERSON> hành thẻ RFID thất bại!", "phatHanhLaiTheRFID": "<PERSON><PERSON><PERSON> hành lại thẻ RFID", "maSoThe": "Mã số thẻ", "vuiLongNhapMaSoThe": "<PERSON><PERSON> lòng nhập mã số thẻ", "taiLenAnhDaiDien": "<PERSON><PERSON><PERSON> lên <PERSON>nh đ<PERSON>n", "thongTuyen": "<PERSON><PERSON><PERSON><PERSON> tuy<PERSON>n", "kiemTraLaiPhanMemiSofHTool": "<PERSON>ết n<PERSON>i với phần mềm iSofHTools không thành công", "khamMoi": "<PERSON><PERSON><PERSON><PERSON> mới", "doiDichVuKham": "Đổi d<PERSON><PERSON> v<PERSON>", "doiDichVu": "Đ<PERSON><PERSON> d<PERSON><PERSON> vụ", "chonDichVuMoi": "<PERSON><PERSON><PERSON> d<PERSON> v<PERSON> mới", "danhSachLichHen": "<PERSON><PERSON> s<PERSON>ch lịch hẹn", "timMaHoSoNguoiBenh": "<PERSON><PERSON><PERSON> mã hồ sơ ng<PERSON><PERSON><PERSON> b<PERSON>nh", "timTheoTenNguoiBenh": "<PERSON><PERSON><PERSON> theo tên ng<PERSON><PERSON> b<PERSON>nh", "timTheoNgayHen": "<PERSON><PERSON><PERSON> theo ng<PERSON>y hẹn", "ngayHen": "Ngày hẹn", "loaiLichHen": "<PERSON><PERSON><PERSON> lịch hẹn", "nhacLichHen": "<PERSON><PERSON><PERSON><PERSON> lịch hẹn", "dvHen": "DV hẹn", "nhacLich": "<PERSON><PERSON><PERSON><PERSON>", "lenLichHenKhamThucHienDvkt": "<PERSON><PERSON><PERSON> lịch hẹn khám / Thực hiện DVKT", "tenDvHenThucHien": "<PERSON>ên DV hẹn thực hiện", "phongThucHien": "<PERSON><PERSON><PERSON> thự<PERSON> hi<PERSON>n", "loaiHinhThanhToan": "<PERSON><PERSON><PERSON> hình <PERSON>h toán", "bsKham": "BS khám", "caiDatHienThiDichVu": "Cài đặt hiển thị dịch vụ", "hienThiTatCaDv": "<PERSON><PERSON><PERSON> thị tất cả dv", "hienThiDvTheoKhuVuc": "<PERSON><PERSON><PERSON> thị dv theo khu vực", "choKhamThuong": "CK thường", "choKhamUuTien": "CK ƯT", "slNbTheoPk": "SL NB theo PK", "tuyChinhHienThi": "<PERSON><PERSON><PERSON> chỉnh hiển thị", "hienThiPopup": "<PERSON><PERSON><PERSON> thị popup", "luonHienThi": "<PERSON><PERSON><PERSON> hiển thị", "thongTinSinhHieu": "<PERSON><PERSON><PERSON><PERSON> tin sinh hiệu", "nhanDeThemThongTinSinhHieu": "đ<PERSON> thêm thông tin sinh hiệu", "vuiLongNhapDuNgayThangNamSinh": "<PERSON><PERSON> lòng nh<PERSON><PERSON> đủ <PERSON>, th<PERSON><PERSON>, n<PERSON><PERSON> sinh", "chonBenhAnDaiHan": "<PERSON><PERSON><PERSON> b<PERSON>nh <PERSON>n dài hạn", "benhAnDaiHan": "Chọn BA dài hạn", "chuaNhapNoiGioiThieuThucHienTiepDonNguoiBenhTraiTuyen": "<PERSON><PERSON><PERSON> nhập nơi giới thiệu. Thực hiện tiếp đón người bệnh TRÁI TUYẾN?", "maHopDong": "<PERSON><PERSON> hợp đồng", "maHoSo": "<PERSON><PERSON> hồ sơ", "nhapDeTimKiemMaHopDong": "<PERSON><PERSON><PERSON><PERSON> để tìm kiếm mã hợp đồng", "tenHopDong": "<PERSON><PERSON><PERSON><PERSON><PERSON> đ<PERSON>ng", "nhapDeTimKiemTenHopDong": "<PERSON><PERSON><PERSON><PERSON> để tìm kiếm tên hợp đồng", "chonCongTy": "<PERSON><PERSON><PERSON> công ty", "tenNguoiBenh": "<PERSON><PERSON><PERSON> b<PERSON>nh", "congTy": "<PERSON><PERSON>ng ty", "vuiLongNhapLoaiGiayToTuyThan": "<PERSON><PERSON> lòng nhập lo<PERSON>i giấy tờ tùy thân", "dichVuBVE": {"dsDvChiDinhTuBenhVienE": "<PERSON><PERSON> sách DV chỉ định từ Bệnh viện E", "soPhieuBVE": "Số phiếu BVE", "maDichVu": "Mã d<PERSON>ch vụ", "tenDichVu": "<PERSON><PERSON><PERSON> v<PERSON>", "ngayChiDinhBVE": "Ngày chỉ định BVE", "maHsBVE": "Mã HS BVE", "luuY": "Lưu ý: DV Không liên thông được nếu không trùng mã dịch vụ và mã phòng"}, "daCheckin": "Đã checkin", "hienThiDichVuDuocKeTuTiepDon": "<PERSON><PERSON><PERSON> thị dịch vụ được kê từ tiếp đón", "taiLenThongTin": "<PERSON><PERSON><PERSON> lên thông tin", "loaiDVApDung": "Loại DV áp dụng", "macDinhHienThiLoaiDV": "Mặc định hiển thị loại DV", "daCheckinChoNBVaoPhongKham": "<PERSON><PERSON> checkin cho người bệnh vào phòng khám", "tong": "Tổng", "timHoTenQrMaNbMaHs": "<PERSON><PERSON><PERSON> h<PERSON> tên, <PERSON><PERSON>, mã NB, mã hs", "100ktc": "100% KTC", "hangBenhVien": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> viện", "tuyen": "<PERSON><PERSON><PERSON><PERSON>", "nbSuDungGoi": "NB sử dụng gói", "canTiepNhanKsk": "<PERSON><PERSON><PERSON> ti<PERSON><PERSON> nhận KSK", "canHuyTiepNhanKsk": "c<PERSON>n h<PERSON><PERSON> ti<PERSON><PERSON> nhận KSK", "canHuyHoanThanhKsk": "c<PERSON>n h<PERSON>y hoàn thành KSK", "thongTinTaiKhoanIVIE": "Thông tin tài khoản IVIE", "ngayVao": "Ngày vào", "ngayRa": "<PERSON><PERSON>y ra", "ngayCapGiayToTuyThan": "<PERSON><PERSON><PERSON> c<PERSON>p gi<PERSON>y tờ tùy thân", "nhapNgayCapGiayToTuyThan": "<PERSON><PERSON><PERSON><PERSON> ngày cấp gi<PERSON>y tờ tùy thân", "noiCapGiayToTuyThan": "<PERSON><PERSON><PERSON> cấp gi<PERSON>y tờ tùy thân", "nhapNoiCapGiayToTuyThan": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>i cấp gi<PERSON>y tờ tùy thân", "vuiLongChonItNhatMotDvDeXoa": "<PERSON><PERSON> lòng chọn ít nhất 1 dịch vụ để xoá", "banCoChacMuonXoaDv": "Bạn sẽ xoá các dịch vụ này của người bệnh?", "xacNhanXoaDv": "<PERSON><PERSON><PERSON>n xoá dịch vụ", "ngayHenKham": "Ngày hẹn khám", "thuocDangSuDung": "<PERSON><PERSON><PERSON>c đang sử dụng", "nguoiBenhConHoSoChuaThanhToan": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> còn hồ sơ chưa thanh toán mã hồ sơ {{maHs}} ngày {{thoiGian}}{{loaiDoiTuong}}. Bạn có muốn tiếp đón lư<PERSON>t khám mới?", "nguoiBenhConHoSoBaoHiemChuaThanhToan": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> còn hồ sơ bảo hiểm chưa thanh toán mã hồ sơ {{maHs}} ngày {{thoiGian}}{{loaiDoiTuong}}. <PERSON><PERSON> tạo hồ sơ mới vui lòng chọn đối tượng <PERSON>ông bảo hiểm", "nguoiBenhTonTaiHoSo": "<PERSON><PERSON><PERSON><PERSON> bệnh tồn tại hồ sơ {{maHs}} ngày {{thoiGian}}{{loaiDoiTuong}}. Bạn có muốn tiếp đón lượt khám mới?", "daTonTaiHoSoBaoHiemTrongNgay": "<PERSON><PERSON> tồn tại hồ sơ bảo hiểm trong ngày mã hồ sơ {{maHs}} ngày {{thoiGian}}{{loaiDoiTuong}}. <PERSON><PERSON> tạo hồ sơ mới vui lòng chọn đối tượng <PERSON>ông bảo hiểm", "nguoiBenhTonTaiMaHoSoTrongNgay": "<PERSON><PERSON><PERSON><PERSON> bệnh tồn tại mã hồ sơ {{maHs}} trong ngày{{loaiDoiTuong}}. <PERSON>ui lòng tiếp tục sử dụng hồ sơ cũ", "chon": "<PERSON><PERSON><PERSON>", "tiepTucSuDungMaHoSoCu": "<PERSON><PERSON><PERSON><PERSON> tục sử dụng mã hồ sơ cũ", "boQuaDieuKienThanhToan": "Bỏ qua điều kiện thanh toán", "taoMaHoSoKhongBaoHiemMoi": "<PERSON><PERSON>o mã hồ sơ không bảo hiểm mới", "taoMaKhamChoNgayHienTai": "Tạo mã khám cho ngày hiện tại", "maBhxhNguoiBaoLanh": "Mã BHXH người bảo lãnh", "nhapMaBhxhNguoiBaoLanh": "Nhập mã BHXH người bảo lãnh", "khongTonTaiMaNguoiBenh": "<PERSON><PERSON><PERSON><PERSON> tồn tại mã ng<PERSON><PERSON>i b<PERSON>nh {0}", "tiepDonNoiTruYeuCauKhoaNhapVien": "Ti<PERSON><PERSON> đón nội trú yêu cầu khoa nhập viện", "tiepDonNoiTruYeuCauChanDoanVaoVien": "Tiế<PERSON> đón nội trú yêu cầu chẩn đoán vào viện", "tiepDonNoiTruYeuCauCanNangChoTreDuoi1Tuoi": "Tiế<PERSON> đón nội trú yêu cầu cân nặng cho trẻ dưới 1 tuổi", "nguoiBenhConHoSoChuaThanhToanDeTaoHoSoMoiVuiLongChonDoiTuongKhongBaoHiem": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> còn hồ sơ Bảo hiểm chưa thanh toán mã hồ sơ {{maHoSo}}  ngày {{ngay}}. <PERSON><PERSON> tạ<PERSON> hồ sơ mới vui lòng chọn đối tượng không bảo hiểm.", "nhapDayDuTamTrungVaTamThu": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON> đủ Tâm trương và Tâm thu", "daHetSttDoiVaoQuay": "<PERSON><PERSON> hết số thứ tự đợi vào quầy", "soLuongNguoiBenhDaTiepDon": "<PERSON><PERSON> lượng ngư<PERSON>i bệnh đã tiếp đón", "tenMaPhong": "Mã + Tên phòng", "thietLapHienThiPhong": "<PERSON><PERSON><PERSON><PERSON> lập hiển thị phòng", "tuyChinhHienThiO": "<PERSON><PERSON><PERSON> chỉnh hiển thị ô", "checkInDichVu": "<PERSON><PERSON> d<PERSON> vụ", "canHoSoDeTiepNhan": "<PERSON><PERSON><PERSON> chọ<PERSON> hồ sơ để tiếp nhận", "themNguoiGioiThieu": "<PERSON><PERSON><PERSON><PERSON> người giới thiệu", "khongTimThayDvTrongDmPhong": "<PERSON><PERSON><PERSON>ng tìm thấy {{dichVu}}; mã phòng({{maPhong}}) trong danh mục", "formatDateTime": "{{gio}} giờ {{phut}} ph<PERSON><PERSON>, ngày {{ngay}} tháng {{thang}} năm {{nam}}", "xoaLichHenThanhCong": "<PERSON><PERSON><PERSON> l<PERSON>ch hẹn khám thành công", "themLichHenThanhCong": "<PERSON><PERSON><PERSON><PERSON> lịch hẹn thành công", "tinhTienErrMsg": "<b>{{message}} </b> <br/> Bạn có chắc chắn muốn <b> tiế<PERSON> tục chỉ định dịch vụ </b>?", "vuiLongNhapMaSoGiayToTuyThan": "<PERSON><PERSON> lòng nhập mã số giấy tờ tùy thân!", "vuiLongNhapSDThoacCCCD/CMND": "<PERSON>ui lòng nhập SĐT hoặc CCCD/ CMND!", "vuiLongChanDoanNoiGioiThieu": "<PERSON>ui lòng chẩn đoán nơi giới thiệu!", "layDLSinhHieu": "<PERSON><PERSON><PERSON> <PERSON>h hi<PERSON>u", "themMoiLichHenKhamNb": "Thê<PERSON> mới lịch hẹn khám NB", "themMoiNbKhamSangLoc": "Thêm mới NB khám sàng lọc", "taiFileMau": "Tải file mẫu", "chonSheetImport": "Chọn sheet import", "chonDongBatDauImport": "Chọn dòng bắt đầu import", "taiFileLen": "<PERSON><PERSON><PERSON> file lên", "dangKyHangBangLai": "Đăng ký hạng bằng lái", "chonDangKyHangBangLai": "<PERSON><PERSON>n đăng ký hạng bằng lái", "dongBoNbNgoaiVien": "Đồng bộ NB ngoại viện", "daDongBoNbNgoaiVien": "<PERSON><PERSON> đồng bộ NB ngoại viện", "dongBoNguoiBenhSangPhanMemGutThanhCong": "<PERSON><PERSON><PERSON> bộ ng<PERSON><PERSON><PERSON> b<PERSON><PERSON> sang phần mềm GUT thành công", "maHoSoNgoaiVien": "<PERSON><PERSON> hồ sơ ngoại viện", "maNguoiBenhNgoaiVien": "<PERSON><PERSON> ng<PERSON><PERSON><PERSON> b<PERSON>nh ngoại viện", "timMaNbNgoaiVien": "<PERSON><PERSON><PERSON> mã ng<PERSON><PERSON>i bệnh ngoại viện", "diaChiCongTy": "Địa chỉ công ty", "maSoThueCongTy": "Mã số thuế công ty", "soTaiKhoanCongTy": "Số tài k<PERSON>n công ty", "nhapDiaChiCongTy": "<PERSON><PERSON><PERSON><PERSON> địa chỉ công ty", "nhapMaSoThueCongTy": "<PERSON>hâ<PERSON> mã số thuế công ty", "nhapSoTaiKhoanCongTy": "<PERSON><PERSON><PERSON><PERSON> số tài k<PERSON>n công ty", "xemLichSuSinhHieu": "<PERSON><PERSON> l<PERSON>ch sử sinh hiệu", "doSinhHieu": "<PERSON><PERSON> hi<PERSON>u", "taoTaiKhoanMatKhauIvie": "<PERSON><PERSON><PERSON> <PERSON>, mậ<PERSON> kh<PERSON>u IVIE", "maSoThueSaiDinhDang": "Mã số thuế sai định dạng!", "soTaiKhoanSaiDinhDang": "Số tài khoản sai định dạng!", "timTheoMaNguoiBenh": "<PERSON><PERSON><PERSON> theo mã ng<PERSON><PERSON><PERSON> b<PERSON>nh", "timTheoMaNguoiBenhNgoaiVien": "<PERSON><PERSON><PERSON> theo mã ng<PERSON><PERSON>i bệnh ngoại viện", "nhapMaNbNgoaiVien": "Nhập mã NB ngoại viện", "huyLich": "<PERSON><PERSON><PERSON> l<PERSON>", "huyNhieuLich": "<PERSON><PERSON><PERSON> <PERSON> l<PERSON>", "maNbNgoaiVien": "Mã NB ngoại viện", "banCoChacChanMuonXoaDichVu": "Bạn có chắc chắn muốn xóa dịch vụ?", "salesPhuTrach": "Sales phụ trách", "chonSalesPhuTrach": "Chọn sales phụ trách", "dongBoNbHisCu": "Đồng bộ NB HIS cũ", "congTyBaoHiemBaoLanh": "<PERSON><PERSON><PERSON> ty bảo hiểm bảo lãnh", "chonCongTyBaoHiemBaoLanh": "<PERSON><PERSON><PERSON> công ty bảo hiểm bảo lãnh", "vuiLongChonCongTyBaoHiemBaoLanh": "<PERSON><PERSON> lòng chọn công ty bảo hiểm bảo lãnh", "vuiLongChonNguonNguoiBenh": "<PERSON>ui lòng chọn nguồn người bệnh!", "vuiLongNhapSoDienThoai": "<PERSON><PERSON> lòng nhập số điện thoại!", "canhBaoTuoiNguoiBenhLonHon": "<PERSON><PERSON><PERSON>i bệnh <b style='font-weight:900'>>={{tuoi}}</b> tuổi. Vẫn tiếp tục tiếp đón?", "phanLoaiDoiTuong": "<PERSON><PERSON> loại đối tư<PERSON>", "chonPhanLoaiDoiTuong": "<PERSON><PERSON><PERSON> phân loại đối tượng", "dieuTriNgoaiTru": "<PERSON><PERSON><PERSON><PERSON> trị ngoại trú", "taiKham": "<PERSON><PERSON><PERSON>", "huyTiepNhanNBKSK": "<PERSON><PERSON><PERSON> ti<PERSON><PERSON> nhận NB KSK", "huyHoanThanhNBKSK": "<PERSON><PERSON><PERSON> hoàn thành NB KSK", "thanhToanKsk": "Thanh toán KSK", "luuDichVu": "<PERSON><PERSON><PERSON> v<PERSON>", "huyTiepNhanNbKskThanhCong": "<PERSON><PERSON><PERSON> ti<PERSON><PERSON> nhận NB KSK thành công", "huyHoanThanhNbKskThanhCong": "Hủy Hoàn thành NB KSK thành công", "chiDuocPhepHuyTiepNhanVoiHoSoCoTrangThaiKhamSucKhoeTaoMoi": "Chỉ được phép hủy tiếp nhận với hồ sơ có trạng thái khám sức khỏe Tạo mới, mã hồ sơ: {{maHoSo}}", "chiDuocPhepHuyHoanThanhVoiHoSoCoTrangThaiKhamSucKhoeDaTiepDon": "Chỉ được phép hủy hoàn thành hồ sơ có trạng thái khám sức khỏe Đã tiếp đón. M<PERSON> hồ sơ: {{maHoSo}}", "suaNgayThucHien": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> thự<PERSON> hi<PERSON>n", "vuiLongNhapThoiGian": "<PERSON><PERSON> lòng nhập thời gian", "CMT_CCCD": "CMT/CCCD", "salePhuTrach": "Sale phụ trách", "danhSachLichHenTaiKham": "<PERSON><PERSON> sách lịch hẹn tái khám", "dichVuHen": "<PERSON><PERSON><PERSON> v<PERSON> hẹn", "bacSiHen": "<PERSON><PERSON><PERSON> s<PERSON> hẹn", "trangThaiLichHen": "<PERSON>r<PERSON><PERSON> thái lịch hẹn", "nbDaDen": "NB đã đến", "themDVTheoLichHen": "Thêm DV theo lịch hẹn", "batBuocNhapBacSiKham": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> kh<PERSON>m", "luuYSLHoSoTiepNhanToiDa": "<PERSON><PERSON><PERSON> <PERSON> hồ sơ tiếp nhận tối đa", "luuYSLHoSoHuyTiepNhanToiDa": "<PERSON><PERSON><PERSON> ý <PERSON> hồ sơ hủy tiếp nhận tối đa", "luuYSLHoSoHuyHoanThanhToiDa": "<PERSON><PERSON><PERSON> ý <PERSON> hồ sơ hủy hoàn thành tối đa", "chiDuocChonToiDaHoSo": "Chỉ đư<PERSON><PERSON> chọn tối đa {{num}} hồ sơ", "xacNhanDoiThongTinHanhChinhCuaNguoiBenh": "<PERSON><PERSON><PERSON> nhận đổi thông tin hành chính của ngư<PERSON>i bệnh", "thongTinHanhChinhCacDotDieuTriCuaNguoiBenh": "<PERSON>h<PERSON><PERSON> tin hành chính các đợt điều trị của người bệnh", "kiemTraLaiTranhTruongHopSuaNhamHoSo": "<PERSON><PERSON><PERSON> tra lại tr<PERSON>h tr<PERSON><PERSON><PERSON> hợp sửa nh<PERSON><PERSON> hồ sơ", "canhBaoDoiThongTinNb": "Từ <strong>{{oldData}}</strong> thành <strong>{{newData}}</strong>", "maGoiKsk": "Mã gói KSK", "tenGoiKsk": "<PERSON><PERSON>n gói KSK", "phongHenTaiKham": "Phòng hẹn tái khám", "sdtNguoiBaoLanh1": "SĐT người bảo lãnh 1", "sdtNguoiBaoLanh2": "SĐT người bả<PERSON> lãnh 2", "validateCanCuoc": "<PERSON><PERSON><PERSON><PERSON> gồ<PERSON> 12 ký tự số", "validateHoChieu": "<PERSON><PERSON> chiếu tối đa 9 ký tự", "validateMaDinhDanh": "<PERSON><PERSON> đ<PERSON>nh danh gồm 12 ký tự số", "validateChungMinh": "Chứng minh gồm 9 hoặc 12 ký tự số", "vuiLongNhapLyDoDenKham": "<PERSON><PERSON> lòng nhập lý do đến khám", "vuiLongChonDanToc": "<PERSON><PERSON> lòng chọn dân tộc", "xacNhanTuDongNhanDienLaiKhuonMat": "<PERSON><PERSON><PERSON> thực thất bại, bạn có muốn tự động nhận diện lại khuôn mặt?", "xacThucThatBaiVuiLongDangKyLai": "<PERSON><PERSON><PERSON> thực thất bại, vui lòng đăng ký lại khuôn mặt", "hoNgheoCanNgheo": "<PERSON><PERSON> nghèo/cận nghèo", "danhSachChiDinhCuaBo": "<PERSON><PERSON> s<PERSON>ch dịch v<PERSON> c<PERSON>a bộ", "thieuThongTinNgheNghiep": "<PERSON><PERSON><PERSON><PERSON> thông tin nghề nghiệp", "vuiLongChonNgheNghiep": "<PERSON><PERSON> lòng chọn ngh<PERSON> nghiệp", "thoiGianApDungHoNgheoCanNgheo": "<PERSON>h<PERSON><PERSON> gian <PERSON>p dụng hộ nghèo/cận nghèo", "soHoNgheo": "Số hộ nghèo", "nhapSoHoNgheo": "<PERSON><PERSON><PERSON><PERSON> số hộ nghèo", "chuaChonDichVuKhamXnVoiNbCoNguonBsNgoaiVien": "Chưa chọn dịch vụ Khám XN với NB có nguồn BS ngoại viện", "nguoiBenhThuocNguonBacSiNgoaiVienVaChuaDuocChiDinhDichVuKhamXnTiepTucGoiNbTiepTheo": "<PERSON><PERSON><PERSON><PERSON> bệnh thuộc nguồn bác sĩ ngoại viện và chưa được chỉ định dịch vụ khám XN. Tiếp tục gọi Nb tiếp theo?", "hienThiPhongKhamTheoKhuVuc": "<PERSON><PERSON><PERSON> thị phòng khám theo khu vực", "hienThiTatCaPhongKham": "<PERSON><PERSON><PERSON> thị tất cả phòng khám", "dayKetQuaSangBve": "<PERSON><PERSON><PERSON><PERSON> q<PERSON> sang B<PERSON>", "chuaKiemTra": "<PERSON><PERSON><PERSON> kiểm tra", "daKiemTra": "<PERSON><PERSON> kiểm tra", "guiKetQuaSangBveThanhCong": "<PERSON><PERSON><PERSON> q<PERSON> sang <PERSON><PERSON> thành công", "banCoMuonTiepTucThucHienKhong": "Bạn có muốn tiếp tục thực hiện không?", "vuiLongNhapChieuCaoCanNang": "<PERSON><PERSON> lòng nhập chi<PERSON>u cao, cân nặng!", "canhBaoKhiLuuMaTheCAQD": "Chưa có thông tin thẻ này trong cơ sở dữ liệu thẻ do BHXH Bộ Quốc Phòng cung cấp, đề nghị cơ sở khám chữa bệnh đối chiếu với giấy tờ tùy thân theo quy định để tổ chức khám chữa bệnh. Vẫn tiếp tục thực hiện?", "batBuocNhapChieuCao": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> nhập chiều cao NB có mã bệnh: {{message}}", "dongBoQuay": "<PERSON><PERSON><PERSON> bộ quầy", "chuaCoThongTinKioskId": "Chưa có thông tin kioskId!", "huyTiepDonNhieuNb": "<PERSON><PERSON><PERSON> tiếp đón nhiều NB", "nguonNb": "Nguồn NB", "chonNguonNb": "Ch<PERSON>n nguồn Nb", "luuYSLHoSoHuyTiepDonToiDa": "<PERSON><PERSON><PERSON> ý <PERSON> hồ sơ hủy tiếp đón tối đa", "canHuyTiepDon": "c<PERSON>n h<PERSON><PERSON> ti<PERSON><PERSON> đ<PERSON>", "quayDuocChonKhogBaoGomLoaiDuoiTuongCuaNb": "<PERSON>u<PERSON><PERSON> đư<PERSON><PERSON> chọn không bao gồm loại đối tượng của NB. Loại đối tượng của NB {{tenLoaiDoiTuong}}. <PERSON><PERSON> lòng chọn lại đúng quầy.", "timTenNbMaHs": "<PERSON><PERSON><PERSON> tên <PERSON><PERSON> b<PERSON>, mã hồ sơ", "benhNhanDaChiDinhDichVuKham": "Bệnh nhân đã được chỉ định dịnh vụ khám", "khoaDTNgoaiTru": "Khoa ĐT ngoại trú", "tenTk": "<PERSON><PERSON><PERSON>", "soTk": "Số TK", "nbDenSomSoVoiLichHen": "NB {{tenNb}} đến sớm {{soNgay}} ngày so với lịch hẹn. Lịch hẹn: {{thoiGianHen}}", "ngayKiemTra": "<PERSON><PERSON><PERSON> k<PERSON> tra", "taiKhoanKiemTra": "<PERSON><PERSON><PERSON> k<PERSON>n kiểm tra", "raVien": "<PERSON> vi<PERSON>", "tronVien": "<PERSON><PERSON><PERSON><PERSON> vi<PERSON>n", "chuyenVien": "<PERSON><PERSON><PERSON><PERSON> viện", "xinRaVien": "<PERSON>n ra viện", "chinhSuaChanDoanBenh": "Chỉnh sửa chẩn đo<PERSON> bệnh", "chonChanDoanBenh": "<PERSON><PERSON><PERSON> chẩn đo<PERSON> bệnh", "vuiLongChonBacSiKhamChoDichVu": "<PERSON>ui lòng chọn bác sĩ khám cho dịch vụ {{tenDichVu}}", "vuiLongNhapChanDoanBenh": "<PERSON><PERSON> lòng nh<PERSON>p chẩn đo<PERSON> b<PERSON>nh", "vuiLongNhapChanDoanBenhChoDichVu": "<PERSON><PERSON> lòng nhập chẩn đo<PERSON> b<PERSON>nh cho dịch vụ {{tenDichVu}}", "vuiLongXoaDichVuConTruocKhiXoaDichVuCha": "<PERSON>ui lòng xóa dịch vụ con trước khi xóa dịch vụ cha", "chiHienThiToiDaBenhKemTheoChoDv": "Chỉ hiển thị tối đa {{num}} bệ<PERSON> kèm theo cho {{tenDichVu}}", "doiMuBaoHiem": "<PERSON><PERSON><PERSON> mũ b<PERSON>o hi<PERSON>", "neuCoKhiDoiCoGaiQuaiKhong": "<PERSON><PERSON><PERSON> có, khi đội có gài quai không", "muBaoHiemCoBiVoKhong": "<PERSON><PERSON> bảo hiểm có bị vỡ không", "loaiMuBaoHiemTenHang": "<PERSON><PERSON><PERSON> mũ b<PERSON>o hi<PERSON>/<PERSON><PERSON><PERSON> hãng", "suDungRuouBia": "Sử dụng rư<PERSON>u bia", "neuCoHoacKhongRo": "<PERSON><PERSON><PERSON> có hoặc không rõ", "theoCamQuan": "<PERSON> quan", "noiXayRaTaiNan": "<PERSON><PERSON><PERSON> ra tai nạn", "chonNoiXayRaTaiNan": "<PERSON><PERSON><PERSON> n<PERSON>i x<PERSON>y ra tai nạn", "doNongDoConTrongMau": "<PERSON><PERSON> nồng độ cồn trong máu", "khongDo": "<PERSON><PERSON><PERSON><PERSON> đo", "mgmlMau": "mg/ml máu", "nongDoConTrongHoiTho": "<PERSON>ồng độ cồn trong hơi thở", "mglKhiTho": "mg/l khí thở", "hieuLucGiayChuyen": "<PERSON><PERSON><PERSON> g<PERSON><PERSON>", "vuiLongChonThoiGianGiayChuyen": "<PERSON><PERSON> lòng chọn thời gian g<PERSON><PERSON> chuy<PERSON>n", "hieuLucGiayChuyenDaHetHieuLuc": "<PERSON><PERSON><PERSON> lực gi<PERSON>y chuyển đã hết. <PERSON><PERSON><PERSON> lực: {{hieuLuc}}", "nbConHSDangDieuTriNgoaiTruTaiKhoa": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> còn hồ sơ đang điều trị ngoại trú tại khoa {{khoaNb}} - {{maHs}}. B<PERSON>n có muốn tiếp đón lư<PERSON> khám mới", "vuiLongChonThoiGianApDungGiayChuyen": "<PERSON><PERSON> lòng chọn thời gian áp dụng gi<PERSON>y chuyển", "thoiGianApDungGiayChuyen": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON> dụng gi<PERSON> chuy<PERSON>n", "validateHoChieu8KyTu": "<PERSON><PERSON> chiếu 8 ký tự, bắt đầu là chữ in hoa và 7 ký tự số ở sau", "luuVaDangKyKhamTrucTiep": " <PERSON><PERSON><PERSON> và đăng ký khám trực tiếp", "nbThuocHoNgheo": "NB th<PERSON><PERSON><PERSON> hộ nghèo", "maNBCu": "Mã NB cũ", "nhapMaNBCu": "Nhậ<PERSON> mã NB cũ", "ngayThangNamSinhNguoiBaoLanh2": "<PERSON><PERSON><PERSON> th<PERSON>g năm sinh ng<PERSON><PERSON><PERSON> b<PERSON><PERSON> l<PERSON> 2", "ngayThangNamSinhNguoiBaoLanh": "<PERSON><PERSON><PERSON> tháng năm sinh ng<PERSON><PERSON>i b<PERSON>o l<PERSON>nh", "thoiDiemXayRaTaiNan": "<PERSON><PERSON><PERSON><PERSON> điểm x<PERSON>y ra tai nạn", "canNangPhaiLonHon0": "Cân nặng phải lớn hơn 0", "hetGioTiepDon": "<PERSON><PERSON><PERSON> giờ tiếp đón ca {{ca}}", "chuaDenGioTiepDonCa": "<PERSON><PERSON><PERSON> đến giờ tiếp đón ca {{ca}}", "thoiGianTiepDonTu": "Th<PERSON>i gian tiếp đón từ {{tuThoiGian}} đến {{denThoiGian}}", "ngayChuyenVien": "<PERSON><PERSON><PERSON> viện", "ngayChuyenTuyen": "<PERSON><PERSON><PERSON> chuy<PERSON> tuyến", "giaTriSuDungCaNam": "<PERSON><PERSON><PERSON> trị sử dụng cả năm", "giayChuyenVienCoGiaTriTrong01Nam": "GCV có giá trị trong 01 năm", "vuiLongNhapChuyenVien": "<PERSON><PERSON> lòng nhập ngày chuyển viện", "vuiLongNhapChuyenTuyen": "<PERSON><PERSON> lòng nhập ngày chuyển tuyến", "maSoDVQHNS": "Mã số ĐVQHNS", "nhapMaSoDVQHNS": "<PERSON><PERSON><PERSON><PERSON> mã số ĐVQHNS", "validateMaSoDVQHNS": "Mã số ĐVQHNS gồm 7 ký tự số", "coDemThuocTaoPhieuHoanTra": "<PERSON><PERSON> đem <PERSON>, t<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> hoàn trả", "khongDemThuocTaoPhieuThuThem": "<PERSON><PERSON><PERSON><PERSON> đem thuố<PERSON>, t<PERSON><PERSON> phi<PERSON>u thu thêm", "boQuaVaChoTiepDon": "Bỏ qua và cho tiếp đón", "daTaoPhieuYeuCauTraThuocThanhCongCanDuyetHoanThuocTruocKhiTiepDon": "<PERSON><PERSON> tạo phiếu yêu cầu trả thuốc thành công. Cần duyệt hoàn thuốc trước khi tiếp đón", "daTaoPhieuThuThanhCongVuiLongThanhToanPhieuThuTruocKhiTiepDon": "<PERSON><PERSON> tạo phiếu thu thành công. <PERSON><PERSON> lòng <PERSON>h toán phiếu thu trước khi tiếp đón", "nguoiBenhBHDenTiepDonSomHonLichHenConThuocBHTraChuaTra": "<PERSON><PERSON><PERSON><PERSON> bệnh BH đến tái khám sớm hơn lịch hẹn và còn thuốc bảo hiểm chưa trả, mã HS cũ là <b>{{maHs}}</b>. <PERSON>ần phải hoàn trả tiền/thuốc bảo hiểm còn dư.", "vuiLongChonMotTrongCacGiaTriBenDuoi": "<PERSON>ui lòng chọn một trong các giá trị bên dưới!", "banCoChacChanMuonHuyTiepDonKhong": "Bạn có chắc chắn muốn huỷ tiếp đón không?", "daTTSang": "Đã TT(Sáng)", "daKhamSang": "<PERSON><PERSON> k<PERSON>(Sáng)", "daTTChieu": "Đã TT(Chiều)", "daKhamChieu": "<PERSON><PERSON>(Chiều)", "nhapTuanThai": "<PERSON><PERSON><PERSON><PERSON> tu<PERSON> thai", "tuanThai": "<PERSON><PERSON><PERSON> thai", "soThai": "Số thai", "nhapSoThai": "<PERSON><PERSON><PERSON><PERSON> số thai", "chanDoanIcdNoiGioiThieu": "<PERSON><PERSON><PERSON> đo<PERSON> ICD nơi giới thiệu", "chonChanDoanIcdNoiGioiThieu": "<PERSON><PERSON><PERSON> đoán ICD nơi giới thiệu", "soPhieuChuyenVien": "<PERSON><PERSON> phi<PERSON>u chuy<PERSON>n viện", "nhapSoPhieuChuyenVien": "<PERSON><PERSON><PERSON><PERSON> số phi<PERSON>u chuyển viện", "vneid": "VNeID", "banKhongCoQuyenSuaTenNguoiBenh": "<PERSON><PERSON>n không có quyền sửa Tên người bệnh!", "tenNoiGioiThieu": "<PERSON><PERSON><PERSON> n<PERSON>i gi<PERSON>i thiệu", "doiTuongKcbLapBenhAn": "<PERSON><PERSON><PERSON> KCB lập b<PERSON><PERSON>n", "importDsNb": "Import DS NB", "khoaChuyenDen": "<PERSON><PERSON><PERSON> chuy<PERSON>n đến", "chonKhoaChuyenDen": "<PERSON><PERSON><PERSON> khoa chuyển đến", "vuiLongChonKhoaChuyenDen": "<PERSON><PERSON> lòng chọn khoa chuyển đến", "baoMatTheRFID": "Báo mất thẻ RFID", "baoMatTheThanhCong": "<PERSON><PERSON>o mất thẻ thành công!", "xacNhanBaoMatTheRFID": "<PERSON><PERSON><PERSON> nhận báo mất thẻ RFID?"}