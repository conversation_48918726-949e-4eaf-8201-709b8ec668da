{"ttThanhToan": "Total payment", "ttHoan": "Total amount of return", "bsChiDinh": "Indicating doctor", "bsThucHien": "Performing doctor", "khoaChiDinh": "Indicating department", "thoiGianChiDinh": "Indication time", "thoiGianThucHien": "Performance time", "phongThucHien": "Performance Room", "tuTra": "Self-pay", "khongTinhTien": "No charge", "thanhToanSau": "Pay later", "xemAnh": "View images", "xemKetQua": "View results", "xemKqPacs": "View results (PACS)", "xemKqPdf": "View results (PDF)", "phieuKhamChung": "General medical examination note", "danhSachPhieu": "List of notes", "tenPhieu": "Note name", "tenThuoc": "Medication name", "thongTinPhimChup": "Information of films", "slKe": "Quantity of prescription", "slDung": "Quantity of use", "lieuDungCachDung": "Dosage - Usage", "tgChiDinh": "Indication time", "thoiGianPhat": "Time of distribution", "diem": "Point", "capNhatThongTin": "Update information", "tenVatTu": "Supply name", "nhapTenVatTu": "Enter name of supplies", "nhapTenVatTuHoacTenBoChiDinh": "Enter name of supplies or indicated set", "khongCoDuLieuVatTuDaChon": "The selected supply data does not exist", "nhapGiaVatTu": "Enter price of supplies", "hoSoKhamChuaBenh": "Record of Medical examination and treatment", "tomTatBenhAn": "Summary of Medical Record", "temLuuTruBenhAn": "Archive Label of Medical Record", "danhSachDichVu": "List of services", "danhSachThuoc": "List of medications", "danhSachVatTu": "List of supplies", "hoSoBenhAn": "Medical record", "lichSuKhamBenh": "History of Medical examination", "nhapGiaTriTuoiNhoNhatCanTin": "Enter the smallest value of age", "ngayDangKy": "Registration date", "hoTen": "Full Name", "soDienThoai": "Phone number", "soBHYT": "Health insurance number", "timHoTenNguoiBenh": "Search patient name", "timSoDienThoai": "Search phone number", "nhapHoacQuetQRNBMaNb": "Enter or scan patient QR code, patient code", "soDienThoaiSaiDinhDang": "Invalid format of phone number!", "ngayThangNamSinh": "DOB", "nhapNgayThangNamSinh": "Enter date of birth", "tuoi": "Age", "maSoBHXH": "Social insurance number", "nhapMaSoBHXH": "Enter Social insurance number", "maKhuVuc": "Area code", "nhapMaKhuVuc": "Enter Area code", "hanThe": "Card expiration date", "thoiDiemDu5NamLienTuc": "Period for 5 consecutive years", "nhapThoiDiemDu5NamLienTuc": "Enter a period of 5 consecutive years", "diaChiBHYT": "Health insurance address", "nhapDiaChiBHYT": "Enter Health insurance address", "noiDangKyKCB": "Healthcare service establishment", "chonNoiDangKy": "Select establishment", "loaiGiayToTuyThan": "Type of identity paper", "chonLoaiGiayToTuyThan": "Select type of identity paper", "maSoGiayToTuyThan": "Code of identity paper", "nhapMaSoGiayToTuyThan": "Enter code of identity paper", "diaChiTaiNuocNgoai": "Overseas address", "nhapDiaChiTaiNuocNgoai": "Enter overseas address", "canNangKhiVaoVien": "Weight at admission", "nhapHanThe": "Enter card expiration date", "nhapThongTin": "Enter information", "suaThongTinNguoiBenh": "Edit patient information", "chiTietThongTinNguoiBenh": "Detailed patient information", "doiTuong": "Subject", "chonDoiTuong": "Select subject", "chonLoaiDoiTuong": "Select subject type", "loaiDoiTuong": "Type of Subject", "thoiGianCoKetQua": "The time when results are available", "thoiGianKetLuan": "Conclusion Time", "giayToTaiLen": "Uploaded papers", "taoThuMucThanhCong": "Created folder successfully", "ten": "name", "nguoiTao": "Creator", "ngayTao": "Created date", "tenThuMuc": "Name of folder", "chonHoacKeoAnhVaoDay": "Select or drag photos here", "tenAnh": "Photo name", "dongHoSo": "Close medical record", "moHoSo": "Open patient file", "danhSachLuuTruBa": "List of medical record archives", "danhSachHoaChat": "List of chemicals", "thoiGianLuuTru": "Archive time", "maLuuTru": "Archive code", "trangThaiBenhAn": "Medical record status", "chonTrangThaiBenhAn": "Select medical record status", "doiTuongKcb": "Subject for examination and treatment", "thoiGianNhanBa": "Date of receiving medical record", "thoiGianLapBenhAn": "Registration Date", "tenKhoaNb": "Department of Patient", "soNgayDieuTri": "Number of days of treatment", "dsCdChinh": "Treatment diagnosis", "dsCdKemTheo": "Diagnosis of comorbidities", "thoiGianVaoVien": "Time of entering", "tienConLai": "Remaining amount", "huongDieuTri": "Treatment direction", "ketQuaDieuTri": "Treatment results", "soNamLuuTru": "Number of archive years", "trangThaiNb": "Patient status", "tenFile": "File name", "chonHoacKeoFileVaoDay": "Select or drag a file here", "timTenNbMaHs": "Search patient name, patient file code", "danhSachSuatAn": "List of meals", "dichVuGiuong": "Bed service", "giaPhuThu": "Surcharge price", "tenDVGiuong": "Name of bed service", "thoiGianNam": "Time spent lying", "chinhSua": "Edit", "themMoi": "Add new", "luuVaThemMoi": "Save and add", "ghiChu": "Note", "taiTepCoSan": "Upload available files", "taiTepPdf": "Upload PDF files", "taiAnh": "Upload photo", "chonMotFile": "Select 1 file", "chonNhieuFile": "Select multiple files", "tenBieuMau": "Form name", "chonNgay": "Select date", "danhSachBieuMauScan": "List of scan forms", "maBieuMau": "Form code", "tuVanVien": "Consultant", "ngayThucHienPhaiLonHonNgayDangKy": "Performance date must be greater than registration date", "hoSoBenhAnNguoiBenh": "Patient medical record", "maLuuTruKhoa": "Department archive code", "khongTonTaiMaBADaiHan": "Long-term medical record code does not exist", "khongThucHien": "Not executed", "khoaNguoiBenh": "Patient department", "loaiThuoc": "Type of medication", "timTenNbQrNbMaNbMaHoSo": "Search patient name, patient QR, patient code, patient file code", "choThucHien": "Waiting for execution", "daTiepNhan": "Received", "hoanThanh": "Completed", "boQua": "<PERSON><PERSON>", "chonBsChiDinh": "Select indicating doctor", "chonBsThucHien": "Select performing doctor", "ngayRaVien": "Discharge date", "tenKhongDuocDeTrong": "Name cannot be blank", "tenDaTonTai": "The name {0} already exists", "banChacChanMuonXoa": "Are you sure you want to delete", "danhSachLuuTruBenhAn": "List of medical record archives", "chiTietLuuTruBenhAn": "Details of medical record storage", "nhanBA": "Receive Medical Record", "tuChoi": "Refuse", "huyNhan": "Cancel receipt", "duyetBA": "Approve Medical Record", "huyDuyet": "Cancel approval", "luuTru": "Storage", "huyLuuTru": "Cancel archive", "choMuon": "Loan", "nhanLai": "Receive", "soTapLuuTruPhaiLonHon0": "The number of archived volumes must be greater than 0", "thongTinLuuTru": "Storage information", "nhapMaLuuTru": "Enter the storage code", "nhapMaLuuTruKhoa": "Enter the department archive code", "chonThoiGianLuuTru": "Choose the storage period", "loaiBenhAn": "Type of medical record", "chonLoaiBenhAn": "Select type of medical record", "nguoiNhan": "Receiver", "nhapNguoiNhan": "Enter recipient", "thoiGianNhanBA": "Date of receiving medical record", "chonThoiGianNhanBA": "Select medical record receipt time", "lyDoTuChoi": "Reason for refusal", "nhapLyDoTuChoi": "Enter the reason for rejection", "doiTuongKCB": "Subject for examination and treatment", "chonDoiTuongKCB": "Select the Subject for examination and treatment", "ngayTuChoi": "Rejection date", "chonNgayTuChoi": "Select an opt-out date", "chonNgayMuon": "Select loan date", "dayLuuTru": "Storage range", "nhapDayLuuTru": "Enter the storage range", "keLuuTru": "Storage shelves", "nhapKeLuuTru": "Enter storage shelves", "oLuuTru": "Storage cell", "nhapOLuuTru": "Please enter the storage box", "choMuonBenhAn": "Loan medical records", "nguoiMuon": "<PERSON><PERSON><PERSON>", "vuiLongChonNguoiMuon": "Please select a borrower", "chonNguoiMuon": "Choose a borrower", "khoaMuon": "Borrowing Department", "chonKhoaMuon": "Choose a loan department", "nhapSoDienThoai": "Enter phone number", "ngayMuon": "Borrowed date", "vuiLongChonNgayMuon": "Please select a rental date", "chonThoiGianMuon": "Select the loan period", "lyDoMuon": "Reason for borrowing", "vuiLongNhapLyDoMuon": "Please enter the reason for borrowing", "nhapLyDoMuon": "Enter the reason for borrowing", "mucDich": "Purpose", "nhapMucDich": "Enter purpose", "luuTruKho": "Warehouse storage", "tomTat": "Summary", "cacBenhAnCoTrangThaiKhacNhau": "Medical records have different statuses", "daNhan": "Received", "nBRaVien": "patient  was discharged from the hospital", "timTheoMaLuuTruKhoa": "Search by department archive code", "xoaLuuTruPhimThanhCong": "Deleted movie archive successfully", "themMoiLuuTruPhimThanhCong": "Added new movie archive successfully", "capNhatLuuTruPhimThanhCong": "Movie archive updated successfully", "doiTrangThaiBenhAnThanhCong": "Successfully changed medical record status", "luuTruBenhAnThanhCong": "Successfully archived medical records", "capNhatThongTinThanhCong": "Successfully updated", "chanDoanChinh": "Primary diagnosis", "thoiGianNhapVien": "Hospital admission time", "soTienConLai": "Remaining amount", "trangThai": "Status", "chanDoanKemTheo": "Accompanied Diseases", "tGVaoVien": "Hospital admission time", "daLuuTru": "Archived", "chuaLuuTru": "Not archived", "loaiDichVu": "Type of service", "soPhimChupTaiVien": "Number of films taken at the hospital", "soPhimChupTuyenKhac": "Number of other radiographs", "banCoChacChanMuonXoa": "Are you sure you want to delete", "keOLuuTru": "Shelves/Storage boxes", "luuTruBenhAn": "Store medical records", "loaiLuuTru": "Type of archive", "chonSoNamLuuTru": "Select the number of years to store", "vuiLongNhapDayLuuTru": "Please enter storage range", "vuiLongNhapKeLuuTru": "Please enter storage shelves", "vuiLongNhapOLuuTru": "Please enter the storage box", "vuiLongChonSoNamLuuTru": "Please select the number of years of storage", "chonLoaiLuuTru": "Select storage type", "vuiLongChonLoaiLuuTru": "Please select storage type", "tuChoiBenhAn": "Refuse medical records", "thongTinDieuTri": "Treatment information", "chonNhomDichVuCap2": "Select service group level 2", "vuiLongNhapLyDoTuChoi": "Please enter reason for refusal", "nhapSoPhimChupTaiVien": "Enter the number of films taken at the hospital", "nhapSoPhimChupTuyenKhac": "Enter the number of other radiographs", "xemDienBienChiSoXetNghiem": "See the progress of test indicators", "timTenBieuMau": "Find form name", "timTenNbMaHoSoMaThe": "Find patient name, profile code, card code, patient code, vaccination code", "vuiLongChonLaiFileScan": "Please select scan file", "phieu1": "Ticket 1", "chonMaBa": "Select medical record code", "phieuChuaDuocThietLap": "Ticket not set up yet", "sapXepHoSoBenhAn": "Arrange medical records", "nbKhongThucHien": "patient  does not perform", "ngayXacNhanKhongThucHien": "Confirmation date not fulfilled", "lyDoNguoiBenhKhongThucHien": "Reasons why patients do not perform", "danhSachHSGiamDinhBHTrucTiep": "List of direct insurance appraisal records", "hinhThucThanhToanKsk": "KSK Payment Methods", "hoSoBenhAnDuPhong": "Backup medical records", "taoPhieuMuon": "Create a loan slip", "tuNgayYeuCauMuonBA": "From medical record loan request date", "denNgayYeuCauMuonBA": "To medical record loan request date", "taoPhieuMuonBA": "Create medical record loan form", "taoPhieuMuonThanhCong": "Medical record loan form created successfully", "taoPhieuTraBA": "Create medical record return form", "taoPhieuTraThanhCong": "Created payment slip successfully", "hoSoBenhAnCuaNguoiBenh": "Patient' medical records", "xemDanhSachPhieuSan": "View list of scanned vouchers", "nhapSoNgayDieuTri": "Enter the number of treatment days", "huyHoanThanhBenhAnThanhCong": "Successfully canceled the completion of the medical record.", "chinhSuaThoiGianLapBenhAn": "Edit the time of medical record creation.", "maHoSoMoiNhat": "Latest file code", "xemPhieuScan": "View scan sheet", "nguoiLuuTru": "The curator", "nhapTenNguoiLuuTru": "Enter the name of the storage person", "dayGiayTo": "Push the paperwork.", "khongCoFilePhuHop": "No matching file.", "phieuNameChuaDuocKySoBanCoMuonDayGiayToKhong": "{{ten<PERSON>hieu}} has not been digitally signed. Do you want to push the documents?", "dongGoi": "Packaging", "hoSoBenhAnDongGoi": "Medical record packaging", "phieuChuaDuocDongGoi": "The package has not been sealed.", "xemHoSoBenhAnDongGoi": "View packaged medical records", "phieuThuHoaDon": "Receipt - Invoice", "inHoaDonDienTu": "Electronic invoice printing", "trinhKyToBia": "Signature on the cover page", "luuTruBenhAnDienTu": "Store electronic medical records"}